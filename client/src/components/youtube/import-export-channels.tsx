import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { YoutubeChannel } from '@shared/schema';
import { Download, Upload, AlertCircle, Database, Server } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

interface ImportExportChannelsProps {
  channels: YoutubeChannel[];
  onImportComplete: () => void;
}

export function ImportExportChannels({ channels, onImportComplete }: ImportExportChannelsProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const allDataFileInputRef = useRef<HTMLInputElement>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importResults, setImportResults] = useState<{
    total: number;
    added: number;
    updated: number;
    skipped: number;
    failed: number;
    details: Array<{
      channelTitle: string;
      status: 'added' | 'updated' | 'skipped' | 'failed';
      message?: string;
    }>;
  } | null>(null);

  // State for all data import/export
  const [isImportingAllData, setIsImportingAllData] = useState(false);
  const [allDataImportDialogOpen, setAllDataImportDialogOpen] = useState(false);
  const [allDataImportProgress, setAllDataImportProgress] = useState(0);
  const [allDataImportResults, setAllDataImportResults] = useState<{
    channels: {
      total: number;
      added: number;
      updated: number;
      skipped: number;
      failed: number;
    };
    videos: {
      total: number;
      added: number;
      updated: number;
      skipped: number;
      failed: number;
    };
    details: Array<{
      channelTitle: string;
      status: 'added' | 'updated' | 'skipped' | 'failed';
      message?: string;
    }>;
  } | null>(null);

  // Export channels to a JSON file
  const handleExportChannels = () => {
    try {
      // Format channels for export
      const channelsToExport = channels.map(channel => ({
        channelId: channel.channelId,
        channelTitle: channel.channelTitle,
        channelUrl: channel.channelUrl,
        videoLimit: channel.videoLimit || 15
      }));

      // Create a JSON blob
      const blob = new Blob([JSON.stringify(channelsToExport, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.download = `youtube-channels-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: 'Channels Exported',
        description: `${channels.length} channels exported successfully.`,
      });
    } catch (error) {
      console.error('Error exporting channels:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export channels. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Trigger file input click
  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle file selection
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsImporting(true);
      setImportDialogOpen(true);
      setImportProgress(0);
      setImportResults(null);

      // Read the file
      const fileContent = await file.text();
      const channelsToImport = JSON.parse(fileContent);

      // Validate the imported data
      if (!Array.isArray(channelsToImport)) {
        throw new Error('Invalid import format. Expected an array of channels.');
      }

      // Initialize results tracking
      const results = {
        total: channelsToImport.length,
        added: 0,
        updated: 0,
        skipped: 0,
        failed: 0,
        details: [] as Array<{
          channelTitle: string;
          status: 'added' | 'updated' | 'skipped' | 'failed';
          message?: string;
        }>,
      };

      // Use the batch import endpoint
      setImportProgress(10); // Show initial progress

      try {
        // Validate all channels before sending
        for (const channel of channelsToImport) {
          if (!channel.channelId || !channel.channelTitle || !channel.channelUrl) {
            results.failed++;
            results.details.push({
              channelTitle: channel.channelTitle || 'Unknown Channel',
              status: 'failed',
              message: 'Missing required fields (channelId, channelTitle, or channelUrl)',
            });
          }
        }

        // If there are validation errors, don't proceed with the import
        if (results.failed > 0) {
          setImportProgress(100);
          setImportResults(results);
          return;
        }

        setImportProgress(30); // Update progress

        // Send the batch import request
        const res = await apiRequest('POST', '/api/youtube-channels/batch-import', channelsToImport);

        if (!res.ok) {
          const errorData = await res.json();
          throw new Error(errorData.message || 'Failed to import channels');
        }

        setImportProgress(70); // Update progress

        // Process the response
        const importResponse = await res.json();

        // Update results based on the server response
        for (const result of importResponse.results) {
          switch (result.status) {
            case 'added':
              results.added++;
              results.details.push({
                channelTitle: result.channelTitle,
                status: 'added',
              });
              break;
            case 'updated':
              results.updated++;
              results.details.push({
                channelTitle: result.channelTitle,
                status: 'updated',
                message: result.message,
              });
              break;
            case 'skipped':
              results.skipped++;
              results.details.push({
                channelTitle: result.channelTitle,
                status: 'skipped',
                message: result.message,
              });
              break;
            case 'failed':
              results.failed++;
              results.details.push({
                channelTitle: result.channelTitle,
                status: 'failed',
                message: result.message,
              });
              break;
          }
        }

        setImportProgress(100); // Complete progress
      } catch (error) {
        console.error('Error during batch import:', error);
        results.failed = channelsToImport.length;
        results.details.push({
          channelTitle: 'Batch Import',
          status: 'failed',
          message: error instanceof Error ? error.message : 'Unknown error during batch import',
        });
        setImportProgress(100); // Complete progress
      }

      // Update results
      setImportResults(results);

      // Refresh channels list
      onImportComplete();

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error importing channels:', error);
      toast({
        title: 'Import Failed',
        description: error instanceof Error ? error.message : 'Failed to import channels. Please check the file format.',
        variant: 'destructive',
      });
      setImportDialogOpen(false);
    } finally {
      setIsImporting(false);
    }
  };

  // Export all channel and video data
  const handleExportAllData = async () => {
    try {
      toast({
        title: 'Export Started',
        description: 'Preparing to export all channel and video data. This may take a moment...',
      });

      // Call the export endpoint
      const response = await apiRequest('GET', '/api/youtube-channels/export-all-data', null, {
        responseType: 'blob'
      });

      if (!response.ok) {
        throw new Error('Failed to export data');
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `youtube-all-data-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: 'Export Complete',
        description: `All channel and video data exported successfully.`,
      });
    } catch (error) {
      console.error('Error exporting all data:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export all data. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Trigger all data file input click
  const handleImportAllDataClick = () => {
    if (allDataFileInputRef.current) {
      allDataFileInputRef.current.click();
    }
  };

  // Handle all data file selection
  const handleAllDataFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsImportingAllData(true);
      setAllDataImportDialogOpen(true);
      setAllDataImportProgress(0);
      setAllDataImportResults(null);

      // Read the file
      const fileContent = await file.text();
      const dataToImport = JSON.parse(fileContent);

      // Basic validation
      if (!dataToImport.version || !dataToImport.channels) {
        throw new Error('Invalid import format. Expected a valid export file with version and channels.');
      }

      setAllDataImportProgress(20);

      // Send the import request
      const res = await apiRequest('POST', '/api/youtube-channels/import-all-data', dataToImport);

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to import data');
      }

      setAllDataImportProgress(80);

      // Process the response
      const importResponse = await res.json();
      setAllDataImportResults(importResponse.stats);
      setAllDataImportProgress(100);

      // Refresh channels list
      onImportComplete();

      // Reset file input
      if (allDataFileInputRef.current) {
        allDataFileInputRef.current.value = '';
      }

      toast({
        title: 'Import Complete',
        description: `Imported ${importResponse.stats.channels.added + importResponse.stats.channels.updated} channels and processed ${importResponse.stats.videos.total} videos.`,
      });
    } catch (error) {
      console.error('Error importing all data:', error);
      toast({
        title: 'Import Failed',
        description: error instanceof Error ? error.message : 'Failed to import data. Please check the file format.',
        variant: 'destructive',
      });
      setAllDataImportDialogOpen(false);
    } finally {
      setIsImportingAllData(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Import & Export</CardTitle>
        <CardDescription>
          Import and export channels and video data
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Channels only section */}
        <div>
          <h3 className="text-sm font-medium mb-2">Channels Only</h3>
          <p className="text-xs text-muted-foreground mb-3">Import or export just the channel information without videos or analysis</p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={handleExportChannels}
              disabled={channels.length === 0}
              className="flex-1"
              size="sm"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Channels ({channels.length})
            </Button>
            <Button
              onClick={handleImportClick}
              variant="outline"
              className="flex-1"
              size="sm"
            >
              <Upload className="h-4 w-4 mr-2" />
              Import Channels
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept=".json"
              className="hidden"
            />
          </div>
        </div>

        <Separator />

        {/* Complete data section */}
        <div>
          <h3 className="text-sm font-medium mb-2">Complete Data</h3>
          <p className="text-xs text-muted-foreground mb-3">Import or export all channel data including videos, transcriptions, and AI analysis</p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={handleExportAllData}
              disabled={channels.length === 0}
              className="flex-1"
              size="sm"
              variant="default"
            >
              <Database className="h-4 w-4 mr-2" />
              Export All Data
            </Button>
            <Button
              onClick={handleImportAllDataClick}
              variant="outline"
              className="flex-1"
              size="sm"
            >
              <Server className="h-4 w-4 mr-2" />
              Import All Data
            </Button>
            <input
              type="file"
              ref={allDataFileInputRef}
              onChange={handleAllDataFileChange}
              accept=".json"
              className="hidden"
            />
          </div>
        </div>

        {/* Import Progress Dialog */}
        <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                {importResults ? 'Import Complete' : 'Importing Channels...'}
              </DialogTitle>
              <DialogDescription>
                {importResults
                  ? `Processed ${importResults.total} channels`
                  : 'Please wait while we process your channels'}
              </DialogDescription>
            </DialogHeader>

            {!importResults ? (
              <div className="py-6">
                <Progress value={importProgress} className="mb-2" />
                <p className="text-center text-sm text-muted-foreground">
                  {importProgress}% complete
                </p>
              </div>
            ) : (
              <div className="py-4 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex flex-col items-center p-2 border rounded-md">
                    <span className="text-sm text-muted-foreground">Added</span>
                    <span className="text-2xl font-bold text-green-500">{importResults.added}</span>
                  </div>
                  <div className="flex flex-col items-center p-2 border rounded-md">
                    <span className="text-sm text-muted-foreground">Updated</span>
                    <span className="text-2xl font-bold text-blue-500">{importResults.updated}</span>
                  </div>
                  <div className="flex flex-col items-center p-2 border rounded-md">
                    <span className="text-sm text-muted-foreground">Skipped</span>
                    <span className="text-2xl font-bold text-yellow-500">{importResults.skipped}</span>
                  </div>
                  <div className="flex flex-col items-center p-2 border rounded-md">
                    <span className="text-sm text-muted-foreground">Failed</span>
                    <span className="text-2xl font-bold text-red-500">{importResults.failed}</span>
                  </div>
                </div>

                {importResults.failed > 0 && (
                  <div className="border rounded-md p-2 bg-red-50 dark:bg-red-900/20">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-sm font-medium">Failed Imports</span>
                    </div>
                    <div className="space-y-1 max-h-32 overflow-y-auto text-sm">
                      {importResults.details
                        .filter(detail => detail.status === 'failed')
                        .map((detail, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <span className="font-medium">{detail.channelTitle}:</span>
                            <span className="text-muted-foreground">{detail.message}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            <DialogFooter>
              <Button
                onClick={() => setImportDialogOpen(false)}
                disabled={isImporting}
              >
                {importResults ? 'Close' : 'Cancel'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* All Data Import Progress Dialog */}
        <Dialog open={allDataImportDialogOpen} onOpenChange={setAllDataImportDialogOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                {allDataImportResults ? 'Import Complete' : 'Importing All Data...'}
              </DialogTitle>
              <DialogDescription>
                {allDataImportResults
                  ? `Processed ${allDataImportResults.channels.total} channels and ${allDataImportResults.videos.total} videos`
                  : 'Please wait while we process your data'}
              </DialogDescription>
            </DialogHeader>

            {!allDataImportResults ? (
              <div className="py-6">
                <Progress value={allDataImportProgress} className="mb-2" />
                <p className="text-center text-sm text-muted-foreground">
                  {allDataImportProgress}% complete
                </p>
              </div>
            ) : (
              <div className="py-4 space-y-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Channels</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col items-center p-2 border rounded-md">
                      <span className="text-sm text-muted-foreground">Added</span>
                      <span className="text-2xl font-bold text-green-500">{allDataImportResults.channels.added}</span>
                    </div>
                    <div className="flex flex-col items-center p-2 border rounded-md">
                      <span className="text-sm text-muted-foreground">Updated</span>
                      <span className="text-2xl font-bold text-blue-500">{allDataImportResults.channels.updated}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Videos</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col items-center p-2 border rounded-md">
                      <span className="text-sm text-muted-foreground">Updated</span>
                      <span className="text-2xl font-bold text-blue-500">{allDataImportResults.videos.updated}</span>
                    </div>
                    <div className="flex flex-col items-center p-2 border rounded-md">
                      <span className="text-sm text-muted-foreground">Skipped</span>
                      <span className="text-2xl font-bold text-yellow-500">{allDataImportResults.videos.skipped}</span>
                    </div>
                  </div>
                </div>

                {allDataImportResults.channels.failed > 0 && (
                  <div className="border rounded-md p-2 bg-red-50 dark:bg-red-900/20">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-sm font-medium">Failed Imports</span>
                    </div>
                    <div className="space-y-1 max-h-32 overflow-y-auto text-sm">
                      {allDataImportResults.details
                        .filter(detail => detail.status === 'failed')
                        .map((detail, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <span className="font-medium">{detail.channelTitle}:</span>
                            <span className="text-muted-foreground">{detail.message}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            <DialogFooter>
              <Button
                onClick={() => setAllDataImportDialogOpen(false)}
                disabled={isImportingAllData}
              >
                {allDataImportResults ? 'Close' : 'Cancel'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
