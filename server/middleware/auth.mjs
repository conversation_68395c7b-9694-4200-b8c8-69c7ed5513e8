// Middleware to ensure user is authenticated
export function ensureAuthenticated(req, res, next) {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ message: 'Unauthorized' });
}

// Middleware to ensure user is an admin
export function ensureAdmin(req, res, next) {
  if (req.isAuthenticated() && (req.user?.isAdmin || req.user?.role === 'admin')) {
    return next();
  }
  res.status(403).json({ message: 'Forbidden' });
}

// Alias for ensureAuthenticated for backward compatibility
export const authenticateUser = ensureAuthenticated;
