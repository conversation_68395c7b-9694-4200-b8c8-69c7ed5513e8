import { useState } from "react";
import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Users, Settings, BarChart, Home, LogOut, Server, Power, Loader2 } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";

export function AdminSidebar() {
  const [location] = useLocation();
  const { logoutMutation } = useAuth();
  const { toast } = useToast();
  const [isRestarting, setIsRestarting] = useState(false);

  const isActive = (path: string) => {
    return location === path;
  };

  const handleLogout = () => {
    logoutMutation.mutate(undefined, {
      onSuccess: () => {
        toast({
          title: "Logged out",
          description: "You have been logged out successfully.",
        });
        // Force a page reload to ensure the user is properly logged out
        window.location.href = '/auth';
      }
    });
  };

  // Admin sidebar functionality

  // Simple restart handler
  const handleRestart = () => {
    // Confirm restart
    if (!window.confirm("Are you sure you want to restart the application?")) {
      return;
    }

    // Set loading state
    setIsRestarting(true);

    // Make the API call
    fetch("/api/admin/restart", {
      method: "POST",
      headers: { "Content-Type": "application/json" }
    })
    .then(response => response.json())
    .then(data => {
      // Show success message
      toast({
        title: "Restarting Application",
        description: "The server is restarting. The page will refresh in 5 seconds."
      });

      // Refresh the page after 5 seconds
      setTimeout(() => window.location.reload(), 5000);
    })
    .catch(error => {
      // Show error message
      console.error("Restart error:", error);
      toast({
        title: "Error",
        description: "Failed to restart the application",
        variant: "destructive"
      });

      // Reset loading state
      setIsRestarting(false);
    });
  };

  return (
    <div className="w-64 border-r h-screen p-4 flex flex-col">
      <div className="text-xl font-bold mb-6">Admin Panel</div>

      <nav className="space-y-2 flex-1">
        <Link to="/">
          <Button
            variant={isActive("/") ? "default" : "ghost"}
            className="w-full justify-start"
          >
            <Home className="mr-2 h-4 w-4" />
            Home
          </Button>
        </Link>

        <Link to="/admin/dashboard">
          <Button
            variant={isActive("/admin/dashboard") ? "default" : "ghost"}
            className="w-full justify-start"
          >
            <BarChart className="mr-2 h-4 w-4" />
            Dashboard
          </Button>
        </Link>

        <Link to="/admin/users">
          <Button
            variant={isActive("/admin/users") ? "default" : "ghost"}
            className="w-full justify-start"
          >
            <Users className="mr-2 h-4 w-4" />
            Users
          </Button>
        </Link>

        <Link to="/settings">
          <Button
            variant={isActive("/settings") ? "default" : "ghost"}
            className="w-full justify-start"
          >
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </Link>

        <Link to="/admin/settings">
          <Button
            variant={isActive("/admin/settings") ? "default" : "ghost"}
            className="w-full justify-start"
          >
            <Server className="mr-2 h-4 w-4" />
            Server Settings
          </Button>
        </Link>
      </nav>

      <div className="pt-4 border-t">
        <div className="text-sm text-muted-foreground mb-2">
          Admin Area
        </div>
        <div className="space-y-2">

          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={handleRestart}
            disabled={isRestarting}
          >
            {isRestarting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Restarting...
              </>
            ) : (
              <>
                <Power className="mr-2 h-4 w-4" />
                Restart App
              </>
            )}
          </Button>

          <Button
            variant="destructive"
            className="w-full justify-start"
            onClick={handleLogout}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Logout
          </Button>
        </div>
      </div>
    </div>
  );
}
