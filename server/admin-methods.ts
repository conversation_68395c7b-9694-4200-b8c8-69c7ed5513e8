// Add these methods to your SQLiteStorage class in sqliteStorage.ts

/**
 * Get all users in the system
 * This method is required for the admin user management functionality
 */
async getAllUsers(): Promise<User[]> {
    try {
        const stmt = this.db.prepare('SELECT id FROM users');
        const userIds = stmt.all();
        
        // Get full user objects with settings
        const users = await Promise.all(
            userIds.map(row => this.getUser(row.id))
        );
        
        return users.filter(user => user !== undefined) as User[];
    } catch (error) {
        console.error('Error fetching all users:', error);
        throw error;
    }
}

/**
 * Delete a user by ID
 * This method is required for the admin user management functionality
 */
async deleteUser(id: number): Promise<void> {
    try {
        // Don't allow deleting the admin user
        const user = await this.getUser(id);
        if (user && user.username === 'admin') {
            throw new Error("Cannot delete the admin user");
        }
        
        this.db.transaction(() => {
            this.db.prepare('DELETE FROM videos WHERE user_id = ?').run(id);
            this.db.prepare('DELETE FROM settings WHERE user_id = ?').run(id);
            this.db.prepare('DELETE FROM users WHERE id = ?').run(id);
        })();
    } catch (error) {
        console.error('Failed to delete user:', error);
        throw error;
    }
}

// Also ensure the admin user has the 'admin' role and isAdmin flag set to true
// Run this SQL command in your database:
// UPDATE users SET role = 'admin', isAdmin = 1 WHERE username = 'admin';
