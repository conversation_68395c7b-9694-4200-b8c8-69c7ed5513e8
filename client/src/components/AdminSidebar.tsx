import { useAuth } from '../hooks/useAuth';
import { Link } from 'react-router-dom';

export default function AdminSidebar() {
  const { isAdmin } = useAuth();

  if (!isAdmin) return null;

  return (
    <div className="admin-sidebar">
      <h3>Admin Panel</h3>
      <ul>
        <li>
          <Link to="/user-management">User Management</Link>
        </li>
        <li>System Settings</li>
      </ul>
    </div>
  );
}

