import { checkAndInstallDependencies } from './server/utils/dependency-checker.js';

console.log('Starting non-blocking dependency check test...');

// Run the dependency check in non-blocking mode
checkAndInstallDependencies(true)
  .then(result => {
    console.log(`Non-blocking dependency check started: ${result}`);
    
    // This will execute immediately since the check is non-blocking
    console.log('Main process continues immediately without waiting');
    
    // Keep the process alive for a bit to see background messages
    setTimeout(() => {
      console.log('Test completed');
    }, 3000);
  })
  .catch(error => {
    console.error('Error during dependency check:', error);
  });
