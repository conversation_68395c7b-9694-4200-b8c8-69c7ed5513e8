import type { Express } from "express";
import { createServer, type Server } from "http";
import { setupAuth } from "./auth";
import { storage } from "./storage";
import { z } from "zod";
import { userSettingsSchema } from "@shared/schema";
import vidiqRouter from "./routes/vidiq";
import keywordGroupsRouter from "./routes/keyword-groups";
import apiKeysRouter from "./routes/api-keys";
import youtubeChannelsRouter from "./routes/youtube-channels";
import youtubeHandleResolverRouter from "./routes/youtube-handle-resolver";
import tasksRouter from "./routes/tasks";
import requestsRouter from "./routes/requests";
import openRouterRouter from "./routes/openrouter";
import ollamaPromptsRouter from "./routes/ollama-prompts"; // Legacy, redirects to OpenRouter
import openRouterPromptsRouter from "./routes/openrouter-prompts";
import openRouterApiKeysRouter from "./routes/openrouter-api-keys";
import youtubeChannelsRawAnalysisRouter from "./routes/youtube-channels-raw-analysis";
import { setupPlaylistRoutes } from "./routes/playlists";
import { setupVideoMetricsRoutes } from "./routes/video-metrics.js";
import { setupYoutubeVideosRoutes } from "./routes/youtube-videos";
import { setupYoutubeSearchRoutes } from "./routes/youtube-search";
import youtubeUtilsRouter from "./routes/youtube-utils";
import { startWorkerManager } from "./services/task-queue";
import { getAvailableOpenRouterModels } from "./services/openrouter-service";
import { getAvailableOllamaModels } from "./services/ollama-service";
import rssFeedsRouter from "./routes/rss-feeds";
import savedArticlesRouter from "./routes/saved-articles";
import clientStateRouter from "./routes/client-state";
import ytrSearchUrlsRouter from "./routes/ytr-search-urls";
import txtTabRouter from "./routes/txt-tab";
import txtProTabRouter from "./routes/txt-pro-tab";
import { createTsRoutes } from "./routes/ts";

export async function registerRoutes(app: Express): Promise<Server> {
  setupAuth(app);

  // Client state routes (for idle detection)
  app.use('/api/client-state', clientStateRouter);

  // VidIQ API routes
  app.use('/api/vidiq', vidiqRouter);

  // Keyword Groups routes
  app.use('/api/keyword-groups', keywordGroupsRouter);

  // API Keys routes
  app.use('/api/api-keys', apiKeysRouter);

  // YouTube Channels routes
  app.use('/api/youtube-channels', youtubeChannelsRouter);

  // YouTube Channels Raw Analysis routes
  app.use('/api', youtubeChannelsRawAnalysisRouter);

  // YouTube Handle Resolver routes
  app.use('/api/youtube-handle-resolver', youtubeHandleResolverRouter);

  // Background tasks routes
  app.use('/api/tasks', tasksRouter);

  // API requests status routes
  app.use('/api/requests', requestsRouter);

  // OpenRouter routes
  app.use('/api/openrouter', openRouterRouter);

  // OpenRouter API Keys routes
  app.use('/api/openrouter-api-keys', openRouterApiKeysRouter);

  // Ollama prompts routes (legacy)
  app.use('/api/ollama-prompts', ollamaPromptsRouter);

  // OpenRouter prompts routes
  app.use('/api/openrouter-prompts', openRouterPromptsRouter);

  // RSS feeds routes
  app.use('/api/rss-feeds', rssFeedsRouter);

  // Saved articles routes
  app.use('/api/saved-articles', savedArticlesRouter);

  // YouTube videos routes
  app.use('/api/youtube-videos', setupYoutubeVideosRoutes(storage));

  // YouTube search routes
  app.use('/api/youtube-search', setupYoutubeSearchRoutes(storage));

  // YouTube utils routes
  app.use('/api/youtube-utils', youtubeUtilsRouter);

  // YTR search URLs routes
  app.use('/api/ytr-search-urls', ytrSearchUrlsRouter);

  // TXT tab routes
  app.use('/api/txt-tab', txtTabRouter);

  // TXT PRO tab routes
  app.use('/api/txt-pro-tab', txtProTabRouter);

  // TS (Transcript) tab routes
  app.use('/api/ts', createTsRoutes(storage));

  // Simple ping endpoint for connectivity checks
  app.get('/ping', (req, res) => {
    res.status(200).send('pong');
  });

  // Playlist routes
  setupPlaylistRoutes(app, storage);

  // Video metrics routes
  setupVideoMetricsRoutes(app, storage);

  // Start the background worker manager
  startWorkerManager();

  // Ollama LLM routes (legacy)
  app.get('/api/ollama/models', async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const models = await getAvailableOllamaModels();
      res.json(models);
    } catch (error) {
      console.error('Error getting Ollama models:', error);
      res.status(500).json({
        message: "Failed to get Ollama models",
        error: error.message,
        hint: "Make sure Ollama is running on your machine at http://localhost:11434"
      });
    }
  });

  // OpenRouter API routes
  app.get('/api/openrouter/models', async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const models = await getAvailableOpenRouterModels();
      res.json(models);
    } catch (error) {
      console.error('Error getting OpenRouter models:', error);
      res.status(500).json({
        message: "Failed to get OpenRouter models",
        error: error.message
      });
    }
  });

  // Admin settings routes
  app.get('/api/admin/settings', async (req, res) => {
    if (!req.isAuthenticated() || !req.user.isAdmin) {
      return res.status(403).json({ message: 'Forbidden: Admin access required' });
    }

    try {
      const settings = await storage.getAdminSettings();
      res.json(settings);
    } catch (error) {
      console.error('Error getting admin settings:', error);
      res.status(500).json({ message: 'Failed to get admin settings' });
    }
  });

  app.put('/api/admin/settings', async (req, res) => {
    if (!req.isAuthenticated() || !req.user.isAdmin) {
      return res.status(403).json({ message: 'Forbidden: Admin access required' });
    }

    try {
      const { serverPort, localhostOnly } = req.body;

      // Validate input
      if (typeof serverPort !== 'number' || serverPort < 1024 || serverPort > 65535) {
        return res.status(400).json({ message: 'Invalid port number. Must be between 1024 and 65535.' });
      }

      if (typeof localhostOnly !== 'boolean') {
        return res.status(400).json({ message: 'Invalid localhostOnly value. Must be a boolean.' });
      }

      const settings = await storage.updateAdminSettings({ serverPort, localhostOnly });

      // Notify that server restart is required
      res.json({
        ...settings,
        message: 'Settings updated. Server restart required for changes to take effect.'
      });
    } catch (error) {
      console.error('Error updating admin settings:', error);
      res.status(500).json({ message: 'Failed to update admin settings' });
    }
  });

  // Test endpoint to check authentication
  app.get('/api/admin/test', (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Forbidden: Admin access required' });
    }

    return res.status(200).json({
      success: true,
      message: 'Authentication successful',
      user: {
        id: req.user.id,
        username: req.user.username,
        role: req.user.role,
        isAdmin: req.user.isAdmin
      }
    });
  });

  // Handle restart for both GET and POST requests
  const handleRestart = (req: any, res: any) => {
    // Check if user is authenticated and is an admin
    if (!req.isAuthenticated()) {
      if (req.method === 'GET') {
        return res.redirect('/auth');
      }
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (!req.user.isAdmin) {
      if (req.method === 'GET') {
        return res.redirect('/');
      }
      return res.status(403).json({ message: 'Forbidden: Admin access required' });
    }

    // Send success response
    if (req.method === 'GET') {
      res.send('<html><body><h1>Restarting application...</h1><script>setTimeout(() => { window.location.href = "/"; }, 5000);</script></body></html>');
    } else {
      res.status(200).json({ success: true, message: 'Restart initiated' });
    }

    // Log the restart request
    console.log(`Restart requested by admin via ${req.method}, shutting down in 1 second...`);

    // Schedule the restart
    setTimeout(() => {
      console.log('Executing restart now');
      process.exit(0);
    }, 1000);
  };

  // Register both GET and POST handlers
  app.get('/api/admin/restart', handleRestart);
  app.post('/api/admin/restart', handleRestart);

  // Direct export endpoint
  app.get('/api/keyword-groups/export', async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const groups = await storage.getKeywordGroups(req.user.id);

      // Format for export (remove IDs and user-specific data)
      const exportData = groups.map(group => ({
        name: group.name,
        keywords: group.keywords,
        excludeWords: group.excludeWords || [],
      }));

      // Set headers for file download
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=keyword-groups.json');

      res.json(exportData);
    } catch (error) {
      console.error('Error exporting keyword groups:', error);
      res.status(500).json({ message: "Failed to export keyword groups" });
    }
  });

  // Admin routes
  app.get('/api/admin/users', async (req, res) => {
    if (!req.isAuthenticated() || !req.user.isAdmin) {
      return res.sendStatus(403);
    }

    try {
      // Get all users without passwords
      const users = await storage.getAllUsers();
      res.json(users.map(({ password, ...user }) => user));
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  app.delete('/api/admin/users/:id', async (req, res) => {
    if (!req.isAuthenticated() || !req.user.isAdmin) {
      return res.sendStatus(403);
    }

    try {
      const userId = parseInt(req.params.id);
      await storage.deleteUser(userId);
      res.sendStatus(200);
    } catch (error) {
      res.status(500).json({ message: "Failed to delete user" });
    }
  });

  // Note: Raw analysis endpoint is now handled by youtubeChannelsRawAnalysisRouter

  // Video feed endpoints
  app.get("/api/videos", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const videos = await storage.getVideos(req.user.id);
      res.json(videos);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch videos" });
    }
  });

  // Trendy videos endpoint (alias for /api/videos)
  app.get("/api/trendy", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const videos = await storage.getVideos(req.user.id);
      res.json(videos);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch trendy videos" });
    }
  });

  app.post("/api/videos/refresh", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const user = await storage.getUser(req.user.id);
      if (!user) return res.sendStatus(401);

      // Import the request queue manager
      const { requestQueueManager, RequestType, RequestPriority } = await import('./services/request-queue-manager');

      // Add the refresh request to the queue with a timeout
      const request = requestQueueManager.addRequest(
        RequestType.VIDEO_REFRESH,
        RequestPriority.NORMAL,
        { userId: req.user.id },
        req.user.id,
        60000 // 60 second timeout
      );

      // Return immediately with the request ID
      res.json({
        requestId: request.id,
        message: "Video refresh has been queued and will be processed in the background.",
        status: request.status
      });
    } catch (error) {
      console.error('Error queueing video refresh:', error);
      res.status(500).json({ message: "Failed to queue video refresh" });
    }
  });

  // Trendy videos refresh endpoint (alias for /api/videos/refresh)
  app.post("/api/trendy/refresh", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const user = await storage.getUser(req.user.id);
      if (!user) return res.sendStatus(401);

      // Import the request queue manager
      const { requestQueueManager, RequestType, RequestPriority } = await import('./services/request-queue-manager');

      // Add the refresh request to the queue with a timeout
      const request = requestQueueManager.addRequest(
        RequestType.VIDEO_REFRESH,
        RequestPriority.NORMAL,
        { userId: req.user.id },
        req.user.id,
        60000 // 60 second timeout
      );

      // Return immediately with the request ID
      res.json({
        requestId: request.id,
        message: "Trendy video refresh has been queued and will be processed in the background.",
        status: request.status
      });
    } catch (error) {
      console.error('Error queueing trendy video refresh:', error);
      res.status(500).json({ message: "Failed to queue trendy video refresh" });
    }
  });

  // Cast queue endpoints
  app.get("/api/cast-queue", async (req, res) => {
    if (!req.isAuthenticated()) {
      console.log('Unauthorized attempt to fetch cast queue');
      return res.sendStatus(401);
    }

    try {
      // Validate user ID
      if (!req.user || !req.user.id) {
        console.error('Invalid user in request');
        return res.status(400).json({ message: "Invalid user" });
      }

      // Get the queue from storage
      const queue = await storage.getCastQueue(req.user.id);

      // Ensure queue is an array
      const safeQueue = Array.isArray(queue) ? queue : [];

      res.json(safeQueue);
    } catch (error) {
      console.error('Error fetching cast queue:', error);
      res.status(500).json({ message: "Failed to fetch cast queue", error: error.message });
    }
  });

  app.post("/api/cast-queue", async (req, res) => {
    if (!req.isAuthenticated()) {
      console.log('Unauthorized attempt to add to cast queue');
      return res.sendStatus(401);
    }

    try {
      console.log('Received request to add to cast queue:', req.body);
      const { video } = req.body;

      if (!video || !video.id) {
        console.log('Invalid video data received:', req.body);
        return res.status(400).json({ message: "Invalid video data" });
      }

      console.log(`Adding video ${video.id} to cast queue for user ${req.user.id}`);
      await storage.addToCastQueue(req.user.id, video);

      console.log(`Fetching updated cast queue for user ${req.user.id}`);
      const queue = await storage.getCastQueue(req.user.id);

      console.log(`Returning updated cast queue with ${queue.length} videos`);
      res.json(queue);
    } catch (error) {
      console.error('Error adding to cast queue:', error);
      res.status(500).json({ message: "Failed to add to cast queue", error: error.message });
    }
  });

  app.delete("/api/cast-queue/:videoId", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const { videoId } = req.params;
      await storage.removeFromCastQueue(req.user.id, videoId);
      const queue = await storage.getCastQueue(req.user.id);
      res.json(queue);
    } catch (error) {
      console.error('Error removing from cast queue:', error);
      res.status(500).json({ message: "Failed to remove from cast queue" });
    }
  });

  app.delete("/api/cast-queue", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      await storage.clearCastQueue(req.user.id);
      res.json([]);
    } catch (error) {
      console.error('Error clearing cast queue:', error);
      res.status(500).json({ message: "Failed to clear cast queue" });
    }
  });

  app.put("/api/cast-queue/position", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const { position } = req.body;
      if (typeof position !== 'number') {
        return res.status(400).json({ message: "Invalid position" });
      }

      await storage.updateCastQueuePosition(req.user.id, position);
      res.json({ success: true, position });
    } catch (error) {
      console.error('Error updating cast queue position:', error);
      res.status(500).json({ message: "Failed to update cast queue position" });
    }
  });

  // Settings endpoints
  app.get("/api/settings", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const settings = await storage.getSettings(req.user.id);
      res.json(settings);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch settings" });
    }
  });

  // Update playback sound setting directly
  app.post('/api/settings/playbackSound', async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      console.log('Received playbackSound update request:', req.body);

      const { playbackSound } = req.body;

      // Explicitly convert to boolean
      const playbackSoundValue = playbackSound === true || playbackSound === 'true';

      // Get current settings
      const currentSettings = await storage.getSettings(req.user.id);

      // Update only the playbackSound setting
      const updatedSettings = {
        ...currentSettings,
        playbackSound: playbackSoundValue
      };

      console.log('Updating playbackSound to:', playbackSoundValue);

      // Save the updated settings
      const result = await storage.updateSettings(req.user.id, updatedSettings);

      console.log('PlaybackSound update successful:', result);
      res.json({ success: true, playbackSound: playbackSoundValue });
    } catch (error) {
      console.error('Error updating playbackSound:', error);
      res.status(500).json({ error: 'Failed to update playbackSound setting' });
    }
  });

  // Update global feed
  app.put("/api/settings/global-feed", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const { keywords, excludeWords } = req.body;

      if (!Array.isArray(keywords) || !Array.isArray(excludeWords)) {
        return res.status(400).json({ message: "Invalid request format. 'keywords' and 'excludeWords' must be arrays." });
      }

      // Get current settings to preserve other values
      const currentSettings = await storage.getSettings(req.user.id);
      if (!currentSettings) {
        return res.status(404).json({ message: "User settings not found" });
      }

      // Update only the keywords and exclude words
      const updatedSettings = await storage.updateSettings(req.user.id, {
        ...currentSettings,
        searchKeywords: keywords,
        excludeWords: excludeWords
      });
      res.json(updatedSettings);
    } catch (error) {
      console.error('Error updating global feed:', error);
      res.status(500).json({ message: "Failed to update global feed" });
    }
  });

  app.put("/api/settings", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      console.log('Received settings update request:', req.body);

      // Convert lastRefreshTime from string to Date if it's a string
      const requestData = { ...req.body };
      if (requestData.lastRefreshTime && typeof requestData.lastRefreshTime === 'string') {
        try {
          requestData.lastRefreshTime = new Date(requestData.lastRefreshTime);
        } catch (e) {
          console.error('Error converting lastRefreshTime to Date:', e);
          requestData.lastRefreshTime = null;
        }
      }

      // Ensure all boolean values are properly handled
      // Handle autoAnalyzeOnRefresh
      if ('autoAnalyzeOnRefresh' in requestData) {
        // Force to a boolean value by strictly comparing to true
        const boolValue = requestData.autoAnalyzeOnRefresh === true;
        requestData.autoAnalyzeOnRefresh = boolValue;
        console.log('Explicitly set autoAnalyzeOnRefresh to:', boolValue, '(type:', typeof boolValue, ')');
      } else {
        // If not provided, get the current value from settings
        try {
          const currentSettings = await storage.getSettings(req.user.id);
          if (currentSettings && currentSettings.autoAnalyzeOnRefresh !== undefined) {
            // Ensure it's a proper boolean
            requestData.autoAnalyzeOnRefresh = currentSettings.autoAnalyzeOnRefresh === true;
            console.log('Using current autoAnalyzeOnRefresh value:', requestData.autoAnalyzeOnRefresh);
          } else {
            // Default to true if not set
            requestData.autoAnalyzeOnRefresh = true;
            console.log('Setting default autoAnalyzeOnRefresh to true');
          }
        } catch (e) {
          console.error('Error getting current settings for autoAnalyzeOnRefresh:', e);
          // Default to true if there's an error
          requestData.autoAnalyzeOnRefresh = true;
        }
      }

      // Handle other boolean values
      ['darkMode', 'removeDuplicates', 'useInAppPlayer', 'previewSound'].forEach(boolField => {
        if (boolField in requestData) {
          requestData[boolField] = requestData[boolField] === true;
          console.log(`Explicitly set ${boolField} to:`, requestData[boolField]);
        }
      });

      // Check if this is a direct playbackSound update
      if (Object.keys(requestData).length === 1 && requestData.playbackSound !== undefined) {
        console.log('Direct playbackSound update detected');

        // Convert to boolean if it's not already
        if (requestData.playbackSound === false || requestData.playbackSound === 'false' || requestData.playbackSound === 0) {
          requestData.playbackSound = false;
          console.log('Explicitly set playbackSound to FALSE');
        } else {
          requestData.playbackSound = true;
          console.log('Explicitly set playbackSound to TRUE');
        }

        // Get current settings to preserve other values
        const currentSettings = await storage.getSettings(req.user.id);
        if (currentSettings) {
          // Merge current settings with the updated playbackSound value
          requestData = {
            ...currentSettings,
            playbackSound: requestData.playbackSound
          };
          console.log('Merged with current settings:', requestData);
        }
      }
      // Regular settings update
      else {
        // Ensure playbackSound is included in the request and properly typed
        if (requestData.playbackSound === undefined) {
          // Get current settings to preserve playbackSound value
          const currentSettings = await storage.getSettings(req.user.id);
          if (currentSettings && currentSettings.playbackSound !== undefined) {
            requestData.playbackSound = currentSettings.playbackSound;
          } else {
            requestData.playbackSound = true; // Default value
          }
        } else {
          // Convert to boolean if it's not already
          if (requestData.playbackSound === false || requestData.playbackSound === 'false' || requestData.playbackSound === 0) {
            requestData.playbackSound = false;
            console.log('Explicitly set playbackSound to FALSE');
          } else {
            requestData.playbackSound = true;
            console.log('Explicitly set playbackSound to TRUE');
          }
        }
      }

      const settings = userSettingsSchema.parse(requestData);
      console.log('Parsed settings:', settings);
      const updatedSettings = await storage.updateSettings(req.user.id, settings);
      console.log('Settings updated successfully:', updatedSettings);
      res.json(updatedSettings);
    } catch (error) {
      console.error('Error updating settings:', error);
      if (error instanceof z.ZodError) {
        console.error('Validation errors:', error.errors);
        res.status(400).json({ message: "Invalid settings data", errors: error.errors });
      } else {
        res.status(500).json({ message: "Failed to update settings" });
      }
    }
  });

  app.delete("/api/data", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      await storage.deleteUserData(req.user.id);
      res.sendStatus(200);
    } catch (error) {
      res.status(500).json({ message: "Failed to delete user data" });
    }
  });

  // Admin endpoints

  // Middleware to check if user is admin
  const isAdmin = (req: express.Request, res: express.Response, next: express.NextFunction) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    const user = req.user as User;
    if (user.role !== 'admin' && !user.isAdmin) {
      return res.status(403).json({ message: "Forbidden: Admin access required" });
    }

    next();
  };

  // Get all users (admin only)
  app.get("/api/admin/users", isAdmin, async (req, res) => {
    try {
      const users = await storage.getAllUsers();
      res.json(users);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  // Add a new user (admin only)
  app.post("/api/admin/users", isAdmin, async (req, res) => {
    try {
      const { username, password, role } = req.body;

      if (!username || !password) {
        return res.status(400).json({ message: "Username and password are required" });
      }

      const isAdmin = role === 'admin';
      const newUser = await storage.createUser(username, password, role, isAdmin);
      res.status(201).json(newUser);
    } catch (error) {
      res.status(500).json({ message: "Failed to create user" });
    }
  });

  // Delete a user (admin only)
  app.delete("/api/admin/users/:id", isAdmin, async (req, res) => {
    try {
      const userId = Number(req.params.id);
      await storage.deleteUser(userId);
      res.sendStatus(200);
    } catch (error) {
      res.status(500).json({ message: "Failed to delete user" });
    }
  });

  // Update a user's role (admin only)
  app.patch("/api/admin/users/:id", isAdmin, async (req, res) => {
    try {
      const userId = Number(req.params.id);
      const { role, isAdmin } = req.body;

      const updatedUser = await storage.updateUserRole(userId, role, isAdmin);
      res.json(updatedUser);
    } catch (error) {
      res.status(500).json({ message: "Failed to update user" });
    }
  });

  // Get admin dashboard stats
  app.get("/api/admin/stats", isAdmin, async (req, res) => {
    try {
      const stats = await storage.getAdminStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch stats" });
    }
  });

  // Vacuum database endpoint
  app.post('/api/admin/vacuum-database', isAdmin, async (req, res) => {
    try {
      const result = await storage.vacuumDatabase();
      res.json({
        success: true,
        message: `Database vacuum completed successfully. Space reclaimed: ${result.spaceReclaimed} MB`,
        ...result
      });
    } catch (error) {
      console.error('Error vacuuming database:', error);
      res.status(500).json({
        success: false,
        message: "Failed to vacuum database",
        error: error.message
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
