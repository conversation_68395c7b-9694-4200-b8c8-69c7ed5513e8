import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Brain,
  Send,
  FileText,
  MessageSquare,
  Loader2,
  Copy,
  Download,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  ExternalLink,
  Power
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';

interface ChatGptIntegrationProps {
  transcripts: any[];
  prompts: any[];
  onTranscriptUpdated: (transcript: any) => void;
}

export function ChatGptIntegration({
  transcripts,
  prompts,
  onTranscriptUpdated
}: ChatGptIntegrationProps) {
  // Server status state
  const [serverStatus, setServerStatus] = useState<{
    connected: boolean;
    serverUrl: string;
    lastCheck: Date | null;
    checking: boolean;
  }>({
    connected: false,
    serverUrl: 'http://localhost:5000',
    lastCheck: null,
    checking: false
  });

  // Direct chat interface state
  const [chatPrompt, setChatPrompt] = useState('');
  const [chatTranscript, setChatTranscript] = useState('');
  const [chatResponse, setChatResponse] = useState('');
  const [isChatProcessing, setIsChatProcessing] = useState(false);
  const [chatHistory, setChatHistory] = useState<any[]>([]);

  // Legacy interface state
  const [selectedTranscript, setSelectedTranscript] = useState<string>('');
  const [selectedPrompt, setSelectedPrompt] = useState<string>('');
  const [customPrompt, setCustomPrompt] = useState('');
  const [useCustomPrompt, setUseCustomPrompt] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedResult, setProcessedResult] = useState<string>('');
  const [processingHistory, setProcessingHistory] = useState<any[]>([]);

  const { toast } = useToast();

  // Check server status
  const checkServerStatus = async () => {
    setServerStatus(prev => ({ ...prev, checking: true }));

    try {
      const response = await fetch('/api/txt-tab/chatgpt/status');
      if (response.ok) {
        const status = await response.json();
        setServerStatus({
          connected: status.connected,
          serverUrl: status.serverUrl,
          lastCheck: status.lastCheck ? new Date(status.lastCheck) : new Date(),
          checking: false
        });
      } else {
        setServerStatus(prev => ({
          ...prev,
          connected: false,
          lastCheck: new Date(),
          checking: false
        }));
      }
    } catch (error) {
      setServerStatus(prev => ({
        ...prev,
        connected: false,
        lastCheck: new Date(),
        checking: false
      }));
    }
  };

  // Initialize login
  const initializeLogin = async () => {
    try {
      const response = await fetch('/api/txt-tab/chatgpt/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();
      if (result.success) {
        toast({
          title: "Login Initialized",
          description: "Please complete login in the browser window that opened",
        });
        // Check status after a delay to see if login was successful
        setTimeout(checkServerStatus, 3000);
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to initialize login",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to initialize login",
        variant: "destructive",
      });
    }
  };

  // Check server status on component mount and periodically
  useEffect(() => {
    checkServerStatus();
    const interval = setInterval(checkServerStatus, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);

  // Direct chat processing
  const handleDirectChat = async () => {
    if (!chatPrompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a prompt",
        variant: "destructive",
      });
      return;
    }

    // Check server connection first
    if (!serverStatus.connected) {
      toast({
        title: "Server Not Connected",
        description: "Please start the ChatGPT server first",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsChatProcessing(true);

      const response = await fetch('/api/txt-tab/chatgpt/direct-chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: chatPrompt.trim(),
          transcript: chatTranscript.trim() || undefined
        })
      });

      if (response.ok) {
        const result = await response.json();

        if (result.success) {
          setChatResponse(result.response || '');

          // Add to chat history
          const historyEntry = {
            id: Date.now(),
            timestamp: new Date(),
            prompt: chatPrompt.trim(),
            transcript: chatTranscript.trim(),
            response: result.response || '',
            success: true
          };

          setChatHistory(prev => [historyEntry, ...prev.slice(0, 9)]); // Keep last 10

          toast({
            title: "Success",
            description: "ChatGPT response received",
          });
        } else {
          throw new Error(result.error || 'Processing failed');
        }
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to process with ChatGPT');
      }
    } catch (error) {
      console.error('Error processing with ChatGPT:', error);

      // Add failed entry to history
      const historyEntry = {
        id: Date.now(),
        timestamp: new Date(),
        prompt: chatPrompt.trim(),
        transcript: chatTranscript.trim(),
        response: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };

      setChatHistory(prev => [historyEntry, ...prev.slice(0, 9)]);

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process with ChatGPT",
        variant: "destructive",
      });
    } finally {
      setIsChatProcessing(false);
    }
  };

  const handleProcess = async () => {
    if (!selectedTranscript) {
      toast({
        title: "Error",
        description: "Please select a transcript to process",
        variant: "destructive",
      });
      return;
    }

    // Check server connection first
    if (!serverStatus.connected) {
      toast({
        title: "Server Not Connected",
        description: "Please start the ChatGPT server first",
        variant: "destructive",
      });
      return;
    }

    if (!useCustomPrompt && !selectedPrompt) {
      toast({
        title: "Error",
        description: "Please select a prompt or enter a custom prompt",
        variant: "destructive",
      });
      return;
    }

    if (useCustomPrompt && !customPrompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a custom prompt",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);
      
      const requestData: any = {
        transcriptId: parseInt(selectedTranscript)
      };

      if (useCustomPrompt) {
        requestData.customPrompt = customPrompt.trim();
      } else {
        requestData.promptId = parseInt(selectedPrompt);
      }

      const response = await fetch('/api/txt-tab/chatgpt/process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });

      if (response.ok) {
        const result = await response.json();
        
        if (result.success) {
          setProcessedResult(result.processedContent || '');
          
          // Add to processing history
          const transcript = transcripts.find(t => t.id === parseInt(selectedTranscript));
          const prompt = useCustomPrompt 
            ? { name: 'Custom Prompt', content: customPrompt }
            : prompts.find(p => p.id === parseInt(selectedPrompt));
          
          const historyEntry = {
            id: Date.now(),
            timestamp: new Date(),
            transcript: transcript?.title || 'Unknown',
            prompt: prompt?.name || 'Unknown',
            result: result.processedContent || '',
            success: true
          };
          
          setProcessingHistory(prev => [historyEntry, ...prev.slice(0, 9)]); // Keep last 10
          
          toast({
            title: "Success",
            description: result.message || "Transcript processed successfully",
          });
        } else {
          throw new Error(result.error || 'Processing failed');
        }
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to process transcript');
      }
    } catch (error) {
      console.error('Error processing transcript:', error);
      
      // Add failed entry to history
      const historyEntry = {
        id: Date.now(),
        timestamp: new Date(),
        transcript: transcripts.find(t => t.id === parseInt(selectedTranscript))?.title || 'Unknown',
        prompt: useCustomPrompt ? 'Custom Prompt' : prompts.find(p => p.id === parseInt(selectedPrompt))?.name || 'Unknown',
        result: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };
      
      setProcessingHistory(prev => [historyEntry, ...prev.slice(0, 9)]);
      
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process transcript",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Success",
        description: "Copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const downloadResult = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const selectedTranscriptData = transcripts.find(t => t.id === parseInt(selectedTranscript));
  const selectedPromptData = prompts.find(p => p.id === parseInt(selectedPrompt));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Brain className="h-5 w-5" />
          ChatGPT Integration
        </h3>
        <p className="text-sm text-muted-foreground">
          Process your transcripts with AI to generate new content, improve scripts, or analyze text
        </p>
      </div>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Processing Configuration</CardTitle>
          <CardDescription>
            Select a transcript and prompt to process with ChatGPT
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Transcript Selection */}
          <div className="space-y-2">
            <Label>Select Transcript</Label>
            <Select value={selectedTranscript} onValueChange={setSelectedTranscript}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a transcript to process..." />
              </SelectTrigger>
              <SelectContent>
                {transcripts.map((transcript) => (
                  <SelectItem key={transcript.id} value={transcript.id.toString()}>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span className="truncate">{transcript.title}</span>
                      <Badge variant="outline" className="text-xs">
                        {new Date(transcript.updated_at).toLocaleDateString()}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {selectedTranscriptData && (
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm font-medium mb-1">Preview</p>
                <p className="text-xs text-muted-foreground">
                  {selectedTranscriptData.content.substring(0, 200)}
                  {selectedTranscriptData.content.length > 200 ? '...' : ''}
                </p>
              </div>
            )}
          </div>

          {/* Prompt Selection */}
          <div className="space-y-2">
            <div className="flex items-center gap-4">
              <Label>Prompt Type</Label>
              <div className="flex items-center gap-2">
                <input
                  type="radio"
                  id="predefined"
                  name="promptType"
                  checked={!useCustomPrompt}
                  onChange={() => setUseCustomPrompt(false)}
                />
                <label htmlFor="predefined" className="text-sm">Predefined</label>
                
                <input
                  type="radio"
                  id="custom"
                  name="promptType"
                  checked={useCustomPrompt}
                  onChange={() => setUseCustomPrompt(true)}
                />
                <label htmlFor="custom" className="text-sm">Custom</label>
              </div>
            </div>

            {!useCustomPrompt ? (
              <div className="space-y-2">
                <Select value={selectedPrompt} onValueChange={setSelectedPrompt}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a predefined prompt..." />
                  </SelectTrigger>
                  <SelectContent>
                    {prompts.map((prompt) => (
                      <SelectItem key={prompt.id} value={prompt.id.toString()}>
                        <div className="flex items-center gap-2">
                          <MessageSquare className="h-4 w-4" />
                          <span>{prompt.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {prompt.category}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {selectedPromptData && (
                  <div className="p-3 bg-muted rounded-md">
                    <p className="text-sm font-medium mb-1">Prompt Preview</p>
                    <p className="text-xs text-muted-foreground">
                      {selectedPromptData.content.substring(0, 200)}
                      {selectedPromptData.content.length > 200 ? '...' : ''}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <Textarea
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="Enter your custom prompt here..."
                  className="min-h-[100px]"
                />
              </div>
            )}
          </div>

          {/* Process Button */}
          <Button 
            onClick={handleProcess} 
            disabled={isProcessing || !selectedTranscript}
            className="w-full"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Process with ChatGPT
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Results */}
      {processedResult && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-base">Processed Result</CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(processedResult)}
                >
                  <Copy className="h-4 w-4 mr-1" />
                  Copy
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => downloadResult(processedResult, 'chatgpt-result.txt')}
                >
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[300px] border rounded-md p-4">
              <pre className="whitespace-pre-wrap text-sm">{processedResult}</pre>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Processing History */}
      {processingHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Processing History</CardTitle>
            <CardDescription>
              Recent ChatGPT processing results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[200px]">
              <div className="space-y-3">
                {processingHistory.map((entry) => (
                  <div 
                    key={entry.id}
                    className={`p-3 rounded-md border ${
                      entry.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {entry.transcript} → {entry.prompt}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {entry.timestamp.toLocaleString()}
                        </p>
                      </div>
                      <Badge variant={entry.success ? "default" : "destructive"} className="text-xs">
                        {entry.success ? "Success" : "Failed"}
                      </Badge>
                    </div>
                    
                    {entry.success && entry.result && (
                      <div className="mt-2">
                        <p className="text-xs text-muted-foreground mb-1">Result:</p>
                        <p className="text-xs">
                          {entry.result.substring(0, 100)}
                          {entry.result.length > 100 ? '...' : ''}
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="mt-1 h-6 px-2 text-xs"
                          onClick={() => copyToClipboard(entry.result)}
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </Button>
                      </div>
                    )}
                    
                    {!entry.success && entry.error && (
                      <div className="mt-2">
                        <p className="text-xs text-red-600">Error: {entry.error}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Server Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            ChatGPT Server Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Connection Status */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {serverStatus.checking ? (
                  <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                ) : serverStatus.connected ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm font-medium">
                  {serverStatus.checking ? 'Checking...' :
                   serverStatus.connected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={checkServerStatus}
                disabled={serverStatus.checking}
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${serverStatus.checking ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>

            {/* Server URL */}
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <ExternalLink className="h-4 w-4" />
              <span>Server: {serverStatus.serverUrl}</span>
            </div>

            {/* Last Check */}
            {serverStatus.lastCheck && (
              <div className="text-xs text-muted-foreground">
                Last checked: {serverStatus.lastCheck.toLocaleTimeString()}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              {!serverStatus.connected && (
                <div className="flex flex-col gap-2 w-full">
                  <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <div className="text-sm text-yellow-800">
                      <p className="font-medium">ChatGPT Server Not Running</p>
                      <p className="text-xs mt-1">
                        Please start the ChatGPT server manually using the startup script.
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      toast({
                        title: "Manual Server Startup Required",
                        description: "Please run the start_chatgpt_server.sh script in the ChatGPT Server directory",
                      });
                    }}
                  >
                    <Power className="h-4 w-4 mr-1" />
                    How to Start Server
                  </Button>
                </div>
              )}

              {serverStatus.connected && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={initializeLogin}
                >
                  <Brain className="h-4 w-4 mr-1" />
                  Initialize Login
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
