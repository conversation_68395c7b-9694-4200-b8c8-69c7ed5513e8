import { useState, useEffect } from 'react';

export interface ToastProps {
  id?: string;
  title?: string;
  description: string;
  variant?: 'default' | 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  onDismiss?: (id: string) => void;
}

export function Toast({
  id = '',
  title,
  description,
  variant = 'default',
  duration = 3000,
  onDismiss,
}: ToastProps) {
  const [isOpen, setIsOpen] = useState(true);

  useEffect(() => {
    if (!duration) return;

    const timer = setTimeout(() => {
      setIsOpen(false);
      if (onDismiss) onDismiss(id);
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, id, onDismiss]);

  if (!isOpen) return null;

  return (
    <div className={`toast toast-${variant}`}>
      {title && <div className="toast-title">{title}</div>}
      <div className="toast-description">{description}</div>
    </div>
  );
}

