import React from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Define a type for view history data with timestamps
export interface ViewHistoryEntry {
  views: number;
  timestamp: number;
}

interface ViewHistoryGraphProps {
  videoId: string;
  currentViews: number;
  viewHistory?: ViewHistoryEntry[];
  height?: number;
  width?: number;
  maxHours?: number;
}

// Use React.memo to prevent unnecessary re-renders
export const ViewHistoryGraph = React.memo(function ViewHistoryGraph({
  videoId,
  currentViews,
  viewHistory = [],
  height = 20,
  width = 80, // Increased width to accommodate 24 bars
  maxHours = 24 // Changed from 12 to 24 hours
}: ViewHistoryGraphProps) {
  const now = Date.now();

  // First, get all entries from the last maxHours
  const recentHistory = viewHistory.filter(entry => {
    const hoursDiff = (now - entry.timestamp) / (1000 * 60 * 60);
    return hoursDiff <= maxHours;
  });

  // Create an array of hourly timestamps (one for each hour in the last maxHours)
  const hourlyTimestamps: number[] = [];
  for (let i = 0; i < maxHours; i++) {
    // Calculate timestamp for each hour, starting from maxHours ago to now
    const hourTimestamp = now - ((maxHours - i) * 60 * 60 * 1000);
    hourlyTimestamps.push(hourTimestamp);
  }

  // Find the closest data point for each hourly timestamp
  const hourlyDataPoints: ViewHistoryEntry[] = [];

  // For each hourly timestamp, find the closest data point
  hourlyTimestamps.forEach(timestamp => {
    // Find all entries within 30 minutes of this hour
    const entriesForThisHour = viewHistory.filter(entry => {
      const timeDiff = Math.abs(entry.timestamp - timestamp);
      return timeDiff <= 30 * 60 * 1000; // Within 30 minutes
    });

    if (entriesForThisHour.length > 0) {
      // If we have entries for this hour, use the closest one
      const closestEntry = entriesForThisHour.reduce((closest, entry) => {
        const currentDiff = Math.abs(entry.timestamp - timestamp);
        const closestDiff = Math.abs(closest.timestamp - timestamp);
        return currentDiff < closestDiff ? entry : closest;
      }, entriesForThisHour[0]);

      hourlyDataPoints.push(closestEntry);
    } else {
      // If we don't have an entry for this hour, interpolate or use the closest one
      // Find the closest entries before and after this timestamp
      const entriesBefore = viewHistory.filter(entry => entry.timestamp < timestamp)
        .sort((a, b) => b.timestamp - a.timestamp);
      const entriesAfter = viewHistory.filter(entry => entry.timestamp > timestamp)
        .sort((a, b) => a.timestamp - b.timestamp);

      const closestBefore = entriesBefore.length > 0 ? entriesBefore[0] : null;
      const closestAfter = entriesAfter.length > 0 ? entriesAfter[0] : null;

      if (closestBefore && closestAfter) {
        // Interpolate between the two closest entries
        const totalTimeDiff = closestAfter.timestamp - closestBefore.timestamp;
        const currentTimeDiff = timestamp - closestBefore.timestamp;
        const ratio = totalTimeDiff > 0 ? currentTimeDiff / totalTimeDiff : 0;

        const interpolatedViews = Math.round(
          closestBefore.views + ratio * (closestAfter.views - closestBefore.views)
        );

        hourlyDataPoints.push({
          views: interpolatedViews,
          timestamp: timestamp
        });
      } else if (closestBefore) {
        // Use the closest entry before
        hourlyDataPoints.push({
          views: closestBefore.views,
          timestamp: timestamp
        });
      } else if (closestAfter) {
        // Use the closest entry after
        hourlyDataPoints.push({
          views: closestAfter.views,
          timestamp: timestamp
        });
      } else if (viewHistory.length > 0) {
        // If we have any history at all, use the most recent entry
        const mostRecent = [...viewHistory].sort((a, b) => b.timestamp - a.timestamp)[0];
        hourlyDataPoints.push({
          views: mostRecent.views,
          timestamp: timestamp
        });
      }
      // If we have no history at all, this hour will be skipped
    }
  });

  // Filter out any undefined entries and sort by timestamp
  const filteredHistory = hourlyDataPoints.filter(Boolean).sort((a, b) => a.timestamp - b.timestamp);

  // If we still don't have enough data points, include the original recent history
  if (filteredHistory.length < maxHours && recentHistory.length > 0) {
    // Add any recent entries that aren't already included
    recentHistory.forEach(entry => {
      if (!filteredHistory.some(e => Math.abs(e.timestamp - entry.timestamp) < 5 * 60 * 1000)) {
        filteredHistory.push(entry);
      }
    });

    // Sort again after adding
    filteredHistory.sort((a, b) => a.timestamp - b.timestamp);
  }

  // Add current views to history
  const allHistory = [
    ...filteredHistory,
    { views: currentViews, timestamp: now }
  ];

  // Sort by timestamp (oldest first)
  allHistory.sort((a, b) => a.timestamp - b.timestamp);

  // ONLY show real data - NO fake/mock/placeholder data
  const displayData = React.useMemo(() => {
    // If we have real historical data, use it
    if (viewHistory && viewHistory.length > 0) {
      // Sort real data by timestamp (oldest to newest)
      const sortedHistory = [...viewHistory].sort((a, b) => a.timestamp - b.timestamp);

      // Take the most recent data points (up to maxHours)
      const recentHistory = sortedHistory.slice(-maxHours);

      // Extract view counts
      const realData = recentHistory.map(entry => Math.max(1, entry.views));

      console.log(`ViewHistoryGraph: Using ${realData.length} real data points for video ${videoId}`);
      return realData;
    }

    // NO REAL DATA = NO CHART
    // Return empty array to indicate no data available
    console.log(`ViewHistoryGraph: No real data available for video ${videoId}, showing no chart`);
    return [];
  }, [viewHistory, maxHours, videoId]);

  // Memoize calculations to reduce CPU usage
  const { maxValue, minValue, percentChange, trendColor } = React.useMemo(() => {
    // If no real data, return defaults
    if (displayData.length === 0) {
      return { maxValue: 0, minValue: 0, percentChange: 0, trendColor: 'bg-gray-500' };
    }

    // Calculate max and min values for proper scaling
    const maxVal = Math.max(...displayData);
    const minVal = Math.min(...displayData);

    // Calculate percentage change over the entire period
    const pctChange = displayData.length > 1
      ? ((displayData[displayData.length - 1] - displayData[0]) / displayData[0]) * 100
      : 0;

    // Determine color based on trend
    const color = pctChange > 0 ? 'bg-green-500' : pctChange < 0 ? 'bg-red-500' : 'bg-gray-500';

    return { maxValue: maxVal, minValue: minVal, percentChange: pctChange, trendColor: color };
  }, [displayData]);

  // Format a timestamp for display - memoized to avoid recreating on every render
  const formatTime = React.useCallback((timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }, []);

  // Calculate hours ago for a timestamp - memoized to avoid recreating on every render
  const getHoursAgo = React.useCallback((timestamp: number) => {
    const hoursDiff = (now - timestamp) / (1000 * 60 * 60);
    if (hoursDiff < 1) {
      const minutesDiff = Math.floor((now - timestamp) / (1000 * 60));
      return `${minutesDiff} min ago`;
    }
    return `${Math.floor(hoursDiff)}h ago`;
  }, [now]);

  // Prepare table rows for view history - moved outside of JSX for proper hook usage
  const historyTableRows = React.useMemo(() => {
    // Show all hourly data points
    const entriesToRender = [...hourlyDataPoints];

    // Add the current views as the latest entry if not already included
    if (entriesToRender.length > 0 &&
        entriesToRender[entriesToRender.length - 1].timestamp !== now) {
      entriesToRender.push({ views: currentViews, timestamp: now });
    }

    // Sort by timestamp (newest first) for display
    entriesToRender.sort((a, b) => b.timestamp - a.timestamp);

    // Limit to 12 entries maximum (one per hour)
    const limitedEntries = entriesToRender.slice(0, 12);

    return limitedEntries.map((entry, index) => {
      // Calculate change from previous hour
      const prevEntry = index < limitedEntries.length - 1 ? limitedEntries[index + 1] : null;

      // If we have a previous entry, calculate hourly change
      let change = 0;
      if (prevEntry && prevEntry.views > 0) {
        change = ((entry.views - prevEntry.views) / prevEntry.views) * 100;
      }

      const changeClass = change > 0 ? 'text-green-400' : change < 0 ? 'text-red-400' : '';

      // Format the time as hour:minute
      const entryTime = new Date(entry.timestamp);
      const timeStr = entryTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

      // Determine if this is the current hour
      const isCurrentHour = index === 0;

      return (
        <tr key={entry.timestamp} className={isCurrentHour ? 'font-medium' : ''}>
          <td className="text-left">
            {isCurrentHour ? 'Now' : timeStr}
          </td>
          <td className="text-right">{entry.views.toLocaleString()}</td>
          <td className={`text-right ${changeClass}`}>
            {prevEntry ? (change !== 0 ? `${change > 0 ? '+' : ''}${change.toFixed(1)}%` : '-') : ''}
          </td>
        </tr>
      );
    });
  }, [hourlyDataPoints, currentViews, now]);

  // Memoize the chart bars to avoid recreating on every render
  const chartBars = React.useMemo(() => {
    // If no real data, return null
    if (displayData.length === 0) {
      return null;
    }

    // Pre-calculate bar width once
    const barWidth = Math.max(2, (width / displayData.length) - 0.5);
    const valueRange = maxValue - minValue;

    return displayData.map((value, index) => {
      // Calculate bar height with proper scaling using the full range
      let normalizedHeight;
      if (valueRange > 0) {
        // Scale based on the range from min to max
        normalizedHeight = ((value - minValue) / valueRange) * (height - 4);
      } else {
        // All values are the same, use half height
        normalizedHeight = (height - 4) / 2;
      }

      const barHeight = Math.max(2, normalizedHeight + 2); // Minimum 2px height, +2 for visibility

      // Determine color based on overall trend and position in sequence
      let barColor = '#6b7280'; // Default gray

      // Calculate the position in the sequence (0 to 1)
      const positionRatio = index / (displayData.length - 1);

      // Determine color based on overall trend and individual bar position
      if (percentChange > 5) {
        // Strong upward trend - use green gradient
        const intensity = 0.5 + (positionRatio * 0.5); // 0.5 to 1.0
        barColor = `rgba(34, 197, 94, ${intensity})`; // Green with increasing intensity
      } else if (percentChange < -5) {
        // Strong downward trend - use red gradient
        const intensity = 1.0 - (positionRatio * 0.5); // 1.0 to 0.5
        barColor = `rgba(239, 68, 68, ${intensity})`; // Red with decreasing intensity
      } else if (percentChange > 0) {
        // Mild upward trend - light green
        barColor = `rgba(132, 204, 22, ${0.6 + positionRatio * 0.4})`; // Light green
      } else if (percentChange < 0) {
        // Mild downward trend - orange
        barColor = `rgba(249, 115, 22, ${0.8 - positionRatio * 0.3})`; // Orange
      } else {
        // No change - gray
        barColor = '#6b7280';
      }

      // For individual bar comparison, add slight color variation
      if (index > 0) {
        const prevValue = displayData[index - 1];
        const localChange = ((value - prevValue) / prevValue) * 100;

        if (Math.abs(localChange) > 10) {
          // Significant local change - enhance the color
          if (localChange > 0) {
            barColor = '#22c55e'; // Bright green for significant increase
          } else {
            barColor = '#ef4444'; // Bright red for significant decrease
          }
        }
      }

      return (
        <div
          key={`${videoId}-bar-${index}`}
          style={{
            position: 'absolute',
            bottom: 0,
            height: `${barHeight}px`,
            width: `${barWidth}px`,
            left: `${index * (width / displayData.length)}px`,
            backgroundColor: barColor,
            borderRadius: '1px 1px 0 0',
            opacity: 0.9,
            border: '1px solid rgba(255,255,255,0.1)',
            transition: 'all 0.3s ease' // Smooth transitions when data updates
          }}
          title={`${value.toLocaleString()} views`}
        />
      );
    });
  }, [displayData, maxValue, minValue, height, width, percentChange, videoId]);

  // If no real data, show a simple placeholder
  if (displayData.length === 0) {
    return (
      <div
        className="flex items-center justify-center bg-gray-800/30 rounded border border-gray-600/30"
        style={{ height: `${height}px`, width: `${width}px` }}
        title="No view history data available yet"
      >
        <span className="text-xs text-gray-400">No data</span>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="relative" style={{ height: `${height}px`, width: `${width}px` }}>
            {chartBars}
          </div>
        </TooltipTrigger>
        <TooltipContent side="right" className="w-64">
          <div className="text-xs">
            <p className="font-bold mb-1">Hourly View History (Last {maxHours} Hours)</p>
            <p className="text-[10px] text-muted-foreground">Showing {maxHours} hourly bars with view count changes</p>
            <p>Current: {currentViews.toLocaleString()} views (now)</p>

            {allHistory.length > 1 && (
              <div className="mt-2">
                <p className="font-medium">Change: {percentChange > 0 ? '+' : ''}{percentChange.toFixed(1)}% over period</p>

                <div className="mt-1 max-h-32 overflow-y-auto">
                  <table className="w-full text-xs">
                    <thead>
                      <tr>
                        <th className="text-left">Time</th>
                        <th className="text-right">Views</th>
                        <th className="text-right">Change</th>
                      </tr>
                    </thead>
                    <tbody>
                      {historyTableRows}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
});

// NO FAKE DATA GENERATION - Only real data is displayed
