import axios from 'axios';
import { OpenRouterPrompt as Prompt } from '@shared/schema';

// Helper function for consistent logging
const logWithTimestamp = (message: string, data?: any) => {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0]; // HH:MM:SS format
  console.log(`[${timestamp}] 🔌 PromptsService: ${message}`, data ? data : '');
};

/**
 * Get all prompts for the current user
 */
export async function getPrompts(): Promise<Prompt[]> {
  logWithTimestamp('Fetching all prompts');
  try {
    const response = await axios.get('/api/openrouter-prompts');
    logWithTimestamp(`Successfully fetched ${response.data.length} prompts`, response.data);
    return response.data;
  } catch (error) {
    logWithTimestamp('Error fetching prompts', error);
    throw error;
  }
}

/**
 * Get a specific prompt by ID
 */
export async function getPrompt(promptId: number): Promise<Prompt> {
  logWithTimestamp(`Fetching prompt with ID ${promptId}`);
  try {
    const response = await axios.get(`/api/openrouter-prompts/${promptId}`);
    logWithTimestamp(`Successfully fetched prompt ID ${promptId}`, response.data);
    return response.data;
  } catch (error) {
    logWithTimestamp(`Error fetching prompt ID ${promptId}`, error);
    throw error;
  }
}

/**
 * Create a new prompt
 */
export async function createPrompt(
  name: string,
  promptText: string,
  isDefault: boolean = false
): Promise<Prompt> {
  logWithTimestamp('Creating new prompt', { name, promptTextLength: promptText.length, isDefault });
  try {
    const response = await axios.post('/api/openrouter-prompts', {
      name,
      promptText,
      isDefault
    });
    logWithTimestamp('Successfully created new prompt', response.data);
    return response.data;
  } catch (error) {
    logWithTimestamp('Error creating prompt', error);
    throw error;
  }
}

/**
 * Update an existing prompt
 */
export async function updatePrompt(
  promptId: number,
  updates: Partial<Prompt>
): Promise<Prompt> {
  logWithTimestamp(`Updating prompt ID ${promptId}`, {
    updates,
    nameUpdate: updates.name,
    textLengthUpdate: updates.promptText ? updates.promptText.length : 'unchanged'
  });
  try {
    const response = await axios.put(`/api/openrouter-prompts/${promptId}`, updates);
    logWithTimestamp(`Successfully updated prompt ID ${promptId}`, response.data);
    return response.data;
  } catch (error) {
    logWithTimestamp(`Error updating prompt ID ${promptId}`, error);
    throw error;
  }
}

/**
 * Delete a prompt
 */
export async function deletePrompt(promptId: number): Promise<void> {
  logWithTimestamp(`Deleting prompt ID ${promptId}`);
  try {
    await axios.delete(`/api/openrouter-prompts/${promptId}`);
    logWithTimestamp(`Successfully deleted prompt ID ${promptId}`);
  } catch (error) {
    logWithTimestamp(`Error deleting prompt ID ${promptId}`, error);
    throw error;
  }
}

/**
 * Set a prompt as the default for the current user
 */
export async function setDefaultPrompt(promptId: number): Promise<void> {
  logWithTimestamp(`Setting prompt ID ${promptId} as default`);
  try {
    await axios.post(`/api/openrouter-prompts/${promptId}/set-default`);
    logWithTimestamp(`Successfully set prompt ID ${promptId} as default`);
  } catch (error) {
    logWithTimestamp(`Error setting prompt ID ${promptId} as default`, error);
    throw error;
  }
}
