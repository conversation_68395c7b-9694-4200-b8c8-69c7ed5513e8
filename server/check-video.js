import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function findVideo() {
  try {
    // Search for the video by title and channel
    const videos = await prisma.youtubeVideo.findMany({
      where: {
        title: {
          contains: 'DOUBLE DEPOSIT ALERT'
        },
        channelTitle: {
          contains: 'philipinesseniors'
        }
      }
    });

    if (videos.length > 0) {
      console.log('Found videos:', videos.length);
      videos.forEach(video => {
        console.log('Video ID:', video.id);
        console.log('Title:', video.title);
        console.log('Channel:', video.channelTitle);
        console.log('View Count:', video.viewCount);
        console.log('Published At:', video.publishedAt);
        console.log('---');
      });
    } else {
      console.log('No videos found with that title and channel');

      // Try a broader search
      console.log('Trying broader search...');
      const allVideos = await prisma.youtubeVideo.findMany({
        where: {
          title: {
            contains: 'DEPOSIT ALERT'
          }
        },
        take: 5
      });

      if (allVideos.length > 0) {
        console.log('Found videos with similar title:', allVideos.length);
        allVideos.forEach(video => {
          console.log('Video ID:', video.id);
          console.log('Title:', video.title);
          console.log('Channel:', video.channelTitle);
          console.log('View Count:', video.viewCount);
          console.log('Published At:', video.publishedAt);
          console.log('---');
        });
      } else {
        console.log('No videos found with similar title');
      }
    }
  } catch (error) {
    console.error('Error searching for video:', error);
  } finally {
    await prisma.$disconnect();
  }
}

findVideo();
