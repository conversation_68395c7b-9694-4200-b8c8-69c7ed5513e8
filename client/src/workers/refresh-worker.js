// refresh-worker.js
// This worker handles video refresh operations in a separate thread

// Set up event listener for messages from the main thread
self.addEventListener('message', async (event) => {
  const { type, payload } = event.data;

  try {
    switch (type) {
      case 'refresh-videos':
        // Simulate the refresh operation
        await refreshVideos(payload);
        break;

      case 'refresh-group':
        // Refresh a specific group
        await refreshGroupVideos(payload);
        break;

      default:
        console.error('Unknown message type:', type);
    }
  } catch (error) {
    // Send error back to main thread
    self.postMessage({
      type: 'error',
      error: error.message
    });
  }
});

// Function to refresh videos
async function refreshVideos(options = {}) {
  try {
    // Notify main thread that refresh has started
    self.postMessage({ type: 'refresh-started' });

    // Request the refresh from the server
    const response = await fetch('/api/videos/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(options)
    });

    if (!response.ok) {
      throw new Error(`Refresh failed: ${response.status} ${response.statusText}`);
    }

    // Get the request ID from the response
    const result = await response.json();
    const requestId = result.requestId;

    if (!requestId) {
      throw new Error('No request ID returned from server');
    }

    // Poll for request status
    await pollRequestStatus(requestId, 'refresh-completed');
  } catch (error) {
    // Send error back to main thread
    self.postMessage({
      type: 'error',
      error: error.message
    });
  }
}

// Function to poll for request status
async function pollRequestStatus(requestId, completedEventType) {
  try {
    let completed = false;
    let attempts = 0;
    const maxAttempts = 30; // Maximum number of polling attempts

    while (!completed && attempts < maxAttempts) {
      attempts++;

      // Wait before polling (increasing delay for each attempt)
      await new Promise(resolve => setTimeout(resolve, Math.min(1000 * attempts, 5000)));

      // Check request status
      const response = await fetch(`/api/requests/${requestId}`);

      if (!response.ok) {
        throw new Error(`Failed to check request status: ${response.status} ${response.statusText}`);
      }

      const request = await response.json();

      // Check if request is completed
      if (request.status === 'completed') {
        completed = true;

        // Send success message back to main thread
        self.postMessage({
          type: completedEventType,
          result: request.result,
          requestId
        });
      }
      // Check if request failed
      else if (request.status === 'failed' || request.status === 'cancelled' || request.status === 'timeout') {
        throw new Error(request.error || `Request ${request.status}: ${request.id}`);
      }
    }

    // If we've reached max attempts without completion
    if (!completed) {
      throw new Error(`Request timed out after ${maxAttempts} polling attempts`);
    }
  } catch (error) {
    throw error;
  }
}

// Function to refresh group videos
async function refreshGroupVideos(groupId) {
  try {
    // Notify main thread that refresh has started
    self.postMessage({
      type: 'group-refresh-started',
      groupId
    });

    // Request the group refresh from the server
    const response = await fetch(`/api/keyword-groups/${groupId}/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Group refresh failed: ${response.status} ${response.statusText}`);
    }

    // Get the request ID from the response
    const result = await response.json();
    const requestId = result.requestId;

    if (!requestId) {
      throw new Error('No request ID returned from server');
    }

    // Poll for request status
    await pollRequestStatus(requestId, 'group-refresh-completed');
  } catch (error) {
    // Send error back to main thread
    self.postMessage({
      type: 'error',
      error: error.message,
      groupId
    });
  }
}
