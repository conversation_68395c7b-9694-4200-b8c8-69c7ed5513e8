import express from 'express';
import { z } from 'zod';
import { storage } from '../storage';

const router = express.Router();

// Middleware to check authentication
const requireAuth = (req: any, res: any, next: any) => {
  if (!req.isAuthenticated()) {
    return res.sendStatus(401);
  }
  next();
};

// Validation schemas
const createScriptSchema = z.object({
  id: z.string(),
  title: z.string().min(1).max(255),
  sourceVideoUrl: z.string().url().optional(),
  transcript: z.string().min(1),
  tags: z.array(z.string()).default([]),
  dateAdded: z.string().transform(str => new Date(str)),
  editHistory: z.array(z.object({
    date: z.string().transform(str => new Date(str)),
    changes: z.string()
  })).optional()
});

const updateScriptSchema = z.object({
  title: z.string().min(1).max(255).optional(),
  sourceVideoUrl: z.string().url().optional(),
  transcript: z.string().min(1).optional(),
  tags: z.array(z.string()).optional(),
  editHistory: z.array(z.object({
    date: z.string().transform(str => new Date(str)),
    changes: z.string()
  })).optional()
});

const createChunkSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  content: z.string().min(1),
  purpose: z.enum(['opening-hook', 'call-to-action', 'information-drop', 'summary', 'other']),
  tags: z.array(z.string()).default([]),
  sourceScriptId: z.string().optional()
});

const updateChunkSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  content: z.string().min(1).optional(),
  purpose: z.enum(['opening-hook', 'call-to-action', 'information-drop', 'summary', 'other']).optional(),
  tags: z.array(z.string()).optional(),
  sourceScriptId: z.string().optional()
});

const createHighlightSchema = z.object({
  id: z.string(),
  scriptId: z.string(),
  startIndex: z.number().int().min(0),
  endIndex: z.number().int().min(0),
  color: z.enum(['red', 'green', 'blue', 'yellow']),
  note: z.string(),
  analyticsNote: z.string().optional()
});

const updateHighlightSchema = z.object({
  scriptId: z.string().optional(),
  startIndex: z.number().int().min(0).optional(),
  endIndex: z.number().int().min(0).optional(),
  color: z.enum(['red', 'green', 'blue', 'yellow']).optional(),
  note: z.string().optional(),
  analyticsNote: z.string().optional()
});

const createStrategySchema = z.object({
  id: z.string(),
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  samplePhrases: z.array(z.string()).default([]),
  tone: z.string().optional()
});

const updateStrategySchema = z.object({
  title: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  samplePhrases: z.array(z.string()).optional(),
  tone: z.string().optional()
});

const createContentStructureSchema = z.object({
  id: z.string(),
  name: z.string().min(1).max(255),
  category: z.string().optional(),
  introFormat: z.string().optional(),
  mainInfoDrop: z.string().optional(),
  explanationBreakdown: z.string().optional(),
  engagementInsert: z.string().optional(),
  outroCTA: z.string().optional()
});

const updateContentStructureSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  category: z.string().optional(),
  introFormat: z.string().optional(),
  mainInfoDrop: z.string().optional(),
  explanationBreakdown: z.string().optional(),
  engagementInsert: z.string().optional(),
  outroCTA: z.string().optional()
});

const createCoreInfoSchema = z.object({
  id: z.string(),
  eventDate: z.string().optional(),
  eligibilityCriteria: z.string().optional(),
  actBillProposalName: z.string().optional(),
  payDates: z.string().optional(),
  programType: z.string().optional(),
  sourceLink: z.string().url().optional(),
  notes: z.string().optional()
});

const updateCoreInfoSchema = z.object({
  eventDate: z.string().optional(),
  eligibilityCriteria: z.string().optional(),
  actBillProposalName: z.string().optional(),
  payDates: z.string().optional(),
  programType: z.string().optional(),
  sourceLink: z.string().url().optional(),
  notes: z.string().optional()
});

const createNarrationStyleSchema = z.object({
  id: z.string(),
  landmarkName: z.string().min(1).max(255),
  description: z.string().optional(),
  speakerTone: z.string().optional(),
  commonPhrases: z.array(z.string()).default([]),
  timingRhythm: z.string().optional()
});

const updateNarrationStyleSchema = z.object({
  landmarkName: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  speakerTone: z.string().optional(),
  commonPhrases: z.array(z.string()).optional(),
  timingRhythm: z.string().optional()
});

// Scripts routes
router.get('/scripts', requireAuth, async (req, res) => {
  try {
    const scripts = await storage.getTxtProScripts(req.user.id);
    res.json(scripts);
  } catch (error) {
    console.error('Error fetching TXT PRO scripts:', error);
    res.status(500).json({ message: 'Failed to fetch scripts' });
  }
});

router.post('/scripts', requireAuth, async (req, res) => {
  try {
    const result = createScriptSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const scriptId = await storage.createTxtProScript(req.user.id, result.data);
    const scripts = await storage.getTxtProScripts(req.user.id);
    const newScript = scripts.find(s => s.id === scriptId);
    
    res.status(201).json(newScript);
  } catch (error) {
    console.error('Error creating TXT PRO script:', error);
    res.status(500).json({ message: 'Failed to create script' });
  }
});

router.put('/scripts/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const result = updateScriptSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const success = await storage.updateTxtProScript(id, req.user.id, result.data);
    if (!success) {
      return res.status(404).json({ message: 'Script not found' });
    }

    const scripts = await storage.getTxtProScripts(req.user.id);
    const updatedScript = scripts.find(s => s.id === id);
    res.json(updatedScript);
  } catch (error) {
    console.error('Error updating TXT PRO script:', error);
    res.status(500).json({ message: 'Failed to update script' });
  }
});

router.delete('/scripts/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const success = await storage.deleteTxtProScript(id, req.user.id);
    if (!success) {
      return res.status(404).json({ message: 'Script not found' });
    }
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting TXT PRO script:', error);
    res.status(500).json({ message: 'Failed to delete script' });
  }
});

// Chunks routes
router.get('/chunks', requireAuth, async (req, res) => {
  try {
    const chunks = await storage.getTxtProChunks(req.user.id);
    res.json(chunks);
  } catch (error) {
    console.error('Error fetching TXT PRO chunks:', error);
    res.status(500).json({ message: 'Failed to fetch chunks' });
  }
});

router.post('/chunks', requireAuth, async (req, res) => {
  try {
    const result = createChunkSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const chunkId = await storage.createTxtProChunk(req.user.id, result.data);
    const chunks = await storage.getTxtProChunks(req.user.id);
    const newChunk = chunks.find(c => c.id === chunkId);
    
    res.status(201).json(newChunk);
  } catch (error) {
    console.error('Error creating TXT PRO chunk:', error);
    res.status(500).json({ message: 'Failed to create chunk' });
  }
});

router.put('/chunks/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const result = updateChunkSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const success = await storage.updateTxtProChunk(id, req.user.id, result.data);
    if (!success) {
      return res.status(404).json({ message: 'Chunk not found' });
    }

    const chunks = await storage.getTxtProChunks(req.user.id);
    const updatedChunk = chunks.find(c => c.id === id);
    res.json(updatedChunk);
  } catch (error) {
    console.error('Error updating TXT PRO chunk:', error);
    res.status(500).json({ message: 'Failed to update chunk' });
  }
});

router.delete('/chunks/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const success = await storage.deleteTxtProChunk(id, req.user.id);
    if (!success) {
      return res.status(404).json({ message: 'Chunk not found' });
    }
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting TXT PRO chunk:', error);
    res.status(500).json({ message: 'Failed to delete chunk' });
  }
});

// Highlights routes
router.get('/highlights', requireAuth, async (req, res) => {
  try {
    const highlights = await storage.getTxtProHighlights(req.user.id);
    res.json(highlights);
  } catch (error) {
    console.error('Error fetching TXT PRO highlights:', error);
    res.status(500).json({ message: 'Failed to fetch highlights' });
  }
});

router.post('/highlights', requireAuth, async (req, res) => {
  try {
    const result = createHighlightSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const highlightId = await storage.createTxtProHighlight(req.user.id, result.data);
    const highlights = await storage.getTxtProHighlights(req.user.id);
    const newHighlight = highlights.find(h => h.id === highlightId);

    res.status(201).json(newHighlight);
  } catch (error) {
    console.error('Error creating TXT PRO highlight:', error);
    res.status(500).json({ message: 'Failed to create highlight' });
  }
});

router.put('/highlights/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const result = updateHighlightSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const success = await storage.updateTxtProHighlight(id, req.user.id, result.data);
    if (!success) {
      return res.status(404).json({ message: 'Highlight not found' });
    }

    const highlights = await storage.getTxtProHighlights(req.user.id);
    const updatedHighlight = highlights.find(h => h.id === id);
    res.json(updatedHighlight);
  } catch (error) {
    console.error('Error updating TXT PRO highlight:', error);
    res.status(500).json({ message: 'Failed to update highlight' });
  }
});

router.delete('/highlights/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const success = await storage.deleteTxtProHighlight(id, req.user.id);
    if (!success) {
      return res.status(404).json({ message: 'Highlight not found' });
    }
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting TXT PRO highlight:', error);
    res.status(500).json({ message: 'Failed to delete highlight' });
  }
});

// Strategies routes
router.get('/strategies', requireAuth, async (req, res) => {
  try {
    const strategies = await storage.getTxtProStrategies(req.user.id);
    res.json(strategies);
  } catch (error) {
    console.error('Error fetching TXT PRO strategies:', error);
    res.status(500).json({ message: 'Failed to fetch strategies' });
  }
});

router.post('/strategies', requireAuth, async (req, res) => {
  try {
    const result = createStrategySchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const strategyId = await storage.createTxtProStrategy(req.user.id, result.data);
    const strategies = await storage.getTxtProStrategies(req.user.id);
    const newStrategy = strategies.find(s => s.id === strategyId);

    res.status(201).json(newStrategy);
  } catch (error) {
    console.error('Error creating TXT PRO strategy:', error);
    res.status(500).json({ message: 'Failed to create strategy' });
  }
});

router.put('/strategies/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const result = updateStrategySchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const success = await storage.updateTxtProStrategy(id, req.user.id, result.data);
    if (!success) {
      return res.status(404).json({ message: 'Strategy not found' });
    }

    const strategies = await storage.getTxtProStrategies(req.user.id);
    const updatedStrategy = strategies.find(s => s.id === id);
    res.json(updatedStrategy);
  } catch (error) {
    console.error('Error updating TXT PRO strategy:', error);
    res.status(500).json({ message: 'Failed to update strategy' });
  }
});

router.delete('/strategies/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const success = await storage.deleteTxtProStrategy(id, req.user.id);
    if (!success) {
      return res.status(404).json({ message: 'Strategy not found' });
    }
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting TXT PRO strategy:', error);
    res.status(500).json({ message: 'Failed to delete strategy' });
  }
});

// Content Structures routes
router.get('/content-structures', requireAuth, async (req, res) => {
  try {
    const contentStructures = await storage.getTxtProContentStructures(req.user.id);
    res.json(contentStructures);
  } catch (error) {
    console.error('Error fetching TXT PRO content structures:', error);
    res.status(500).json({ message: 'Failed to fetch content structures' });
  }
});

router.post('/content-structures', requireAuth, async (req, res) => {
  try {
    const result = createContentStructureSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const contentStructureId = await storage.createTxtProContentStructure(req.user.id, result.data);
    const contentStructures = await storage.getTxtProContentStructures(req.user.id);
    const newContentStructure = contentStructures.find(cs => cs.id === contentStructureId);

    res.status(201).json(newContentStructure);
  } catch (error) {
    console.error('Error creating TXT PRO content structure:', error);
    res.status(500).json({ message: 'Failed to create content structure' });
  }
});

router.put('/content-structures/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const result = updateContentStructureSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const success = await storage.updateTxtProContentStructure(id, req.user.id, result.data);
    if (!success) {
      return res.status(404).json({ message: 'Content structure not found' });
    }

    const contentStructures = await storage.getTxtProContentStructures(req.user.id);
    const updatedContentStructure = contentStructures.find(cs => cs.id === id);
    res.json(updatedContentStructure);
  } catch (error) {
    console.error('Error updating TXT PRO content structure:', error);
    res.status(500).json({ message: 'Failed to update content structure' });
  }
});

router.delete('/content-structures/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const success = await storage.deleteTxtProContentStructure(id, req.user.id);
    if (!success) {
      return res.status(404).json({ message: 'Content structure not found' });
    }
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting TXT PRO content structure:', error);
    res.status(500).json({ message: 'Failed to delete content structure' });
  }
});

// Core Infos routes
router.get('/core-infos', requireAuth, async (req, res) => {
  try {
    const coreInfos = await storage.getTxtProCoreInfos(req.user.id);
    res.json(coreInfos);
  } catch (error) {
    console.error('Error fetching TXT PRO core infos:', error);
    res.status(500).json({ message: 'Failed to fetch core infos' });
  }
});

router.post('/core-infos', requireAuth, async (req, res) => {
  try {
    const result = createCoreInfoSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const coreInfoId = await storage.createTxtProCoreInfo(req.user.id, result.data);
    const coreInfos = await storage.getTxtProCoreInfos(req.user.id);
    const newCoreInfo = coreInfos.find(ci => ci.id === coreInfoId);

    res.status(201).json(newCoreInfo);
  } catch (error) {
    console.error('Error creating TXT PRO core info:', error);
    res.status(500).json({ message: 'Failed to create core info' });
  }
});

router.put('/core-infos/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const result = updateCoreInfoSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const success = await storage.updateTxtProCoreInfo(id, req.user.id, result.data);
    if (!success) {
      return res.status(404).json({ message: 'Core info not found' });
    }

    const coreInfos = await storage.getTxtProCoreInfos(req.user.id);
    const updatedCoreInfo = coreInfos.find(ci => ci.id === id);
    res.json(updatedCoreInfo);
  } catch (error) {
    console.error('Error updating TXT PRO core info:', error);
    res.status(500).json({ message: 'Failed to update core info' });
  }
});

router.delete('/core-infos/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const success = await storage.deleteTxtProCoreInfo(id, req.user.id);
    if (!success) {
      return res.status(404).json({ message: 'Core info not found' });
    }
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting TXT PRO core info:', error);
    res.status(500).json({ message: 'Failed to delete core info' });
  }
});

// Narration Styles routes
router.get('/narration-styles', requireAuth, async (req, res) => {
  try {
    const narrationStyles = await storage.getTxtProNarrationStyles(req.user.id);
    res.json(narrationStyles);
  } catch (error) {
    console.error('Error fetching TXT PRO narration styles:', error);
    res.status(500).json({ message: 'Failed to fetch narration styles' });
  }
});

router.post('/narration-styles', requireAuth, async (req, res) => {
  try {
    const result = createNarrationStyleSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const narrationStyleId = await storage.createTxtProNarrationStyle(req.user.id, result.data);
    const narrationStyles = await storage.getTxtProNarrationStyles(req.user.id);
    const newNarrationStyle = narrationStyles.find(ns => ns.id === narrationStyleId);

    res.status(201).json(newNarrationStyle);
  } catch (error) {
    console.error('Error creating TXT PRO narration style:', error);
    res.status(500).json({ message: 'Failed to create narration style' });
  }
});

router.put('/narration-styles/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const result = updateNarrationStyleSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const success = await storage.updateTxtProNarrationStyle(id, req.user.id, result.data);
    if (!success) {
      return res.status(404).json({ message: 'Narration style not found' });
    }

    const narrationStyles = await storage.getTxtProNarrationStyles(req.user.id);
    const updatedNarrationStyle = narrationStyles.find(ns => ns.id === id);
    res.json(updatedNarrationStyle);
  } catch (error) {
    console.error('Error updating TXT PRO narration style:', error);
    res.status(500).json({ message: 'Failed to update narration style' });
  }
});

router.delete('/narration-styles/:id', requireAuth, async (req, res) => {
  try {
    const id = req.params.id;
    const success = await storage.deleteTxtProNarrationStyle(id, req.user.id);
    if (!success) {
      return res.status(404).json({ message: 'Narration style not found' });
    }
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting TXT PRO narration style:', error);
    res.status(500).json({ message: 'Failed to delete narration style' });
  }
});

export default router;
