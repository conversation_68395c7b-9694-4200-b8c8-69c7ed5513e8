import Database from 'better-sqlite3';

async function vacuumDatabase() {
  try {
    console.log('Starting database vacuum process...');
    const db = new Database('data.db');
    
    // Get database size before vacuum
    const pageSize = db.prepare('PRAGMA page_size').get().page_size;
    const pageCount = db.prepare('PRAGMA page_count').get().page_count;
    const sizeBefore = (pageSize * pageCount) / (1024 * 1024); // Size in MB
    
    console.log(`Database size before vacuum: ${sizeBefore.toFixed(2)} MB`);
    
    // Run the VACUUM command
    console.log('Running VACUUM command...');
    db.exec('VACUUM');
    
    // Get database size after vacuum
    const pageCountAfter = db.prepare('PRAGMA page_count').get().page_count;
    const sizeAfter = (pageSize * pageCountAfter) / (1024 * 1024); // Size in MB
    
    console.log(`Database size after vacuum: ${sizeAfter.toFixed(2)} MB`);
    console.log(`Space reclaimed: ${(sizeBefore - sizeAfter).toFixed(2)} MB`);
    
    console.log('Database vacuum completed successfully.');
    db.close();
  } catch (error) {
    console.error('Vacuum process failed:', error);
  }
}

vacuumDatabase();
