import { useQuery, useMutation } from "@tanstack/react-query";
import { KeywordGroup } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export function useKeywordGroups() {
  const { toast } = useToast();

  // Get all keyword groups
  const {
    data: groups = [],
    isLoading,
    error,
    refetch,
  } = useQuery<KeywordGroup[]>({
    queryKey: ["/api/keyword-groups"],
  });

  // Create a new keyword group
  const createGroup = useMutation({
    mutationFn: async (data: { name: string; keywords: string[]; excludeWords?: string[] }) => {
      const res = await apiRequest("POST", "/api/keyword-groups", data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/keyword-groups"] });
      toast({
        title: "Group created",
        description: "Your keyword group has been created.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create group",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update a keyword group
  const updateGroup = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: { name: string; keywords: string[]; excludeWords?: string[] } }) => {
      const res = await apiRequest("PUT", `/api/keyword-groups/${id}`, data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/keyword-groups"] });
      toast({
        title: "Group updated",
        description: "Your keyword group has been updated.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update group",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete a keyword group
  const deleteGroup = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/keyword-groups/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/keyword-groups"] });
      toast({
        title: "Group deleted",
        description: "Your keyword group has been deleted.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete group",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Set active keyword group
  const setActiveGroup = useMutation({
    mutationFn: async (id: number | null) => {
      const groupId = id === null ? 0 : id; // Use 0 to deactivate all groups
      const res = await apiRequest("POST", `/api/keyword-groups/${groupId}/activate`);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/settings"] });
      toast({
        title: "Group activated",
        description: "Your keyword group has been activated.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to activate group",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Refresh videos for a specific group
  const refreshGroupVideos = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("POST", `/api/keyword-groups/${id}/refresh`);
      return res.json();
    },
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: [`/api/keyword-groups/${id}/videos`] });
      toast({
        title: "Videos refreshed",
        description: "Your group's videos have been updated.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to refresh videos",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Get videos for a specific group
  const useGroupVideos = (groupId: number | null) => {
    return useQuery({
      queryKey: [`/api/keyword-groups/${groupId}/videos`],
      enabled: groupId !== null,
      staleTime: 60000, // 1 minute - override the global setting
      queryFn: async () => {
        if (groupId === null) return [];
        const res = await apiRequest("GET", `/api/keyword-groups/${groupId}/videos`);
        return res.json();
      },
    });
  };

  // Export keyword groups
  const exportGroups = async () => {
    try {
      // Get the groups directly from the query cache
      const groupsData = groups || [];

      // Format for export (remove IDs and user-specific data)
      const exportData = groupsData.map(group => ({
        name: group.name,
        keywords: group.keywords,
        excludeWords: group.excludeWords || [],
      }));

      // Create a Blob with the JSON data
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);

      // Create a download link and trigger it
      const a = document.createElement('a');
      a.href = url;
      a.download = 'keyword-groups.json';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Groups exported",
        description: "Your keyword groups have been exported.",
      });
    } catch (error) {
      console.error('Error exporting groups:', error);
      toast({
        title: "Export failed",
        description: "Failed to export keyword groups.",
        variant: "destructive",
      });
    }
  };

  // Import keyword groups
  const importGroups = useMutation({
    mutationFn: async (data: { name: string; keywords: string[]; excludeWords?: string[] }[]) => {
      const res = await apiRequest("POST", "/api/keyword-groups/import", data);
      return res.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["/api/keyword-groups"] });
      toast({
        title: "Groups imported",
        description: data.message || `Successfully imported ${data.groups?.length || 0} keyword groups.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to import groups",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Reorder keyword groups
  const reorderGroups = useMutation({
    mutationFn: async (data: { id: number; order: number }[]) => {
      const res = await apiRequest("POST", "/api/keyword-groups/reorder", data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/keyword-groups"] });
      toast({
        title: "Groups reordered",
        description: "Your keyword groups have been reordered.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to reorder groups",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    groups,
    isLoading,
    error,
    refetch,
    createGroup,
    updateGroup,
    deleteGroup,
    setActiveGroup,
    refreshGroupVideos,
    useGroupVideos,
    exportGroups,
    importGroups,
    reorderGroups,
  };
}
