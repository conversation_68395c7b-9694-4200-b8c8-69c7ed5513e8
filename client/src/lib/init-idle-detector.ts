import { initIdleDetector } from './idle-detector';
import { queryClient } from './queryClient';

// Initialize the idle detector
export function initializeIdleDetection() {
  // Create a global flag to track idle state
  window.__APP_IDLE_STATE__ = false;

  // Initialize the idle detector with callbacks
  const idleDetector = initIdleDetector({
    onIdle: () => {
      console.log('[IdleDetection] App is now idle - reducing background activity');
      window.__APP_IDLE_STATE__ = true;

      // When idle, pause non-essential background queries
      queryClient.setDefaultOptions({
        queries: {
          ...queryClient.getDefaultOptions().queries,
          refetchInterval: (query) => {
            // Keep essential queries running but at a reduced rate
            if (query.queryKey[0] === '/api/tasks/all') {
              return 120000; // Check tasks every 2 minutes when idle (reduced frequency)
            }

            // Pause most other queries
            return false;
          },
          // Increase stale time to reduce refetches
          staleTime: 600000, // 10 minutes
        },
      });

      // Notify the server that the app is idle to reduce server-side processing
      try {
        fetch('/api/client-state/idle', {
          method: 'POST',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ idle: true })
        }).catch(() => {}); // Ignore errors
      } catch (e) {
        // Ignore errors
      }
    },
    onActive: () => {
      console.log('[IdleDetection] App is now active - resuming normal activity');
      window.__APP_IDLE_STATE__ = false;

      // Reset query client options to default
      queryClient.setDefaultOptions({
        queries: {
          ...queryClient.getDefaultOptions().queries,
          refetchInterval: false, // Let individual queries control their intervals
          staleTime: 300000, // 5 minutes (default)
        },
      });

      // Only invalidate essential queries to prevent a flood of requests
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/all'] });

      // Notify the server that the app is active
      try {
        fetch('/api/client-state/idle', {
          method: 'POST',
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ idle: false })
        }).catch(() => {}); // Ignore errors
      } catch (e) {
        // Ignore errors
      }
    },
  }, 60 * 1000); // Set idle timeout to 1 minute (reduced from 3 minutes)

  // Add to window for debugging
  window.__idleDetector = idleDetector;

  return idleDetector;
}

// Add type definitions for the global window object
declare global {
  interface Window {
    __APP_IDLE_STATE__: boolean;
    __idleDetector: any;
  }
}
