import { useEffect } from 'react';
import { useSettings } from '@/hooks/use-settings';
import { logger } from '@/lib/logger';

/**
 * Component that initializes the theme based on user settings
 * This ensures dark mode is applied immediately when the app loads
 */
export function ThemeInitializer() {
  const { settings, isLoading } = useSettings();

  useEffect(() => {
    // Apply dark mode based on settings when they load
    if (!isLoading && settings) {
      logger.verbose('ThemeInitializer: Applying theme from settings, darkMode =', settings.darkMode);
      if (settings.darkMode) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  }, [settings, isLoading]);

  // This component doesn't render anything
  return null;
}
