import { storage } from '../storage';
import { VidIQ<PERSON><PERSON>K<PERSON> } from '../storage';

/**
 * VidIQ API Key Manager
 * Handles API key rotation and management
 */
export class VidIQKeyManager {
  private static instance: VidIQKeyManager;
  private currentKey: VidIQApiKey | null = null;
  
  private constructor() {}
  
  /**
   * Get the singleton instance of the key manager
   */
  public static getInstance(): VidIQKeyManager {
    if (!VidIQKeyManager.instance) {
      VidIQKeyManager.instance = new VidIQKeyManager();
    }
    return VidIQKeyManager.instance;
  }
  
  /**
   * Get the next available API key
   * @returns Promise with the next available API key
   */
  public async getNextKey(): Promise<VidIQApiKey | null> {
    try {
      this.currentKey = await storage.getNextAvailableApiKey() || null;
      return this.currentKey;
    } catch (error) {
      console.error('Error getting next API key:', error);
      return null;
    }
  }
  
  /**
   * Get the current API key
   * @returns The current API key or null if none is set
   */
  public getCurrentKey(): Vid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> | null {
    return this.currentKey;
  }
  
  /**
   * Mark the current API key as exhausted
   * @param minutes Number of minutes to mark the key as exhausted for
   * @returns Promise<boolean> True if successful, false otherwise
   */
  public async markCurrentKeyExhausted(minutes: number = 60): Promise<boolean> {
    if (!this.currentKey) return false;
    
    try {
      await storage.markApiKeyExhausted(this.currentKey.id, minutes);
      console.log(`API Key #${this.currentKey.id} marked as exhausted for ${minutes} minutes`);
      
      // Get a new key
      this.currentKey = await this.getNextKey();
      return true;
    } catch (error) {
      console.error('Error marking API key as exhausted:', error);
      return false;
    }
  }
  
  /**
   * Get API key headers for making requests
   * @returns Headers object or null if no key is available
   */
  public async getRequestHeaders(): Promise<Record<string, string> | null> {
    // If we don't have a current key, try to get one
    if (!this.currentKey) {
      this.currentKey = await this.getNextKey();
      if (!this.currentKey) return null;
    }
    
    return {
      'Accept': 'application/json, text/plain, */*',
      'Authorization': `Bearer ${this.currentKey.token}`,
      'Referer': 'https://app.vidiq.com/',
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
      'X-TimeZone': 'Asia/Calcutta',
      'X-Vidiq-Client': 'web 1a7277ec05e25b41808d7ed49f9a3f3c6ff86254',
      'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'x-vidiq-auth': this.currentKey.auth_token || this.currentKey.token,
      'Cookie': this.currentKey.cookie || ''
    };
  }
  
  /**
   * Handle API response error
   * @param error The error object
   * @returns True if a new key was selected, false otherwise
   */
  public async handleApiError(error: any): Promise<boolean> {
    // Check if it's a rate limit error (429 Too Many Requests)
    if (error.response && error.response.status === 429) {
      console.log('Rate limit exceeded, rotating to next API key...');
      
      // Mark the current key as exhausted
      if (this.currentKey) {
        await this.markCurrentKeyExhausted();
        return true;
      }
    }
    
    return false;
  }
}

// Export a singleton instance
export const keyManager = VidIQKeyManager.getInstance();
export default keyManager;
