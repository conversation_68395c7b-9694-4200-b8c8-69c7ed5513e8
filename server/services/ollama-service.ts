/**
 * Ollama Service
 *
 * This service provides functions for interacting with a locally running Ollama LLM server.
 */

// No external imports needed for fetch API
import { storage } from '../storage';

// Base URL for Ollama API
const OLLAMA_API_URL = 'http://localhost:11434/api';

// Interface for Ollama model information
interface OllamaModel {
  name: string;
  modified_at: string;
  size: number;
  digest: string;
  details: {
    parent_model: string;
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

// Interface for Ollama generation request
interface OllamaGenerateRequest {
  model: string;
  prompt: string;
  system?: string;
  template?: string;
  context?: number[];
  options?: {
    temperature?: number;
    top_p?: number;
    top_k?: number;
    num_predict?: number;
    stop?: string[];
  };
}

// Interface for Ollama generation response
interface OllamaGenerateResponse {
  model: string;
  created_at: string;
  response: string;
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

/**
 * Get a list of available Ollama models that are installed on the system
 * @returns Array of installed model objects
 */
export async function getAvailableOllamaModels(): Promise<{name: string}[]> {
  try {
    console.log('🤖 Ollama API: Fetching installed models...');
    // Set a timeout for the fetch request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch(`${OLLAMA_API_URL}/tags`, {
      signal: controller.signal
    });

    // Clear the timeout since the request completed
    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data || !data.models || !Array.isArray(data.models)) {
      console.error('🤖 Ollama API: Invalid response format from Ollama:', data);
      throw new Error('Invalid response format from Ollama server');
    }

    // Filter to only include models that are actually installed (have a size > 0)
    const models = data.models as OllamaModel[];
    const installedModels = models.filter(model => model.size > 0);
    const modelNames = installedModels.map(model => ({ name: model.name }));

    console.log(`🤖 Ollama API: Found ${modelNames.length} installed models out of ${models.length} total models`);
    if (modelNames.length > 0) {
      console.log(`🤖 Ollama API: Installed models: ${modelNames.map(m => m.name).join(', ')}`);
    } else {
      console.log('🤖 Ollama API: No installed models found');
    }

    return modelNames;
  } catch (error) {
    console.error('🤖 Ollama API: Error fetching Ollama models:', error);

    if (error.name === 'AbortError') {
      throw new Error('Connection to Ollama server timed out. Make sure Ollama is running on your machine.');
    }

    if (error.message && error.message.includes('HTTP error')) {
      throw new Error(`Ollama server returned an error: ${error.message}`);
    }

    throw new Error(`Failed to fetch Ollama models: ${error.message || 'Unknown error'}. Make sure Ollama is running on your machine at http://localhost:11434`);
  }
}

/**
 * Get the currently selected Ollama model for a user
 * @param userId User ID
 * @returns Selected model name or default
 */
export async function getUserSelectedOllamaModel(userId: number): Promise<string> {
  try {
    console.log(`🤖 Ollama API: Getting selected model for user ${userId}`);

    // First check if Ollama is running and get available models
    let availableModels: string[] = [];
    try {
      const response = await fetch(`${OLLAMA_API_URL}/tags`);
      if (response.ok) {
        const data = await response.json();
        availableModels = data.models.map((model: any) => model.name);
        console.log('🤖 Ollama API: Available Ollama models:', availableModels);
      } else {
        console.error(`🤖 Ollama API: Error fetching available Ollama models: HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('🤖 Ollama API: Error fetching available Ollama models:', error);
      // Continue with default model
    }

    // Define fallback models in order of preference
    const fallbackModels = ['llama3.2:latest', 'phi4-mini:latest', 'llama3:8b', 'tinyllama:latest'];

    // Get user settings
    const settings = await storage.getSettings(userId);
    if (!settings) {
      console.log('🤖 Ollama API: No settings found for user, using default Ollama model');
      // Use llama3.2:latest as the default model
      const defaultModel = 'llama3.2:latest';
      console.log(`🤖 Ollama API: Selected default model: ${defaultModel}`);
      return defaultModel;
    }

    if (!settings.ollamaModel) {
      console.log('🤖 Ollama API: No Ollama model specified in settings, using default');
      // Use llama3.2:latest as the default model
      const defaultModel = 'llama3.2:latest';

      // Update the settings with the default model
      try {
        await storage.updateSettings(userId, {
          ...settings,
          ollamaModel: defaultModel
        });
        console.log(`🤖 Ollama API: Updated user settings with default model: ${defaultModel}`);
      } catch (updateError) {
        console.error('🤖 Ollama API: Error updating settings with default Ollama model:', updateError);
      }
      return defaultModel;
    }

    // Make sure we're using the correct model name format
    let modelName = settings.ollamaModel;

    // If the model doesn't have a version tag (like :latest), add it
    if (modelName && !modelName.includes(':')) {
      modelName = `${modelName}:latest`;

      // Update the settings with the corrected model name
      try {
        console.log(`🤖 Ollama API: Updating model name format from ${settings.ollamaModel} to ${modelName}`);
        await storage.updateSettings(userId, {
          ...settings,
          ollamaModel: modelName
        });
      } catch (updateError) {
        console.error('🤖 Ollama API: Error updating model name format:', updateError);
      }
    }

    // Check if the model is empty or undefined, use default in that case
    if (!modelName) {
      console.log('🤖 Ollama API: Model name is empty, using default');
      modelName = 'llama3.2:latest';
    }

    console.log(`🤖 Ollama API: Using user-selected Ollama model: ${modelName}`);
    return modelName;
  } catch (error) {
    console.error('🤖 Ollama API: Error getting user selected Ollama model:', error);
    return 'llama3.2:latest';
  }
}

/**
 * Generate a response from the Ollama LLM using streaming
 * @param model Model name to use
 * @param prompt The prompt to send to the model
 * @param systemPrompt Optional system prompt for context
 * @returns The generated response
 */
export async function generateOllamaResponse(
  model: string,
  prompt: string,
  systemPrompt?: string
): Promise<string> {
  try {
    console.log(`🤖 Ollama API: Generating response with model: ${model}`);
    console.log(`🤖 Ollama API: Prompt length: ${prompt.length} characters`);

    // Adjust parameters based on model to allow for more variation
    let temperature = 0.8; // Higher temperature for more creativity

    // Adjust temperature based on model type
    if (model.includes('phi4-mini')) {
      temperature = 0.9; // Higher temperature for smaller models
    } else if (model.includes('llama3.2')) {
      temperature = 0.75; // Balanced for llama3.2
    } else if (model.includes('phi4')) {
      temperature = 0.8; // Standard for phi4
    } else if (model.includes('tinyllama')) {
      temperature = 0.95; // Higher for tinyllama
    }

    console.log(`🤖 Ollama API: Using temperature ${temperature} for model ${model}`);

    const request: OllamaGenerateRequest = {
      model,
      prompt,
      options: {
        temperature: temperature,
        top_p: 0.9,
        top_k: 40,
        num_predict: 1024,
      }
    };

    if (systemPrompt) {
      request.system = systemPrompt;
      console.log(`🤖 Ollama API: Using system prompt (length: ${systemPrompt.length} characters)`);
    }

    // Check if Ollama is running by making a request to the API
    try {
      const tagsResponse = await fetch(`${OLLAMA_API_URL}/tags`);
      if (!tagsResponse.ok) {
        throw new Error(`HTTP error! status: ${tagsResponse.status}`);
      }

      const data = await tagsResponse.json();
      const availableModels = data.models.map((m: any) => m.name);
      console.log(`🤖 Ollama API: Ollama server is running with ${availableModels.length} models available`);

      // Check if the requested model is available
      if (!availableModels.includes(model)) {
        console.error(`🤖 Ollama API: Requested model '${model}' is not available in Ollama`);
        console.log(`🤖 Ollama API: Available models: ${availableModels.join(', ')}`);
        throw new Error(`Model '${model}' is not available in Ollama. Available models: ${availableModels.join(', ')}`);
      }
    } catch (connectionError) {
      console.error('🤖 Ollama API: Error connecting to Ollama server:', connectionError);
      throw new Error('Ollama server is not running. Please start Ollama and try again.');
    }

    console.log(`🤖 Ollama API: Sending request to Ollama API for model: ${model}`);
    const startTime = Date.now();

    // Use fetch with AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

    try {
      // Call the Ollama API
      console.log(`🤖 Ollama API: Sending request to ${OLLAMA_API_URL}/generate`);
      const response = await fetch(`${OLLAMA_API_URL}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request),
        signal: controller.signal
      });

      // Clear the timeout since the request completed
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
      }

      // Read the response as a stream of JSON objects
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      let result = '';
      let decoder = new TextDecoder();

      console.log(`🤖 Ollama API: Reading response stream...`);
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n').filter(line => line.trim());

        for (const line of lines) {
          try {
            const data = JSON.parse(line);
            if (data.response) {
              result += data.response;
            }
          } catch (e) {
            console.error('Error parsing JSON:', e);
          }
        }
      }

      const endTime = Date.now();
      console.log(`🤖 Ollama API: Received response from Ollama in ${endTime - startTime}ms`);
      console.log(`🤖 Ollama API: Response length: ${result.length} characters`);

      if (!result) {
        console.error('🤖 Ollama API: Empty response from Ollama');
        throw new Error('Empty response received from Ollama');
      }

      return result;
    } catch (error) {
      // Clear the timeout in case of error
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        throw new Error(`Request to Ollama timed out after 60 seconds. The model ${model} may be too slow or resource-intensive.`);
      }

      throw error;
    }
  } catch (error) {
    console.error('🤖 Ollama API: Error generating Ollama response:', error);

    // Handle specific error cases
    if (error.message === 'Ollama server is not running. Please start Ollama and try again.') {
      throw error;
    }

    if (error.name === 'AbortError') {
      throw new Error(`Request to Ollama timed out. The model ${model} may be too slow or resource-intensive.`);
    }

    if (error.message && error.message.includes('HTTP error')) {
      // The request was made and the server responded with a non-2xx status code
      console.error('🤖 Ollama API HTTP error:', error.message);
      throw new Error(`Ollama API error: ${error.message}`);
    }

    // Something happened in setting up the request that triggered an Error
    throw new Error(`Failed to generate response from Ollama: ${error.message}`);
  }
}

/**
 * Generate a JSON response from the Ollama LLM
 * @param model Model name to use
 * @param prompt The prompt to send to the model
 * @param systemPrompt Optional system prompt for context
 * @returns The generated JSON response
 */
export async function generateOllamaJsonResponse<T>(
  model: string,
  prompt: string,
  systemPrompt?: string
): Promise<T> {
  try {
    // Add JSON formatting instructions to the prompt
    const jsonPrompt = `${prompt}\n\nRespond with valid JSON only, no other text. The JSON should match the format specified above.`;

    console.log(`🤖 Ollama API: Generating JSON response using model: ${model}`);
    const response = await generateOllamaResponse(model, jsonPrompt, systemPrompt);

    // Check if response is undefined or empty
    if (!response) {
      console.error('🤖 Ollama API: Empty or undefined response from Ollama');
      throw new Error('Empty or undefined response from Ollama');
    }

    console.log(`🤖 Ollama API: Received response from Ollama (length: ${response.length})`);
    console.log(`🤖 Ollama API: Response preview: ${response.substring(0, 100)}...`);

    // Try different approaches to extract JSON

    // First, try to find JSON object with regex
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        console.log('🤖 Ollama API: Found JSON object with regex, attempting to parse');
        let jsonStr = jsonMatch[0];

        // Try to fix common JSON formatting issues
        jsonStr = jsonStr
          // Fix trailing commas in arrays
          .replace(/,\s*\]/g, ']')
          // Fix trailing commas in objects
          .replace(/,\s*\}/g, '}')
          // Fix "etc." in arrays
          .replace(/"\$\d+", "\$\d+", etc\.\]/g, '"$1200", "$600"]')
          // Fix missing quotes around property names
          .replace(/(\{|,)\s*([a-zA-Z0-9_]+)\s*:/g, '$1"$2":')
          // Fix single quotes
          .replace(/'/g, '"');

        console.log('🤖 Ollama API: Cleaned JSON string:', jsonStr);
        return JSON.parse(jsonStr) as T;
      } catch (parseError) {
        console.error('🤖 Ollama API: Failed to parse JSON match:', parseError);
        // Continue to next approach
      }
    }

    // Second, try to parse the entire response
    try {
      console.log('🤖 Ollama API: Attempting to parse entire response as JSON');
      return JSON.parse(response) as T;
    } catch (parseError) {
      console.error('🤖 Ollama API: Failed to parse entire response as JSON:', parseError);
      // Continue to next approach
    }

    // Third, try to clean the response and parse
    try {
      console.log('🤖 Ollama API: Attempting to clean and parse response');
      // Remove any markdown code block markers and extract JSON
      let cleaned = response.replace(/```json\n|```\n|```json|```/g, '');

      // Try to extract JSON from the cleaned response
      const jsonRegex = /\{[\s\S]*\}/;
      const jsonMatch = cleaned.match(jsonRegex);
      if (jsonMatch) {
        cleaned = jsonMatch[0];
      }

      // Apply the same fixes as in the first approach
      cleaned = cleaned
        // Fix trailing commas in arrays
        .replace(/,\s*\]/g, ']')
        // Fix trailing commas in objects
        .replace(/,\s*\}/g, '}')
        // Fix "etc." in arrays
        .replace(/"\$\d+", "\$\d+", etc\.\]/g, '"$1200", "$600"]')
        .replace(/\["\$\d+", "\$\d+", etc\.\]/g, '["$1200", "$600"]')
        .replace(/\[\$\d+, \$\d+, etc\.\]/g, '["$1200", "$600"]')
        .replace(/\[\$\d+, \$\d+\]/g, '["$1200", "$600"]')
        // Fix missing quotes around property names
        .replace(/(\{|,)\s*([a-zA-Z0-9_]+)\s*:/g, '$1"$2":')
        // Fix single quotes
        .replace(/'/g, '"');

      console.log('🤖 Ollama API: Cleaned JSON string:', cleaned);
      return JSON.parse(cleaned) as T;
    } catch (parseError) {
      console.error('🤖 Ollama API: Failed to parse cleaned response as JSON:', parseError);
      // Continue to fallback
    }

    // If all parsing attempts fail, try to extract information and create a fallback response
    console.error('🤖 Ollama API: All JSON parsing attempts failed. Response:', response);

    // Try to extract information from the response text
    try {
      console.log('🤖 Ollama API: Attempting to create a fallback response');

      // Try to manually extract JSON from the response
      const jsonRegex = /\{[\s\S]*?benefitInfo[\s\S]*?classification[\s\S]*?\}/;
      const jsonMatch = response.match(jsonRegex);

      if (jsonMatch) {
        console.log('🤖 Ollama API: Found potential JSON structure in response');
        let jsonStr = jsonMatch[0];

        // Apply extensive cleaning to the JSON string
        jsonStr = jsonStr
          // Fix trailing commas in arrays
          .replace(/,\s*\]/g, ']')
          // Fix trailing commas in objects
          .replace(/,\s*\}/g, '}')
          // Fix "etc." in arrays
          .replace(/"\$\d+", "\$\d+", etc\.\]/g, '"$1200", "$600"]')
          .replace(/\["\$\d+", "\$\d+", etc\.\]/g, '["$1200", "$600"]')
          .replace(/\[\$\d+, \$\d+, etc\.\]/g, '["$1200", "$600"]')
          .replace(/\[\$\d+, \$\d+\]/g, '["$1200", "$600"]')
          // Fix missing quotes around property names
          .replace(/(\{|,)\s*([a-zA-Z0-9_]+)\s*:/g, '$1"$2":')
          // Fix single quotes
          .replace(/'/g, '"')
          // Fix missing commas between properties
          .replace(/("[^"]+"):([^\{\}\[\],]+)("[^"]+":)/g, '$1:$2,$3')
          // Fix actionsToClaim appearing in both objects
          .replace(/"actionsToClaim":[^\}]+\},[^\{]+\{[^\}]+"actionsToClaim":/g, '"actionsToClaim":[^\}]+\},[^\{]+\{[^\}]+"viralPotential":');

        try {
          console.log('🤖 Ollama API: Attempting to parse cleaned JSON:', jsonStr);
          return JSON.parse(jsonStr) as T;
        } catch (parseError) {
          console.error('🤖 Ollama API: Failed to parse extracted JSON:', parseError);
          // Continue to fallback approach
        }
      }

      // Extract benefit amounts using regex
      const benefitAmountsMatch = response.match(/\$\d+/g);
      const benefitAmounts = benefitAmountsMatch || [];

      // Extract date using regex
      const dateMatch = response.match(/(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}/);
      const expectedDate = dateMatch ? dateMatch[0] : '';

      // Try to extract score
      const scoreMatch = response.match(/score"?:\s*(\d+)/i);
      const score = scoreMatch ? parseInt(scoreMatch[1], 10) : 20;

      // Try to extract priority tag
      const priorityMatch = response.match(/priorityTag"?:\s*"([^"]+)"/i);
      const priorityTag = priorityMatch ? priorityMatch[1] : 'Doubtful';

      // Try to extract eligible people
      const eligibleMatch = response.match(/eligiblePeople"?:\s*"([^"]+)"/i);
      const eligiblePeople = eligibleMatch ? eligibleMatch[1] : 'Information not available';

      // Try to extract proof or source
      const proofMatch = response.match(/proofOrSource"?:\s*"([^"]+)"/i);
      const proofOrSource = proofMatch ? proofMatch[1] : 'Information not available';

      // Try to extract actions to claim
      const actionsMatch = response.match(/actionsToClaim"?:\s*"([^"]+)"/i);
      const actionsToClaim = actionsMatch ? actionsMatch[1] : 'Information not available';

      // Try to extract viral potential
      const viralMatch = response.match(/viralPotential"?:\s*"([^"]+)"/i);
      const viralPotential = viralMatch ? viralMatch[1] : 'Low';

      // Try to extract reason for priority
      const reasonMatch = response.match(/reasonForPriority"?:\s*"([^"]+)"/i);
      const reasonForPriority = reasonMatch ? reasonMatch[1] : 'Generated from fallback due to parsing error';

      // Create a fallback response
      const fallbackResponse = {
        benefitInfo: {
          benefitAmounts: benefitAmounts,
          expectedArrivalDate: expectedDate,
          eligiblePeople: eligiblePeople,
          proofOrSource: proofOrSource,
          actionsToClaim: actionsToClaim
        },
        classification: {
          score: score,
          priorityTag: priorityTag,
          reasonForPriority: reasonForPriority,
          viralPotential: viralPotential
        }
      };

      console.log('🤖 Ollama API: Created fallback response:', fallbackResponse);
      return fallbackResponse as T;
    } catch (fallbackError) {
      console.error('🤖 Ollama API: Failed to create fallback response:', fallbackError);

      // Create a minimal fallback response as a last resort
      const minimalFallback = {
        benefitInfo: {
          benefitAmounts: [],
          expectedArrivalDate: '',
          eligiblePeople: 'Information not available',
          proofOrSource: 'Information not available',
          actionsToClaim: 'Information not available'
        },
        classification: {
          score: 20,
          priorityTag: 'Doubtful',
          reasonForPriority: 'Generated from minimal fallback due to parsing error',
          viralPotential: 'Low'
        }
      };

      console.log('🤖 Ollama API: Created minimal fallback response as last resort');
      return minimalFallback as T;
    }
  } catch (error) {
    console.error('🤖 Ollama API: Error generating Ollama JSON response:', error);
    throw new Error(`Failed to generate JSON response from Ollama: ${error.message}`);
  }
}
