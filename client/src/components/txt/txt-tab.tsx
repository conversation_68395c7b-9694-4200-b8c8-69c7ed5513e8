import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  FileText, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Download, 
  Upload,
  Palette,
  MessageSquare,
  Brain,
  Database,
  Clock,
  ExternalLink,
  BarChart3
} from 'lucide-react';
import { ManualScriptEditor } from './manual-script-editor';
import { ExistingTranscriptsView } from './existing-transcripts-view';
import { ChatGptIntegration } from './chatgpt-integration';
import { PromptsManagement } from './prompts-management';
import { AnalyticsDashboard } from './analytics-dashboard';

interface TxtTabProps {
  isActive: boolean;
  onRefresh?: (refreshTime: Date) => void;
}

export function TxtTab({ isActive, onRefresh }: TxtTabProps) {
  const [activeSubTab, setActiveSubTab] = useState('prompts');
  const [transcripts, setTranscripts] = useState<any[]>([]);
  const [prompts, setPrompts] = useState<any[]>([]);
  const [existingTranscripts, setExistingTranscripts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Fetch data when tab becomes active
  useEffect(() => {
    if (isActive) {
      fetchTranscripts();
      fetchPrompts();
      fetchExistingTranscripts();
    }
  }, [isActive]);

  const fetchTranscripts = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/txt-tab/transcripts');
      if (response.ok) {
        const data = await response.json();
        setTranscripts(data);
      } else {
        throw new Error('Failed to fetch transcripts');
      }
    } catch (error) {
      console.error('Error fetching transcripts:', error);
      toast({
        title: "Error",
        description: "Failed to fetch transcripts",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPrompts = async () => {
    try {
      const response = await fetch('/api/txt-tab/prompts');
      if (response.ok) {
        const data = await response.json();
        setPrompts(data);
      } else {
        throw new Error('Failed to fetch prompts');
      }
    } catch (error) {
      console.error('Error fetching prompts:', error);
      toast({
        title: "Error",
        description: "Failed to fetch prompts",
        variant: "destructive",
      });
    }
  };

  const fetchExistingTranscripts = async () => {
    try {
      const response = await fetch('/api/txt-tab/existing-transcripts');
      if (response.ok) {
        const data = await response.json();
        setExistingTranscripts(data);
      } else {
        throw new Error('Failed to fetch existing transcripts');
      }
    } catch (error) {
      console.error('Error fetching existing transcripts:', error);
      toast({
        title: "Error",
        description: "Failed to fetch existing transcripts",
        variant: "destructive",
      });
    }
  };

  const handleTranscriptCreated = (newTranscript: any) => {
    setTranscripts(prev => [newTranscript, ...prev]);
    toast({
      title: "Success",
      description: "Transcript created successfully",
    });
  };

  const handleTranscriptUpdated = (updatedTranscript: any) => {
    setTranscripts(prev => prev.map(t => t.id === updatedTranscript.id ? updatedTranscript : t));
    toast({
      title: "Success",
      description: "Transcript updated successfully",
    });
  };

  const handleTranscriptDeleted = (transcriptId: number) => {
    setTranscripts(prev => prev.filter(t => t.id !== transcriptId));
    toast({
      title: "Success",
      description: "Transcript deleted successfully",
    });
  };

  const handlePromptCreated = (newPrompt: any) => {
    setPrompts(prev => [newPrompt, ...prev]);
    toast({
      title: "Success",
      description: "Prompt created successfully",
    });
  };

  const handlePromptUpdated = (updatedPrompt: any) => {
    setPrompts(prev => prev.map(p => p.id === updatedPrompt.id ? updatedPrompt : p));
    toast({
      title: "Success",
      description: "Prompt updated successfully",
    });
  };

  const handlePromptDeleted = (promptId: number) => {
    setPrompts(prev => prev.filter(p => p.id !== promptId));
    toast({
      title: "Success",
      description: "Prompt deleted successfully",
    });
  };

  const handleTranscriptImported = (importedTranscript: any) => {
    setTranscripts(prev => [importedTranscript, ...prev]);
    toast({
      title: "Success",
      description: "Transcript imported successfully",
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <FileText className="h-6 w-6" />
            TXT - Script Writing
          </h2>
          <Badge variant="outline" className="text-xs">
            {transcripts.length} Transcripts
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              fetchTranscripts();
              fetchPrompts();
              fetchExistingTranscripts();
              onRefresh?.(new Date());
            }}
            disabled={isLoading}
          >
            <Database className="h-4 w-4 mr-1" />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs value={activeSubTab} onValueChange={setActiveSubTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="manual" className="flex items-center gap-2">
            <Edit className="h-4 w-4" />
            Manual Writing
          </TabsTrigger>
          <TabsTrigger value="existing" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Existing Transcripts
          </TabsTrigger>
          <TabsTrigger value="chatgpt" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            ChatGPT Integration
          </TabsTrigger>
          <TabsTrigger value="prompts" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Prompts
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="manual" className="space-y-4">
          <ManualScriptEditor
            transcripts={transcripts}
            onTranscriptCreated={handleTranscriptCreated}
            onTranscriptUpdated={handleTranscriptUpdated}
            onTranscriptDeleted={handleTranscriptDeleted}
          />
        </TabsContent>

        <TabsContent value="existing" className="space-y-4">
          <ExistingTranscriptsView
            existingTranscripts={existingTranscripts}
            onTranscriptImported={handleTranscriptImported}
            onRefresh={fetchExistingTranscripts}
          />
        </TabsContent>

        <TabsContent value="chatgpt" className="space-y-4">
          <ChatGptIntegration
            transcripts={transcripts}
            prompts={prompts}
            onTranscriptUpdated={handleTranscriptUpdated}
          />
        </TabsContent>

        <TabsContent value="prompts" className="space-y-4">
          <PromptsManagement
            prompts={prompts}
            onPromptCreated={handlePromptCreated}
            onPromptUpdated={handlePromptUpdated}
            onPromptDeleted={handlePromptDeleted}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <AnalyticsDashboard
            transcripts={transcripts}
            onRefresh={() => {
              fetchTranscripts();
              fetchPrompts();
              fetchExistingTranscripts();
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
