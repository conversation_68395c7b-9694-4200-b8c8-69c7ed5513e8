import { Database } from 'better-sqlite3';

export function addHomePageColumn(db: Database) {
  console.log('Running migration: Adding home_page column to users table');
  
  // Check if the column already exists
  const tableInfo = db.prepare('PRAGMA table_info(users)').all();
  const columnExists = tableInfo.some((column: any) => column.name === 'home_page');
  
  if (columnExists) {
    console.log('Column home_page already exists in users table');
    return;
  }
  
  // Add the column
  db.prepare('ALTER TABLE users ADD COLUMN home_page TEXT NOT NULL DEFAULT "trendy"').run();
  console.log('Added home_page column to users table');
}
