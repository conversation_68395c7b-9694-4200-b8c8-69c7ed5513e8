import axios from 'axios';
import * as cheerio from 'cheerio';

// Test URL
const testUrl = "https://www.msn.com/en-us/money/retirement/social-security-checks-of-up-to-5-108-going-out-this-week/ar-AA1DKbYM";

// Function to extract article ID from URL
const extractArticleId = (url) => {
  const match = url.match(/\/ar-([A-Za-z0-9]+)/);
  return match ? match[1] : null;
};

// Test different proxy services
const testProxyServices = async () => {
  const articleId = extractArticleId(testUrl);
  console.log(`Article ID: ${articleId}`);
  
  const proxyServices = [
    {
      name: "12ft.io",
      url: `https://12ft.io/proxy?q=${encodeURIComponent(testUrl)}`
    },
    {
      name: "AllOrigins",
      url: `https://api.allorigins.win/raw?url=${encodeURIComponent(testUrl)}`
    },
    {
      name: "Archive.today",
      url: `https://archive.today/newest/${testUrl}`
    },
    {
      name: "Bypass Paywalls",
      url: `https://bypass.paywalls.news/proxy?url=${encodeURIComponent(testUrl)}`
    }
  ];
  
  for (const service of proxyServices) {
    console.log(`\n\nTesting Proxy Service: ${service.name}`);
    console.log(`URL: ${service.url}`);
    console.log('='.repeat(50));
    
    try {
      const response = await axios.get(service.url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 20000
      });
      
      console.log(`Status: ${response.status}`);
      console.log(`Content Type: ${response.headers['content-type']}`);
      console.log(`Content Length: ${response.data.length} characters`);
      
      // Parse the HTML
      const $ = cheerio.load(response.data);
      
      // Check for title
      const title = $('h1').first().text().trim() || $('title').text().trim();
      console.log(`Title: ${title}`);
      
      // Check for paragraphs
      const paragraphs = $('p');
      console.log(`Paragraphs: ${paragraphs.length}`);
      
      // Get the first few paragraphs
      if (paragraphs.length > 0) {
        console.log('\nFirst 3 paragraphs:');
        paragraphs.slice(0, 3).each((i, el) => {
          const text = $(el).text().trim();
          if (text.length > 0) {
            console.log(`- ${text}`);
          }
        });
      }
      
      // Check for article content
      const contentSelectors = [
        '[data-testid="article-body-content"]',
        '[data-testid="articleBodyContent"]',
        '.articlebody',
        '.article-body',
        '.article-content',
        '.content-article',
        '.articlecontent',
        '#content-main',
        '.main-content',
        '.article-page-content',
        '.page-content',
        '.article-text',
        '.article',
        'article'
      ];
      
      console.log('\nContent Containers:');
      for (const selector of contentSelectors) {
        const elements = $(selector);
        if (elements.length > 0) {
          console.log(`- ${selector}: ${elements.length} elements, text length: ${elements.text().length}`);
          if (elements.text().length > 0) {
            console.log(`  Preview: ${elements.text().substring(0, 100).replace(/\n/g, ' ')}...`);
          }
        }
      }
      
    } catch (error) {
      console.log(`Error: ${error.message}`);
    }
  }
};

// Run the test
console.log('=== MSN ARTICLE PROXY SERVICES TEST ===\n');
testProxyServices();
