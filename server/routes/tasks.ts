import express from 'express';
import { worker<PERSON>anager, TaskStatus, TaskPriority } from '../services/worker-manager';
import { queueTranscriptionsForVideos, queueFinancialAnalysis, queueChannelVideoRefresh, queueChannelRefresh } from '../services/task-queue';
import { queueOllamaFinancialAnalysis } from '../services/task-queue-ollama';
import { queueOpenRouterFinancialAnalysis } from '../services/task-queue-openrouter';
import { storage } from '../storage';
import { z } from 'zod';

const router = express.Router();

// Test endpoint to check if the tasks API is working
router.get('/test-api', async (req, res) => {
  res.json({ message: 'Tasks API is working' });
});

// Authentication middleware
function requireAuth(req: express.Request, res: express.Response, next: express.NextFunction) {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  next();
}

// Get all tasks for the current user
router.get('/', requireAuth, async (req, res) => {
  try {
    const tasks = workerManager.getUserTasks(req.user.id);
    res.json(tasks);
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(500).json({ message: 'Failed to fetch tasks' });
  }
});

// Keep track of previous API response to avoid excessive logging
let prevApiTasksCount = 0;
let prevApiStatusCounts = {};

// Cache for tasks to reduce CPU usage
let tasksCache = {
  data: null as any[] | null,
  timestamp: 0,
  statusCounts: {} as Record<string, number>
};

// Cache TTL in milliseconds (500ms)
const CACHE_TTL = 500;

// Get all tasks (no authentication required)
router.get('/all', async (req, res) => {
  try {
    const now = Date.now();

    // Check if we can use the cache
    if (tasksCache.data && now - tasksCache.timestamp < CACHE_TTL) {
      // Use cached data
      return res.json(tasksCache.data);
    }

    // Get all tasks from the worker manager
    const allTasks = workerManager.getAllTasks();

    // Only log when there's a significant change in the response
    const statusCounts = allTasks.reduce((acc, task) => {
      acc[task.status] = (acc[task.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const statusCountsStr = JSON.stringify(statusCounts);
    if (allTasks.length !== prevApiTasksCount || statusCountsStr !== JSON.stringify(prevApiStatusCounts)) {
      console.log(`/api/tasks/all: Returning ${allTasks.length} tasks. Status counts:`, statusCounts);
      prevApiTasksCount = allTasks.length;
      prevApiStatusCounts = JSON.parse(statusCountsStr);
    }

    // Update cache
    tasksCache = {
      data: allTasks,
      timestamp: now,
      statusCounts
    };

    res.json(allTasks);
  } catch (error) {
    console.error('Error fetching all tasks:', error);
    res.status(500).json({ message: 'Failed to fetch all tasks' });
  }
});

// Get recent tasks for the current user
router.get('/recent', requireAuth, async (req, res) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const tasks = workerManager.getRecentUserTasks(req.user.id, limit);
    res.json(tasks);
  } catch (error) {
    console.error('Error fetching recent tasks:', error);
    res.status(500).json({ message: 'Failed to fetch recent tasks' });
  }
});

// Get a specific task
router.get('/:id', requireAuth, async (req, res) => {
  try {
    const task = workerManager.getTask(req.params.id);

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Only allow users to access their own tasks
    if (task.userId !== req.user.id) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    res.json(task);
  } catch (error) {
    console.error('Error fetching task:', error);
    res.status(500).json({ message: 'Failed to fetch task' });
  }
});

// Cancel a task
router.post('/:id/cancel', requireAuth, async (req, res) => {
  try {
    const task = workerManager.getTask(req.params.id);

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Only allow users to cancel their own tasks
    if (task.userId !== req.user.id) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    const success = workerManager.cancelTask(req.params.id);

    if (success) {
      res.json({ message: 'Task cancelled' });
    } else {
      res.status(400).json({ message: 'Cannot cancel task' });
    }
  } catch (error) {
    console.error('Error cancelling task:', error);
    res.status(500).json({ message: 'Failed to cancel task' });
  }
});

// Cancel all active tasks for the current user
router.post('/cancel-all', requireAuth, async (req, res) => {
  try {
    console.log(`Cancelling all active tasks for user ${req.user.id}`);
    const userTasks = workerManager.getUserTasks(req.user.id);
    const activeTasks = userTasks.filter(task =>
      task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING
    );

    console.log(`Found ${activeTasks.length} active tasks to cancel`);

    let cancelledCount = 0;
    const cancelledTaskIds = [];
    const cancelledByStatus = {
      pending: 0,
      running: 0
    };

    for (const task of activeTasks) {
      console.log(`Attempting to cancel task ${task.id} (${task.name}) with status ${task.status}`);

      // Track original status
      const wasRunning = task.status === TaskStatus.RUNNING;

      if (workerManager.cancelTask(task.id)) {
        cancelledCount++;
        cancelledTaskIds.push(task.id);

        // Track counts by status
        if (wasRunning) {
          cancelledByStatus.running++;
        } else {
          cancelledByStatus.pending++;
        }

        console.log(`Successfully cancelled task ${task.id}`);
      } else {
        console.log(`Failed to cancel task ${task.id}`);
      }
    }

    console.log(`Cancelled ${cancelledCount} out of ${activeTasks.length} tasks`);
    console.log(`Breakdown: ${cancelledByStatus.pending} pending, ${cancelledByStatus.running} running`);

    res.json({
      message: `Cancelled ${cancelledCount} tasks`,
      cancelledCount,
      cancelledTaskIds,
      cancelledByStatus
    });
  } catch (error) {
    console.error('Error cancelling all tasks:', error);
    res.status(500).json({ message: 'Failed to cancel all tasks' });
  }
});

// Retry all failed tasks for the current user
router.post('/retry-failed', requireAuth, async (req, res) => {
  try {
    console.log(`Retrying failed tasks for user ${req.user.id}`);
    const userTasks = workerManager.getUserTasks(req.user.id);
    const failedTasks = userTasks.filter(task =>
      task.status === TaskStatus.FAILED
    );

    console.log(`Found ${failedTasks.length} failed tasks to retry`);

    let retriedCount = 0;
    const retriedTasks = [];

    for (const task of failedTasks) {
      console.log(`Retrying task ${task.id} of type ${task.type}: ${task.name}`);

      // Handle specific task types
      let newTask;

      if (task.type === 'financial-analysis' || task.type === 'ollama-financial-analysis') {
        // For financial analysis, we need to get the video details again
        const videoId = task.data.videoId;
        console.log(`Retrying financial analysis for video ${videoId}`);

        try {
          // Get video details
          const video = await storage.getYoutubeVideo(videoId);

          if (!video) {
            console.error(`Video ${videoId} not found, skipping retry`);
            continue;
          }

          if (!video.hasTranscription) {
            console.error(`Video ${videoId} does not have a transcription, skipping retry`);
            continue;
          }

          // Get transcription from the video object
          const transcription = video.transcription;

          if (!transcription) {
            console.error(`Transcription for video ${videoId} not found, skipping retry`);
            continue;
          }

          if (task.type === 'ollama-financial-analysis') {
            // Queue Ollama financial analysis
            const taskId = await queueOllamaFinancialAnalysis(
              videoId,
              transcription,
              video.title,
              video.description || '',
              req.user.id
            );

            newTask = workerManager.getTask(taskId);
          } else {
            // Queue regular financial analysis
            newTask = workerManager.addTask(
              'financial-analysis',
              `Analyze financial benefits for ${video.title}`,
              `Analyzing financial benefits for video ${videoId}`,
              req.user.id,
              {
                videoId,
                transcription,
                title: video.title,
                description: video.description
              },
              task.priority
            );
          }
        } catch (err) {
          console.error(`Error retrying financial analysis for video ${task.data.videoId}:`, err);
          continue;
        }
      } else if (task.type === 'fetch-transcription') {
        // For transcription fetching
        const videoId = task.data.videoId;
        console.log(`Retrying transcription fetch for video ${videoId}`);

        try {
          // Get video details
          const video = await storage.getYoutubeVideo(videoId);

          if (!video) {
            console.error(`Video ${videoId} not found, skipping retry`);
            continue;
          }

          // Create task
          newTask = workerManager.addTask(
            'fetch-transcription',
            `Fetch transcription for ${video.title}`,
            `Fetching transcription for video ${videoId}`,
            req.user.id,
            { videoId },
            task.priority
          );
        } catch (err) {
          console.error(`Error retrying transcription fetch for video ${task.data.videoId}:`, err);
          continue;
        }
      } else if (task.type === 'refresh-channel') {
        // For channel refresh
        const channelId = task.data.channelId;
        console.log(`Retrying channel refresh for channel ${channelId}`);

        try {
          // Get channel details
          const channel = await storage.getYoutubeChannel(channelId);

          if (!channel) {
            console.error(`Channel ${channelId} not found, skipping retry`);
            continue;
          }

          // Create task
          const taskId = await queueChannelVideoRefresh(channelId, req.user.id);
          newTask = workerManager.getTask(taskId);
        } catch (err) {
          console.error(`Error retrying channel refresh for channel ${task.data.channelId}:`, err);
          continue;
        }
      } else {
        // For other task types, just create a new task with the same parameters
        console.log(`Creating generic task for type ${task.type}`);
        newTask = workerManager.addTask(
          task.type,
          task.name,
          task.description,
          req.user.id,
          task.data,
          task.priority
        );
      }

      if (newTask) {
        console.log(`Successfully created new task ${newTask.id} to retry ${task.id}`);
        retriedCount++;
        retriedTasks.push(newTask);
      } else {
        console.error(`Failed to create new task to retry ${task.id}`);
      }
    }

    console.log(`Retried ${retriedCount} out of ${failedTasks.length} failed tasks`);
    res.json({
      message: `Retried ${retriedCount} failed tasks`,
      retriedCount,
      retriedTasks
    });
  } catch (error) {
    console.error('Error retrying failed tasks:', error);
    res.status(500).json({ message: 'Failed to retry failed tasks' });
  }
});

// Clear all tasks (completed, failed, cancelled) for the current user
router.post('/clear-history', requireAuth, async (req, res) => {
  try {
    console.log(`Clearing task history for user ${req.user.id}`);
    const userTasks = workerManager.getUserTasks(req.user.id);

    // Get all tasks that can be cleared (completed, failed, cancelled)
    const tasksToRemove = userTasks.filter(task =>
      task.status === TaskStatus.COMPLETED ||
      task.status === TaskStatus.FAILED ||
      task.status === TaskStatus.CANCELLED
    );

    console.log(`Found ${tasksToRemove.length} tasks to clear`);

    let clearedCount = 0;
    const clearedTaskIds = [];
    const clearedByStatus = {
      completed: 0,
      failed: 0,
      cancelled: 0
    };

    for (const task of tasksToRemove) {
      console.log(`Attempting to remove task ${task.id} (${task.name}) with status ${task.status}`);
      if (workerManager.removeTask(task.id)) {
        clearedCount++;
        clearedTaskIds.push(task.id);

        // Track counts by status
        if (task.status === TaskStatus.COMPLETED) {
          clearedByStatus.completed++;
        } else if (task.status === TaskStatus.FAILED) {
          clearedByStatus.failed++;
        } else if (task.status === TaskStatus.CANCELLED) {
          clearedByStatus.cancelled++;
        }

        console.log(`Successfully removed task ${task.id}`);
      } else {
        console.log(`Failed to remove task ${task.id}`);
      }
    }

    console.log(`Cleared ${clearedCount} out of ${tasksToRemove.length} tasks`);
    console.log(`Breakdown: ${clearedByStatus.completed} completed, ${clearedByStatus.failed} failed, ${clearedByStatus.cancelled} cancelled`);

    // Force a cleanup of the worker manager's task map
    workerManager.cleanupTasks(0);

    res.json({
      message: `Cleared ${clearedCount} tasks`,
      clearedCount,
      clearedTaskIds,
      clearedByStatus
    });
  } catch (error) {
    console.error('Error clearing task history:', error);
    res.status(500).json({ message: 'Failed to clear task history' });
  }
});

// Queue transcription fetching for a video
router.post('/fetch-transcription', requireAuth, async (req, res) => {
  try {
    const schema = z.object({
      videoId: z.string()
    });

    const result = schema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { videoId } = result.data;

    // Get video details
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    // Create task
    const task = workerManager.addTask(
      'fetch-transcription',
      `Fetch transcription for ${video.title}`,
      `Fetching transcription for video ${videoId}`,
      req.user.id,
      { videoId },
      TaskPriority.NORMAL
    );

    res.json(task);
  } catch (error) {
    console.error('Error queueing transcription fetch:', error);
    res.status(500).json({ message: 'Failed to queue transcription fetch' });
  }
});

// Queue financial analysis for a video
router.post('/analyze-financial', requireAuth, async (req, res) => {
  try {
    const schema = z.object({
      videoId: z.string()
    });

    const result = schema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { videoId } = result.data;

    // Get video details
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    if (!video.hasTranscription) {
      return res.status(400).json({ message: 'Video does not have a transcription' });
    }

    // Get transcription from the video object
    const transcription = video.transcription;

    if (!transcription) {
      return res.status(400).json({ message: 'Transcription not found' });
    }

    // Create task
    const task = workerManager.addTask(
      'financial-analysis',
      `Analyze financial benefits for ${video.title}`,
      `Analyzing financial benefits for video ${videoId}`,
      req.user.id,
      {
        videoId,
        transcription,
        title: video.title,
        description: video.description
      },
      TaskPriority.NORMAL
    );

    res.json(task);
  } catch (error) {
    console.error('Error queueing financial analysis:', error);
    res.status(500).json({ message: 'Failed to queue financial analysis' });
  }
});

// Queue Ollama LLM financial analysis for a video
router.post('/analyze-ollama', requireAuth, async (req, res) => {
  try {
    const schema = z.object({
      videoId: z.string()
    });

    const result = schema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { videoId } = result.data;

    // Get video details
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    if (!video.hasTranscription) {
      return res.status(400).json({ message: 'Video does not have a transcription' });
    }

    // Get transcription from the video object
    const transcription = video.transcription;

    if (!transcription) {
      return res.status(400).json({ message: 'Transcription not found' });
    }

    // Queue Ollama financial analysis
    const taskId = await queueOllamaFinancialAnalysis(
      videoId,
      transcription,
      video.title,
      video.description || '',
      req.user.id
    );

    const task = workerManager.getTask(taskId);

    res.json(task);
  } catch (error) {
    console.error('Error queueing Ollama financial analysis:', error);
    res.status(500).json({ message: 'Failed to queue Ollama financial analysis' });
  }
});

// Queue channel video refresh (authenticated)
router.post('/refresh-channel', requireAuth, async (req, res) => {
  try {
    const schema = z.object({
      channelId: z.string()
    });

    const result = schema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { channelId } = result.data;

    // Get channel details
    const channel = await storage.getYoutubeChannel(channelId);

    if (!channel) {
      return res.status(404).json({ message: 'Channel not found' });
    }

    // Create task
    const taskId = await queueChannelVideoRefresh(channelId, req.user.id);
    const task = workerManager.getTask(taskId);

    res.json(task);
  } catch (error) {
    console.error('Error queueing channel refresh:', error);
    res.status(500).json({ message: 'Failed to queue channel refresh' });
  }
});

// Direct endpoint for refreshing a channel (no authentication required)
router.post('/refresh-channel-direct', async (req, res) => {
  try {
    const schema = z.object({
      channelId: z.string()
    });

    const result = schema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { channelId } = result.data;

    // Create task
    const taskId = await queueChannelRefresh(channelId);
    const task = workerManager.getTask(taskId);

    res.json(task);
  } catch (error) {
    console.error('Error queueing direct channel refresh:', error);
    res.status(500).json({ message: 'Failed to queue channel refresh' });
  }
});

// Create a new task (authenticated)
router.post('/create', requireAuth, async (req, res) => {
  try {
    const schema = z.object({
      type: z.string(),
      data: z.record(z.any())
    });

    const result = schema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { type, data } = result.data;

    // Create task
    const task = workerManager.addTask(
      type,
      `Task: ${type}`,
      `Processing task of type ${type}`,
      req.user.id,
      data,
      TaskPriority.NORMAL
    );

    console.log(`Created task ${task.id} of type ${type} for user ${req.user.id}`);
    res.json(task);
  } catch (error) {
    console.error('Error creating task:', error);
    res.status(500).json({ message: 'Failed to create task' });
  }
});

// Queue OpenRouter financial analysis for a video
router.post('/analyze-openrouter', requireAuth, async (req, res) => {
  try {
    const schema = z.object({
      videoId: z.string()
    });

    const result = schema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { videoId } = result.data;

    // Get video details
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    if (!video.hasTranscription) {
      return res.status(400).json({ message: 'Video does not have a transcription' });
    }

    // Get transcription from the video object
    const transcription = video.transcription;

    if (!transcription) {
      return res.status(400).json({ message: 'Transcription not found' });
    }

    // Queue OpenRouter financial analysis
    const taskId = await queueOpenRouterFinancialAnalysis(
      videoId,
      transcription,
      video.title,
      video.description || '',
      req.user.id,
      'manual_api_request', // Source of the analysis
      false // Don't force reanalysis
    );

    // Check if a task was created (null means analysis already exists)
    if (taskId === null) {
      console.log(`No task created for video ${videoId} - analysis already exists`);

      // Return a special response indicating no task was created
      return res.json({
        id: 'no-task-needed',
        status: TaskStatus.COMPLETED,
        title: `Analysis already exists for ${video.title}`,
        description: 'This video already has an OpenRouter analysis. No new task was created.',
        message: 'Analysis already exists for this video. No new task was created.',
        alreadyAnalyzed: true
      });
    }

    const task = workerManager.getTask(taskId);

    res.json(task);
  } catch (error) {
    console.error('Error queueing OpenRouter financial analysis:', error);
    res.status(500).json({ message: 'Failed to queue OpenRouter financial analysis' });
  }
});

export default router;
