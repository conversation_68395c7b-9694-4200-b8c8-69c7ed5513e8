import { Router } from 'express';
import { storage } from '../storage';
import { ensureAuthenticated } from '../middleware/auth';

const router = Router();

// Get all prompts for the current user
router.get('/', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const prompts = await storage.getOllamaPrompts(userId);
    res.json(prompts);
  } catch (error) {
    console.error('Error getting Ollama prompts:', error);
    res.status(500).json({ message: 'Error getting Ollama prompts' });
  }
});

// Get a specific prompt
router.get('/:id', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const promptId = parseInt(req.params.id);
    const prompt = await storage.getOllamaPrompt(promptId);

    if (!prompt) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    // Check if the prompt belongs to the current user
    if (prompt.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    res.json(prompt);
  } catch (error) {
    console.error('Error getting Ollama prompt:', error);
    res.status(500).json({ message: 'Error getting Ollama prompt' });
  }
});

// Create a new prompt
router.post('/', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { name, promptText, isDefault } = req.body;

    if (!name || !promptText) {
      return res.status(400).json({ message: 'Name and prompt text are required' });
    }

    const prompt = await storage.createOllamaPrompt(userId, name, promptText, isDefault);
    res.status(201).json(prompt);
  } catch (error) {
    console.error('Error creating Ollama prompt:', error);
    res.status(500).json({ message: 'Error creating Ollama prompt' });
  }
});

// Update a prompt
router.put('/:id', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const promptId = parseInt(req.params.id);
    const prompt = await storage.getOllamaPrompt(promptId);

    if (!prompt) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    // Check if the prompt belongs to the current user
    if (prompt.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    const { name, promptText, isDefault } = req.body;
    const updates: any = {};

    if (name !== undefined) updates.name = name;
    if (promptText !== undefined) updates.promptText = promptText;
    if (isDefault !== undefined) updates.isDefault = isDefault;

    const updatedPrompt = await storage.updateOllamaPrompt(promptId, updates);
    res.json(updatedPrompt);
  } catch (error) {
    console.error('Error updating Ollama prompt:', error);
    res.status(500).json({ message: 'Error updating Ollama prompt' });
  }
});

// Delete a prompt
router.delete('/:id', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const promptId = parseInt(req.params.id);
    const prompt = await storage.getOllamaPrompt(promptId);

    if (!prompt) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    // Check if the prompt belongs to the current user
    if (prompt.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    await storage.deleteOllamaPrompt(promptId);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting Ollama prompt:', error);
    res.status(500).json({ message: 'Error deleting Ollama prompt' });
  }
});

// Set a prompt as default
router.post('/:id/set-default', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const promptId = parseInt(req.params.id);
    const prompt = await storage.getOllamaPrompt(promptId);

    if (!prompt) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    // Check if the prompt belongs to the current user
    if (prompt.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    await storage.setDefaultOllamaPrompt(userId, promptId);
    
    // Update user settings to use this prompt
    const userSettings = await storage.getSettings(userId);
    if (userSettings) {
      await storage.updateSettings(userId, {
        ...userSettings,
        selectedPromptId: promptId
      });
    }
    
    res.status(200).json({ message: 'Prompt set as default' });
  } catch (error) {
    console.error('Error setting default Ollama prompt:', error);
    res.status(500).json({ message: 'Error setting default Ollama prompt' });
  }
});

export default router;
