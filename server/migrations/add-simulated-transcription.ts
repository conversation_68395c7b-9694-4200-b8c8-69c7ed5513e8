import Database from 'better-sqlite3';

/**
 * Migration to add isSimulatedTranscription column to youtube_videos table
 */
function runMigration() {
  console.log('Running migration: Adding isSimulatedTranscription column to youtube_videos table');

  try {
    // Connect to the database
    const db = new Database('data.db');

    // Check if the column already exists
    const tableInfo = db.prepare('PRAGMA table_info(youtube_videos)').all();
    const columnExists = tableInfo.some(column => column.name === 'is_simulated_transcription');

    if (columnExists) {
      console.log('Column is_simulated_transcription already exists in youtube_videos table');
      return;
    }

    // Add the column
    db.exec('ALTER TABLE youtube_videos ADD COLUMN is_simulated_transcription BOOLEAN DEFAULT 0');

    console.log('Added is_simulated_transcription column to youtube_videos table');

    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error adding is_simulated_transcription column:', error);
    throw error;
  }
}

// Run the migration
runMigration();
