# Test Plan for Auto-Refresh Improvements

This test plan outlines the steps to verify that all the auto-refresh improvements are working correctly.

## 1. Basic Functionality Tests

### 1.1 Auto-Refresh Toggle

- [ ] **Test:** Toggle auto-refresh on and off using the switch
- [ ] **Expected:** Auto-refresh should start/stop accordingly
- [ ] **Verification:** Check that the countdown timer appears/disappears and refreshes occur/don't occur at the specified interval

### 1.2 Interval Selection

- [ ] **Test:** Change the refresh interval using the dropdown
- [ ] **Expected:** Auto-refresh should use the new interval
- [ ] **Verification:** Check that the countdown timer resets to the new interval and refreshes occur at the new interval

### 1.3 Manual Refresh

- [ ] **Test:** Click the "Refresh Now" button
- [ ] **Expected:** Data should refresh immediately
- [ ] **Verification:** Check that the refresh animation plays and new data is loaded

### 1.4 Throttling

- [ ] **Test:** Click the "Refresh Now" button multiple times in quick succession
- [ ] **Expected:** Throttling should prevent too many refreshes
- [ ] **Verification:** Check that a toast message appears asking to wait before refreshing again

## 2. Advanced Features Tests

### 2.1 Pause Functionality

- [ ] **Test:** Click the "Pause for 5m" button
- [ ] **Expected:** Auto-refresh should pause for 5 minutes
- [ ] **Verification:** Check that the pause timer appears and auto-refresh doesn't occur during the pause period

### 2.2 Resume Functionality

- [ ] **Test:** Click the "Resume" button while paused
- [ ] **Expected:** Auto-refresh should resume immediately
- [ ] **Verification:** Check that the countdown timer reappears and refreshes resume

### 2.3 Countdown Timer

- [ ] **Test:** Enable auto-refresh and observe the countdown timer
- [ ] **Expected:** Timer should count down to the next refresh
- [ ] **Verification:** Check that the timer decreases every second and resets after a refresh

### 2.4 Background Throttling

- [ ] **Test:** Enable auto-refresh, then switch to another browser tab
- [ ] **Expected:** Refresh rate should slow down when the tab is not visible
- [ ] **Verification:** Switch back to the tab and check the console logs to see if background throttling was applied

### 2.5 Adaptive Refresh Rate

- [ ] **Test:** Load videos with high VPH (views per hour) values
- [ ] **Expected:** Component should suggest a shorter refresh interval
- [ ] **Verification:** Check for a toast message suggesting a shorter interval

## 3. Debug Mode Tests

### 3.1 Debug Mode Toggle

- [ ] **Test:** Press Ctrl+Shift+D to toggle debug mode
- [ ] **Expected:** Debug information should appear/disappear
- [ ] **Verification:** Check that the debug card appears with detailed information

### 3.2 Debug Information

- [ ] **Test:** Enable debug mode and observe the information
- [ ] **Expected:** Debug card should show accurate information
- [ ] **Verification:** Check that all values match the current state (enabled status, interval, etc.)

### 3.3 Debug Logging

- [ ] **Test:** Click the "Log Debug Info" button in debug mode
- [ ] **Expected:** Detailed information should be logged to the console
- [ ] **Verification:** Check the browser console for the logged information

## 4. Edge Case Tests

### 4.1 No Videos

- [ ] **Test:** Navigate to the Realtime tab when no videos are loaded
- [ ] **Expected:** Auto-refresh should still work but not cause errors
- [ ] **Verification:** Check that no console errors appear and the UI remains functional

### 4.2 Tab Switching

- [ ] **Test:** Enable auto-refresh, switch to another tab, then switch back
- [ ] **Expected:** Auto-refresh should stop when away from Realtime tab and resume when returning
- [ ] **Verification:** Check that the interval is cleared and recreated appropriately

### 4.3 Browser Refresh

- [ ] **Test:** Enable auto-refresh with a custom interval, then refresh the browser
- [ ] **Expected:** Settings should persist after refresh
- [ ] **Verification:** Check that auto-refresh is still enabled with the same interval after the page reloads

### 4.4 Very Short Interval

- [ ] **Test:** Set a very short interval (30 seconds)
- [ ] **Expected:** Auto-refresh should work correctly without UI glitches
- [ ] **Verification:** Check that refreshes occur at the correct interval and the UI remains responsive

### 4.5 Very Long Interval

- [ ] **Test:** Set a very long interval (1 hour)
- [ ] **Expected:** Auto-refresh should still work correctly
- [ ] **Verification:** Check that the countdown timer shows the correct time and a refresh occurs after the interval

## 5. Performance Tests

### 5.1 Memory Usage

- [ ] **Test:** Leave auto-refresh running for an extended period (30+ minutes)
- [ ] **Expected:** Memory usage should remain stable
- [ ] **Verification:** Check browser task manager for memory usage over time

### 5.2 CPU Usage

- [ ] **Test:** Monitor CPU usage during auto-refresh operations
- [ ] **Expected:** CPU usage should spike briefly during refresh but remain low otherwise
- [ ] **Verification:** Check browser task manager or system monitor during refreshes

## Test Results

| Test ID | Test Name | Result | Notes |
|---------|-----------|--------|-------|
| 1.1     | Auto-Refresh Toggle | | |
| 1.2     | Interval Selection | | |
| 1.3     | Manual Refresh | | |
| 1.4     | Throttling | | |
| 2.1     | Pause Functionality | | |
| 2.2     | Resume Functionality | | |
| 2.3     | Countdown Timer | | |
| 2.4     | Background Throttling | | |
| 2.5     | Adaptive Refresh Rate | | |
| 3.1     | Debug Mode Toggle | | |
| 3.2     | Debug Information | | |
| 3.3     | Debug Logging | | |
| 4.1     | No Videos | | |
| 4.2     | Tab Switching | | |
| 4.3     | Browser Refresh | | |
| 4.4     | Very Short Interval | | |
| 4.5     | Very Long Interval | | |
| 5.1     | Memory Usage | | |
| 5.2     | CPU Usage | | |
