import express from 'express';
import { UserService } from '../services/userService';
import { UserResponse } from '../types/user';

export function createAuthRouter(userService: UserService) {
    const router = express.Router();

    router.post('/login', async (req, res) => {
        try {
            const { username, password } = req.body;

            if (!username || !password) {
                return res.status(400).json({ message: 'Username and password are required' });
            }

            const user = await userService.authenticateUser(username, password);
            
            if (!user) {
                return res.status(401).json({ message: 'Invalid credentials' });
            }

            res.json(user);
        } catch (error) {
            console.error('Login error:', error);
            res.status(500).json({ message: 'Login failed', error: error.message });
        }
    });

    return router;
}
