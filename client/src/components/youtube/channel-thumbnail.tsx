import { useState } from 'react';

interface ChannelThumbnailProps {
  thumbnail?: string;
  title?: string;
  className?: string;
}

/**
 * A component specifically for channel thumbnails with a fallback
 * Handles different property naming conventions in the codebase
 */
export function ChannelThumbnail({
  thumbnail,
  title,
  className = "w-full h-full object-cover"
}: ChannelThumbnailProps) {
  const [error, setError] = useState(false);

  // If there's no thumbnail or an error occurred, show a placeholder
  if (!thumbnail || error) {
    // Create a placeholder with the first letter of the channel title
    const initial = title ? title.charAt(0).toUpperCase() : '?';

    return (
      <div
        className={`flex items-center justify-center bg-primary/10 text-primary font-bold ${className}`}
        title={title || 'Channel'}
      >
        {initial}
      </div>
    );
  }

  return (
    <img
      src={thumbnail}
      alt={title || 'Channel thumbnail'}
      className={className}
      onError={() => setError(true)}
      loading="lazy"
    />
  );
}
