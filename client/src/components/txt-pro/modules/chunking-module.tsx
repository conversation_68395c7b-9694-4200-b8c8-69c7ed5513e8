import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { 
  Save, 
  Plus, 
  X, 
  Edit, 
  Trash2, 
  Copy, 
  Scissors, 
  DragHandleDots2Icon,
  Target,
  FileText
} from 'lucide-react';

interface Script {
  id: string;
  title: string;
  sourceVideoUrl?: string;
  transcript: string;
  tags: string[];
  dateAdded: Date;
}

interface Chunk {
  id: string;
  name: string;
  description: string;
  content: string;
  purpose: 'opening-hook' | 'call-to-action' | 'information-drop' | 'summary' | 'other';
  tags: string[];
  sourceScriptId?: string;
  dateCreated: Date;
}

interface ChunkingModuleProps {
  scripts: Script[];
  chunks: Chunk[];
  setChunks: React.Dispatch<React.SetStateAction<Chunk[]>>;
  selectedScript: Script | null;
  toast: any;
}

const ChunkingModule: React.FC<ChunkingModuleProps> = ({
  scripts,
  chunks,
  setChunks,
  selectedScript,
  toast
}) => {
  const [selectedText, setSelectedText] = useState('');
  const [selectionRange, setSelectionRange] = useState<{ start: number; end: number } | null>(null);
  const [isCreatingChunk, setIsCreatingChunk] = useState(false);
  const [editingChunk, setEditingChunk] = useState<Chunk | null>(null);
  const [chunkForm, setChunkForm] = useState({
    name: '',
    description: '',
    purpose: 'other' as Chunk['purpose'],
    tags: [] as string[]
  });
  const [newTag, setNewTag] = useState('');
  const [filterPurpose, setFilterPurpose] = useState<string>('all');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const purposeOptions = [
    { value: 'opening-hook', label: 'Opening Hook', color: 'bg-red-100 text-red-800' },
    { value: 'call-to-action', label: 'Call-To-Action', color: 'bg-green-100 text-green-800' },
    { value: 'information-drop', label: 'Information Drop', color: 'bg-blue-100 text-blue-800' },
    { value: 'summary', label: 'Summary', color: 'bg-purple-100 text-purple-800' },
    { value: 'other', label: 'Other', color: 'bg-gray-100 text-gray-800' }
  ];

  const handleTextSelection = () => {
    if (!textareaRef.current || !selectedScript) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    if (start !== end) {
      const selected = selectedScript.transcript.substring(start, end);
      setSelectedText(selected);
      setSelectionRange({ start, end });
    } else {
      setSelectedText('');
      setSelectionRange(null);
    }
  };

  const handleCreateChunkFromSelection = () => {
    if (!selectedText.trim()) {
      toast({
        title: "No Text Selected",
        description: "Please select some text from the transcript first",
        variant: "destructive"
      });
      return;
    }

    setChunkForm({
      name: '',
      description: '',
      purpose: 'other',
      tags: []
    });
    setIsCreatingChunk(true);
  };

  const handleSaveChunk = () => {
    if (!chunkForm.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Chunk name is required",
        variant: "destructive"
      });
      return;
    }

    const content = editingChunk ? editingChunk.content : selectedText;
    
    const chunkData: Chunk = {
      id: editingChunk?.id || `chunk_${Date.now()}`,
      name: chunkForm.name.trim(),
      description: chunkForm.description.trim(),
      content: content,
      purpose: chunkForm.purpose,
      tags: chunkForm.tags,
      sourceScriptId: editingChunk?.sourceScriptId || selectedScript?.id,
      dateCreated: editingChunk?.dateCreated || new Date()
    };

    if (editingChunk) {
      setChunks(prev => prev.map(chunk => 
        chunk.id === editingChunk.id ? chunkData : chunk
      ));
      toast({
        title: "Chunk Updated",
        description: `"${chunkData.name}" has been updated successfully`
      });
    } else {
      setChunks(prev => [...prev, chunkData]);
      toast({
        title: "Chunk Saved",
        description: `"${chunkData.name}" has been saved to your library`
      });
    }

    resetForm();
  };

  const handleDeleteChunk = (chunkId: string) => {
    const chunk = chunks.find(c => c.id === chunkId);
    if (chunk && window.confirm(`Are you sure you want to delete "${chunk.name}"?`)) {
      setChunks(prev => prev.filter(c => c.id !== chunkId));
      toast({
        title: "Chunk Deleted",
        description: "Chunk has been deleted successfully"
      });
    }
  };

  const handleEditChunk = (chunk: Chunk) => {
    setEditingChunk(chunk);
    setChunkForm({
      name: chunk.name,
      description: chunk.description,
      purpose: chunk.purpose,
      tags: [...chunk.tags]
    });
    setIsCreatingChunk(true);
  };

  const handleCopyChunk = (chunk: Chunk) => {
    navigator.clipboard.writeText(chunk.content);
    toast({
      title: "Copied to Clipboard",
      description: `"${chunk.name}" content has been copied`
    });
  };

  const resetForm = () => {
    setChunkForm({
      name: '',
      description: '',
      purpose: 'other',
      tags: []
    });
    setIsCreatingChunk(false);
    setEditingChunk(null);
    setSelectedText('');
    setSelectionRange(null);
    setNewTag('');
  };

  const handleAddTag = () => {
    if (newTag.trim() && !chunkForm.tags.includes(newTag.trim())) {
      setChunkForm(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setChunkForm(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const filteredChunks = chunks.filter(chunk => 
    filterPurpose === 'all' || chunk.purpose === filterPurpose
  );

  const getPurposeStyle = (purpose: string) => {
    return purposeOptions.find(opt => opt.value === purpose)?.color || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scissors className="h-5 w-5" />
            Segment Chunking & Reusability
          </CardTitle>
          <CardDescription>
            Mark and save parts of transcripts as reusable chunks for future scripts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {selectedScript ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left: Script Text Selection */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">
                    Select Text from: {selectedScript.title}
                  </Label>
                  {selectedText && (
                    <Button onClick={handleCreateChunkFromSelection} size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Save as Chunk
                    </Button>
                  )}
                </div>
                
                <Textarea
                  ref={textareaRef}
                  value={selectedScript.transcript}
                  onSelect={handleTextSelection}
                  onMouseUp={handleTextSelection}
                  onKeyUp={handleTextSelection}
                  readOnly
                  rows={15}
                  className="min-h-[400px] cursor-text"
                  placeholder="No script selected"
                />

                {selectedText && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <Label className="text-sm font-medium text-blue-800">Selected Text:</Label>
                    <p className="text-sm mt-1 text-blue-700">
                      "{selectedText.substring(0, 100)}{selectedText.length > 100 ? '...' : ''}"
                    </p>
                  </div>
                )}
              </div>

              {/* Right: Chunk Creation Form */}
              <div className="space-y-4">
                {isCreatingChunk ? (
                  <div className="space-y-4 p-4 border rounded-lg">
                    <h3 className="font-medium">
                      {editingChunk ? 'Edit Chunk' : 'Create New Chunk'}
                    </h3>
                    
                    <div className="space-y-2">
                      <Label htmlFor="chunkName">Chunk Name *</Label>
                      <Input
                        id="chunkName"
                        placeholder="e.g., Urgent Hook, Cliffhanger CTA"
                        value={chunkForm.name}
                        onChange={(e) => setChunkForm(prev => ({ ...prev, name: e.target.value }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="chunkDescription">Description</Label>
                      <Textarea
                        id="chunkDescription"
                        placeholder="Describe when and how to use this chunk"
                        value={chunkForm.description}
                        onChange={(e) => setChunkForm(prev => ({ ...prev, description: e.target.value }))}
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="chunkPurpose">Purpose</Label>
                      <Select 
                        value={chunkForm.purpose} 
                        onValueChange={(value: Chunk['purpose']) => 
                          setChunkForm(prev => ({ ...prev, purpose: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {purposeOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Tags</Label>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Add tags"
                          value={newTag}
                          onChange={(e) => setNewTag(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                        />
                        <Button onClick={handleAddTag} variant="outline" size="sm">
                          Add
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {chunkForm.tags.map(tag => (
                          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                            {tag}
                            <X 
                              className="h-3 w-3 cursor-pointer hover:text-destructive" 
                              onClick={() => handleRemoveTag(tag)}
                            />
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button onClick={handleSaveChunk}>
                        <Save className="h-4 w-4 mr-2" />
                        Save Chunk
                      </Button>
                      <Button onClick={resetForm} variant="outline">
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                    <Target className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Select text from the transcript and click "Save as Chunk"
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Script Selected</h3>
              <p className="text-muted-foreground">
                Select a script from the sidebar to start creating chunks
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Chunk Library */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Chunk Library</CardTitle>
              <CardDescription>
                Your saved reusable content chunks ({filteredChunks.length} total)
              </CardDescription>
            </div>
            <Select value={filterPurpose} onValueChange={setFilterPurpose}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Purposes</SelectItem>
                {purposeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            <div className="space-y-3">
              {filteredChunks.map(chunk => (
                <Card key={chunk.id} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{chunk.name}</h4>
                        <Badge className={getPurposeStyle(chunk.purpose)}>
                          {purposeOptions.find(opt => opt.value === chunk.purpose)?.label}
                        </Badge>
                      </div>
                      {chunk.description && (
                        <p className="text-sm text-muted-foreground mb-2">{chunk.description}</p>
                      )}
                      <p className="text-sm bg-muted/30 p-2 rounded">
                        "{chunk.content.substring(0, 150)}{chunk.content.length > 150 ? '...' : ''}"
                      </p>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {chunk.tags.map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex gap-1 ml-4">
                      <Button onClick={() => handleCopyChunk(chunk)} variant="ghost" size="sm">
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button onClick={() => handleEditChunk(chunk)} variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button onClick={() => handleDeleteChunk(chunk.id)} variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
              {filteredChunks.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No chunks found for the selected filter</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChunkingModule;
