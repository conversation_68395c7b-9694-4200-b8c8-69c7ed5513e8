.draggable-item {
  transition: transform 0.2s, box-shadow 0.2s, opacity 0.2s;
  position: relative;
}

.draggable-item.dragging {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -6px rgba(0, 0, 0, 0.1);
  z-index: 50;
  opacity: 0.8;
  transform: scale(1.02);
  border: 2px dashed var(--primary);
}

.draggable-item .cursor-grab {
  transition: all 0.2s ease;
}

.draggable-item .cursor-grab:hover {
  background-color: var(--primary-light, rgba(0, 0, 0, 0.1));
  transform: scale(1.1);
}

.draggable-item .cursor-grab:active {
  cursor: grabbing;
  background-color: var(--primary, rgba(0, 0, 0, 0.2));
  transform: scale(0.95);
}

/* Add a subtle animation to indicate draggability */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.draggable-item:hover .cursor-grab {
  animation: pulse 2s infinite;
}
