/* RSS Feed Styles */

/* Ensure all text in article content is visible in dark mode */
.article-content {
  color: white !important;
}

.article-content * {
  color: white !important;
}

/* Override any inline styles with !important */
.article-content [style*="color"] {
  color: white !important;
}

/* Force all text elements to use white color */
.article-content p,
.article-content span,
.article-content div,
.article-content li,
.article-content td,
.article-content th,
.article-content b,
.article-content strong,
.article-content i,
.article-content em,
.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6,
.article-content font,
.article-content label,
.article-content small,
.article-content cite,
.article-content figcaption,
.article-content time,
.article-content address {
  color: white !important;
}

/* Ensure proper styling for HTML tags */
.article-content b,
.article-content strong,
h3 b,
h3 strong,
p b,
p strong {
  font-weight: bold !important;
  color: white !important;
}

.article-content i,
.article-content em,
h3 i,
h3 em,
p i,
p em {
  font-style: italic !important;
  color: white !important;
}

/* Fix for literal <b> tags that might be displayed as text */
.article-content:not(b),
h3:not(b),
p:not(b) {
  color: white !important;
}

/* Ensure all feed items text is white */
.rss-feed-item,
.rss-feed-item *,
.rss-feed-item h3,
.rss-feed-item p,
.rss-feed-item span,
.rss-feed-item div {
  color: white !important;
}

/* Force white text for any elements with inline styles */
[style*="color: black"],
[style*="color:black"],
[style*="color: #000"],
[style*="color:#000"],
[style*="color: rgb(0, 0, 0)"],
[style*="color:rgb(0,0,0)"],
[style*="color: rgba(0, 0, 0,"] {
  color: white !important;
}

/* Style links in article content */
.article-content a {
  color: var(--primary) !important;
  text-decoration: underline;
}

/* Style headings in article content */
.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
  color: white !important;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

/* Style paragraphs in article content */
.article-content p {
  margin-bottom: 0.75rem;
}

/* Style lists in article content */
.article-content ul,
.article-content ol {
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

/* Style images in article content */
.article-content img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 0.25rem;
}

/* Style blockquotes in article content */
.article-content blockquote {
  border-left: 3px solid var(--primary);
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
}

/* Style code blocks in article content */
.article-content pre,
.article-content code {
  background-color: var(--muted);
  border-radius: 0.25rem;
  padding: 0.2rem 0.4rem;
  font-family: monospace;
}

/* Style tables in article content */
.article-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.article-content th,
.article-content td {
  border: 1px solid var(--border);
  padding: 0.5rem;
}

.article-content th {
  background-color: var(--muted);
}

/* YouTube video and transcript styling */
.video-container {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.transcript-container {
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  border-left: 3px solid var(--primary);
}

.transcript-container h2,
.transcript-container h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--primary) !important;
  font-weight: 600;
}

.transcript-content {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 1rem;
}

.transcript-content p {
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

/* Additional rules to ensure all text in RSS tab is white */
.rss-tab h1,
.rss-tab h2,
.rss-tab h3,
.rss-tab h4,
.rss-tab h5,
.rss-tab h6,
.rss-tab p,
.rss-tab span,
.rss-tab div,
.rss-tab li,
.rss-tab td,
.rss-tab th {
  color: white !important;
}

/* Ensure feed titles and descriptions are white */
.rss-feed-title,
.rss-feed-description {
  color: white !important;
}

/* Ensure published time and source are visible */
.rss-item-published-time,
.rss-item-source {
  color: white !important;
}

/* Fix for any font tags with color attributes */
font[color],
font[style*="color"] {
  color: white !important;
}

/* Fix for any elements with dark text colors */
[style*="color: #333"],
[style*="color:#333"],
[style*="color: #444"],
[style*="color:#444"],
[style*="color: #555"],
[style*="color:#555"],
[style*="color: #666"],
[style*="color:#666"],
[style*="color: #777"],
[style*="color:#777"],
[style*="color: #888"],
[style*="color:#888"],
[style*="color: #999"],
[style*="color:#999"] {
  color: white !important;
}

/* Ensure all text in feed items list is white */
.feed-items-list h3,
.feed-items-list p,
.feed-items-list span,
.feed-items-list div {
  color: white !important;
}

/* Ensure all text in article view is white */
.article-view h3,
.article-view p,
.article-view span,
.article-view div {
  color: white !important;
}

/* Financial term highlighting classes */
.financial-term-positive {
  background-color: #4ade80;
  color: black !important;
  font-weight: bold;
  padding: 0 2px;
}

.financial-term-negative {
  background-color: #f87171;
  color: black !important;
  font-weight: bold;
  padding: 0 2px;
}

.financial-term-neutral {
  background-color: #94a3b8;
  color: black !important;
  font-weight: bold;
  padding: 0 2px;
}

/* Dollar amount highlighting */
.dollar-amount-highlight {
  background-color: #4ade80;
  color: black !important;
  font-weight: bold;
  padding: 0 2px;
}

/* Date highlighting */
.date-highlight {
  background-color: #60a5fa;
  color: black !important;
  font-weight: bold;
  padding: 0 2px;
}

/* Percentage highlighting */
.percentage-highlight {
  background-color: #fb923c;
  color: black !important;
  font-weight: bold;
  padding: 0 2px;
}

/* Year highlighting */
.year-highlight {
  background-color: #c084fc;
  color: black !important;
  font-weight: bold;
  padding: 0 2px;
}

/* Article sentiment highlighting */
.article-positive-sentiment {
  border: 2px solid #4ade80 !important;
  box-shadow: 0 0 8px rgba(74, 222, 128, 0.4);
}

.article-negative-sentiment {
  border: 2px solid #f87171 !important;
  box-shadow: 0 0 8px rgba(248, 113, 113, 0.4);
}

.article-neutral-sentiment {
  border: 2px solid #94a3b8 !important;
}
