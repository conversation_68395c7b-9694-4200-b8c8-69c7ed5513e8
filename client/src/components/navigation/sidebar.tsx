import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useAuth } from "@/hooks/use-auth";
import { Settings, Menu, Video, LogOut, Users, Bookmark, Cast, History, Youtube, Flame, BarChart, Server, Activity } from "lucide-react";
import { CastQueueManager } from "@/components/video/cast-queue-manager";
import { Dialog, DialogContent, DialogTitle, DialogHeader } from "@/components/ui/dialog";
import React, { useState, useEffect } from "react";
import { useCastQueue } from "@/context/cast-queue-context";
import { useBackgroundTasks } from "@/hooks/use-background-tasks";
import { Badge } from "@/components/ui/badge";
import { LogLevelControl } from "@/components/debug/log-level-control";

// Create a context to share the sidebar state and toggle function
export const SidebarContext = React.createContext({
  collapsed: false,
  toggleSidebar: () => {}
});

// Hook to use the sidebar context
export function useSidebarCollapse() {
  return React.useContext(SidebarContext);
}

export function Sidebar() {
  const { logoutMutation, isAdmin, isLoading, error } = useAuth();
  const { refetchQueue } = useCastQueue();
  const { activeTasksCount } = useBackgroundTasks();
  const [open, setOpen] = useState(false);
  const [castQueueOpen, setCastQueueOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(() => {
    // Try to load from localStorage
    const savedState = localStorage.getItem('sidebar:collapsed');
    return savedState === 'true';
  });

  // Log active tasks count for debugging
  useEffect(() => {
    // Import logger here to avoid circular dependencies
    import('@/lib/logger').then(({ logger }) => {
      logger.debug(`Sidebar: Active tasks count: ${activeTasksCount}`);
    });
  }, [activeTasksCount]);

  // Function to toggle sidebar collapsed state
  const toggleSidebar = () => {
    const newState = !collapsed;
    setCollapsed(newState);
    localStorage.setItem('sidebar:collapsed', String(newState));
  };

  // Shared navigation items for both full and mini sidebar
  const navItems = [
    { href: "/trendy", icon: <Flame className="h-5 w-5" />, label: "Trendy" },
    { href: "/youtube", icon: <Youtube className="h-5 w-5" />, label: "YouTube" },
    { href: "/playlists", icon: <Bookmark className="h-5 w-5" />, label: "Playlists" },
    { href: "/history", icon: <History className="h-5 w-5" />, label: "Watch History" },
    { href: "/tasks", icon: <Activity className="h-5 w-5" />, label: "Background Tasks" },
  ];

  // Settings item (will be placed at the bottom)
  const settingsItem = { href: "/settings", icon: <Settings className="h-5 w-5" />, label: "Settings" };

  // Admin navigation items
  const adminNavItems = [
    { href: "/admin/dashboard", icon: <BarChart className="h-5 w-5" />, label: "Dashboard" },
    { href: "/admin/users", icon: <Users className="h-5 w-5" />, label: "Users" },
    { href: "/admin/settings", icon: <Server className="h-5 w-5" />, label: "Server Settings" },
  ];

  // Mini sidebar content with just icons
  const MiniSidebarContent = () => {
    return (
      <div className="h-full flex flex-col items-center py-4">
        <div className="space-y-4 flex flex-col items-center">
          <Button
            variant="ghost"
            size="icon"
            className="mb-4"
            onClick={toggleSidebar}
            title="Expand Sidebar"
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Regular nav items */}
          {navItems.map((item, index) => (
            <Link key={index} href={item.href}>
              <Button
                variant="ghost"
                size="icon"
                className="mb-1 relative"
                title={item.label}
              >
                {item.icon}
                {item.href === "/tasks" && activeTasksCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs font-semibold">
                    {activeTasksCount}
                  </Badge>
                )}
              </Button>
            </Link>
          ))}

          {/* Cast Queue button */}
          <Button
            variant="ghost"
            size="icon"
            className="mb-1"
            title="Cast Queue"
            onClick={() => {
              refetchQueue();
              setCastQueueOpen(true);
            }}
          >
            <Cast className="h-5 w-5" />
          </Button>

          {/* Admin menu items */}
          {!isLoading && !error && typeof isAdmin === 'boolean' && isAdmin && (
            <>
              <div className="w-full border-t my-2 border-gray-200 dark:border-gray-800"></div>

              {adminNavItems.map((item, index) => (
                <Link key={index} href={item.href}>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="mb-1"
                    title={item.label}
                  >
                    {item.icon}
                  </Button>
                </Link>
              ))}
            </>
          )}
        </div>

        <div className="mt-auto">
          <Link href={settingsItem.href}>
            <Button
              variant="ghost"
              size="icon"
              className="mb-1"
              title={settingsItem.label}
            >
              {settingsItem.icon}
            </Button>
          </Link>

          {/* Log Level Control */}
          <div className="mb-1">
            <LogLevelControl />
          </div>

          <Button
            variant="ghost"
            size="icon"
            className="mb-1"
            onClick={() => logoutMutation.mutate()}
            disabled={logoutMutation.isPending}
            title="Logout"
          >
            <LogOut className="h-5 w-5" />
          </Button>
        </div>
      </div>
    );
  };

  // Full sidebar content with text and icons
  const SidebarContent = () => {
    return (
      <div className="h-full flex flex-col">
        <div className="px-3 py-2">
          <div className="flex items-center justify-between mb-2 px-4">
            <h2 className="text-lg font-semibold">Menu</h2>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={toggleSidebar}
              title="Collapse Sidebar"
            >
              <Menu className="h-4 w-4" />
            </Button>
          </div>
          <div className="space-y-1">
            {/* Regular nav items */}
            {navItems.map((item, index) => (
              <Link key={index} href={item.href}>
                <Button
                  variant="ghost"
                  className="w-full justify-start relative"
                  onClick={() => setOpen(false)}
                >
                  <div className="mr-2 h-4 w-4">{item.icon}</div>
                  {item.label}
                  {item.href === "/tasks" && activeTasksCount > 0 && (
                    <Badge
                      variant="destructive"
                      className="ml-auto h-5 px-1.5 text-xs font-semibold">
                      {activeTasksCount}
                    </Badge>
                  )}
                </Button>
              </Link>
            ))}

            {/* Cast Queue button */}
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={() => {
                // Refetch the queue before opening the dialog
                refetchQueue();
                setCastQueueOpen(true);
                setOpen(false);
              }}
            >
              <Cast className="mr-2 h-4 w-4" />
              Cast Queue
            </Button>
            {isLoading ? (
              <div className="px-4 py-2 text-sm text-muted-foreground">
                Checking permissions...
              </div>
            ) : error ? (
              <div className="px-4 py-2 text-sm text-destructive">
                Error checking permissions
              </div>
            ) : typeof isAdmin === 'boolean' && isAdmin ? (
              import('@/lib/logger').then(({ logger }) => logger.debug('[Sidebar] Rendering admin menu items for verified admin')),
              <div className="pl-8 space-y-1">
                {adminNavItems.map((item, index) => (
                  <Link key={index} href={item.href}>
                    <Button
                      variant="ghost"
                      className="w-full justify-start"
                      onClick={() => setOpen(false)}
                    >
                      <div className="mr-2 h-4 w-4">{item.icon}</div>
                      {item.label}
                    </Button>
                  </Link>
                ))}
              </div>
            ) : null}
            {typeof isAdmin === 'boolean' && !isAdmin && (
              import('@/lib/logger').then(({ logger }) => logger.debug('[Sidebar] User is not admin'))
            )}
          </div>
        </div>
        <div className="mt-auto px-3 py-2">
          <Link href={settingsItem.href}>
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={() => setOpen(false)}
            >
              <div className="mr-2 h-4 w-4">{settingsItem.icon}</div>
              {settingsItem.label}
            </Button>
          </Link>

          {/* Debug Controls */}
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-muted-foreground">Debug Level:</span>
            <LogLevelControl />
          </div>

          <Button
            variant="ghost"
            className="w-full justify-start"
            onClick={() => logoutMutation.mutate()}
            disabled={logoutMutation.isPending}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Logout
          </Button>
        </div>
      </div>
    );
  };

  return (
    <SidebarContext.Provider value={{ collapsed, toggleSidebar }}>
      {/* Mobile Sidebar - Only for truly mobile devices, not just small screens */}
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetContent side="left" className="p-0">
          <ScrollArea className="h-full">
            <SidebarContent />
          </ScrollArea>
        </SheetContent>
      </Sheet>

      {/* Desktop Sidebar - Full or Mini based on collapsed state */}
      {collapsed ? (
        <div className="hidden lg:block h-screen w-16 border-r bg-background">
          <ScrollArea className="h-full">
            <MiniSidebarContent />
          </ScrollArea>
        </div>
      ) : (
        <div className="hidden lg:block h-screen w-64 border-r bg-background">
          <ScrollArea className="h-full">
            <SidebarContent />
          </ScrollArea>
        </div>
      )}
      {/* Cast Queue Dialog */}
      <Dialog open={castQueueOpen} onOpenChange={setCastQueueOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Cast className="h-5 w-5" />
              Cast Queue
            </DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            <CastQueueManager />
          </div>
        </DialogContent>
      </Dialog>
    </SidebarContext.Provider>
  );
}
