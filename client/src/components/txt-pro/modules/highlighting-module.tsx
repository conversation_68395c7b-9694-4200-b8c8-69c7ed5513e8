import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { 
  Highlighter, 
  Save, 
  X, 
  Edit, 
  Trash2, 
  FileText,
  TrendingUp,
  Eye,
  Clock
} from 'lucide-react';

interface Script {
  id: string;
  title: string;
  transcript: string;
  tags: string[];
}

interface Highlight {
  id: string;
  scriptId: string;
  startIndex: number;
  endIndex: number;
  color: 'red' | 'green' | 'blue' | 'yellow';
  note: string;
  analyticsNote?: string;
  timestamp?: string;
  dateCreated: Date;
}

interface HighlightingModuleProps {
  scripts: Script[];
  highlights: Highlight[];
  setHighlights: React.Dispatch<React.SetStateAction<Highlight[]>>;
  selectedScript: Script | null;
  toast: any;
}

const HighlightingModule: React.FC<HighlightingModuleProps> = ({
  scripts,
  highlights,
  setHighlights,
  selectedScript,
  toast
}) => {
  const [selectedText, setSelectedText] = useState('');
  const [selectionRange, setSelectionRange] = useState<{ start: number; end: number } | null>(null);
  const [isCreatingHighlight, setIsCreatingHighlight] = useState(false);
  const [editingHighlight, setEditingHighlight] = useState<Highlight | null>(null);
  const [highlightForm, setHighlightForm] = useState({
    color: 'red' as Highlight['color'],
    note: '',
    analyticsNote: '',
    timestamp: ''
  });
  const [filterColor, setFilterColor] = useState<string>('all');
  const textDisplayRef = useRef<HTMLDivElement>(null);

  const colorOptions = [
    { value: 'red', label: 'High Engagement', color: 'bg-red-100 text-red-800 border-red-200', bgColor: 'bg-red-200' },
    { value: 'green', label: 'Decent Engagement', color: 'bg-green-100 text-green-800 border-green-200', bgColor: 'bg-green-200' },
    { value: 'blue', label: 'Low but Important', color: 'bg-blue-100 text-blue-800 border-blue-200', bgColor: 'bg-blue-200' },
    { value: 'yellow', label: 'Experimental Tone', color: 'bg-yellow-100 text-yellow-800 border-yellow-200', bgColor: 'bg-yellow-200' }
  ];

  const scriptHighlights = highlights.filter(h => h.scriptId === selectedScript?.id);

  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (!selection || !selectedScript || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const selectedText = selection.toString();

    if (selectedText.trim()) {
      // Calculate character positions in the full transcript
      const container = textDisplayRef.current;
      if (!container) return;

      const textContent = container.textContent || '';
      const beforeRange = range.cloneRange();
      beforeRange.selectNodeContents(container);
      beforeRange.setEnd(range.startContainer, range.startOffset);
      const startIndex = beforeRange.toString().length;
      const endIndex = startIndex + selectedText.length;

      setSelectedText(selectedText);
      setSelectionRange({ start: startIndex, end: endIndex });
    } else {
      setSelectedText('');
      setSelectionRange(null);
    }
  };

  const handleCreateHighlight = () => {
    if (!selectedText.trim() || !selectionRange) {
      toast({
        title: "No Text Selected",
        description: "Please select some text from the transcript first",
        variant: "destructive"
      });
      return;
    }

    setHighlightForm({
      color: 'red',
      note: '',
      analyticsNote: '',
      timestamp: ''
    });
    setIsCreatingHighlight(true);
  };

  const handleSaveHighlight = () => {
    if (!highlightForm.note.trim()) {
      toast({
        title: "Validation Error",
        description: "Note is required for the highlight",
        variant: "destructive"
      });
      return;
    }

    if (!selectedScript || !selectionRange) return;

    const highlightData: Highlight = {
      id: editingHighlight?.id || `highlight_${Date.now()}`,
      scriptId: selectedScript.id,
      startIndex: editingHighlight?.startIndex || selectionRange.start,
      endIndex: editingHighlight?.endIndex || selectionRange.end,
      color: highlightForm.color,
      note: highlightForm.note.trim(),
      analyticsNote: highlightForm.analyticsNote.trim() || undefined,
      timestamp: highlightForm.timestamp.trim() || undefined,
      dateCreated: editingHighlight?.dateCreated || new Date()
    };

    if (editingHighlight) {
      setHighlights(prev => prev.map(h => 
        h.id === editingHighlight.id ? highlightData : h
      ));
      toast({
        title: "Highlight Updated",
        description: "Highlight has been updated successfully"
      });
    } else {
      setHighlights(prev => [...prev, highlightData]);
      toast({
        title: "Highlight Saved",
        description: "Text has been highlighted successfully"
      });
    }

    resetForm();
  };

  const handleDeleteHighlight = (highlightId: string) => {
    if (window.confirm('Are you sure you want to delete this highlight?')) {
      setHighlights(prev => prev.filter(h => h.id !== highlightId));
      toast({
        title: "Highlight Deleted",
        description: "Highlight has been deleted successfully"
      });
    }
  };

  const handleEditHighlight = (highlight: Highlight) => {
    setEditingHighlight(highlight);
    setHighlightForm({
      color: highlight.color,
      note: highlight.note,
      analyticsNote: highlight.analyticsNote || '',
      timestamp: highlight.timestamp || ''
    });
    setIsCreatingHighlight(true);
  };

  const resetForm = () => {
    setHighlightForm({
      color: 'red',
      note: '',
      analyticsNote: '',
      timestamp: ''
    });
    setIsCreatingHighlight(false);
    setEditingHighlight(null);
    setSelectedText('');
    setSelectionRange(null);
  };

  const renderHighlightedText = () => {
    if (!selectedScript) return null;

    const text = selectedScript.transcript;
    const sortedHighlights = scriptHighlights
      .filter(h => filterColor === 'all' || h.color === filterColor)
      .sort((a, b) => a.startIndex - b.startIndex);

    let result = [];
    let lastIndex = 0;

    sortedHighlights.forEach((highlight, index) => {
      // Add text before highlight
      if (highlight.startIndex > lastIndex) {
        result.push(
          <span key={`text-${index}`}>
            {text.substring(lastIndex, highlight.startIndex)}
          </span>
        );
      }

      // Add highlighted text
      const colorOption = colorOptions.find(opt => opt.value === highlight.color);
      result.push(
        <span
          key={`highlight-${highlight.id}`}
          className={`${colorOption?.bgColor} px-1 rounded cursor-pointer relative group`}
          title={highlight.note}
        >
          {text.substring(highlight.startIndex, highlight.endIndex)}
          <div className="absolute bottom-full left-0 mb-2 hidden group-hover:block z-10 bg-black text-white text-xs rounded p-2 whitespace-nowrap max-w-xs">
            <div className="font-medium">{colorOption?.label}</div>
            <div>{highlight.note}</div>
            {highlight.analyticsNote && (
              <div className="text-gray-300">Analytics: {highlight.analyticsNote}</div>
            )}
            {highlight.timestamp && (
              <div className="text-gray-300">Time: {highlight.timestamp}</div>
            )}
          </div>
        </span>
      );

      lastIndex = highlight.endIndex;
    });

    // Add remaining text
    if (lastIndex < text.length) {
      result.push(
        <span key="text-end">
          {text.substring(lastIndex)}
        </span>
      );
    }

    return result;
  };

  const filteredHighlights = scriptHighlights.filter(h => 
    filterColor === 'all' || h.color === filterColor
  );

  const getColorStyle = (color: string) => {
    return colorOptions.find(opt => opt.value === color)?.color || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Highlighter className="h-5 w-5" />
            Highlighting Watchtime Hotspots
          </CardTitle>
          <CardDescription>
            Highlight powerful script parts based on video analytics insights with color-coded engagement levels
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {selectedScript ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Left: Script Text with Highlights */}
              <div className="lg:col-span-2 space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">
                    {selectedScript.title}
                  </Label>
                  <div className="flex items-center gap-2">
                    <Select value={filterColor} onValueChange={setFilterColor}>
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Highlights</SelectItem>
                        {colorOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {selectedText && (
                      <Button onClick={handleCreateHighlight} size="sm">
                        <Highlighter className="h-4 w-4 mr-2" />
                        Highlight
                      </Button>
                    )}
                  </div>
                </div>

                <div 
                  ref={textDisplayRef}
                  className="p-4 bg-muted/30 rounded-lg min-h-[400px] cursor-text select-text"
                  onMouseUp={handleTextSelection}
                  onKeyUp={handleTextSelection}
                >
                  <div className="text-sm leading-relaxed whitespace-pre-wrap">
                    {renderHighlightedText()}
                  </div>
                </div>

                {selectedText && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <Label className="text-sm font-medium text-blue-800">Selected Text:</Label>
                    <p className="text-sm mt-1 text-blue-700">
                      "{selectedText.substring(0, 100)}{selectedText.length > 100 ? '...' : ''}"
                    </p>
                  </div>
                )}

                {/* Color Legend */}
                <div className="flex flex-wrap gap-2">
                  {colorOptions.map(option => (
                    <Badge key={option.value} className={option.color}>
                      {option.label}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Right: Highlight Creation Form */}
              <div className="space-y-4">
                {isCreatingHighlight ? (
                  <div className="space-y-4 p-4 border rounded-lg">
                    <h3 className="font-medium">
                      {editingHighlight ? 'Edit Highlight' : 'Create Highlight'}
                    </h3>
                    
                    <div className="space-y-2">
                      <Label htmlFor="highlightColor">Engagement Level</Label>
                      <Select 
                        value={highlightForm.color} 
                        onValueChange={(value: Highlight['color']) => 
                          setHighlightForm(prev => ({ ...prev, color: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {colorOptions.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center gap-2">
                                <div className={`w-3 h-3 rounded ${option.bgColor}`}></div>
                                {option.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="highlightNote">Note *</Label>
                      <Textarea
                        id="highlightNote"
                        placeholder="Why is this section engaging? What makes it work?"
                        value={highlightForm.note}
                        onChange={(e) => setHighlightForm(prev => ({ ...prev, note: e.target.value }))}
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="analyticsNote">Analytics Note</Label>
                      <Input
                        id="analyticsNote"
                        placeholder="e.g., Analytics spike at 2:13, Audience rewatches this line"
                        value={highlightForm.analyticsNote}
                        onChange={(e) => setHighlightForm(prev => ({ ...prev, analyticsNote: e.target.value }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="timestamp">Timestamp</Label>
                      <Input
                        id="timestamp"
                        placeholder="e.g., 2:13, 5:03"
                        value={highlightForm.timestamp}
                        onChange={(e) => setHighlightForm(prev => ({ ...prev, timestamp: e.target.value }))}
                      />
                    </div>

                    <div className="flex gap-2">
                      <Button onClick={handleSaveHighlight}>
                        <Save className="h-4 w-4 mr-2" />
                        Save Highlight
                      </Button>
                      <Button onClick={resetForm} variant="outline">
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                    <TrendingUp className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Select text and click "Highlight" to mark engagement hotspots
                    </p>
                  </div>
                )}

                {/* Highlights List */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Highlights ({filteredHighlights.length})
                  </Label>
                  <ScrollArea className="h-64">
                    <div className="space-y-2">
                      {filteredHighlights.map(highlight => (
                        <Card key={highlight.id} className="p-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <Badge className={getColorStyle(highlight.color)} size="sm">
                                {colorOptions.find(opt => opt.value === highlight.color)?.label}
                              </Badge>
                              <p className="text-xs mt-1 text-muted-foreground">
                                "{selectedScript.transcript.substring(highlight.startIndex, highlight.endIndex).substring(0, 50)}..."
                              </p>
                              <p className="text-xs mt-1">{highlight.note}</p>
                              {highlight.analyticsNote && (
                                <p className="text-xs text-blue-600 mt-1">
                                  <Eye className="h-3 w-3 inline mr-1" />
                                  {highlight.analyticsNote}
                                </p>
                              )}
                              {highlight.timestamp && (
                                <p className="text-xs text-green-600 mt-1">
                                  <Clock className="h-3 w-3 inline mr-1" />
                                  {highlight.timestamp}
                                </p>
                              )}
                            </div>
                            <div className="flex gap-1 ml-2">
                              <Button onClick={() => handleEditHighlight(highlight)} variant="ghost" size="sm">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button onClick={() => handleDeleteHighlight(highlight.id)} variant="ghost" size="sm">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </Card>
                      ))}
                      {filteredHighlights.length === 0 && (
                        <div className="text-center py-4">
                          <p className="text-xs text-muted-foreground">No highlights found</p>
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Script Selected</h3>
              <p className="text-muted-foreground">
                Select a script from the sidebar to start highlighting engagement hotspots
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default HighlightingModule;
