import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { AlertCircle, CheckCircle, Clock, RefreshCw } from "lucide-react";

export interface RefreshStats {
  totalVideos: number;
  videosWithTranscription: number;
  videosWithFinancialAnalysis: number;
  failedAnalyses: number;
  errors: string[];
  inProgress: boolean;
  lastUpdated: Date | null;
}

interface RefreshStatsProps {
  stats: RefreshStats;
  onDismiss?: () => void;
}

export function RefreshStatsCard({ stats, onDismiss }: RefreshStatsProps) {
  const [timeAgo, setTimeAgo] = useState<string>("");

  // Update the "time ago" text every minute
  useEffect(() => {
    const updateTimeAgo = () => {
      if (!stats.lastUpdated) {
        setTimeAgo("");
        return;
      }

      const now = new Date();
      const diffMs = now.getTime() - stats.lastUpdated.getTime();
      const diffMins = Math.floor(diffMs / 60000);
      
      if (diffMins < 1) {
        setTimeAgo("just now");
      } else if (diffMins === 1) {
        setTimeAgo("1 minute ago");
      } else if (diffMins < 60) {
        setTimeAgo(`${diffMins} minutes ago`);
      } else {
        const diffHours = Math.floor(diffMins / 60);
        if (diffHours === 1) {
          setTimeAgo("1 hour ago");
        } else {
          setTimeAgo(`${diffHours} hours ago`);
        }
      }
    };

    updateTimeAgo();
    const interval = setInterval(updateTimeAgo, 60000); // Update every minute
    
    return () => clearInterval(interval);
  }, [stats.lastUpdated]);

  // Calculate percentages
  const transcriptionPercentage = stats.totalVideos > 0 
    ? Math.round((stats.videosWithTranscription / stats.totalVideos) * 100) 
    : 0;
  
  const analysisPercentage = stats.totalVideos > 0 
    ? Math.round((stats.videosWithFinancialAnalysis / stats.totalVideos) * 100) 
    : 0;

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Refresh Statistics
          </CardTitle>
          {stats.inProgress ? (
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              <Clock className="h-3 w-3 mr-1 animate-pulse" />
              In Progress
            </Badge>
          ) : (
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <CheckCircle className="h-3 w-3 mr-1" />
              Complete
            </Badge>
          )}
        </div>
        <CardDescription>
          {stats.lastUpdated 
            ? `Last updated ${timeAgo}`
            : "No refresh data available"}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Videos Fetched</span>
              <span className="text-sm font-medium">{stats.totalVideos}</span>
            </div>
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Videos with Transcription</span>
              <span className="text-sm font-medium">{stats.videosWithTranscription} ({transcriptionPercentage}%)</span>
            </div>
            <Progress value={transcriptionPercentage} className="h-2" />
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Videos with Financial Analysis</span>
              <span className="text-sm font-medium">{stats.videosWithFinancialAnalysis} ({analysisPercentage}%)</span>
            </div>
            <Progress value={analysisPercentage} className="h-2" />
          </div>

          {stats.failedAnalyses > 0 && (
            <div className="pt-2">
              <Badge variant="destructive" className="mb-2">
                <AlertCircle className="h-3 w-3 mr-1" />
                {stats.failedAnalyses} Failed Analyses
              </Badge>
              
              {stats.errors.length > 0 && (
                <div className="mt-2 text-sm text-red-600 max-h-24 overflow-y-auto">
                  <Separator className="my-2" />
                  <p className="font-semibold mb-1">Errors:</p>
                  <ul className="list-disc pl-5 space-y-1">
                    {stats.errors.slice(0, 3).map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                    {stats.errors.length > 3 && (
                      <li>...and {stats.errors.length - 3} more errors</li>
                    )}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
