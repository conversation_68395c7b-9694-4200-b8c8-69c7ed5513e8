// Helper function to decode HTML entities
export const decodeHtmlEntities = (html: string): string => {
  if (!html) return '';

  // First use the browser's built-in decoder
  const textarea = document.createElement('textarea');
  textarea.innerHTML = html;
  let decoded = textarea.value;

  // Then handle specific entities and tags that might still be encoded
  // Common HTML entities
  decoded = decoded.replace(/&amp;/g, '&');
  decoded = decoded.replace(/&lt;/g, '<');
  decoded = decoded.replace(/&gt;/g, '>');
  decoded = decoded.replace(/&quot;/g, '"');
  decoded = decoded.replace(/&#39;/g, "'");
  decoded = decoded.replace(/&nbsp;/g, ' ');

  // Handle visible tags
  decoded = decoded.replace(/<b>Social Security<\/b>/g, '<b>Social Security</b>');
  decoded = decoded.replace(/<b>(\w+)<\/b>/g, '<b>$1</b>');

  return decoded;
};

// Import the financial term highlighter
import { highlightFinancialTerms } from './financial-term-highlighter';

// Process HTML content to fix tags and entities
export const processHtmlContent = (html: string): string => {
  if (!html) return '';

  // Decode HTML entities
  let processed = decodeHtmlEntities(html);

  // Fix visible tags - replace literal <b> and </b> with actual HTML tags
  processed = processed.replace(/&lt;b&gt;/g, '<b style="color:white;font-weight:bold">');
  processed = processed.replace(/&lt;\/b&gt;/g, '</b>');

  // Replace literal <b> tags that might be displayed as text
  processed = processed.replace(/<b>/g, '<b style="color:white;font-weight:bold">');

  // Fix specific patterns
  processed = processed.replace(/<b>Social Security<\/b>/g, '<b style="color:white;font-weight:bold">Social Security</b>');
  processed = processed.replace(/<b>2025<\/b>/g, '<b style="color:white;font-weight:bold">2025</b>');
  processed = processed.replace(/<b>COLA<\/b>/g, '<b style="color:white;font-weight:bold">COLA</b>');
  processed = processed.replace(/<b>SSI<\/b>/g, '<b style="color:white;font-weight:bold">SSI</b>');
  processed = processed.replace(/<b>SNAP<\/b>/g, '<b style="color:white;font-weight:bold">SNAP</b>');

  // Replace any remaining encoded tags
  processed = processed.replace(/&lt;(\/?[a-z][a-z0-9]*)(?: [^&]*)&gt;/gi, '<$1>');

  // Fix HTML entities that might still be present
  processed = processed.replace(/&#39;/g, "'");
  processed = processed.replace(/&quot;/g, '"');
  processed = processed.replace(/&nbsp;/g, ' ');
  processed = processed.replace(/&amp;/g, '&');

  // Final pass to catch any remaining visible tags
  processed = processed.replace(/<b>([^<]+)<\/b>/g, '<b style="color:white;font-weight:bold">$1</b>');

  // Handle specific cases where <b> might appear at the beginning of text
  processed = processed.replace(/^<b>/g, '<b style="color:white;font-weight:bold">');
  processed = processed.replace(/\s<b>/g, ' <b style="color:white;font-weight:bold">');

  // Apply financial term highlighting
  const { highlightedContent, counts } = highlightFinancialTerms(processed);
  processed = highlightedContent;

  // Store the highlight counts in a data attribute for later use
  processed = `<div data-positive-count="${counts.positive}" data-negative-count="${counts.negative}" data-neutral-count="${counts.neutral}">${processed}</div>`;

  // We'll use a DOM-based approach for highlighting to avoid text corruption
  try {
    // Create a temporary DOM element to safely manipulate the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = processed;

    // Function to safely highlight text in DOM nodes
    function highlightTextInNode(node: Node, pattern: RegExp, className: string) {
      if (node.nodeType === Node.TEXT_NODE && node.textContent) {
        const text = node.textContent;
        const matches = Array.from(text.matchAll(pattern));

        if (matches.length > 0) {
          // We have matches in this text node
          let lastIndex = 0;
          const fragment = document.createDocumentFragment();

          for (const match of matches) {
            if (match.index === undefined) continue;

            // Add text before the match
            if (match.index > lastIndex) {
              fragment.appendChild(document.createTextNode(text.substring(lastIndex, match.index)));
            }

            // Add the highlighted match
            const span = document.createElement('span');
            span.className = className;
            span.textContent = match[0];
            fragment.appendChild(span);

            lastIndex = match.index + match[0].length;
          }

          // Add any remaining text
          if (lastIndex < text.length) {
            fragment.appendChild(document.createTextNode(text.substring(lastIndex)));
          }

          // Replace the original node with our fragment
          const parent = node.parentNode;
          if (parent) {
            parent.replaceChild(fragment, node);
          }

          return true; // Node was replaced
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // Process child nodes (make a copy of childNodes as it's a live collection)
        const childNodes = Array.from(node.childNodes);
        for (const child of childNodes) {
          highlightTextInNode(child, pattern, className);
        }
      }

      return false; // Node was not replaced
    }

    // Highlight dollar amounts
    const dollarPattern = /\$\d{1,3}(,\d{3})*(\.\d+)?/g;
    Array.from(tempDiv.childNodes).forEach(node =>
      highlightTextInNode(node, dollarPattern, 'dollar-amount-highlight')
    );

    // Highlight dates (month names)
    const monthDatePattern = /\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2}(st|nd|rd|th)?,\s+\d{4}\b/gi;
    Array.from(tempDiv.childNodes).forEach(node =>
      highlightTextInNode(node, monthDatePattern, 'date-highlight')
    );

    // Highlight dates (numeric format)
    const numericDatePattern = /\b\d{1,2}\/\d{1,2}\/\d{2,4}\b/g;
    Array.from(tempDiv.childNodes).forEach(node =>
      highlightTextInNode(node, numericDatePattern, 'date-highlight')
    );

    // Highlight percentages
    const percentagePattern = /\b\d+(\.\d+)?%\b/g;
    Array.from(tempDiv.childNodes).forEach(node =>
      highlightTextInNode(node, percentagePattern, 'percentage-highlight')
    );

    // Highlight years
    const yearPattern = /\b(202\d|203\d)\b/g;
    Array.from(tempDiv.childNodes).forEach(node =>
      highlightTextInNode(node, yearPattern, 'year-highlight')
    );

    // Get the processed HTML
    processed = tempDiv.innerHTML;
  } catch (error) {
    console.error('Error highlighting special content:', error);
    // If there's an error, we'll fall back to the original content
  }

  // Ensure all links open in a new tab and have proper styling
  processed = processed.replace(/<a\s+href="([^"]+)"([^>]*)>/gi, '<a href="$1" target="_blank" rel="noopener noreferrer" style="color: #3b82f6; text-decoration: underline;"$2>');

  // Style paragraphs for better readability
  processed = processed.replace(/<p>/g, '<p style="margin-bottom: 1em; line-height: 1.5; color: white;">');

  // Style headings for better visibility
  processed = processed.replace(/<h1>/g, '<h1 style="font-size: 1.8em; font-weight: bold; margin: 0.5em 0; color: white;">');
  processed = processed.replace(/<h2>/g, '<h2 style="font-size: 1.5em; font-weight: bold; margin: 0.5em 0; color: white;">');
  processed = processed.replace(/<h3>/g, '<h3 style="font-size: 1.3em; font-weight: bold; margin: 0.5em 0; color: white;">');
  processed = processed.replace(/<h4>/g, '<h4 style="font-size: 1.1em; font-weight: bold; margin: 0.5em 0; color: white;">');
  processed = processed.replace(/<h5>/g, '<h5 style="font-size: 1em; font-weight: bold; margin: 0.5em 0; color: white;">');
  processed = processed.replace(/<h6>/g, '<h6 style="font-size: 0.9em; font-weight: bold; margin: 0.5em 0; color: white;">');

  // Style lists for better readability
  processed = processed.replace(/<ul>/g, '<ul style="list-style-type: disc; margin-left: 1.5em; margin-bottom: 1em;">');
  processed = processed.replace(/<ol>/g, '<ol style="list-style-type: decimal; margin-left: 1.5em; margin-bottom: 1em;">');
  processed = processed.replace(/<li>/g, '<li style="margin-bottom: 0.5em; color: white;">');

  // Add a class to source notes for styling
  processed = processed.replace(/<p class="source-note">/g, '<p class="source-note" style="font-size: 0.8em; color: white; margin-top: 2em; border-top: 1px solid #374151; padding-top: 0.5em;">');

  // Fix span elements to ensure they have white text (but not our highlighted amounts)
  processed = processed.replace(/<span(?![^>]*background-color: #)/g, '<span style="color: white;"');

  // Fix div elements to ensure they have white text
  processed = processed.replace(/<div(?![^>]*style)/g, '<div style="color: white;"');

  // Fix any elements with black color style
  processed = processed.replace(/style="([^"]*)color:\s*black([^"]*)"/gi, 'style="$1color: white$2"');
  processed = processed.replace(/style="([^"]*)color:\s*#000([^"]*)"/gi, 'style="$1color: white$2"');
  processed = processed.replace(/style="([^"]*)color:\s*#333([^"]*)"/gi, 'style="$1color: white$2"');
  processed = processed.replace(/style="([^"]*)color:\s*#444([^"]*)"/gi, 'style="$1color: white$2"');
  processed = processed.replace(/style="([^"]*)color:\s*#555([^"]*)"/gi, 'style="$1color: white$2"');
  processed = processed.replace(/style="([^"]*)color:\s*rgb\(0,\s*0,\s*0\)([^"]*)"/gi, 'style="$1color: white$2"');

  // Fix font tags
  processed = processed.replace(/<font([^>]*)>/gi, '<font$1 style="color: white;">');
  processed = processed.replace(/color="black"/gi, 'color="white"');
  processed = processed.replace(/color="#000"/gi, 'color="white"');

  // Wrap the entire content in a div with white text
  processed = `<div style="color: white !important;">${processed}</div>`;

  return processed;
};
