import React, { useState } from 'react';
import { SavedArticleList, RssFeedItem } from '@shared/schema';
import { useSavedArticleLists, useSavedArticles } from '@/hooks/use-saved-articles';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Trash2, Pencil, FileDown, Copy, ChevronRight } from 'lucide-react';
import { format } from 'date-fns';

interface SavedArticleListsProps {
  onSelectList: (listId: number) => void;
  selectedListId: number | null;
}

export function SavedArticleLists({ onSelectList, selectedListId }: SavedArticleListsProps) {
  const { lists, isLoading, createList, updateList, deleteList } = useSavedArticleLists();
  const { toast } = useToast();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newListName, setNewListName] = useState('');
  const [editListId, setEditListId] = useState<number | null>(null);
  const [editListName, setEditListName] = useState('');
  // Handle creating a new list
  const handleCreateList = async () => {
    if (!newListName.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a list name',
        variant: 'destructive',
      });
      return;
    }

    await createList.mutateAsync({ name: newListName });
    setNewListName('');
    setIsCreateDialogOpen(false);
  };

  // Handle updating a list
  const handleUpdateList = async () => {
    if (!editListId || !editListName.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a list name',
        variant: 'destructive',
      });
      return;
    }

    await updateList.mutateAsync({ listId: editListId, name: editListName });
    setEditListId(null);
    setEditListName('');
    setIsEditDialogOpen(false);
  };

  // Handle deleting a list
  const handleDeleteList = async (listId: number) => {
    if (confirm('Are you sure you want to delete this list? This action cannot be undone.')) {
      await deleteList.mutateAsync({ listId });
      if (selectedListId === listId) {
        onSelectList(0);
      }
    }
  };

  // Handle opening the edit dialog
  const handleOpenEditDialog = (list: SavedArticleList) => {
    setEditListId(list.id);
    setEditListName(list.name);
    setIsEditDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Saved Lists</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsCreateDialogOpen(true)}
          className="flex items-center gap-1"
        >
          <Plus className="h-4 w-4" />
          New List
        </Button>
      </div>

      {/* List of saved article lists */}
      <ScrollArea className="h-[200px]">
        {isLoading ? (
          <div className="flex justify-center items-center h-20">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        ) : lists.length === 0 ? (
          <div className="text-center text-muted-foreground p-4">
            No saved lists yet. Create one to start saving articles.
          </div>
        ) : (
          <div className="space-y-2">
            {lists.map((list) => (
              <div
                key={list.id}
                className={`p-3 rounded-md flex justify-between items-center cursor-pointer ${
                  selectedListId === list.id ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
                }`}
                onClick={() => onSelectList(list.id)}
              >
                <div className="flex flex-col">
                  <span className="font-medium">{list.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {list.articleCount} article{list.articleCount !== 1 ? 's' : ''}
                  </span>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenEditDialog(list);
                    }}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteList(list.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>



      {/* Create List Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New List</DialogTitle>
            <DialogDescription>
              Create a new list to save articles from RSS feeds.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">List Name</Label>
              <Input
                id="name"
                placeholder="Enter list name"
                value={newListName}
                onChange={(e) => setNewListName(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateList} disabled={!newListName.trim() || createList.isPending}>
              {createList.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                  Creating...
                </>
              ) : (
                'Create List'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit List Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit List</DialogTitle>
            <DialogDescription>
              Update the name of your saved article list.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">List Name</Label>
              <Input
                id="edit-name"
                placeholder="Enter list name"
                value={editListName}
                onChange={(e) => setEditListName(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateList} disabled={!editListName.trim() || updateList.isPending}>
              {updateList.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                  Updating...
                </>
              ) : (
                'Update List'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>


    </div>
  );
}
