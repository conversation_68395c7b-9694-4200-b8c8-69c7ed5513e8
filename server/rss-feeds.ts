import { SQLiteStorage } from './sqliteStorage';
import { RssFeed, RssFeedItem, SavedArticleList, SavedArticle } from '@shared/schema';

export function implementRssFeedMethods(storage: SQLiteStorage) {
  // Initialize RSS feed tables
  storage.db.exec(`
    CREATE TABLE IF NOT EXISTS rss_feeds (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      url TEXT NOT NULL,
      last_refresh_time TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      display_order INTEGER DEFAULT 0,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );

    CREATE TABLE IF NOT EXISTS rss_feed_items (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      feed_id INTEGER NOT NULL,
      title TEXT NOT NULL,
      link TEXT NOT NULL,
      published_at TEXT NOT NULL,
      content TEXT,
      scraped_content TEXT,
      is_read INTEGER DEFAULT 0,
      <PERSON>OREI<PERSON><PERSON> KEY (feed_id) REFERENCES rss_feeds(id) ON DELETE CASCADE
    );

    CREATE TABLE IF NOT EXISTS saved_article_lists (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );

    CREATE TABLE IF NOT EXISTS saved_articles (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      list_id INTEGER NOT NULL,
      feed_id INTEGER NOT NULL,
      title TEXT NOT NULL,
      link TEXT NOT NULL,
      published_at TEXT NOT NULL,
      content TEXT,
      scraped_content TEXT,
      added_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (list_id) REFERENCES saved_article_lists(id) ON DELETE CASCADE,
      FOREIGN KEY (feed_id) REFERENCES rss_feeds(id) ON DELETE CASCADE
    );
  `);

  // Get all RSS feeds for a user
  storage.getRssFeeds = async function(userId: number): Promise<RssFeed[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT id, user_id as userId, name, url, last_refresh_time as lastRefreshTime, created_at as createdAt, display_order as displayOrder
        FROM rss_feeds
        WHERE user_id = ?
        ORDER BY display_order, name
      `);

      const feeds = stmt.all(userId);

      return feeds.map(feed => ({
        ...feed,
        lastRefreshTime: feed.lastRefreshTime ? new Date(feed.lastRefreshTime) : undefined,
        createdAt: new Date(feed.createdAt)
      }));
    } catch (error) {
      console.error('Error getting RSS feeds:', error);
      throw error;
    }
  };

  // Get a specific RSS feed
  storage.getRssFeed = async function(feedId: number): Promise<RssFeed | undefined> {
    try {
      const stmt = this.db.prepare(`
        SELECT id, user_id as userId, name, url, last_refresh_time as lastRefreshTime, created_at as createdAt, display_order as displayOrder
        FROM rss_feeds
        WHERE id = ?
      `);

      const feed = stmt.get(feedId);

      if (!feed) {
        return undefined;
      }

      return {
        ...feed,
        lastRefreshTime: feed.lastRefreshTime ? new Date(feed.lastRefreshTime) : undefined,
        createdAt: new Date(feed.createdAt)
      };
    } catch (error) {
      console.error('Error getting RSS feed:', error);
      throw error;
    }
  };

  // Add a new RSS feed
  storage.addRssFeed = async function(userId: number, feed: { name: string, url: string }): Promise<RssFeed> {
    try {
      // Get the highest display order for this user
      const maxOrderStmt = this.db.prepare(`
        SELECT MAX(display_order) as maxOrder
        FROM rss_feeds
        WHERE user_id = ?
      `);

      const maxOrderResult = maxOrderStmt.get(userId);
      const nextOrder = (maxOrderResult.maxOrder || 0) + 1;

      const stmt = this.db.prepare(`
        INSERT INTO rss_feeds (user_id, name, url, created_at, display_order)
        VALUES (?, ?, ?, ?, ?)
      `);

      const now = new Date().toISOString();
      const result = stmt.run(userId, feed.name, feed.url, now, nextOrder);
      const feedId = Number(result.lastInsertRowid);

      return {
        id: feedId,
        userId,
        name: feed.name,
        url: feed.url,
        createdAt: new Date(now),
        displayOrder: nextOrder
      };
    } catch (error) {
      console.error('Error adding RSS feed:', error);
      throw error;
    }
  };

  // Update an RSS feed
  storage.updateRssFeed = async function(feedId: number, updates: Partial<RssFeed>): Promise<RssFeed> {
    try {
      // Build the update query dynamically based on the provided updates
      const updateFields = [];
      const params = [];

      if (updates.name !== undefined) {
        updateFields.push('name = ?');
        params.push(updates.name);
      }

      if (updates.url !== undefined) {
        updateFields.push('url = ?');
        params.push(updates.url);
      }

      if (updates.lastRefreshTime !== undefined) {
        updateFields.push('last_refresh_time = ?');
        params.push(updates.lastRefreshTime.toISOString());
      }

      if (updates.displayOrder !== undefined) {
        updateFields.push('display_order = ?');
        params.push(updates.displayOrder);
      }

      if (updateFields.length === 0) {
        // No updates provided, just return the current feed
        return this.getRssFeed(feedId) as Promise<RssFeed>;
      }

      const updateQuery = `
        UPDATE rss_feeds
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;

      params.push(feedId);

      const stmt = this.db.prepare(updateQuery);
      stmt.run(...params);

      // Return the updated feed
      return this.getRssFeed(feedId) as Promise<RssFeed>;
    } catch (error) {
      console.error('Error updating RSS feed:', error);
      throw error;
    }
  };

  // Delete an RSS feed
  storage.deleteRssFeed = async function(feedId: number): Promise<void> {
    try {
      // First delete all items for this feed
      const deleteItemsStmt = this.db.prepare(`
        DELETE FROM rss_feed_items
        WHERE feed_id = ?
      `);

      deleteItemsStmt.run(feedId);

      // Then delete the feed
      const deleteFeedStmt = this.db.prepare(`
        DELETE FROM rss_feeds
        WHERE id = ?
      `);

      deleteFeedStmt.run(feedId);
    } catch (error) {
      console.error('Error deleting RSS feed:', error);
      throw error;
    }
  };

  // Get all items for a feed
  storage.getRssFeedItems = async function(feedId: number): Promise<RssFeedItem[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT id, feed_id as feedId, title, link, published_at as publishedAt,
               content, scraped_content as scrapedContent, is_read as isRead,
               financial_benefit_amount as financialBenefitAmount,
               financial_benefit_description as financialBenefitDescription,
               financial_benefit_type as financialBenefitType
        FROM rss_feed_items
        WHERE feed_id = ?
        ORDER BY published_at DESC
      `);

      const items = stmt.all(feedId);

      // Log the raw items to debug
      console.log(`Retrieved ${items.length} items for feed ${feedId}`);
      if (items.length > 0) {
        console.log('Sample raw item:', {
          id: items[0].id,
          financialBenefitAmount: items[0].financialBenefitAmount,
          type: typeof items[0].financialBenefitAmount
        });
      }

      const processedItems = items.map(item => {
        // Ensure financialBenefitAmount is a number
        const financialBenefitAmount =
          item.financialBenefitAmount !== null && item.financialBenefitAmount !== undefined
            ? Number(item.financialBenefitAmount)
            : 0;

        return {
          ...item,
          publishedAt: new Date(item.publishedAt),
          isRead: Boolean(item.isRead),
          financialBenefitAmount,
          financialBenefitDescription: item.financialBenefitDescription || null,
          financialBenefitType: item.financialBenefitType || null
        };
      });

      // Log the processed items to debug
      if (processedItems.length > 0) {
        console.log('Sample processed item:', {
          id: processedItems[0].id,
          financialBenefitAmount: processedItems[0].financialBenefitAmount,
          type: typeof processedItems[0].financialBenefitAmount
        });
      }

      return processedItems;
    } catch (error) {
      console.error('Error getting RSS feed items:', error);
      throw error;
    }
  };

  // Get a specific feed item
  storage.getRssFeedItem = async function(itemId: number): Promise<RssFeedItem | undefined> {
    try {
      const stmt = this.db.prepare(`
        SELECT id, feed_id as feedId, title, link, published_at as publishedAt,
               content, scraped_content as scrapedContent, is_read as isRead,
               financial_benefit_amount as financialBenefitAmount,
               financial_benefit_description as financialBenefitDescription,
               financial_benefit_type as financialBenefitType
        FROM rss_feed_items
        WHERE id = ?
      `);

      const item = stmt.get(itemId);

      if (!item) {
        return undefined;
      }

      // Ensure financialBenefitAmount is a number
      const financialBenefitAmount =
        item.financialBenefitAmount !== null && item.financialBenefitAmount !== undefined
          ? Number(item.financialBenefitAmount)
          : 0;

      return {
        ...item,
        publishedAt: new Date(item.publishedAt),
        isRead: Boolean(item.isRead),
        financialBenefitAmount,
        financialBenefitDescription: item.financialBenefitDescription || null,
        financialBenefitType: item.financialBenefitType || null
      };
    } catch (error) {
      console.error('Error getting RSS feed item:', error);
      throw error;
    }
  };

  // Get feed items by link
  storage.getRssFeedItemByLink = async function(feedId: number, link: string): Promise<RssFeedItem[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT id, feed_id as feedId, title, link, published_at as publishedAt,
               content, scraped_content as scrapedContent, is_read as isRead,
               financial_benefit_amount as financialBenefitAmount,
               financial_benefit_description as financialBenefitDescription,
               financial_benefit_type as financialBenefitType
        FROM rss_feed_items
        WHERE feed_id = ? AND link = ?
      `);

      const items = stmt.all(feedId, link);

      return items.map(item => {
        // Ensure financialBenefitAmount is a number
        const financialBenefitAmount =
          item.financialBenefitAmount !== null && item.financialBenefitAmount !== undefined
            ? Number(item.financialBenefitAmount)
            : 0;

        return {
          ...item,
          publishedAt: new Date(item.publishedAt),
          isRead: Boolean(item.isRead),
          financialBenefitAmount,
          financialBenefitDescription: item.financialBenefitDescription || null,
          financialBenefitType: item.financialBenefitType || null
        };
      });
    } catch (error) {
      console.error('Error getting RSS feed items by link:', error);
      throw error;
    }
  };

  // Add a new feed item
  storage.addRssFeedItem = async function(item: Partial<RssFeedItem>): Promise<RssFeedItem> {
    try {
      // Check if the financial benefit columns exist, and add them if they don't
      try {
        const tableInfo = this.db.prepare('PRAGMA table_info(rss_feed_items)').all();
        const hasFinancialBenefitAmount = tableInfo.some((column: any) => column.name === 'financial_benefit_amount');
        const hasFinancialBenefitDescription = tableInfo.some((column: any) => column.name === 'financial_benefit_description');
        const hasFinancialBenefitType = tableInfo.some((column: any) => column.name === 'financial_benefit_type');

        if (!hasFinancialBenefitAmount) {
          console.log('Adding financial_benefit_amount column to rss_feed_items table');
          this.db.exec('ALTER TABLE rss_feed_items ADD COLUMN financial_benefit_amount REAL DEFAULT 0');
        }

        if (!hasFinancialBenefitDescription) {
          console.log('Adding financial_benefit_description column to rss_feed_items table');
          this.db.exec('ALTER TABLE rss_feed_items ADD COLUMN financial_benefit_description TEXT');
        }

        if (!hasFinancialBenefitType) {
          console.log('Adding financial_benefit_type column to rss_feed_items table');
          this.db.exec('ALTER TABLE rss_feed_items ADD COLUMN financial_benefit_type TEXT');
        }
      } catch (error) {
        console.error('Error checking or adding financial benefit columns:', error);
      }

      const stmt = this.db.prepare(`
        INSERT INTO rss_feed_items (
          feed_id, title, link, published_at, content, scraped_content, is_read,
          financial_benefit_amount, financial_benefit_description, financial_benefit_type
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const result = stmt.run(
        item.feedId,
        item.title,
        item.link,
        item.publishedAt?.toISOString(),
        item.content || null,
        item.scrapedContent || null,
        item.isRead ? 1 : 0,
        item.financialBenefitAmount || 0,
        item.financialBenefitDescription || null,
        item.financialBenefitType || null
      );

      const itemId = Number(result.lastInsertRowid);

      return {
        id: itemId,
        feedId: item.feedId!,
        title: item.title!,
        link: item.link!,
        publishedAt: item.publishedAt!,
        content: item.content,
        scrapedContent: item.scrapedContent,
        isRead: item.isRead || false,
        financialBenefitAmount: item.financialBenefitAmount || 0,
        financialBenefitDescription: item.financialBenefitDescription,
        financialBenefitType: item.financialBenefitType
      };
    } catch (error) {
      console.error('Error adding RSS feed item:', error);
      throw error;
    }
  };

  // Update a feed item
  storage.updateRssFeedItem = async function(itemId: number, updates: Partial<RssFeedItem>): Promise<RssFeedItem> {
    try {
      // Build the update query dynamically based on the provided updates
      const updateFields = [];
      const params = [];

      if (updates.title !== undefined) {
        updateFields.push('title = ?');
        params.push(updates.title);
      }

      if (updates.link !== undefined) {
        updateFields.push('link = ?');
        params.push(updates.link);
      }

      if (updates.publishedAt !== undefined) {
        updateFields.push('published_at = ?');
        params.push(updates.publishedAt.toISOString());
      }

      if (updates.content !== undefined) {
        updateFields.push('content = ?');
        params.push(updates.content);
      }

      if (updates.scrapedContent !== undefined) {
        updateFields.push('scraped_content = ?');
        params.push(updates.scrapedContent);
      }

      if (updates.isRead !== undefined) {
        updateFields.push('is_read = ?');
        params.push(updates.isRead ? 1 : 0);
      }

      if (updates.financialBenefitAmount !== undefined) {
        updateFields.push('financial_benefit_amount = ?');
        params.push(updates.financialBenefitAmount);
      }

      if (updates.financialBenefitDescription !== undefined) {
        updateFields.push('financial_benefit_description = ?');
        params.push(updates.financialBenefitDescription);
      }

      if (updates.financialBenefitType !== undefined) {
        updateFields.push('financial_benefit_type = ?');
        params.push(updates.financialBenefitType);
      }

      if (updateFields.length === 0) {
        // No updates provided, just return the current item
        return this.getRssFeedItem(itemId) as Promise<RssFeedItem>;
      }

      const updateQuery = `
        UPDATE rss_feed_items
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;

      params.push(itemId);

      const stmt = this.db.prepare(updateQuery);
      stmt.run(...params);

      // Return the updated item
      return this.getRssFeedItem(itemId) as Promise<RssFeedItem>;
    } catch (error) {
      console.error('Error updating RSS feed item:', error);
      throw error;
    }
  };

  // Delete a feed item
  storage.deleteRssFeedItem = async function(itemId: number): Promise<void> {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM rss_feed_items
        WHERE id = ?
      `);

      stmt.run(itemId);
    } catch (error) {
      console.error('Error deleting RSS feed item:', error);
      throw error;
    }
  };

  // Update the order of multiple feeds at once
  storage.updateRssFeedsOrder = async function(userId: number, feedOrders: { id: number, displayOrder: number }[]): Promise<RssFeed[]> {
    try {
      // Begin a transaction
      this.db.prepare('BEGIN TRANSACTION').run();

      // Update each feed's display order
      const updateStmt = this.db.prepare(`
        UPDATE rss_feeds
        SET display_order = ?
        WHERE id = ? AND user_id = ?
      `);

      for (const feed of feedOrders) {
        updateStmt.run(feed.displayOrder, feed.id, userId);
      }

      // Commit the transaction
      this.db.prepare('COMMIT').run();

      // Return the updated feeds
      return this.getRssFeeds(userId);
    } catch (error) {
      // Rollback the transaction on error
      this.db.prepare('ROLLBACK').run();
      console.error('Error updating RSS feeds order:', error);
      throw error;
    }
  };

  // Get all saved article lists for a user
  storage.getSavedArticleLists = async function(userId: number): Promise<SavedArticleList[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT
          sal.id,
          sal.user_id as userId,
          sal.name,
          sal.created_at as createdAt,
          sal.updated_at as updatedAt,
          COUNT(sa.id) as articleCount
        FROM saved_article_lists sal
        LEFT JOIN saved_articles sa ON sal.id = sa.list_id
        WHERE sal.user_id = ?
        GROUP BY sal.id
        ORDER BY sal.updated_at DESC
      `);

      const lists = stmt.all(userId);

      return lists.map(list => ({
        ...list,
        createdAt: new Date(list.createdAt),
        updatedAt: new Date(list.updatedAt),
        articleCount: list.articleCount || 0
      }));
    } catch (error) {
      console.error('Error getting saved article lists:', error);
      throw error;
    }
  };

  // Get a specific saved article list
  storage.getSavedArticleList = async function(listId: number): Promise<SavedArticleList | undefined> {
    try {
      const stmt = this.db.prepare(`
        SELECT
          sal.id,
          sal.user_id as userId,
          sal.name,
          sal.created_at as createdAt,
          sal.updated_at as updatedAt,
          COUNT(sa.id) as articleCount
        FROM saved_article_lists sal
        LEFT JOIN saved_articles sa ON sal.id = sa.list_id
        WHERE sal.id = ?
        GROUP BY sal.id
      `);

      const list = stmt.get(listId);

      if (!list) {
        return undefined;
      }

      return {
        ...list,
        createdAt: new Date(list.createdAt),
        updatedAt: new Date(list.updatedAt),
        articleCount: list.articleCount || 0
      };
    } catch (error) {
      console.error('Error getting saved article list:', error);
      throw error;
    }
  };

  // Create a new saved article list
  storage.createSavedArticleList = async function(userId: number, name: string): Promise<SavedArticleList> {
    try {
      const now = new Date().toISOString();

      const stmt = this.db.prepare(`
        INSERT INTO saved_article_lists (user_id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);

      const result = stmt.run(userId, name, now, now);
      const listId = Number(result.lastInsertRowid);

      return {
        id: listId,
        userId,
        name,
        createdAt: new Date(now),
        updatedAt: new Date(now),
        articleCount: 0
      };
    } catch (error) {
      console.error('Error creating saved article list:', error);
      throw error;
    }
  };

  // Update a saved article list
  storage.updateSavedArticleList = async function(listId: number, name: string): Promise<SavedArticleList> {
    try {
      const now = new Date().toISOString();

      const stmt = this.db.prepare(`
        UPDATE saved_article_lists
        SET name = ?, updated_at = ?
        WHERE id = ?
      `);

      stmt.run(name, now, listId);

      // Return the updated list
      return this.getSavedArticleList(listId) as Promise<SavedArticleList>;
    } catch (error) {
      console.error('Error updating saved article list:', error);
      throw error;
    }
  };

  // Delete a saved article list
  storage.deleteSavedArticleList = async function(listId: number): Promise<void> {
    try {
      // First delete all articles in this list
      const deleteArticlesStmt = this.db.prepare(`
        DELETE FROM saved_articles
        WHERE list_id = ?
      `);

      deleteArticlesStmt.run(listId);

      // Then delete the list
      const deleteListStmt = this.db.prepare(`
        DELETE FROM saved_article_lists
        WHERE id = ?
      `);

      deleteListStmt.run(listId);
    } catch (error) {
      console.error('Error deleting saved article list:', error);
      throw error;
    }
  };

  // Get all saved articles in a list
  storage.getSavedArticles = async function(listId: number): Promise<SavedArticle[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT
          id,
          list_id as listId,
          feed_id as feedId,
          title,
          link,
          published_at as publishedAt,
          content,
          scraped_content as scrapedContent,
          added_at as addedAt
        FROM saved_articles
        WHERE list_id = ?
        ORDER BY added_at DESC
      `);

      const articles = stmt.all(listId);

      return articles.map(article => ({
        ...article,
        publishedAt: new Date(article.publishedAt),
        addedAt: new Date(article.addedAt)
      }));
    } catch (error) {
      console.error('Error getting saved articles:', error);
      throw error;
    }
  };

  // Add an article to a saved list
  storage.addSavedArticle = async function(listId: number, article: RssFeedItem): Promise<SavedArticle> {
    try {
      // Check if the article already exists in this list
      const checkStmt = this.db.prepare(`
        SELECT id FROM saved_articles
        WHERE list_id = ? AND link = ?
      `);

      const existingArticle = checkStmt.get(listId, article.link);

      if (existingArticle) {
        // Article already exists, return it
        const getStmt = this.db.prepare(`
          SELECT
            id,
            list_id as listId,
            feed_id as feedId,
            title,
            link,
            published_at as publishedAt,
            content,
            scraped_content as scrapedContent,
            added_at as addedAt
          FROM saved_articles
          WHERE id = ?
        `);

        const savedArticle = getStmt.get(existingArticle.id);

        return {
          ...savedArticle,
          publishedAt: new Date(savedArticle.publishedAt),
          addedAt: new Date(savedArticle.addedAt)
        };
      }

      // Article doesn't exist, add it
      const now = new Date().toISOString();

      const stmt = this.db.prepare(`
        INSERT INTO saved_articles (
          list_id,
          feed_id,
          title,
          link,
          published_at,
          content,
          scraped_content,
          added_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const result = stmt.run(
        listId,
        article.feedId,
        article.title,
        article.link,
        article.publishedAt.toISOString(),
        article.content || null,
        article.scrapedContent || null,
        now
      );

      const articleId = Number(result.lastInsertRowid);

      // Update the list's updated_at timestamp
      const updateListStmt = this.db.prepare(`
        UPDATE saved_article_lists
        SET updated_at = ?
        WHERE id = ?
      `);

      updateListStmt.run(now, listId);

      return {
        id: articleId,
        listId,
        feedId: article.feedId,
        title: article.title,
        link: article.link,
        publishedAt: article.publishedAt,
        content: article.content,
        scrapedContent: article.scrapedContent,
        addedAt: new Date(now)
      };
    } catch (error) {
      console.error('Error adding saved article:', error);
      throw error;
    }
  };

  // Remove an article from a saved list
  storage.removeSavedArticle = async function(articleId: number): Promise<void> {
    try {
      // Get the list ID before deleting the article
      const getListStmt = this.db.prepare(`
        SELECT list_id FROM saved_articles
        WHERE id = ?
      `);

      const result = getListStmt.get(articleId);

      if (!result) {
        throw new Error(`Article with ID ${articleId} not found`);
      }

      const listId = result.list_id;
      const now = new Date().toISOString();

      // Delete the article
      const deleteStmt = this.db.prepare(`
        DELETE FROM saved_articles
        WHERE id = ?
      `);

      deleteStmt.run(articleId);

      // Update the list's updated_at timestamp
      const updateListStmt = this.db.prepare(`
        UPDATE saved_article_lists
        SET updated_at = ?
        WHERE id = ?
      `);

      updateListStmt.run(now, listId);
    } catch (error) {
      console.error('Error removing saved article:', error);
      throw error;
    }
  };
}
