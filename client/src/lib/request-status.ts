// request-status.ts
// Utility for checking the status of server-side requests

import { useState, useEffect, useCallback } from 'react';
import { apiRequest } from './queryClient';

// Request status enum (must match server-side enum)
export enum RequestStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  TIMEOUT = 'timeout'
}

// Request interface
export interface Request {
  id: string;
  type: string;
  status: RequestStatus;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  error?: string;
  result?: any;
}

// Hook for checking request status
export function useRequestStatus(requestId?: string) {
  const [request, setRequest] = useState<Request | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [completed, setCompleted] = useState<boolean>(false);

  // Function to check request status
  const checkStatus = useCallback(async () => {
    if (!requestId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiRequest('GET', `/api/requests/${requestId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to check request status: ${response.statusText}`);
      }
      
      const data = await response.json();
      setRequest(data);
      
      // Check if the request is completed or failed
      if (
        data.status === RequestStatus.COMPLETED ||
        data.status === RequestStatus.FAILED ||
        data.status === RequestStatus.CANCELLED ||
        data.status === RequestStatus.TIMEOUT
      ) {
        setCompleted(true);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  }, [requestId]);

  // Function to cancel a request
  const cancelRequest = useCallback(async () => {
    if (!requestId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiRequest('POST', `/api/requests/${requestId}/cancel`);
      
      if (!response.ok) {
        throw new Error(`Failed to cancel request: ${response.statusText}`);
      }
      
      // Check status after cancellation
      await checkStatus();
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  }, [requestId, checkStatus]);

  // Effect to check status periodically
  useEffect(() => {
    if (!requestId || completed) return;

    // Check status immediately
    checkStatus();

    // Set up interval to check status every 2 seconds
    const intervalId = setInterval(checkStatus, 2000);

    // Clean up interval on unmount or when completed
    return () => {
      clearInterval(intervalId);
    };
  }, [requestId, checkStatus, completed]);

  return {
    request,
    loading,
    error,
    completed,
    checkStatus,
    cancelRequest
  };
}

// Hook for getting all requests for the current user
export function useUserRequests() {
  const [requests, setRequests] = useState<Request[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Function to fetch all requests
  const fetchRequests = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiRequest('GET', '/api/requests');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch requests: ${response.statusText}`);
      }
      
      const data = await response.json();
      setRequests(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  }, []);

  // Effect to fetch requests on mount
  useEffect(() => {
    fetchRequests();
  }, [fetchRequests]);

  return {
    requests,
    loading,
    error,
    fetchRequests
  };
}
