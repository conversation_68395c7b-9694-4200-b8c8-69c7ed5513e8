import { analyzeTranscriptForFinancialBenefits } from './services/financial-analysis';
import Database from 'better-sqlite3';
import path from 'path';

async function analyzeAllVideos() {
  console.log('Starting financial analysis for all videos...');
  
  try {
    // Connect to the database
    const dbPath = path.join(process.cwd(), 'data.db');
    console.log(`Opening database at: ${dbPath}`);
    const db = new Database(dbPath);
    
    // Get all videos with transcriptions that haven't been analyzed yet
    const stmt = db.prepare(`
      SELECT id, title, description, transcription FROM youtube_videos 
      WHERE has_transcription = 1 AND (has_financial_analysis IS NULL OR has_financial_analysis = 0)
      LIMIT 20
    `);
    
    const rows = stmt.all();
    if (rows.length === 0) {
      console.log('No videos found that need financial analysis');
      return;
    }
    
    console.log(`Found ${rows.length} videos that need financial analysis`);
    
    // Analyze each video
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      const videoId = row.id;
      const title = row.title;
      const description = row.description;
      const transcription = row.transcription;
      
      console.log(`\n[${i+1}/${rows.length}] Analyzing video: ${videoId}`);
      console.log(`Title: ${title}`);
      
      // Analyze the transcription
      const analysis = await analyzeTranscriptForFinancialBenefits(
        transcription,
        title,
        description
      );
      
      // Save the analysis to the database
      const updateStmt = db.prepare(`
        UPDATE youtube_videos
        SET financial_score = ?,
            financial_category = ?,
            financial_amount = ?,
            financial_timeline = ?,
            financial_recipients = ?,
            financial_steps = ?,
            financial_viral_potential = ?,
            financial_skepticism = ?,
            financial_analysis = ?,
            financial_timestamps = ?,
            has_financial_analysis = ?
        WHERE id = ?
      `);
      
      const result = updateStmt.run(
        analysis.score,
        analysis.category,
        analysis.amount || null,
        analysis.timeline || null,
        analysis.recipients || null,
        analysis.steps || null,
        analysis.viralPotential || null,
        analysis.skepticism || null,
        analysis.analysis,
        analysis.timestamps,
        1,
        videoId
      );
      
      console.log(`Updated database for video ${videoId}: ${result.changes} rows affected`);
      console.log(`Score: ${analysis.score}/100, Category: ${analysis.category}`);
    }
    
    console.log('\nFinancial analysis completed for all videos');
    
    // Close the database
    db.close();
  } catch (error) {
    console.error('Error in financial analysis:', error);
  }
}

// Run the analysis
analyzeAllVideos();
