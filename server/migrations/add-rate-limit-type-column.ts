import Database from 'better-sqlite3';
import path from 'path';

/**
 * Migration to add rate_limit_type column to openrouter_api_keys table
 */
async function main() {
  console.log('Starting migration to add rate_limit_type column to openrouter_api_keys table...');
  
  // Connect to the database
  const dbPath = path.resolve(process.cwd(), 'data.db');
  console.log(`Using database at: ${dbPath}`);
  const db = new Database(dbPath);
  
  try {
    // Check if the column already exists
    const tableInfo = db.prepare("PRAGMA table_info(openrouter_api_keys)").all();
    const hasRateLimitType = tableInfo.some((column: any) => column.name === 'rate_limit_type');
    
    if (hasRateLimitType) {
      console.log('rate_limit_type column already exists. No migration needed.');
    } else {
      console.log('Adding rate_limit_type column to openrouter_api_keys table');
      db.exec(`ALTER TABLE openrouter_api_keys ADD COLUMN rate_limit_type TEXT;`);
      console.log('Successfully added rate_limit_type column to openrouter_api_keys table');
    }
    
    // Log the current columns
    const updatedTableInfo = db.prepare("PRAGMA table_info(openrouter_api_keys)").all();
    console.log(`openrouter_api_keys table now has ${updatedTableInfo.length} columns`);
    console.log(`Column names: ${updatedTableInfo.map((col: any) => col.name).join(', ')}`);
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    // Close the database connection
    db.close();
  }
}

main().catch(console.error);
