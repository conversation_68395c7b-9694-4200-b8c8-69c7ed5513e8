import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { 
  Save, 
  Plus, 
  X, 
  Edit, 
  Trash2, 
  Lightbulb,
  Sparkles,
  Copy
} from 'lucide-react';

interface Strategy {
  id: string;
  title: string;
  description: string;
  samplePhrases: string[];
  tone: string;
  dateCreated: Date;
}

interface StrategiesModuleProps {
  strategies: Strategy[];
  setStrategies: React.Dispatch<React.SetStateAction<Strategy[]>>;
  toast: any;
}

const StrategiesModule: React.FC<StrategiesModuleProps> = ({
  strategies,
  setStrategies,
  toast
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [editingStrategy, setEditingStrategy] = useState<Strategy | null>(null);
  const [strategyForm, setStrategyForm] = useState({
    title: '',
    description: '',
    tone: '',
    samplePhrases: [] as string[]
  });
  const [newPhrase, setNewPhrase] = useState('');

  const presetStrategies = [
    {
      title: "Bold & Urgent",
      description: "High-energy, time-sensitive messaging for breaking news",
      tone: "Urgent, authoritative, action-oriented",
      samplePhrases: ["This changes everything", "You need to know this NOW", "Don't wait - this affects you directly"]
    },
    {
      title: "Empathetic Senior Voice",
      description: "Warm, understanding tone for senior audience concerns",
      tone: "Compassionate, respectful, reassuring",
      samplePhrases: ["I understand your concerns", "You've worked hard for this", "Let me walk you through this step by step"]
    },
    {
      title: "Storytelling + Facts",
      description: "Narrative approach combined with solid information",
      tone: "Engaging, informative, relatable",
      samplePhrases: ["Here's what happened", "Let me tell you a story", "The facts speak for themselves"]
    },
    {
      title: "Reassuring Tone for Fixed Income",
      description: "Calming approach for financial anxiety",
      tone: "Stable, confident, supportive",
      samplePhrases: ["You're going to be okay", "This is good news for you", "Here's what this means for your budget"]
    }
  ];

  const handleSaveStrategy = () => {
    if (!strategyForm.title.trim()) {
      toast({
        title: "Validation Error",
        description: "Strategy title is required",
        variant: "destructive"
      });
      return;
    }

    const strategyData: Strategy = {
      id: editingStrategy?.id || `strategy_${Date.now()}`,
      title: strategyForm.title.trim(),
      description: strategyForm.description.trim(),
      tone: strategyForm.tone.trim(),
      samplePhrases: strategyForm.samplePhrases,
      dateCreated: editingStrategy?.dateCreated || new Date()
    };

    if (editingStrategy) {
      setStrategies(prev => prev.map(s => 
        s.id === editingStrategy.id ? strategyData : s
      ));
      toast({
        title: "Strategy Updated",
        description: `"${strategyData.title}" has been updated successfully`
      });
    } else {
      setStrategies(prev => [...prev, strategyData]);
      toast({
        title: "Strategy Saved",
        description: `"${strategyData.title}" has been saved to your library`
      });
    }

    resetForm();
  };

  const handleDeleteStrategy = (strategyId: string) => {
    const strategy = strategies.find(s => s.id === strategyId);
    if (strategy && window.confirm(`Are you sure you want to delete "${strategy.title}"?`)) {
      setStrategies(prev => prev.filter(s => s.id !== strategyId));
      toast({
        title: "Strategy Deleted",
        description: "Strategy has been deleted successfully"
      });
    }
  };

  const handleEditStrategy = (strategy: Strategy) => {
    setEditingStrategy(strategy);
    setStrategyForm({
      title: strategy.title,
      description: strategy.description,
      tone: strategy.tone,
      samplePhrases: [...strategy.samplePhrases]
    });
    setIsCreating(true);
  };

  const handleAddPresetStrategy = (preset: any) => {
    const strategyData: Strategy = {
      id: `strategy_${Date.now()}`,
      title: preset.title,
      description: preset.description,
      tone: preset.tone,
      samplePhrases: preset.samplePhrases,
      dateCreated: new Date()
    };

    setStrategies(prev => [...prev, strategyData]);
    toast({
      title: "Preset Added",
      description: `"${preset.title}" has been added to your strategies`
    });
  };

  const resetForm = () => {
    setStrategyForm({
      title: '',
      description: '',
      tone: '',
      samplePhrases: []
    });
    setIsCreating(false);
    setEditingStrategy(null);
    setNewPhrase('');
  };

  const handleAddPhrase = () => {
    if (newPhrase.trim() && !strategyForm.samplePhrases.includes(newPhrase.trim())) {
      setStrategyForm(prev => ({
        ...prev,
        samplePhrases: [...prev.samplePhrases, newPhrase.trim()]
      }));
      setNewPhrase('');
    }
  };

  const handleRemovePhrase = (phraseToRemove: string) => {
    setStrategyForm(prev => ({
      ...prev,
      samplePhrases: prev.samplePhrases.filter(phrase => phrase !== phraseToRemove)
    }));
  };

  const handleCopyStrategy = (strategy: Strategy) => {
    const content = `Strategy: ${strategy.title}\n\nDescription: ${strategy.description}\n\nTone: ${strategy.tone}\n\nSample Phrases:\n${strategy.samplePhrases.map(phrase => `- ${phrase}`).join('\n')}`;
    navigator.clipboard.writeText(content);
    toast({
      title: "Copied to Clipboard",
      description: `"${strategy.title}" strategy has been copied`
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5" />
                Entertainment Strategies
              </CardTitle>
              <CardDescription>
                Save custom writing prompts and tonal strategies for script generation
              </CardDescription>
            </div>
            <Button onClick={() => setIsCreating(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Strategy
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {isCreating && (
            <div className="space-y-4 p-4 border rounded-lg">
              <h3 className="font-medium">
                {editingStrategy ? 'Edit Strategy' : 'Create New Strategy'}
              </h3>
              
              <div className="space-y-2">
                <Label htmlFor="strategyTitle">Strategy Title *</Label>
                <Input
                  id="strategyTitle"
                  placeholder="e.g., Bold & Urgent, Empathetic Senior Voice"
                  value={strategyForm.title}
                  onChange={(e) => setStrategyForm(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="strategyDescription">Description</Label>
                <Textarea
                  id="strategyDescription"
                  placeholder="Describe when and how to use this strategy"
                  value={strategyForm.description}
                  onChange={(e) => setStrategyForm(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="strategyTone">Tone</Label>
                <Input
                  id="strategyTone"
                  placeholder="e.g., Urgent, authoritative, action-oriented"
                  value={strategyForm.tone}
                  onChange={(e) => setStrategyForm(prev => ({ ...prev, tone: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label>Sample Phrases</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Add sample phrases"
                    value={newPhrase}
                    onChange={(e) => setNewPhrase(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddPhrase())}
                  />
                  <Button onClick={handleAddPhrase} variant="outline" size="sm">
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {strategyForm.samplePhrases.map(phrase => (
                    <Badge key={phrase} variant="secondary" className="flex items-center gap-1">
                      {phrase}
                      <X 
                        className="h-3 w-3 cursor-pointer hover:text-destructive" 
                        onClick={() => handleRemovePhrase(phrase)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleSaveStrategy}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Strategy
                </Button>
                <Button onClick={resetForm} variant="outline">
                  Cancel
                </Button>
              </div>
            </div>
          )}

          {/* Preset Strategies */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              <h3 className="font-medium">Preset Strategies</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {presetStrategies.map((preset, index) => (
                <Card key={index} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium">{preset.title}</h4>
                      <p className="text-sm text-muted-foreground mt-1">{preset.description}</p>
                      <p className="text-xs text-blue-600 mt-2">Tone: {preset.tone}</p>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {preset.samplePhrases.slice(0, 2).map(phrase => (
                          <Badge key={phrase} variant="outline" className="text-xs">
                            "{phrase}"
                          </Badge>
                        ))}
                        {preset.samplePhrases.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{preset.samplePhrases.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button 
                      onClick={() => handleAddPresetStrategy(preset)} 
                      variant="ghost" 
                      size="sm"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Saved Strategies */}
          <div className="space-y-4">
            <h3 className="font-medium">Your Strategies ({strategies.length})</h3>
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {strategies.map(strategy => (
                  <Card key={strategy.id} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium">{strategy.title}</h4>
                        {strategy.description && (
                          <p className="text-sm text-muted-foreground mt-1">{strategy.description}</p>
                        )}
                        {strategy.tone && (
                          <p className="text-xs text-blue-600 mt-2">Tone: {strategy.tone}</p>
                        )}
                        <div className="flex flex-wrap gap-1 mt-2">
                          {strategy.samplePhrases.slice(0, 3).map(phrase => (
                            <Badge key={phrase} variant="outline" className="text-xs">
                              "{phrase}"
                            </Badge>
                          ))}
                          {strategy.samplePhrases.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{strategy.samplePhrases.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-1 ml-4">
                        <Button onClick={() => handleCopyStrategy(strategy)} variant="ghost" size="sm">
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button onClick={() => handleEditStrategy(strategy)} variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button onClick={() => handleDeleteStrategy(strategy.id)} variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
                {strategies.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No strategies saved yet. Add some presets or create your own!</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StrategiesModule;
