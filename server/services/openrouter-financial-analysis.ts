/**
 * OpenRouter Financial Analysis Service
 *
 * This service uses the OpenRouter API to analyze YouTube video transcripts
 * for financial benefits and related content.
 */

import { generateOpenRouterJsonResponse, getUserSelectedOpenRouterModel } from './openrouter-service';
import { storage } from '../storage';

// Interface for extracted financial benefit information
export interface ExtractedFinancialBenefitInfo {
  benefitAmounts: string[];
  expectedArrivalDate: string;
  eligiblePeople: string;
  proofOrSource: string;
  actionsToClaim: string;
}

// Interface for financial analysis results
export interface OpenRouterFinancialAnalysis {
  hasBenefit: boolean;
  certaintyScore: number;
  benefitType: string;
  benefitDescription: string;
  extractedInfo: ExtractedFinancialBenefitInfo;
  priorityTag: 'high' | 'medium' | 'low' | 'none';
  score: number;
  reasoning: string;
}

// No system prompt - removed as requested by user
export const FINANCIAL_ANALYSIS_SYSTEM_PROMPT = null;

/**
 * Create a prompt for financial analysis
 * @param transcript The video transcript
 * @param title The video title
 * @param description The video description (optional)
 * @param publishedAt The video published date (optional)
 * @param viewCount The video view count (optional)
 * @param customPrompt Custom prompt template (optional)
 * @returns The formatted prompt
 */
export function createFinancialAnalysisPrompt(
  transcript: string,
  title: string = '',
  description: string = '',
  customPrompt?: string,
  publishedAt?: Date | string | null,
  viewCount?: number | string | null
): string {
  // Require a custom prompt - no default template
  if (!customPrompt) {
    console.error('🤖 OpenRouter API: No custom prompt provided for analysis');
    throw new Error('A custom prompt is required for analysis. Please select a prompt in the settings.');
  }

  // Use the provided custom prompt
  const promptTemplate = customPrompt;

  // Format the published date if available
  let formattedDate = 'Not provided';
  if (publishedAt) {
    try {
      // Check if publishedAt is a Date object or a string
      if (publishedAt instanceof Date) {
        formattedDate = publishedAt.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      } else if (typeof publishedAt === 'string') {
        // If it's a string, convert to Date first
        const dateObj = new Date(publishedAt);
        if (!isNaN(dateObj.getTime())) {
          formattedDate = dateObj.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        } else {
          // If date parsing fails, use the string as is
          formattedDate = publishedAt || 'Not provided';
        }
      }
    } catch (error) {
      console.error('Error formatting published date:', error);
      // Use the string representation if available
      if (publishedAt && typeof publishedAt.toString === 'function') {
        formattedDate = publishedAt.toString();
      } else {
        formattedDate = String(publishedAt) || 'Not provided';
      }
    }
  }

  // Format the view count if available
  let formattedViewCount = 'Not provided';
  if (viewCount) {
    try {
      // Check if viewCount is a number or a string
      if (typeof viewCount === 'number') {
        formattedViewCount = viewCount.toLocaleString();
      } else if (typeof viewCount === 'string') {
        // If it's a string, convert to number first
        formattedViewCount = parseInt(viewCount, 10).toLocaleString();
      }
    } catch (error) {
      console.error('Error formatting view count:', error);
      // Use the string representation if available
      formattedViewCount = viewCount.toString() || 'Not provided';
    }
  }

  // Replace placeholders with actual content
  let prompt = promptTemplate
    .replace(/\[Video Title\]/g, title)
    .replace(/\[Video Description\]/g, description || 'Not provided')
    .replace(/\[Published Date\]/g, formattedDate)
    .replace(/\[View Count\]/g, formattedViewCount)
    .replace(/\[Paste transcript here\]/g, transcript)
    .replace(/TITLE: "\[title\]"/g, `TITLE: "${title}"`)
    .replace(/DESCRIPTION: "\[description\]"/g, `DESCRIPTION: "${description || 'Not provided'}"`)

    // Add more replacements for other possible formats
    .replace(/\[title\]/g, title)
    .replace(/\[description\]/g, description || 'Not provided')
    .replace(/\[transcript\]/g, transcript);

  // Log the first 100 characters of the prompt to verify it's working
  console.log(`🤖 OpenRouter API: First 100 chars of prompt: ${prompt.substring(0, 100)}...`);

  return prompt;
}

/**
 * Analyze a YouTube video transcript for financial benefits using OpenRouter API
 * @param userId User ID for getting the selected model
 * @param transcript The video transcript
 * @param title The video title
 * @param description The video description (optional)
 * @param publishedAt The video published date (optional)
 * @param viewCount The video view count (optional)
 * @returns Financial analysis results
 */
export async function analyzeTranscriptWithOpenRouter(
  userId: number,
  transcript: string,
  title: string,
  description?: string,
  publishedAt?: Date,
  viewCount?: number,
  customPrompt?: string,
  modelOverride?: string
): Promise<OpenRouterFinancialAnalysis & { modelUsed: string, rawData?: string, promptName?: string }> {
  // Define promptName outside the try-catch block so it's accessible in both
  let promptName = "Custom Prompt";

  try {
    // Use the model override if provided, otherwise get the user's selected model
    let model;
    if (modelOverride) {
      model = modelOverride;
      console.log(`🤖 OpenRouter API: Using model override: ${model}`);
    } else {
      // Get the user's selected model from settings
      model = await getUserSelectedOpenRouterModel(userId);
      console.log(`🤖 OpenRouter API: Using user's selected model: ${model}`);
    }

    // Get user settings to check for custom prompt
    const userSettings = await storage.getSettings(userId);
    let userCustomPrompt = null;

    // If user has a selected prompt ID, use that prompt instead (preferred method)
    if (userSettings?.selectedPromptId) {
      try {
        const selectedPrompt = await storage.getOpenRouterPrompt(userSettings.selectedPromptId);
        if (selectedPrompt) {
          console.log(`🤖 OpenRouter API: Using selected prompt: ${selectedPrompt.name}`);
          console.log(`🤖 OpenRouter API: Selected prompt text (first 100 chars): ${selectedPrompt.promptText.substring(0, 100)}...`);
          userCustomPrompt = selectedPrompt.promptText;
          promptName = selectedPrompt.name;
        }
      } catch (error) {
        console.error('🤖 OpenRouter API: Error getting selected prompt:', error);
      }
    }

    // If no selected prompt, try using the custom prompt from settings
    if (!userCustomPrompt && userSettings?.openRouterAnalysisPrompt) {
      console.log(`🤖 OpenRouter API: Using custom prompt from settings`);
      userCustomPrompt = userSettings.openRouterAnalysisPrompt;
    }

    // If still no prompt, use the provided custom prompt parameter
    if (!userCustomPrompt && customPrompt) {
      console.log(`🤖 OpenRouter API: Using custom prompt from parameter`);
      userCustomPrompt = customPrompt;
    }

    // If still no prompt, throw an error
    if (!userCustomPrompt) {
      throw new Error('No prompt available. Please select a prompt in the settings.');
    }

    // Create the analysis prompt
    const prompt = createFinancialAnalysisPrompt(transcript, title, description, userCustomPrompt, publishedAt, viewCount);
    console.log(`🤖 OpenRouter API: Created analysis prompt (length: ${prompt.length} characters)`);

    // Generate the analysis using OpenRouter
    console.log(`🤖 OpenRouter API: Sending request to OpenRouter model: ${model}`);
    console.log(`🤖 OpenRouter API: Not using any system prompt as requested by user`);

    // Add a timeout for the analysis
    let analysisPromise = generateOpenRouterJsonResponse<OpenRouterFinancialAnalysis>(
      model,
      prompt
      // No system prompt parameter - completely removed
    );

    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Analysis timed out after 180 seconds with model ${model}`));
      }, 180000); // 3 minute timeout
    });

    // Race the analysis against the timeout
    console.log(`🤖 OpenRouter API: Waiting for analysis with timeout of 180 seconds...`);
    const analysis = await Promise.race([analysisPromise, timeoutPromise]) as OpenRouterFinancialAnalysis;

    console.log(`🤖 OpenRouter API: Analysis completed successfully with model: ${model}`);

    // Validate the analysis structure
    if (!analysis) {
      console.error('🤖 OpenRouter API: Null or undefined analysis returned from OpenRouter');
      throw new Error('Null or undefined analysis returned from OpenRouter');
    }

    // Log the raw analysis for debugging
    console.log('🤖 OpenRouter API: Raw analysis:', JSON.stringify(analysis, null, 2));

    // Log the extractedInfo and benefitAmounts specifically
    console.log('🤖 OpenRouter API: extractedInfo:', analysis.extractedInfo);
    console.log('🤖 OpenRouter API: benefitAmounts:', analysis.extractedInfo?.benefitAmounts);

    // Ensure all required fields are present
    if (analysis.hasBenefit === undefined) {
      console.error('🤖 OpenRouter API: Missing hasBenefit field in analysis');
      analysis.hasBenefit = false;
    }

    if (analysis.certaintyScore === undefined) {
      console.error('🤖 OpenRouter API: Missing certaintyScore field in analysis');
      analysis.certaintyScore = 0;
    }

    if (!analysis.benefitType) {
      analysis.benefitType = 'none';
    }

    if (!analysis.benefitDescription) {
      analysis.benefitDescription = 'No financial benefits identified';
    }

    if (!analysis.extractedInfo) {
      analysis.extractedInfo = {
        benefitAmounts: [],
        expectedArrivalDate: '',
        eligiblePeople: '',
        proofOrSource: '',
        actionsToClaim: ''
      };
    }

    // If benefitAmounts is empty, try to extract them from the benefitDescription
    if (!analysis.extractedInfo.benefitAmounts || analysis.extractedInfo.benefitAmounts.length === 0) {
      console.log('🤖 OpenRouter API: No benefit amounts found, trying to extract from description');
      if (analysis.benefitDescription) {
        // Helper function to extract dollar amounts from text
        const extractDollarAmounts = (text: string): string[] => {
          if (!text) return [];

          // First try to find simple dollar amounts
          const simpleMatches = text.match(/\$\d+(?:,\d+)*(?:\.\d+)?/g);

          if (simpleMatches && simpleMatches.length > 0) {
            console.log('🤖 OpenRouter API: Found simple dollar amounts:', simpleMatches);
            return simpleMatches;
          }

          // If no simple matches, try to extract from more complex formats
          // Look for patterns like "$600 monthly benefit" or "$700 monthly benefit check"
          const complexPattern = /\$(\d+(?:,\d+)*(?:\.\d+)?)\s+(?:monthly|annual|weekly|yearly|per\s+\w+)\s+(?:benefit|payment|check|amount)/gi;
          const complexMatches = [];
          let match;

          while ((match = complexPattern.exec(text)) !== null) {
            if (match[1]) {
              complexMatches.push(`$${match[1]}`);
            }
          }

          if (complexMatches.length > 0) {
            console.log('🤖 OpenRouter API: Found complex dollar amounts:', complexMatches);
            return complexMatches;
          }

          // Try to handle the specific format with COLA
          // Example: "$600 monthly benefit check (+$12 with 2% COLA)"
          const colaPattern = /\$(\d+(?:,\d+)*(?:\.\d+)?)\s+(?:.*?)(?:\(\+\$\d+(?:,\d+)*(?:\.\d+)?\s+with\s+\d+%\s+COLA\))/gi;
          const colaMatches = [];
          let colaMatch;

          while ((colaMatch = colaPattern.exec(text)) !== null) {
            if (colaMatch[1]) {
              colaMatches.push(`$${colaMatch[1]}`);
            }
          }

          if (colaMatches.length > 0) {
            console.log('🤖 OpenRouter API: Found COLA dollar amounts:', colaMatches);
            return colaMatches;
          }

          return [];
        };

        const matches = extractDollarAmounts(analysis.benefitDescription);
        if (matches && matches.length > 0) {
          console.log('🤖 OpenRouter API: Extracted benefit amounts from description:', matches);
          analysis.extractedInfo.benefitAmounts = matches;
        }
      }
    }

    // Log the benefit amounts after extraction
    console.log('🤖 OpenRouter API: Final benefit amounts:', analysis.extractedInfo.benefitAmounts);

    if (!analysis.priorityTag) {
      analysis.priorityTag = 'none';
    }

    if (analysis.score === undefined) {
      analysis.score = 0;
    }

    if (!analysis.reasoning) {
      analysis.reasoning = 'No reasoning provided';
    }

    // Create a complete analysis object with all fields
    const completeAnalysis = {
      hasBenefit: analysis.hasBenefit,
      certaintyScore: analysis.certaintyScore,
      benefitType: analysis.benefitType,
      benefitDescription: analysis.benefitDescription,
      extractedInfo: {
        ...analysis.extractedInfo,
        // Ensure benefitAmounts is always an array
        benefitAmounts: Array.isArray(analysis.extractedInfo.benefitAmounts) ?
          analysis.extractedInfo.benefitAmounts : []
      },
      priorityTag: analysis.priorityTag,
      score: analysis.score,
      reasoning: analysis.reasoning,
      modelUsed: model
    };

    // Log the final analysis object
    console.log('🤖 OpenRouter API: Final analysis object:', JSON.stringify(completeAnalysis, null, 2));

    // Add the model used to the analysis and store the complete analysis as raw data
    const result = {
      ...completeAnalysis,
      promptName: promptName || 'default',
      rawData: JSON.stringify({
        ...completeAnalysis,
        promptName: promptName || 'default',
        prompt
        // No system prompt as requested by user
      }, null, 2)
    };

    console.log(`🤖 OpenRouter API: Using prompt name in analysis result: ${promptName || 'default'}`);

    return result;
  } catch (error) {
    console.error('🤖 OpenRouter API: Error analyzing transcript:', error);

    // Check if this is a rate limit error
    let errorMessage = error.message || 'Unknown error';
    let errorType = 'general';

    if (errorMessage.includes('rate limit') ||
        errorMessage.includes('Daily limit') ||
        errorMessage.includes('daily limit') ||
        errorMessage.includes('free-models-per-day')) {
      errorType = 'rate_limit';
      console.log('🤖 OpenRouter API: Rate limit error detected');
      // Add a suggestion to the error message
      errorMessage += '. Try changing the model in settings to a different model.';
    }

    // Create a complete error analysis object
    const errorAnalysis = {
      hasBenefit: false,
      certaintyScore: 0,
      benefitType: 'Error',
      benefitDescription: `Error analyzing transcript: ${errorMessage}`,
      extractedInfo: {
        benefitAmounts: [],
        expectedArrivalDate: '',
        eligiblePeople: '',
        proofOrSource: '',
        actionsToClaim: ''
      },
      priorityTag: 'none',
      score: 0,
      reasoning: `Analysis failed with error: ${errorMessage}`,
      modelUsed: modelOverride || 'Error',
      errorType: errorType
    };

    // Return the error analysis with raw data
    return {
      ...errorAnalysis,
      promptName: promptName, // Use promptName directly since it's now accessible in this scope
      rawData: JSON.stringify({
        ...errorAnalysis,
        promptName: promptName,
        error: errorMessage,
        errorType: errorType
      }, null, 2)
    };
  }
}

/**
 * Convert OpenRouter analysis to app format
 * @param analysis The OpenRouter analysis
 * @returns The formatted analysis for the app
 */
export function convertOpenRouterAnalysisToAppFormat(analysis: OpenRouterFinancialAnalysis & { modelUsed: string, promptName?: string }) {
  return {
    hasBenefit: analysis.hasBenefit,
    certaintyScore: analysis.certaintyScore,
    benefitType: analysis.benefitType,
    benefitDescription: analysis.benefitDescription,
    extractedInfo: analysis.extractedInfo,
    priorityTag: analysis.priorityTag,
    score: analysis.score,
    reasoning: analysis.reasoning,
    modelUsed: analysis.modelUsed,
    promptName: analysis.promptName
  };
}
