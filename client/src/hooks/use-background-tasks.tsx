import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from './use-toast';
import { apiRequest } from '@/lib/queryClient';
import { logger } from '@/lib/logger';

// Task status enum
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Task priority enum
export enum TaskPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2
}

// Task type
export interface Task {
  id: string;
  name: string;
  description: string;
  status: TaskStatus;
  progress: number;
  error?: string;
  result?: any;
  userId: number;
  createdAt: Date;
  updatedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  priority: TaskPriority;
  type: string;
  data: any;
}

// Background tasks context type
interface BackgroundTasksContextType {
  tasks: Task[];
  recentTasks: Task[];
  isLoading: boolean;
  error: Error | null;
  activeTasksCount: number;
  fetchTask: (id: string) => Promise<Task | null>;
  cancelTask: (id: string) => Promise<boolean>;
  cancelAllTasks: () => Promise<boolean>;
  clearTaskHistory: () => Promise<boolean>;
  retryFailedTasks: () => Promise<boolean>;
  fetchTranscription: (videoId: string) => Promise<Task>;
  analyzeFinancial: (videoId: string) => Promise<Task>;
  refreshChannel: (channelId: string) => Promise<Task>;
  refetchTasks: () => void;
}

// Create context
const BackgroundTasksContext = createContext<BackgroundTasksContextType | null>(null);

// Provider component
export function BackgroundTasksProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [error, setError] = useState<Error | null>(null);

  // Track whether there are active tasks
  const [hasActiveTasks, setHasActiveTasks] = useState(false);
  const [activeTasksCount, setActiveTasksCount] = useState(0);

  // Track if the window is visible
  const [isWindowVisible, setIsWindowVisible] = useState<boolean>(document.visibilityState === 'visible');

  // Listen for visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsWindowVisible(document.visibilityState === 'visible');
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Fetch all tasks
  const {
    data: tasks = [],
    isLoading,
    refetch: refetchTasks
  } = useQuery<Task[]>({
    queryKey: ['/api/tasks/all'],
    queryFn: async () => {
      const response = await fetch('/api/tasks/all', {
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch tasks: ${response.status}`);
      }
      const data = await response.json();

      // Check if there are any active tasks
      const activeTasks = data.filter(task =>
        task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING
      );

      // Log active tasks count for debugging
      logger.verbose(`Fetched ${data.length} tasks, ${activeTasks.length} active tasks`);

      // Update the active tasks state
      setHasActiveTasks(activeTasks.length > 0);
      setActiveTasksCount(activeTasks.length);

      return data;
    },
    // Adjust polling based on window visibility and active tasks
    refetchInterval: !isWindowVisible ? 60000 : (hasActiveTasks ? 3000 : 45000),
    // Reduce polling when window is not visible
    refetchIntervalInBackground: true,
  });

  // Fetch recent tasks
  const {
    data: recentTasks = []
  } = useQuery<Task[]>({
    queryKey: ['/api/tasks/recent'],
    queryFn: async () => {
      const response = await fetch('/api/tasks/recent?limit=5', {
        credentials: 'include'
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch recent tasks: ${response.status}`);
      }
      const data = await response.json();
      return data;
    },
    // Adjust polling based on window visibility and active tasks
    refetchInterval: !isWindowVisible ? 60000 : (hasActiveTasks ? 3000 : 45000),
    // Disable this query entirely if we're not on a page that shows tasks
    enabled: (window.location.pathname.includes('/tasks') || hasActiveTasks),
    // Reduce polling when window is not visible
    refetchIntervalInBackground: true,
  });

  // Fetch a specific task
  const fetchTask = async (id: string): Promise<Task | null> => {
    try {
      const task = await apiRequest(`/api/tasks/${id}`);
      return task;
    } catch (error) {
      logger.error('Error fetching task:', error);
      setError(error as Error);
      return null;
    }
  };

  // Cancel a task
  const cancelTaskMutation = useMutation({
    mutationFn: (id: string) => apiRequest(`/api/tasks/${id}/cancel`, { method: 'POST' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/tasks'] });
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/recent'] });
    },
  });

  const cancelTask = async (id: string): Promise<boolean> => {
    try {
      await cancelTaskMutation.mutateAsync(id);
      return true;
    } catch (error) {
      logger.error('Error cancelling task:', error);
      setError(error as Error);
      return false;
    }
  };

  // Cancel all tasks
  const cancelAllTasksMutation = useMutation({
    mutationFn: () => apiRequest('/api/tasks/cancel-all', { method: 'POST' }),
    onSuccess: (data) => {
      logger.debug('Cancel all tasks response:', data);
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/all'] });
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/recent'] });

      if (data.cancelledCount > 0) {
        // Create a detailed message about what was cancelled
        const { pending, running } = data.cancelledByStatus || { pending: 0, running: 0 };
        const details = [];
        if (pending > 0) details.push(`${pending} pending`);
        if (running > 0) details.push(`${running} running`);

        const detailsText = details.join(', ');

        toast({
          title: 'Tasks Cancelled',
          description: `${data.cancelledCount} tasks have been cancelled (${detailsText}).`,
        });
      } else {
        toast({
          title: 'No Tasks Cancelled',
          description: 'No active tasks were found to cancel.',
          variant: 'default',
        });
      }
    },
  });

  const cancelAllTasks = async (): Promise<boolean> => {
    try {
      // Get the current active tasks count for verification
      const activeTasks = tasks.filter(task =>
        task.status === TaskStatus.PENDING ||
        task.status === TaskStatus.RUNNING
      );
      logger.debug(`Attempting to cancel ${activeTasks.length} active tasks`);

      if (activeTasks.length === 0) {
        toast({
          title: 'No Active Tasks',
          description: 'There are no active tasks to cancel.',
          variant: 'default',
        });
        return false;
      }

      const result = await cancelAllTasksMutation.mutateAsync();
      logger.debug('Cancel all result:', result);

      // Force a refetch to update the UI immediately
      await queryClient.invalidateQueries({ queryKey: ['/api/tasks/all'] });
      await queryClient.invalidateQueries({ queryKey: ['/api/tasks/recent'] });
      refetchTasks();

      return result.cancelledCount > 0;
    } catch (error) {
      logger.error('Error cancelling all tasks:', error);
      setError(error as Error);
      toast({
        title: 'Error',
        description: 'Failed to cancel all tasks: ' + (error as Error).message,
        variant: 'destructive',
      });
      return false;
    }
  };

  // Clear task history
  const clearTaskHistoryMutation = useMutation({
    mutationFn: () => apiRequest('/api/tasks/clear-history', { method: 'POST' }),
    onSuccess: (data) => {
      logger.debug('Clear history response:', data);
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/all'] });
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/recent'] });

      if (data.clearedCount > 0) {
        // Create a detailed message about what was cleared
        const { completed, failed, cancelled } = data.clearedByStatus || { completed: 0, failed: 0, cancelled: 0 };
        const details = [];
        if (completed > 0) details.push(`${completed} completed`);
        if (failed > 0) details.push(`${failed} failed`);
        if (cancelled > 0) details.push(`${cancelled} cancelled`);

        const detailsText = details.join(', ');

        toast({
          title: 'Task History Cleared',
          description: `${data.clearedCount} tasks have been cleared (${detailsText}).`,
        });
      } else {
        toast({
          title: 'No Tasks Cleared',
          description: 'No tasks were found to clear.',
          variant: 'default',
        });
      }
    },
  });

  const clearTaskHistory = async (): Promise<boolean> => {
    try {
      // Get the current completed and failed tasks count for verification
      const tasksToRemove = tasks.filter(task =>
        task.status === TaskStatus.COMPLETED ||
        task.status === TaskStatus.FAILED ||
        task.status === TaskStatus.CANCELLED
      );
      logger.debug(`Attempting to clear ${tasksToRemove.length} tasks from history`);

      if (tasksToRemove.length === 0) {
        toast({
          title: 'No Tasks to Clear',
          description: 'There are no completed, failed, or cancelled tasks to clear.',
          variant: 'default',
        });
        return false;
      }

      const result = await clearTaskHistoryMutation.mutateAsync();
      logger.debug('Clear result:', result);

      // Force a refetch to update the UI immediately
      await queryClient.invalidateQueries({ queryKey: ['/api/tasks/all'] });
      await queryClient.invalidateQueries({ queryKey: ['/api/tasks/recent'] });
      refetchTasks();

      return result.clearedCount > 0;
    } catch (error) {
      logger.error('Error clearing task history:', error);
      setError(error as Error);
      toast({
        title: 'Error',
        description: 'Failed to clear task history: ' + (error as Error).message,
        variant: 'destructive',
      });
      return false;
    }
  };

  // Retry failed tasks
  const retryFailedTasksMutation = useMutation({
    mutationFn: () => apiRequest('/api/tasks/retry-failed', { method: 'POST' }),
    onSuccess: (data) => {
      logger.debug('Retry failed tasks response:', data);
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/all'] });
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/recent'] });

      if (data.retriedCount > 0) {
        toast({
          title: 'Failed Tasks Retried',
          description: `${data.retriedCount} failed tasks have been requeued.`,
        });
      } else {
        toast({
          title: 'No Tasks Retried',
          description: 'No failed tasks were able to be retried.',
          variant: 'default',
        });
      }
    },
  });

  const retryFailedTasks = async (): Promise<boolean> => {
    try {
      // Get the current failed tasks count for verification
      const failedTasks = tasks.filter(task => task.status === TaskStatus.FAILED);
      logger.debug(`Attempting to retry ${failedTasks.length} failed tasks`);

      if (failedTasks.length === 0) {
        toast({
          title: 'No Failed Tasks',
          description: 'There are no failed tasks to retry.',
          variant: 'default',
        });
        return false;
      }

      const result = await retryFailedTasksMutation.mutateAsync();
      logger.debug('Retry result:', result);

      // Force a refetch to update the UI immediately
      await queryClient.invalidateQueries({ queryKey: ['/api/tasks/all'] });
      await queryClient.invalidateQueries({ queryKey: ['/api/tasks/recent'] });
      refetchTasks();

      return result.retriedCount > 0;
    } catch (error) {
      logger.error('Error retrying failed tasks:', error);
      setError(error as Error);
      toast({
        title: 'Error',
        description: 'Failed to retry failed tasks: ' + (error as Error).message,
        variant: 'destructive',
      });
      return false;
    }
  };

  // Fetch transcription
  const fetchTranscriptionMutation = useMutation({
    mutationFn: (videoId: string) =>
      apiRequest('/api/tasks/fetch-transcription', {
        method: 'POST',
        body: JSON.stringify({ videoId }),
        headers: { 'Content-Type': 'application/json' }
      }),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/tasks'] });
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/recent'] });
      toast({
        title: 'Transcription Requested',
        description: 'The transcription is being fetched in the background.',
      });
    },
  });

  const fetchTranscription = async (videoId: string): Promise<Task> => {
    return fetchTranscriptionMutation.mutateAsync(videoId);
  };

  // Analyze financial benefits
  const analyzeFinancialMutation = useMutation({
    mutationFn: (videoId: string) =>
      apiRequest('/api/tasks/analyze-financial', {
        method: 'POST',
        body: JSON.stringify({ videoId }),
        headers: { 'Content-Type': 'application/json' }
      }),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/tasks'] });
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/recent'] });
      toast({
        title: 'Financial Analysis Requested',
        description: 'The financial analysis is being performed in the background.',
      });
    },
  });

  const analyzeFinancial = async (videoId: string): Promise<Task> => {
    return analyzeFinancialMutation.mutateAsync(videoId);
  };

  // Refresh channel videos
  const refreshChannelMutation = useMutation({
    mutationFn: (channelId: string) =>
      apiRequest('/api/tasks/refresh-channel', {
        method: 'POST',
        body: JSON.stringify({ channelId }),
        headers: { 'Content-Type': 'application/json' }
      }),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/tasks'] });
      queryClient.invalidateQueries({ queryKey: ['/api/tasks/recent'] });
      toast({
        title: 'Channel Refresh Requested',
        description: 'The channel videos are being refreshed in the background.',
      });
    },
  });

  const refreshChannel = async (channelId: string): Promise<Task> => {
    return refreshChannelMutation.mutateAsync(channelId);
  };

  // Monitor tasks for status changes and show notifications
  useEffect(() => {
    const completedTaskIds = new Set<string>();
    const failedTaskIds = new Set<string>();

    // Check for completed or failed tasks
    tasks.forEach(task => {
      if (task.status === TaskStatus.COMPLETED && !completedTaskIds.has(task.id)) {
        completedTaskIds.add(task.id);
        toast({
          title: 'Task Completed',
          description: task.name,
          variant: 'default',
        });
      } else if (task.status === TaskStatus.FAILED && !failedTaskIds.has(task.id)) {
        failedTaskIds.add(task.id);
        toast({
          title: 'Task Failed',
          description: `${task.name}: ${task.error || 'Unknown error'}`,
          variant: 'destructive',
        });
      }
    });

    // Clean up
    return () => {
      completedTaskIds.clear();
      failedTaskIds.clear();
    };
  }, [tasks, toast]);

  return (
    <BackgroundTasksContext.Provider
      value={{
        tasks,
        recentTasks,
        isLoading,
        error,
        activeTasksCount,
        fetchTask,
        cancelTask,
        cancelAllTasks,
        clearTaskHistory,
        retryFailedTasks,
        fetchTranscription,
        analyzeFinancial,
        refreshChannel,
        refetchTasks,
      }}
    >
      {children}
    </BackgroundTasksContext.Provider>
  );
}

// Hook to use background tasks context
export function useBackgroundTasks() {
  const context = useContext(BackgroundTasksContext);
  if (!context) {
    throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');
  }
  return context;
}
