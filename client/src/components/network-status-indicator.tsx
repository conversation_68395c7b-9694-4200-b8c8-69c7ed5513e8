// network-status-indicator.tsx
// Component to display network status in the UI

import React, { useEffect, useRef } from 'react';
import { useNetworkStatus } from '@/hooks/use-network-status';
import { NetworkStatus } from '@/lib/network-manager';
import { Wifi, WifiOff, Alert<PERSON>riangle, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';

interface NetworkStatusIndicatorProps {
  className?: string;
  showLabel?: boolean;
  showTooltip?: boolean;
  showResetButton?: boolean;
  hideWhenOnline?: boolean;
}

export function NetworkStatusIndicator({
  className,
  showLabel = false,
  showTooltip = true,
  showResetButton = false,
  hideWhenOnline = false
}: NetworkStatusIndicatorProps) {
  const { networkStatus, resetNetworkManager } = useNetworkStatus();
  const { toast } = useToast();
  const prevStatusRef = useRef<NetworkStatus>(networkStatus);

  // Show toasts when network status changes
  useEffect(() => {
    // Only show toasts when status changes, not on initial render
    if (prevStatusRef.current !== networkStatus && prevStatusRef.current !== undefined) {
      if (networkStatus === NetworkStatus.OFFLINE) {
        toast({
          title: 'Server Connection Lost',
          description: 'Connection to the application server has been lost. Some features may be unavailable.',
          variant: 'destructive',
        });
      } else if (networkStatus === NetworkStatus.ONLINE && prevStatusRef.current === NetworkStatus.OFFLINE) {
        toast({
          title: 'Server Connection Restored',
          description: 'Connection to the application server has been restored.',
          duration: 3000,
        });
      } else if (networkStatus === NetworkStatus.UNSTABLE) {
        toast({
          title: 'Unstable Server Connection',
          description: 'Connection to the application server is unstable. Some features may be slow or unavailable.',
          variant: 'warning',
        });
      }
    }

    // Update previous status
    prevStatusRef.current = networkStatus;
  }, [networkStatus, toast]);

  // Define status-specific properties
  const statusConfig = {
    [NetworkStatus.ONLINE]: {
      icon: <Wifi className="h-3 w-3 text-green-500/70" />,
      label: 'Connected',
      tooltip: 'Connected to server',
      badgeVariant: 'success' as const
    },
    [NetworkStatus.OFFLINE]: {
      icon: <WifiOff className="h-3 w-3 text-destructive" />,
      label: 'Disconnected',
      tooltip: 'Not connected to server',
      badgeVariant: 'destructive' as const
    },
    [NetworkStatus.UNSTABLE]: {
      icon: <AlertTriangle className="h-3 w-3 text-amber-500" />,
      label: 'Unstable',
      tooltip: 'Server connection is unstable',
      badgeVariant: 'warning' as const
    },
    [NetworkStatus.RECONNECTING]: {
      icon: <RefreshCw className="h-3 w-3 text-blue-500 animate-spin" />,
      label: 'Reconnecting',
      tooltip: 'Reconnecting to server',
      badgeVariant: 'outline' as const
    }
  };

  const config = statusConfig[networkStatus];

  // Handle reset button click
  const handleReset = () => {
    resetNetworkManager();
  };

  // Render the indicator
  const indicator = (
    <div className={cn("flex items-center gap-1 p-1 rounded-full bg-background/50 backdrop-blur-sm", className)}>
      {config.icon}

      {showLabel && (
        <Badge variant={config.badgeVariant} className="text-xs text-[10px] px-1.5 py-0">
          {config.label}
        </Badge>
      )}

      {showResetButton && (
        <Button
          variant="ghost"
          size="icon"
          className="h-5 w-5"
          onClick={handleReset}
          title="Reset network status"
        >
          <RefreshCw className="h-2.5 w-2.5" />
        </Button>
      )}
    </div>
  );

  // Hide when online if hideWhenOnline is true
  if (hideWhenOnline && networkStatus === NetworkStatus.ONLINE) {
    return null;
  }

  // Wrap in tooltip if needed
  if (showTooltip) {
    return (
      <TooltipProvider delayDuration={700}>
        <Tooltip>
          <TooltipTrigger asChild>
            {indicator}
          </TooltipTrigger>
          <TooltipContent side="left" className="text-xs py-1 px-2">
            <p>{config.tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return indicator;
}
