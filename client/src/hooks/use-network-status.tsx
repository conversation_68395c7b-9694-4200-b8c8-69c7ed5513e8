// use-network-status.tsx
// React hook for using the network manager

import { useState, useEffect, useCallback } from 'react';
import { networkManager, NetworkStatus } from '@/lib/network-manager';

export function useNetworkStatus() {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>(
    networkManager.getStatus()
  );
  
  // Update state when network status changes
  useEffect(() => {
    const handleStatusChange = (status: NetworkStatus) => {
      setNetworkStatus(status);
    };
    
    // Add listener
    networkManager.addListener(handleStatusChange);
    
    // Remove listener on cleanup
    return () => {
      networkManager.removeListener(handleStatusChange);
    };
  }, []);
  
  // Check if network is available for requests
  const isNetworkAvailable = useCallback(() => {
    return networkManager.isNetworkAvailable();
  }, []);
  
  // Get the current retry delay
  const getRetryDelay = useCallback(() => {
    return networkManager.getRetryDelay();
  }, []);
  
  // Reset the network manager
  const resetNetworkManager = useCallback(() => {
    networkManager.reset();
  }, []);
  
  return {
    networkStatus,
    isNetworkAvailable,
    getRetryDelay,
    resetNetworkManager,
    isOnline: networkStatus === NetworkStatus.ONLINE,
    isOffline: networkStatus === NetworkStatus.OFFLINE,
    isUnstable: networkStatus === NetworkStatus.UNSTABLE,
    isReconnecting: networkStatus === NetworkStatus.RECONNECTING
  };
}
