import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

// Hook to test Ollama connection
export const useTestOllamaConnection = () => {
  return useQuery({
    queryKey: ['ollama-connection-test'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/ollama/test-connection');
      return await response.json();
    },
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });
};

// Hook to get available Ollama models
export const useOllamaModels = () => {
  return useQuery({
    queryKey: ['ollama-models'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/ollama/models');
      return await response.json();
    },
    retry: 1,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
  });
};

// Hook to analyze a video with Ollama
export const useAnalyzeVideoWithOllama = () => {
  return useMutation({
    mutationFn: async (videoId: string) => {
      const response = await apiRequest('GET', `/api/ollama/analyze-youtube-video/${videoId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Ollama error: Failed to analyze video');
      }
      return await response.json();
    },
    onError: (error) => {
      console.error('Error analyzing video with Ollama:', error);
      const { toast } = useToast();
      toast({
        title: 'Ollama Analysis Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive',
      });
    }
  });
};

// Hook to analyze all videos in a channel with Ollama
export const useAnalyzeChannelWithOllama = () => {
  return useMutation({
    mutationFn: async (channelId: number) => {
      const response = await apiRequest('POST', `/api/ollama/analyze-channel/${channelId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Ollama error: Failed to analyze channel');
      }
      return await response.json();
    },
    onError: (error) => {
      console.error('Error analyzing channel with Ollama:', error);
      const { toast } = useToast();
      toast({
        title: 'Ollama Channel Analysis Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive',
      });
    }
  });
};
