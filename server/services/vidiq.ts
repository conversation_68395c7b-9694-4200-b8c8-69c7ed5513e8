import axios, { AxiosError, AxiosResponse } from 'axios';
import dotenv from 'dotenv';
import { keyManager } from './vidiq-key-manager';
import { storage } from '../storage';

// Load environment variables
dotenv.config({ path: '.env.vidiq' });

// API response types
export interface TrendyVideo {
  id: string;
  title: string;
  channelId: string;
  channelTitle: string;
  publishedAt: string;
  thumbnails: {
    default?: { url: string; width: number; height: number };
    medium?: { url: string; width: number; height: number };
    high?: { url: string; width: number; height: number };
    standard?: { url: string; width: number; height: number };
    maxres?: { url: string; width: number; height: number };
  };
  viewCount: number;
  likeCount: number;
  commentCount: number;
  description: string;
  tags: string[];
  score: number;
}

export interface TrendyVideoResponse {
  videos: TrendyVideo[];
  totalCount: number;
  nextPageToken?: string;
}

// Error handling
export class VidIQApiError extends Error {
  status?: number;
  data?: any;

  constructor(message: string, status?: number, data?: any) {
    super(message);
    this.name = 'VidIQApiError';
    this.status = status;
    this.data = data;
  }
}

// API parameters interface
export interface TrendyVideosParams {
  keywords: string[];
  country?: string;
  language?: string;
  contentDetails?: boolean;
  pageToken?: string;
  limit?: number;
}

export class VidIQService {
  private apiUrl: string = 'https://api.vidiq.com/v0';

  constructor() {
    // Initialize the key manager
    keyManager.getNextKey().catch(err => {
      console.error('Failed to initialize key manager:', err);
    });
  }

  /**
   * Get common headers for VidIQ API requests
   */
  private async getHeaders(): Promise<Record<string, string>> {
    // Get headers from key manager
    const headers = await keyManager.getRequestHeaders();

    // If no headers are available, try to use environment variables as fallback
    if (!headers) {
      const bearerToken = process.env.VIDIQ_TOKEN;
      const authToken = process.env.VIDIQ_AUTH;
      const cookie = process.env.VIDIQ_COOKIE;

      if (!bearerToken) {
        throw new Error('No VidIQ API keys available and no fallback found in environment variables');
      }

      return {
        'Accept': 'application/json, text/plain, */*',
        'Authorization': `Bearer ${bearerToken}`,
        'Referer': 'https://app.vidiq.com/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        'X-TimeZone': 'Asia/Calcutta',
        'X-Vidiq-Client': 'web 1a7277ec05e25b41808d7ed49f9a3f3c6ff86254',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'x-vidiq-auth': authToken || bearerToken,
        'Cookie': cookie || ''
      };
    }

    return headers;
  }

  /**
   * Fetch trendy videos based on search keywords
   * @param params Search parameters
   * @returns Promise with trendy videos
   */
  public async getTrendyVideos(params: TrendyVideosParams): Promise<TrendyVideoResponse> {
    // Maximum number of retries when API keys are exhausted
    const MAX_RETRIES = 3;
    let retries = 0;

    while (retries < MAX_RETRIES) {
      try {
        // Build query parameters
        const queryParams = new URLSearchParams();

        // Add keywords as q[] parameters
        params.keywords.forEach(keyword => {
          queryParams.append('q[]', keyword);
        });

        // Add optional parameters
        if (params.country) queryParams.append('c', params.country);
        if (params.language) queryParams.append('l', params.language);
        if (params.contentDetails !== undefined) queryParams.append('content_details', params.contentDetails.toString());
        if (params.pageToken) queryParams.append('page_token', params.pageToken);
        if (params.limit) queryParams.append('limit', params.limit.toString());

        const url = `${this.apiUrl}/trendy?${queryParams.toString()}`;

        // Get headers for the request
        const headers = await this.getHeaders();

        // Log which API key is being used
        const currentKey = keyManager.getCurrentKey();
        if (currentKey) {
          console.log(`Using VidIQ API Key #${currentKey.id} (${currentKey.name || 'Unnamed'}) for request`);
        }

        const response: AxiosResponse<TrendyVideoResponse> = await axios.get(url, {
          headers
        });

        return response.data;
      } catch (error) {
        if (axios.isAxiosError(error)) {
          const axiosError = error as AxiosError;

          // Check if it's a rate limit error (429 Too Many Requests)
          if (axiosError.response?.status === 429) {
            console.log('Rate limit exceeded, trying next API key...');

            // Try to rotate to the next API key
            const rotated = await keyManager.handleApiError(axiosError);

            if (rotated) {
              retries++;
              console.log(`Retrying request with new API key (attempt ${retries}/${MAX_RETRIES})`);
              continue; // Try again with the new key
            }
          }

          throw new VidIQApiError(
            axiosError.message || 'Failed to fetch trendy videos',
            axiosError.response?.status,
            axiosError.response?.data
          );
        }

        throw new VidIQApiError('Unknown error occurred while fetching trendy videos');
      }
    }

    throw new VidIQApiError('All API keys exhausted, unable to complete request');
  }

  /**
   * Search trendy videos with a single keyword
   * @param keyword Search keyword
   * @param country Country code (default: US)
   * @param language Language code (default: en)
   * @returns Promise with trendy videos
   */
  public async searchTrendyVideos(
    keyword: string,
    country: string = 'US',
    language: string = 'en'
  ): Promise<TrendyVideoResponse> {
    return this.getTrendyVideos({
      keywords: [keyword],
      country,
      language,
      contentDetails: true // Enable content details to get full metadata
    });
  }

  /**
   * Verify if the API connection is working
   * @returns Promise<boolean>
   */
  public async testConnection(): Promise<boolean> {
    try {
      await this.getTrendyVideos({
        keywords: ['test'],
        limit: 1
      });
      return true;
    } catch (error) {
      console.error('VidIQ API connection test failed:', error);
      return false;
    }
  }

  /**
   * Test a specific API key
   * @param apiKeyId The ID of the API key to test
   * @returns Promise<{success: boolean, message: string}>
   */
  public async testApiKey(apiKeyId: number): Promise<{success: boolean, message: string}> {
    try {
      const apiKey = await storage.getApiKey(apiKeyId);
      if (!apiKey) {
        return { success: false, message: 'API key not found' };
      }

      // Create headers for this specific key
      const headers = {
        'Accept': 'application/json, text/plain, */*',
        'Authorization': `Bearer ${apiKey.token}`,
        'Referer': 'https://app.vidiq.com/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        'X-TimeZone': 'Asia/Calcutta',
        'X-Vidiq-Client': 'web 1a7277ec05e25b41808d7ed49f9a3f3c6ff86254',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'x-vidiq-auth': apiKey.auth_token || apiKey.token,
        'Cookie': apiKey.cookie || ''
      };

      // Make a simple request to test the key
      const url = `${this.apiUrl}/trendy?q[]=test&c=US&l=en&limit=1`;
      await axios.get(url, { headers });

      // Update the last_used timestamp
      await storage.updateApiKey(apiKeyId, {
        last_used: new Date()
      });

      return { success: true, message: 'API key is working correctly' };
    } catch (error) {
      console.error(`Test failed for API key ${apiKeyId}:`, error);

      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;

        // If it's a rate limit error, mark the key as exhausted
        if (axiosError.response?.status === 429) {
          await storage.markApiKeyExhausted(apiKeyId);
          return {
            success: false,
            message: `API key is rate limited (429 Too Many Requests). Marked as exhausted for 60 minutes.`
          };
        }

        return {
          success: false,
          message: `API key test failed: ${axiosError.response?.status} ${axiosError.response?.statusText || ''}`
        };
      }

      return { success: false, message: `Unknown error testing API key: ${error.message}` };
    }
  }
}

// Export a singleton instance of the service
export const vidiqService = new VidIQService();

// Default export
export default vidiqService;

