import { VideoCard } from "./video-card";
import { Video } from "@shared/schema";
import { Skeleton } from "@/components/ui/skeleton";
import { memo } from "react";
import "./video-grid.css";

interface VideoGridProps {
  videos: Video[];
  isLoading?: boolean;
  onPlayVideo: (video: Video) => void;
  gridSize?: number; // Number of columns in the grid (1-6)
  pauseInfiniteScroll?: () => void; // Function to pause infinite scroll
}

function VideoGridComponent({ videos, isLoading, onPlayVideo, gridSize = 4, pauseInfiniteScroll }: VideoGridProps) {
  // Get the appropriate grid class based on the gridSize prop
  const getGridClass = () => {
    // Use a fixed set of classes instead of template literals for better compatibility
    switch (gridSize) {
      case 1: return "grid gap-4 px-0 grid-cols-1";
      case 2: return "grid gap-4 px-0 grid-cols-2";
      case 3: return "grid gap-4 px-0 grid-cols-3";
      case 5: return "grid gap-4 px-0 grid-cols-5";
      case 6: return "grid gap-4 px-0 grid-cols-6";
      case 4:
      default: return "grid gap-4 px-0 grid-cols-4";
    }
  };
  if (isLoading) {
    return (
      <div className={getGridClass()}>
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={i} className="space-y-3">
            <Skeleton className="h-[180px] w-full" />
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-4 w-[200px]" />
          </div>
        ))}
      </div>
    );
  }

  if (!videos.length) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No videos found.</p>
      </div>
    );
  }

  return (
    <div className={getGridClass()}>
      {videos.map((video) => (
        <VideoCard
          key={video.id}
          video={video}
          onPlay={onPlayVideo}
          pauseInfiniteScroll={pauseInfiniteScroll}
        />
      ))}
    </div>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export const VideoGrid = memo(VideoGridComponent);