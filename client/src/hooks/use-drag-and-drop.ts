import { useState, useRef, useCallback, useEffect } from 'react';

interface DraggableItem {
  id: number | string;
  displayOrder: number;
}

interface UseDragAndDropOptions<T extends DraggableItem> {
  items: T[];
  onReorder: (items: T[]) => void;
}

interface DragHandleProps {
  onMouseDown: (e: React.MouseEvent) => void;
  onTouchStart: (e: React.TouchEvent) => void;
}

export function useDragAndDrop<T extends DraggableItem>({
  items,
  onReorder
}: UseDragAndDropOptions<T>) {
  const [draggedItemId, setDraggedItemId] = useState<number | string | null>(null);
  const [orderedItems, setOrderedItems] = useState<T[]>(items);
  const dragRef = useRef<HTMLElement | null>(null);
  const dragStartY = useRef<number>(0);
  const dragCurrentIndex = useRef<number>(-1);

  // Update ordered items when items change
  useEffect(() => {
    setOrderedItems(items);
  }, [items]);

  const handleDragStart = useCallback((e: React.MouseEvent | React.TouchEvent, id: number | string, index: number) => {
    e.preventDefault();

    // Store the target element
    if (e.currentTarget instanceof HTMLElement) {
      dragRef.current = e.currentTarget.closest('.draggable-item') as HTMLElement;

      if (dragRef.current) {
        // Add a class to indicate dragging
        dragRef.current.classList.add('dragging');

        // Set initial styles
        dragRef.current.style.zIndex = '100';
        dragRef.current.style.position = 'relative';
        dragRef.current.style.transition = 'transform 0.2s, box-shadow 0.2s';

        // Add a subtle animation to make it more interactive
        dragRef.current.animate(
          [
            { transform: 'scale(1)' },
            { transform: 'scale(1.02)' },
            { transform: 'scale(1)' }
          ],
          {
            duration: 200,
            iterations: 1
          }
        );
      }
    }

    setDraggedItemId(id);
    dragCurrentIndex.current = index;

    // Get the starting position
    if ('touches' in e) {
      dragStartY.current = e.touches[0].clientY;
    } else {
      dragStartY.current = e.clientY;
    }

    // Add event listeners for drag movement and end
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('touchmove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
    document.addEventListener('touchend', handleDragEnd);
  }, []);

  const handleDragMove = useCallback((e: MouseEvent | TouchEvent) => {
    if (draggedItemId === null || !dragRef.current) return;

    // Get current position
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
    const deltaY = clientY - dragStartY.current;

    // Apply visual transform during drag with a slight rotation for better visual feedback
    const rotationAmount = Math.min(Math.abs(deltaY) * 0.05, 2) * Math.sign(deltaY);
    dragRef.current.style.transform = `translateY(${deltaY}px) rotate(${rotationAmount}deg)`;

    // Determine if we need to swap items
    if (Math.abs(deltaY) > 30) { // Threshold for movement
      const direction = deltaY > 0 ? 1 : -1;
      const newIndex = dragCurrentIndex.current + direction;

      // Check if the new index is valid
      if (newIndex >= 0 && newIndex < orderedItems.length) {
        // Reset transform before swapping with a smooth animation
        dragRef.current.style.transition = 'transform 0.2s';
        dragRef.current.style.transform = 'translateY(0) rotate(0deg)';

        // After the animation completes, swap the items
        setTimeout(() => {
          // Swap items
          const newItems = [...orderedItems];
          const temp = newItems[dragCurrentIndex.current];
          newItems[dragCurrentIndex.current] = newItems[newIndex];
          newItems[newIndex] = temp;

          // Update display order
          newItems.forEach((item, index) => {
            item.displayOrder = index;
          });

          setOrderedItems(newItems);
          dragCurrentIndex.current = newIndex;
          dragStartY.current = clientY;

          // Reset transition to allow immediate movement again
          if (dragRef.current) {
            dragRef.current.style.transition = 'none';
            // Force a reflow to apply the transition change immediately
            void dragRef.current.offsetHeight;
          }
        }, 50);
      }
    }
  }, [draggedItemId, orderedItems]);

  const handleDragEnd = useCallback(() => {
    if (draggedItemId !== null) {
      // Remove event listeners
      document.removeEventListener('mousemove', handleDragMove);
      document.removeEventListener('touchmove', handleDragMove);
      document.removeEventListener('mouseup', handleDragEnd);
      document.removeEventListener('touchend', handleDragEnd);

      // Clean up styles with a nice animation
      if (dragRef.current) {
        // Add a drop animation
        dragRef.current.style.transition = 'all 0.3s';
        dragRef.current.style.transform = 'translateY(0) rotate(0deg) scale(1)';

        // Play a subtle bounce animation
        dragRef.current.animate(
          [
            { transform: 'scale(1.03)', offset: 0 },
            { transform: 'scale(0.97)', offset: 0.3 },
            { transform: 'scale(1)', offset: 1 }
          ],
          {
            duration: 300,
            easing: 'ease-out'
          }
        );

        // Remove the dragging class after the animation
        setTimeout(() => {
          if (dragRef.current) {
            dragRef.current.classList.remove('dragging');
            dragRef.current.style.zIndex = '';
            dragRef.current.style.position = '';
            dragRef.current.style.transition = '';
            dragRef.current.style.transform = '';
          }
        }, 300);
      }

      // Notify parent component of the new order
      onReorder(orderedItems);

      // Reset drag state
      setDraggedItemId(null);
      // Don't reset dragRef.current immediately to allow the animation to complete
      setTimeout(() => {
        dragRef.current = null;
      }, 300);
    }
  }, [draggedItemId, orderedItems, onReorder, handleDragMove]);

  const getDragHandleProps = useCallback((id: number | string, index: number): DragHandleProps => {
    return {
      onMouseDown: (e) => handleDragStart(e, id, index),
      onTouchStart: (e) => handleDragStart(e, id, index)
    };
  }, [handleDragStart]);

  return {
    orderedItems,
    draggedItemId,
    getDragHandleProps
  };
}
