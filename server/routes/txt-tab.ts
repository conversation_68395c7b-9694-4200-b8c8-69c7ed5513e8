import express from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { chatGPTClient } from '../services/chatgpt-client';

const router = express.Router();

// Middleware to check authentication
const requireAuth = (req: any, res: any, next: any) => {
  if (!req.isAuthenticated()) {
    return res.sendStatus(401);
  }
  next();
};

// Validation schemas
const createTranscriptSchema = z.object({
  title: z.string().min(1).max(255),
  content: z.string().min(1),
  videoUrl: z.string().url().optional(),
  sourceType: z.enum(['manual', 'rss', 'ytr', 'realtime', 'feed', 'imported']).optional(),
  sourceId: z.string().optional()
});

const updateTranscriptSchema = z.object({
  title: z.string().min(1).max(255).optional(),
  content: z.string().min(1).optional(),
  videoUrl: z.string().url().optional()
});

const createHighlightSchema = z.object({
  transcriptId: z.number().int().positive(),
  startPos: z.number().int().min(0),
  endPos: z.number().int().min(0),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/),
  note: z.string().optional()
});

const createPromptSchema = z.object({
  name: z.string().min(1).max(255),
  content: z.string().min(1),
  category: z.string().max(100).optional()
});

const updatePromptSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  content: z.string().min(1).optional(),
  category: z.string().max(100).optional()
});

// Transcript routes
router.get('/transcripts', requireAuth, async (req, res) => {
  try {
    const transcripts = await storage.getTxtTranscripts(req.user.id);
    res.json(transcripts);
  } catch (error) {
    console.error('Error fetching transcripts:', error);
    res.status(500).json({ message: 'Failed to fetch transcripts' });
  }
});

router.get('/transcripts/:id', requireAuth, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid transcript ID' });
    }

    const transcript = await storage.getTxtTranscript(id, req.user.id);
    if (!transcript) {
      return res.status(404).json({ message: 'Transcript not found' });
    }

    // Also get highlights for this transcript
    const highlights = await storage.getTxtHighlights(id, req.user.id);
    transcript.highlights = highlights;

    res.json(transcript);
  } catch (error) {
    console.error('Error fetching transcript:', error);
    res.status(500).json({ message: 'Failed to fetch transcript' });
  }
});

router.post('/transcripts', requireAuth, async (req, res) => {
  try {
    const result = createTranscriptSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const transcriptId = await storage.createTxtTranscript(req.user.id, result.data);
    const transcript = await storage.getTxtTranscript(transcriptId, req.user.id);
    
    res.status(201).json(transcript);
  } catch (error) {
    console.error('Error creating transcript:', error);
    res.status(500).json({ message: 'Failed to create transcript' });
  }
});

router.put('/transcripts/:id', requireAuth, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid transcript ID' });
    }

    const result = updateTranscriptSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const success = await storage.updateTxtTranscript(id, req.user.id, result.data);
    if (!success) {
      return res.status(404).json({ message: 'Transcript not found' });
    }

    const transcript = await storage.getTxtTranscript(id, req.user.id);
    res.json(transcript);
  } catch (error) {
    console.error('Error updating transcript:', error);
    res.status(500).json({ message: 'Failed to update transcript' });
  }
});

router.delete('/transcripts/:id', requireAuth, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid transcript ID' });
    }

    const success = await storage.deleteTxtTranscript(id, req.user.id);
    if (!success) {
      return res.status(404).json({ message: 'Transcript not found' });
    }

    res.json({ message: 'Transcript deleted successfully' });
  } catch (error) {
    console.error('Error deleting transcript:', error);
    res.status(500).json({ message: 'Failed to delete transcript' });
  }
});

// Highlight routes
router.get('/transcripts/:id/highlights', requireAuth, async (req, res) => {
  try {
    const transcriptId = parseInt(req.params.id);
    if (isNaN(transcriptId)) {
      return res.status(400).json({ message: 'Invalid transcript ID' });
    }

    const highlights = await storage.getTxtHighlights(transcriptId, req.user.id);
    res.json(highlights);
  } catch (error) {
    console.error('Error fetching highlights:', error);
    res.status(500).json({ message: 'Failed to fetch highlights' });
  }
});

router.post('/highlights', requireAuth, async (req, res) => {
  try {
    const result = createHighlightSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    // Validate that endPos > startPos
    if (result.data.endPos <= result.data.startPos) {
      return res.status(400).json({ message: 'End position must be greater than start position' });
    }

    // Verify transcript exists and belongs to user
    const transcript = await storage.getTxtTranscript(result.data.transcriptId, req.user.id);
    if (!transcript) {
      return res.status(404).json({ message: 'Transcript not found' });
    }

    const highlightId = await storage.createTxtHighlight(req.user.id, result.data);
    const highlights = await storage.getTxtHighlights(result.data.transcriptId, req.user.id);
    const newHighlight = highlights.find(h => h.id === highlightId);
    
    res.status(201).json(newHighlight);
  } catch (error) {
    console.error('Error creating highlight:', error);
    res.status(500).json({ message: 'Failed to create highlight' });
  }
});

router.delete('/highlights/:id', requireAuth, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid highlight ID' });
    }

    const success = await storage.deleteTxtHighlight(id, req.user.id);
    if (!success) {
      return res.status(404).json({ message: 'Highlight not found' });
    }

    res.json({ message: 'Highlight deleted successfully' });
  } catch (error) {
    console.error('Error deleting highlight:', error);
    res.status(500).json({ message: 'Failed to delete highlight' });
  }
});

// Prompt routes
router.get('/prompts', requireAuth, async (req, res) => {
  try {
    const prompts = await storage.getTxtPrompts(req.user.id);
    res.json(prompts);
  } catch (error) {
    console.error('Error fetching prompts:', error);
    res.status(500).json({ message: 'Failed to fetch prompts' });
  }
});

router.post('/prompts', requireAuth, async (req, res) => {
  try {
    const result = createPromptSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const promptId = await storage.createTxtPrompt(req.user.id, result.data);
    const prompts = await storage.getTxtPrompts(req.user.id);
    const newPrompt = prompts.find(p => p.id === promptId);
    
    res.status(201).json(newPrompt);
  } catch (error) {
    console.error('Error creating prompt:', error);
    res.status(500).json({ message: 'Failed to create prompt' });
  }
});

router.put('/prompts/:id', requireAuth, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid prompt ID' });
    }

    const result = updatePromptSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const success = await storage.updateTxtPrompt(id, req.user.id, result.data);
    if (!success) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    const prompts = await storage.getTxtPrompts(req.user.id);
    const updatedPrompt = prompts.find(p => p.id === id);
    res.json(updatedPrompt);
  } catch (error) {
    console.error('Error updating prompt:', error);
    res.status(500).json({ message: 'Failed to update prompt' });
  }
});

router.delete('/prompts/:id', requireAuth, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid prompt ID' });
    }

    const success = await storage.deleteTxtPrompt(id, req.user.id);
    if (!success) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    res.json({ message: 'Prompt deleted successfully' });
  } catch (error) {
    console.error('Error deleting prompt:', error);
    res.status(500).json({ message: 'Failed to delete prompt' });
  }
});

// Get all existing transcripts from various sources
router.get('/existing-transcripts', requireAuth, async (req, res) => {
  try {
    const transcripts = await storage.getAllExistingTranscripts(req.user.id);
    res.json(transcripts);
  } catch (error) {
    console.error('Error fetching existing transcripts:', error);
    res.status(500).json({ message: 'Failed to fetch existing transcripts' });
  }
});

// ChatGPT integration routes
const chatGptProcessSchema = z.object({
  transcriptId: z.number().int().positive(),
  promptId: z.number().int().positive().optional(),
  customPrompt: z.string().optional()
});

router.post('/chatgpt/process', requireAuth, async (req, res) => {
  try {
    const result = chatGptProcessSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { transcriptId, promptId, customPrompt } = result.data;

    // Verify transcript exists and belongs to user
    const transcript = await storage.getTxtTranscript(transcriptId, req.user.id);
    if (!transcript) {
      return res.status(404).json({ message: 'Transcript not found' });
    }

    let promptContent = customPrompt;

    // If promptId is provided, get the prompt content
    if (promptId && !customPrompt) {
      const prompts = await storage.getTxtPrompts(req.user.id);
      const prompt = prompts.find(p => p.id === promptId);
      if (!prompt) {
        return res.status(404).json({ message: 'Prompt not found' });
      }
      promptContent = prompt.content;
    }

    if (!promptContent) {
      return res.status(400).json({ message: 'Either promptId or customPrompt must be provided' });
    }

    // Check if ChatGPT server is available
    const isConnected = await chatGPTClient.checkConnection();
    if (!isConnected) {
      return res.status(503).json({
        success: false,
        message: 'ChatGPT server is not available. Please start the ChatGPT server first.',
        error: 'Server not connected'
      });
    }

    // Process the transcript with ChatGPT
    const chatResult = await chatGPTClient.processTranscript(promptContent, transcript.content);

    if (chatResult.success) {
      res.json({
        success: true,
        processedContent: chatResult.response,
        transcriptTitle: transcript.title,
        promptUsed: promptContent.substring(0, 100) + (promptContent.length > 100 ? '...' : '')
      });
    } else {
      res.status(500).json({
        success: false,
        message: chatResult.error || 'Failed to process with ChatGPT',
        error: chatResult.error
      });
    }
  } catch (error) {
    console.error('Error processing with ChatGPT:', error);
    res.status(500).json({ message: 'Failed to process with ChatGPT' });
  }
});

// Direct chat with ChatGPT (without transcript)
const directChatSchema = z.object({
  prompt: z.string().min(1),
  transcript: z.string().optional()
});

router.post('/chatgpt/direct-chat', requireAuth, async (req, res) => {
  try {
    const result = directChatSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { prompt, transcript } = result.data;

    // Check if ChatGPT server is available
    const isConnected = await chatGPTClient.checkConnection();
    if (!isConnected) {
      return res.status(503).json({
        success: false,
        message: 'ChatGPT server is not available. Please start the ChatGPT server first.',
        error: 'Server not connected'
      });
    }

    // Send message to ChatGPT
    const chatResult = transcript
      ? await chatGPTClient.processTranscript(prompt, transcript)
      : await chatGPTClient.sendMessage(prompt);

    if (chatResult.success) {
      res.json({
        success: true,
        response: chatResult.response,
        prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : '')
      });
    } else {
      res.status(500).json({
        success: false,
        message: chatResult.error || 'Failed to chat with ChatGPT',
        error: chatResult.error
      });
    }
  } catch (error) {
    console.error('Error in direct chat with ChatGPT:', error);
    res.status(500).json({ message: 'Failed to chat with ChatGPT' });
  }
});

// ChatGPT server status and management
router.get('/chatgpt/status', requireAuth, async (req, res) => {
  try {
    const status = await chatGPTClient.getStatus();
    res.json(status);
  } catch (error) {
    console.error('Error getting ChatGPT status:', error);
    res.status(500).json({ message: 'Failed to get ChatGPT status' });
  }
});

router.post('/chatgpt/login', requireAuth, async (req, res) => {
  try {
    const result = await chatGPTClient.initializeLogin();
    res.json(result);
  } catch (error) {
    console.error('Error initializing ChatGPT login:', error);
    res.status(500).json({ message: 'Failed to initialize ChatGPT login' });
  }
});

router.post('/chatgpt/verify-login', requireAuth, async (req, res) => {
  try {
    const result = await chatGPTClient.verifyLogin();
    res.json(result);
  } catch (error) {
    console.error('Error verifying ChatGPT login:', error);
    res.status(500).json({ message: 'Failed to verify ChatGPT login' });
  }
});

router.post('/chatgpt/new-chat', requireAuth, async (req, res) => {
  try {
    const result = await chatGPTClient.startNewChat();
    res.json(result);
  } catch (error) {
    console.error('Error starting new ChatGPT chat:', error);
    res.status(500).json({ message: 'Failed to start new ChatGPT chat' });
  }
});

// Import transcript from existing source
const importTranscriptSchema = z.object({
  sourceType: z.enum(['youtube', 'ytr', 'rss']),
  sourceId: z.string(),
  title: z.string().optional()
});

router.post('/import-transcript', requireAuth, async (req, res) => {
  try {
    const result = importTranscriptSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { sourceType, sourceId, title } = result.data;
    let content = '';
    let videoUrl = '';
    let transcriptTitle = title || '';

    if (sourceType === 'youtube') {
      // Get YouTube video transcript
      const video = await storage.getYoutubeVideo(sourceId);
      if (!video || video.userId !== req.user.id) {
        return res.status(404).json({ message: 'Video not found' });
      }
      if (!video.transcription) {
        return res.status(400).json({ message: 'Video has no transcription' });
      }
      content = video.transcription;
      videoUrl = `https://www.youtube.com/watch?v=${sourceId}`;
      transcriptTitle = title || video.title;
    } else {
      return res.status(400).json({ message: 'Source type not yet supported' });
    }

    // Create new transcript
    const transcriptId = await storage.createTxtTranscript(req.user.id, {
      title: transcriptTitle,
      content,
      videoUrl,
      sourceType: 'imported',
      sourceId
    });

    const transcript = await storage.getTxtTranscript(transcriptId, req.user.id);
    res.status(201).json(transcript);
  } catch (error) {
    console.error('Error importing transcript:', error);
    res.status(500).json({ message: 'Failed to import transcript' });
  }
});

// Bulk import transcripts
const bulkImportSchema = z.object({
  transcripts: z.array(z.object({
    title: z.string().min(1),
    content: z.string().min(1),
    videoUrl: z.string().url().optional(),
    sourceType: z.enum(['manual', 'rss', 'ytr', 'realtime', 'feed', 'imported']).optional()
  }))
});

router.post('/bulk-import', requireAuth, async (req, res) => {
  try {
    const result = bulkImportSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: 'Invalid request', errors: result.error.errors });
    }

    const { transcripts: transcriptsToImport } = result.data;
    const importedTranscripts = [];
    const errors = [];

    for (let i = 0; i < transcriptsToImport.length; i++) {
      try {
        const transcriptData = transcriptsToImport[i];
        const transcriptId = await storage.createTxtTranscript(req.user.id, {
          title: transcriptData.title,
          content: transcriptData.content,
          videoUrl: transcriptData.videoUrl,
          sourceType: transcriptData.sourceType || 'imported'
        });

        const transcript = await storage.getTxtTranscript(transcriptId, req.user.id);
        importedTranscripts.push(transcript);
      } catch (error) {
        errors.push(`Transcript ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    res.json({
      success: true,
      imported: importedTranscripts.length,
      failed: errors.length,
      transcripts: importedTranscripts,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (error) {
    console.error('Error bulk importing transcripts:', error);
    res.status(500).json({ message: 'Failed to import transcripts' });
  }
});

// Export all transcripts
router.get('/export', requireAuth, async (req, res) => {
  try {
    const transcripts = await storage.getTxtTranscripts(req.user.id);

    // Get highlights for each transcript
    const transcriptsWithHighlights = await Promise.all(
      transcripts.map(async (transcript) => {
        const highlights = await storage.getTxtHighlights(transcript.id, req.user.id);
        return { ...transcript, highlights };
      })
    );

    const exportData = {
      version: '1.0',
      exported_at: new Date().toISOString(),
      user_id: req.user.id,
      transcripts: transcriptsWithHighlights
    };

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename="txt-transcripts-export.json"');
    res.json(exportData);
  } catch (error) {
    console.error('Error exporting transcripts:', error);
    res.status(500).json({ message: 'Failed to export transcripts' });
  }
});

export default router;
