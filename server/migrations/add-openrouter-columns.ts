import Database from 'better-sqlite3';

/**
 * Migration script to add OpenRouter columns to the settings table
 * and rename Ollama columns to OpenRouter columns in the youtube_videos table
 */
function runMigration() {
  console.log('Running migration: Adding OpenRouter columns to settings table');
  
  try {
    // Connect to the database
    const db = new Database('data.db');
    
    // Check if the openrouter_model column already exists in the settings table
    const settingsInfo = db.prepare('PRAGMA table_info(settings)').all();
    const hasOpenRouterModel = settingsInfo.some(column => column.name === 'openrouter_model');
    
    if (!hasOpenRouterModel) {
      // Add the openrouter_model column to the settings table
      db.exec(`
        ALTER TABLE settings ADD COLUMN openrouter_model TEXT DEFAULT 'google/gemini-2.0-flash-exp:free';
      `);
      
      console.log('Successfully added openrouter_model column to settings table');
    } else {
      console.log('openrouter_model column already exists in settings table');
    }
    
    // Check if the openrouter_analysis_prompt column already exists in the settings table
    const hasOpenRouterAnalysisPrompt = settingsInfo.some(column => column.name === 'openrouter_analysis_prompt');
    
    if (!hasOpenRouterAnalysisPrompt) {
      // Add the openrouter_analysis_prompt column to the settings table
      db.exec(`
        ALTER TABLE settings ADD COLUMN openrouter_analysis_prompt TEXT DEFAULT NULL;
      `);
      
      console.log('Successfully added openrouter_analysis_prompt column to settings table');
    } else {
      console.log('openrouter_analysis_prompt column already exists in settings table');
    }
    
    // Check if the openrouter_raw_data column already exists in the youtube_videos table
    const tableInfo = db.prepare('PRAGMA table_info(youtube_videos)').all();
    const hasOpenRouterRawData = tableInfo.some(column => column.name === 'openrouter_raw_data');
    
    if (!hasOpenRouterRawData) {
      // Add the openrouter_raw_data column to the youtube_videos table
      db.exec(`
        ALTER TABLE youtube_videos ADD COLUMN openrouter_raw_data TEXT DEFAULT NULL;
      `);
      
      console.log('Successfully added openrouter_raw_data column to youtube_videos table');
    } else {
      console.log('openrouter_raw_data column already exists in youtube_videos table');
    }
    
    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
}

// Run the migration
runMigration();
