import Database from 'better-sqlite3';
import { analyzeTranscriptWithOpenRouter } from './services/openrouter-financial-analysis';

/**
 * Script to fix OpenRouter raw data for videos that have financial analysis but no raw data
 */
async function fixOpenRouterRawData() {
  console.log('Starting to fix OpenRouter raw data...');

  try {
    // Connect to the database
    const db = new Database('data.db');

    // Get videos that have financial analysis but no OpenRouter raw data
    const videos = db.prepare(`
      SELECT id, title, transcription, description, financial_score
      FROM youtube_videos
      WHERE has_financial_analysis = 1
      AND financial_score > 0
      AND (openrouter_raw_data IS NULL OR openrouter_raw_data = '')
      AND has_transcription = 1
      LIMIT 10
    `).all();

    console.log(`Found ${videos.length} videos that need OpenRouter raw data`);

    for (const video of videos) {
      console.log(`Processing video ${video.id}: ${video.title}`);
      
      try {
        // Re-analyze the video with OpenRouter
        const analysis = await analyzeTranscriptWithOpenRouter(
          8, // Default user ID
          video.transcription,
          video.title,
          video.description
        );

        console.log(`Generated new analysis for video ${video.id} with score ${analysis.score}`);

        // Update the video with the raw data
        const updateStmt = db.prepare(`
          UPDATE youtube_videos
          SET openrouter_raw_data = ?
          WHERE id = ?
        `);

        updateStmt.run(analysis.rawData, video.id);
        console.log(`Updated raw data for video ${video.id}`);
      } catch (error) {
        console.error(`Error processing video ${video.id}:`, error);
      }
    }

    console.log('Finished fixing OpenRouter raw data');
    db.close();
  } catch (error) {
    console.error('Error fixing OpenRouter raw data:', error);
  }
}

// Run the function
fixOpenRouterRawData();
