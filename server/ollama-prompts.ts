/**
 * Implementation of Ollama Prompts methods for SQLiteStorage
 * This is a compatibility layer that redirects to OpenRouter prompts
 */

import { OllamaPrompt, OpenRouterPrompt } from '@shared/schema';
import { SQLiteStorage } from './sqliteStorage';

export function implementOllamaPromptMethods(storage: SQLiteStorage) {
  /**
   * Get all Ollama prompts for a user
   * Redirects to OpenRouter prompts
   */
  storage.getOllamaPrompts = async function(userId: number): Promise<OllamaPrompt[]> {
    try {
      // Get OpenRouter prompts and convert them to Ollama prompts
      const stmt = this.db.prepare(`
        SELECT id, user_id, name, prompt_text, is_default, created_at
        FROM openrouter_prompts
        WHERE user_id = ?
        ORDER BY is_default DESC, name ASC
      `);

      const prompts = stmt.all(userId);

      return prompts.map(prompt => ({
        id: prompt.id,
        userId: prompt.user_id,
        name: prompt.name,
        promptText: prompt.prompt_text,
        isDefault: <PERSON><PERSON>an(prompt.is_default),
        createdAt: prompt.created_at ? new Date(prompt.created_at) : undefined
      }));
    } catch (error) {
      console.error('Error getting Ollama prompts:', error);
      return []; // Return empty array instead of throwing
    }
  };

  /**
   * Get a specific Ollama prompt by ID
   * Redirects to OpenRouter prompts
   */
  storage.getOllamaPrompt = async function(promptId: number): Promise<OllamaPrompt | undefined> {
    try {
      // Get OpenRouter prompt and convert it to Ollama prompt
      const stmt = this.db.prepare(`
        SELECT id, user_id, name, prompt_text, is_default, created_at
        FROM openrouter_prompts
        WHERE id = ?
      `);

      const prompt = stmt.get(promptId);

      if (!prompt) return undefined;

      return {
        id: prompt.id,
        userId: prompt.user_id,
        name: prompt.name,
        promptText: prompt.prompt_text,
        isDefault: Boolean(prompt.is_default),
        createdAt: prompt.created_at ? new Date(prompt.created_at) : undefined
      };
    } catch (error) {
      console.error('Error getting Ollama prompt:', error);
      return undefined;
    }
  };

  /**
   * Create a new Ollama prompt
   * Redirects to OpenRouter prompts
   */
  storage.createOllamaPrompt = async function(
    userId: number,
    name: string,
    promptText: string,
    isDefault: boolean = false
  ): Promise<OllamaPrompt> {
    try {
      // Check if the createOpenRouterPrompt function exists
      if (typeof storage.createOpenRouterPrompt !== 'function') {
        // Implement the function directly if it doesn't exist
        console.log('createOpenRouterPrompt function not found, implementing directly');

        // If this prompt is set as default, unset any existing default prompts for this user
        if (isDefault) {
          const unsetDefaultStmt = this.db.prepare(`
            UPDATE openrouter_prompts
            SET is_default = 0
            WHERE user_id = ? AND is_default = 1
          `);
          unsetDefaultStmt.run(userId);
        }

        // Insert the new prompt
        const insertStmt = this.db.prepare(`
          INSERT INTO openrouter_prompts (user_id, name, prompt_text, system_prompt, is_default)
          VALUES (?, ?, ?, ?, ?)
        `);

        const result = insertStmt.run(userId, name, promptText, '', isDefault ? 1 : 0);
        const promptId = result.lastInsertRowid as number;

        // If this is the default prompt, update the user's settings
        if (isDefault) {
          const updateSettingsStmt = this.db.prepare(`
            UPDATE settings
            SET selected_prompt_id = ?
            WHERE user_id = ?
          `);
          updateSettingsStmt.run(promptId, userId);
        }

        return {
          id: promptId,
          userId,
          name,
          promptText,
          isDefault,
          createdAt: new Date()
        };
      } else {
        // Use the existing createOpenRouterPrompt function
        const openRouterPrompt = await storage.createOpenRouterPrompt(userId, name, promptText, '', isDefault);

        // Convert OpenRouter prompt to Ollama prompt
        return {
          id: openRouterPrompt.id,
          userId: openRouterPrompt.userId,
          name: openRouterPrompt.name,
          promptText: openRouterPrompt.promptText,
          isDefault: openRouterPrompt.isDefault,
          createdAt: openRouterPrompt.createdAt
        };
      }
    } catch (error) {
      console.error('Error creating Ollama prompt:', error);
      throw error;
    }
  };

  /**
   * Update an existing Ollama prompt
   * Redirects to OpenRouter prompts
   */
  storage.updateOllamaPrompt = async function(
    promptId: number,
    updates: Partial<OllamaPrompt>
  ): Promise<OllamaPrompt> {
    try {
      // Check if the updateOpenRouterPrompt function exists
      if (typeof storage.updateOpenRouterPrompt !== 'function') {
        // Implement the function directly if it doesn't exist
        console.log('updateOpenRouterPrompt function not found, implementing directly');

        // Get the current prompt
        const getPromptStmt = this.db.prepare(`
          SELECT id, user_id, name, prompt_text, system_prompt, is_default, created_at
          FROM openrouter_prompts
          WHERE id = ?
        `);

        const currentPrompt = getPromptStmt.get(promptId);
        if (!currentPrompt) {
          throw new Error(`Prompt with ID ${promptId} not found`);
        }

        // If setting this prompt as default, unset any existing default prompts for this user
        if (updates.isDefault && !currentPrompt.is_default) {
          const unsetDefaultStmt = this.db.prepare(`
            UPDATE openrouter_prompts
            SET is_default = 0
            WHERE user_id = ? AND is_default = 1
          `);
          unsetDefaultStmt.run(currentPrompt.user_id);
        }

        // Update the prompt
        const updateStmt = this.db.prepare(`
          UPDATE openrouter_prompts
          SET
            name = COALESCE(?, name),
            prompt_text = COALESCE(?, prompt_text),
            is_default = COALESCE(?, is_default)
          WHERE id = ?
        `);

        updateStmt.run(
          updates.name || null,
          updates.promptText || null,
          updates.isDefault !== undefined ? (updates.isDefault ? 1 : 0) : null,
          promptId
        );

        // If this is now the default prompt, update the user's settings
        if (updates.isDefault) {
          const updateSettingsStmt = this.db.prepare(`
            UPDATE settings
            SET selected_prompt_id = ?
            WHERE user_id = ?
          `);
          updateSettingsStmt.run(promptId, currentPrompt.user_id);
        }

        // Get the updated prompt
        const updatedPrompt = getPromptStmt.get(promptId);
        if (!updatedPrompt) {
          throw new Error(`Failed to retrieve updated prompt with ID ${promptId}`);
        }

        // Convert to OllamaPrompt format
        return {
          id: updatedPrompt.id,
          userId: updatedPrompt.user_id,
          name: updatedPrompt.name,
          promptText: updatedPrompt.prompt_text,
          isDefault: Boolean(updatedPrompt.is_default),
          createdAt: updatedPrompt.created_at ? new Date(updatedPrompt.created_at) : undefined
        };
      } else {
        // Convert Ollama prompt updates to OpenRouter prompt updates
        const openRouterUpdates: Partial<OpenRouterPrompt> = {
          name: updates.name,
          promptText: updates.promptText,
          isDefault: updates.isDefault
        };

        // Update OpenRouter prompt
        const openRouterPrompt = await storage.updateOpenRouterPrompt(promptId, openRouterUpdates);

        // Convert OpenRouter prompt to Ollama prompt
        return {
          id: openRouterPrompt.id,
          userId: openRouterPrompt.userId,
          name: openRouterPrompt.name,
          promptText: openRouterPrompt.promptText,
          isDefault: openRouterPrompt.isDefault,
          createdAt: openRouterPrompt.createdAt
        };
      }
    } catch (error) {
      console.error('Error updating Ollama prompt:', error);
      throw error;
    }
  };

  /**
   * Delete an Ollama prompt
   * Redirects to OpenRouter prompts
   */
  storage.deleteOllamaPrompt = async function(promptId: number): Promise<void> {
    try {
      // Check if the deleteOpenRouterPrompt function exists
      if (typeof storage.deleteOpenRouterPrompt !== 'function') {
        // Implement the function directly if it doesn't exist
        console.log('deleteOpenRouterPrompt function not found, implementing directly');

        // Get the prompt to check if it's the default
        const getPromptStmt = this.db.prepare(`
          SELECT id, user_id, is_default
          FROM openrouter_prompts
          WHERE id = ?
        `);

        const prompt = getPromptStmt.get(promptId);
        if (!prompt) {
          return; // Prompt doesn't exist, nothing to delete
        }

        // Delete the prompt
        const deleteStmt = this.db.prepare('DELETE FROM openrouter_prompts WHERE id = ?');
        deleteStmt.run(promptId);

        // If this was the default prompt, update the user's settings
        if (prompt.is_default) {
          // Find another prompt to set as default
          const findAnotherPromptStmt = this.db.prepare(`
            SELECT id FROM openrouter_prompts
            WHERE user_id = ? AND id != ?
            ORDER BY created_at DESC
            LIMIT 1
          `);

          const anotherPrompt = findAnotherPromptStmt.get(prompt.user_id, promptId);

          if (anotherPrompt) {
            // Set this prompt as the new default
            await this.setDefaultOllamaPrompt(prompt.user_id, anotherPrompt.id);
          } else {
            // No other prompts, set selected_prompt_id to NULL
            const updateSettingsStmt = this.db.prepare(`
              UPDATE settings
              SET selected_prompt_id = NULL
              WHERE user_id = ?
            `);
            updateSettingsStmt.run(prompt.user_id);
          }
        }
      } else {
        // Use the existing deleteOpenRouterPrompt function
        await storage.deleteOpenRouterPrompt(promptId);
      }
    } catch (error) {
      console.error('Error deleting Ollama prompt:', error);
      throw error;
    }
  };

  /**
   * Set a prompt as the default for a user
   * Redirects to OpenRouter prompts
   */
  storage.setDefaultOllamaPrompt = async function(userId: number, promptId: number): Promise<void> {
    try {
      // Check if the setDefaultOpenRouterPrompt function exists
      if (typeof storage.setDefaultOpenRouterPrompt !== 'function') {
        // Implement the function directly if it doesn't exist
        console.log('setDefaultOpenRouterPrompt function not found, implementing directly');

        // Unset any existing default prompts for this user
        const unsetDefaultStmt = this.db.prepare(`
          UPDATE openrouter_prompts
          SET is_default = 0
          WHERE user_id = ? AND is_default = 1
        `);
        unsetDefaultStmt.run(userId);

        // Set the new default prompt
        const setDefaultStmt = this.db.prepare(`
          UPDATE openrouter_prompts
          SET is_default = 1
          WHERE id = ? AND user_id = ?
        `);
        setDefaultStmt.run(promptId, userId);

        // Update the user's settings
        const updateSettingsStmt = this.db.prepare(`
          UPDATE settings
          SET selected_prompt_id = ?
          WHERE user_id = ?
        `);
        updateSettingsStmt.run(promptId, userId);
      } else {
        // Use the existing setDefaultOpenRouterPrompt function
        await storage.setDefaultOpenRouterPrompt(userId, promptId);
      }
    } catch (error) {
      console.error('Error setting default Ollama prompt:', error);
      throw error;
    }
  };
}
