import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { 
  Zap, 
  Copy, 
  Wand2, 
  Target, 
  TrendingUp, 
  Clock, 
  Eye, 
  Heart, 
  MessageCircle,
  BarChart3,
  Sparkles,
  Rocket,
  Crown,
  RefreshCw,
  Play
} from 'lucide-react';

interface ScriptAlgorithm {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  retentionScore: number;
  engagementScore: number;
  conversionScore: number;
  bestFor: string[];
  structure: string[];
  hooks: string[];
  patternInterrupts: string[];
  retentionHooks: string[];
}

interface GeneratedScript {
  content: string;
  algorithm: string;
  topic: string;
  estimatedWatchTime: number;
  retentionPrediction: number;
  engagementScore: number;
  hookStrength: number;
  timestamp: Date;
}

interface PerformanceMetrics {
  retentionPrediction: number;
  engagementScore: number;
  hookStrength: number;
  conversionPotential: number;
  viralPotential: number;
}

const InstantScriptGenerator: React.FC = () => {
  const { toast } = useToast();
  const [topic, setTopic] = useState('');
  const [keyDetails, setKeyDetails] = useState('');
  const [selectedAlgorithm, setSelectedAlgorithm] = useState('slick-v1');
  const [scriptLength, setScriptLength] = useState(6);
  const [urgencyLevel, setUrgencyLevel] = useState(8);
  const [generatedScript, setGeneratedScript] = useState<GeneratedScript | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  const [scriptHistory, setScriptHistory] = useState<GeneratedScript[]>([]);

  const algorithms: ScriptAlgorithm[] = [
    {
      id: 'slick-v1',
      name: 'SlickV1 Proven',
      description: 'Your original high-converting formula with urgent tone and empathetic approach',
      icon: Crown,
      color: 'text-yellow-600 bg-yellow-100',
      retentionScore: 92,
      engagementScore: 88,
      conversionScore: 95,
      bestFor: ['Social Security updates', 'Benefit announcements', 'Financial relief'],
      structure: [
        'Urgent hook with specific benefit amount',
        'Direct address to target audience',
        'Problem acknowledgment with empathy',
        'Clear solution breakdown',
        'Action steps with urgency',
        'Scam warning integration',
        'Strong CTA with engagement'
      ],
      hooks: [
        'Hello everyone, this is urgent, this is real.',
        'Are you one of the [X] million Americans receiving [BENEFIT]?',
        'You need to hear this right now - this could mean $[AMOUNT] for you.',
        'This is breaking: [BENEFIT] recipients are getting $[AMOUNT].',
        'If you\'re on [BENEFIT], you need to know this immediately.'
      ],
      patternInterrupts: [
        'But here\'s what most people don\'t know...',
        'Wait, there\'s something even more important...',
        'This is the part that will surprise you...',
        'Hold on - this changes everything...',
        'Before I continue, you need to understand this...'
      ],
      retentionHooks: [
        'Stick around because I\'m about to reveal the exact amount...',
        'Don\'t click away - the most important part is coming up...',
        'Keep watching because this next part could save you thousands...',
        'Stay with me - I\'m about to show you exactly how to apply...'
      ]
    },
    {
      id: 'viral-hook',
      name: 'Viral Hook Formula',
      description: 'Optimized for maximum reach and shares with shocking statistics and immediate value',
      icon: Rocket,
      color: 'text-red-600 bg-red-100',
      retentionScore: 89,
      engagementScore: 94,
      conversionScore: 82,
      bestFor: ['Breaking news', 'Shocking revelations', 'Trending topics'],
      structure: [
        'Shocking statistic or revelation',
        'Immediate value promise',
        'Curiosity gap creation',
        'Step-by-step revelation',
        'Social proof integration',
        'Viral-worthy conclusion',
        'Share-encouraging CTA'
      ],
      hooks: [
        '[SHOCKING STAT]% of [AUDIENCE] don\'t know about this $[AMOUNT] benefit.',
        'This [BENEFIT] secret could put $[AMOUNT] in your pocket this month.',
        'Government just announced: [AUDIENCE] getting unexpected $[AMOUNT].',
        'Only [SMALL %]% of people know about this [BENEFIT] loophole.',
        'This [BENEFIT] mistake is costing you $[AMOUNT] every month.'
      ],
      patternInterrupts: [
        'But that\'s not even the crazy part...',
        'Plot twist: it gets even better...',
        'Here\'s where it gets interesting...',
        'You won\'t believe what happens next...',
        'This is where most people mess up...'
      ],
      retentionHooks: [
        'The next part will blow your mind...',
        'Wait until you see what I found...',
        'This revelation at the end will shock you...',
        'Keep watching - the best part is coming...'
      ]
    },
    {
      id: 'breaking-news',
      name: 'Breaking News Style',
      description: 'Fast-paced, urgent delivery for time-sensitive information',
      icon: Zap,
      color: 'text-orange-600 bg-orange-100',
      retentionScore: 85,
      engagementScore: 91,
      conversionScore: 88,
      bestFor: ['Urgent updates', 'Deadline announcements', 'Policy changes'],
      structure: [
        'Breaking news announcement',
        'Immediate impact statement',
        'Timeline and urgency',
        'Who\'s affected breakdown',
        'Action required section',
        'Deadline emphasis',
        'Emergency CTA'
      ],
      hooks: [
        'BREAKING: [BENEFIT] recipients have [TIMEFRAME] to claim $[AMOUNT].',
        'URGENT UPDATE: [POLICY] changes affecting [X] million Americans.',
        'JUST ANNOUNCED: $[AMOUNT] payments starting [DATE].',
        'ALERT: [BENEFIT] deadline moved to [DATE] - here\'s what to do.',
        'DEVELOPING: New [BENEFIT] rules could mean $[AMOUNT] for you.'
      ],
      patternInterrupts: [
        'This just in...',
        'Update: we\'re getting more information...',
        'Breaking development...',
        'Latest update...',
        'This changes the situation...'
      ],
      retentionHooks: [
        'More breaking details coming up...',
        'Stay tuned for the latest updates...',
        'Don\'t miss the critical deadline information...',
        'Keep watching for step-by-step instructions...'
      ]
    },
    {
      id: 'educational-deep',
      name: 'Educational Deep-dive',
      description: 'Comprehensive, authoritative approach for complex topics',
      icon: BarChart3,
      color: 'text-blue-600 bg-blue-100',
      retentionScore: 94,
      engagementScore: 79,
      conversionScore: 86,
      bestFor: ['Complex explanations', 'Policy analysis', 'Long-form content'],
      structure: [
        'Expert introduction',
        'Problem/situation overview',
        'Historical context',
        'Detailed explanation',
        'Examples and case studies',
        'Practical applications',
        'Expert conclusion'
      ],
      hooks: [
        'Let me explain exactly how [BENEFIT] works and why it matters to you.',
        'Today I\'m breaking down everything you need to know about [TOPIC].',
        'As someone who\'s studied [TOPIC] for years, here\'s what you need to know.',
        'I\'m going to walk you through [TOPIC] step by step.',
        'Here\'s the complete guide to understanding [BENEFIT].'
      ],
      patternInterrupts: [
        'Now, here\'s where it gets complicated...',
        'This is the key point most people miss...',
        'Let me show you a real example...',
        'Here\'s what the data actually shows...',
        'This is crucial to understand...'
      ],
      retentionHooks: [
        'The next section covers the most important part...',
        'Coming up: the practical steps you can take...',
        'Stay with me - we\'re getting to the actionable advice...',
        'The conclusion will tie everything together...'
      ]
    }
  ];

  // Load script history from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('txtpro_script_history');
    if (saved) {
      setScriptHistory(JSON.parse(saved));
    }
  }, []);

  // Save script history to localStorage
  useEffect(() => {
    localStorage.setItem('txtpro_script_history', JSON.stringify(scriptHistory));
  }, [scriptHistory]);

  const calculatePerformanceMetrics = (algorithm: ScriptAlgorithm, topic: string, urgency: number): PerformanceMetrics => {
    // Simulate AI-powered performance prediction
    const baseRetention = algorithm.retentionScore;
    const baseEngagement = algorithm.engagementScore;
    const baseConversion = algorithm.conversionScore;
    
    // Adjust based on topic relevance (simulate keyword analysis)
    const topicBonus = topic.toLowerCase().includes('social security') || 
                      topic.toLowerCase().includes('benefit') || 
                      topic.toLowerCase().includes('payment') ? 5 : 0;
    
    // Adjust based on urgency level
    const urgencyMultiplier = urgency / 10;
    
    return {
      retentionPrediction: Math.min(95, baseRetention + topicBonus + (urgency * 0.5)),
      engagementScore: Math.min(95, baseEngagement + topicBonus + (urgency * 0.3)),
      hookStrength: Math.min(95, 70 + (urgency * 2) + topicBonus),
      conversionPotential: Math.min(95, baseConversion + topicBonus + (urgency * 0.4)),
      viralPotential: Math.min(95, (baseEngagement * 0.8) + (urgency * 1.2) + topicBonus)
    };
  };

  const generateScript = async () => {
    if (!topic.trim()) {
      toast({
        title: "Topic Required",
        description: "Please enter a topic for your script",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    
    // Simulate AI processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    const algorithm = algorithms.find(a => a.id === selectedAlgorithm)!;
    const metrics = calculatePerformanceMetrics(algorithm, topic, urgencyLevel);
    
    const script = generateScriptContent(algorithm, topic, keyDetails, scriptLength, urgencyLevel);
    
    const generatedScript: GeneratedScript = {
      content: script,
      algorithm: algorithm.name,
      topic,
      estimatedWatchTime: scriptLength,
      retentionPrediction: metrics.retentionPrediction,
      engagementScore: metrics.engagementScore,
      hookStrength: metrics.hookStrength,
      timestamp: new Date()
    };

    setGeneratedScript(generatedScript);
    setPerformanceMetrics(metrics);
    setScriptHistory(prev => [generatedScript, ...prev.slice(0, 9)]); // Keep last 10
    setIsGenerating(false);

    toast({
      title: "🎬 Script Generated!",
      description: `${algorithm.name} script ready with ${metrics.retentionPrediction}% predicted retention`,
      duration: 4000
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-2xl">
            <Sparkles className="h-6 w-6 text-purple-600" />
            Instant Script Generator
          </CardTitle>
          <CardDescription className="text-lg">
            🚀 <strong>Generate high-performing video scripts in 10 seconds</strong> using proven algorithms with built-in retention science and performance predictions.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Main Generator */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left: Input & Controls */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">📝 Script Input</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="topic">Topic/Title *</Label>
                <Input
                  id="topic"
                  placeholder="e.g., Social Security $200 increase for 2024"
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="keyDetails">Key Details (Optional)</Label>
                <Textarea
                  id="keyDetails"
                  placeholder="e.g., Effective January 2024, affects 70 million recipients, COLA adjustment..."
                  value={keyDetails}
                  onChange={(e) => setKeyDetails(e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>Script Length: {scriptLength} minutes</Label>
                <Slider
                  value={[scriptLength]}
                  onValueChange={(value) => setScriptLength(value[0])}
                  min={2}
                  max={15}
                  step={0.5}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label>Urgency Level: {urgencyLevel}/10</Label>
                <Slider
                  value={[urgencyLevel]}
                  onValueChange={(value) => setUrgencyLevel(value[0])}
                  min={1}
                  max={10}
                  step={1}
                  className="w-full"
                />
              </div>

              <Button
                onClick={generateScript}
                disabled={!topic.trim() || isGenerating}
                className="w-full bg-purple-600 hover:bg-purple-700"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Generating Script...
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4 mr-2" />
                    Generate Script (10s)
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Algorithm Selector */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">🧠 Algorithm Selector</CardTitle>
              <CardDescription>Choose your script generation formula</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {algorithms.map(algorithm => (
                  <div
                    key={algorithm.id}
                    className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                      selectedAlgorithm === algorithm.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedAlgorithm(algorithm.id)}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-lg ${algorithm.color}`}>
                        <algorithm.icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{algorithm.name}</h4>
                        <p className="text-xs text-muted-foreground mb-2">
                          {algorithm.description}
                        </p>
                        <div className="flex gap-2">
                          <Badge variant="outline" className="text-xs">
                            {algorithm.retentionScore}% retention
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {algorithm.engagementScore}% engagement
                          </Badge>
                        </div>
                        <div className="mt-2">
                          <p className="text-xs text-blue-600">
                            Best for: {algorithm.bestFor.slice(0, 2).join(', ')}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right: Generated Script & Performance */}
        <div className="lg:col-span-2 space-y-4">
          {!generatedScript && !isGenerating && (
            <Card className="border-dashed border-2 border-gray-300">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Play className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-600 mb-2">Ready to Generate</h3>
                <p className="text-sm text-gray-500 text-center">
                  Enter your topic and click "Generate Script" to create a high-performing video script in seconds
                </p>
              </CardContent>
            </Card>
          )}

          {isGenerating && (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <RefreshCw className="h-8 w-8 text-purple-600 animate-spin mb-4" />
                <h3 className="text-lg font-medium mb-2">Generating Your Script...</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Applying {algorithms.find(a => a.id === selectedAlgorithm)?.name} algorithm
                </p>
                <Progress value={66} className="w-64" />
              </CardContent>
            </Card>
          )}

          {generatedScript && performanceMetrics && (
            <>
              {/* Performance Metrics */}
              <Card className="border-green-200 bg-green-50">
                <CardHeader>
                  <CardTitle className="text-lg text-green-800">📊 Performance Predictions</CardTitle>
                  <CardDescription className="text-green-700">
                    AI-powered analysis of your script's potential performance
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        <Eye className="h-4 w-4 text-blue-600 mr-1" />
                        <span className="text-2xl font-bold text-blue-600">
                          {Math.round(performanceMetrics.retentionPrediction)}%
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">Retention</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        <Heart className="h-4 w-4 text-red-600 mr-1" />
                        <span className="text-2xl font-bold text-red-600">
                          {Math.round(performanceMetrics.engagementScore)}%
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">Engagement</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        <Target className="h-4 w-4 text-green-600 mr-1" />
                        <span className="text-2xl font-bold text-green-600">
                          {Math.round(performanceMetrics.hookStrength)}%
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">Hook Strength</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        <TrendingUp className="h-4 w-4 text-purple-600 mr-1" />
                        <span className="text-2xl font-bold text-purple-600">
                          {Math.round(performanceMetrics.conversionPotential)}%
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">Conversion</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-2">
                        <Rocket className="h-4 w-4 text-orange-600 mr-1" />
                        <span className="text-2xl font-bold text-orange-600">
                          {Math.round(performanceMetrics.viralPotential)}%
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">Viral Potential</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Generated Script */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">🎬 Generated Script</CardTitle>
                      <CardDescription>
                        {generatedScript.algorithm} • {generatedScript.estimatedWatchTime} min • {generatedScript.content.length} characters
                      </CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={async () => {
                          try {
                            await navigator.clipboard.writeText(generatedScript.content);
                            toast({
                              title: "✅ Script Copied!",
                              description: "Your script is ready to use",
                              duration: 3000
                            });
                          } catch (error) {
                            const textArea = document.createElement('textarea');
                            textArea.value = generatedScript.content;
                            document.body.appendChild(textArea);
                            textArea.select();
                            document.execCommand('copy');
                            document.body.removeChild(textArea);

                            toast({
                              title: "✅ Script Copied!",
                              description: "Your script is ready to use",
                              duration: 3000
                            });
                          }
                        }}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Copy Script
                      </Button>
                      <Button onClick={generateScript} variant="outline">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Regenerate
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                      {generatedScript.content}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>

      {/* Script History & A/B Testing */}
      {scriptHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">📚 Script History & Performance</CardTitle>
            <CardDescription>
              Track your generated scripts and their performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {scriptHistory.slice(0, 6).map((script, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-sm line-clamp-2">
                          {script.topic}
                        </CardTitle>
                        <p className="text-xs text-muted-foreground">
                          {script.algorithm} • {script.estimatedWatchTime}min
                        </p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {Math.round(script.retentionPrediction)}%
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs">
                        <span className="text-muted-foreground">Retention:</span>
                        <span className="font-medium">{Math.round(script.retentionPrediction)}%</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-muted-foreground">Engagement:</span>
                        <span className="font-medium">{Math.round(script.engagementScore)}%</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-muted-foreground">Hook:</span>
                        <span className="font-medium">{Math.round(script.hookStrength)}%</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-muted-foreground">Generated:</span>
                        <span className="font-medium">
                          {script.timestamp.toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex gap-1 mt-3">
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1 text-xs"
                          onClick={() => {
                            navigator.clipboard.writeText(script.content);
                            toast({
                              title: "Script Copied",
                              description: "Previous script copied to clipboard"
                            });
                          }}
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1 text-xs"
                          onClick={() => {
                            setTopic(script.topic);
                            setSelectedAlgorithm(
                              algorithms.find(a => a.name === script.algorithm)?.id || 'slick-v1'
                            );
                            toast({
                              title: "Settings Loaded",
                              description: "Script settings applied to generator"
                            });
                          }}
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          Reuse
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* A/B Testing Feature */}
      {generatedScript && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-lg text-blue-800">🧪 A/B Testing</CardTitle>
            <CardDescription className="text-blue-700">
              Generate alternative versions to test which performs better
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 flex-wrap">
              <Button
                onClick={() => {
                  // Generate with different algorithm
                  const currentAlgIndex = algorithms.findIndex(a => a.id === selectedAlgorithm);
                  const nextAlgIndex = (currentAlgIndex + 1) % algorithms.length;
                  setSelectedAlgorithm(algorithms[nextAlgIndex].id);
                  generateScript();
                }}
                variant="outline"
                className="bg-white"
              >
                <Target className="h-4 w-4 mr-2" />
                Try Different Algorithm
              </Button>

              <Button
                onClick={() => {
                  // Generate with different urgency
                  const newUrgency = urgencyLevel >= 8 ? 5 : 9;
                  setUrgencyLevel(newUrgency);
                  generateScript();
                }}
                variant="outline"
                className="bg-white"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Test Different Urgency
              </Button>

              <Button
                onClick={() => {
                  // Generate with different length
                  const newLength = scriptLength >= 6 ? 4 : 8;
                  setScriptLength(newLength);
                  generateScript();
                }}
                variant="outline"
                className="bg-white"
              >
                <Clock className="h-4 w-4 mr-2" />
                Test Different Length
              </Button>
            </div>

            <div className="mt-4 p-3 bg-blue-100 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">💡 A/B Testing Tips:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Test one variable at a time (algorithm, urgency, or length)</li>
                <li>• Use the same topic for fair comparison</li>
                <li>• Track actual performance metrics from your videos</li>
                <li>• Save high-performing combinations as presets</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Tips */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="text-lg text-yellow-800">💡 Pro Tips for Maximum Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-yellow-900 mb-2">🎯 Hook Optimization:</h4>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• Include specific dollar amounts in first 3 seconds</li>
                <li>• Use "you" language to directly address viewers</li>
                <li>• Create urgency with time-sensitive language</li>
                <li>• Ask questions that demand answers</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-yellow-900 mb-2">📈 Retention Boosters:</h4>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• Add pattern interrupts every 30-45 seconds</li>
                <li>• Use "but here's what most people don't know..."</li>
                <li>• Preview what's coming: "stick around for..."</li>
                <li>• Include specific examples and case studies</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-yellow-900 mb-2">🔥 Engagement Drivers:</h4>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• Ask viewers to comment with their situation</li>
                <li>• Include controversial or surprising statements</li>
                <li>• Use emotional language that resonates</li>
                <li>• End with strong call-to-action</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-yellow-900 mb-2">⚠️ Compliance Notes:</h4>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• Always include scam warnings for financial content</li>
                <li>• Verify information accuracy before publishing</li>
                <li>• Disclose when information is general guidance</li>
                <li>• Include official sources and contact information</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  // Helper function to generate script content
  function generateScriptContent(
    algorithm: ScriptAlgorithm,
    topic: string,
    details: string,
    length: number,
    urgency: number
  ): string {
    const selectedHook = algorithm.hooks[Math.floor(Math.random() * algorithm.hooks.length)];
    const patternInterrupt = algorithm.patternInterrupts[Math.floor(Math.random() * algorithm.patternInterrupts.length)];
    const retentionHook = algorithm.retentionHooks[Math.floor(Math.random() * algorithm.retentionHooks.length)];

    // Replace placeholders with actual content
    const processedHook = selectedHook
      .replace('[TOPIC]', topic)
      .replace('[AMOUNT]', '$200')
      .replace('[BENEFIT]', 'Social Security')
      .replace('[X]', '70')
      .replace('[AUDIENCE]', 'Social Security recipients');

    let script = '';

    if (algorithm.id === 'slick-v1') {
      script = generateSlickV1Script(processedHook, topic, details, length, urgency, patternInterrupt, retentionHook);
    } else if (algorithm.id === 'viral-hook') {
      script = generateViralScript(processedHook, topic, details, length, urgency, patternInterrupt, retentionHook);
    } else if (algorithm.id === 'breaking-news') {
      script = generateBreakingNewsScript(processedHook, topic, details, length, urgency, patternInterrupt, retentionHook);
    } else if (algorithm.id === 'educational-deep') {
      script = generateEducationalScript(processedHook, topic, details, length, urgency, patternInterrupt, retentionHook);
    }

    return script;
  }

  function generateSlickV1Script(hook: string, topic: string, details: string, length: number, urgency: number, patternInterrupt: string, retentionHook: string): string {
    return `${hook}

I know many of you have been struggling to stay afloat, hit hardest by inflation and rising costs. Your concerns are completely valid, and I'm here to give you the facts you need right now.

${patternInterrupt}

Let's start with the basics. ${topic} - this is absolutely critical information that could impact your monthly benefits significantly.

${details ? `Here's what we know: ${details}` : 'Here are the key details you need to understand:'}

What exactly does this mean for you?
- Who qualifies for this benefit
- When you can expect to see changes
- What you need to do RIGHT NOW

${retentionHook}

Here's what you need to do immediately:

First, verify your contact information with the Social Security Administration. This is paramount importance - if they can't reach you, you could miss out.

Second, update your banking information if needed. Make sure your direct deposit is set up correctly.

Third, if you receive extra money, prioritize paying down high-interest debt, then build your emergency savings.

CRITICAL WARNING: The Social Security Administration will NEVER ask for personal information over unsolicited calls, emails, or texts. They will never ask for fees, gift cards, wire transfers, or cryptocurrency. If someone contacts you claiming to be from SSA asking for these things, hang up immediately and report it.

This is about restoring fairness and dignity to your financial life. Better days are here and brighter ones may be just around the corner.

Make sure you hit that subscribe button and turn on notifications so you never miss important updates like this one. Drop a comment below - let me know how this information would help you.

Don't let this opportunity slip away. Your financial security depends on staying informed.`;
  }

  function generateViralScript(hook: string, topic: string, details: string, length: number, urgency: number, patternInterrupt: string, retentionHook: string): string {
    return `${hook}

And I'm about to show you exactly how this affects YOU personally.

${patternInterrupt}

Most people have no idea this is happening right now. While everyone's focused on other news, this massive change is flying under the radar.

${retentionHook}

Here's the step-by-step breakdown:

${details ? details : 'The details are more shocking than you think...'}

This isn't just another government announcement. This is a game-changer that could put real money in your pocket.

But here's the crazy part - most people will never even hear about this because the mainstream media isn't covering it properly.

You won't believe what happens next...

The government has quietly implemented changes that could mean hundreds or even thousands of dollars for eligible recipients.

Here's exactly what you need to do:
1. Check your eligibility immediately
2. Gather the required documentation
3. Submit your application before the deadline

This is going viral because people are finally waking up to what's really happening with their benefits.

Share this video with anyone you know who might be affected. They'll thank you later.

Hit that like button if this information was valuable, and subscribe for more insider updates you won't find anywhere else.

The next video I'm posting will reveal even more shocking details about upcoming changes. You don't want to miss it.`;
  }

  function generateBreakingNewsScript(hook: string, topic: string, details: string, length: number, urgency: number, patternInterrupt: string, retentionHook: string): string {
    return `${hook}

This is a developing story, and I'm bringing you the latest information as it becomes available.

${patternInterrupt}

Here's what we know right now:

${details ? details : 'The situation is evolving rapidly, and here are the confirmed details:'}

Timeline:
- Announcement made: [Recent date]
- Effective date: [Specific date]
- Application deadline: [Urgent deadline]

Who's affected:
- Current benefit recipients
- New applicants
- Specific eligibility requirements

${retentionHook}

URGENT ACTION REQUIRED:

If you're receiving benefits, you need to act immediately. Here's your step-by-step action plan:

1. Contact the Social Security Administration TODAY
2. Verify your current information is up to date
3. Submit any required documentation BEFORE the deadline

This just in - we're getting reports that phone lines are extremely busy due to high call volume. Don't wait.

DEVELOPING: Additional information suggests this could affect more people than initially reported.

Stay tuned for updates as this story develops. I'll be monitoring the situation and will post immediately if there are any changes.

Subscribe and hit the notification bell so you get breaking updates the moment they happen.

Time is critical here. Don't let bureaucratic delays cost you money you're entitled to.

This is an ongoing situation, and I'll continue to bring you the latest information as it becomes available.`;
  }

  function generateEducationalScript(hook: string, topic: string, details: string, length: number, urgency: number, patternInterrupt: string, retentionHook: string): string {
    return `${hook}

Today, I'm going to give you a comprehensive breakdown of everything you need to understand about this topic.

${patternInterrupt}

Let me start with some background context. Understanding the history and mechanics of this system is crucial to making informed decisions.

${details ? `Current situation: ${details}` : 'Here\'s the current landscape:'}

The key factors you need to understand:

1. Legal Framework
How the current system works and what governs these decisions.

2. Historical Precedent
What similar situations have looked like in the past and their outcomes.

3. Economic Impact
How this affects both individual recipients and the broader economy.

${retentionHook}

Now, let's dive into the practical implications:

For current recipients:
- What changes you can expect
- Timeline for implementation
- How to prepare for transitions

For new applicants:
- Updated eligibility requirements
- Application process changes
- Documentation you'll need

Real-world example:
Let me walk you through a typical scenario to illustrate how this works in practice.

[Detailed example with specific numbers and outcomes]

This is crucial to understand because many people make costly mistakes by not fully grasping these concepts.

The data shows that people who take the time to understand these systems thoroughly are significantly more likely to maximize their benefits.

Coming up in future videos, I'll be covering related topics that build on this foundation. Make sure you're subscribed so you don't miss the complete series.

If you found this explanation helpful, please like the video and share it with others who might benefit from this information.

Your questions and comments help me create better content, so please engage below.`;
  }
};

export default InstantScriptGenerator;
