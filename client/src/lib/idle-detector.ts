/**
 * Utility to detect when the app is idle and reduce background activity
 */

// Default idle timeout in milliseconds (5 minutes)
const DEFAULT_IDLE_TIMEOUT = 5 * 60 * 1000;

// Events to listen for to detect user activity
const ACTIVITY_EVENTS = [
  'mousedown',
  'mousemove',
  'keydown',
  'scroll',
  'touchstart',
  'click',
];

export class IdleDetector {
  private idleTimeout: number;
  private idleTimer: NodeJS.Timeout | null = null;
  private isIdle = false;
  private callbacks: { onIdle: () => void; onActive: () => void };
  private lastActivityTime: number = Date.now();

  constructor(
    callbacks: { onIdle: () => void; onActive: () => void },
    idleTimeout: number = DEFAULT_IDLE_TIMEOUT
  ) {
    this.idleTimeout = idleTimeout;
    this.callbacks = callbacks;
    this.setupEventListeners();
    this.startIdleTimer();
  }

  private setupEventListeners() {
    const handleActivity = this.handleUserActivity.bind(this);
    
    // Add event listeners for user activity
    ACTIVITY_EVENTS.forEach(eventName => {
      window.addEventListener(eventName, handleActivity, { passive: true });
    });

    // Also check visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        this.handleUserActivity();
      } else {
        // When tab is hidden, consider it idle immediately
        this.setIdle();
      }
    });
  }

  private handleUserActivity() {
    this.lastActivityTime = Date.now();
    
    if (this.isIdle) {
      this.setActive();
    }
    
    this.resetIdleTimer();
  }

  private startIdleTimer() {
    this.resetIdleTimer();
  }

  private resetIdleTimer() {
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
    }
    
    this.idleTimer = setTimeout(() => {
      this.setIdle();
    }, this.idleTimeout);
  }

  private setIdle() {
    if (!this.isIdle) {
      this.isIdle = true;
      this.callbacks.onIdle();
      console.log('[IdleDetector] App is now idle');
    }
  }

  private setActive() {
    if (this.isIdle) {
      this.isIdle = false;
      this.callbacks.onActive();
      console.log('[IdleDetector] App is now active');
    }
  }

  // Public methods
  public getIdleState(): boolean {
    return this.isIdle;
  }

  public getTimeSinceLastActivity(): number {
    return Date.now() - this.lastActivityTime;
  }

  public dispose() {
    const handleActivity = this.handleUserActivity.bind(this);
    
    // Remove event listeners
    ACTIVITY_EVENTS.forEach(eventName => {
      window.removeEventListener(eventName, handleActivity);
    });
    
    // Clear timers
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
      this.idleTimer = null;
    }
  }
}

// Create a singleton instance
let idleDetectorInstance: IdleDetector | null = null;

export function initIdleDetector(
  callbacks: { onIdle: () => void; onActive: () => void },
  idleTimeout: number = DEFAULT_IDLE_TIMEOUT
): IdleDetector {
  if (idleDetectorInstance) {
    idleDetectorInstance.dispose();
  }
  
  idleDetectorInstance = new IdleDetector(callbacks, idleTimeout);
  return idleDetectorInstance;
}

export function getIdleDetector(): IdleDetector | null {
  return idleDetectorInstance;
}
