import { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

type User = {
  id: string;
  username: string;
  email?: string;
  role?: 'admin' | 'user' | 'guest';
  isAdmin: boolean;
};

type APILoginResponse = {
  id: string;
  username: string;
  email?: string;
  role?: string;
  isAdmin: boolean;
};

const validateUser = (user: unknown): user is User => {
  if (!user || typeof user !== 'object') return false;
  if (!('id' in user) || typeof (user as any).id !== 'string' || !(user as any).id) return false;
  if (!('username' in user) || typeof (user as any).username !== 'string' || !(user as any).username) return false;
  if ('isAdmin' in user && typeof (user as any).isAdmin !== 'boolean') return false;
  return true;
};

type AuthContextType = {
  user: User | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isLoading: boolean;
  login: (user: Omit<User, 'isAdmin'>) => void;
  logout: () => void;
};

const defaultUserState = {
  user: null,
  isAuthenticated: false,
  isAdmin: false,
  isLoading: true
} as const;

const AuthContext = createContext<AuthContextType>({
  ...defaultUserState,
  login: () => {},
  logout: () => {}
});

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const login = (apiResponse: APILoginResponse) => {
    if (!apiResponse?.id || !apiResponse?.username) {
      throw new Error('Invalid API response data');
    }

    // Ensure proper boolean conversion and validation
    const normalizedUser = {
      ...apiResponse,
      isAdmin: Boolean(apiResponse.isAdmin ?? (apiResponse.role === 'admin')),
      role: ['admin', 'user', 'guest'].includes(apiResponse.role ?? '')
        ? apiResponse.role
        : apiResponse.isAdmin ? 'admin' : 'user'
    };

    try {
      setUser(normalizedUser);
      localStorage.setItem('user', JSON.stringify(normalizedUser));
    } catch (error) {
      console.error('Failed to persist user to localStorage:', error);
      setUser(userWithAdmin); // Still set user even if localStorage fails
    }
  };
  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
    navigate('/login');
  };

  useEffect(() => {
    setIsLoading(true);
    const initializeAuth = async () => {
      try {
        const storedUser = localStorage.getItem('user');
        if (!storedUser) {
          setIsLoading(false);
          return;
        }

        const parsedUser = JSON.parse(storedUser);
        if (!validateUser(parsedUser)) {
          console.warn('Invalid/malformed user data in localStorage');
          localStorage.removeItem('user');
          setIsLoading(false);
          return;
        }

        // Normalize and validate user data from localStorage
        const normalizedUser = {
        const normalizedUser = {
          ...parsedUser,
          isAdmin: 'isAdmin' in parsedUser 
            ? Boolean(parsedUser.isAdmin)
            : parsedUser.role === 'admin',
          role: ['admin', 'user', 'guest'].includes(parsedUser.role ?? '')
            ? parsedUser.role
            : Boolean(parsedUser.isAdmin) ? 'admin' : 'user'
        };
        // Validate required fields
        if (!normalizedUser.id || !normalizedUser.username) {
          console.warn('Stored user data is missing required fields');
          localStorage.removeItem('user');
          return;
        }
        setUser(normalizedUser);
      } catch (error) {
        console.error('Error initializing auth state:', error);
        localStorage.removeItem('user');
        setUser(null);
      } finally {
        setTimeout(() => setIsLoading(false), 500); // Minimum loading time for smoother UX
      }
      }
    };

    initializeAuth();
  }, []);

  const isAuthenticated = !!user;
  const isAdmin = user?.isAdmin || false;

  return (
    <AuthContext.Provider value={{ 
      user, 
      isAuthenticated,
      isAdmin,
      isLoading,
      login, 
      logout 
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

