import { useState, useEffect } from 'react';
import { useBackgroundTasks, TaskStatus, Task } from './use-background-tasks';
import { useToast } from './use-toast';
import { useYoutubeChannels } from './use-youtube-channels';
import { useQueryClient } from '@tanstack/react-query';

export function useYoutubeBackgroundTasks() {
  const { tasks, fetchTranscription, refreshChannel, analyzeFinancial, refetchTasks } = useBackgroundTasks();
  const { getVideoTranscription, refreshChannelVideos } = useYoutubeChannels();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [pendingTranscriptions, setPendingTranscriptions] = useState<Record<string, string>>({});
  const [pendingChannelRefreshes, setPendingChannelRefreshes] = useState<Record<string, string>>({});

  // Monitor tasks for completion
  useEffect(() => {
    // Check for completed transcription tasks
    const completedTranscriptionTasks = tasks.filter(
      task =>
        task.status === TaskStatus.COMPLETED &&
        task.type === 'fetch-transcription' &&
        pendingTranscriptions[task.data.videoId]
    );

    // Notify about completed transcriptions
    completedTranscriptionTasks.forEach(task => {
      const videoId = task.data.videoId;

      // Remove from pending list
      setPendingTranscriptions(prev => {
        const newPending = { ...prev };
        delete newPending[videoId];
        return newPending;
      });

      // Show notification
      toast({
        title: 'Transcription Ready',
        description: `The transcription for video ${videoId.substring(0, 8)}... is now available.`,
      });
    });

    // Check for failed transcription tasks
    const failedTranscriptionTasks = tasks.filter(
      task =>
        (task.status === TaskStatus.FAILED || task.status === TaskStatus.CANCELLED) &&
        task.type === 'fetch-transcription' &&
        pendingTranscriptions[task.data.videoId]
    );

    // Notify about failed transcriptions
    failedTranscriptionTasks.forEach(task => {
      const videoId = task.data.videoId;

      // Remove from pending list
      setPendingTranscriptions(prev => {
        const newPending = { ...prev };
        delete newPending[videoId];
        return newPending;
      });

      // Show notification
      toast({
        title: 'Transcription Failed',
        description: `Failed to fetch transcription for video ${videoId.substring(0, 8)}...`,
        variant: 'destructive',
      });
    });

    // Check for completed channel refresh tasks
    const completedChannelRefreshTasks = tasks.filter(
      task =>
        task.status === TaskStatus.COMPLETED &&
        task.type === 'refresh-channel-videos' &&
        pendingChannelRefreshes[task.data.channelId]
    );

    // Process completed channel refresh tasks
    completedChannelRefreshTasks.forEach(task => {
      const channelId = task.data.channelId;

      // Remove from pending list
      setPendingChannelRefreshes(prev => {
        const newPending = { ...prev };
        delete newPending[channelId];
        return newPending;
      });

      // Invalidate queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/${channelId}/videos`] });
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels/all-videos"] });
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels/video-counts"] });

      // Force immediate refetches
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: [`/api/youtube-channels/${channelId}/videos`] });
        queryClient.refetchQueries({ queryKey: ["/api/youtube-channels/all-videos"] });
        queryClient.refetchQueries({ queryKey: ["/api/youtube-channels/video-counts"] });
      }, 500);

      // Show notification
      toast({
        title: 'Channel Refresh Complete',
        description: `Videos for channel ${channelId} have been refreshed.`,
      });
    });

    // Check for failed channel refresh tasks
    const failedChannelRefreshTasks = tasks.filter(
      task =>
        (task.status === TaskStatus.FAILED || task.status === TaskStatus.CANCELLED) &&
        task.type === 'refresh-channel-videos' &&
        pendingChannelRefreshes[task.data.channelId]
    );

    // Process failed channel refresh tasks
    failedChannelRefreshTasks.forEach(task => {
      const channelId = task.data.channelId;

      // Remove from pending list
      setPendingChannelRefreshes(prev => {
        const newPending = { ...prev };
        delete newPending[channelId];
        return newPending;
      });

      // Show notification
      toast({
        title: 'Channel Refresh Failed',
        description: `Failed to refresh videos for channel ${channelId}. ${task.error || ''}`,
        variant: 'destructive',
      });
    });
  }, [tasks, pendingTranscriptions, pendingChannelRefreshes, toast, queryClient]);

  // Enhanced version of getVideoTranscription that uses background tasks
  const getVideoTranscriptionWithBackgroundTask = async (videoId: string) => {
    try {
      // First check if the video already has a transcription
      const response = await getVideoTranscription.mutateAsync(videoId);
      console.log('Transcription response:', response);

      // If the response contains a transcription property, return it directly
      if (response && typeof response.transcription === 'string') {
        return response.transcription;
      }

      // If the response indicates a background task was created
      if (response && response.taskId) {
        // Add to pending transcriptions
        setPendingTranscriptions(prev => ({
          ...prev,
          [videoId]: response.taskId
        }));

        // Show notification
        toast({
          title: 'Transcription Requested',
          description: 'The transcription is being fetched in the background. You will be notified when it is ready.',
        });

        // Trigger a refetch of tasks to ensure we have the latest task status
        setTimeout(() => {
          refetchTasks();
        }, 1000);

        // Return a message indicating the task is in progress
        return `Transcription is being processed in the background. Please check back later. You can view this video on YouTube: https://www.youtube.com/watch?v=${videoId}`;
      }

      // Fallback for unexpected response format
      return `Unable to fetch transcription. You can view this video on YouTube: https://www.youtube.com/watch?v=${videoId}`;
    } catch (error) {
      console.error('Error in getVideoTranscriptionWithBackgroundTask:', error);

      // Create a background task directly
      try {
        console.log('Creating background task for transcription directly');
        const task = await fetchTranscription(videoId);
        console.log('Created background task:', task);

        // Add to pending transcriptions
        setPendingTranscriptions(prev => ({
          ...prev,
          [videoId]: task.id
        }));

        // Show notification
        toast({
          title: 'Transcription Requested',
          description: 'The transcription is being fetched in the background. You will be notified when it is ready.',
        });

        // Trigger a refetch of tasks to ensure we have the latest task status
        setTimeout(() => {
          refetchTasks();
        }, 1000);

        // Return a message indicating the task is in progress
        return `Transcription is being processed in the background. Please check back later. You can view this video on YouTube: https://www.youtube.com/watch?v=${videoId}`;
      } catch (taskError) {
        console.error('Error creating background task:', taskError);
        return `Unable to fetch transcription. You can view this video on YouTube: https://www.youtube.com/watch?v=${videoId}`;
      }
    }
  };

  // Enhanced version of refreshChannelVideos that uses background tasks
  const refreshChannelVideosWithBackgroundTask = async (channelId: number) => {
    try {
      console.log(`Creating background task to refresh channel ${channelId}`);

      // Create a background task
      const task = await refreshChannel(channelId.toString());

      // Add to pending channel refreshes
      setPendingChannelRefreshes(prev => ({
        ...prev,
        [channelId.toString()]: task.id
      }));

      // Show notification
      toast({
        title: 'Channel Refresh Requested',
        description: 'The channel videos are being refreshed in the background. This may take a few minutes.',
      });

      // Trigger a refetch of tasks to ensure we have the latest task status
      setTimeout(() => {
        refetchTasks();
      }, 1000);

      // Return the task
      return task;
    } catch (error) {
      console.error('Error in refreshChannelVideosWithBackgroundTask:', error);

      // Show error notification
      toast({
        title: 'Channel Refresh Failed',
        description: 'Failed to start channel refresh. Falling back to direct refresh.',
        variant: 'destructive',
      });

      // Fall back to the original method
      return refreshChannelVideos.mutateAsync(channelId);
    }
  };

  // Function to analyze financial benefits for a video
  const analyzeFinancialBenefits = async (videoId: string) => {
    try {
      // Create a background task
      const task = await analyzeFinancial(videoId);

      // Show notification
      toast({
        title: 'Financial Analysis Requested',
        description: 'The financial analysis is being performed in the background. You will be notified when it is ready.',
      });

      // Return the task
      return task;
    } catch (error) {
      console.error('Error in analyzeFinancialBenefits:', error);

      // Show error notification
      toast({
        title: 'Financial Analysis Failed',
        description: 'Failed to start financial analysis. Please try again later.',
        variant: 'destructive',
      });

      throw error;
    }
  };

  return {
    getVideoTranscription: getVideoTranscriptionWithBackgroundTask,
    refreshChannelVideos: refreshChannelVideosWithBackgroundTask,
    analyzeFinancialBenefits,
    pendingTranscriptions,
    pendingChannelRefreshes
  };
}
