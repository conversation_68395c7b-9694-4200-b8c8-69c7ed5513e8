const Database = require('better-sqlite3');

/**
 * Migration to add a video_metrics table to store metrics data for the Data Analysis page
 */
async function runMigration() {
  try {
    // Connect to the database
    const db = new Database('data.db');
    
    // Check if the table already exists
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='video_metrics'").all();
    if (tables.length > 0) {
      console.log('video_metrics table already exists');
      return;
    }
    
    // Create the video_metrics table
    db.exec(`
      CREATE TABLE IF NOT EXISTS video_metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        timestamp INTEGER NOT NULL,
        new_videos_count INTEGER NOT NULL,
        trending_videos_count INTEGER NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      );
      
      -- Create an index on user_id and timestamp for faster queries
      CREATE INDEX IF NOT EXISTS idx_video_metrics_user_timestamp 
      ON video_metrics(user_id, timestamp);
    `);
    
    console.log('Successfully created video_metrics table');
    
    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
}

// Run the migration
runMigration();
