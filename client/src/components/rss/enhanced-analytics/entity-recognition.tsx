import React, { useMemo, useState } from 'react';
import { RssFeedItem } from '@shared/schema';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { highlightFinancialTerms } from '../financial-term-highlighter';
import { KeywordDetailDialog } from './keyword-detail-dialog';

interface EntityRecognitionProps {
  feedItems: RssFeedItem[];
  onOpenArticle?: (articleId: number) => void;
  onSaveArticle?: (articleId: number) => void;
  onOpenInBrowser?: (articleId: number) => void;
}

// Simple entity types
type EntityType = 'person' | 'organization' | 'location' | 'date' | 'money';

interface Entity {
  text: string;
  type: EntityType;
  count: number;
  articles: number[];
}

// Common titles to help identify people
const PERSON_TITLES = [
  'mr', 'mrs', 'ms', 'miss', 'dr', 'prof', 'president', 'senator', 'rep', 'representative',
  'secretary', 'director', 'ceo', 'chairman', 'chairwoman', 'spokesperson', 'governor', 'mayor'
];

// Common organization indicators
const ORG_INDICATORS = [
  'inc', 'corp', 'corporation', 'company', 'co', 'ltd', 'llc', 'association', 'institute',
  'university', 'college', 'school', 'department', 'agency', 'committee', 'commission',
  'foundation', 'fund', 'bank', 'group', 'administration'
];

// Money patterns
const MONEY_PATTERN = /\$\d+(?:[,.]\d+)*(?:\s*(?:million|billion|trillion))?|\d+\s*(?:dollars|usd|cents)/gi;

// Date patterns (simplified)
const DATE_PATTERN = /(?:jan(?:uary)?|feb(?:ruary)?|mar(?:ch)?|apr(?:il)?|may|jun(?:e)?|jul(?:y)?|aug(?:ust)?|sep(?:tember)?|oct(?:ober)?|nov(?:ember)?|dec(?:ember)?)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4}|\d{1,2}\/\d{1,2}\/\d{2,4}/gi;

export function EntityRecognition({
  feedItems,
  onOpenArticle,
  onSaveArticle,
  onOpenInBrowser
}: EntityRecognitionProps) {
  // State for the keyword detail dialog
  const [selectedEntity, setSelectedEntity] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Handle entity click
  const handleEntityClick = (entity: string) => {
    setSelectedEntity(entity);
    setIsDialogOpen(true);
  };

  // Extract entities from feed items
  const entities = useMemo(() => {
    if (!feedItems || feedItems.length === 0) return {
      persons: [],
      organizations: [],
      locations: [],
      dates: [],
      money: []
    };

    const personMap = new Map<string, Entity>();
    const orgMap = new Map<string, Entity>();
    const locationMap = new Map<string, Entity>();
    const dateMap = new Map<string, Entity>();
    const moneyMap = new Map<string, Entity>();

    // Process each feed item
    feedItems.forEach(item => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      // Combine all text content
      const allText = `${title} ${content} ${scrapedContent}`;

      // First, strip HTML tags and formatting elements
      const strippedText = allText
        .replace(/<[^>]*>/g, ' ') // Remove HTML tags
        .replace(/&[a-z]+;/g, ' ') // Remove HTML entities like &nbsp;
        .replace(/style="[^"]*"/g, ' ') // Remove style attributes
        .replace(/class="[^"]*"/g, ' ') // Remove class attributes
        .replace(/data-[a-z-]+="[^"]*"/g, ' '); // Remove data attributes

      // Extract money mentions
      const moneyMatches = strippedText.match(MONEY_PATTERN) || [];
      moneyMatches.forEach(match => {
        const normalized = match.toLowerCase().trim();
        if (!moneyMap.has(normalized)) {
          moneyMap.set(normalized, {
            text: normalized,
            type: 'money',
            count: 0,
            articles: []
          });
        }

        const entity = moneyMap.get(normalized)!;
        entity.count += 1;
        if (!entity.articles.includes(item.id)) {
          entity.articles.push(item.id);
        }
      });

      // Extract date mentions
      const dateMatches = strippedText.match(DATE_PATTERN) || [];
      dateMatches.forEach(match => {
        const normalized = match.toLowerCase().trim();
        if (!dateMap.has(normalized)) {
          dateMap.set(normalized, {
            text: normalized,
            type: 'date',
            count: 0,
            articles: []
          });
        }

        const entity = dateMap.get(normalized)!;
        entity.count += 1;
        if (!entity.articles.includes(item.id)) {
          entity.articles.push(item.id);
        }
      });

      // Simple NER for persons and organizations using capitalization patterns
      // This is a simplified approach - a real NER would use ML models
      const words = strippedText.split(/\s+/);

      for (let i = 0; i < words.length; i++) {
        const word = words[i];
        const nextWord = i < words.length - 1 ? words[i + 1] : '';
        const prevWord = i > 0 ? words[i - 1] : '';

        // Check for person names (capitalized words with titles)
        if (word.length > 1 && word[0] === word[0].toUpperCase() && word[0] !== word[0].toLowerCase()) {
          // Check if preceded by a title
          const prevWordLower = prevWord.toLowerCase().replace(/[.,;:]/g, '');

          if (PERSON_TITLES.includes(prevWordLower)) {
            const personName = `${prevWord} ${word}`;
            const normalized = personName.toLowerCase();

            if (!personMap.has(normalized)) {
              personMap.set(normalized, {
                text: personName,
                type: 'person',
                count: 0,
                articles: []
              });
            }

            const entity = personMap.get(normalized)!;
            entity.count += 1;
            if (!entity.articles.includes(item.id)) {
              entity.articles.push(item.id);
            }
          }
        }

        // Check for organization names (capitalized words with org indicators)
        if (word.length > 1 && word[0] === word[0].toUpperCase() && word[0] !== word[0].toLowerCase()) {
          // Check if followed by an org indicator
          const nextWordLower = nextWord.toLowerCase().replace(/[.,;:]/g, '');

          if (ORG_INDICATORS.includes(nextWordLower)) {
            const orgName = `${word} ${nextWord}`;
            const normalized = orgName.toLowerCase();

            if (!orgMap.has(normalized)) {
              orgMap.set(normalized, {
                text: orgName,
                type: 'organization',
                count: 0,
                articles: []
              });
            }

            const entity = orgMap.get(normalized)!;
            entity.count += 1;
            if (!entity.articles.includes(item.id)) {
              entity.articles.push(item.id);
            }
          }
        }
      }
    });

    // Convert maps to arrays and sort by count
    return {
      persons: Array.from(personMap.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 20),
      organizations: Array.from(orgMap.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 20),
      locations: Array.from(locationMap.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 20),
      dates: Array.from(dateMap.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 20),
      money: Array.from(moneyMap.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 20)
    };
  }, [feedItems]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-white">Entity Recognition</CardTitle>
        <CardDescription className="text-white">
          People, organizations, dates, and monetary values mentioned in articles
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="money">
          <TabsList className="mb-4">
            <TabsTrigger value="money">Money</TabsTrigger>
            <TabsTrigger value="dates">Dates</TabsTrigger>
            <TabsTrigger value="persons">People</TabsTrigger>
            <TabsTrigger value="organizations">Organizations</TabsTrigger>
          </TabsList>

          <TabsContent value="money">
            <ScrollArea className="h-[300px]">
              {entities.money.length === 0 ? (
                <div className="flex justify-center items-center h-[100px]">
                  <p className="text-white text-lg">No monetary values found</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {entities.money.map((entity, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center p-2 border-b border-border hover:bg-primary/10 cursor-pointer"
                      onClick={() => handleEntityClick(entity.text)}
                    >
                      <span className="text-white font-medium">{entity.text}</span>
                      <div className="flex gap-2">
                        <Badge variant="outline" className="bg-primary/20">
                          {entity.count} mentions
                        </Badge>
                        <Badge variant="outline" className="bg-primary/20">
                          {entity.articles.length} articles
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="dates">
            <ScrollArea className="h-[300px]">
              {entities.dates.length === 0 ? (
                <div className="flex justify-center items-center h-[100px]">
                  <p className="text-white text-lg">No dates found</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {entities.dates.map((entity, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center p-2 border-b border-border hover:bg-primary/10 cursor-pointer"
                      onClick={() => handleEntityClick(entity.text)}
                    >
                      <span className="text-white font-medium">{entity.text}</span>
                      <div className="flex gap-2">
                        <Badge variant="outline" className="bg-primary/20">
                          {entity.count} mentions
                        </Badge>
                        <Badge variant="outline" className="bg-primary/20">
                          {entity.articles.length} articles
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="persons">
            <ScrollArea className="h-[300px]">
              {entities.persons.length === 0 ? (
                <div className="flex justify-center items-center h-[100px]">
                  <p className="text-white text-lg">No people found</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {entities.persons.map((entity, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center p-2 border-b border-border hover:bg-primary/10 cursor-pointer"
                      onClick={() => handleEntityClick(entity.text)}
                    >
                      <span className="text-white font-medium">{entity.text}</span>
                      <div className="flex gap-2">
                        <Badge variant="outline" className="bg-primary/20">
                          {entity.count} mentions
                        </Badge>
                        <Badge variant="outline" className="bg-primary/20">
                          {entity.articles.length} articles
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="organizations">
            <ScrollArea className="h-[300px]">
              {entities.organizations.length === 0 ? (
                <div className="flex justify-center items-center h-[100px]">
                  <p className="text-white text-lg">No organizations found</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {entities.organizations.map((entity, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center p-2 border-b border-border hover:bg-primary/10 cursor-pointer"
                      onClick={() => handleEntityClick(entity.text)}
                    >
                      <span className="text-white font-medium">{entity.text}</span>
                      <div className="flex gap-2">
                        <Badge variant="outline" className="bg-primary/20">
                          {entity.count} mentions
                        </Badge>
                        <Badge variant="outline" className="bg-primary/20">
                          {entity.articles.length} articles
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Entity Detail Dialog */}
      {selectedEntity && (
        <KeywordDetailDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          keyword={selectedEntity}
          feedItems={feedItems}
          type="word"
          onOpenArticle={onOpenArticle}
          onSaveArticle={onSaveArticle}
          onOpenInBrowser={onOpenInBrowser}
        />
      )}
    </Card>
  );
}
