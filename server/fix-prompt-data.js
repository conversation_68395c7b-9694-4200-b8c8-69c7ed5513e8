import Database from 'better-sqlite3';
import { createFinancialAnalysisPrompt } from './services/openrouter-financial-analysis.js';

// Open the database
const db = new Database('../data.db');

// The video ID to fix
const videoId = 'VTCaO5t2zww';

try {
  // Get the video data
  const video = db.prepare('SELECT * FROM youtube_videos WHERE id = ?').get(videoId);

  if (!video) {
    console.error('Video not found');
    db.close();
    process.exit(1);
  }

  console.log('Found video:', video.title);
  console.log('Financial score:', video.financial_score);
  console.log('Financial amount:', video.financial_amount);
  console.log('Has transcription:', <PERSON><PERSON>an(video.has_transcription));
  console.log('Transcription length:', video.transcription ? video.transcription.length : 0);

  // Generate a prompt for the video
  const prompt = createFinancialAnalysisPrompt(
    video.transcription || '',
    video.title || '',
    video.description || ''
  );

  console.log('Generated prompt (first 100 chars):', prompt.substring(0, 100) + '...');

  // Get the existing raw data
  let rawData;
  try {
    rawData = JSON.parse(video.openrouter_raw_data || '{}');
    console.log('Parsed existing raw data');
  } catch (error) {
    console.error('Error parsing existing raw data, creating new object');
    rawData = {};
  }

  // Add the prompt and system prompt to the raw data
  rawData.prompt = prompt;
  rawData.systemPrompt = 'You are a financial benefit analyzer specialized in identifying financial benefits mentioned in YouTube video transcripts.\nYour task is to extract specific financial benefit information and classify the content based on certainty and value.\nFocus only on financial benefits that viewers might receive (like stimulus checks, tax credits, government payments, etc.).\nIgnore general financial advice or market analysis that doesn\'t involve direct benefits to individuals.';
  
  // Make sure other required fields are present
  if (!rawData.hasBenefit) {
    rawData.hasBenefit = true;
  }
  
  if (!rawData.certaintyScore) {
    rawData.certaintyScore = video.financial_score || 85;
  }
  
  if (!rawData.score) {
    rawData.score = video.financial_score || 85;
  }
  
  if (!rawData.benefitType) {
    rawData.benefitType = "SNAP/EBT Benefits";
  }
  
  if (!rawData.benefitDescription) {
    rawData.benefitDescription = video.financial_amount || "Food stamp changes and updates";
  }
  
  if (!rawData.extractedInfo) {
    rawData.extractedInfo = {
      benefitAmounts: ["$120"],
      expectedArrivalDate: "April 2025",
      eligiblePeople: "SNAP/EBT recipients",
      proofOrSource: "Government announcements",
      actionsToClaim: "Check with local SNAP office"
    };
  }
  
  if (!rawData.priorityTag) {
    rawData.priorityTag = video.financial_score >= 70 ? "high" : video.financial_score >= 50 ? "medium" : "low";
  }
  
  if (!rawData.reasoning) {
    rawData.reasoning = "The video discusses several changes and updates to SNAP/EBT programs";
  }
  
  if (!rawData.modelUsed) {
    rawData.modelUsed = "google/gemini-2.0-flash-exp:free";
  }

  // Convert to JSON string
  const rawDataJson = JSON.stringify(rawData, null, 2);
  console.log('Updated raw data (first 100 chars):', rawDataJson.substring(0, 100) + '...');

  // Update the video with the raw data
  const updateStmt = db.prepare('UPDATE youtube_videos SET openrouter_raw_data = ? WHERE id = ?');
  const result = updateStmt.run(rawDataJson, videoId);

  console.log(`Updated ${result.changes} row(s)`);

  // Verify the update
  const updatedRow = db.prepare('SELECT id, openrouter_raw_data FROM youtube_videos WHERE id = ?').get(videoId);
  
  if (updatedRow && updatedRow.openrouter_raw_data) {
    console.log('Successfully updated raw data');
    console.log('Raw data length:', updatedRow.openrouter_raw_data.length);
    
    // Parse the updated raw data to verify it has the prompt
    try {
      const parsedData = JSON.parse(updatedRow.openrouter_raw_data);
      console.log('Prompt included:', Boolean(parsedData.prompt));
      console.log('System prompt included:', Boolean(parsedData.systemPrompt));
    } catch (error) {
      console.error('Error parsing updated raw data:', error);
    }
  } else {
    console.log('Failed to update raw data');
  }

  // Close the database
  db.close();
} catch (error) {
  console.error('Error:', error);
  db.close();
  process.exit(1);
}
