import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { 
  Search, 
  Download, 
  ExternalLink,
  Clock,
  Database,
  Youtube,
  Rss,
  Activity,
  Import
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';

interface ExistingTranscriptsViewProps {
  existingTranscripts: any[];
  onTranscriptImported: (transcript: any) => void;
  onRefresh: () => void;
}

const SOURCE_ICONS = {
  youtube: Youtube,
  ytr: Activity,
  rss: Rss,
  realtime: Activity,
  feed: Database
};

const SOURCE_LABELS = {
  youtube: 'YouTube',
  ytr: 'YTR',
  rss: 'RSS',
  realtime: 'Realtime',
  feed: 'Video Feed'
};

export function ExistingTranscriptsView({ 
  existingTranscripts, 
  onTranscriptImported, 
  onRefresh 
}: ExistingTranscriptsViewProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSource, setSelectedSource] = useState<string>('all');
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [transcriptToImport, setTranscriptToImport] = useState<any>(null);
  const [customTitle, setCustomTitle] = useState('');
  const [isImporting, setIsImporting] = useState(false);
  
  const { toast } = useToast();

  // Filter transcripts based on search and source
  const filteredTranscripts = existingTranscripts.filter(transcript => {
    const matchesSearch = !searchQuery || 
      transcript.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (transcript.content && transcript.content.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesSource = selectedSource === 'all' || transcript.sourceType === selectedSource;
    
    return matchesSearch && matchesSource;
  });

  // Group transcripts by source
  const groupedTranscripts = filteredTranscripts.reduce((acc, transcript) => {
    const source = transcript.sourceType || 'unknown';
    if (!acc[source]) {
      acc[source] = [];
    }
    acc[source].push(transcript);
    return acc;
  }, {} as Record<string, any[]>);

  const handleImport = async () => {
    if (!transcriptToImport) return;

    try {
      setIsImporting(true);
      
      const response = await fetch('/api/txt-tab/import-transcript', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sourceType: transcriptToImport.sourceType,
          sourceId: transcriptToImport.id,
          title: customTitle.trim() || transcriptToImport.title
        })
      });

      if (response.ok) {
        const importedTranscript = await response.json();
        onTranscriptImported(importedTranscript);
        setImportDialogOpen(false);
        setTranscriptToImport(null);
        setCustomTitle('');
        toast({
          title: "Success",
          description: "Transcript imported successfully",
        });
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to import transcript');
      }
    } catch (error) {
      console.error('Error importing transcript:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to import transcript",
        variant: "destructive",
      });
    } finally {
      setIsImporting(false);
    }
  };

  const openImportDialog = (transcript: any) => {
    setTranscriptToImport(transcript);
    setCustomTitle(transcript.title);
    setImportDialogOpen(true);
  };

  const getSourceStats = () => {
    const stats = existingTranscripts.reduce((acc, transcript) => {
      const source = transcript.sourceType || 'unknown';
      acc[source] = (acc[source] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return stats;
  };

  const sourceStats = getSourceStats();

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Existing Transcripts</h3>
          <p className="text-sm text-muted-foreground">
            Import transcripts from other tabs to use in your script writing
          </p>
        </div>
        <Button variant="outline" onClick={onRefresh}>
          <Database className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats */}
      <div className="flex flex-wrap gap-2">
        <Badge variant="outline">
          Total: {existingTranscripts.length}
        </Badge>
        {Object.entries(sourceStats).map(([source, count]) => {
          const Icon = SOURCE_ICONS[source as keyof typeof SOURCE_ICONS] || Database;
          return (
            <Badge key={source} variant="secondary">
              <Icon className="h-3 w-3 mr-1" />
              {SOURCE_LABELS[source as keyof typeof SOURCE_LABELS] || source}: {count}
            </Badge>
          );
        })}
      </div>

      {/* Search and Filter */}
      <div className="flex gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search transcripts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <select
          value={selectedSource}
          onChange={(e) => setSelectedSource(e.target.value)}
          className="px-3 py-2 border rounded-md bg-background"
        >
          <option value="all">All Sources</option>
          {Object.keys(sourceStats).map(source => (
            <option key={source} value={source}>
              {SOURCE_LABELS[source as keyof typeof SOURCE_LABELS] || source}
            </option>
          ))}
        </select>
      </div>

      {/* Transcripts List */}
      <ScrollArea className="h-[500px]">
        <div className="space-y-4">
          {Object.entries(groupedTranscripts).map(([source, transcripts]) => {
            const Icon = SOURCE_ICONS[source as keyof typeof SOURCE_ICONS] || Database;
            const label = SOURCE_LABELS[source as keyof typeof SOURCE_LABELS] || source;
            
            return (
              <div key={source}>
                <div className="flex items-center gap-2 mb-2">
                  <Icon className="h-4 w-4" />
                  <h4 className="font-medium">{label}</h4>
                  <Badge variant="outline" className="text-xs">
                    {transcripts.length}
                  </Badge>
                </div>
                
                <div className="grid gap-3">
                  {transcripts.map((transcript) => (
                    <Card key={`${source}-${transcript.id}`} className="hover:bg-muted/50 transition-colors">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1 min-w-0">
                            <h5 className="font-medium text-sm mb-1 truncate">
                              {transcript.title}
                            </h5>
                            
                            {transcript.content && (
                              <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                                {transcript.content.substring(0, 150)}
                                {transcript.content.length > 150 ? '...' : ''}
                              </p>
                            )}
                            
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              {transcript.publishedAt && (
                                <div className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {new Date(transcript.publishedAt).toLocaleDateString()}
                                </div>
                              )}
                              
                              {transcript.video_url && (
                                <a 
                                  href={transcript.video_url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center gap-1 text-blue-500 hover:underline"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <ExternalLink className="h-3 w-3" />
                                  Video
                                </a>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2 ml-4">
                            <Badge variant="outline" className="text-xs">
                              {label}
                            </Badge>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openImportDialog(transcript)}
                            >
                              <Import className="h-3 w-3 mr-1" />
                              Import
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            );
          })}
          
          {filteredTranscripts.length === 0 && (
            <div className="text-center py-12 text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No transcripts found</p>
              {searchQuery && (
                <p className="text-sm mt-2">Try adjusting your search or filter</p>
              )}
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Import Dialog */}
      <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Transcript</DialogTitle>
            <DialogDescription>
              Import this transcript to your TXT tab for script writing and editing.
            </DialogDescription>
          </DialogHeader>
          
          {transcriptToImport && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="import-title">Title</Label>
                <Input
                  id="import-title"
                  value={customTitle}
                  onChange={(e) => setCustomTitle(e.target.value)}
                  placeholder="Enter custom title..."
                />
              </div>
              
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm font-medium mb-1">Source</p>
                <div className="flex items-center gap-2">
                  {(() => {
                    const Icon = SOURCE_ICONS[transcriptToImport.sourceType as keyof typeof SOURCE_ICONS] || Database;
                    return <Icon className="h-4 w-4" />;
                  })()}
                  <span className="text-sm">
                    {SOURCE_LABELS[transcriptToImport.sourceType as keyof typeof SOURCE_LABELS] || transcriptToImport.sourceType}
                  </span>
                </div>
                
                {transcriptToImport.content && (
                  <div className="mt-2">
                    <p className="text-sm font-medium mb-1">Preview</p>
                    <p className="text-xs text-muted-foreground">
                      {transcriptToImport.content.substring(0, 200)}
                      {transcriptToImport.content.length > 200 ? '...' : ''}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setImportDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleImport} disabled={isImporting || !customTitle.trim()}>
              {isImporting ? 'Importing...' : 'Import'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
