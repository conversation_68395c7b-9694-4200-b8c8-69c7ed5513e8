/**
 * Ollama Financial Analysis Service
 *
 * This service uses a locally running Ollama LLM model to analyze YouTube video transcripts
 * for financial benefits and related content.
 */

import { generateOllamaJsonResponse, getUserSelectedOllamaModel } from './ollama-service';
import { storage } from '../storage';

// Interface for extracted financial benefit information
export interface ExtractedFinancialBenefitInfo {
  benefitAmounts: string[];
  expectedArrivalDate: string;
  eligiblePeople: string;
  proofOrSource: string;
  actionsToClaim: string;
}

// Interface for scoring and classification
export interface FinancialScoreClassification {
  score: number;
  priorityTag: 'Urgent - High Certainty' | 'Anticipated' | 'Doubtful' | 'Low Benefit';
  reasonForPriority: string;
  viralPotential: string;
}

// Combined interface for the complete financial analysis
export interface OllamaFinancialAnalysis {
  benefitInfo: ExtractedFinancialBenefitInfo;
  classification: FinancialScoreClassification;
}

// System prompt for financial analysis
const FINANCIAL_ANALYSIS_SYSTEM_PROMPT = `
You are a financial benefit analyzer specialized in identifying financial benefits mentioned in YouTube video transcripts.
Your task is to extract specific financial benefit information and classify the content based on certainty and value.
Focus ONLY on financial benefits that viewers might receive (like stimulus checks, tax credits, government payments, Social Security benefits, etc.).
Ignore general financial advice or market analysis that doesn't involve direct benefits to individuals.

Be thorough in your analysis and use your unique capabilities to provide insightful evaluations.
Don't be afraid to assign low scores to dubious claims or high scores to well-supported benefits.
Use your judgment to determine the credibility of the information presented.
`;

/**
 * Analyze a YouTube video transcript for financial benefits using Ollama LLM
 * @param userId User ID for getting the selected model
 * @param transcript The video transcript
 * @param title The video title
 * @param description The video description (optional)
 * @returns Financial analysis results
 */
export async function analyzeTranscriptWithOllama(
  userId: number,
  transcript: string,
  title: string,
  description?: string,
  customPrompt?: string,
  modelOverride?: string
): Promise<OllamaFinancialAnalysis & { modelUsed: string, rawData?: string }> {
  try {
    // Use the model override if provided, otherwise get the user's selected model
    let model;
    if (modelOverride) {
      model = modelOverride;
      console.log(`🤖 Ollama API: Using model override: ${model}`);
    } else {
      // Get the user's selected Ollama model
      model = await getUserSelectedOllamaModel(userId);
      console.log(`🤖 Ollama API: Using user's selected model: ${model}`);
    }

    // Ensure the model has the correct format
    if (!model.includes(':')) {
      model = `${model}:latest`;
      console.log(`🤖 Ollama API: Fixed model name format to: ${model}`);
    }

    // Make sure we have a valid model name
    if (!model || model === 'Unknown') {
      model = 'llama3.2:latest';
      console.log(`🤖 Ollama API: Using default model: ${model}`);
    }

    console.log(`🤖 Ollama API: Using model: ${model} for analysis of video with title: ${title}`);
    console.log(`🤖 Ollama API: Transcript length: ${transcript.length} characters`);

    // Check if Ollama is running by making a simple request
    try {
      console.log('🤖 Ollama API: Checking if Ollama server is running...');
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      try {
        const response = await fetch('http://localhost:11434/api/tags', {
          signal: controller.signal
        });

        // Clear the timeout
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const modelNames = data.models.map((m: any) => m.name);
        console.log(`🤖 Ollama API: Server is running with ${modelNames.length} models available: ${modelNames.join(', ')}`);

        // Check if the requested model is available
        if (!modelNames.includes(model)) {
          console.error(`🤖 Ollama API: Requested model '${model}' is not available in Ollama`);
          console.log(`🤖 Ollama API: Available models: ${modelNames.join(', ')}`);
          throw new Error(`Model '${model}' is not available in Ollama. Available models: ${modelNames.join(', ')}`);
        }
      } catch (fetchError) {
        // Clear the timeout in case of error
        clearTimeout(timeoutId);
        throw fetchError;
      }
    } catch (connectionError) {
      console.error('🤖 Ollama API: Error connecting to Ollama server:', connectionError);
      throw new Error('Ollama server is not running or not accessible. Please check if Ollama is running and try again.');
    }

    // Get user settings to check for custom prompt
    const userSettings = await storage.getSettings(userId);

    // Check if user has a selected prompt
    let userCustomPrompt = userSettings?.ollamaAnalysisPrompt || customPrompt;
    let promptName = "Default Prompt";

    // If user has a selected prompt ID, use that prompt instead
    if (userSettings?.selectedPromptId) {
      try {
        const selectedPrompt = await storage.getOllamaPrompt(userSettings.selectedPromptId);
        if (selectedPrompt) {
          console.log(`🤖 Ollama API: Using selected prompt: ${selectedPrompt.name}`);
          userCustomPrompt = selectedPrompt.promptText;
          promptName = selectedPrompt.name;
        }
      } catch (error) {
        console.error('🤖 Ollama API: Error getting selected prompt:', error);
      }
    }

    // Create the analysis prompt
    const prompt = createFinancialAnalysisPrompt(transcript, title, description, userCustomPrompt);
    console.log(`🤖 Ollama API: Created analysis prompt (length: ${prompt.length} characters)`);

    // Generate the analysis using Ollama
    console.log(`🤖 Ollama API: Sending request to Ollama model: ${model}`);
    console.log(`🤖 Ollama API: System prompt length: ${FINANCIAL_ANALYSIS_SYSTEM_PROMPT.length} characters`);

    // Add a timeout for the analysis
    let analysisPromise = generateOllamaJsonResponse<OllamaFinancialAnalysis>(
      model,
      prompt,
      FINANCIAL_ANALYSIS_SYSTEM_PROMPT
    );

    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Analysis timed out after 180 seconds with model ${model}`));
      }, 180000); // 3 minute timeout (increased from 2 minutes)
    });

    // Race the analysis against the timeout
    console.log(`🤖 Ollama API: Waiting for analysis with timeout of 180 seconds...`);
    const analysis = await Promise.race([analysisPromise, timeoutPromise]) as OllamaFinancialAnalysis;

    console.log(`🤖 Ollama API: Analysis completed successfully with model: ${model}`);

    // Validate the analysis structure
    if (!analysis) {
      console.error('🤖 Ollama API: Null or undefined analysis returned from Ollama');
      throw new Error('Null or undefined analysis returned from Ollama');
    }

    // Handle different response formats
    // Check if we got the new format with financial_benefits array
    if (analysis.financial_benefits && Array.isArray(analysis.financial_benefits)) {
      console.log('🤖 Ollama API: Received response in new format with financial_benefits array');

      try {
        // Extract all benefit amounts from the financial_benefits array
        let benefitAmounts = [];

        // Safely extract amounts from the financial_benefits array
        if (Array.isArray(analysis.financial_benefits)) {
          // First, try to extract amounts directly
          benefitAmounts = analysis.financial_benefits
            .map(benefit => benefit && typeof benefit === 'object' ? benefit.amount : null)
            .filter(amount => amount && typeof amount === 'string');

          // If no amounts found, try to parse them from the amounts as strings
          if (benefitAmounts.length === 0) {
            // Look for dollar amounts in each benefit object
            analysis.financial_benefits.forEach(benefit => {
              if (benefit && typeof benefit === 'object') {
                // Check all string properties for dollar amounts
                Object.values(benefit).forEach(value => {
                  if (typeof value === 'string') {
                    // Extract dollar amounts using regex
                    const matches = value.match(/\$\d+(?:,\d+)*(?:\.\d+)?/g);
                    if (matches) {
                      benefitAmounts.push(...matches);
                    }
                  }
                });
              }
            });
          }
        }

        console.log('🤖 Ollama API: Extracted benefit amounts:', benefitAmounts);

        // Create a compatible analysis object - use a new variable instead of reassigning the constant
        const convertedAnalysis = {
          benefitInfo: {
            benefitAmounts: benefitAmounts,
            expectedArrivalDate: analysis.financial_benefits[0]?.expected_arrival || '',
            eligiblePeople: analysis.financial_benefits[0]?.eligibility || '',
            proofOrSource: analysis.financial_benefits[0]?.proof_source || '',
            actionsToClaim: analysis.financial_benefits[0]?.how_to_apply || ''
          },
          classification: {
            // Set a default score based on classification if credibility_score is 0 or missing
            score: analysis.credibility_score > 0 ? analysis.credibility_score :
                   analysis.classification === 'Urgent_High_Benefit' ? 85 :
                   analysis.classification === 'Anticipated_Benefit' ? 65 :
                   analysis.classification === 'Doubtful_Benefit' ? 30 : 20,
            priorityTag: analysis.classification === 'Urgent_High_Benefit' ? 'Urgent - High Certainty' :
                         analysis.classification === 'Anticipated_Benefit' ? 'Anticipated' :
                         'Doubtful',
            reasonForPriority: analysis.reason_for_doubt || '',
            viralPotential: analysis.virality_reason || 'Low'
          }
        };

        console.log(`🤖 Ollama API: Set score to ${convertedAnalysis.classification.score} based on classification: ${convertedAnalysis.classification.priorityTag}`);

        console.log('🤖 Ollama API: Converted response to expected format');

        // Return the converted analysis instead of continuing with the original function
        return {
          ...convertedAnalysis,
          modelUsed: model,
          promptName: promptName,
          prompt: prompt,
          rawData: JSON.stringify({
            ...convertedAnalysis,
            modelUsed: model,
            promptName: promptName,
            prompt: prompt
          })
        };
      } catch (conversionError) {
        console.error('🤖 Ollama API: Error converting financial_benefits format:', conversionError);
        // Continue with the original analysis format
        console.log('🤖 Ollama API: Falling back to standard analysis format');
      }
    }

    // Now validate the converted or original analysis
    if (!analysis.benefitInfo) {
      console.error('🤖 Ollama API: Analysis missing benefitInfo:', analysis);
      throw new Error('Analysis missing benefitInfo field');
    }

    if (!analysis.classification) {
      console.error('🤖 Ollama API: Analysis missing classification:', analysis);
      throw new Error('Analysis missing classification field');
    }

    // Add the model used and prompt name to the analysis result
    console.log(`🤖 Ollama API: Analysis complete using model: ${model} with score ${analysis.classification.score}/100`);
    console.log(`🤖 Ollama API: Analysis used prompt: ${promptName}`);

    // Ensure model name has the correct format
    if (!model.includes(':')) {
      model = `${model}:latest`;
      console.log(`🤖 Ollama API: Added version tag to model name: ${model}`);
    }

    // Create a complete analysis object with all fields
    const completeAnalysis = {
      ...analysis,
      modelUsed: model, // Always use the actual model name from settings with proper format
      promptName: promptName,
      prompt: prompt
    };

    console.log(`🤖 Ollama API: Setting model name in analysis to: ${model}`);

    // Store the complete analysis as raw data for later reference
    const rawAnalysis = JSON.stringify(completeAnalysis);
    console.log(`🤖 Ollama API: Storing raw analysis data (${rawAnalysis.length} bytes)`);
    console.log(`🤖 Ollama API: Benefit amounts in raw data:`, completeAnalysis.benefitInfo.benefitAmounts);
    console.log(`🤖 Ollama API: Model used in raw data:`, model);

    // Verify the raw data has the correct model name
    try {
      const parsedRaw = JSON.parse(rawAnalysis);
      if (parsedRaw.modelUsed !== model) {
        console.error(`🤖 Ollama API: Model mismatch in raw data! Expected ${model}, got ${parsedRaw.modelUsed}`);
      } else {
        console.log(`🤖 Ollama API: Raw data model name verified: ${parsedRaw.modelUsed}`);
      }
    } catch (e) {
      console.error(`🤖 Ollama API: Error verifying raw data:`, e);
    }

    return {
      ...completeAnalysis,
      rawData: rawAnalysis
    };
  } catch (error) {
    console.error('🤖 Ollama API: Error analyzing transcript with Ollama:', error);
    // Try to get the model that was being used
    let modelName = 'llama3.2:latest'; // Default to a known model
    try {
      modelName = await getUserSelectedOllamaModel(userId);
    } catch (modelError) {
      console.error('🤖 Ollama API: Error getting model name for error report:', modelError);
    }

    // Get the error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`🤖 Ollama API: Analysis failed with model ${modelName}: ${errorMessage}`);

    // Get a default prompt name
    let promptName = 'Default Financial Analysis';

    // Try to get the prompt that was being used
    let promptText = '';
    try {
      // Create a sample prompt to show what would have been used
      promptText = createFinancialAnalysisPrompt(
        transcript.substring(0, 500) + '... (truncated for error report)',
        title,
        description?.substring(0, 100) + '... (truncated for error report)',
        customPrompt
      );
    } catch (promptError) {
      console.error('🤖 Ollama API: Error creating sample prompt for error report:', promptError);
    }

    // Ensure model name has the correct format
    if (!modelName.includes(':') && !modelName.includes('Unknown')) {
      modelName = `${modelName}:latest`;
    }

    // Create a default analysis with empty values
    const errorAnalysis = {
      benefitInfo: {
        benefitAmounts: [],
        expectedArrivalDate: '',
        eligiblePeople: '',
        proofOrSource: '',
        actionsToClaim: ''
      },
      classification: {
        score: 0,
        priorityTag: 'Doubtful',
        reasonForPriority: `Analysis failed: ${errorMessage}`,
        viralPotential: 'Low'
      },
      modelUsed: `${modelName}`,
      promptName: promptName,
      prompt: promptText
    };

    // Create raw data for the error case
    const rawData = JSON.stringify(errorAnalysis);
    console.log(`🤖 Ollama API: Created error raw data (${rawData.length} bytes)`);

    return {
      ...errorAnalysis,
      rawData: rawData
    };
  }
}

/**
 * Create a prompt for financial benefit analysis
 * @param transcript The video transcript
 * @param title The video title
 * @param description The video description (optional)
 * @returns The formatted prompt
 */
export function createFinancialAnalysisPrompt(
  transcript: string,
  title: string,
  description?: string,
  customPrompt?: string
): string {
  // If a custom prompt is provided, use it with the transcript and title
  if (customPrompt) {
    // Replace [Paste transcript here] with the actual transcript
    let prompt = customPrompt
      .replace('[Paste transcript here]', transcript.substring(0, 15000) + (transcript.length > 15000 ? '... (transcript truncated)' : ''))
      .replace('TITLE: ""', `TITLE: "${title}"`)
      .replace('"title": ""', `"title": "${title}"`)
      .replace('TITLE: "[title]"', `TITLE: "${title}"`)
      .replace('"title": "[title]"', `"title": "${title}"`);

    // Add description if available
    if (description) {
      prompt = prompt.replace('DESCRIPTION: ""', `DESCRIPTION: "${description}"`)
                    .replace('DESCRIPTION: "[description]"', `DESCRIPTION: "${description}"`);
    } else {
      // If no description, replace with empty or 'Not provided'
      prompt = prompt.replace('DESCRIPTION: "[description]"', 'DESCRIPTION: "Not provided"');
    }

    // Log the first 500 characters of the prompt for debugging
    console.log(`🤖 Ollama API: Prompt preview (first 500 chars): ${prompt.substring(0, 500)}...`);
    console.log(`🤖 Ollama API: Transcript placeholder replaced: ${!prompt.includes('[Paste transcript here]')}`);

    return prompt;
  }

  // Otherwise use the default prompt
  return `
Analyze the following YouTube video content for financial benefits:

TITLE: ${title}

${description ? `DESCRIPTION: ${description}\n\n` : ''}

TRANSCRIPT:
${transcript.substring(0, 15000)} ${transcript.length > 15000 ? '... (transcript truncated)' : ''}

Your task is to:
1. Extract any financial benefit information mentioned in the content
2. Score and classify the content based on certainty and value

Please provide your analysis in the following JSON format:

{
  "benefitInfo": {
    "benefitAmounts": ["$1200", "$600", etc.],
    "expectedArrivalDate": "April 25, 2025",
    "eligiblePeople": "Low-income families, senior citizens",
    "proofOrSource": "CARES Act 2.0, IRS Announcement",
    "actionsToClaim": "File taxes before May 1st, Apply via IRS portal"
  },
  "classification": {
    "score": 87,
    "priorityTag": "Urgent - High Certainty",
    "reasonForPriority": "Clear mention of CARES Act, $1200, and IRS portal with deadline",
    "viralPotential": "High benefit and urgent deadline create viral appeal"
  }
}

Scoring Rules:
- "Urgent - High Certainty" (80-100): Clear date, amount, eligible group, and valid proof (govt source, program)
- "Anticipated" (50-79): Talk of upcoming benefits, not finalized yet, but possibly valid
- "Doubtful" (20-49): Claims unrealistic or have no proof, vague terms like "could happen soon"
- "Low Benefit" (0-19): Small amount or hard-to-access, low relevance to general users

If no financial benefits are mentioned, return empty strings and a score of 0.
`;
}

/**
 * Convert Ollama analysis to the application's financial analysis format
 * @param ollamaAnalysis The analysis from Ollama
 * @returns Formatted financial analysis for the application
 */
export function convertOllamaAnalysisToAppFormat(ollamaAnalysis: OllamaFinancialAnalysis & { modelUsed?: string, promptName?: string, prompt?: string, rawData?: string }) {
  // Log the model used to help with debugging
  console.log(`🤖 Ollama API: Converting analysis from model: ${ollamaAnalysis.modelUsed || 'Unknown'}`);

  // Log benefit amounts to help with debugging
  console.log(`🤖 Ollama API: Benefit amounts in analysis:`, ollamaAnalysis.benefitInfo?.benefitAmounts || []);

  // Map priority tag to financial category
  const categoryMap = {
    'Urgent - High Certainty': 'urgent',
    'Anticipated': 'anticipated',
    'Doubtful': 'doubtful',
    'Low Benefit': 'low'
  };

  const category = categoryMap[ollamaAnalysis.classification.priorityTag] || 'doubtful';

  // Normalize benefitAmounts to ensure it's always an array
  if (!ollamaAnalysis.benefitInfo.benefitAmounts) {
    ollamaAnalysis.benefitInfo.benefitAmounts = [];
  } else if (typeof ollamaAnalysis.benefitInfo.benefitAmounts === 'string') {
    // If it's a string, convert it to an array with that string as the only element
    ollamaAnalysis.benefitInfo.benefitAmounts = [ollamaAnalysis.benefitInfo.benefitAmounts];
  } else if (!Array.isArray(ollamaAnalysis.benefitInfo.benefitAmounts)) {
    // If it's not an array or string, convert to empty array
    ollamaAnalysis.benefitInfo.benefitAmounts = [];
  }

  // Get the primary amount (first in the list, if any)
  const primaryAmount = ollamaAnalysis.benefitInfo.benefitAmounts.length > 0
    ? ollamaAnalysis.benefitInfo.benefitAmounts[0]
    : '';

  // Create a formatted analysis text
  const analysisText = `
Benefits Analysis:

💰 Benefit: ${primaryAmount}
${ollamaAnalysis.benefitInfo.benefitAmounts.length > 1 ? `Additional Amounts: ${ollamaAnalysis.benefitInfo.benefitAmounts.slice(1).join(', ')}` : ''}

📅 Expected Arrival: ${ollamaAnalysis.benefitInfo.expectedArrivalDate}

👥 Eligible Recipients: ${ollamaAnalysis.benefitInfo.eligiblePeople}

🔍 Source/Proof: ${ollamaAnalysis.benefitInfo.proofOrSource}

✅ Actions to Claim: ${ollamaAnalysis.benefitInfo.actionsToClaim}

⭐ Priority: ${ollamaAnalysis.classification.priorityTag} (Score: ${ollamaAnalysis.classification.score}/100)
📝 Reason: ${ollamaAnalysis.classification.reasonForPriority}

🔄 Viral Potential: ${ollamaAnalysis.classification.viralPotential}
`.trim();

  // Log the model used for debugging
  console.log(`🤖 Ollama API: Converting analysis to app format. Model used: ${ollamaAnalysis.modelUsed || 'Unknown'}`);
  console.log(`🤖 Ollama API: Analysis score: ${ollamaAnalysis.classification.score}/100, category: ${category}`);

  // Set a default score based on priority tag if score is 0
  const score = ollamaAnalysis.classification.score > 0 ?
    ollamaAnalysis.classification.score :
    ollamaAnalysis.classification.priorityTag === 'Urgent - High Certainty' ? 85 :
    ollamaAnalysis.classification.priorityTag === 'Anticipated' ? 65 : 30;

  console.log(`🤖 Ollama API: Using score ${score} for analysis (original: ${ollamaAnalysis.classification.score})`);

  return {
    score: score,
    category,
    amount: primaryAmount,
    timeline: ollamaAnalysis.benefitInfo.expectedArrivalDate,
    recipients: ollamaAnalysis.benefitInfo.eligiblePeople,
    steps: ollamaAnalysis.benefitInfo.actionsToClaim,
    viralPotential: ollamaAnalysis.classification.viralPotential,
    skepticism: ollamaAnalysis.classification.priorityTag === 'Doubtful' ? 'High' : 'Low',
    analysis: analysisText,
    // Ollama-specific fields
    ollamaBenefitAmounts: ollamaAnalysis.benefitInfo.benefitAmounts,
    ollamaExpectedArrivalDate: ollamaAnalysis.benefitInfo.expectedArrivalDate,
    ollamaEligiblePeople: ollamaAnalysis.benefitInfo.eligiblePeople,
    ollamaProofOrSource: ollamaAnalysis.benefitInfo.proofOrSource,
    ollamaActionsToClaim: ollamaAnalysis.benefitInfo.actionsToClaim,
    ollamaPriorityTag: ollamaAnalysis.classification.priorityTag,
    ollamaReasonForPriority: ollamaAnalysis.classification.reasonForPriority,
    ollamaViralPotential: ollamaAnalysis.classification.viralPotential,
    ollamaModelUsed: ollamaAnalysis.modelUsed && ollamaAnalysis.modelUsed !== 'Unknown' ?
      (ollamaAnalysis.modelUsed.includes(':') ? ollamaAnalysis.modelUsed : `${ollamaAnalysis.modelUsed}:latest`) :
      'llama3.2:latest', // Use a default model name if not provided, ensuring it has the correct format
    ollamaPromptName: ollamaAnalysis.promptName || 'Default Prompt',
    ollamaPrompt: ollamaAnalysis.prompt || '',
    ollamaSystemPrompt: FINANCIAL_ANALYSIS_SYSTEM_PROMPT,
    ollamaRawData: ollamaAnalysis.rawData || (() => {
      // Create a complete raw data object with all fields
      const rawData = {
        benefitInfo: ollamaAnalysis.benefitInfo,
        classification: ollamaAnalysis.classification,
        modelUsed: ollamaAnalysis.modelUsed && ollamaAnalysis.modelUsed !== 'Unknown' ?
          (ollamaAnalysis.modelUsed.includes(':') ? ollamaAnalysis.modelUsed : `${ollamaAnalysis.modelUsed}:latest`) :
          'llama3.2:latest', // Use a default model name if not provided, ensuring it has the correct format
        promptName: ollamaAnalysis.promptName || 'Default Financial Analysis',
        prompt: ollamaAnalysis.prompt || '',
        systemPrompt: FINANCIAL_ANALYSIS_SYSTEM_PROMPT
      };

      // Log the benefit amounts in the raw data
      console.log(`🤖 Ollama API: Creating raw data with benefit amounts:`, rawData.benefitInfo.benefitAmounts);

      return JSON.stringify(rawData);
    })()
  };
}
