/**
 * Utility functions to determine video types
 */

/**
 * Determines if a video is a YouTube Short based on its duration
 * @param durationStr ISO 8601 duration string (e.g., "PT8M10S")
 * @returns boolean indicating if the video is a Short
 */
export function isYoutubeShort(durationStr: string | undefined): boolean {
  if (!durationStr) return false;

  // Handle special case for P0D (zero duration)
  if (durationStr === 'P0D') return true;

  // Parse the ISO 8601 duration format (PT8M10S = 8 minutes 10 seconds)
  const minutesMatch = durationStr.match(/([0-9]+)M/);
  const secondsMatch = durationStr.match(/([0-9]+)S/);
  const hoursMatch = durationStr.match(/([0-9]+)H/);

  const hours = hoursMatch ? parseInt(hoursMatch[1]) : 0;
  const minutes = minutesMatch ? parseInt(minutesMatch[1]) : 0;
  const seconds = secondsMatch ? parseInt(secondsMatch[1]) : 0;

  const totalSeconds = (hours * 3600) + (minutes * 60) + seconds;

  // YouTube shorts are typically less than 60 seconds
  return totalSeconds < 60;
}

/**
 * Determines if a video is an active live stream based on its title, duration, and metadata
 * @param title Video title
 * @param durationStr ISO 8601 duration string
 * @param isActiveLive Optional flag to indicate if the video is currently live (from API)
 * @param isUnplayable Optional flag to indicate if the video is unplayable (for ended live streams)
 * @returns boolean indicating if the video is an active live stream
 */
export function isLiveStream(
  title: string,
  durationStr: string | undefined,
  isActiveLive?: boolean,
  isUnplayable?: boolean
): boolean {
  // If we have explicit information that the video is active live, use that
  if (isActiveLive !== undefined) {
    return isActiveLive;
  }

  // If we know it's an unplayable ended live stream, it's not an active live stream
  if (isUnplayable === true) {
    return false;
  }

  // Check for common live stream indicators in the title
  const liveIndicators = [
    '🔴 LIVE',
    '🔴LIVE',
    '[LIVE]',
    '(LIVE)',
    'LIVE:',
    'LIVE STREAM',
    'LIVESTREAM',
    'LIVE NOW',
    'STREAMING NOW',
    'STREAMING LIVE',
  ];

  const titleUpper = title.toUpperCase();

  // Check if any live indicator is present in the title
  const hasLiveIndicator = liveIndicators.some(indicator =>
    titleUpper.includes(indicator.toUpperCase())
  );

  // Check if it's an ended live stream
  if (isEndedLiveStream(title)) {
    return false;
  }

  // Only consider it a live stream if it has live indicators in the title
  // We can't reliably detect live streams just from duration
  return hasLiveIndicator;
}

/**
 * Determines the type of a YouTube video
 * @param title Video title
 * @param durationStr ISO 8601 duration string
 * @param isActiveLive Optional flag to indicate if the video is currently live (from API)
 * @param isUnplayable Optional flag to indicate if the video is unplayable (for ended live streams)
 * @returns The video type: 'short', 'live', or 'normal'
 */
export function getVideoType(
  title: string,
  durationStr: string | undefined,
  isActiveLive?: boolean,
  isUnplayable?: boolean
): 'short' | 'live' | 'normal' {
  if (isLiveStream(title, durationStr, isActiveLive, isUnplayable)) return 'live';
  if (isYoutubeShort(durationStr)) return 'short';
  return 'normal';
}

/**
 * Determines if a video is an ended live stream based on its title and metadata
 * @param title Video title
 * @param isUnplayable Optional flag to indicate if the video is unplayable (for ended live streams)
 * @returns boolean indicating if the video is an ended live stream
 */
export function isEndedLiveStream(title: string, isUnplayable?: boolean): boolean {
  // If we know it's an unplayable ended live stream, return true immediately
  if (isUnplayable === true) {
    return true;
  }

  // Check for indicators that suggest this is an ended live stream
  const endedLiveStreamIndicators = [
    'WAS STREAMED',
    'STREAMED LIVE ON',
    'PREMIERED',
    'STREAMED ON',
    'LIVE STREAM RECORDING',
    'STREAM RECORDING',
  ];

  const titleUpper = title.toUpperCase();

  // Check if any ended live stream indicator is present in the title
  return endedLiveStreamIndicators.some(indicator =>
    titleUpper.includes(indicator.toUpperCase())
  );
}
