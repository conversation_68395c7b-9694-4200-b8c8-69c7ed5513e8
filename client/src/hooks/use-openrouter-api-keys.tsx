import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { OpenRouterApiKeyExportData, OpenRouterApiKeyImportResult } from "@shared/schema";

interface OpenRouterApiKey {
  id: number;
  name: string;
  masked_token: string;
  is_active: boolean;
  last_used?: string;
  created_at: string;
  is_default?: boolean;
  exhausted_until?: string;
  rate_limit_type?: string;
}

export function useOpenRouterApiKeys() {
  const { toast } = useToast();

  // Fetch all OpenRouter API keys
  const {
    data: apiKeys,
    isLoading: isLoadingApiKeys,
    error: apiKeysError,
    refetch: refetchApiKeys,
  } = useQuery<OpenRouterApiKey[]>({
    queryKey: ["/api/openrouter-api-keys"],
    staleTime: 60000, // 1 minute
  });

  // Add a new OpenRouter API key
  const addApiKey = useMutation({
    mutationFn: async (data: { token: string; name?: string; is_active?: boolean }) => {
      const res = await apiRequest("POST", "/api/openrouter-api-keys", data);
      if (!res.ok) {
        throw new Error(`Failed to add OpenRouter API key: ${res.status} ${res.statusText}`);
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/openrouter-api-keys"] });
      toast({
        title: "OpenRouter API key added",
        description: "Your OpenRouter API key has been added successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to add OpenRouter API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update an OpenRouter API key
  const updateApiKey = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<OpenRouterApiKey> }) => {
      const res = await apiRequest("PUT", `/api/openrouter-api-keys/${id}`, data);
      if (!res.ok) {
        throw new Error(`Failed to update OpenRouter API key: ${res.status} ${res.statusText}`);
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/openrouter-api-keys"] });
      toast({
        title: "OpenRouter API key updated",
        description: "Your OpenRouter API key has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update OpenRouter API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete an OpenRouter API key
  const deleteApiKey = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("DELETE", `/api/openrouter-api-keys/${id}`);
      if (!res.ok) {
        throw new Error(`Failed to delete OpenRouter API key: ${res.status} ${res.statusText}`);
      }
      return id;
    },
    onSuccess: (id) => {
      queryClient.invalidateQueries({ queryKey: ["/api/openrouter-api-keys"] });
      toast({
        title: "OpenRouter API key deleted",
        description: "Your OpenRouter API key has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete OpenRouter API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Test an OpenRouter API key
  const testApiKey = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("POST", `/api/openrouter-api-keys/${id}/test`);
      if (!res.ok) {
        throw new Error(`Failed to test OpenRouter API key: ${res.status} ${res.statusText}`);
      }
      return res.json();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to test OpenRouter API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Export all OpenRouter API keys
  const exportApiKeys = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("GET", "/api/openrouter-api-keys/export");
      if (!res.ok) {
        throw new Error(`Failed to export OpenRouter API keys: ${res.status} ${res.statusText}`);
      }
      return res.json() as Promise<OpenRouterApiKeyExportData>;
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to export OpenRouter API keys",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Import OpenRouter API keys
  const importApiKeys = useMutation({
    mutationFn: async (data: { keys: Array<{ token: string; name?: string; is_active?: boolean; is_default?: boolean }> }) => {
      const res = await apiRequest("POST", "/api/openrouter-api-keys/batch-import", data);
      if (!res.ok) {
        throw new Error(`Failed to import OpenRouter API keys: ${res.status} ${res.statusText}`);
      }
      return res.json() as Promise<OpenRouterApiKeyImportResult>;
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ["/api/openrouter-api-keys"] });
      toast({
        title: result.success ? "OpenRouter API keys imported" : "Import partially failed",
        description: result.message,
        variant: result.success ? "default" : "destructive",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to import OpenRouter API keys",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Reset all exhausted API keys
  const resetExhaustedKeys = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("POST", "/api/openrouter-api-keys/reset-exhausted");
      if (!res.ok) {
        throw new Error(`Failed to reset exhausted API keys: ${res.status} ${res.statusText}`);
      }
      return res.json();
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ["/api/openrouter-api-keys"] });
      toast({
        title: "Exhausted API keys reset",
        description: result.message,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to reset exhausted API keys",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    apiKeys: apiKeys || [],
    isLoading: isLoadingApiKeys,
    error: apiKeysError,
    refetch: refetchApiKeys,
    addApiKey,
    updateApiKey,
    deleteApiKey,
    testApiKey,
    exportApiKeys,
    importApiKeys,
    resetExhaustedKeys,
  };
}

export default useOpenRouterApiKeys;
