import Database from 'better-sqlite3';
import { User, Video, UserSettings, InsertUser } from "@shared/schema";
import session from "express-session";
import createMemoryStore from "memorystore";
import fetch from "node-fetch";
import 'dotenv/config';
import { IStorage } from './storage';

export class SQLiteStorage implements IStorage {
    private db: Database.Database;
    readonly sessionStore: session.Store;

    constructor() {
        this.db = new Database('data.db');
        this.initializeTables();
        this.ensureAdminRole();

        const MemoryStore = createMemoryStore(session);
        this.sessionStore = new MemoryStore({
            checkPeriod: 86400000,
        });
    }

    private initializeTables() {
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE,
                password TEXT,
                role TEXT DEFAULT 'user'
            );
            CREATE TABLE IF NOT EXISTS settings (
                user_id INTEGER PRIMARY KEY,
                dark_mode BOOLEAN,
                search_keywords TEXT,
                min_views_per_hour INTEGER DEFAULT 10,
                remove_duplicates BOOLEAN DEFAULT 1,
                exclude_words TEXT DEFAULT '[]',
                auto_refresh_interval INTEGER DEFAULT 0,
                preferred_playback TEXT DEFAULT 'in_app',
                last_refresh_time TEXT DEFAULT NULL,
                parallel_api_calls BOOLEAN DEFAULT 1,
                watched_videos TEXT DEFAULT '[]',
                use_in_app_player BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users(id)
            );
            CREATE TABLE IF NOT EXISTS videos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                video_id TEXT,
                title TEXT,
                thumbnail TEXT,
                channel_title TEXT,
                view_count INTEGER,
                published_at TEXT,
                user_id INTEGER,
                FOREIGN KEY (user_id) REFERENCES users(id)
            );
        `);
    }

    // Ensure admin user has admin role
    private ensureAdminRole() {
        try {
            // Check if admin user exists
            const adminUser = this.db.prepare("SELECT * FROM users WHERE username = 'admin'").get();
            if (adminUser) {
                // Update admin role if needed
                this.db.prepare("UPDATE users SET role = 'admin' WHERE username = 'admin'").run();
            }
        } catch (error) {
            console.error('Error ensuring admin role:', error);
        }
    }

    async createUser(insertUser: InsertUser): Promise<User> {
        // Set admin role for username 'admin'
        const role = insertUser.username === 'admin' ? 'admin' : 'user';
        const stmt = this.db.prepare('INSERT INTO users (username, password, role) VALUES (?, ?, ?)');
        const result = stmt.run(String(insertUser.username), String(insertUser.password), role);
        const userId = Number(result.lastInsertRowid);

        // Initialize default settings for new user
        const settingsStmt = this.db.prepare(`
            INSERT INTO settings (
                user_id, dark_mode, search_keywords, min_views_per_hour, remove_duplicates,
                exclude_words, auto_refresh_interval, preferred_playback,
                parallel_api_calls, watched_videos, use_in_app_player
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        settingsStmt.run(
            userId,
            1,  // darkMode: true
            JSON.stringify(["vlog", "funny", "fail"]),
            10, // minViewsPerHour
            1,  // removeDuplicates: true
            JSON.stringify([]), // excludeWords
            0,  // autoRefreshInterval
            'in_app', // preferredPlayback
            1,  // parallelApiCalls: true
            JSON.stringify([]), // watchedVideos
            1   // useInAppPlayer: true
        );

        return {
            id: userId,
            username: String(insertUser.username),
            password: String(insertUser.password),
            role: role,
            darkMode: true,
            searchKeywords: ["vlog", "funny", "fail"],
            minViewsPerHour: 10,
            removeDuplicates: true,
            excludeWords: [],
            autoRefreshInterval: 0,
            preferredPlayback: "in_app",
            lastRefreshTime: null,
            parallelApiCalls: true,
            watchedVideos: [],
            useInAppPlayer: true
        };
    }

    async getUser(id: number): Promise<User | undefined> {
        const userStmt = this.db.prepare('SELECT * FROM users WHERE id = ?');
        const user = userStmt.get(id);

        if (!user) return undefined;

        const settingsStmt = this.db.prepare('SELECT * FROM settings WHERE user_id = ?');
        const settings = settingsStmt.get(id);

        if (!settings) {
            return {
                id: user.id,
                username: user.username,
                password: user.password,
                role: user.role || (user.username === 'admin' ? 'admin' : 'user'),
                darkMode: true,
                searchKeywords: ["vlog", "funny", "fail"],
                minViewsPerHour: 10,
                removeDuplicates: true,
                excludeWords: [],
                autoRefreshInterval: 0,
                preferredPlayback: "in_app",
                lastRefreshTime: null,
                parallelApiCalls: false,
                watchedVideos: [],
                useInAppPlayer: true
            };
        }

        return {
            id: user.id,
            username: user.username,
            password: user.password,
            role: user.role || (user.username === 'admin' ? 'admin' : 'user'),
            darkMode: Boolean(settings.dark_mode),
            searchKeywords: JSON.parse(settings.search_keywords),
            minViewsPerHour: settings.min_views_per_hour,
            removeDuplicates: Boolean(settings.remove_duplicates),
            excludeWords: JSON.parse(settings.exclude_words),
            autoRefreshInterval: settings.auto_refresh_interval,
            preferredPlayback: settings.preferred_playback,
            lastRefreshTime: settings.last_refresh_time ? new Date(settings.last_refresh_time) : null,
            parallelApiCalls: Boolean(settings.parallel_api_calls),
            watchedVideos: JSON.parse(settings.watched_videos),
            useInAppPlayer: Boolean(settings.use_in_app_player)
        };
    }

    async getUserByUsername(username: string): Promise<User | undefined> {
        const stmt = this.db.prepare('SELECT * FROM users WHERE username = ?');
        const user = stmt.get(String(username));

        if (!user) return undefined;

        return this.getUser(user.id);
    }

    async getAllUsers(): Promise<User[]> {
        try {
            const stmt = this.db.prepare('SELECT id FROM users');
            const userIds = stmt.all();

            // Get full user objects with settings
            const users = await Promise.all(
                userIds.map(row => this.getUser(row.id))
            );

            return users.filter(user => user !== undefined) as User[];
        } catch (error) {
            console.error('Error fetching all users:', error);
            throw error;
        }
    }

    async getSettings(userId: number): Promise<UserSettings | undefined> {
        const user = await this.getUser(userId);
        if (!user) return undefined;

        const { id, username, password, role, ...settings } = user;
        return settings;
    }

    async updateSettings(userId: number, settings: UserSettings): Promise<UserSettings> {
        const user = await this.getUser(userId);
        if (!user) throw new Error("User not found");

        const stmt = this.db.prepare(`
            UPDATE settings SET
                dark_mode = ?,
                search_keywords = ?,
                min_views_per_hour = ?,
                remove_duplicates = ?,
                exclude_words = ?,
                auto_refresh_interval = ?,
                preferred_playback = ?,
                last_refresh_time = ?,
                parallel_api_calls = ?,
                watched_videos = ?,
                use_in_app_player = ?
            WHERE user_id = ?
        `);

        stmt.run(
            settings.darkMode ? 1 : 0,
            JSON.stringify(settings.searchKeywords),
            settings.minViewsPerHour || 10,
            settings.removeDuplicates ? 1 : 0,
            JSON.stringify(settings.excludeWords || []),
            settings.autoRefreshInterval || 0,
            settings.preferredPlayback || 'in_app',
            settings.lastRefreshTime ? settings.lastRefreshTime.toISOString() : null,
            settings.parallelApiCalls ? 1 : 0,
            JSON.stringify(settings.watchedVideos || []),
            settings.useInAppPlayer ? 1 : 0,
            Number(userId)
        );

        return settings;
    }

    async getVideos(userId: number): Promise<Video[]> {
        try {
            const stmt = this.db.prepare('SELECT * FROM videos WHERE user_id = ?');
            const videos = stmt.all(Number(userId));

            console.log('Retrieved videos from DB:', videos); // Debug log

            return videos.map(v => ({
                id: v.video_id,
                url: `https://youtube.com/watch?v=${v.video_id}`,
                title: v.title,
                thumbnail: v.thumbnail,
                channelTitle: v.channel_title,
                viewCount: v.view_count,
                publishedAt: new Date(v.published_at),
                userId: v.user_id,
                watched: false  // Add this field if it's required by the frontend
            }));
        } catch (error) {
            console.error('Error in getVideos:', error);
            throw error;
        }
    }

    async updateVideos(userId: number, videos: Video[]): Promise<void> {
        try {
            console.log('Updating videos for user:', userId, 'Videos:', videos); // Debug log

            const deleteStmt = this.db.prepare('DELETE FROM videos WHERE user_id = ?');
            const insertStmt = this.db.prepare(`
                INSERT INTO videos (
                    video_id, title, thumbnail, channel_title,
                    view_count, published_at, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `);

            this.db.transaction(() => {
                deleteStmt.run(Number(userId));
                for (const video of videos) {
                    insertStmt.run(
                        video.id,
                        video.title,
                        video.thumbnail,
                        video.channelTitle,
                        video.viewCount,
                        video.publishedAt.toISOString(),
                        userId
                    );
                }
            })();

            // Verify the update
            const updatedVideos = await this.getVideos(userId);
            console.log('Videos after update:', updatedVideos);
        } catch (error) {
            console.error('Error in updateVideos:', error);
            throw error;
        }
    }

    async refreshVideos(user: User): Promise<Video[]> {
        try {
            const headers = {
                'Accept': 'application/json, text/plain, */*',
                'Authorization': `Bearer ${process.env.VIDIQ_API_KEY || ''}`,
                'Referer': 'https://app.vidiq.com/',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
                'X-TimeZone': 'Asia/Calcutta',
                'X-Vidiq-Client': 'web 1a7277ec05e25b41808d7ed49f9a3f3c6ff86254',
                'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'x-vidiq-auth': process.env.VIDIQ_API_KEY || ''
            };

            const fetchVideos = async (keyword: string) => {
                console.log(`Fetching videos for keyword: ${keyword}`);
                try {
                    const response = await fetch(
                        `https://api.vidiq.com/v0/trendy?q[]=${encodeURIComponent(keyword)}&c=US&l=en&content_details=false`,
                        { headers }
                    );

                    if (!response.ok) {
                        const responseText = await response.text();
                        console.error(`API request failed for ${keyword}:`, {
                            status: response.status,
                            statusText: response.statusText,
                            body: responseText
                        });
                        throw new Error(`API request failed: ${response.statusText}`);
                    }

                    const data = await response.json();
                    console.log(`Received ${data.videos?.length || 0} videos for keyword: ${keyword}`);
                    return data.videos || [];
                } catch (error) {
                    console.error(`Error fetching videos for ${keyword}:`, error);
                    throw error;
                }
            };

            let allVideos: Video[] = [];

            if (user.parallelApiCalls) {
                const promises = user.searchKeywords.map(keyword => fetchVideos(keyword));
                const results = await Promise.all(promises);
                allVideos = results.flat();
            } else {
                for (const keyword of user.searchKeywords) {
                    const videos = await fetchVideos(keyword);
                    allVideos = allVideos.concat(videos);
                }
            }

            console.log(`Total videos fetched: ${allVideos.length}`);

            // Apply filters
            let filteredVideos = allVideos
                .filter(video => {
                    const hoursElapsed = Math.max(1, (Date.now() - new Date(video.published_at).getTime()) / (1000 * 60 * 60));
                    const viewsPerHour = video.views_count / hoursElapsed;
                    return viewsPerHour >= user.minViewsPerHour;
                })
                .filter(video => !user.excludeWords.some(word =>
                    video.title.toLowerCase().includes(word.toLowerCase())
                ));

            console.log(`Videos after filtering: ${filteredVideos.length}`);

            if (user.removeDuplicates) {
                const seen = new Set();
                filteredVideos = filteredVideos.filter(video => {
                    if (seen.has(video.id)) return false;
                    seen.add(video.id);
                    return true;
                });
                console.log(`Videos after deduplication: ${filteredVideos.length}`);
            }

            const processedVideos = filteredVideos.map(video => ({
                id: video.id,
                url: `https://youtube.com/watch?v=${video.id}`,
                title: video.title,
                thumbnail: `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`,
                channelTitle: video.channel_title,
                viewCount: video.views_count,
                publishedAt: new Date(video.published_at),
                userId: user.id,
            }));

            console.log(`Processing ${processedVideos.length} videos for database update`);

            try {
                // Clear existing videos for this user
                const deleteStmt = this.db.prepare('DELETE FROM videos WHERE user_id = ?');
                const insertStmt = this.db.prepare(
                    'INSERT INTO videos (video_id, title, thumbnail, channel_title, view_count, published_at, user_id) VALUES (?, ?, ?, ?, ?, ?, ?)'
                );

                this.db.transaction(() => {
                    deleteStmt.run(user.id);
                    for (const video of processedVideos) {
                        insertStmt.run(
                            video.id,
                            video.title,
                            video.thumbnail,
                            video.channelTitle,
                            video.viewCount,
                            video.publishedAt.toISOString(),
                            video.userId
                        );
                    }
                })();

                // Update the user's lastRefreshTime
                await this.updateSettings(user.id, {
                    ...user,
                    lastRefreshTime: new Date(),
                });

                console.log('Database update completed successfully');
                return processedVideos;
            } catch (dbError) {
                console.error('Database operation failed:', dbError);
                throw dbError;
            }
        } catch (error) {
            console.error('Error in refreshVideos:', error);
            throw error;
        }
    }

    async deleteUser(id: number): Promise<void> {
        try {
            // Don't allow deleting the admin user
            const user = await this.getUser(id);
            if (user && user.username === 'admin') {
                throw new Error("Cannot delete the admin user");
            }

            this.db.transaction(() => {
                this.db.prepare('DELETE FROM videos WHERE user_id = ?').run(id);
                this.db.prepare('DELETE FROM settings WHERE user_id = ?').run(id);
                this.db.prepare('DELETE FROM users WHERE id = ?').run(id);
            })();
        } catch (error) {
            console.error('Failed to delete user:', error);
            throw error;
        }
    }

    async deleteUserData(userId: number): Promise<void> {
        try {
            this.db.transaction(() => {
                this.db.prepare('DELETE FROM videos WHERE user_id = ?').run(userId);
                this.db.prepare('DELETE FROM settings WHERE user_id = ?').run(userId);
                // Option 1: Delete user completely
                // this.db.prepare('DELETE FROM users WHERE id = ?').run(userId);

                // Option 2: Reset user to defaults (like MemStorage implementation)
                this.db.prepare(`
                    UPDATE settings SET
                        dark_mode = 1,
                        search_keywords = ?,
                        min_views_per_hour = 10,
                        remove_duplicates = 1,
                        exclude_words = '[]',
                        auto_refresh_interval = 0,
                        preferred_playback = 'in_app',
                        last_refresh_time = NULL,
                        parallel_api_calls = 1,
                        watched_videos = '[]',
                        use_in_app_player = 1
                    WHERE user_id = ?
                `).run(JSON.stringify(["vlog", "funny", "fail"]), userId);
            })();
        } catch (error) {
            console.error('Failed to delete user data:', error);
            throw error;
        }
    }
}
