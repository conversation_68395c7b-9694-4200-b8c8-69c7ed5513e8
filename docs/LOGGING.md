# Logging Configuration in Trendy

This document explains how to configure logging in the Trendy application to control the amount of console output.

## Overview

Trendy includes a structured logging system that allows you to control the verbosity of logs in the browser console. By default, the application is configured to show only warnings and errors in development mode, and only errors in production mode.

## Log Levels

The following log levels are available, from least to most verbose:

1. `NONE` (0) - No logs at all
2. `ERROR` (1) - Only error messages
3. `WARN` (2) - Warnings and errors
4. `INFO` (3) - Informational messages, warnings, and errors
5. `DEBUG` (4) - Debug messages, informational messages, warnings, and errors
6. `VERBOSE` (5) - All logs, including verbose debug information

## Changing the Log Level

You can change the log level by editing the `client/src/config/logging.ts` file:

```typescript
export const USER_LOG_LEVEL = LogLevel.WARN; // Change this to your preferred level
```

For example:
- To see only errors: `export const USER_LOG_LEVEL = LogLevel.ERROR;`
- To see more detailed logs: `export const USER_LOG_LEVEL = LogLevel.INFO;`
- To see all logs for debugging: `export const USER_LOG_LEVEL = LogLevel.DEBUG;`
- To disable all logs: `export const USER_LOG_LEVEL = LogLevel.NONE;`

## Performance Impact

Excessive logging can impact application performance, especially in production environments. It's recommended to use lower log levels (ERROR or WARN) in production and higher levels only during development or debugging.

## Additional Logging Options

The logging configuration file also includes options to enable specialized logging:

```typescript
// Enable detailed performance logging
export const ENABLE_PERFORMANCE_LOGGING = false;

// Enable detailed API request logging
export const ENABLE_API_LOGGING = false;

// Enable memory usage logging
export const ENABLE_MEMORY_LOGGING = false;
```

These options can be set to `true` to enable additional specialized logging when needed for debugging specific issues.

## Console Filtering

Most modern browsers allow you to filter console output by log level. In Chrome DevTools, you can use the dropdown in the console panel to show only errors, warnings, or all logs.

## Troubleshooting

If you're experiencing performance issues or console clutter, try setting the log level to `ERROR` or `WARN`. If you need more information for debugging, you can temporarily increase the log level to `DEBUG` or `VERBOSE`.
