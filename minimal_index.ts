// Minimal version of server/index.ts
import 'dotenv/config';
import express from "express";
import session from "express-session";
import Database from 'better-sqlite3';
import createMemoryStore from "memorystore";

console.log('Starting minimal server...');

try {
  // Create Express app
  const app = express();
  console.log('Express app created');
  
  // Set up middleware
  app.use(express.json());
  app.use(express.urlencoded({ extended: false }));
  console.log('Middleware set up');
  
  // Set up session
  const MemoryStore = createMemoryStore(session);
  app.use(session({
    secret: 'test-secret-key',
    resave: false,
    saveUninitialized: false,
    store: new MemoryStore({
      checkPeriod: 86400000,
    }),
    cookie: {
      secure: false,
      httpOnly: true,
      maxAge: 30 * 24 * 60 * 60 * 1000,
      sameSite: 'lax'
    }
  }));
  console.log('Session middleware set up');
  
  // Set up database
  console.log('Opening database...');
  const db = new Database('data.db');
  
  // Set up routes
  app.get('/api/test', (req, res) => {
    try {
      const users = db.prepare('SELECT COUNT(*) as count FROM users').get();
      res.json({ success: true, userCount: users.count });
    } catch (error) {
      console.error('Error accessing database:', error);
      res.status(500).json({ success: false, error: error.message });
    }
  });
  console.log('Routes set up');
  
  // Start the server
  const port = 5007;
  app.listen(port, () => {
    console.log(`Minimal server running on port ${port}`);
  });
  
} catch (error) {
  console.error('Error starting minimal server:', error);
}
