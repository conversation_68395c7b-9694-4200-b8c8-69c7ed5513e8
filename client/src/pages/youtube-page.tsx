import React, { useState, useEffect, useCallback, useRef, useMemo, memo } from 'react';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useYoutubeChannels } from '@/hooks/use-youtube-channels';
import { useYoutubeBackgroundTasks } from '@/hooks/use-youtube-background-tasks';
import { useBackgroundTasks, TaskStatus } from '@/hooks/use-background-tasks';
import { useAnalyzeChannelWithOpenRouter } from '@/hooks/use-openrouter';
import { useRefreshMetadata } from '@/hooks/use-refresh-metadata';
import { useCachedVideos } from '@/hooks/use-cached-videos';
import { logger, LogLevel, conditionalLog } from '@/lib/logger';
import { isLiveStream, isEndedLiveStream } from '@/lib/video-type';
import { useMutation } from '@tanstack/react-query';
import { FixedSizeList as List } from 'react-window';
import { YoutubeChannel, YoutubeVideo } from '@shared/schema';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { useSettings } from '@/hooks/use-settings';
import { useYoutubeHandleResolver } from '@/hooks/use-youtube-handle-resolver';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { YoutubeVideoCard } from '@/components/video/youtube-video-card';
import { YoutubeThumbnail } from '@/components/youtube-thumbnail';
import { ChannelThumbnail } from '@/components/youtube/channel-thumbnail';
import { InfoIcon, RefreshCw, Plus, Trash2, Menu, Clock, Search, X, Filter, Home, Eye, AlertCircle, DatabaseZap, Loader2, VideoOff, Sparkles, Zap, ArrowUpRight, ClipboardCopy, ExternalLink, Users, User, FileText, Pencil, BarChart, Activity, Radio } from 'lucide-react';
import * as RechartsPrimitive from "recharts";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";
import { RecentTasksWidget } from '@/components/ui/task-status';
import '@/components/video/video-grid.css';
import { formatDistanceToNow } from 'date-fns';
import { useIsMobile } from '@/hooks/use-mobile';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { formatRefreshTime } from '@/lib/date-format';
import { Sidebar, useSidebarCollapse } from '@/components/navigation/sidebar';
import { ImportExportChannels } from '@/components/youtube/import-export-channels';
import { RealtimeAutoRefresh } from '@/components/realtime-auto-refresh';
import { RssTab } from '@/components/rss/rss-tab';
import { VphIndicator } from '@/components/video/vph-indicator';
import CustomUrlAccountLauncher from '@/components/nlm/custom-url-account-launcher';
import { YtrTab } from '@/components/youtube/ytr-tab';
import { TxtTab } from '@/components/txt/txt-tab';
import TxtProTab from '@/components/txt-pro/txt-pro-tab';

// Helper function to get day name with offset
function getDayNameWithOffset(daysAgo: number): string {
  const today = new Date();
  const targetDate = new Date(today);
  targetDate.setDate(today.getDate() - daysAgo);
  const dayName = targetDate.toLocaleDateString('en-US', { weekday: 'long' });
  return `-${daysAgo} ${dayName}`;
}

// Duration filter type
type DurationFilterType = 'all' | 'shorts' | 'videos';

// No longer using transcript filter

// Simplified sort options with clear grouping
const COMBINED_SORT_OPTIONS = [
  // Popular & Trending (most useful options first)
  { value: "views_desc", label: "Most Views", group: "popular" },
  { value: "date_desc", label: "Newest First", group: "popular" },
  { value: "date_asc", label: "Oldest First", group: "popular" },
  { value: "vph_desc", label: "Views Per Hour", group: "popular" },

  // Content Type
  { value: "shorts_first", label: "Shorts First", group: "content" },
  { value: "videos_first", label: "Videos First", group: "content" },
  { value: "duration_asc", label: "Shortest First", group: "content" },
  { value: "duration_desc", label: "Longest First", group: "content" },

  // Alphabetical
  { value: "title_asc", label: "Title (A-Z)", group: "basic" },
];

type SortOption =
  // Popular & Trending
  | 'vph_desc'
  | 'views_desc'
  | 'date_desc'
  | 'date_asc'

  // Content Type
  | 'shorts_first'
  | 'videos_first'
  | 'duration_asc'
  | 'duration_desc'

  // Alphabetical
  | 'title_asc'

  // Legacy options for backward compatibility
  | 'title'
  | 'none';

// Helper function to extract numeric value from financial amount string
function extractNumericValue(financialAmount: string): number {
  if (!financialAmount) return 0;

  // Remove all non-numeric characters except decimal point
  const numericString = financialAmount.replace(/[^0-9.]/g, '');
  const value = parseFloat(numericString);

  // Handle thousand, million, billion, trillion
  if (financialAmount.toLowerCase().includes('thousand')) {
    return value * 1000;
  } else if (financialAmount.toLowerCase().includes('million')) {
    return value * 1000000;
  } else if (financialAmount.toLowerCase().includes('billion')) {
    return value * 1000000000;
  } else if (financialAmount.toLowerCase().includes('trillion')) {
    return value * 1000000000000;
  }

  return isNaN(value) ? 0 : value;
}



export default function YoutubePage() {
  const { toast } = useToast();
  const { settings } = useSettings();
  const isMobile = useIsMobile();

  // Channel state
  const [selectedChannelId, setSelectedChannelId] = useState<number | null>(null);
  const [newChannelUrl, setNewChannelUrl] = useState('');
  const [newChannelVideoLimit, setNewChannelVideoLimit] = useState(5);
  const [addChannelDialogOpen, setAddChannelDialogOpen] = useState(false);

  // Video display state
  const [displayedVideos, setDisplayedVideos] = useState<YoutubeVideo[]>([]);
  const [allFilteredVideos, setAllFilteredVideos] = useState<YoutubeVideo[]>([]);
  const [videosPerPage, setVideosPerPage] = useState<number>(30); // Initial batch size
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalVideos, setTotalVideos] = useState<number>(0);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  // Debounce function for search
  const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);

      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]);

    return debouncedValue;
  };

  const [gridSize, setGridSize] = useState(() => {
    // Try to load from localStorage
    const savedGridSize = localStorage.getItem('preferredGridSize');
    const parsedSize = savedGridSize ? parseInt(savedGridSize) : 4; // Default to 4 columns
    // Ensure the grid size is between 1 and 6
    return Math.max(1, Math.min(6, parsedSize));
  });
  const [sortBy, setSortBy] = useState<SortOption>(() => {
    // Default to 'date_desc' (Newest First) as the most sensible default
    const defaultSort: SortOption = 'date_desc';

    // Only use saved sort if explicitly set by the user
    const savedSort = localStorage.getItem('youtubePageSortMethod');
    if (savedSort) {
      // Validate that the saved sort is still in our options
      const isValidSort = COMBINED_SORT_OPTIONS.some(option => option.value === savedSort);
      if (isValidSort) {
        console.log(`Using saved sort from localStorage: ${savedSort}`);
        return savedSort as SortOption;
      } else {
        // If saved sort is invalid, remove it from localStorage
        console.log(`Removing invalid sort from localStorage: ${savedSort}`);
        localStorage.removeItem('youtubePageSortMethod');
      }
    }

    // Use the default sort
    console.log(`Using default sort: ${defaultSort}`);
    return defaultSort;
  });
  const [durationFilter, setDurationFilter] = useState<DurationFilterType>('all');
  const [isSorting, setIsSorting] = useState(false);
  const [showUnwatchedOnly, setShowUnwatchedOnly] = useState(false);
  const [searchInputValue, setSearchInputValue] = useState('');
  const searchQuery = useDebounce(searchInputValue, 200); // 200ms debounce - faster response
  const [needDirectFetch, setNeedDirectFetch] = useState<number | null>(null);

  // Create a dedicated query key for the sort option to ensure proper API sorting
  const [querySortKey, setQuerySortKey] = useState<SortOption>(sortBy);

  // Create a ref to track the stable sort value
  const stableSortRef = useRef<SortOption>(sortBy);

  // Log when the debounced search query changes
  useEffect(() => {
    logger.debug(`Debounced search query changed to: "${searchQuery}"`);
  }, [searchQuery]);

  // UI state
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    // Try to load sidebar state from localStorage
    const savedSidebarState = localStorage.getItem('sidebarOpen');
    // If there's a saved state, use it; otherwise, close sidebar by default
    return savedSidebarState === 'true' ? true : false;
  });

  // State for selected videos to work on in NLM
  const [selectedVideos, setSelectedVideos] = useState<YoutubeVideo[]>([]);

  // State for eliminated videos (excluded from calculations)
  const [eliminatedVideos, setEliminatedVideos] = useState<Set<string>>(new Set());

  // Function to handle video selection
  const handleVideoSelection = (video: YoutubeVideo) => {
    setSelectedVideos(prev => {
      // Check if video is already selected
      if (prev.some(v => v.id === video.id)) {
        // If already selected, remove it
        return prev.filter(v => v.id !== video.id);
      } else {
        // If not selected, add it
        return [...prev, video];
      }
    });
  };

  // Function to handle video elimination
  const handleVideoElimination = (videoId: string) => {
    setEliminatedVideos(prev => {
      const newSet = new Set(prev);
      if (newSet.has(videoId)) {
        newSet.delete(videoId);
      } else {
        newSet.add(videoId);
      }

      // Persist to localStorage
      const eliminatedArray = Array.from(newSet);
      localStorage.setItem('eliminated_videos', JSON.stringify(eliminatedArray));

      return newSet;
    });
  };

  // State for Data Analysis tab
  const [selectedDay, setSelectedDay] = useState<string>("today");
  const [videoMetricsHistory, setVideoMetricsHistory] = useState<{
    timestamp: number;
    newVideosCount: number;
    trendingVideosCount: number;
  }[]>([]);

  // Loading state for all videos (used during refresh)
  const [isLoadingAllVideosState, setIsLoadingAllVideosState] = useState<boolean>(false);

  // State to track open NotebookLM windows
  const [openNotebookLMWindows, setOpenNotebookLMWindows] = useState<{[key: number]: Window | null}>({});

  // State for managing text prompts
  const [prompts, setPrompts] = useState<{id: string; title: string; content: string}[]>([]);

  // Auto-refresh functionality
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState<boolean>(() => {
    // Try to load auto-refresh state from localStorage
    const savedState = localStorage.getItem('realtimeAutoRefreshEnabled');
    // If there's a saved state, use it; otherwise, default to false
    return savedState !== null ? savedState === 'true' : false;
  });

  const [autoRefreshInterval, setAutoRefreshInterval] = useState<number>(() => {
    // Try to load auto-refresh interval from localStorage
    const savedInterval = localStorage.getItem('realtimeAutoRefreshInterval');
    // If there's a saved interval, use it; otherwise, default to 120 seconds (2 minutes)
    return savedInterval ? parseInt(savedInterval) : 120;
  });

  const [lastViewCountRefreshTime, setLastViewCountRefreshTime] = useState<Date | null>(null);
  const [autoRefreshIntervalId, setAutoRefreshIntervalId] = useState<NodeJS.Timeout | null>(null);

  // State for tracking VPH changes
  const [previousVphValues, setPreviousVphValues] = useState<{[videoId: string]: {
    value: number,                // Current VPH value
    highestValue: number,         // Highest recorded VPH value
    previousValue: number,        // Previous measurement VPH value (for momentum tracking)
    timestamp: number,            // Timestamp of current measurement
    previousTimestamp: number,    // Timestamp of previous measurement
    _preRefreshValue?: number,    // Temporary storage for pre-refresh value (used during refresh cycle)
    hasMomentum?: boolean         // Flag to track if a video has momentum (for persistent highlighting)
  }}>(() => {
    // Try to load previous VPH values from localStorage
    const savedValues = localStorage.getItem('previousVphValues');
    if (savedValues) {
      try {
        const parsedValues = JSON.parse(savedValues);

        // Check if we're using the new compact format (with v, h, p, t, pt keys)
        // and convert it back to the full format
        const isCompactFormat = Object.values(parsedValues)[0] &&
                               'v' in Object.values(parsedValues)[0];

        if (isCompactFormat) {
          logger.debug('Converting compact VPH format to full format');
          const fullFormatValues: Record<string, any> = {};

          Object.entries(parsedValues).forEach(([videoId, data]: [string, any]) => {
            fullFormatValues[videoId] = {
              value: data.v,                  // value
              highestValue: data.h,           // highestValue
              previousValue: data.p,          // previousValue
              timestamp: data.t,              // timestamp
              previousTimestamp: data.pt,     // previousTimestamp
              hasMomentum: data.m || false    // hasMomentum (with fallback)
            };
          });

          return fullFormatValues;
        }

        // If it's already in the full format, use it directly

        // Migrate old format to new format if needed
        const migratedValues: {[videoId: string]: {
          value: number,
          highestValue: number,
          previousValue: number,
          timestamp: number,
          previousTimestamp: number
        }} = {};

        Object.entries(parsedValues).forEach(([videoId, data]) => {
          if (typeof data === 'object') {
            // @ts-ignore - Handle potential missing fields in old format
            const currentValue = data.value || 0;
            // @ts-ignore
            const highestValue = data.highestValue !== undefined ? data.highestValue : currentValue;
            // @ts-ignore
            const currentTimestamp = data.timestamp || Date.now();

            migratedValues[videoId] = {
              value: currentValue,
              highestValue: highestValue,
              // For migration, set previousValue equal to current value to avoid showing false momentum
              previousValue: currentValue,
              timestamp: currentTimestamp,
              // For migration, set previousTimestamp to 5 minutes before current timestamp
              previousTimestamp: currentTimestamp - (5 * 60 * 1000),
              // Initialize with no momentum for migrated data
              hasMomentum: false
            };
          }
        });

        return migratedValues;
      } catch (error) {
        logger.error('Error parsing saved VPH values:', error);
        return {};
      }
    }
    return {};
  });
  const [newPromptTitle, setNewPromptTitle] = useState('');
  const [newPromptContent, setNewPromptContent] = useState('');
  const [editingPromptId, setEditingPromptId] = useState<string | null>(null);
  const [isAddingPrompt, setIsAddingPrompt] = useState(false);

  // Load saved prompts from localStorage on component mount
  useEffect(() => {
    const savedPrompts = localStorage.getItem('savedPrompts');
    if (savedPrompts) {
      try {
        setPrompts(JSON.parse(savedPrompts));
      } catch (error) {
        logger.error('Error parsing saved prompts:', error);
      }
    }
  }, []);

  // Load eliminated videos from localStorage on component mount
  useEffect(() => {
    const savedEliminatedVideos = localStorage.getItem('eliminated_videos');
    if (savedEliminatedVideos) {
      try {
        const eliminatedArray = JSON.parse(savedEliminatedVideos);
        setEliminatedVideos(new Set(eliminatedArray));
      } catch (error) {
        logger.error('Error parsing eliminated videos:', error);
      }
    }
  }, []);

  // Save prompts to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('savedPrompts', JSON.stringify(prompts));
  }, [prompts]);

  // Function to clean up old VPH data (older than 30 days)
  const cleanupOldVphData = useCallback(() => {
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 days in milliseconds
    const cleanedData: {[videoId: string]: {
      value: number,
      highestValue: number,
      previousValue: number,
      timestamp: number,
      previousTimestamp: number
    }} = {};

    // Keep only data that's newer than 30 days
    Object.entries(previousVphValues).forEach(([videoId, data]) => {
      if (data.timestamp > thirtyDaysAgo) {
        cleanedData[videoId] = data;
      }
    });

    // Only update if we actually removed something
    if (Object.keys(cleanedData).length < Object.keys(previousVphValues).length) {
      setPreviousVphValues(cleanedData);
      localStorage.setItem('previousVphValues', JSON.stringify(cleanedData));
      logger.debug(`Cleaned up VPH data: Removed ${Object.keys(previousVphValues).length - Object.keys(cleanedData).length} old entries`);
    }
  }, [previousVphValues]);

  // Function to clean up VPH data for videos that are no longer in the feed
  const cleanupUnusedVphData = useCallback(() => {
    if (displayedVideos.length === 0) return;

    // Get the set of video IDs currently displayed
    const currentVideoIds = new Set(displayedVideos.map(video => video.id));

    // Keep only data for videos that are currently displayed
    const cleanedData: {[videoId: string]: {
      value: number,
      highestValue: number,
      previousValue: number,
      timestamp: number,
      previousTimestamp: number
    }} = {};
    let removedCount = 0;

    Object.entries(previousVphValues).forEach(([videoId, data]) => {
      if (currentVideoIds.has(videoId)) {
        cleanedData[videoId] = data;
      } else {
        removedCount++;
      }
    });

    // Only update if we actually removed something
    if (removedCount > 0) {
      setPreviousVphValues(cleanedData);
      localStorage.setItem('previousVphValues', JSON.stringify(cleanedData));
      logger.debug(`Cleaned up VPH data: Removed ${removedCount} entries for videos no longer in feed`);
    }
  }, [displayedVideos, previousVphValues]);

  // Function to clean up stale VPH data (older than 7 days)
  const cleanupStaleVphData = useCallback(() => {
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days in milliseconds
    const cleanedData: {[videoId: string]: {
      value: number,
      highestValue: number,
      previousValue: number,
      timestamp: number,
      previousTimestamp: number
    }} = {};
    let removedCount = 0;

    // Keep only data that's newer than 7 days
    Object.entries(previousVphValues).forEach(([videoId, data]) => {
      if (data.timestamp > sevenDaysAgo) {
        cleanedData[videoId] = data;
      } else {
        removedCount++;
      }
    });

    // Only update if we actually removed something
    if (removedCount > 0) {
      setPreviousVphValues(cleanedData);
      localStorage.setItem('previousVphValues', JSON.stringify(cleanedData));
      logger.debug(`Cleaned up VPH data: Removed ${removedCount} stale entries older than 7 days`);
    }
  }, [previousVphValues]);

  // Save previous VPH values to localStorage whenever they change
  // Use a debounced approach to avoid excessive writes to localStorage
  useEffect(() => {
    // Create a debounced function to save to localStorage
    const debouncedSave = () => {
      // Only save if we have data to save
      if (Object.keys(previousVphValues).length > 0) {
        // Clean up old data before saving to reduce storage size
        cleanupOldVphData();

        // Use a more memory-efficient approach by only storing essential data
        const compactData: Record<string, any> = {};

        Object.entries(previousVphValues).forEach(([videoId, data]) => {
          // Only store the essential fields, not the full object
          compactData[videoId] = {
            v: data.value,                  // value
            h: data.highestValue,           // highestValue
            p: data.previousValue,          // previousValue
            t: data.timestamp,              // timestamp
            pt: data.previousTimestamp,     // previousTimestamp
            m: data.hasMomentum || false    // hasMomentum
          };
        });

        try {
          localStorage.setItem('previousVphValues', JSON.stringify(compactData));
          logger.debug(`Saved ${Object.keys(compactData).length} VPH entries to localStorage (optimized format)`);
        } catch (error) {
          logger.error('Error saving to localStorage:', error);
          // If we hit storage limits, clear some data and try again
          if (error instanceof DOMException && error.name === 'QuotaExceededError') {
            logger.warn('localStorage quota exceeded, clearing old data');
            // Force a more aggressive cleanup
            const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
            const reducedData: Record<string, any> = {};

            // Keep only the most recent entries
            Object.entries(compactData).forEach(([videoId, data]) => {
              if (data.t > thirtyDaysAgo) {
                reducedData[videoId] = data;
              }
            });

            // Try saving the reduced data
            try {
              localStorage.setItem('previousVphValues', JSON.stringify(reducedData));
              logger.debug(`Saved ${Object.keys(reducedData).length} VPH entries after aggressive cleanup`);
            } catch (retryError) {
              // If still failing, clear everything
              logger.error('Still cannot save to localStorage, clearing all VPH data');
              localStorage.removeItem('previousVphValues');
            }
          }
        }
      }
    };

    // Use a timeout to debounce the save operation
    const saveTimeout = setTimeout(debouncedSave, 2000);

    // Clean up the timeout
    return () => clearTimeout(saveTimeout);
  }, [previousVphValues, cleanupOldVphData]);

  // Initialize previous VPH values when videos are loaded
  useEffect(() => {
    if (displayedVideos.length > 0) {
      // Only initialize for videos that don't already have previous values
      const newValues: {[videoId: string]: {
        value: number,
        highestValue: number,
        previousValue: number,
        timestamp: number,
        previousTimestamp: number
      }} = {};
      let hasNewValues = false;
      const currentTimestamp = Date.now();

      displayedVideos.forEach(video => {
        if (video.id && video.vph !== undefined && previousVphValues[video.id] === undefined) {
          newValues[video.id] = {
            value: video.vph,
            highestValue: video.vph,
            previousValue: video.vph, // For new videos, set previous value equal to current value
            timestamp: currentTimestamp,
            previousTimestamp: currentTimestamp - (5 * 60 * 1000) // Set previous timestamp to 5 minutes ago
          };
          hasNewValues = true;
        }
      });

      if (hasNewValues) {
        setPreviousVphValues(prev => ({
          ...prev,
          ...newValues
        }));
      }
    }
  }, [displayedVideos, previousVphValues]);

  // Function to clean up metrics data to prevent it from growing too large
  // while ensuring we maintain a full week of data for analysis
  const cleanupMetricsData = useCallback((data: {
    timestamp: number;
    newVideosCount: number;
    trendingVideosCount: number;
  }[]) => {
    // Quick return for empty data
    if (!data || data.length === 0) return [];

    // Make a copy of the data to avoid mutating the original
    let processedData = [...data];

    // Sort the data by timestamp to ensure chronological order
    processedData.sort((a, b) => a.timestamp - b.timestamp);

    // Remove duplicate timestamps to ensure data integrity
    const uniqueTimestamps = new Map();
    processedData.forEach(item => {
      // If we have multiple entries for the same timestamp, keep the one with higher counts
      if (!uniqueTimestamps.has(item.timestamp) ||
          (item.newVideosCount + item.trendingVideosCount >
           uniqueTimestamps.get(item.timestamp).newVideosCount +
           uniqueTimestamps.get(item.timestamp).trendingVideosCount)) {
        uniqueTimestamps.set(item.timestamp, item);
      }
    });

    // Convert back to array
    processedData = Array.from(uniqueTimestamps.values());

    // Keep only the last 3 days of data to reduce localStorage usage
    const threeDaysAgo = Date.now() - (3 * 24 * 60 * 60 * 1000);

    // Filter out data older than 3 days
    let filteredData = processedData.filter(item => item.timestamp > threeDaysAgo);

    // If we filtered out all data, keep at least the most recent data point
    if (filteredData.length === 0 && processedData.length > 0) {
      // Find the most recent data point
      const mostRecent = processedData.reduce((latest, current) =>
        current.timestamp > latest.timestamp ? current : latest, processedData[0]);
      filteredData = [mostRecent];
    }

    // Ensure we don't have data points too close to each other
    // But make sure we keep enough points for the chart to display properly
    if (filteredData.length > 1) {
      // Use a 2-minute interval to match our collection frequency
      const minTimeBetweenPoints = 2 * 60 * 1000; // 2 minutes in milliseconds
      const timeFilteredData = [filteredData[0]]; // Always keep the first point

      for (let i = 1; i < filteredData.length; i++) {
        const timeSinceLastPoint = filteredData[i].timestamp - timeFilteredData[timeFilteredData.length - 1].timestamp;

        // Keep points that are at least 2 minutes apart
        if (timeSinceLastPoint >= minTimeBetweenPoints) {
          timeFilteredData.push(filteredData[i]);
        }
        // Always include the last point to ensure current data is shown
        else if (i === filteredData.length - 1) {
          // Replace the last point if this one is newer
          if (filteredData[i].timestamp > timeFilteredData[timeFilteredData.length - 1].timestamp) {
            timeFilteredData[timeFilteredData.length - 1] = filteredData[i];
          }
        }
      }

      // Only use time filtering if we'd still have enough points for a meaningful chart
      if (timeFilteredData.length >= 3) {
        filteredData = timeFilteredData;
      }
    }

    // Set a reasonable maximum data points for performance while ensuring enough for visualization
    // Reduced to 50 to significantly reduce localStorage usage while still providing good visualization
    const MAX_DATA_POINTS = 50;

    if (filteredData.length > MAX_DATA_POINTS) {
      // Optimize by avoiding unnecessary sorting if data is already in chronological order
      // Most time-series data is already ordered, so we can check first and avoid sort
      const isOrdered = filteredData.every((item, i) =>
        i === 0 || item.timestamp >= filteredData[i-1].timestamp
      );

      if (!isOrdered) {
        // Only sort if necessary
        filteredData.sort((a, b) => a.timestamp - b.timestamp);
      }

      // Use a more efficient downsampling algorithm
      // This algorithm preserves important features like peaks and valleys
      const downsampledData = [];
      const bucketSize = Math.ceil(filteredData.length / MAX_DATA_POINTS);

      // Always include the first point
      downsampledData.push(filteredData[0]);

      // Process middle points using a more efficient algorithm
      for (let i = 1; i < filteredData.length - 1; i += bucketSize) {
        const bucket = filteredData.slice(i, Math.min(i + bucketSize, filteredData.length - 1));

        if (bucket.length > 0) {
          // Find min and max for new and trending counts in this bucket
          let maxNewCount = -Infinity;
          let maxTrendingCount = -Infinity;
          let maxNewIndex = 0;
          let maxTrendingIndex = 0;

          for (let j = 0; j < bucket.length; j++) {
            if (bucket[j].newVideosCount > maxNewCount) {
              maxNewCount = bucket[j].newVideosCount;
              maxNewIndex = j;
            }
            if (bucket[j].trendingVideosCount > maxTrendingCount) {
              maxTrendingCount = bucket[j].trendingVideosCount;
              maxTrendingIndex = j;
            }
          }

          // Add the point with max new videos if it's significant
          if (maxNewCount > 0) {
            downsampledData.push(bucket[maxNewIndex]);
          }

          // Add the point with max trending videos if it's different and significant
          if (maxTrendingCount > 0 && maxTrendingIndex !== maxNewIndex) {
            downsampledData.push(bucket[maxTrendingIndex]);
          }

          // If neither had significant values, just add the middle point
          if (maxNewCount === 0 && maxTrendingCount === 0) {
            downsampledData.push(bucket[Math.floor(bucket.length / 2)]);
          }
        }
      }

      // Always include the last point for current state
      if (filteredData.length > 0) {
        downsampledData.push(filteredData[filteredData.length - 1]);
      }

      // Remove any duplicates that might have been added
      const uniqueData = [];
      const seen = new Set();

      for (const item of downsampledData) {
        if (!seen.has(item.timestamp)) {
          seen.add(item.timestamp);
          uniqueData.push(item);
        }
      }

      // Sort the final data by timestamp
      uniqueData.sort((a, b) => a.timestamp - b.timestamp);

      return uniqueData;
    }

    return filteredData;
  }, []);

  // Track the last time metrics were collected to prevent too frequent updates
  const [lastMetricsCollectionTime, setLastMetricsCollectionTime] = useState<number>(0);

  // Function to clean up localStorage backups to prevent quota exceeded errors
  const cleanupLocalStorageBackups = useCallback((aggressive = false) => {
    try {
      const keysToRemove: string[] = [];
      const backupKeys: { key: string; timestamp: number }[] = [];

      // Find all backup keys and other large data keys
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          if (key.startsWith('metrics_backup_')) {
            const timestamp = parseInt(key.replace('metrics_backup_', ''));
            if (!isNaN(timestamp)) {
              backupKeys.push({ key, timestamp });
            }
          } else if (aggressive && (
            key.startsWith('ytr_ranking_history') ||
            key.startsWith('videoMetricsHistory') ||
            key.startsWith('cached_') ||
            key.includes('_cache')
          )) {
            // In aggressive mode, also clean up other large data
            try {
              const data = localStorage.getItem(key);
              if (data && data.length > 50000) { // If larger than 50KB
                keysToRemove.push(key);
              }
            } catch (e) {
              // If we can't read it, remove it
              keysToRemove.push(key);
            }
          }
        }
      }

      // Sort backup keys by timestamp (oldest first)
      backupKeys.sort((a, b) => a.timestamp - b.timestamp);

      // Remove old backup keys (keep only the last 5)
      const maxBackups = aggressive ? 2 : 5;
      if (backupKeys.length > maxBackups) {
        const keysToRemoveFromBackups = backupKeys.slice(0, backupKeys.length - maxBackups);
        keysToRemoveFromBackups.forEach(({ key }) => keysToRemove.push(key));
      }

      // Remove old backup keys (older than 24 hours)
      const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
      backupKeys.forEach(({ key, timestamp }) => {
        if (timestamp < oneDayAgo && !keysToRemove.includes(key)) {
          keysToRemove.push(key);
        }
      });

      // Remove the identified keys
      keysToRemove.forEach(key => {
        try {
          localStorage.removeItem(key);
          console.log(`Cleaned up localStorage key: ${key}`);
        } catch (e) {
          console.error(`Failed to remove localStorage key ${key}:`, e);
        }
      });

      if (keysToRemove.length > 0) {
        console.log(`Cleaned up ${keysToRemove.length} localStorage entries to free up space`);
      }

      // Also clean up ranking history if it's too large
      try {
        const rankingHistory = localStorage.getItem('ytr_ranking_history');
        if (rankingHistory) {
          const parsed = JSON.parse(rankingHistory);
          if (Array.isArray(parsed) && parsed.length > 1000) {
            // Keep only the last 500 entries
            const trimmed = parsed.slice(-500);
            localStorage.setItem('ytr_ranking_history', JSON.stringify(trimmed));
            console.log(`Trimmed ranking history from ${parsed.length} to ${trimmed.length} entries`);
          }
        }
      } catch (e) {
        console.error('Error cleaning up ranking history:', e);
        localStorage.removeItem('ytr_ranking_history');
      }

    } catch (error) {
      console.error('Error during localStorage cleanup:', error);
    }
  }, []);

  // Function to collect metrics for Data Analysis tab
  const collectMetricsData = useCallback(() => {
    if (displayedVideos.length === 0) return;

    // Prevent collecting metrics too frequently (minimum 2 minutes between collections)
    const now = Date.now();
    const timeSinceLastCollection = now - lastMetricsCollectionTime;
    const minCollectionInterval = 2 * 60 * 1000; // 2 minutes in milliseconds

    if (lastMetricsCollectionTime > 0 && timeSinceLastCollection < minCollectionInterval) {
      logger.verbose(`Skipping metrics collection - last collection was ${timeSinceLastCollection / 1000}s ago`);
      return;
    }

    // Update the last collection time
    setLastMetricsCollectionTime(now);

    // Count new videos (published in the last 6 hours)
    const newVideosCount = displayedVideos.filter(video => {
      const publishedDate = new Date(video.publishedAt);
      const now = new Date();
      const sixHoursAgo = new Date(now.getTime() - 6 * 60 * 60 * 1000);
      return publishedDate > sixHoursAgo;
    }).length;

    // Count trending videos
    const trendingVideosCount = displayedVideos.filter(video => {
      if (previousVphValues[video.id]) {
        const prevVphData = previousVphValues[video.id];
        const previousVph = prevVphData.previousValue || 0;
        const currentVph = video.vph || 0;
        const momentumDelta = currentVph - previousVph;
        const momentumThreshold = Math.max(2, previousVph * 0.01);
        const timeSincePrevious = prevVphData.timestamp - prevVphData.previousTimestamp;
        const minutesSincePrevious = timeSincePrevious / (1000 * 60);

        return momentumDelta > 0 && Math.abs(momentumDelta) >= momentumThreshold && minutesSincePrevious >= 5;
      }
      return false;
    }).length;

    // Create the metrics data object
    const metricsData = {
      timestamp: now,
      newVideosCount,
      trendingVideosCount
    };

    // Save to local state
    setVideoMetricsHistory(prev => {
      // Add the new data point
      const updatedHistory = [...prev, metricsData];

      // Clean up the data to prevent it from growing too large
      return cleanupMetricsData(updatedHistory);
    });

    // Save to database via API with better error handling and retry logic
    const saveToDatabase = (retryCount = 0) => {
      const maxRetries = 3;

      // Check if user is authenticated before making the API call
      const isAuthenticated = document.cookie.includes('connect.sid=');

      if (!isAuthenticated) {
        logger.verbose('User not authenticated, skipping metrics save to database');
        // Clean up old backup data before saving new data
        cleanupLocalStorageBackups();
        // Save to localStorage as a backup
        const backupKey = `metrics_backup_${metricsData.timestamp}`;
        try {
          localStorage.setItem(backupKey, JSON.stringify(metricsData));
          logger.verbose(`Saved metrics to localStorage backup with key ${backupKey}`);
        } catch (error) {
          console.error('Failed to save to localStorage, cleaning up and retrying:', error);
          // Clean up more aggressively and try again
          cleanupLocalStorageBackups(true);
          try {
            localStorage.setItem(backupKey, JSON.stringify(metricsData));
            logger.verbose(`Saved metrics to localStorage backup with key ${backupKey} after cleanup`);
          } catch (retryError) {
            console.error('Failed to save to localStorage even after cleanup:', retryError);
          }
        }
        return;
      }

      fetch('/api/video-metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metricsData),
        // Ensure credentials are included
        credentials: 'include'
      })
      .then(response => {
        if (!response.ok) {
          console.error(`Failed to save metrics to database (attempt ${retryCount + 1}/${maxRetries}):`, response.statusText);

          // Retry logic for server errors (5xx)
          if (response.status >= 500 && retryCount < maxRetries) {
            console.log(`Retrying in ${(retryCount + 1) * 2} seconds...`);
            setTimeout(() => saveToDatabase(retryCount + 1), (retryCount + 1) * 2000);
          }
        } else {
          console.log(`Successfully saved metrics to database at ${new Date(metricsData.timestamp).toLocaleTimeString()}`);

          // Set a flag in localStorage to indicate we have data in the database
          localStorage.setItem('hasMetricsInDatabase', 'true');
        }
        return response.json().catch(() => ({})); // Handle empty responses
      })
      .catch(error => {
        console.error(`Error saving metrics to database (attempt ${retryCount + 1}/${maxRetries}):`, error);

        // Retry on network errors
        if (retryCount < maxRetries) {
          console.log(`Retrying in ${(retryCount + 1) * 2} seconds...`);
          setTimeout(() => saveToDatabase(retryCount + 1), (retryCount + 1) * 2000);
        } else {
          // If all retries fail, save to localStorage as a backup
          cleanupLocalStorageBackups();
          const backupKey = `metrics_backup_${metricsData.timestamp}`;
          try {
            localStorage.setItem(backupKey, JSON.stringify(metricsData));
            console.log(`Saved metrics to localStorage backup with key ${backupKey}`);
          } catch (error) {
            console.error('Failed to save to localStorage, cleaning up and retrying:', error);
            cleanupLocalStorageBackups(true);
            try {
              localStorage.setItem(backupKey, JSON.stringify(metricsData));
              console.log(`Saved metrics to localStorage backup with key ${backupKey} after cleanup`);
            } catch (retryError) {
              console.error('Failed to save to localStorage even after cleanup:', retryError);
            }
          }
        }
      });
    };

    // Start the save process
    saveToDatabase();

  }, [displayedVideos, previousVphValues, cleanupMetricsData, lastMetricsCollectionTime]);

  // Load metrics data from database and localStorage on component mount - using eager loading approach
  useEffect(() => {
    // First, perform immediate cleanup to prevent quota issues
    cleanupLocalStorageBackups(true);

    // Create a function to load data that we can call when needed
    const loadMetricsData = () => {
      // Always try to load from database, even if we have data in memory
      // This ensures we have the most up-to-date data from the database
      logger.verbose('Loading metrics data from database...');

      // Check if user is authenticated before making the API call
      const isAuthenticated = document.cookie.includes('connect.sid=');

      if (!isAuthenticated) {
        logger.verbose('User not authenticated, skipping metrics load from database');
        // Fall back to localStorage
        fallbackToLocalStorage();
        return;
      }

      fetch('/api/video-metrics', {
        credentials: 'include' // Ensure credentials are included
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to fetch metrics from database');
          }
          return response.json();
        })
        .then(data => {
          if (Array.isArray(data) && data.length > 0) {
            logger.verbose(`Loaded ${data.length} metrics data points from database`);

            // Apply cleanup to ensure we don't load too much data
            const cleanedMetrics = cleanupMetricsData(data);

            // Only update if we have more or newer data than what's currently in memory
            if (cleanedMetrics.length > 0 && (
                videoMetricsHistory.length === 0 ||
                cleanedMetrics.length > videoMetricsHistory.length ||
                cleanedMetrics[cleanedMetrics.length - 1].timestamp >
                  (videoMetricsHistory.length > 0 ? videoMetricsHistory[videoMetricsHistory.length - 1].timestamp : 0)
            )) {
              console.log(`Updating metrics history with ${cleanedMetrics.length} data points from database`);
              setVideoMetricsHistory(cleanedMetrics);

              // Also update localStorage as a backup
              localStorage.setItem('videoMetricsHistory', JSON.stringify(cleanedMetrics));
            } else {
              console.log('No new metrics data found in database');
            }
          } else {
            console.log('No metrics data found in database, checking localStorage');
            // If no data in database, fall back to localStorage
            fallbackToLocalStorage();
          }
        })
        .catch(error => {
          console.error('Error loading metrics from database:', error);
          // Fall back to localStorage if database fetch fails
          fallbackToLocalStorage();
        });
    };

    // Fallback function to load from localStorage and ensure data is saved to database
    const fallbackToLocalStorage = () => {
      try {
        const savedMetrics = localStorage.getItem('videoMetricsHistory');
        if (!savedMetrics) {
          console.log('No metrics data found in localStorage');
          return;
        }

        // Parse the saved metrics
        const parsedMetrics = JSON.parse(savedMetrics);
        if (!Array.isArray(parsedMetrics) || parsedMetrics.length === 0) {
          console.log('No valid metrics data found in localStorage');
          return;
        }

        // Apply cleanup to ensure we don't load too much data
        const cleanedMetrics = cleanupMetricsData(parsedMetrics);
        logger.verbose(`Loaded ${cleanedMetrics.length} metrics data points from localStorage`);

        // Update the state with the cleaned metrics
        setVideoMetricsHistory(cleanedMetrics);

        // If the cleanup reduced the data size significantly, save it back to localStorage
        if (cleanedMetrics.length < parsedMetrics.length * 0.8) { // If we reduced by more than 20%
          console.log(`Cleanup reduced metrics data by more than 20%, saving back to localStorage`);
          localStorage.setItem('videoMetricsHistory', JSON.stringify(cleanedMetrics));
        }

        // Save to database for future use with better error handling and batching
        if (cleanedMetrics.length > 0) {
          // Check if user is authenticated before making the API call
          const isAuthenticated = document.cookie.includes('connect.sid=');

          if (!isAuthenticated) {
            logger.verbose('User not authenticated, skipping metrics sync to database');
            return;
          }

          logger.verbose(`Saving ${cleanedMetrics.length} metrics data points to database`);

          // Use Promise.all to batch the requests and track completion
          const savePromises = cleanedMetrics.map(metric =>
            fetch('/api/video-metrics', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(metric),
              credentials: 'include' // Ensure credentials are included
            })
            .then(response => {
              if (!response.ok) {
                throw new Error(`Failed to save metric at timestamp ${new Date(metric.timestamp).toISOString()}`);
              }
              return response.json();
            })
          );

          // Handle the batch operation
          Promise.all(savePromises)
            .then(() => {
              console.log(`Successfully saved all ${cleanedMetrics.length} metrics to database`);
              toast({
                title: "Data Synchronized",
                description: `Saved ${cleanedMetrics.length} metrics to database`,
                duration: 3000
              });
            })
            .catch(error => {
              console.error('Error saving some metrics to database:', error);
              toast({
                title: "Partial Data Sync",
                description: "Some metrics may not have been saved to the database",
                variant: "warning",
                duration: 3000
              });
            });
        }
      } catch (error) {
        console.error('Error parsing saved metrics data:', error);
        // If there was an error, don't reset the data completely, just log the error
        toast({
          title: "Error",
          description: "Failed to load metrics from localStorage",
          variant: "destructive",
          duration: 3000
        });
      }
    };

    // Set up a listener for tab changes to load data when switching to the DA tab
    const handleTabChange = () => {
      if (window.location.hash === '#data-analysis') {
        console.log('Switched to Data Analysis tab, loading metrics data');
        // Always load fresh data when the DA tab is selected
        loadMetricsData();
      }
    };

    // Add event listener for hash changes
    window.addEventListener('hashchange', handleTabChange);

    // Set up periodic data refresh for the metrics
    const setupPeriodicRefresh = () => {
      // Set up an interval to refresh the data every 5 minutes
      const refreshInterval = setInterval(() => {
        // Only refresh if we're on the DA tab or if we have no data
        if (window.location.hash === '#data-analysis' || videoMetricsHistory.length === 0) {
          console.log('Periodic refresh of metrics data');
          loadMetricsData();
        }
      }, 5 * 60 * 1000); // 5 minutes

      return refreshInterval;
    };

    // Start the periodic refresh
    const refreshInterval = setupPeriodicRefresh();

    // Always load data on component mount, regardless of which tab we're on
    // This ensures we have data ready when the user switches to the DA tab
    logger.debug('Component mounted, loading initial metrics data');
    loadMetricsData();

    // Check for and recover any backed-up metrics data from localStorage
    const recoverBackupMetrics = () => {
      // Find all backup keys in localStorage
      const backupKeys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('metrics_backup_')) {
          backupKeys.push(key);
        }
      }

      if (backupKeys.length > 0) {
        logger.debug(`Found ${backupKeys.length} backed-up metrics data points to recover`);

        // Process each backup in sequence
        const processNextBackup = (index = 0) => {
          if (index >= backupKeys.length) {
            logger.debug('All backed-up metrics data recovered');
            return;
          }

          const key = backupKeys[index];
          try {
            const backupData = JSON.parse(localStorage.getItem(key) || '');

            // Check if user is authenticated before making the API call
            const isAuthenticated = document.cookie.includes('connect.sid=');

            if (!isAuthenticated) {
              logger.debug(`User not authenticated, skipping recovery of backup ${key}`);
              // Process the next backup
              processNextBackup(index + 1);
              return;
            }

            // Save to database
            fetch('/api/video-metrics', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(backupData),
              credentials: 'include' // Ensure credentials are included
            })
            .then(response => {
              if (response.ok) {
                logger.debug(`Successfully recovered backup data from ${key}`);
                // Remove the backup after successful recovery
                localStorage.removeItem(key);
              } else {
                logger.error(`Failed to recover backup data from ${key}:`, response.statusText);
              }

              // Process the next backup
              processNextBackup(index + 1);
            })
            .catch(error => {
              logger.error(`Error recovering backup data from ${key}:`, error);
              // Process the next backup even if this one failed
              processNextBackup(index + 1);
            });
          } catch (error) {
            logger.error(`Error parsing backup data from ${key}:`, error);
            // Remove invalid backup data
            localStorage.removeItem(key);
            // Process the next backup
            processNextBackup(index + 1);
          }
        };

        // Start processing backups
        processNextBackup();
      }
    };

    // Run the recovery process after a short delay to allow the component to finish mounting
    setTimeout(recoverBackupMetrics, 5000);

    return () => {
      // Clean up event listeners and intervals
      window.removeEventListener('hashchange', handleTabChange);
      clearInterval(refreshInterval);
    };
  }, [videoMetricsHistory.length, cleanupMetricsData]);

  // Use localStorage to track window names across page refreshes
  useEffect(() => {
    // On component mount, check if we have stored window names
    const storedWindowNames = localStorage.getItem('notebookLMWindowNames');
    if (storedWindowNames) {
      try {
        const windowNames = JSON.parse(storedWindowNames) as string[];
        // For each stored window name, check if the window exists
        windowNames.forEach((name, index) => {
          if (name) {
            // Try to find the window by name
            const existingWindow = window.open('', name);
            if (existingWindow && !existingWindow.closed && existingWindow.location.href.includes('notebooklm.google.com')) {
              // If we found a valid window, add it to our state
              setOpenNotebookLMWindows(prev => ({
                ...prev,
                [index + 1]: existingWindow
              }));
            }
          }
        });
      } catch (error) {
        logger.error('Error parsing stored window names:', error);
      }
    }
  }, []);

  // Track total videos across all channels
  const [totalChannelVideos, setTotalChannelVideos] = useState<number>(0);

  // Get the sidebar context for the toggle button
  const { toggleSidebar } = useSidebarCollapse();

  // Save sidebar state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarOpen', sidebarOpen.toString());
  }, [sidebarOpen]);
  const [activeTab, setActiveTab] = useState<string>("ytr");
  const [refreshStartTime, setRefreshStartTime] = useState<number | null>(null);
  const [refreshDuration, setRefreshDuration] = useState<number | null>(null);
  const [recentTasksVisible, setRecentTasksVisible] = useState<boolean>(true);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const [rssLastRefreshTime, setRssLastRefreshTime] = useState<Date | null>(null);
  const [refreshStatus, setRefreshStatus] = useState<{
    isRefreshing: boolean;
    currentChannel: string | null;
    progress: number;
    totalChannels: number;
    completedChannels: number;
  }>({
    isRefreshing: false,
    currentChannel: null,
    progress: 0,
    totalChannels: 0,
    completedChannels: 0
  });

  // Run cleanup functions periodically
  useEffect(() => {
    // Run cleanup when component mounts
    cleanupOldVphData();
    cleanupStaleVphData();

    // Memory optimization function to reduce memory usage
    const optimizeMemory = () => {
      logger.debug('Running memory optimization to reduce memory usage');

      // Force garbage collection of React Query cache for unused queries
      // In TanStack Query v5, we use refetchQueries with type: 'inactive' instead of gc()
      queryClient.refetchQueries({ type: 'inactive', stale: true });

      // Clean up unused data when on the realtime tab
      if (activeTab === "realtime" && displayedVideos.length > 0) {
        cleanupUnusedVphData();
      }

      // Clean up stale data
      cleanupStaleVphData();

      // Clean up old data
      cleanupOldVphData();

      // Clean up video metrics history if it's too large
      if (videoMetricsHistory.length > 100) {
        logger.debug(`Cleaning up videoMetricsHistory (${videoMetricsHistory.length} entries)`);
        setVideoMetricsHistory(prev => cleanupMetricsData(prev));
      }

      // Trim the video cache if it's getting too large and if cachedVideos is available
      if (typeof cachedVideos !== 'undefined' && cachedVideos?.length > 100 && typeof trimCache === 'function') {
        logger.debug(`Trimming video cache from ${cachedVideos.length} to 100 videos to reduce memory usage`);
        trimCache(100);
      }

      // Force a more aggressive garbage collection
      if (typeof window !== 'undefined') {
        // Clear any unused objects from memory
        if (window.gc) {
          try {
            // @ts-ignore - This is a non-standard browser API
            window.gc();
            logger.debug('Forced garbage collection');
          } catch (e) {
            // Ignore errors - gc() is not available in all browsers
          }
        }

        // Clear browser caches that might be using memory
        if (window.caches && typeof window.caches.keys === 'function') {
          try {
            window.caches.keys().then(cacheNames => {
              cacheNames.forEach(cacheName => {
                if (cacheName.includes('image-cache')) {
                  window.caches.delete(cacheName);
                }
              });
            });
          } catch (e) {
            // Ignore errors with cache API
          }
        }
      }
    };

    // Set up interval to run cleanup functions
    const cleanupInterval = setInterval(() => {
      // Run standard cleanup every hour
      cleanupStaleVphData();
      cleanupOldVphData();
    }, 60 * 60 * 1000); // Run every hour

    // Set up a separate interval for memory optimization (every 5 minutes)
    const memoryOptimizationInterval = setInterval(optimizeMemory, 5 * 60 * 1000);

    // Run memory optimization once on mount
    optimizeMemory();

    return () => {
      clearInterval(cleanupInterval);
      clearInterval(memoryOptimizationInterval);
    };
  }, [activeTab, cleanupOldVphData, cleanupStaleVphData, cleanupUnusedVphData, displayedVideos.length, videoMetricsHistory.length, cleanupMetricsData, queryClient]);

  // Get YouTube channels
  const {
    channels,
    isLoading: isLoadingChannels,
    createChannel,
    updateChannel,
    deleteChannel,
    deleteChannelData,
    deleteAllChannelsData,
    useChannelVideos,
    useAllChannelVideos,
    refetch: refetchChannels,
    channelVideoCounts,
    isLoadingVideoCounts,
    refreshAllChannels,
  } = useYoutubeChannels();

  // AI batch analysis
  const analyzeChannelWithOpenRouter = useAnalyzeChannelWithOpenRouter();

  // Refresh metadata only (no transcripts or AI analysis)
  const { refreshMetadata } = useRefreshMetadata();

  // Use our stable video cache
  const { cachedVideos, updateCache, getMergedVideos, clearCache } = useCachedVideos();

  // Calculate total videos across all channels when channelVideoCounts changes
  // Use a lazy calculation approach to avoid blocking the UI
  useEffect(() => {
    if (Object.keys(channelVideoCounts).length > 0) {
      // Use a setTimeout to defer the calculation to the next tick
      // This allows the UI to render first before doing this calculation
      setTimeout(() => {
        const total = Object.values(channelVideoCounts).reduce((sum, count) => sum + (count || 0), 0);
        setTotalChannelVideos(total);
        logger.debug(`Total videos across all channels: ${total}`);
      }, 0);
    }
  }, [channelVideoCounts]);

  // Get background task functionality
  const { getVideoTranscription, refreshChannelVideos, analyzeFinancialBenefits, pendingTranscriptions, pendingChannelRefreshes } = useYoutubeBackgroundTasks();
  const { recentTasks, tasks } = useBackgroundTasks();

  // Get videos for the selected channel
  const {
    data: channelVideos = [],
    isLoading: isLoadingVideos,
    refetch: refetchVideos,
  } = useChannelVideos(selectedChannelId);

  // Only prefetch videos for the selected channel when it changes
  useEffect(() => {
    if (selectedChannelId !== null) {
      logger.debug(`Prefetching videos for selected channel ${selectedChannelId}`);
      queryClient.prefetchQuery({
        queryKey: [`/api/youtube-channels/${selectedChannelId}/videos`],
        queryFn: async () => {
          const res = await apiRequest("GET", `/api/youtube-channels/${selectedChannelId}/videos`);
          return res.json();
        },
      });
    }
  }, [selectedChannelId]);

  // Current page is already declared above

  // Get all videos from all channels with pagination
  // Log the current sort key for debugging
  useEffect(() => {
    logger.debug(`Current sort key: sortBy=${sortBy}, querySortKey=${querySortKey}`);
  }, [sortBy, querySortKey]);

  const {
    data: videosData,
    isLoading: isLoadingAllVideos,
    refetch: refetchAllVideos,
  } = useAllChannelVideos(currentPage, videosPerPage, {
    sortBy: sortBy, // Use the sortBy state directly
    searchQuery,
    durationFilter,
    // When no channel is selected (All Channels view), fetch all videos at once
    fetchAll: selectedChannelId === null,
    // Pass the selected channel ID if one is selected
    channelId: selectedChannelId ? channels.find(c => c.id === selectedChannelId)?.channelId : null
  });

  // Extract videos and pagination info
  const allChannelVideos = videosData?.videos || [];
  const paginationInfo = videosData?.pagination;

  // Effect to refetch videos when sort changes
  // We don't need this effect anymore since we're handling refetching in the onValueChange handler
  // and in the updateSort function directly
  /*
  useEffect(() => {
    // Skip on initial render
    if (userInitiatedSortRef.current) {
      console.log(`Sort changed to ${querySortKey}, refetching videos...`);
      // Force a refetch with the new sort parameter
      setTimeout(() => {
        refetchAllVideos();
      }, 100);
    }
  }, [querySortKey, refetchAllVideos]);
  */

  // Log the number of videos from each channel
  useEffect(() => {
    if (allChannelVideos.length > 0) {
      const videosByChannel: Record<string, number> = {};
      allChannelVideos.forEach(video => {
        const channelTitle = video.channelTitle || 'Unknown';
        videosByChannel[channelTitle] = (videosByChannel[channelTitle] || 0) + 1;
      });

      logger.debug('Videos by channel in allChannelVideos:', videosByChannel);

      // Check if any channels are missing
      const channelsInVideos = Object.keys(videosByChannel);
      const allChannelTitles = channels.map(c => c.channelTitle);
      const missingChannels = allChannelTitles.filter(title => !channelsInVideos.includes(title));

      if (missingChannels.length > 0) {
        logger.warn(`Missing videos from these channels: ${missingChannels.join(', ')}`);
      }
    }
  }, [allChannelVideos, channels]);

  // We no longer need to fetch videos directly on page load
  // The useAllChannelVideos hook handles this with pagination

  // Load preferences from localStorage - only on initial mount
  useEffect(() => {
    console.log(`[LOCALSTORAGE EFFECT] Loading preferences from localStorage`);

    const storedGridSize = localStorage.getItem('preferredGridSize');
    if (storedGridSize) {
      setGridSize(parseInt(storedGridSize));
    }

    // Only use the stored sort method if it's explicitly set by the user and is valid
    const storedSortMethod = localStorage.getItem('youtubePageSortMethod');
    console.log(`[LOCALSTORAGE EFFECT] Found YouTube sort preference in localStorage: ${storedSortMethod}`);

    if (storedSortMethod) {
      // Validate that the saved sort is still in our options
      const isValidSort = COMBINED_SORT_OPTIONS.some(option => option.value === storedSortMethod);
      if (isValidSort) {
        console.log(`[LOCALSTORAGE EFFECT] Updating sort to: ${storedSortMethod}`);
        // Update the state and query key
        setSortBy(storedSortMethod as SortOption);
        setQuerySortKey(storedSortMethod as SortOption);
      } else {
        // If the sort is invalid, remove it from localStorage
        console.log(`[LOCALSTORAGE EFFECT] Removing invalid sort from localStorage: ${storedSortMethod}`);
        localStorage.removeItem('youtubePageSortMethod');
        // Use the default sort
        const defaultSort: SortOption = 'date_desc';
        setSortBy(defaultSort);
        setQuerySortKey(defaultSort);
      }
    } else {
      // If no sort is set, use the default sort
      const defaultSort: SortOption = 'date_desc';
      console.log(`[LOCALSTORAGE EFFECT] No sort preference found, using default: ${defaultSort}`);
      setSortBy(defaultSort);
      setQuerySortKey(defaultSort);
    }

    const storedDurationFilter = localStorage.getItem('preferredDurationFilter');
    if (storedDurationFilter && ['all', 'shorts', 'videos'].includes(storedDurationFilter)) {
      setDurationFilter(storedDurationFilter as DurationFilterType);
    }

    const storedUnwatchedFilter = localStorage.getItem('preferredUnwatchedFilter');
    if (storedUnwatchedFilter) {
      setShowUnwatchedOnly(storedUnwatchedFilter === 'true');
    }

    // Transcript filter removed
  }, []);

  // Apply user's default settings when they are loaded, but only on initial load
  // Use a ref to track if we've already applied settings to avoid reapplying them
  const settingsAppliedRef = useRef(false);

  useEffect(() => {
    // Only apply settings once on initial load and only if we have settings
    if (!settings || settingsAppliedRef.current) {
      return;
    }

    console.log(`[SETTINGS EFFECT] Settings loaded:`, settings);

    // Mark settings as applied to prevent reapplying
    settingsAppliedRef.current = true;

    // We no longer need to apply sort from settings
    // The sort is already set in the initial state and localStorage effect
    console.log(`[SETTINGS EFFECT] Current sort: ${sortBy}`);

    // We don't need to apply default filters from settings anymore
    // User preferences are stored in localStorage and loaded directly
  }, [settings, sortBy]);

  // Initialize the channel selection to All Channels when the component first loads
  // This useEffect is only needed for the initial load
  useEffect(() => {
    logger.debug('Initializing channel selection');
    // We're already initializing to null in the useState, so this is just for logging
  }, []); // Empty dependency array means this only runs once on mount

  // Handle direct fetching of videos when needed
  useEffect(() => {
    if (needDirectFetch === null) return;

    logger.debug(`Direct fetching videos for channel ID: ${needDirectFetch}`);

    const fetchVideosDirectly = async () => {
      try {
        const res = await apiRequest("GET", `/api/youtube-channels/${needDirectFetch}/videos`);
        const videos = await res.json();
        logger.debug(`Directly fetched ${videos.length} videos for channel ID ${needDirectFetch}`);

        if (videos.length > 0) {
          // Add these videos to the displayed videos
          setDisplayedVideos(prev => {
            // Create a Set of existing video IDs to avoid duplicates
            const existingIds = new Set(prev.map(v => v.id));
            // Filter out any videos we already have
            const newVideos = videos.filter(v => !existingIds.has(v.id));

            logger.debug(`Adding ${newVideos.length} new videos to the displayed list`);
            return [...prev, ...newVideos];
          });
        }
      } catch (error) {
        logger.error(`Error directly fetching videos for channel ID ${needDirectFetch}:`, error);
      } finally {
        // Reset the needDirectFetch state
        setNeedDirectFetch(null);
      }
    };

    fetchVideosDirectly();
  }, [needDirectFetch]);

  // Update lastRefreshTime when channels are loaded or selectedChannelId changes
  useEffect(() => {
    if (channels.length > 0) {
      if (selectedChannelId) {
        // Get the selected channel's last refresh time
        const selectedChannel = channels.find(c => c.id === selectedChannelId);
        if (selectedChannel?.lastRefreshTime) {
          setLastRefreshTime(new Date(selectedChannel.lastRefreshTime));
        }
      } else {
        // If no channel is selected but channels exist, use the most recent refresh time
        const mostRecentRefresh = channels
          .filter(c => c.lastRefreshTime)
          .sort((a, b) => {
            const dateA = a.lastRefreshTime ? new Date(a.lastRefreshTime).getTime() : 0;
            const dateB = b.lastRefreshTime ? new Date(b.lastRefreshTime).getTime() : 0;
            return dateB - dateA;
          })[0]?.lastRefreshTime;

        if (mostRecentRefresh) {
          setLastRefreshTime(new Date(mostRecentRefresh));
        }
      }
    }
  }, [channels, selectedChannelId]);

  // Loading state for sorting operations is defined above

  // Memory cleanup function - remove videos that are far from view
  const cleanupVideosRef = useRef<number | null>(null);

  useEffect(() => {
    // Cleanup function to remove videos that are far from view
    const cleanupVideos = () => {
      if (allFilteredVideos.length <= videosPerPage) return; // Don't cleanup if we have few videos

      const scrollPosition = window.scrollY;
      const viewportHeight = window.innerHeight;

      // If we've scrolled far enough, remove videos from the beginning
      if (scrollPosition > viewportHeight && displayedVideos.length > videosPerPage * 1.5) {
        // Calculate how many videos to keep based on current scroll position
        const scrollRatio = scrollPosition / (document.body.scrollHeight - viewportHeight);
        const keepCount = Math.min(
          displayedVideos.length,
          Math.max(videosPerPage, Math.floor(displayedVideos.length * 0.5))
        );

        // Keep the most recent videos and discard old ones
        const videosToKeep = displayedVideos.slice(-keepCount);

        // Only update if we're actually removing a significant number of videos
        if (displayedVideos.length - videosToKeep.length > videosPerPage / 3) {
          // Force garbage collection by nullifying references
          const discardedVideos = displayedVideos.slice(0, displayedVideos.length - keepCount);
          discardedVideos.forEach(video => {
            // @ts-ignore - This is a memory optimization technique
            video.description = null;
          });

          setDisplayedVideos(videosToKeep);

          // Reset hasMore to true since we've removed videos
          setHasMore(true);

          // Update current page based on remaining videos
          const newPage = Math.max(1, Math.ceil(videosToKeep.length / videosPerPage));
          setCurrentPage(newPage);
        }
      }
    };

    // Debounce the cleanup function to avoid too frequent updates
    const debouncedCleanup = () => {
      if (cleanupVideosRef.current) {
        clearTimeout(cleanupVideosRef.current);
      }

      cleanupVideosRef.current = window.setTimeout(cleanupVideos, 500) as unknown as number;
    };

    // Set up scroll event listener for cleanup
    window.addEventListener('scroll', debouncedCleanup, { passive: true });

    // Also run cleanup when window is blurred (user switches tabs)
    window.addEventListener('blur', cleanupVideos, { passive: true });

    return () => {
      window.removeEventListener('scroll', debouncedCleanup);
      window.removeEventListener('blur', cleanupVideos);
      if (cleanupVideosRef.current) {
        clearTimeout(cleanupVideosRef.current);
      }
    };
  }, [displayedVideos, allFilteredVideos.length, videosPerPage]);

  // debouncedSort function is already defined above

  // Function to load more videos for infinite scrolling (server-side pagination)
  const loadMoreVideos = useCallback(() => {
    // Prevent loading if already loading or no more content
    if (isLoadingMore || !hasMore || isLoadingAllVideos) {
      console.log('Skipping loadMoreVideos:', { isLoadingMore, hasMore, isLoadingAllVideos });
      return;
    }

    // Check if we have pagination info and if there are more pages
    if (!paginationInfo?.hasMore) {
      console.log('No more pages available according to pagination info');
      setHasMore(false);
      return;
    }

    // Calculate next page
    const nextPage = currentPage + 1;

    // Don't exceed total pages if we know it
    if (totalPages && nextPage > totalPages) {
      console.log(`Reached the last page (${totalPages})`);
      setHasMore(false);
      return;
    }

    console.log(`Loading more videos - requesting page ${nextPage} with current filters and sorting`);
    console.log(`Current filters - Sort: ${querySortKey}, Search: "${searchQuery}"`);
    setIsLoadingMore(true);

    // Update the current page - this will trigger a refetch with the new page
    // The useAllChannelVideos hook will use the current sortBy, searchQuery, etc. values
    // because they're included in the dependency array of the useCallback
    setCurrentPage(nextPage);

    // The actual loading is handled by the useAllChannelVideos hook
    // which will refetch when currentPage changes or any filter/sort option changes

    // We'll handle the new data in the useEffect that watches for videosData changes
    // Don't reset isLoadingMore here - we'll do it in the useEffect when data arrives

    // Set a safety timeout to reset loading state if the request takes too long
    const safetyTimeout = setTimeout(() => {
      if (isLoadingMore) {
        console.warn('Loading more videos timed out - resetting loading state');
        setIsLoadingMore(false);
      }
    }, 10000); // 10 seconds timeout

    // Clear the timeout when component unmounts
    return () => clearTimeout(safetyTimeout);
  }, [
    currentPage,
    isLoadingMore,
    hasMore,
    isLoadingAllVideos,
    paginationInfo,
    totalPages,
    // Include all sorting and filtering parameters in the dependency array
    // so that when they change, this callback will use the latest values
    querySortKey,
    searchQuery,
    durationFilter
  ]);

  // Update pagination state when new data arrives
  useEffect(() => {
    // Skip processing if we're still loading
    if (!videosData || isLoadingAllVideos) return;

    // Import logger dynamically to avoid circular dependencies
    import('@/lib/logger').then(({ logger }) => {
      logger.debug('New video data received:', {
        videoCount: allChannelVideos?.length || 0,
        currentPage,
        isRefreshing: refreshMetadata.isPending,
        cachedVideoCount: cachedVideos.length
      });
    });

    // Update pagination state from the API response
    if (paginationInfo) {
      setTotalPages(paginationInfo.totalPages);
      setTotalVideos(paginationInfo.totalCount);
      setHasMore(paginationInfo.hasMore);
    }

    // Reset loading state after data is processed
    setIsLoadingMore(false);
  }, [videosData, isLoadingAllVideos, paginationInfo, allChannelVideos, currentPage, refreshMetadata.isPending, cachedVideos.length]);

  // Process videos when new data arrives
  useEffect(() => {
    // Skip processing if we're still loading or no data
    if (!videosData || isLoadingAllVideos || !allChannelVideos) return;

    // Only process videos if we actually have videos in the response
    if (allChannelVideos.length > 0) {
      // Always update our cache with the new videos
      updateCache(allChannelVideos);

      // If this is the first page, replace displayed videos
      if (currentPage === 1) {
        // Store the current video IDs for comparison
        const currentVideoIds = new Set(displayedVideos.map(v => v.id));
        const newVideoIds = new Set(allChannelVideos.map(v => v.id));

        // Check if the video sets are significantly different
        let addedCount = 0;
        let removedCount = 0;

        // Count added videos
        allChannelVideos.forEach(v => {
          if (!currentVideoIds.has(v.id)) addedCount++;
        });

        // Count removed videos
        displayedVideos.forEach(v => {
          if (!newVideoIds.has(v.id)) removedCount++;
        });

        // Use VERBOSE level for these frequent updates to minimize console noise
        logger.verbose(`Video set changes: ${addedCount} added, ${removedCount} removed`);

        // If we're refreshing, use our cached videos with updated metadata
        if (refreshMetadata.isPending || isLoadingAllVideosState) {
          logger.verbose('Refresh in progress, using merged videos from cache with updated metadata');

          // Get merged videos from cache with updated metadata
          const mergedVideos = getMergedVideos(allChannelVideos);

          // During refresh, we want to maintain the current sort order to prevent UI flicker
          // We'll update the data but keep the same order
          if (displayedVideos.length > 0) {
            logger.verbose('Maintaining current sort order during refresh to prevent UI flicker');

            // Create a map of existing videos by ID for quick lookup
            const existingVideosMap = new Map(
              displayedVideos.map(video => [video.id, video])
            );

            // Update existing videos with new data while maintaining order
            const updatedVideos = displayedVideos.map(video => {
              // Find the updated video data
              const updatedVideo = mergedVideos.find(v => v.id === video.id);
              // Return the updated video if found, otherwise keep the existing one
              return updatedVideo || video;
            });

            // Add any new videos that weren't in the original list
            const newVideos = mergedVideos.filter(video => !existingVideosMap.has(video.id));

            // Only update state if there are actual changes to avoid unnecessary re-renders
            const hasNewVideos = newVideos.length > 0;
            const hasUpdatedMetadata = updatedVideos.some((video, index) => {
              // Check if any key properties have changed
              const originalVideo = displayedVideos[index];
              return (
                video.vph !== originalVideo.vph ||
                video.viewCount !== originalVideo.viewCount ||
                video.likeCount !== originalVideo.likeCount
              );
            });

            if (hasNewVideos || hasUpdatedMetadata) {
              logger.verbose(`Updating displayed videos: ${hasNewVideos ? newVideos.length + ' new videos, ' : ''}${hasUpdatedMetadata ? 'metadata changes' : 'no metadata changes'}`);

              if (hasNewVideos) {
                // Add new videos at the end - they'll be sorted properly after refresh completes
                setDisplayedVideos([...updatedVideos, ...newVideos]);
              } else {
                // Just update the existing videos
                setDisplayedVideos(updatedVideos);
              }
            } else {
              logger.verbose('No changes detected in videos, skipping state update to prevent re-render');
            }
          } else {
            // If no existing videos, sort the merged videos using the current sort settings
            // Use the sortBy state directly - this is what the user selected
            const sortKey = sortBy;
            logger.debug(`Sorting merged videos with key: ${sortKey}`);

            const sortedMergedVideos = [...mergedVideos].sort((a, b) => {
              // Simplified sorting with only one criterion per sort option
              if (sortKey === 'vph_desc') return (b.vph || 0) - (a.vph || 0);
              if (sortKey === 'date_desc') return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
              if (sortKey === 'date_asc') return new Date(a.publishedAt).getTime() - new Date(b.publishedAt).getTime();
              if (sortKey === 'views_desc') return (b.viewCount || 0) - (a.viewCount || 0);
              if (sortKey === 'title_asc') return a.title.localeCompare(b.title);
              if (sortKey === 'shorts_first') {
                const aIsShort = getDurationInSeconds(a) < 60;
                const bIsShort = getDurationInSeconds(b) < 60;
                if (aIsShort && !bIsShort) return -1;
                if (!aIsShort && bIsShort) return 1;
                return 0;
              }
              if (sortKey === 'videos_first') {
                const aIsShort = getDurationInSeconds(a) < 60;
                const bIsShort = getDurationInSeconds(b) < 60;
                if (!aIsShort && bIsShort) return -1;
                if (aIsShort && !bIsShort) return 1;
                return 0;
              }
              if (sortKey === 'duration_asc') return getDurationInSeconds(a) - getDurationInSeconds(b);
              if (sortKey === 'duration_desc') return getDurationInSeconds(b) - getDurationInSeconds(a);
              // Default to newest first
              return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
            });

            // Log the top 3 videos to verify the sort
            if (sortedMergedVideos.length > 0 && sortKey === 'views_desc') {
              logger.debug('Top 3 videos after merged sort:');
              for (let i = 0; i < Math.min(3, sortedMergedVideos.length); i++) {
                logger.debug(`  ${i+1}. ${sortedMergedVideos[i].title} (${sortedMergedVideos[i].viewCount || 0} views)`);
              }
            }

            logger.verbose(`Using ${sortedMergedVideos.length} merged videos from cache during refresh`);

            // Update the displayed videos in a single operation
            setDisplayedVideos(sortedMergedVideos);
          }
        } else {
          // If not refreshing or this is initial load, use the new videos
          logger.verbose(`Set initial ${allChannelVideos.length} videos for page 1`);
          setDisplayedVideos(allChannelVideos);
        }
      }
      // Otherwise append the new videos
      else if (currentPage > 1) {
        setDisplayedVideos(prev => {
          // Create a Set of existing video IDs to avoid duplicates
          const existingIds = new Set(prev.map(v => v.id));
          // Filter out any videos we already have
          const newVideos = allChannelVideos.filter(v => !existingIds.has(v.id));

          logger.verbose(`Adding ${newVideos.length} new videos from page ${currentPage} to existing ${prev.length} videos`);

          // Always append new videos, even if the array is empty
          return [...prev, ...newVideos];
        });
      }
    } else {
      logger.verbose('No videos in response, using cached videos');
      // If we're on page 1 and got no videos, use cached videos if available
      if (currentPage === 1) {
        if (cachedVideos.length > 0 && refreshMetadata.isPending) {
          logger.verbose(`Using ${cachedVideos.length} cached videos during refresh`);
          setDisplayedVideos(cachedVideos);
        } else if (!refreshMetadata.isPending) {
          // Only clear if not refreshing
          setDisplayedVideos([]);
          clearCache();
        }
      }
    }
  }, [videosData, isLoadingAllVideos, isLoadingAllVideosState, allChannelVideos, currentPage, refreshMetadata.isPending, querySortKey, displayedVideos, cachedVideos, updateCache, getMergedVideos, clearCache]);

  // Setup infinite scroll with improved settings for better detection
  const { sentinelRef } = useInfiniteScroll(loadMoreVideos, {
    threshold: 0.01, // Very low threshold to detect as soon as any part becomes visible
    rootMargin: '300px', // Load content before it's visible (300px before it enters viewport)
    enabled: hasMore && !isLoadingMore && !isSorting && !isLoadingAllVideos // Only enable when we can load more
  });

  // Handle channel selection changes
  useEffect(() => {
    // Show loading state
    setIsSorting(true);

    // Clear displayed videos to show loading state
    setDisplayedVideos([]);

    if (selectedChannelId === null) {
      logger.debug('All channels selected, using all videos');
      // When switching to All Channels, we need to reset the page and refetch
      setCurrentPage(1);
      // Force a refetch of all videos
      setTimeout(() => {
        refetchAllVideos().then(() => {
          // Reset sorting state after videos are loaded
          setIsSorting(false);
        }).catch(() => {
          // Make sure we reset the sorting state even if there's an error
          setIsSorting(false);
        });
      }, 100);
    } else {
      logger.debug(`Channel ${selectedChannelId} selected, using channel videos`);
      // The useChannelVideos hook will automatically fetch videos for this channel
      setTimeout(() => {
        refetchVideos().then(() => {
          // Reset sorting state after videos are loaded
          setIsSorting(false);
        }).catch(() => {
          // Make sure we reset the sorting state even if there's an error
          setIsSorting(false);
        });
      }, 100);
    }
  }, [selectedChannelId, refetchAllVideos, refetchVideos]);

  // Reset to page 1 when filters change for server-side filtering
  useEffect(() => {
    // Only reset if we're not already on page 1 and we're viewing all channels
    if (selectedChannelId === null && currentPage !== 1) {
      logger.debug('Resetting to page 1 due to filter change');
      setCurrentPage(1);
      setHasMore(true); // Reset hasMore to ensure we can load more after filtering
      // The rest of the processing will happen when the new data arrives from the server
      return;
    }

    // Ensure the sort state is always up to date with the query sort key
    if (sortBy !== querySortKey && querySortKey !== 'none') {
      logger.debug(`Updating sort state from ${sortBy} to ${querySortKey}`);
      setSortBy(querySortKey);
    }
  }, [selectedChannelId, searchQuery, durationFilter, querySortKey, showUnwatchedOnly, currentPage]);

  // Apply client-side filtering and sorting to the videos
  useEffect(() => {
    // Show loading state when starting to process videos
    setIsSorting(true);

    // Only log in development mode to reduce console noise
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Processing videos with sort key: ${querySortKey}`);
      logger.debug('Processing videos for display');
      logger.debug(`Selected channel ID: ${selectedChannelId}`);
      logger.debug(`All channels videos: ${allChannelVideos.length}`);
      logger.debug(`Channel videos: ${channelVideos.length}`);
    }

    // Ensure the sort key is consistent between sortBy and querySortKey
    if (sortBy && querySortKey !== sortBy) {
      logger.debug(`Syncing query sort key with sort state: ${sortBy}`);
      setQuerySortKey(sortBy);
    }

    // If we're viewing a specific channel, we need to reset to page 1 when filters change
    if (selectedChannelId !== null && (searchQuery || showUnwatchedOnly)) {
      // Only reset if we're not already on page 1
      if (currentPage !== 1) {
        logger.debug('Resetting to page 1 due to filter change for selected channel');
        setCurrentPage(1);
        setHasMore(true); // Reset hasMore to ensure we can load more after filtering
        // The rest of the processing will happen when the new data arrives
        return;
      }
    }

    // If we're switching to a channel and the data is already cached, process it immediately
    if (selectedChannelId !== null && channelVideos.length > 0) {
      logger.debug(`Using ${channelVideos.length} cached videos for channel ${selectedChannelId}`);
    }

    // Step 1: Get the base set of videos (all or filtered by channel)
    let videosToProcess = [];

    // If a channel is selected, use the useChannelVideos hook data
    if (selectedChannelId !== null) {
      logger.debug(`Using direct channel videos for channel ID: ${selectedChannelId}`);

      // Use the channelVideos from the hook
      if (channelVideos.length > 0) {
        logger.debug(`Using ${channelVideos.length} videos from useChannelVideos hook`);
        videosToProcess = [...channelVideos];

        // Clear allChannelVideos from the displayed videos to ensure only channel videos are shown
        if (allChannelVideos.length > 0) {
          logger.debug('Clearing all channel videos to show only selected channel videos');
        }
      } else {
        logger.debug(`No videos found in useChannelVideos hook, fetching directly from server`);
        // If no videos in the hook data, trigger a direct fetch
        setNeedDirectFetch(selectedChannelId);
        return; // Exit early and wait for the direct fetch to complete
      }
    } else {
      // For All Channels, use the allChannelVideos from the hook
      logger.debug(`Using all channel videos: ${allChannelVideos.length} videos`);
      videosToProcess = [...allChannelVideos];
    }

    // Step 2: Apply all filters
    let filteredVideos = [...videosToProcess];

    // Apply search filter - HIGHEST PRIORITY (1)
    if (searchQuery && searchQuery.trim() !== '') {
      logger.debug(`Applying search filter: "${searchQuery}"`);
      const query = searchQuery.toLowerCase().trim();

      // Apply the search filter - ONLY search in video titles as requested
      const beforeCount = filteredVideos.length;
      filteredVideos = filteredVideos.filter(video => {
        // Make sure we have a string to search in
        const videoTitle = (video.title || '').toLowerCase();

        // Check for matches in title only
        return videoTitle.includes(query);
      });

      logger.debug(`Search filter applied: ${beforeCount} videos → ${filteredVideos.length} videos match "${searchQuery}" in titles`);
    }

    // Apply unwatched filter - PRIORITY (2)
    if (showUnwatchedOnly) {
      logger.debug('Applying unwatched filter');
      filteredVideos = filteredVideos.filter(video => {
        return !settings?.watchedVideos?.includes(video.id);
      });
      logger.debug(`After unwatched filter: ${filteredVideos.length} videos`);
    }

    // Transcript filter removed

    // Step 3: Sort the filtered videos
    // Only log in development mode to reduce console noise
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Applying sort: ${querySortKey} to ${filteredVideos.length} videos`);
    }

    // Create a simple sorting function that handles null/undefined values properly
    const stableSort = (a: YoutubeVideo, b: YoutubeVideo) => {
      // Always use sortBy directly - this is what the user selected
      const sortKey = sortBy;

      // Log the sort key for debugging
      logger.debug(`Applying client-side sort with key: ${sortKey}`);

      // For views_desc sort, log the first few comparisons to help debug
      if (sortKey === 'views_desc' && a.title.includes('Stimulus') && b.title.includes('Stimulus')) {
        logger.debug(`Sorting by views_desc: ${a.title} (${a.viewCount || 0} views) vs ${b.title} (${b.viewCount || 0} views)`);
      }

      // First, handle the case where one or both videos have missing data for the sort field
      switch (sortKey) {
        case 'views_desc':
        case 'views_asc':
          // If viewCount is missing for either video, put videos with data first
          if (a.viewCount === undefined && b.viewCount !== undefined) return 1;
          if (a.viewCount !== undefined && b.viewCount === undefined) return -1;
          break;

        case 'vph_desc':
        case 'vph_asc':
          // If vph is missing for either video, put videos with data first
          if (a.vph === undefined && b.vph !== undefined) return 1;
          if (a.vph !== undefined && b.vph === undefined) return -1;
          break;
      }

      // Helper function to get video duration in seconds
      const getDurationInSeconds = (video: YoutubeVideo): number => {
        if (!video.duration) return 0;

        // Parse the ISO 8601 duration format (PT8M10S = 8 minutes 10 seconds)
        const minutesMatch = video.duration.match(/([0-9]+)M/);
        const secondsMatch = video.duration.match(/([0-9]+)S/);
        const hoursMatch = video.duration.match(/([0-9]+)H/);

        const hours = hoursMatch ? parseInt(hoursMatch[1]) : 0;
        const minutes = minutesMatch ? parseInt(minutesMatch[1]) : 0;
        const seconds = secondsMatch ? parseInt(secondsMatch[1]) : 0;

        return (hours * 3600) + (minutes * 60) + seconds;
      };

      // Helper function to determine if a video is a short
      const isShort = (video: YoutubeVideo): boolean => {
        const durationSeconds = getDurationInSeconds(video);
        return durationSeconds > 0 && durationSeconds < 60;
      };

      // Apply the sort based on the selected option - simplified to use only one criterion per sort
      switch (sortKey) {
        // Popular & Trending sorts
        case 'date_desc':
          return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();

        case 'views_desc':
          // Make sure we're using numbers for comparison
          const aViews = typeof a.viewCount === 'number' ? a.viewCount : 0;
          const bViews = typeof b.viewCount === 'number' ? b.viewCount : 0;
          return bViews - aViews;

        case 'vph_desc':
          return (b.vph || 0) - (a.vph || 0);

        case 'date_asc':
          return new Date(a.publishedAt).getTime() - new Date(b.publishedAt).getTime();

        // Content Type sorts
        case 'shorts_first':
          // Sort by short status only
          const aIsShort = isShort(a);
          const bIsShort = isShort(b);
          if (aIsShort && !bIsShort) return -1;
          if (!aIsShort && bIsShort) return 1;
          return 0; // Equal priority if both are shorts or both are not shorts

        case 'videos_first':
          // Sort by video status only (not short)
          const aIsVideo = !isShort(a);
          const bIsVideo = !isShort(b);
          if (aIsVideo && !bIsVideo) return -1;
          if (!aIsVideo && bIsVideo) return 1;
          return 0; // Equal priority if both are videos or both are shorts

        case 'duration_asc':
          return getDurationInSeconds(a) - getDurationInSeconds(b);

        case 'duration_desc':
          return getDurationInSeconds(b) - getDurationInSeconds(a);

        // Alphabetical sort
        case 'title_asc':
        case 'title': // Legacy support
          return (a.title || '').localeCompare(b.title || '');

        default:
          // Default to newest first
          return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
      }
    };

    // Create a copy of the array to avoid mutating the original
    const sortedVideos = [...filteredVideos].sort(stableSort);

    // Log the sort key and the first few videos to help debug (only in development)
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Sorted videos using key: ${sortBy}`);
      if (sortedVideos.length > 0) {
        logger.debug(`First video after sorting: ${sortedVideos[0].title} (${sortedVideos[0].viewCount || 0} views)`);

        // Log the top 3 videos if sorting by views
        if (sortBy === 'views_desc' && sortedVideos.length >= 3) {
          logger.debug(`Top 3 videos by views:`);
          for (let i = 0; i < 3; i++) {
            logger.debug(`  ${i+1}. ${sortedVideos[i].title} (${sortedVideos[i].viewCount || 0} views)`);
          }
        }
      }
    }

    // Store all filtered and sorted videos for client-side operations
    setAllFilteredVideos(sortedVideos);

    // Apply client-side filtering and sorting to the current page of videos
    // The actual pagination is handled by the server, but we need to filter and sort what we have

    // Set the displayed videos
    setDisplayedVideos(sortedVideos);

    // Log the top 3 videos to verify the sort
    if (sortedVideos.length > 0 && sortBy === 'views_desc') {
      logger.debug('Top 3 videos after sort:');
      for (let i = 0; i < Math.min(3, sortedVideos.length); i++) {
        logger.debug(`  ${i+1}. ${sortedVideos[i].title} (${sortedVideos[i].viewCount || 0} views)`);
      }
    }

    // Update UI state
    setIsLoadingMore(false);
    setIsSorting(false);

    conditionalLog(process.env.NODE_ENV !== 'production', LogLevel.DEBUG,
      `Displaying ${sortedVideos.length} filtered/sorted videos`
    );

  }, [selectedChannelId, allChannelVideos, channels, querySortKey, sortBy, searchQuery, showUnwatchedOnly, settings?.watchedVideos, videosPerPage]);

  // Filtering and sorting is now handled directly in the useEffect above

  // Handle grid size change
  const handleGridSizeChange = useCallback((value: number[]) => {
    const newSize = Math.max(1, Math.min(6, value[0]));
    setGridSize(newSize);
    localStorage.setItem('preferredGridSize', newSize.toString());
  }, []);

  // Get the appropriate grid class based on the gridSize
  const getGridClass = useCallback(() => {
    // Use a fixed set of classes instead of template literals for better compatibility
    switch (gridSize) {
      case 1: return "grid gap-4 px-0 grid-cols-1";
      case 2: return "grid gap-4 px-0 grid-cols-2";
      case 3: return "grid gap-4 px-0 grid-cols-3";
      case 5: return "grid gap-4 px-0 grid-cols-5";
      case 6: return "grid gap-4 px-0 grid-cols-6";
      case 4:
      default: return "grid gap-4 px-0 grid-cols-4";
    }
  }, [gridSize]);

  // Get the handle resolver hook
  const { resolveHandle } = useYoutubeHandleResolver();

  // Handle adding a new channel
  const handleAddChannel = async () => {
    try {
      if (!newChannelUrl) {
        toast({
          title: "Error",
          description: "Please enter a YouTube channel URL or handle",
          variant: "destructive",
        });
        return;
      }

      // Extract channel ID from URL
      let channelId = '';
      let channelTitle = '';
      let channelUrl = newChannelUrl;

      // Handle different YouTube URL formats
      if (newChannelUrl.includes('youtube.com/channel/')) {
        // This is the most reliable format as it contains the actual channel ID
        channelId = newChannelUrl.split('youtube.com/channel/')[1].split(/[?#]/)[0];
        channelTitle = channelId; // We'll use the ID as title initially
      } else if (newChannelUrl.includes('youtube.com/c/')) {
        // For custom URLs, we need to extract the channel name
        const customName = newChannelUrl.split('youtube.com/c/')[1].split(/[?#]/)[0];
        // We need to use a different approach for custom URLs
        toast({
          title: "Resolving Custom URL",
          description: "Attempting to resolve the custom URL to a channel ID...",
          variant: "default",
        });

        try {
          // Try to resolve the custom URL to a channel ID
          const resolved = await resolveHandle(customName);
          channelId = resolved.channelId;
          channelTitle = customName;
          channelUrl = resolved.channelUrl;

          toast({
            title: "Custom URL Resolved",
            description: "Successfully resolved the custom URL to a channel ID.",
            variant: "default",
          });
        } catch (error) {
          logger.error('Error resolving custom URL:', error);
          // Fallback to the old method
          channelId = 'UC' + customName.replace(/[^a-zA-Z0-9]/g, '').padEnd(22, 'X');
          channelTitle = customName;
          toast({
            title: "Warning",
            description: "Could not resolve custom URL. Using a placeholder channel ID which may not work reliably.",
            variant: "warning",
          });
        }
      } else if (newChannelUrl.includes('youtube.com/@') || newChannelUrl.startsWith('@')) {
        // Handle URLs with @ or direct @ input
        let handle;

        if (newChannelUrl.includes('youtube.com/@')) {
          handle = newChannelUrl.split('youtube.com/@')[1].split(/[?#]/)[0].trim();
        } else {
          handle = newChannelUrl.startsWith('@') ? newChannelUrl.substring(1).trim() : newChannelUrl.trim();
        }

        toast({
          title: "Resolving Handle",
          description: `Attempting to resolve @${handle} to a channel ID...`,
          variant: "default",
        });

        try {
          // Try to resolve the handle to a channel ID
          const resolved = await resolveHandle(handle);
          channelId = resolved.channelId;
          channelTitle = `@${handle}`;
          channelUrl = resolved.channelUrl;

          toast({
            title: "Handle Resolved",
            description: `Successfully resolved @${handle} to channel ID: ${channelId.substring(0, 10)}...`,
            variant: "default",
          });
        } catch (error) {
          logger.error('Error resolving handle:', error);
          // Fallback to the old method
          channelId = 'UC' + handle.replace(/[^a-zA-Z0-9]/g, '').padEnd(22, 'X');
          channelTitle = '@' + handle;
          channelUrl = `https://www.youtube.com/@${handle}`;

          toast({
            title: "Warning",
            description: "Could not resolve handle. Using a placeholder channel ID which may not work reliably.",
            variant: "warning",
          });
        }
      } else {
        // Try to interpret as a channel name
        const channelName = newChannelUrl.trim();
        if (channelName) {
          toast({
            title: "Resolving Channel Name",
            description: `Attempting to resolve "${channelName}" to a channel ID...`,
            variant: "default",
          });

          try {
            // Try to resolve the channel name to a channel ID
            const resolved = await resolveHandle(channelName);
            channelId = resolved.channelId;
            channelTitle = channelName;
            channelUrl = resolved.channelUrl;

            toast({
              title: "Channel Name Resolved",
              description: `Successfully resolved "${channelName}" to a channel ID.`,
              variant: "default",
            });
          } catch (error) {
            logger.error('Error resolving channel name:', error);
            // Fallback to the old method
            channelId = 'UC' + channelName.replace(/[^a-zA-Z0-9]/g, '').padEnd(22, 'X');
            channelTitle = channelName;
            channelUrl = `https://www.youtube.com/c/${channelName}`;

            toast({
              title: "Warning",
              description: "Could not resolve channel name. Using a placeholder channel ID which may not work reliably.",
              variant: "warning",
            });
          }
        } else {
          toast({
            title: "Invalid Input",
            description: "Please enter a valid YouTube channel URL, handle (@name), or channel name",
            variant: "destructive",
          });
          return;
        }
      }

      // Clear the input field for the next channel, but keep the dialog open
      setNewChannelUrl('');

      // Ensure video limit is capped at 15 due to RSS feed limitations
      const cappedVideoLimit = Math.min(newChannelVideoLimit, 15);

      // Add the channel
      const newChannel = await createChannel.mutateAsync({
        channelId,
        channelTitle,
        channelUrl,
        videoLimit: cappedVideoLimit,
      });

      // Show a success message
      toast({
        title: "Channel Added",
        description: `${channelTitle} has been added to your subscriptions.`,
      });

      // Automatically refresh the newly added channel to fetch videos in the background
      if (newChannel && newChannel.id) {

        toast({
          title: "Fetching Videos",
          description: `Fetching videos for ${channelTitle} in the background...`,
        });

        // Use setTimeout to allow UI to update before starting background tasks
        setTimeout(() => {
          // Use Promise.all to run these operations in parallel without blocking the UI
          Promise.all([
            refreshChannelVideos.mutateAsync(newChannel.id),
            refetchChannels(), // Refresh the channel list
            refetchAllVideos() // Refresh all videos
          ])
          .then(() => {
            toast({
              title: "Videos Fetched",
              description: `Successfully fetched videos for ${channelTitle}`,
            });
          })
          .catch((error) => {
            logger.error('Error refreshing new channel:', error);
            toast({
              title: "Error",
              description: "Failed to fetch videos for the new channel. Try refreshing manually.",
              variant: "destructive",
            });
          });
        }, 100);
      }
    } catch (error) {
      logger.error('Error adding channel:', error);

      // Extract error message from the response if available
      let errorMessage = "Failed to add channel. Please try again.";

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      // Check for specific error messages
      if (errorMessage.includes("already exists")) {
        errorMessage = "This channel is already in your subscriptions.";
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      // The dialog is already open since we didn't close it
    }
  };

  // Handle refreshing all channels with parallel fetching
  const handleRefreshAll = async () => {
    try {
      // Set refresh start time for progress indicator
      setRefreshStartTime(Date.now());

      // Initialize refresh status
      setRefreshStatus({
        isRefreshing: true,
        currentChannel: 'All channels (parallel refresh)',
        progress: 0,
        totalChannels: channels.length,
        completedChannels: 0
      });

      // Start the progress animation - more realistic for parallel fetching
      const progressInterval = setInterval(() => {
        // Calculate elapsed time and use it for progress estimation
        const elapsed = Date.now() - (refreshStartTime || Date.now());
        // Estimate total time as 3 seconds per channel, but at least 5 seconds
        const estimatedTotal = Math.max(5000, channels.length * 3000);
        // Calculate progress percentage based on elapsed time
        const progressPercent = Math.min(90, (elapsed / estimatedTotal) * 100);

        setRefreshStatus(prev => ({
          ...prev,
          progress: progressPercent,
          currentChannel: 'All channels (parallel refresh)',
        }));
      }, 100); // Update frequently for smoother animation

      // Queue background tasks for each channel
      const tasks = [];
      for (const channel of channels) {
        const response = await apiRequest("POST", `/api/tasks/refresh-channel-direct`, {
          channelId: channel.id.toString()
        });

        const task = await response.json();
        tasks.push(task);
        console.log(`Created background task ${task.id} for refreshing channel ${channel.channelTitle}`);

        // Update progress to show each channel being queued
        setRefreshStatus(prev => ({
          ...prev,
          completedChannels: tasks.length,
        }));
      }

      // Clear the progress interval
      clearInterval(progressInterval);

      // Set progress to 100%
      setRefreshStatus(prev => ({
        ...prev,
        progress: 100,
        completedChannels: channels.length,
        currentChannel: 'Complete!'
      }));

      // Calculate refresh duration
      const endTime = Date.now();
      const duration = endTime - (refreshStartTime || endTime);
      setRefreshDuration(duration);

      // Update last refresh time
      const now = new Date();
      setLastRefreshTime(now);

      // Show success toast
      toast({
        title: "Background Tasks Created",
        description: `Created ${tasks.length} background tasks for channel refreshes. Videos with transcriptions but no Ollama analysis will be automatically analyzed. Check the Tasks page for status updates.`,
      });

      // Show a second toast after a delay to remind about the Tasks page
      setTimeout(() => {
        toast({
          title: "Background Processing",
          description: "All channels are being refreshed in the background. Check the Tasks page for status updates.",
        });
      }, 3000);

      // Navigate to the Tasks page after a short delay
      setTimeout(() => {
        window.location.href = '/tasks';
      }, 5000);

      // Log performance metrics
      console.log(`Parallel task creation performance:`);
      console.log(`- Total time: ${(duration / 1000).toFixed(1)}s`);
      console.log(`- Channels: ${channels.length}`);
      console.log(`- Tasks created: ${tasks.length}`);
      console.log(`- Average time per task: ${(duration / tasks.length / 1000).toFixed(1)}s`);
    } catch (error) {
      console.error('Error refreshing channels:', error);

      toast({
        title: "Error",
        description: "Failed to refresh channels. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Reset refresh start time and status after a short delay to show completion
      setTimeout(() => {
        setRefreshStartTime(null);
        setRefreshStatus({
          isRefreshing: false,
          currentChannel: null,
          progress: 0,
          totalChannels: 0,
          completedChannels: 0
        });
      }, 1500);
    }
  };

  // Handle analyzing all videos in a channel with OpenRouter
  const handleAnalyzeChannelWithOpenRouter = async (channelId: number) => {
    try {
      const channelName = channels.find(c => c.id === channelId)?.channelTitle || 'channel';
      toast({
        title: "Starting AI Analysis",
        description: `Starting analysis of videos for ${channelName}. This will run in the background.`,
      });

      const result = await analyzeChannelWithOpenRouter.mutateAsync(channelId);

      toast({
        title: "AI Analysis Started",
        description: `${result.videosToAnalyze} videos from ${channelName} are being analyzed in the background.`,
      });
    } catch (error) {
      console.error('Error analyzing channel with OpenRouter:', error);
      toast({
        title: "AI Analysis Failed",
        description: "Failed to start AI analysis. Please try again later.",
        variant: "destructive",
      });
    }
  };

  // Handle refreshing a specific channel
  const handleRefreshChannel = async (channelId: number) => {
    try {
      // Set refresh start time for progress indicator
      setRefreshStartTime(Date.now());

      // Get channel name
      const channel = channels.find(c => c.id === channelId);
      const channelName = channel?.channelTitle || 'Channel';

      console.log(`Refreshing channel: ${channelName} (ID: ${channelId})`);

      // Initialize refresh status
      setRefreshStatus({
        isRefreshing: true,
        currentChannel: channelName,
        progress: 0,
        totalChannels: 1,
        completedChannels: 0
      });

      // Update status to show progress
      setRefreshStatus(prev => ({
        ...prev,
        progress: 50 // Set to 50% while refreshing
      }));

      // Create a background task for refreshing the channel
      const response = await apiRequest("POST", `/api/tasks/refresh-channel-direct`, {
        channelId: channelId.toString()
      });

      const task = await response.json();
      console.log(`Created background task ${task.id} for refreshing channel ${channelName}`);

      // Update status to show completion
      setRefreshStatus(prev => ({
        ...prev,
        progress: 100,
        completedChannels: 1
      }));

      // Update last refresh time
      const now = new Date();
      setLastRefreshTime(now);

      // Calculate refresh duration
      const endTime = Date.now();
      const duration = endTime - (refreshStartTime || endTime);

      // Show success toast
      toast({
        title: "Background Task Created",
        description: `Refresh task for ${channelName} created in ${(duration / 1000).toFixed(1)}s. Videos with transcriptions but no AI analysis will be automatically analyzed. Check the Tasks page for updates.`,
      });

      // Show a second toast after a delay to remind about the Tasks page
      setTimeout(() => {
        toast({
          title: "Background Processing",
          description: "Channel videos are being refreshed in the background. Check the Tasks page for status updates.",
        });
      }, 3000);
    } catch (error) {
      console.error('Error refreshing channel:', error);

      toast({
        title: "Error",
        description: "Failed to refresh channel. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Reset refresh start time and status after a short delay
      setTimeout(() => {
        setRefreshStartTime(null);
        setRefreshStatus({
          isRefreshing: false,
          currentChannel: null,
          progress: 0,
          totalChannels: 0,
          completedChannels: 0
        });
      }, 1500);
    }
  };

  // Handle deleting a channel
  const handleDeleteChannelData = async (channelId: number) => {
    try {
      // Show confirmation dialog
      const confirmed = window.confirm(
        "This will delete all videos, transcripts, and financial analyses for this channel. The channel itself will remain. Continue?"
      );

      if (!confirmed) return;

      // Show loading toast
      toast({
        title: "Deleting data",
        description: "Deleting all videos, transcripts, and financial analyses...",
      });

      console.log(`Deleting data for channel ID: ${channelId}`);
      await deleteChannelData.mutateAsync(channelId);

      // Only refresh the specific channel's videos, not all videos
      if (selectedChannelId === channelId) {
        // Refresh only the selected channel's videos
        console.log(`Refreshing videos for channel: ${channelId}`);
        setTimeout(() => {
          // Only refetch the videos for this specific channel
          refetchVideos();
        }, 1000);
      } else if (selectedChannelId === null) {
        // If we're in the "All Channels" view, we need to refresh all videos
        console.log('Refreshing all videos (only because we are in All Channels view)');
        setTimeout(() => {
          refetchAllVideos();
        }, 1000);
      }

      // Refresh channel video counts
      setTimeout(() => {
        refetchChannels();
      }, 1500);

    } catch (error) {
      console.error('Error deleting channel data:', error);
      toast({
        title: "Error",
        description: "Failed to delete channel data. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle deleting all channels' data
  const handleDeleteAllChannelsData = async () => {
    try {
      // Show confirmation dialog with the total number of videos to be deleted
      const confirmed = window.confirm(
        `This will delete all videos, transcripts, and financial analyses for ALL channels (${totalChannelVideos.toLocaleString()} videos total). The channels themselves will remain. Continue?`
      );

      if (!confirmed) return;

      // Show loading toast
      toast({
        title: "Deleting all data",
        description: "Deleting all videos, transcripts, and financial analyses from all channels...",
      });

      console.log('Deleting data for all channels');
      try {
        const result = await deleteAllChannelsData.mutateAsync();
        console.log('Delete all channels data result:', result);

        // Show success toast
        toast({
          title: "Success",
          description: `Successfully deleted all videos from ${channels.length} channels.`,
        });

        // Force a hard refresh of the UI
        // First, clear the selected channel to force UI reset
        setSelectedChannelId(null);

        // Also refresh the all-videos endpoint to update the combined feed
        console.log('Refreshing all videos');
        setTimeout(() => {
          refetchAllVideos();
        }, 1000);

        // Refresh channel video counts
        setTimeout(() => {
          refetchChannels();
        }, 1500);
      } catch (mutationError) {
        console.error('Mutation error:', mutationError);
        throw mutationError; // Re-throw to be caught by the outer try/catch
      }
    } catch (error) {
      console.error('Error deleting all channel data:', error);
      toast({
        title: "Error",
        description: typeof error === 'object' && error !== null && 'message' in error
          ? String(error.message)
          : "Failed to delete all channel data. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Delete old videos data (older than 7 days)
  const deleteOldVideosData = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("DELETE", "/api/youtube-channels/old-videos-data");
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to delete old videos data');
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels"] });
      queryClient.invalidateQueries({ queryKey: ["/api/videos"] });
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels/all-videos"] });
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels/video-counts"] });
    },
  });

  // Handle deleting old videos data
  const handleDeleteOldVideosData = async () => {
    try {
      // Show confirmation dialog
      const confirmed = window.confirm(
        "This will delete all videos older than 7 days from all channels. The channels themselves will remain. Continue?"
      );

      if (!confirmed) return;

      // Show loading toast
      toast({
        title: "Deleting old data",
        description: "Deleting all videos older than 7 days...",
      });

      console.log('Deleting old videos data');
      const result = await deleteOldVideosData.mutateAsync();
      console.log('Delete old videos data result:', result);

      // Show success toast
      toast({
        title: "Success",
        description: `Successfully deleted ${result.deletedCount} videos older than 7 days. Database space reclaimed: ${result.vacuumResult.spaceReclaimed.toFixed(2)} MB.`,
      });

      // Refresh the UI
      setTimeout(() => {
        refetchAllVideos();
      }, 1000);

      // Refresh channel video counts
      setTimeout(() => {
        refetchChannels();
      }, 1500);
    } catch (error) {
      console.error('Error deleting old videos data:', error);
      toast({
        title: "Error",
        description: typeof error === 'object' && error !== null && 'message' in error
          ? String(error.message)
          : "Failed to delete old videos data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteChannel = async (channelId: number) => {
    try {
      await deleteChannel.mutateAsync(channelId);

      // If the deleted channel was selected, select another one
      if (selectedChannelId === channelId) {
        const remainingChannels = channels.filter(c => c.id !== channelId);
        if (remainingChannels.length > 0) {
          setSelectedChannelId(remainingChannels[0].id);
        } else {
          setSelectedChannelId(null);
        }
      }
    } catch (error) {
      console.error('Error deleting channel:', error);
      toast({
        title: "Error",
        description: "Failed to delete channel. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Function to open NotebookLM with a specific account number
  const openNotebookLMWithAccount = (accountNumber: number) => {
    // Define a consistent window name
    const windowName = `NotebookLM_Account_${accountNumber}`;

    // First, try to find an existing window with this name
    try {
      // This will either get the existing window or create a blank one
      const existingWindow = window.open('', windowName);

      // Check if this is a valid NotebookLM window that's already open
      if (existingWindow &&
          !existingWindow.closed &&
          existingWindow.location &&
          existingWindow.location.href &&
          existingWindow.location.href.includes('notebooklm.google.com')) {

        // It's a valid window, focus it
        existingWindow.focus();

        // Update our state
        setOpenNotebookLMWindows(prev => ({
          ...prev,
          [accountNumber]: existingWindow
        }));

        // Show a toast
        toast({
          title: `Account ${accountNumber}`,
          description: "Window already open and focused.",
          duration: 2000
        });

        return;
      }

      // If we got here, either the window doesn't exist or it's not a valid NotebookLM window
      // Close it if it exists but isn't valid
      if (existingWindow && existingWindow.location && existingWindow.location.href !== 'about:blank') {
        existingWindow.close();
      }
    } catch (error) {
      console.log(`Error checking for existing window: ${error}`);
      // Continue with creating a new window
    }

    // Calculate window position based on account number
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    const windowWidth = Math.min(screenWidth / 2, 1000);
    const windowHeight = Math.min(screenHeight - 100, 800);

    // Position windows in a grid-like pattern with 4 columns for 12 accounts
    const col = (accountNumber - 1) % 4;
    const row = Math.floor((accountNumber - 1) / 4);

    const left = (col * windowWidth / 2);
    const top = 50 + (row * 80);

    // Window features
    const windowFeatures = `width=${windowWidth},height=${windowHeight},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes`;

    // Direct approach - open NotebookLM with a specific URL that forces login screen
    // For account 1, use regular URL
    let url = 'https://notebooklm.google.com/';

    // For accounts 2-12, use a URL that will force a login prompt
    if (accountNumber > 1) {
      // This URL parameter doesn't actually exist but forces Google to show account chooser
      url = 'https://notebooklm.google.com/?authuser=' + (accountNumber - 1);
    }

    // Open the window with our consistent name
    const newWindow = window.open(url, windowName, windowFeatures);

    if (newWindow) {
      // Update state with the new window reference
      setOpenNotebookLMWindows(prev => ({
        ...prev,
        [accountNumber]: newWindow
      }));

      // Store window names in localStorage for persistence across page refreshes
      try {
        const storedNames = localStorage.getItem('notebookLMWindowNames');
        let windowNames: string[] = storedNames ? JSON.parse(storedNames) : [];

        // Make sure the array is big enough for 12 accounts
        while (windowNames.length < 12) {
          windowNames.push('');
        }

        // Update the name for this account
        windowNames[accountNumber - 1] = windowName;

        // Save back to localStorage
        localStorage.setItem('notebookLMWindowNames', JSON.stringify(windowNames));
      } catch (error) {
        console.error('Error storing window name:', error);
      }

      // Set up an interval to check if the window is closed
      const checkInterval = setInterval(() => {
        try {
          // Try to access a property to verify the window is still valid
          if (newWindow.closed) {
            clearInterval(checkInterval);
            setOpenNotebookLMWindows(prev => {
              const updated = {...prev};
              updated[accountNumber] = null;
              return updated;
            });

            // Also update localStorage
            try {
              const storedNames = localStorage.getItem('notebookLMWindowNames');
              if (storedNames) {
                let windowNames: string[] = JSON.parse(storedNames);
                windowNames[accountNumber - 1] = '';
                localStorage.setItem('notebookLMWindowNames', JSON.stringify(windowNames));
              }
            } catch (error) {
              console.error('Error updating stored window names:', error);
            }

            console.log(`Window for Account ${accountNumber} was closed`);
          }
        } catch (error) {
          // If we get an error, the window reference is no longer valid
          clearInterval(checkInterval);
          setOpenNotebookLMWindows(prev => {
            const updated = {...prev};
            updated[accountNumber] = null;
            return updated;
          });
          console.log(`Window reference for Account ${accountNumber} is no longer valid`);
        }
      }, 1000);

      // Show a toast with account-specific instructions
      if (accountNumber === 1) {
        toast({
          title: `Account ${accountNumber} Opened`,
          description: "Using your primary Google account.",
          duration: 3000
        });
      } else {
        toast({
          title: `Account ${accountNumber} Opened`,
          description: "Click 'Use another account' to sign in with a different Google account.",
          duration: 5000
        });
      }
    } else {
      toast({
        title: "Popup Blocked",
        description: "Please allow popups for this site to open NotebookLM.",
        variant: "destructive"
      });
    }
  };

  // Function to add a new prompt
  const handleAddPrompt = () => {
    if (!newPromptTitle.trim() || !newPromptContent.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide both a title and content for your prompt.",
        variant: "destructive"
      });
      return;
    }

    const newPrompt = {
      id: Date.now().toString(),
      title: newPromptTitle.trim(),
      content: newPromptContent.trim()
    };

    setPrompts([...prompts, newPrompt]);
    setNewPromptTitle('');
    setNewPromptContent('');
    setIsAddingPrompt(false);

    toast({
      title: "Prompt Saved",
      description: `Your prompt "${newPromptTitle}" has been saved.`,
      duration: 3000
    });
  };

  // Function to update an existing prompt
  const handleUpdatePrompt = () => {
    if (!editingPromptId) return;

    if (!newPromptTitle.trim() || !newPromptContent.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide both a title and content for your prompt.",
        variant: "destructive"
      });
      return;
    }

    const updatedPrompts = prompts.map(prompt =>
      prompt.id === editingPromptId
        ? { ...prompt, title: newPromptTitle.trim(), content: newPromptContent.trim() }
        : prompt
    );

    setPrompts(updatedPrompts);
    setNewPromptTitle('');
    setNewPromptContent('');
    setEditingPromptId(null);

    toast({
      title: "Prompt Updated",
      description: `Your prompt "${newPromptTitle}" has been updated.`,
      duration: 3000
    });
  };

  // Function to delete a prompt
  const handleDeletePrompt = (id: string) => {
    const promptToDelete = prompts.find(p => p.id === id);
    if (!promptToDelete) return;

    const updatedPrompts = prompts.filter(prompt => prompt.id !== id);
    setPrompts(updatedPrompts);

    toast({
      title: "Prompt Deleted",
      description: `Your prompt "${promptToDelete.title}" has been deleted.`,
      duration: 3000
    });
  };

  // Function to copy a prompt to clipboard
  const handleCopyPrompt = (content: string, title: string) => {
    navigator.clipboard.writeText(content)
      .then(() => {
        toast({
          title: "Copied to Clipboard",
          description: `Prompt "${title}" has been copied to your clipboard.`,
          duration: 3000
        });
      })
      .catch(err => {
        console.error('Failed to copy prompt:', err);
        toast({
          title: "Copy Failed",
          description: "Could not copy to clipboard. Please try again.",
          variant: "destructive"
        });
      });
  };

  // State to track the next refresh time
  const [nextRefreshTime, setNextRefreshTime] = useState<Date | null>(null);

  // State to track the time remaining until next refresh
  const [timeUntilNextRefresh, setTimeUntilNextRefresh] = useState<string>("");

  // Update the time remaining every second
  useEffect(() => {
    if (!autoRefreshEnabled || !nextRefreshTime) return;

    // Immediately calculate and set the initial time
    const updateCountdown = () => {
      const now = new Date();
      const diffMs = nextRefreshTime.getTime() - now.getTime();

      if (diffMs <= 0) {
        setTimeUntilNextRefresh("Refreshing...");
        return;
      }

      const diffSeconds = Math.floor(diffMs / 1000);
      setTimeUntilNextRefresh(`${diffSeconds}s`);
    };

    // Update immediately when the effect runs
    updateCountdown();

    // Then set up the interval for subsequent updates
    const timer = setInterval(updateCountdown, 1000);

    return () => clearInterval(timer);
  }, [nextRefreshTime, autoRefreshEnabled]);

  // REMOVED: We no longer update the next refresh time based on lastViewCountRefreshTime
  // This was causing duplicate timers and premature refreshes
  // The next refresh time is now only set by the scheduleNextRefresh function

  // Function to format relative time for last refresh
  const formatRelativeTime = (date: Date): string => {
    // For very recent refreshes (less than 60 seconds ago), show exact time
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    // For very recent refreshes, show the actual time instead of relative time
    if (diffInSeconds < 60) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
      });
    }

    // For older refreshes, show relative time
    if (diffInSeconds < 3600) {
      const diffInMinutes = Math.floor(diffInSeconds / 60);
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
    }

    const diffInHours = Math.floor(diffInSeconds / 3600);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays !== 1 ? 's' : ''} ago`;
  };

  // Function to update previous VPH values
  const updatePreviousVphValues = useCallback((videos: YoutubeVideo[], isPreRefresh: boolean = false) => {
    const newPreviousValues: {[videoId: string]: {
      value: number,
      highestValue: number,
      previousValue: number,
      timestamp: number,
      previousTimestamp: number
    }} = {};
    const currentTimestamp = Date.now();

    // Log for debugging
    console.log(`Updating VPH values for ${videos.length} videos (isPreRefresh: ${isPreRefresh})`);

    // Create a copy of the displayed videos to update view counts
    if (!isPreRefresh) {
      // This is after a refresh, so we need to update the displayed videos with the new view counts
      const updatedVideos = [...displayedVideos];
      let updatesCount = 0;

      // Update the view counts in the displayed videos
      videos.forEach(refreshedVideo => {
        if (refreshedVideo.id) {
          // Find the corresponding video in the displayed videos
          const displayedVideoIndex = updatedVideos.findIndex(v => v.id === refreshedVideo.id);
          if (displayedVideoIndex !== -1) {
            let updated = false;

            // Update the view count in the displayed video
            if (refreshedVideo.statistics?.viewCount) {
              const oldViewCount = updatedVideos[displayedVideoIndex].statistics?.viewCount || 0;
              const newViewCount = refreshedVideo.statistics.viewCount;

              // Only update if the view count has actually changed
              if (oldViewCount !== newViewCount) {
                updatedVideos[displayedVideoIndex].statistics = {
                  ...updatedVideos[displayedVideoIndex].statistics,
                  viewCount: newViewCount
                };
                updated = true;
                updatesCount++;
              }
            }

            // Also update the viewCount property directly
            if (refreshedVideo.viewCount) {
              const oldViewCount = updatedVideos[displayedVideoIndex].viewCount || 0;
              const newViewCount = refreshedVideo.viewCount;

              // Only update if the view count has actually changed
              if (oldViewCount !== newViewCount) {
                updatedVideos[displayedVideoIndex].viewCount = newViewCount;
                updated = true;
                updatesCount++;
              }
            }

            // Log the update if any changes were made
            if (updated) {
              console.log(`Updated view count for video ${refreshedVideo.id.substring(0, 6)}... to ${refreshedVideo.statistics?.viewCount || refreshedVideo.viewCount}`);
            }
          }
        }
      });

      // Only update the displayed videos if there were actual changes
      if (updatesCount > 0) {
        console.log(`Updating displayed videos with ${updatesCount} view count changes`);
        setDisplayedVideos(updatedVideos);
      } else {
        console.log('No view count changes detected, skipping UI update');
      }
    }

    videos.forEach(video => {
      if (video.id && video.vph !== undefined) {
        // Get the existing data for this video (if it exists)
        const existingData = previousVphValues[video.id];

        // If we have existing data
        if (existingData) {
          // Different behavior based on whether this is before or after a refresh
          if (isPreRefresh) {
            // Before refresh: Just store the current values for comparison after refresh
            // Don't update the previousValue yet - we'll do that after the refresh
            newPreviousValues[video.id] = {
              ...existingData,
              // Mark this entry as pre-refresh data
              _preRefreshValue: video.vph
            };
          } else {
            // After refresh: Check if we should update the momentum tracking
            const timeSinceLastUpdate = currentTimestamp - existingData.timestamp;
            const threeMinutesInMs = 3 * 60 * 1000; // Reduced from 5 to 3 minutes for more responsive updates

            // If we have pre-refresh data or enough time has passed
            if (existingData._preRefreshValue !== undefined || timeSinceLastUpdate >= threeMinutesInMs) {
              // Use pre-refresh value as previous if available, otherwise use existing previous value
              const effectivePreviousValue = existingData._preRefreshValue !== undefined ?
                existingData._preRefreshValue : existingData.value;

              // Update the highest value if the current VPH is higher
              const newHighestValue = Math.max(existingData.highestValue, video.vph);

              newPreviousValues[video.id] = {
                value: video.vph,
                highestValue: newHighestValue,
                previousValue: effectivePreviousValue, // Use the effective previous value
                timestamp: currentTimestamp,
                previousTimestamp: existingData.timestamp,
                // Preserve the hasMomentum flag if it exists
                hasMomentum: existingData.hasMomentum
              };

              // Log significant changes for debugging
              const momentumDelta = video.vph - effectivePreviousValue;
              const momentumThreshold = Math.max(2, effectivePreviousValue * 0.01);
              const timeSincePrevious = currentTimestamp - existingData.previousTimestamp;
              const minutesSincePrevious = timeSincePrevious / (1000 * 60);

              // Check if this video has new momentum
              const hasNewMomentum = momentumDelta > 0 &&
                                    Math.abs(momentumDelta) >= momentumThreshold &&
                                    minutesSincePrevious >= 5;

              // Update the hasMomentum flag based on the new data
              if (hasNewMomentum) {
                // If it has new momentum, set the flag to true
                newPreviousValues[video.id].hasMomentum = true;
                console.log(`Video ${video.id.substring(0, 6)}... has new momentum: ${effectivePreviousValue} → ${video.vph} (Δ+${momentumDelta.toFixed(1)})`);
              } else if (!hasNewMomentum && existingData.hasMomentum) {
                // If it had momentum before but doesn't anymore, reset the flag
                newPreviousValues[video.id].hasMomentum = false;
                console.log(`Video ${video.id.substring(0, 6)}... lost momentum: ${effectivePreviousValue} → ${video.vph}`);
              }

              if (momentumDelta > 0 && Math.abs(momentumDelta) >= momentumThreshold) {
                console.log(`Significant VPH change for video ${video.id.substring(0, 6)}...: ${effectivePreviousValue} → ${video.vph} (Δ${momentumDelta > 0 ? '+' : ''}${momentumDelta.toFixed(1)})`);
              }
            }
          }
        } else {
          // For new videos without existing data
          newPreviousValues[video.id] = {
            value: video.vph,
            highestValue: video.vph,
            previousValue: video.vph, // For new videos, set previous value equal to current value
            timestamp: currentTimestamp,
            previousTimestamp: currentTimestamp - (3 * 60 * 1000), // Set previous timestamp to 3 minutes ago
            hasMomentum: false // Initialize with no momentum
          };
        }
      }
    });

    // Only update if we have new values to add
    if (Object.keys(newPreviousValues).length > 0) {
      setPreviousVphValues(prev => {
        // Create a merged object with the new values
        const merged = {
          ...prev,
          ...newPreviousValues
        };

        // Log the update for debugging
        console.log(`Updated VPH values for ${Object.keys(newPreviousValues).length} videos`);

        return merged;
      });
    }
  }, [previousVphValues, displayedVideos, setDisplayedVideos]);

  // Auto-refresh functionality

  // Function to handle auto-refresh interval change
  const handleIntervalChange = (value: string) => {
    if (value === "manual") {
      // If manual is selected, disable auto-refresh
      setAutoRefreshEnabled(false);
      localStorage.setItem('realtimeAutoRefreshEnabled', 'false');

      // Clear any existing interval
      if (autoRefreshIntervalId) {
        clearInterval(autoRefreshIntervalId);
        setAutoRefreshIntervalId(null);
      }

      toast({
        title: "Auto-refresh disabled",
        description: "Manual refresh mode activated",
        duration: 3000
      });

      return;
    }

    // Convert value to number
    const interval = parseInt(value);

    // Update interval state
    setAutoRefreshInterval(interval);
    localStorage.setItem('realtimeAutoRefreshInterval', interval.toString());

    // Enable auto-refresh if it's not already enabled
    if (!autoRefreshEnabled) {
      setAutoRefreshEnabled(true);
      localStorage.setItem('realtimeAutoRefreshEnabled', 'true');
    }

    // Clear any existing interval
    if (autoRefreshIntervalId) {
      clearInterval(autoRefreshIntervalId);
    }

    // Set up new interval
    const newIntervalId = setInterval(() => {
      // Only refresh if we're on the realtime tab - removed the refreshMetadata.isPending check
      // to ensure refresh happens even if a previous refresh is still pending
      if (activeTab === "realtime") {
        console.log(`Auto-refreshing view counts (interval: ${interval}s)`);

        // Store current VPH values before refreshing
        if (displayedVideos.length > 0) {
          updatePreviousVphValues(displayedVideos, true);
          updateCache(displayedVideos);
        }

        refreshMetadata.mutate(undefined, {
          onSuccess: () => {
            setLastViewCountRefreshTime(new Date());

            // Update VPH values after refresh
            if (displayedVideos.length > 0) {
              setTimeout(() => {
                updatePreviousVphValues(displayedVideos, false);
              }, 300);
            }
          }
        });
      }
    }, interval * 1000);

    setAutoRefreshIntervalId(newIntervalId);

    toast({
      title: "Auto-refresh enabled",
      description: `View counts will refresh every ${interval >= 60
        ? `${Math.floor(interval / 60)} ${interval >= 120 ? 'minutes' : 'minute'}`
        : `${interval} seconds`}`,
      duration: 3000
    });
  };

  // Function to handle auto-refresh toggle
  const handleAutoRefreshToggle = (enabled: boolean) => {
    setAutoRefreshEnabled(enabled);
    localStorage.setItem('realtimeAutoRefreshEnabled', enabled.toString());

    if (enabled) {
      // Clear any existing interval
      if (autoRefreshIntervalId) {
        clearInterval(autoRefreshIntervalId);
      }

      // Set up new interval
      const newIntervalId = setInterval(() => {
        // Only refresh if we're on the realtime tab - removed the refreshMetadata.isPending check
        // to ensure refresh happens even if a previous refresh is still pending
        if (activeTab === "realtime") {
          console.log(`Auto-refreshing view counts (interval: ${autoRefreshInterval}s)`);

          // Store current VPH values before refreshing
          if (displayedVideos.length > 0) {
            updatePreviousVphValues(displayedVideos, true);
            updateCache(displayedVideos);
          }

          refreshMetadata.mutate(undefined, {
            onSuccess: () => {
              setLastViewCountRefreshTime(new Date());

              // Update VPH values after refresh
              if (displayedVideos.length > 0) {
                setTimeout(() => {
                  updatePreviousVphValues(displayedVideos, false);
                }, 300);
              }
            }
          });
        }
      }, autoRefreshInterval * 1000);

      setAutoRefreshIntervalId(newIntervalId);

      toast({
        title: "Auto-refresh enabled",
        description: `View counts will refresh every ${autoRefreshInterval >= 60
          ? `${Math.floor(autoRefreshInterval / 60)} ${autoRefreshInterval >= 120 ? 'minutes' : 'minute'}`
          : `${autoRefreshInterval} seconds`}`,
        duration: 3000
      });
    } else {
      // Clear interval when disabled
      if (autoRefreshIntervalId) {
        clearInterval(autoRefreshIntervalId);
        setAutoRefreshIntervalId(null);
      }

      toast({
        title: "Auto-refresh disabled",
        description: "Manual refresh mode activated",
        duration: 3000
      });
    }
  };

  // Create a stable reference to track if the auto-refresh effect has already run
  const hasSetupRef = useRef(false);

  // Set up auto-refresh interval when component mounts or when auto-refresh settings change
  useEffect(() => {
    // Only set up interval if auto-refresh is enabled and we haven't already set it up
    if (autoRefreshEnabled && !hasSetupRef.current) {
      // Mark that we've set up the interval
      hasSetupRef.current = true;

      // Clear any existing interval or timeout
      if (autoRefreshIntervalId) {
        clearInterval(autoRefreshIntervalId);
        clearTimeout(autoRefreshIntervalId);
        setAutoRefreshIntervalId(null);
      }

      // Trigger an immediate refresh when auto-refresh is enabled
      if (activeTab === "realtime" && !refreshMetadata.isPending) {

        // Store current VPH values before refreshing
        if (displayedVideos.length > 0) {
          updatePreviousVphValues(displayedVideos, true);
          updateCache(displayedVideos);
        }

        refreshMetadata.mutate(undefined, {
          onSuccess: () => {
            const refreshTime = new Date();
            setLastViewCountRefreshTime(refreshTime);

            // We'll let scheduleNextRefresh handle setting the next refresh time
            // This prevents duplicate timers from being created

            // Update VPH values after refresh
            if (displayedVideos.length > 0) {
              setTimeout(() => {
                updatePreviousVphValues(displayedVideos, false);
              }, 300);
            }
          }
        });
      }

      // Set up new interval
      // Auto-refresh interval is being created

      // Use setTimeout instead of setInterval for more precise timing
      const scheduleNextRefresh = () => {
        // Calculate exact next refresh time based on current time
        const now = new Date();
        const nextTime = new Date(now.getTime() + (autoRefreshInterval * 1000));

        // Update the UI with the exact next refresh time - THIS IS THE ONLY PLACE WE SET THIS
        setNextRefreshTime(nextTime);

        return setTimeout(() => {
          // Only refresh if we're on the realtime tab
          if (activeTab === "realtime") {

            // Store current VPH values before refreshing
            if (displayedVideos.length > 0) {
              updatePreviousVphValues(displayedVideos, true);
              updateCache(displayedVideos);
            }

            refreshMetadata.mutate(undefined, {
              onSuccess: () => {
                const refreshTime = new Date();
                setLastViewCountRefreshTime(refreshTime);

                // Schedule the next refresh if auto-refresh is enabled
                if (autoRefreshEnabled) {
                  // Clear any existing timer first to prevent duplicate timers
                  if (autoRefreshIntervalId) {
                    clearTimeout(autoRefreshIntervalId);
                  }

                  // Schedule the next refresh
                  const nextTimeoutId = scheduleNextRefresh();
                  setAutoRefreshIntervalId(nextTimeoutId);
                }

                // Update VPH values after refresh
                if (displayedVideos.length > 0) {
                  setTimeout(() => {
                    updatePreviousVphValues(displayedVideos, false);
                  }, 300);
                }
              }
            });
          } else {
            // Still schedule the next refresh even if we skip this one
            // Clear any existing timer first to prevent duplicate timers
            if (autoRefreshIntervalId) {
              clearTimeout(autoRefreshIntervalId);
            }

            const nextTimeoutId = scheduleNextRefresh();
            setAutoRefreshIntervalId(nextTimeoutId);
          }
        }, autoRefreshInterval * 1000);
      };

      // Clear any existing timer before starting a new one
      if (autoRefreshIntervalId) {
        clearTimeout(autoRefreshIntervalId);
      }

      // Start the first timeout
      const newIntervalId = scheduleNextRefresh();
      setAutoRefreshIntervalId(newIntervalId);

      // Clean up interval when component unmounts or when dependencies change
      return () => {
        hasSetupRef.current = false;
        if (newIntervalId) {
          clearTimeout(newIntervalId);
        }
        if (autoRefreshIntervalId) {
          clearTimeout(autoRefreshIntervalId);
          clearInterval(autoRefreshIntervalId);
          setAutoRefreshIntervalId(null);
        }
      };
    } else if (!autoRefreshEnabled) {
      // If auto-refresh is disabled, clear any existing interval/timeout
      if (autoRefreshIntervalId) {
        clearTimeout(autoRefreshIntervalId);
        clearInterval(autoRefreshIntervalId);
        setAutoRefreshIntervalId(null);
      }
      hasSetupRef.current = false;
    }

    // Clean up function that runs when component unmounts or dependencies change
    return () => {
      if (autoRefreshIntervalId) {
        clearTimeout(autoRefreshIntervalId);
        clearInterval(autoRefreshIntervalId);
      }
    };
  }, [autoRefreshEnabled, autoRefreshInterval, activeTab]);

  // State to track the next metrics update time
  const [nextMetricsUpdateTime, setNextMetricsUpdateTime] = useState<Date | null>(null);

  // State to track the metrics collection interval (in minutes)
  const [metricsCollectionInterval, setMetricsCollectionInterval] = useState<number>(() => {
    // Try to load metrics collection interval from localStorage
    const savedInterval = localStorage.getItem('metricsCollectionInterval');
    // If there's a saved interval, use it; otherwise, default to 2 minutes
    return savedInterval ? parseInt(savedInterval) : 2;
  });

  // Function to handle metrics collection interval change
  const handleMetricsIntervalChange = useCallback((value: string) => {
    const minutes = parseInt(value);
    setMetricsCollectionInterval(minutes);

    // Save to localStorage for persistence
    localStorage.setItem('metricsCollectionInterval', minutes.toString());

    // Update the next update time based on the new interval
    const nextUpdate = new Date();
    nextUpdate.setMinutes(nextUpdate.getMinutes() + minutes);
    setNextMetricsUpdateTime(nextUpdate);

    // Show a toast notification to confirm the change
    toast({
      title: "Metrics Collection Interval Updated",
      description: `Data will now be collected every ${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`,
      duration: 3000
    });
  }, [toast]);

  // Collect metrics data for Data Analysis tab - now runs regardless of active tab
  useEffect(() => {
    // Always collect metrics on mount if we have videos to analyze
    if (displayedVideos.length > 0) {
      collectMetricsData();
      // Set the next update time based on the selected interval
      const nextUpdate = new Date();
      nextUpdate.setMinutes(nextUpdate.getMinutes() + metricsCollectionInterval);
      setNextMetricsUpdateTime(nextUpdate);
    }

    // Set up interval to collect metrics based on the selected interval
    // This provides updates for chart visualization at the user's preferred frequency
    const metricsInterval = setInterval(() => {
      // Only collect metrics if there are videos to analyze
      if (displayedVideos.length > 0) {
        collectMetricsData();
        // Update the next update time based on the selected interval
        const nextUpdate = new Date();
        nextUpdate.setMinutes(nextUpdate.getMinutes() + metricsCollectionInterval);
        setNextMetricsUpdateTime(nextUpdate);
      }
    }, metricsCollectionInterval * 60 * 1000); // Convert minutes to milliseconds

    // Optimize localStorage writes by debouncing
    const saveMetrics = () => {
      try {
        // Clean up data before saving to ensure localStorage doesn't grow too large
        const cleanedData = cleanupMetricsData(videoMetricsHistory);

        // Only update state if the data changed after cleanup
        if (cleanedData.length !== videoMetricsHistory.length) {
          setVideoMetricsHistory(cleanedData);
        }

        // Only save to localStorage if we have meaningful data
        if (cleanedData.length > 0) {
          // Check the size of the data before saving
          const dataString = JSON.stringify(cleanedData);
          const dataSizeKB = new Blob([dataString]).size / 1024;

          console.log(`Attempting to save ${cleanedData.length} metrics entries (${dataSizeKB.toFixed(1)}KB)`);

          // If data is too large (>2MB), keep only the most recent entries
          if (dataSizeKB > 2048) {
            console.warn(`Metrics data too large (${dataSizeKB.toFixed(1)}KB), keeping only recent entries`);
            const recentData = cleanedData.slice(-50); // Keep only last 50 entries
            localStorage.setItem('videoMetricsHistory', JSON.stringify(recentData));
            setVideoMetricsHistory(recentData);
          } else {
            localStorage.setItem('videoMetricsHistory', JSON.stringify(cleanedData));
          }
        }
      } catch (error) {
        console.error('Error saving metrics to localStorage:', error);

        // If we get a quota exceeded error, try aggressive cleanup
        if (error.name === 'QuotaExceededError' || error.message.includes('quota')) {
          console.warn('localStorage quota exceeded, performing aggressive cleanup');

          try {
            // Clear other large localStorage items first
            const keysToCheck = ['ytr_ranking_history', 'market_share_history', 'market_share_hourly_history'];
            keysToCheck.forEach(key => {
              const item = localStorage.getItem(key);
              if (item && item.length > 10000) {
                console.log(`Removing large localStorage item: ${key} (${(item.length / 1024).toFixed(1)}KB)`);
                localStorage.removeItem(key);
              }
            });

            // Keep only the most recent 20 metrics entries
            const recentData = videoMetricsHistory.slice(-20);
            localStorage.setItem('videoMetricsHistory', JSON.stringify(recentData));
            setVideoMetricsHistory(recentData);

            console.log(`Aggressive cleanup complete, kept ${recentData.length} recent entries`);
          } catch (retryError) {
            console.error('Failed to save even after aggressive cleanup:', retryError);
            // As a last resort, clear the metrics history entirely
            localStorage.removeItem('videoMetricsHistory');
            setVideoMetricsHistory([]);
          }
        }
      }
    };

    // Create a debounced version of saveMetrics to reduce frequent writes
    let saveTimeout: NodeJS.Timeout | null = null;
    const debouncedSaveMetrics = () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }
      saveTimeout = setTimeout(saveMetrics, 5000); // Wait 5 seconds before saving
    };

    // Save metrics when they change, but debounced
    debouncedSaveMetrics();

    // Set up event listener to save metrics before page unloads
    window.addEventListener('beforeunload', saveMetrics); // Use non-debounced version for unload

    // Set up hash change listener to collect metrics when switching to DA tab
    const handleHashChange = () => {
      if (window.location.hash === '#data-analysis') {
        collectMetricsData();
      }
    };
    window.addEventListener('hashchange', handleHashChange);

    return () => {
      if (saveTimeout) {
        clearTimeout(saveTimeout);
      }
      clearInterval(metricsInterval);
      window.removeEventListener('beforeunload', saveMetrics);
      window.removeEventListener('hashchange', handleHashChange);
      // Save metrics one last time before unmounting
      saveMetrics();
    };
  }, [collectMetricsData, cleanupMetricsData, displayedVideos.length, metricsCollectionInterval]);

  // Function to check if a specific account window is open
  const isAccountWindowOpen = (accountNumber: number): boolean => {
    try {
      const win = openNotebookLMWindows[accountNumber];
      if (!win) return false;

      // Check if window is closed
      if (win.closed) return false;

      // Try to access a property to verify the window is still valid
      const href = win.location?.href;

      // Check if it's a NotebookLM window
      return href && href.includes('notebooklm.google.com');
    } catch (error) {
      // If we get an error, the window reference is no longer valid
      return false;
    }
  };

  // Function to copy selected videos information to clipboard
  const copySelectedVideosInfo = () => {
    if (selectedVideos.length === 0) return;

    // Create a header with summary information
    const header = `# YouTube Trending Videos Analysis

**Selected Videos:** ${selectedVideos.length}
**Date:** ${new Date().toLocaleDateString()}
**Top VPH:** ${Math.max(...selectedVideos.map(v => v.vph || 0)).toLocaleString()} views per hour

## Video Details

`;

    // Create detailed information for each video
    const videosInfo = selectedVideos.map((video, index) => {
      const viewCount = video.statistics?.viewCount ? parseInt(video.statistics.viewCount).toLocaleString() : 'N/A';
      const likeCount = video.statistics?.likeCount ? parseInt(video.statistics.likeCount).toLocaleString() : 'N/A';
      const commentCount = video.statistics?.commentCount ? parseInt(video.statistics.commentCount).toLocaleString() : 'N/A';

      return `### ${index + 1}. ${video.title}

**Channel:** ${video.channelTitle}
**URL:** https://www.youtube.com/watch?v=${video.id}
**Views Per Hour:** ${Math.round(video.vph || 0).toLocaleString()}
**Total Views:** ${viewCount}
**Likes:** ${likeCount}
**Comments:** ${commentCount}
**Published:** ${new Date(video.publishedAt).toLocaleDateString()}
**Description:** ${video.description ? video.description.substring(0, 200) + (video.description.length > 200 ? '...' : '') : 'No description'}
`;
    }).join('\n---\n\n');

    // Combine header and video details
    const fullText = header + videosInfo + '\n\n## Analysis Notes\n\n(Add your analysis here)\n';

    navigator.clipboard.writeText(fullText).then(() => {
      toast({
        title: "Copied to clipboard",
        description: `Information about ${selectedVideos.length} videos has been copied to your clipboard in Markdown format.`,
        duration: 3000
      });
    }).catch(err => {
      console.error('Failed to copy videos info:', err);
      toast({
        title: "Copy failed",
        description: "Could not copy to clipboard. Please try again.",
        variant: "destructive"
      });
    });
  };

  // Handle getting video transcription
  const handleGetTranscription = async (videoId: string) => {
    try {
      console.log('Fetching transcription for video:', videoId);

      // Check if this video is already being processed
      if (pendingTranscriptions[videoId]) {
        console.log(`Transcription for video ${videoId} is already being processed`);
        return `Transcription is being processed in the background. Please check back later. You can view this video on YouTube: https://www.youtube.com/watch?v=${videoId}`;
      }

      // Use the background task version of getVideoTranscription
      const result = await getVideoTranscription(videoId);
      console.log('Transcription result:', result);

      // Check if result is an object with a taskId (background task created)
      if (result && typeof result === 'object' && result.taskId) {
        console.log(`Transcription task created with ID: ${result.taskId} for video ${videoId}`);
        return `Transcription is being processed in the background. Please check back later. You can view this video on YouTube: https://www.youtube.com/watch?v=${videoId}`;
      }

      // Check if result is an object with a transcription property
      if (result && typeof result === 'object' && typeof result.transcription === 'string') {
        console.log(`Returning transcription of length ${result.transcription.length} for video ${videoId}`);
        return result.transcription;
      }

      // Handle string result (for backward compatibility)
      if (result && typeof result === 'string') {
        console.log(`Returning transcription of length ${result.length} for video ${videoId}`);
        return result;
      } else {
        // Fallback message if result is not a string or object with transcription
        console.warn('Unexpected transcription result format:', result);
        const fallbackMessage = `No transcription available for this video. You can view it on YouTube: https://www.youtube.com/watch?v=${videoId}`;
        console.log(`Returning fallback message for video ${videoId}`);
        return fallbackMessage;
      }
    } catch (error) {
      console.error('Error getting transcription:', error);
      toast({
        title: "Transcription Notice",
        description: "Using fallback transcription method. Quality may vary.",
        variant: "default",
      });
      // Return a fallback message instead of null
      const errorMessage = `Transcription service encountered an issue. You can view this video on YouTube: https://www.youtube.com/watch?v=${videoId}`;
      console.log(`Returning error message for video ${videoId}`);
      return errorMessage;
    }
  };

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar - Always present, either full or mini */}
      <Sidebar />

      <main className="flex-1 overflow-auto">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col gap-4 mb-8">
            {/* Recent Tasks Widget for Mobile - Only shown when there are active tasks */}
            {!recentTasksVisible && recentTasks.length > 0 && recentTasks.some(task => task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING) && (
              <Button
                variant="outline"
                size="sm"
                className="md:hidden fixed bottom-4 right-4 z-10 shadow-md flex items-center gap-1"
                onClick={() => setRecentTasksVisible(true)}
              >
                <Clock className="h-4 w-4" />
                <span className="font-medium">Tasks</span>
                <Badge variant="secondary" className="ml-1">{recentTasks.filter(task => task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING).length}</Badge>
              </Button>
            )}
            {recentTasksVisible && recentTasks.some(task => task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING) && (
              <div className="md:hidden fixed bottom-4 right-4 z-10 w-[90%] max-w-[350px] shadow-lg">
              <div className="relative bg-card/80 backdrop-blur-sm rounded-md p-2 border border-border/60 shadow-md">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="text-sm font-medium">Recent Tasks</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() => window.location.href = '/tasks'}
                  >
                    View All
                  </Button>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full bg-background shadow-md z-20"
                  onClick={() => setRecentTasksVisible(false)}
                >
                  <X className="h-3 w-3" />
                </Button>

                {/* Last refresh time indicator */}
                <div className="text-xs text-muted-foreground flex items-center font-sans mb-2 bg-background/50 rounded-md px-2 py-1 border border-border/30">
                  <Clock className="h-3 w-3 mr-1" />
                  <span className="whitespace-nowrap">Last refresh: {' '}
                    <span className="font-semibold ml-1">
                      {lastRefreshTime ? (
                        <>
                          {formatRefreshTime(lastRefreshTime)}
                          {refreshDuration && (
                            <span className="ml-1 font-normal">({(refreshDuration / 1000).toFixed(1)}s)</span>
                          )}
                        </>
                      ) : (
                        'Never'
                      )}
                    </span>
                  </span>
                </div>

                {/* Refresh Button */}
                <Button
                  onClick={() => {
                    if (selectedChannelId !== null) {
                      // Refresh the selected channel
                      handleRefreshChannel(selectedChannelId);
                    } else {
                      // Refresh all channels
                      handleRefreshAll();
                    }
                  }}
                  disabled={refreshChannelVideos.isPending || refreshAllChannels.isPending}
                  variant="outline"
                  size="sm"
                  className="relative w-full mb-2 whitespace-nowrap"
                >
                  <RefreshCw
                    className={`h-4 w-4 mr-2 ${(refreshChannelVideos.isPending || refreshAllChannels.isPending) ? 'animate-spin' : ''}`}
                  />
                  {(refreshChannelVideos.isPending || refreshAllChannels.isPending)
                    ? 'Refreshing...'
                    : selectedChannelId !== null
                      ? `Refresh ${channels.find(c => c.id === selectedChannelId)?.channelTitle || 'Channel'}`
                      : 'Refresh All Channels'
                  }

                  {/* Show refresh progress indicator */}
                  {(refreshChannelVideos.isPending || refreshAllChannels.isPending) && refreshStartTime && (
                    <span className="absolute -bottom-1 left-0 h-1 bg-primary rounded-full" style={{
                      width: `${Math.min(((Date.now() - refreshStartTime) / 10000) * 100, 100)}%`,
                      transition: 'width 0.5s linear'
                    }}></span>
                  )}
                </Button>

                {recentTasks.length > 0 ? (
                  <RecentTasksWidget onViewAll={() => window.location.href = '/tasks'} compact={true} />
                ) : (
                  <div className="text-center py-3 text-muted-foreground text-sm bg-background/50 rounded-md">
                    <div className="flex justify-center mb-1">
                      <Clock className="h-4 w-4 opacity-50" />
                    </div>
                    No active tasks
                  </div>
                )}
              </div>
            </div>
            )}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* Menu button removed - now using the sidebar context */}
                <div className="flex items-center gap-2">
                  <Dialog open={addChannelDialogOpen} onOpenChange={setAddChannelDialogOpen}>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add YouTube Channel</DialogTitle>
                        <DialogDescription>
                          Enter a YouTube channel URL or handle (e.g., @GoogleDevelopers) to subscribe to it.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="channelUrl" className="text-right">
                            URL
                          </Label>
                          <Input
                            id="channelUrl"
                            placeholder="@handle or https://www.youtube.com/channel/UC..."
                            className="col-span-3"
                            value={newChannelUrl}
                            onChange={(e) => setNewChannelUrl(e.target.value)}
                          />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="videoLimit" className="text-right">
                            Videos to fetch
                          </Label>
                          <div className="col-span-3 flex items-center gap-2">
                            <Input
                              id="videoLimit"
                              type="number"
                              min="5"
                              max="15"
                              value={newChannelVideoLimit}
                              onChange={(e) => setNewChannelVideoLimit(parseInt(e.target.value) || 15)}
                              className="w-24"
                            />
                            <span className="text-sm text-muted-foreground">Recent videos to fetch (max 15)</span>
                          </div>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          type="submit"
                          onClick={handleAddChannel}
                          disabled={createChannel.isPending}
                        >
                          {createChannel.isPending ? 'Adding...' : 'Add Channel'}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>

              {/* Recent Tasks Widget - Desktop - Only shown when there are active tasks */}
              {recentTasks.length > 0 && recentTasks.some(task => task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING) && (
                <div className="hidden md:block w-[280px] flex-shrink-0">
                  <div className="bg-card/50 rounded-md p-2 shadow-sm border border-border/50">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="text-sm font-medium">Recent Tasks</h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs"
                        onClick={() => window.location.href = '/tasks'}
                      >
                        View All
                      </Button>
                    </div>

                    {/* Last refresh time indicator */}
                    <div className="text-xs text-muted-foreground flex items-center font-sans mb-2 bg-background/50 rounded-md px-2 py-1 border border-border/30">
                      <Clock className="h-3 w-3 mr-1" />
                      <span className="whitespace-nowrap">Last refresh: {' '}
                        <span className="font-semibold ml-1">
                          {lastRefreshTime ? (
                            <>
                              {formatRefreshTime(lastRefreshTime)}
                              {refreshDuration && (
                                <span className="ml-1 font-normal">({(refreshDuration / 1000).toFixed(1)}s)</span>
                              )}
                            </>
                          ) : (
                            'Never'
                          )}
                        </span>
                      </span>
                    </div>

                    {/* Recent Tasks Widget title */}

                    {/* Recent Tasks Widget */}
                    <div className="w-full max-w-full overflow-hidden">
                      <RecentTasksWidget onViewAll={() => window.location.href = '/tasks'} compact={true} />
                    </div>
                  </div>
                </div>
              )}

              {/* Grid slider moved to the filter row above */}
            </div>

            {/* Refresh Status Indicator */}
            {refreshStatus.isRefreshing && (
              <div className="bg-primary/10 border border-primary/30 rounded-md p-3 mb-4">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4 animate-spin text-primary" />
                    <span className="font-medium text-sm">
                      Refreshing {refreshStatus.currentChannel}
                    </span>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {refreshStatus.completedChannels} of {refreshStatus.totalChannels} channels completed
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
                  <div
                    className="bg-primary h-full transition-all duration-300 ease-in-out"
                    style={{ width: `${refreshStatus.progress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* Tabs for Video Feed and Channels */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="flex justify-between items-center mb-4">
                <TabsList>
                  <TabsTrigger value="feed">Video Feed</TabsTrigger>
                  <TabsTrigger value="realtime">Realtime</TabsTrigger>
                  <TabsTrigger value="ytr">YTR</TabsTrigger>
                  <TabsTrigger value="nlm">NLM</TabsTrigger>
                  <TabsTrigger value="data-analysis">DA</TabsTrigger>
                  <TabsTrigger value="rss">RSS</TabsTrigger>
                  <TabsTrigger value="txt">TXT</TabsTrigger>
                  <TabsTrigger value="txt-pro">TXT PRO</TabsTrigger>
                  <TabsTrigger value="channels">Channels</TabsTrigger>
                </TabsList>

                {/* Last refresh time indicator with countdown timer */}
                <div className="text-xs text-white bg-gray-900 rounded-md px-3 py-1.5 border border-gray-700 shadow-sm flex items-center">
                  <Clock className="h-3 w-3 mr-1 text-white" />
                  <span className="whitespace-nowrap">
                    {activeTab === "rss" ? (
                      <>Last refresh: {rssLastRefreshTime ? formatDistanceToNow(rssLastRefreshTime, { addSuffix: true }) : 'Never'}</>
                    ) : (
                      <>
                        Last refresh: {lastViewCountRefreshTime ? formatRelativeTime(lastViewCountRefreshTime) : 'Never'}
                        {autoRefreshEnabled && activeTab === "realtime" && (
                          <span className="ml-1 text-white font-medium">
                            (Next: <span className="text-white">{timeUntilNextRefresh || "waiting..."}</span>)
                          </span>
                        )}
                      </>
                    )}
                  </span>
                </div>
              </div>

              <TabsContent value="feed" className="space-y-4">
                {/* Channel selection and video filtering controls */}
                <div className="pb-2 px-1">
                  <h3 className="text-sm font-medium mb-2">Select Channel:</h3>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant={selectedChannelId === null ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        console.log('Switching to All Channels - fetching all videos');
                        // Show loading state
                        setIsSorting(true);
                        // Clear any existing videos to show loading state
                        setDisplayedVideos([]);
                        // Reset to page 1
                        setCurrentPage(1);
                        // Clear the selected channel to show all videos
                        setSelectedChannelId(null);
                        // Force a refetch of all videos
                        setTimeout(() => {
                          // This will use the fetchAll: true option since selectedChannelId is null
                          refetchAllVideos().then(() => {
                            // Reset sorting state after videos are loaded
                            setIsSorting(false);
                          }).catch(() => {
                            // Make sure we reset the sorting state even if there's an error
                            setIsSorting(false);
                          });
                        }, 100);
                      }}
                      className="whitespace-nowrap mb-1"
                    >
                      All Channels
                      {!isLoadingVideoCounts && totalChannelVideos > 0 && (
                        <span className="ml-1 text-xs bg-background/30 px-1.5 py-0.5 rounded-md">
                          {totalChannelVideos.toLocaleString()}
                        </span>
                      )}
                    </Button>

                    {channels.map(channel => {
                      return (
                        <Button
                          key={channel.id}
                          variant={selectedChannelId === channel.id ? "default" : "outline"}
                          size="sm"
                          onClick={() => {
                            console.log(`Switching to channel ${channel.id} (${channel.channelTitle})`);
                            // Show loading state
                            setIsSorting(true);
                            // Clear any existing videos to show loading state
                            setDisplayedVideos([]);
                            // Reset to page 1
                            setCurrentPage(1);
                            // Set the selected channel
                            setSelectedChannelId(channel.id);
                            // Force a refetch of channel videos
                            setTimeout(() => {
                              // This will trigger the useChannelVideos hook to fetch videos for this channel
                              refetchVideos().then(() => {
                                // Reset sorting state after videos are loaded
                                setIsSorting(false);
                              }).catch(() => {
                                // Make sure we reset the sorting state even if there's an error
                                setIsSorting(false);
                              });
                            }, 100);
                          }}
                          className="whitespace-nowrap mb-1"
                        >
                          {channel.channelTitle}
                          {!isLoadingVideoCounts && channelVideoCounts[channel.id] > 0 && (
                            <span className="ml-1 text-xs bg-background/30 px-1.5 py-0.5 rounded-md">
                              {channelVideoCounts[channel.id].toLocaleString()}
                            </span>
                          )}
                        </Button>
                      );
                    })}
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row justify-between gap-4 mb-4">
                  {/* Search - FILTER 1 (Highest Priority) - Searches only in video titles */}
                  <div className="relative w-full sm:w-64">
                    <Input
                      type="text"
                      placeholder="Search video titles..."
                      value={searchInputValue}
                      onChange={(e) => {
                        // Update the input value, which will be debounced
                        console.log(`Search input changed to: "${e.target.value}"`);
                        setSearchInputValue(e.target.value);
                      }}
                      className="pr-8"
                    />
                    {searchInputValue && (
                      <button
                        onClick={() => setSearchInputValue('')}
                        className="absolute inset-y-0 right-0 flex items-center pr-3"
                        aria-label="Clear search"
                      >
                        <X className="h-4 w-4 text-muted-foreground" />
                      </button>
                    )}
                  </div>

                  {/* Compact Grid Size Slider - Moved here from below */}
                  <div className="flex items-center gap-2 bg-card/50 rounded-md px-3 py-1.5 border border-border/50 shadow-sm">
                    <div className="text-xs font-medium whitespace-nowrap text-foreground">Grid:</div>
                    <Slider
                      value={[gridSize]}
                      min={1}
                      max={6}
                      step={1}
                      onValueChange={handleGridSizeChange}
                      className="w-[80px] mx-1"
                    />
                    <div className="text-xs font-medium min-w-[14px] text-center bg-primary/20 text-primary-foreground px-2 py-0.5 rounded-md">
                      {gridSize}
                    </div>
                  </div>

                  {/* Filters */}
                  <div className="flex flex-wrap gap-2 items-center">
                    {/* Improved sort dropdown with better organization */}
                    <div className="relative">
                      <Select
                        value={sortBy}
                        onValueChange={(value) => {
                          // Skip if already set to this value
                          if (sortBy === value) {
                            return;
                          }

                          console.log(`Changing sort to: ${value}`);

                          // Set loading state to show user something is happening
                          setIsSorting(true);

                          // Update the state first - this is what the API call will use
                          setSortBy(value as SortOption);

                          // Update the query key immediately - this is critical for preserving sort
                          setQuerySortKey(value as SortOption);

                          // Log the sort change for debugging
                          logger.debug(`Sort changed to ${value}, updating state`);

                          // Save to localStorage with YouTube-specific key
                          localStorage.setItem('youtubePageSortMethod', value);

                          // Remove any other sort methods that might interfere
                          localStorage.removeItem('preferredSortMethod');
                          localStorage.removeItem('preferredFinancialSortMethod');

                          // Show a toast notification to confirm the sort change
                          toast({
                            title: "Sort Changed",
                            description: `Videos sorted by ${COMBINED_SORT_OPTIONS.find(option => option.value === value)?.label || value}`,
                            duration: 2000
                          });

                          // Clear displayed videos to show loading state
                          setDisplayedVideos([]);

                          // Reset to page 1 when changing sort
                          setCurrentPage(1);

                          // Add a small delay to ensure state updates are processed
                          setTimeout(() => {
                            // Double-check that the sort is still correct before refetching
                            if (stableSortRef.current !== value) {
                              logger.warn(`Sort reference mismatch before refetch: ${stableSortRef.current} vs ${value}`);
                              stableSortRef.current = value as SortOption;
                            }

                            // Force a refetch with the new sort parameter
                            if (selectedChannelId === null) {
                              // If viewing all channels, use refetchAllVideos
                              refetchAllVideos().then(() => {
                                // Reset the sorting state only after the refetch is complete
                                setIsSorting(false);
                                // Double-check that the sort is still correct
                                if (stableSortRef.current !== value) {
                                  logger.warn(`Sort reference mismatch after refetch: ${stableSortRef.current} vs ${value}`);
                                  stableSortRef.current = value as SortOption;
                                }
                              }).catch(() => {
                                // Make sure we reset the sorting state even if there's an error
                                setIsSorting(false);
                              });
                            } else {
                              // If viewing a specific channel, use refetchVideos
                              refetchVideos().then(() => {
                                // Reset the sorting state only after the refetch is complete
                                setIsSorting(false);
                                // Double-check that the sort is still correct
                                if (stableSortRef.current !== value) {
                                  logger.warn(`Sort reference mismatch after refetch: ${stableSortRef.current} vs ${value}`);
                                  stableSortRef.current = value as SortOption;
                                }
                              }).catch(() => {
                                // Make sure we reset the sorting state even if there's an error
                                setIsSorting(false);
                              });
                            }
                          }, 50); // Small delay to ensure state updates are processed
                        }}
                        disabled={isSorting}
                      >
                        <SelectTrigger className="w-[220px]">
                          {isSorting && (
                            <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
                              <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                            </div>
                          )}
                          <SelectValue placeholder="Sort by">
                            {COMBINED_SORT_OPTIONS.find(option => option.value === sortBy)?.label || 'Trending (VPH ↓)'}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {/* Popular & Trending group */}
                          <SelectItem value="header-popular" disabled className="font-semibold text-primary">
                            Popular & Trending
                          </SelectItem>
                          {COMBINED_SORT_OPTIONS.filter(option => option.group === 'popular').map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}

                          {/* Content Type group */}
                          <SelectItem value="header-content" disabled className="font-semibold text-primary mt-2">
                            Content Type
                          </SelectItem>
                          {COMBINED_SORT_OPTIONS.filter(option => option.group === 'content').map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}

                          {/* Basic group */}
                          <SelectItem value="header-basic" disabled className="font-semibold text-primary mt-2">
                            Alphabetical
                          </SelectItem>
                          {COMBINED_SORT_OPTIONS.filter(option => option.group === 'basic').map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Channel Heading */}
                <div className="mb-4">
                  <div className="flex items-center justify-between border-b pb-2">
                    <h2 className="text-xl font-semibold flex items-center gap-2">
                      {selectedChannelId === null ? (
                        <>
                          All Channels
                          {!isLoadingVideoCounts && totalChannelVideos > 0 && (
                            <span className="text-sm font-normal text-muted-foreground bg-muted px-2 py-0.5 rounded-md">
                              {totalChannelVideos.toLocaleString()} videos
                            </span>
                          )}
                        </>
                      ) : (
                        <>
                          {channels.find(c => c.id === selectedChannelId)?.channelTitle || "Channel"}
                          {!isLoadingVideoCounts && selectedChannelId !== null && channelVideoCounts[selectedChannelId] > 0 && (
                            <span className="text-sm font-normal text-muted-foreground bg-muted px-2 py-0.5 rounded-md">
                              {channelVideoCounts[selectedChannelId].toLocaleString()} videos
                            </span>
                          )}
                        </>
                      )}
                    </h2>
                    <Button
                      onClick={() => {
                        if (selectedChannelId !== null) {
                          // Refresh the selected channel
                          handleRefreshChannel(selectedChannelId);
                        } else {
                          // Refresh all channels
                          handleRefreshAll();
                        }
                      }}
                      disabled={refreshChannelVideos.isPending || refreshAllChannels.isPending}
                      variant="outline"
                      size="sm"
                      className="relative whitespace-nowrap"
                    >
                      <RefreshCw
                        className={`h-4 w-4 mr-2 ${(refreshChannelVideos.isPending || refreshAllChannels.isPending) ? 'animate-spin' : ''}`}
                      />
                      {(refreshChannelVideos.isPending || refreshAllChannels.isPending)
                        ? 'Refreshing...'
                        : 'Refresh'}
                    </Button>
                  </div>
                </div>

                {/* Video grid */}
                {isLoadingVideos || isLoadingAllVideos || isSorting ? (
                  // Loading skeleton with message
                  <div>
                    <div className="text-center mb-4">
                      <div className="inline-flex items-center justify-center gap-2 bg-muted/50 px-4 py-2 rounded-md">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm font-medium">
                          {isLoadingVideos ? "Loading channel videos..." :
                           isSorting ? "Sorting and filtering videos..." :
                           "Loading videos from your channels..."}
                        </span>
                      </div>
                    </div>
                    <div className={getGridClass()}>
                      {[...Array(8)].map((_, i) => (
                        <div key={i} className="space-y-2">
                          <Skeleton className="h-[180px] w-full rounded-lg" />
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-2/3" />
                        </div>
                      ))}
                    </div>
                  </div>
                ) : displayedVideos.length === 0 ? (
                  // No videos message
                  <div className="text-center p-8 border rounded-lg">
                    <p className="text-muted-foreground mb-2">No videos found</p>
                    <p className="text-sm">
                      {selectedChannelId !== null
                        ? `No videos found from ${channels.find(c => c.id === selectedChannelId)?.channelTitle || 'this channel'}. Try refreshing the channel.`
                        : 'No videos found from any channel. Try refreshing all channels or add more channels.'
                      }
                    </p>
                    {/* Refresh button removed to reduce UI clutter */}
                  </div>
                ) : (
                  // Video grid
                  <div className="relative">
                    {/* Loading overlay */}
                    {isSorting && displayedVideos.length > 0 && (
                      <div className="absolute inset-0 flex items-center justify-center bg-background/50 z-10 rounded-md">
                        <div className="bg-background p-4 rounded-md shadow-md flex flex-col items-center">
                          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mb-2"></div>
                          <div className="text-sm font-medium">Sorting videos...</div>
                        </div>
                      </div>
                    )}

                    <div className={getGridClass()}>
                      {displayedVideos.map((video) => (
                        <YoutubeVideoCard
                          key={`${video.id}-${querySortKey}`}
                          video={video}
                          onTranscriptRequest={() => handleGetTranscription(video.id)}
                          hasTranscription={Boolean(video.hasTranscription)}
                        />
                      ))}

                      {/* Infinite Scroll Sentinel - Positioned at the bottom of the grid */}
                      <div
                        ref={sentinelRef}
                        className="col-span-full flex justify-center items-center py-4"
                        id="infinite-scroll-sentinel"
                        style={{ minHeight: '100px' }} /* Ensure the sentinel has enough height to be detected */
                      >
                        {isLoadingMore || isLoadingAllVideos ? (
                          <div className="flex flex-col items-center p-2">
                            <div className="animate-spin h-6 w-6 border-4 border-primary border-t-transparent rounded-full mb-2"></div>
                            <p className="text-sm text-muted-foreground">Loading more videos...</p>
                          </div>
                        ) : hasMore ? (
                          <div className="h-16 w-full flex items-center justify-center">
                            {/* This empty div serves as the sentinel for infinite scroll */}
                            {/* It's intentionally empty to be less intrusive */}
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground p-2">No more videos to load</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="ytr" className="space-y-4">
                <YtrTab
                  isActive={activeTab === "ytr"}
                  onRefresh={(refreshTime) => setLastViewCountRefreshTime(refreshTime)}
                  eliminatedVideos={eliminatedVideos}
                  onVideoElimination={handleVideoElimination}
                />
              </TabsContent>

              <TabsContent value="realtime" className="space-y-4">
                {/* Realtime Tab State */}
                <div>
                {(() => {
                  // Initialize sort settings from localStorage or defaults
                  const initialSortField = React.useMemo<'vph' | 'publishedAt' | 'title' | 'viewCount'>(() => {
                    const savedSortField = localStorage.getItem('youtubeRealtimeSortField');
                    return (savedSortField as 'vph' | 'publishedAt' | 'title' | 'viewCount') || 'vph';
                  }, []);

                  const initialSortDirection = React.useMemo<'asc' | 'desc'>(() => {
                    const savedSortDirection = localStorage.getItem('youtubeRealtimeSortDirection');
                    return (savedSortDirection as 'asc' | 'desc') || 'desc';
                  }, []);

                  // Local state for sorting - expanded to match main tab options
                  const [sortField, setSortField] = React.useState<'vph' | 'publishedAt' | 'title' | 'viewCount'>(initialSortField);
                  const [sortDirection, setSortDirection] = React.useState<'asc' | 'desc'>(initialSortDirection);

                  // State for new and trending filters
                  const [showNewFirst, setShowNewFirst] = React.useState<boolean>(false);
                  const [showTrendingFirst, setShowTrendingFirst] = React.useState<boolean>(false);

                  // Store the current sort settings for use during refresh - initialize with the initial values
                  const currentSortSettingsRef = React.useRef({
                    field: initialSortField,
                    direction: initialSortDirection
                  });

                  // Update the ref when sort settings change
                  React.useEffect(() => {
                    currentSortSettingsRef.current = { field: sortField, direction: sortDirection };
                    logger.verbose(`Sort settings updated in ref: field=${sortField}, direction=${sortDirection}`);
                  }, [sortField, sortDirection]);

                  // Toggle sort direction or change sort field
                  const handleSort = (field: 'vph' | 'publishedAt' | 'title' | 'viewCount') => {
                    if (sortField === field) {
                      // Toggle direction if same field
                      const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
                      setSortDirection(newDirection);
                      // Save to localStorage with realtime-specific key
                      localStorage.setItem('youtubeRealtimeSortDirection', newDirection);

                      // Also update the ref to ensure consistency
                      currentSortSettingsRef.current = {
                        field: sortField,
                        direction: newDirection
                      };
                    } else {
                      // Set new field with default direction
                      setSortField(field);
                      // Save to localStorage with realtime-specific key
                      localStorage.setItem('youtubeRealtimeSortField', field);

                      // Set sensible default directions based on field
                      let newDirection: 'asc' | 'desc';
                      if (field === 'publishedAt') {
                        newDirection = 'desc'; // Newest first
                      } else if (field === 'title') {
                        newDirection = 'asc'; // A-Z
                      } else if (field === 'vph' || field === 'viewCount') {
                        newDirection = 'desc'; // Highest first
                      } else {
                        newDirection = 'desc'; // Default
                      }

                      setSortDirection(newDirection);
                      // Save to localStorage with realtime-specific key
                      localStorage.setItem('youtubeRealtimeSortDirection', newDirection);

                      // Also update the ref to ensure consistency
                      currentSortSettingsRef.current = {
                        field: field,
                        direction: newDirection
                      };
                    }

                    // Make sure we're not using any shared localStorage keys
                    localStorage.removeItem('preferredSortMethod');
                    localStorage.removeItem('preferredFinancialSortMethod');

                    // Show a toast notification to confirm the sort change
                    toast({
                      title: "Sort Changed",
                      description: `Videos sorted by ${field}${sortDirection === 'asc' ? ' (ascending)' : ' (descending)'}`,
                      duration: 2000
                    });
                  };

                  // Handle toggling the new videos filter
                  const handleNewToggle = () => {
                    const newState = !showNewFirst;
                    setShowNewFirst(newState);

                    // Show toast notification
                    toast({
                      title: newState ? "New Videos First" : "Default Sorting",
                      description: newState
                        ? "Showing videos published in the last 6 hours at the top"
                        : "Returned to default sorting order",
                      duration: 3000
                    });

                    // If turning on new filter, turn off trending filter
                    if (newState && showTrendingFirst) {
                      setShowTrendingFirst(false);
                    }
                  };

                  // Handle toggling the trending videos sort
                  const handleTrendingToggle = () => {
                    // Toggle the trending state
                    const newState = !showTrendingFirst;
                    setShowTrendingFirst(newState);

                    if (newState) {
                      // When enabling trending sort, set sort field to VPH
                      setSortField('vph');
                      setSortDirection('desc');

                      // Save to localStorage
                      localStorage.setItem('youtubeRealtimeSortField', 'vph');
                      localStorage.setItem('youtubeRealtimeSortDirection', 'desc');

                      // Update the ref to ensure consistency
                      currentSortSettingsRef.current = {
                        field: 'vph',
                        direction: 'desc'
                      };

                      // Show toast notification
                      toast({
                        title: "Trending Videos First",
                        description: "Showing videos sorted by momentum (VPH)",
                        duration: 3000
                      });
                    } else {
                      // When disabling trending sort, return to default sort
                      toast({
                        title: "Default Sorting",
                        description: "Returned to default sorting order",
                        duration: 3000
                      });
                    }

                    // If turning on trending filter, turn off new filter
                    if (newState && showNewFirst) {
                      setShowNewFirst(false);
                    }
                  };



                  // Sort videos based on current sort settings
                  const sortedVideos = React.useMemo(() => {
                    if (!displayedVideos || displayedVideos.length === 0) return [];

                    // Helper function to check if a video is new (published in the last 6 hours)
                    const isNewVideo = (video: YoutubeVideo) => {
                      const publishedDate = new Date(video.publishedAt);
                      const now = new Date();
                      const sixHoursAgo = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                      return publishedDate > sixHoursAgo;
                    };

                    // Helper function to check if a video is trending
                    const isTrendingVideo = (video: YoutubeVideo) => {
                      if (previousVphValues[video.id]) {
                        const prevVphData = previousVphValues[video.id];
                        const previousVph = prevVphData.previousValue || 0;
                        const currentVph = video.vph || 0;

                        // Calculate absolute and percentage change
                        const momentumDelta = currentVph - previousVph;
                        const percentageChange = previousVph > 0 ? (momentumDelta / previousVph) * 100 : 0;

                        // Dynamic threshold based on previous VPH value
                        // Higher threshold for higher VPH values to reduce noise
                        const momentumThreshold = Math.max(2, previousVph * 0.01);

                        // Check time between measurements
                        const timeSincePrevious = prevVphData.timestamp - prevVphData.previousTimestamp;
                        const minutesSincePrevious = timeSincePrevious / (1000 * 60);

                        // Consider a video trending if:
                        // 1. It has positive momentum above the threshold
                        // 2. Enough time has passed between measurements (at least 5 minutes)
                        // 3. For videos with high VPH, require a higher percentage change
                        return momentumDelta > 0 &&
                               Math.abs(momentumDelta) >= momentumThreshold &&
                               minutesSincePrevious >= 5 &&
                               (previousVph < 1000 || percentageChange >= 1); // Higher bar for high-VPH videos
                      }
                      return false;
                    };

                    // Create a stable copy of the videos to sort
                    const videosToSort = [...displayedVideos];

                    // Always use the current sort settings
                    const actualSortField = sortField;
                    const actualSortDirection = sortDirection;

                    // Log the sort settings being used
                    logger.verbose(`Sorting videos with: field=${actualSortField}, direction=${actualSortDirection}, newFirst=${showNewFirst}, trendingFirst=${showTrendingFirst}`);

                    return videosToSort.sort((a, b) => {
                      // Apply new videos filter first if enabled
                      if (showNewFirst) {
                        const aIsNew = isNewVideo(a);
                        const bIsNew = isNewVideo(b);
                        if (aIsNew && !bIsNew) return -1;
                        if (!aIsNew && bIsNew) return 1;
                      }

                      // Apply trending filter next if enabled
                      if (showTrendingFirst) {
                        const aIsTrending = isTrendingVideo(a);
                        const bIsTrending = isTrendingVideo(b);
                        if (aIsTrending && !bIsTrending) return -1;
                        if (!aIsTrending && bIsTrending) return 1;
                      }

                      // Apply regular sorting if no filters match or as a tiebreaker
                      let comparison = 0;

                      if (actualSortField === 'vph') {
                        comparison = (b.vph || 0) - (a.vph || 0);
                      } else if (actualSortField === 'publishedAt') {
                        comparison = new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
                      } else if (actualSortField === 'title') {
                        comparison = a.title.localeCompare(b.title);
                      } else if (actualSortField === 'viewCount') {
                        comparison = (b.viewCount || 0) - (a.viewCount || 0);
                      }

                      return actualSortDirection === 'asc' ? -comparison : comparison;
                    });
                  }, [
                    displayedVideos,
                    sortField,
                    sortDirection,
                    showNewFirst,
                    showTrendingFirst,
                    previousVphValues
                  ]);

                  // Get sort icon based on current state
                  const getSortIcon = (field: 'vph' | 'publishedAt' | 'title' | 'viewCount') => {
                    if (sortField !== field) return null;
                    return sortDirection === 'asc' ?
                      <ArrowUpRight className="inline-block h-3 w-3 ml-1" /> :
                      <ArrowUpRight className="inline-block h-3 w-3 ml-1 transform rotate-180" />;
                  };

                  return (
                    <>
                      <div className="flex justify-between items-center mb-4">
                        <div className="flex items-center gap-2">
                          <h2 className="text-xl font-semibold flex items-center gap-2">
                            <Zap className="h-5 w-5 text-yellow-500" />
                            Realtime Videos
                          </h2>
                          <div className="flex items-center gap-2">
                            {/* New Videos Toggle Button */}
                            <Badge
                              variant="outline"
                              className={`ml-2 px-3 py-1 rounded-full cursor-pointer ${
                                showNewFirst ? 'bg-green-200 text-green-900' : 'bg-green-100 text-green-800 hover:bg-green-200'
                              }`}
                              onClick={handleNewToggle}
                              title={
                                showNewFirst ? "Click to disable new videos filter" : "Click to show new videos first"
                              }
                            >
                              <span className="font-bold mr-1">N</span>
                              {sortedVideos.filter(video => {
                                const publishedDate = new Date(video.publishedAt);
                                const now = new Date();
                                const sixHoursAgo = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                                return publishedDate > sixHoursAgo;
                              }).length}
                              {showNewFirst && <span className="ml-1">↑</span>}
                            </Badge>

                            {/* Trending Videos Sort Button */}
                            <Badge
                              variant="outline"
                              className={`ml-2 px-3 py-1 rounded-full cursor-pointer ${
                                showTrendingFirst ? 'bg-green-200 text-green-900' : 'bg-green-100 text-green-800 hover:bg-green-200'
                              }`}
                              onClick={handleTrendingToggle}
                              title={
                                showTrendingFirst ? "Click to disable trending videos sort" : "Click to sort videos by momentum (VPH)"
                              }
                            >
                              <span className="font-bold mr-1">Trending</span>
                              {sortedVideos.filter(video => {
                                // Check if video has positive momentum
                                if (previousVphValues[video.id]) {
                                  const prevVphData = previousVphValues[video.id];
                                  const previousVph = prevVphData.previousValue || 0;
                                  const currentVph = video.vph || 0;
                                  const momentumDelta = currentVph - previousVph;
                                  const momentumThreshold = Math.max(2, previousVph * 0.01);
                                  const timeSincePrevious = prevVphData.timestamp - prevVphData.previousTimestamp;
                                  const minutesSincePrevious = timeSincePrevious / (1000 * 60);

                                  // If momentum is positive and exceeds threshold and time between measurements is sufficient
                                  return momentumDelta > 0 && Math.abs(momentumDelta) >= momentumThreshold && minutesSincePrevious >= 5;
                                }
                                return false;
                              }).length}
                              {showTrendingFirst && <span className="ml-1">↑</span>}
                            </Badge>

                            {/* Auto-refresh Controls */}
                            <div className="flex items-center gap-2 ml-4 bg-card/50 rounded-md px-3 py-1.5 border border-border/50 shadow-sm">
                              <div className="flex items-center gap-2">
                                <span className="text-xs font-medium text-white">Auto-refresh:</span>
                                <Select
                                  value={autoRefreshEnabled ? autoRefreshInterval.toString() : "manual"}
                                  onValueChange={handleIntervalChange}
                                >
                                  <SelectTrigger className="h-7 w-[110px] text-xs">
                                    <SelectValue placeholder="Select interval" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="manual">Manual</SelectItem>
                                    <SelectItem value="30">30 seconds</SelectItem>
                                    <SelectItem value="60">1 minute</SelectItem>
                                    <SelectItem value="120">2 minutes</SelectItem>
                                    <SelectItem value="300">5 minutes</SelectItem>
                                    <SelectItem value="600">10 minutes</SelectItem>
                                  </SelectContent>
                                </Select>
                                <div className="flex items-center gap-1">
                                  <span className="text-xs text-white">On</span>
                                  <Switch
                                    checked={autoRefreshEnabled}
                                    onCheckedChange={handleAutoRefreshToggle}
                                    className="data-[state=checked]:bg-green-500"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {/* Work with Selected Videos Button */}
                          {selectedVideos.length > 0 && (
                            <Button
                              onClick={() => {
                                // Switch to NLM tab to work with selected videos
                                setActiveTab('nlm');
                                toast({
                                  title: `${selectedVideos.length} videos selected`,
                                  description: "Switch to NLM tab to analyze these videos with NotebookLM",
                                  duration: 3000
                                });
                              }}
                              variant="default"
                              size="sm"
                              className="relative whitespace-nowrap"
                            >
                              <Sparkles className="h-4 w-4 mr-2" />
                              Work with {selectedVideos.length} Selected {selectedVideos.length === 1 ? 'Video' : 'Videos'}
                            </Button>
                          )}

                          {/* Refresh View Counts Button with Auto-Refresh (Realtime tab only) */}
                          <div className="flex items-center gap-2">
                              <Button
                                onClick={() => {
                                  console.log('Refreshing video metadata only (no transcripts or AI analysis)');
                                  toast({
                                    title: "Updating view counts",
                                    description: "Refreshing in the background. The page will update automatically.",
                                    duration: 3000
                                  });

                                  // Store current VPH values before refreshing with the pre-refresh flag
                                  if (sortedVideos.length > 0) {
                                    updatePreviousVphValues(sortedVideos, true); // Mark as pre-refresh data

                                    // Make sure our cache is up to date with current videos
                                    updateCache(sortedVideos);
                                  }

                                  // Store the current video IDs and their positions for consistent ordering
                                  const currentVideoOrder = new Map(sortedVideos.map((video, index) => [video.id, index]));

                                  refreshMetadata.mutate(undefined, {
                                    onSuccess: () => {
                                      const refreshTime = new Date();
                                      setLastViewCountRefreshTime(refreshTime);

                                      // Set the next refresh time if auto-refresh is enabled
                                      if (autoRefreshEnabled) {
                                        // Calculate exact next refresh time based on current time
                                        const now = new Date();
                                        const nextTime = new Date(now.getTime() + (autoRefreshInterval * 1000));
                                        setNextRefreshTime(nextTime);
                                        console.log(`Next refresh scheduled for: ${nextTime.toLocaleTimeString()}`);
                                      }

                                      // After refresh completes, update VPH values with the new data
                                      // This ensures we maintain momentum tracking through the refresh
                                      if (sortedVideos.length > 0) {
                                        // Small delay to ensure the sortedVideos has been updated with new data
                                        setTimeout(() => {
                                          // Get the current videos after refresh
                                          const currentVideos = displayedVideos;

                                          // Process post-refresh data
                                          updatePreviousVphValues(currentVideos, false);

                                          // Log the video order consistency
                                          const newVideoOrder = new Map(currentVideos.map((video, index) => [video.id, index]));
                                          let orderChanges = 0;

                                          currentVideoOrder.forEach((oldIndex, videoId) => {
                                            const newIndex = newVideoOrder.get(videoId);
                                            if (newIndex !== undefined && Math.abs(newIndex - oldIndex) > 3) {
                                              orderChanges++;
                                            }
                                          });

                                          console.log(`Video order changes after refresh: ${orderChanges} significant position changes`);
                                          console.log(`Cache now contains ${cachedVideos.length} videos`);
                                        }, 300);
                                      }
                                    }
                                  });
                                }}
                                variant="default"
                                size="sm"
                                className="relative whitespace-nowrap bg-green-600 hover:bg-green-700 text-white"
                              >
                                <RefreshCw
                                  className={`h-4 w-4 mr-2 ${refreshMetadata.isPending ? 'animate-spin' : ''}`}
                                />
                                {refreshMetadata.isPending ? 'Updating in background...' : 'Update View Counts Only'}
                              </Button>

                              {/* Removed duplicate refresh indicator as it's now shown at the top */}
                            </div>


                        </div>
                      </div>



                      {/* Small refresh indicator icon with fixed height container */}
                      <div className="sticky top-0 right-0 z-10 flex justify-end h-8">
                        {refreshMetadata.isPending && (
                          <div className="m-2">
                            <Loader2 className="h-4 w-4 animate-spin text-primary" />
                          </div>
                        )}
                      </div>

                      {/* Realtime Video Grid */}
                      {/* Progressive Loading: Show content immediately with placeholders */}
                      {displayedVideos.length === 0 && !sortedVideos.length ? (
                        <div>
                          <div className="text-center mb-4">
                            <div className="inline-flex items-center justify-center gap-2 bg-muted/50 px-4 py-2 rounded-md">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span className="text-sm font-medium">Loading videos...</span>
                            </div>
                          </div>
                          <div className="space-y-2">
                            {[...Array(8)].map((_, i) => (
                              <div key={i} className="grid grid-cols-[1fr_auto_auto] gap-4 items-center p-2 rounded-md">
                                <div className="flex items-center gap-3">
                                  <Skeleton className="w-16 h-9 rounded-md" />
                                  <div>
                                    <Skeleton className="h-4 w-40" />
                                    <Skeleton className="h-3 w-24 mt-1" />
                                  </div>
                                </div>
                                <Skeleton className="h-4 w-24" />
                                <Skeleton className="h-4 w-16" />
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : displayedVideos.length === 0 && sortedVideos.length === 0 && !isLoadingVideos && !isLoadingAllVideos ? (
                        // No videos message
                        <div className="text-center p-8 border rounded-lg">
                          <p className="text-muted-foreground mb-2">No videos found</p>
                          <p className="text-sm">
                            Try refreshing your channels or adding more channels to see content here.
                          </p>
                        </div>
                      ) : (
                        // Video table with sortable columns
                        <div className="relative">
                          {/* Small loading indicator with fixed position - won't cause layout shifts */}
                          <div className="absolute top-0 right-0 m-4 z-10 h-4 w-4 flex items-center justify-center">
                            {isLoadingAllVideosState && (
                              <Loader2 className="h-4 w-4 animate-spin text-primary" />
                            )}
                          </div>
                          {/* Table header */}
                          <div className="grid grid-cols-[40px_1fr_110px_110px_90px_50px] gap-2 mb-2 px-2 py-2 bg-muted/50 rounded-md font-medium text-sm">
                            <div className="flex items-center justify-center">
                              {selectedVideos.length > 0 ? (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                  onClick={() => setSelectedVideos([])}
                                  title="Clear all selections"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              ) : (
                                <span className="text-xs text-white">Select</span>
                              )}
                            </div>
                            <button
                              onClick={() => handleSort('title')}
                              className="text-left flex items-center hover:text-primary transition-colors"
                            >
                              <span className="text-white">Content</span> {getSortIcon('title')}
                            </button>
                            <button
                              onClick={() => handleSort('publishedAt')}
                              className="flex items-center justify-start hover:text-primary transition-colors"
                            >
                              <div className="flex flex-col items-start">
                                <span className="text-white">Published</span>
                                <span className="text-xs text-white">Date & Time</span>
                              </div>
                              {getSortIcon('publishedAt')}
                            </button>
                            <button
                              onClick={() => handleSort('viewCount')}
                              className="flex items-center justify-start hover:text-primary transition-colors"
                            >
                              <span className="text-white">Total Views</span> {getSortIcon('viewCount')}
                            </button>
                            <button
                              onClick={() => handleSort('vph')}
                              className="flex items-center justify-start hover:text-primary transition-colors"
                            >
                              <span className="text-white">VPH</span> {getSortIcon('vph')}
                            </button>
                            <div className="flex items-center justify-center">
                              <span className="text-xs text-white" title="Copy Transcription">T</span>
                            </div>
                          </div>

                          {/* Video list - always use sortedVideos to ensure we show data during refresh */}
                          <div className="space-y-2">
                            {(sortedVideos.length > 0 ? sortedVideos : displayedVideos).slice(0, 100).map((video) => (
                              <div
                                key={video.id}
                                className={`grid grid-cols-[40px_1fr_110px_110px_90px_50px] gap-2 items-center p-2 hover:bg-muted/30 rounded-md transition-colors ${selectedVideos.some(v => v.id === video.id) ? 'bg-muted/50 border border-primary/30' : ''} ${
                                  (() => {
                                    // Check if video has positive momentum
                                    if (previousVphValues[video.id]) {
                                      const prevVphData = previousVphValues[video.id];
                                      const previousVph = prevVphData.previousValue || 0;
                                      const currentVph = video.vph || 0;
                                      const momentumDelta = currentVph - previousVph;

                                      // Use the exact same threshold as the old app (1% or 2 views, whichever is greater)
                                      const momentumThreshold = Math.max(2, previousVph * 0.01);
                                      const timeSincePrevious = prevVphData.timestamp - prevVphData.previousTimestamp;
                                      const minutesSincePrevious = timeSincePrevious / (1000 * 60);

                                      // Check if this video has momentum - only positive momentum counts
                                      const hasMomentum = momentumDelta > 0 && Math.abs(momentumDelta) >= momentumThreshold && minutesSincePrevious >= 5;

                                      // Store the momentum state in the prevVphData object to persist it
                                      if (hasMomentum && !prevVphData.hasMomentum) {
                                        // If it has momentum now but didn't before, update the state
                                        setPreviousVphValues(prev => ({
                                          ...prev,
                                          [video.id]: {
                                            ...prev[video.id],
                                            hasMomentum: true
                                          }
                                        }));
                                      } else if (!hasMomentum && prevVphData.hasMomentum) {
                                        // If it had momentum before but doesn't anymore, reset the flag
                                        setPreviousVphValues(prev => ({
                                          ...prev,
                                          [video.id]: {
                                            ...prev[video.id],
                                            hasMomentum: false
                                          }
                                        }));
                                      }

                                      // Return the green border if the video has positive momentum
                                      if (hasMomentum) {
                                        return 'border-2 border-green-500 bg-green-500/10';
                                      }
                                    }
                                    return '';
                                  })()
                                }`}
                                style={{ cursor: 'pointer' }}
                              >
                                <div
                                  className="flex items-center justify-center"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (selectedVideos.some(v => v.id === video.id)) {
                                      setSelectedVideos(selectedVideos.filter(v => v.id !== video.id));
                                    } else {
                                      setSelectedVideos([...selectedVideos, video]);
                                    }
                                  }}
                                >
                                  <input
                                    type="checkbox"
                                    checked={selectedVideos.some(v => v.id === video.id)}
                                    readOnly
                                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                                  />
                                </div>
                                <div
                                  className="flex items-center gap-3 overflow-hidden"
                                  onClick={() => window.open(`https://www.youtube.com/watch?v=${video.id}`, '_blank')}
                                >
                                  <img
                                    src={video.thumbnail}
                                    alt={video.title}
                                    className="w-16 h-9 object-cover rounded-md flex-shrink-0"
                                  />
                                  <div className="min-w-0 overflow-hidden max-w-full">
                                    <p className="font-medium text-sm truncate max-w-[calc(100%-40px)] text-white" title={video.title}>{video.title}</p>
                                    <div className="flex items-center gap-1">
                                      <p className="text-xs text-white/70 truncate max-w-[calc(100%-40px)]" title={video.channelTitle}>{video.channelTitle}</p>
                                      {/* New Video Tag */}
                                      {(() => {
                                        const publishedDate = new Date(video.publishedAt);
                                        const now = new Date();
                                        const sixHoursAgo = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                                        return publishedDate > sixHoursAgo ? (
                                          <Badge
                                            variant="outline"
                                            className="ml-1 flex-shrink-0 bg-green-100 text-green-800 border-green-200 h-5 px-1 text-[10px]"
                                          >
                                            <span className="font-bold">N</span>
                                          </Badge>
                                        ) : null;
                                      })()}
                                    </div>
                                  </div>
                                </div>
                                <div className="text-sm text-white whitespace-nowrap flex flex-col justify-center items-start h-9">
                                  <div>
                                    {new Date(video.publishedAt).toLocaleDateString('en-IN', {
                                      day: 'numeric',
                                      month: 'short',
                                      year: 'numeric'
                                    })}
                                  </div>
                                  <div className="text-xs text-white/70">
                                    {new Date(video.publishedAt).toLocaleTimeString('en-US', {
                                      hour: 'numeric',
                                      minute: '2-digit',
                                      hour12: true
                                    })}
                                  </div>
                                </div>
                                <div className="text-sm text-white whitespace-nowrap flex justify-start items-center h-9">
                                  {isLiveStream(video.title || '', video.contentDetails?.duration) && !isEndedLiveStream(video.title || '', video.isUnplayable) ? (
                                    <span className="text-red-500 font-medium flex items-center">
                                      <Radio className="w-3 h-3 mr-1 text-red-500 animate-pulse" />
                                      {(() => {
                                        // Get view count from the most reliable source
                                        let viewCount = 0;
                                        if (video.statistics?.viewCount) {
                                          viewCount = Number(video.statistics.viewCount);
                                        } else if (video.viewCount) {
                                          viewCount = typeof video.viewCount === 'string' ?
                                            Number(video.viewCount) : video.viewCount;
                                        }

                                        // Validate the view count is reasonable
                                        if (isNaN(viewCount) || viewCount < 0 || viewCount > 10000000000) {
                                          return "0 watching now";
                                        }

                                        // Additional validation for suspiciously high live viewer counts
                                        // Live streams typically have fewer concurrent viewers than total views
                                        let isSuspicious = false;

                                        // For small channels, more than 10,000 concurrent viewers is unusual
                                        if (viewCount > 10000) {
                                          // Try to get subscriber count if available
                                          const subscriberCount = video.statistics?.subscriberCount ?
                                            Number(video.statistics.subscriberCount) : 0;

                                          // If we have subscriber count, check if view count is suspiciously high
                                          if (subscriberCount > 0 && viewCount > subscriberCount * 0.1) {
                                            // More than 10% of subscribers watching simultaneously is unusual
                                            isSuspicious = true;
                                          } else if (viewCount > 100000) {
                                            // More than 100K concurrent viewers is very rare
                                            isSuspicious = true;
                                          }
                                        }

                                        // Add warning indicator for suspicious view counts
                                        if (isSuspicious) {
                                          return `${viewCount.toLocaleString()} watching now ⚠️`;
                                        }

                                        return `${viewCount.toLocaleString()} watching now`;
                                      })()}
                                    </span>
                                  ) : (
                                    <span>
                                      {(() => {
                                        // Get view count from the most reliable source
                                        let viewCount = 0;
                                        if (video.statistics?.viewCount) {
                                          viewCount = Number(video.statistics.viewCount);
                                        } else if (video.viewCount) {
                                          viewCount = typeof video.viewCount === 'string' ?
                                            Number(video.viewCount) : video.viewCount;
                                        }

                                        // Validate the view count is reasonable
                                        if (isNaN(viewCount) || viewCount < 0 || viewCount > 10000000000) {
                                          return "0";
                                        }

                                        // Additional validation for suspiciously high view counts
                                        // Check if the view count is suspiciously high based on video age
                                        const publishedAt = new Date(video.publishedAt);
                                        const hoursElapsed = Math.max(1, (Date.now() - publishedAt.getTime()) / (1000 * 60 * 60));
                                        const daysElapsed = hoursElapsed / 24;

                                        // Calculate expected maximum views based on age
                                        // These thresholds are based on typical YouTube performance
                                        let isSuspicious = false;

                                        // Very new videos (less than 1 day old)
                                        if (daysElapsed < 1 && viewCount > 100000) {
                                          isSuspicious = true;
                                        }
                                        // New videos (1-7 days old)
                                        else if (daysElapsed < 7 && viewCount > 1000000) {
                                          isSuspicious = true;
                                        }
                                        // Older videos with extremely high view counts
                                        else if (viewCount > 5000000) {
                                          isSuspicious = true;
                                        }

                                        // Add warning indicator for suspicious view counts
                                        if (isSuspicious) {
                                          return `${viewCount.toLocaleString()} ⚠️`;
                                        }

                                        return viewCount.toLocaleString();
                                      })()}
                                      {isEndedLiveStream(video.title || '', video.isUnplayable) && (
                                        <span className="ml-1 text-xs text-gray-400">(ended stream)</span>
                                      )}
                                    </span>
                                  )}
                                </div>
                                <div className="text-sm font-medium whitespace-nowrap flex flex-col justify-start h-9">
                                  {previousVphValues[video.id] !== undefined ? (
                                    <VphIndicator
                                      currentVph={Math.round(video.vph || 0)}
                                      previousVph={previousVphValues[video.id].previousValue || 0}
                                      highestVph={previousVphValues[video.id].highestValue || 0}
                                      timeSincePreviousMeasurement={previousVphValues[video.id].timestamp - previousVphValues[video.id].previousTimestamp}
                                    />
                                  ) : (
                                    <div className="font-medium text-white">{Math.round(video.vph || 0).toLocaleString()}</div>
                                  )}
                                </div>
                                <div className="flex items-center justify-center">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 flex items-center justify-center"
                                    title="Copy Transcription (Pure Text)"
                                    onClick={async (e) => {
                                      e.stopPropagation();

                                      try {
                                        // Show loading toast
                                        toast({
                                          title: "Fetching Transcription",
                                          description: "Retrieving the actual transcript from the server...",
                                          duration: 3000
                                        });

                                        // Fetch transcription
                                        console.log(`Fetching transcription for video ID: ${video.id}`);
                                        const res = await fetch(`/api/youtube-channels/videos/${video.id}/transcription`);

                                        if (!res.ok) {
                                          console.error(`Failed to fetch transcription: ${res.status} ${res.statusText}`);
                                          throw new Error(`Failed to fetch transcription: ${res.status} ${res.statusText}`);
                                        }

                                        const data = await res.json();
                                        console.log('Transcription API response:', data);
                                        let transcription = data.transcription;

                                        // Check if we need to queue a transcription fetch
                                        if (data.message && data.message.includes('queued')) {
                                          toast({
                                            title: "Fetching Transcription",
                                            description: "Transcription is being fetched in the background. Please try again in a few moments.",
                                            duration: 5000
                                          });
                                          return;
                                        }

                                        // Check if transcription is a fallback message or a simulated transcript
                                        if (transcription.includes('Transcription not available') ||
                                            transcription.includes('Error retrieving transcription') ||
                                            transcription.includes('not found in your subscriptions') ||
                                            transcription.includes('The actual transcript could not be retrieved') ||
                                            transcription.includes('This is a simulated transcript')) {
                                          // Try to queue a transcription fetch
                                          await fetch('/api/tasks/fetch-transcription', {
                                            method: 'POST',
                                            headers: {
                                              'Content-Type': 'application/json'
                                            },
                                            body: JSON.stringify({ videoId: video.id })
                                          });

                                          toast({
                                            title: "Fetching Transcription",
                                            description: "Transcription is being fetched in the background. Please try again in a few moments.",
                                            duration: 5000
                                          });
                                          return;
                                        }

                                        // Format the transcription for clipboard - pure transcript only
                                        let formattedText = "";

                                        // Process the transcription content
                                        if (transcription && transcription.trim()) {
                                          // Check if the transcription contains simulated content
                                          if (transcription.includes('This is a simulated transcript')) {
                                            // If it's a simulated transcript, just add a note that the actual transcript is not available
                                            formattedText = "The actual transcript is not available for this video.";
                                          } else {
                                            // Clean up the transcription to get pure text only
                                            let cleanTranscription = transcription
                                              .split('\n')
                                              .map(line => {
                                                // Remove timestamps like [00:00] or [00:00:00]
                                                return line.replace(/\[\d+:\d+(?::\d+)?\]\s*/g, '');
                                              })
                                              .filter(line => {
                                                // Filter out lines that are just URLs or empty
                                                const urlPattern = /^https?:\/\/[\w\-]+(\.[\w\-]+)+([\w\-.,@?^=%&:/~+#]*[\w\-@?^=%&/~+#])?$/;
                                                return line.trim() && !urlPattern.test(line.trim());
                                              });

                                            // Remove any metadata lines that might be in the transcription
                                            const metadataPatterns = [
                                              /^Title:/i,
                                              /^Author:/i,
                                              /^Description:/i,
                                              /^Timestamps:/i,
                                              /^To view this video/i,
                                              /^The video may cover/i,
                                              /^Note:/i,
                                              /^Transcription not available/i,
                                              /^Possible reasons:/i,
                                              /^The actual transcript could not be retrieved/i
                                            ];

                                            cleanTranscription = cleanTranscription.filter(line => {
                                              // Keep the line only if it doesn't match any of the metadata patterns
                                              return !metadataPatterns.some(pattern => pattern.test(line));
                                            });

                                            // Join the lines back together
                                            formattedText = cleanTranscription.join('\n');
                                          }
                                        } else {
                                          formattedText = "No transcript content available for this video.";
                                        }

                                        // Copy to clipboard
                                        console.log('Attempting to copy to clipboard:', formattedText.substring(0, 100) + '...');
                                        try {
                                          await navigator.clipboard.writeText(formattedText);
                                          console.log('Successfully copied to clipboard');
                                        } catch (clipboardError) {
                                          console.error('Clipboard error:', clipboardError);
                                          // Fallback method for clipboard
                                          const textArea = document.createElement('textarea');
                                          textArea.value = formattedText;
                                          document.body.appendChild(textArea);
                                          textArea.select();
                                          const success = document.execCommand('copy');
                                          document.body.removeChild(textArea);
                                          console.log('Fallback clipboard method result:', success);
                                          if (!success) throw new Error('Failed to copy using fallback method');
                                        }

                                        // Show different toast messages based on whether it was a simulated transcript
                                        if (transcription.includes('This is a simulated transcript')) {
                                          toast({
                                            title: "Transcription Unavailable",
                                            description: "The actual transcript is not available for this video.",
                                            duration: 3000
                                          });
                                        } else {
                                          toast({
                                            title: "Transcription Copied",
                                            description: "Pure transcript text copied to clipboard (no timestamps or metadata).",
                                            duration: 3000
                                          });
                                        }
                                      } catch (error) {
                                        logger.error('Error copying transcription:', error);
                                        toast({
                                          title: "Copy Failed",
                                          description: "Failed to copy transcription. Please try again.",
                                          variant: "destructive"
                                        });
                                      }
                                    }}
                                  >
                                    <span className="font-bold text-white text-base bg-gray-900 px-2 py-0.5 rounded-md border border-gray-600 shadow-sm hover:bg-gray-800 transition-colors">T</span>
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  );
                })()}
                </div>
              </TabsContent>



              <TabsContent value="nlm" className="space-y-4">
                {(() => {
                  return (
                    <>
                      {/* NLM Tab Content */}
                      <div className="flex flex-col space-y-4">
                        <div className="flex justify-end items-center">
                          {selectedVideos.length > 0 && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedVideos([])}
                            >
                              <X className="h-4 w-4 mr-2" />
                              Clear {selectedVideos.length} Selected {selectedVideos.length === 1 ? 'Video' : 'Videos'}
                            </Button>
                          )}
                        </div>

                        {/* NotebookLM Multi-Account Integration */}
                        <div className="w-full border rounded-md overflow-hidden bg-muted/20 p-6">
                          <div className="max-w-full w-full space-y-6">
                            <div className="flex items-center gap-3 mb-2">
                              <Sparkles className="h-8 w-8 text-blue-500" />
                              <div>
                                <h3 className="text-xl font-medium">NotebookLM Multi-Account Manager</h3>
                                <p className="text-sm text-muted-foreground">
                                  Open NotebookLM with multiple Google accounts to export more than 3 audio files
                                </p>
                              </div>
                            </div>

                            {/* Multi-Account NotebookLM Launcher */}
                            <div className="bg-background/80 rounded-md p-4 border border-border">
                              <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
                                <Users className="h-5 w-5 text-primary" />
                                Multiple Google Account Launcher
                              </h3>
                              <p className="text-sm text-muted-foreground mb-4">
                                Open NotebookLM with up to 12 different Google accounts to export more than 3 audio files.
                              </p>

                              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2 mb-4">
                                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(accountNumber => (
                                  <Button
                                    key={accountNumber}
                                    onClick={() => openNotebookLMWithAccount(accountNumber)}
                                    variant={isAccountWindowOpen(accountNumber) ? "secondary" : "outline"}
                                    className="h-auto py-2 px-1 relative text-xs"
                                  >
                                    {isAccountWindowOpen(accountNumber) && (
                                      <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-green-500 animate-pulse"></span>
                                    )}
                                    <div className="flex flex-col items-center gap-0.5">
                                      <User className="h-3.5 w-3.5" />
                                      <span>Acc {accountNumber}</span>
                                      {isAccountWindowOpen(accountNumber) && (
                                        <span className="text-[10px] bg-green-100 text-green-800 px-1 py-0.5 rounded-full">Focus</span>
                                      )}
                                    </div>
                                  </Button>
                                ))}
                              </div>

                              <div className="text-xs text-muted-foreground bg-muted/30 p-2 rounded-md mb-4">
                                <strong>How to use:</strong> Each button opens NotebookLM with a different Google account. Account 1 uses your current login. For Accounts 2-12, click "Use another account" to sign in with different Google accounts.
                              </div>

                              {/* Copy selected videos button */}
                              {selectedVideos.length > 0 && (
                                <Button
                                  variant="secondary"
                                  size="lg"
                                  onClick={copySelectedVideosInfo}
                                  className="w-full mt-2"
                                >
                                  <ClipboardCopy className="h-5 w-5 mr-2" />
                                  Copy {selectedVideos.length} Selected {selectedVideos.length === 1 ? 'Video' : 'Videos'} Info
                                </Button>
                              )}
                            </div>

                            {/* Custom URL Account Launcher */}
                            <CustomUrlAccountLauncher />

                            {/* Prompt Management System */}
                            <div className="bg-muted/30 rounded-md p-4 border border-border/50">
                              <div className="flex justify-between items-center mb-4">
                                <h4 className="font-medium flex items-center gap-2">
                                  <FileText className="h-4 w-4 text-primary" />
                                  Prompt Library
                                </h4>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setIsAddingPrompt(true);
                                    setEditingPromptId(null);
                                    setNewPromptTitle('');
                                    setNewPromptContent('');
                                  }}
                                >
                                  <Plus className="h-4 w-4 mr-2" />
                                  Add New Prompt
                                </Button>
                              </div>

                              {/* Prompt Editor */}
                              {(isAddingPrompt || editingPromptId) && (
                                <div className="bg-background rounded-md p-3 mb-4 border border-border">
                                  <h5 className="text-sm font-medium mb-2">
                                    {editingPromptId ? 'Edit Prompt' : 'New Prompt'}
                                  </h5>
                                  <div className="space-y-3">
                                    <div>
                                      <Label htmlFor="prompt-title" className="text-xs">Title</Label>
                                      <Input
                                        id="prompt-title"
                                        value={newPromptTitle}
                                        onChange={(e) => setNewPromptTitle(e.target.value)}
                                        placeholder="Enter a title for your prompt"
                                        className="mt-1"
                                      />
                                    </div>
                                    <div>
                                      <Label htmlFor="prompt-content" className="text-xs">Content</Label>
                                      <Textarea
                                        id="prompt-content"
                                        value={newPromptContent}
                                        onChange={(e) => setNewPromptContent(e.target.value)}
                                        placeholder="Enter your prompt content here"
                                        className="mt-1 min-h-[100px]"
                                      />
                                    </div>
                                    <div className="flex justify-end gap-2 pt-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                          setIsAddingPrompt(false);
                                          setEditingPromptId(null);
                                          setNewPromptTitle('');
                                          setNewPromptContent('');
                                        }}
                                      >
                                        Cancel
                                      </Button>
                                      <Button
                                        size="sm"
                                        onClick={editingPromptId ? handleUpdatePrompt : handleAddPrompt}
                                      >
                                        {editingPromptId ? 'Update' : 'Save'}
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              )}

                              {/* Prompt List */}
                              {prompts.length > 0 ? (
                                <div className="space-y-3 max-h-[400px] overflow-y-auto pr-1">
                                  {prompts.map(prompt => (
                                    <div key={prompt.id} className="bg-background rounded-md p-3 border border-border">
                                      <div className="flex justify-between items-start mb-2">
                                        <h5 className="font-medium text-sm">{prompt.title}</h5>
                                        <div className="flex gap-1">
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-7 w-7"
                                            onClick={() => handleCopyPrompt(prompt.content, prompt.title)}
                                            title="Copy to clipboard"
                                          >
                                            <ClipboardCopy className="h-4 w-4" />
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-7 w-7"
                                            onClick={() => {
                                              setEditingPromptId(prompt.id);
                                              setNewPromptTitle(prompt.title);
                                              setNewPromptContent(prompt.content);
                                              setIsAddingPrompt(false);
                                            }}
                                            title="Edit prompt"
                                          >
                                            <Pencil className="h-4 w-4" />
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-7 w-7 text-destructive hover:text-destructive"
                                            onClick={() => handleDeletePrompt(prompt.id)}
                                            title="Delete prompt"
                                          >
                                            <Trash2 className="h-4 w-4" />
                                          </Button>
                                        </div>
                                      </div>
                                      <p className="text-xs text-muted-foreground whitespace-pre-wrap break-words">
                                        {prompt.content.length > 150
                                          ? `${prompt.content.substring(0, 150)}...`
                                          : prompt.content}
                                      </p>
                                      <Button
                                        variant="secondary"
                                        size="sm"
                                        className="w-full mt-2"
                                        onClick={() => handleCopyPrompt(prompt.content, prompt.title)}
                                      >
                                        <ClipboardCopy className="h-4 w-4 mr-2" />
                                        Copy to Clipboard
                                      </Button>
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <div className="text-center py-6 bg-background/50 rounded-md">
                                  <FileText className="h-10 w-10 text-muted-foreground mx-auto mb-2 opacity-50" />
                                  <p className="text-sm text-muted-foreground mb-2">No prompts saved yet</p>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setIsAddingPrompt(true);
                                      setEditingPromptId(null);
                                    }}
                                  >
                                    <Plus className="h-4 w-4 mr-2" />
                                    Create Your First Prompt
                                  </Button>
                                </div>
                              )}

                              <div className="mt-3 text-xs text-muted-foreground bg-background/50 p-2 rounded-md">
                                <strong>Tip:</strong> Create and save frequently used prompts for NotebookLM. Click the copy button to quickly copy a prompt to your clipboard.
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  );
                })()}
              </TabsContent>

              <TabsContent value="channels" className="space-y-4">
                {/* Import/Export Channels Component */}
                <ImportExportChannels
                  channels={channels}
                  onImportComplete={() => {
                    // Refresh channels list
                    refetchChannels();

                    // Refresh all videos to include newly imported channels
                    refetchAllVideos();

                    // Show a toast notification
                    toast({
                      title: "Channels Updated",
                      description: "Your channel list has been updated. Videos are being fetched and analyzed in the background.",
                    });

                    // Show a second toast after a delay to inform about the background process
                    setTimeout(() => {
                      toast({
                        title: "Background Processing",
                        description: "Transcripts and financial analysis are being processed in the background. Check the Tasks page for status updates.",
                      });
                    }, 3000);
                  }}
                />


                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-2">
                    <h2 className="text-xl font-semibold">Your Channels</h2>
                    {!isLoadingVideoCounts && totalChannelVideos > 0 && (
                      <span className="text-sm text-muted-foreground bg-muted px-2 py-1 rounded-md">
                        {totalChannelVideos.toLocaleString()} videos total
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {!isLoadingVideoCounts && totalChannelVideos > 0 && (
                      <>
                        <Button
                          variant="outline"
                          onClick={handleDeleteOldVideosData}
                          disabled={deleteOldVideosData.isPending}
                          className="flex items-center gap-1"
                        >
                          <Clock className="h-4 w-4" />
                          <span>Delete Old Videos Data</span>
                        </Button>
                        <Button
                          variant="outline"
                          onClick={handleDeleteAllChannelsData}
                          disabled={deleteAllChannelsData.isPending}
                          className="flex items-center gap-1"
                        >
                          <DatabaseZap className="h-4 w-4" />
                          <span>Delete All Videos Data</span>
                        </Button>
                      </>
                    )}
                    <Dialog open={addChannelDialogOpen} onOpenChange={setAddChannelDialogOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Channel
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add YouTube Channel</DialogTitle>
                        <DialogDescription>
                          Enter a YouTube channel URL or handle (e.g., @GoogleDevelopers) to subscribe to it.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="channelUrl-dialog" className="text-right">
                            URL
                          </Label>
                          <Input
                            id="channelUrl-dialog"
                            placeholder="@handle or https://www.youtube.com/channel/UC..."
                            className="col-span-3"
                            value={newChannelUrl}
                            onChange={(e) => setNewChannelUrl(e.target.value)}
                          />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="videoLimit-dialog" className="text-right">
                            Videos to fetch
                          </Label>
                          <div className="col-span-3 flex items-center gap-2">
                            <Input
                              id="videoLimit-dialog"
                              type="number"
                              min="5"
                              max="15"
                              value={newChannelVideoLimit}
                              onChange={(e) => setNewChannelVideoLimit(parseInt(e.target.value) || 15)}
                              className="w-24"
                            />
                            <span className="text-sm text-muted-foreground">Recent videos to fetch (max 15)</span>
                          </div>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          type="submit"
                          onClick={handleAddChannel}
                          disabled={createChannel.isPending}
                        >
                          {createChannel.isPending ? 'Adding...' : 'Add Channel'}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                  </div>
                </div>

                {isLoadingChannels ? (
                  // Loading skeleton
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <Card key={i}>
                        <CardHeader>
                          <Skeleton className="h-5 w-full" />
                          <Skeleton className="h-4 w-2/3 mt-2" />
                        </CardHeader>
                        <CardContent>
                          <Skeleton className="h-4 w-full" />
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : channels.length === 0 ? (
                  // No channels message
                  <div className="text-center p-8 border rounded-lg">
                    <p className="text-muted-foreground mb-2">No channels added yet</p>
                    <p className="text-sm mb-4">Subscribe to YouTube channels to see their videos here.</p>
                    <Button onClick={() => setAddChannelDialogOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Channel
                    </Button>
                  </div>
                ) : (
                  // Channel list
                  <div className="space-y-4">
                    {channels.map((channel) => (
                      <Card key={channel.id}>
                        <CardHeader>
                          <div className="flex flex-col sm:flex-row sm:items-start gap-4">
                            <div className="flex-1 min-w-0"> {/* Added min-width */}
                              <CardTitle className="truncate">{channel.channelTitle}</CardTitle> {/* Added truncate */}
                              <CardDescription className="truncate"> {/* Added truncate */}
                                {isLoadingVideoCounts ? (
                                  'Loading videos...'
                                ) : (
                                  <>
                                    {channelVideoCounts[channel.id] || 0} videos available
                                    (Limit: {channel.videoLimit || 15})
                                  </>
                                )}
                              </CardDescription>
                            </div>
                            <div className="flex-shrink-0 flex flex-wrap gap-2"> {/* Changed to flex-wrap with gap */}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedChannelId(channel.id);
                                  setActiveTab("feed");
                                }}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View Videos
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleRefreshChannel(channel.id)}
                                disabled={refreshChannelVideos.isPending}
                              >
                                <RefreshCw className={`h-4 w-4 mr-2 ${refreshChannelVideos.isPending && refreshChannelVideos.variables === channel.id ? 'animate-spin' : ''}`} />
                                Refresh
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleAnalyzeChannelWithOpenRouter(channel.id)}
                                disabled={analyzeChannelWithOpenRouter.isPending}
                              >
                                <Sparkles className={`h-4 w-4 mr-2 ${analyzeChannelWithOpenRouter.isPending && analyzeChannelWithOpenRouter.variables === channel.id ? 'animate-spin' : ''}`} />
                                Analyze with AI
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteChannelData(channel.id)}
                                disabled={deleteChannelData.isPending}
                              >
                                <DatabaseZap className="h-4 w-4 mr-2" />
                                Delete Data
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleDeleteChannel(channel.id)}
                                disabled={deleteChannel.isPending}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground mb-4">
                            {channel.description || 'No description available'}
                          </p>
                          <div className="flex items-center gap-2">
                            <Label htmlFor={`videoLimit-${channel.id}`} className="text-sm">
                              Videos to fetch:
                            </Label>
                            <Input
                              id={`videoLimit-${channel.id}`}
                              type="number"
                              min="5"
                              max="15"
                              defaultValue={channel.videoLimit || 15}
                              className="w-20 h-8 text-sm"
                              onBlur={(e) => {
                                // Ensure video limit is capped at 15 due to RSS feed limitations
                                const newLimit = Math.min(parseInt(e.target.value) || 15, 15);
                                if (newLimit !== channel.videoLimit) {
                                  updateChannel.mutate({
                                    id: channel.id,
                                    data: {
                                      channelId: channel.channelId,
                                      channelTitle: channel.channelTitle,
                                      channelUrl: channel.channelUrl,
                                      thumbnail: channel.thumbnail,
                                      description: channel.description,
                                      videoLimit: newLimit
                                    }
                                  });
                                }
                              }}
                            />
                            <span className="text-xs text-muted-foreground">(max 15)</span>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => setSelectedChannelId(channel.id)}
                          >
                            View Videos
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="rss" className="space-y-4">
                <RssTab onRefresh={(refreshTime) => setRssLastRefreshTime(refreshTime)} />
              </TabsContent>

              <TabsContent value="txt" className="space-y-4">
                <TxtTab
                  isActive={activeTab === "txt"}
                  onRefresh={(refreshTime) => setLastViewCountRefreshTime(refreshTime)}
                />
              </TabsContent>

              <TabsContent value="txt-pro" className="space-y-4">
                <TxtProTab />
              </TabsContent>

              <TabsContent
                value="data-analysis"
                className="space-y-4"
                onSelect={() => {
                  // Refresh all data analysis tools when the tab is clicked
                  collectMetricsData();
                  // Force refresh data from localStorage
                  const savedMetrics = localStorage.getItem('videoMetricsHistory');
                  if (savedMetrics) {
                    try {
                      const parsedMetrics = JSON.parse(savedMetrics);
                      const cleanedMetrics = cleanupMetricsData(parsedMetrics);
                      setVideoMetricsHistory(cleanedMetrics);
                      console.log(`Loaded ${cleanedMetrics.length} historical metrics data points from localStorage`);
                    } catch (error) {
                      console.error('Error parsing saved metrics data:', error);
                    }
                  }
                }}
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart className="h-5 w-5 text-primary" />
                        Video Metrics Timeline
                      </CardTitle>
                      <CardDescription>
                        Track new and trending videos over time
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <div className="flex flex-col gap-2">
                            <Select
                              value={selectedDay}
                              onValueChange={setSelectedDay}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Select timeframe" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="today">Today</SelectItem>
                                <SelectItem value="yesterday">-1 {getDayNameWithOffset(1).split(' ')[1]}</SelectItem>
                                <SelectItem value="day-2">{getDayNameWithOffset(2)}</SelectItem>
                                <SelectItem value="day-3">{getDayNameWithOffset(3)}</SelectItem>
                                <SelectItem value="day-4">{getDayNameWithOffset(4)}</SelectItem>
                                <SelectItem value="day-5">{getDayNameWithOffset(5)}</SelectItem>
                                <SelectItem value="day-6">{getDayNameWithOffset(6)}</SelectItem>
                                <SelectItem value="week">Last 7 days</SelectItem>
                              </SelectContent>
                            </Select>

                            <Select
                              value={metricsCollectionInterval.toString()}
                              onValueChange={handleMetricsIntervalChange}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Collection interval" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="1">Every 1 minute</SelectItem>
                                <SelectItem value="2">Every 2 minutes</SelectItem>
                                <SelectItem value="5">Every 5 minutes</SelectItem>
                                <SelectItem value="10">Every 10 minutes</SelectItem>
                                <SelectItem value="20">Every 20 minutes</SelectItem>
                                <SelectItem value="30">Every 30 minutes</SelectItem>
                                <SelectItem value="40">Every 40 minutes</SelectItem>
                                <SelectItem value="50">Every 50 minutes</SelectItem>
                                <SelectItem value="60">Every 1 hour</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="flex flex-col items-end">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                // First collect new metrics data
                                collectMetricsData();

                                // Check if user is authenticated before making the API call
                                const isAuthenticated = document.cookie.includes('connect.sid=');

                                if (!isAuthenticated) {
                                  console.log('User not authenticated, skipping metrics load from database');
                                  toast({
                                    title: "Authentication Required",
                                    description: "Please log in to save and load metrics data",
                                    variant: "warning",
                                    duration: 3000
                                  });
                                  return;
                                }

                                // Then fetch all metrics from the database
                                fetch('/api/video-metrics', {
                                  credentials: 'include' // Ensure credentials are included
                                })
                                  .then(response => {
                                    if (!response.ok) {
                                      throw new Error('Failed to fetch metrics from database');
                                    }
                                    return response.json();
                                  })
                                  .then(data => {
                                    if (Array.isArray(data) && data.length > 0) {
                                      console.log(`Loaded ${data.length} metrics data points from database`);

                                      // Apply cleanup to ensure we don't load too much data
                                      const cleanedMetrics = cleanupMetricsData(data);
                                      setVideoMetricsHistory(cleanedMetrics);

                                      // Also update localStorage as a backup
                                      localStorage.setItem('videoMetricsHistory', JSON.stringify(cleanedMetrics));
                                    }
                                  })
                                  .catch(error => {
                                    console.error('Error loading metrics from database:', error);
                                  });
                              }}
                            >
                              <RefreshCw className="h-4 w-4 mr-2" />
                              Update Now
                            </Button>
                            {nextMetricsUpdateTime && (
                              <div className="text-xs mt-1 flex items-center">
                                <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                                <span className="text-foreground font-medium">
                                  Next auto-update: {nextMetricsUpdateTime.toLocaleTimeString('en-US', {
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  })}
                                </span>
                                {' '}
                                <span className="text-xs text-muted-foreground ml-1">
                                  (every {metricsCollectionInterval} {metricsCollectionInterval === 1 ? 'minute' : 'minutes'})
                                </span>
                              </div>
                            )}
                          </div>
                        </div>

                        {videoMetricsHistory.length > 0 ? (
                          <div className="h-[300px] w-full p-2 bg-background border rounded-lg">
                            <VideoMetricsChart
                              videoMetricsHistory={videoMetricsHistory}
                              selectedDay={selectedDay}
                            />
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-[300px] border rounded-lg">
                            <BarChart className="h-12 w-12 text-muted-foreground mb-4" />
                            <p className="text-muted-foreground">No metrics data available yet</p>
                            <p className="text-sm text-muted-foreground mt-1">Data will be collected automatically</p>
                            <Button
                              variant="outline"
                              size="sm"
                              className="mt-4"
                              onClick={() => {
                                // First collect new metrics data
                                collectMetricsData();

                                // Check if user is authenticated before making the API call
                                const isAuthenticated = document.cookie.includes('connect.sid=');

                                if (!isAuthenticated) {
                                  console.log('User not authenticated, skipping metrics load from database');
                                  toast({
                                    title: "Authentication Required",
                                    description: "Please log in to save and load metrics data",
                                    variant: "warning",
                                    duration: 3000
                                  });
                                  return;
                                }

                                // Then fetch all metrics from the database
                                fetch('/api/video-metrics', {
                                  credentials: 'include' // Ensure credentials are included
                                })
                                  .then(response => {
                                    if (!response.ok) {
                                      throw new Error('Failed to fetch metrics from database');
                                    }
                                    return response.json();
                                  })
                                  .then(data => {
                                    if (Array.isArray(data) && data.length > 0) {
                                      console.log(`Loaded ${data.length} metrics data points from database`);

                                      // Apply cleanup to ensure we don't load too much data
                                      const cleanedMetrics = cleanupMetricsData(data);
                                      setVideoMetricsHistory(cleanedMetrics);

                                      // Also update localStorage as a backup
                                      localStorage.setItem('videoMetricsHistory', JSON.stringify(cleanedMetrics));

                                      toast({
                                        title: "Metrics Collected",
                                        description: "Data has been saved to the database",
                                        duration: 3000
                                      });
                                    }
                                  })
                                  .catch(error => {
                                    console.error('Error loading metrics from database:', error);
                                    toast({
                                      title: "Error",
                                      description: "Failed to load metrics from database",
                                      variant: "destructive",
                                      duration: 3000
                                    });
                                  });
                              }}
                            >
                              Collect Data Now
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5 text-primary" />
                        Current Metrics
                      </CardTitle>
                      <CardDescription>
                        Summary of new and trending videos
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="flex flex-col items-center justify-center p-4 border rounded-lg">
                            <Badge variant="outline" className="mb-2 bg-green-100 text-green-800 border-green-200">
                              <span className="font-bold">N</span>
                            </Badge>
                            <div className="text-3xl font-bold">
                              {displayedVideos.filter(video => {
                                const publishedDate = new Date(video.publishedAt);
                                const now = new Date();
                                const sixHoursAgo = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                                return publishedDate > sixHoursAgo;
                              }).length}
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">New Videos</div>
                          </div>

                          <div className="flex flex-col items-center justify-center p-4 border rounded-lg">
                            <Badge variant="outline" className="mb-2 bg-green-100 text-green-800 border-green-200">
                              <span className="font-bold">Trending</span>
                            </Badge>
                            <div className="text-3xl font-bold">
                              {displayedVideos.filter(video => {
                                if (previousVphValues[video.id]) {
                                  const prevVphData = previousVphValues[video.id];
                                  const previousVph = prevVphData.previousValue || 0;
                                  const currentVph = video.vph || 0;
                                  const momentumDelta = currentVph - previousVph;
                                  const momentumThreshold = Math.max(2, previousVph * 0.01);
                                  const timeSincePrevious = prevVphData.timestamp - prevVphData.previousTimestamp;
                                  const minutesSincePrevious = timeSincePrevious / (1000 * 60);
                                  return momentumDelta > 0 && Math.abs(momentumDelta) >= momentumThreshold && minutesSincePrevious >= 5;
                                }
                                return false;
                              }).length}
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">Trending Videos</div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <h3 className="text-sm font-medium">Peak Hours (Last 24 Hours)</h3>
                          {videoMetricsHistory.length > 0 ? (
                            <div>
                              {(() => {
                                // Find peak hours for new videos
                                const last24Hours = videoMetricsHistory.filter(
                                  item => item.timestamp > Date.now() - 24 * 60 * 60 * 1000
                                );

                                if (last24Hours.length === 0) {
                                  return (
                                    <div className="text-sm text-muted-foreground">
                                      Not enough data to determine peak hours
                                    </div>
                                  );
                                }

                                const newVideosPeak = [...last24Hours].sort(
                                  (a, b) => b.newVideosCount - a.newVideosCount
                                )[0];

                                const trendingVideosPeak = [...last24Hours].sort(
                                  (a, b) => b.trendingVideosCount - a.trendingVideosCount
                                )[0];

                                return (
                                  <div className="space-y-2">
                                    <div className="flex justify-between items-center p-2 bg-muted/50 rounded-md">
                                      <div className="flex items-center gap-2">
                                        <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                                          <span className="font-bold">N</span>
                                        </Badge>
                                        <span className="text-sm">New Videos Peak</span>
                                      </div>
                                      <div className="text-sm font-medium">
                                        {new Date(newVideosPeak.timestamp).toLocaleTimeString('en-US', {
                                          hour: '2-digit',
                                          minute: '2-digit'
                                        })}
                                        {' '}({newVideosPeak.newVideosCount})
                                      </div>
                                    </div>

                                    <div className="flex justify-between items-center p-2 bg-muted/50 rounded-md">
                                      <div className="flex items-center gap-2">
                                        <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                                          <span className="font-bold">Trending</span>
                                        </Badge>
                                        <span className="text-sm">Trending Videos Peak</span>
                                      </div>
                                      <div className="text-sm font-medium">
                                        {new Date(trendingVideosPeak.timestamp).toLocaleTimeString('en-US', {
                                          hour: '2-digit',
                                          minute: '2-digit'
                                        })}
                                        {' '}({trendingVideosPeak.trendingVideosCount})
                                      </div>
                                    </div>
                                  </div>
                                );
                              })()}
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground">
                              No data available yet
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Seasonal Trend Analysis */}
                  <Card className="col-span-1 md:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-primary" />
                        Seasonal Trend Analysis
                      </CardTitle>
                      <CardDescription>
                        Analyze performance patterns by time of day and day of week
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <Select
                            value={selectedDay}
                            onValueChange={setSelectedDay}
                          >
                            <SelectTrigger className="w-[180px]">
                              <SelectValue placeholder="Select timeframe" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="today">Today</SelectItem>
                              <SelectItem value="yesterday">-1 {getDayNameWithOffset(1).split(' ')[1]}</SelectItem>
                              <SelectItem value="day-2">{getDayNameWithOffset(2)}</SelectItem>
                              <SelectItem value="day-3">{getDayNameWithOffset(3)}</SelectItem>
                              <SelectItem value="day-4">{getDayNameWithOffset(4)}</SelectItem>
                              <SelectItem value="day-5">{getDayNameWithOffset(5)}</SelectItem>
                              <SelectItem value="day-6">{getDayNameWithOffset(6)}</SelectItem>
                              <SelectItem value="week">Last 7 days</SelectItem>
                            </SelectContent>
                          </Select>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // First collect new metrics data
                              collectMetricsData();

                              // Then fetch all metrics from the database
                              fetch('/api/video-metrics')
                                .then(response => {
                                  if (!response.ok) {
                                    throw new Error('Failed to fetch metrics from database');
                                  }
                                  return response.json();
                                })
                                .then(data => {
                                  if (Array.isArray(data) && data.length > 0) {
                                    console.log(`Loaded ${data.length} metrics data points from database`);

                                    // Apply cleanup to ensure we don't load too much data
                                    const cleanedMetrics = cleanupMetricsData(data);
                                    setVideoMetricsHistory(cleanedMetrics);

                                    // Also update localStorage as a backup
                                    localStorage.setItem('videoMetricsHistory', JSON.stringify(cleanedMetrics));

                                    toast({
                                      title: "Metrics Updated",
                                      description: `Loaded ${cleanedMetrics.length} data points from database`,
                                      duration: 3000
                                    });
                                  } else {
                                    // If no data in database, fall back to localStorage
                                    const savedMetrics = localStorage.getItem('videoMetricsHistory');
                                    if (savedMetrics) {
                                      try {
                                        const parsedMetrics = JSON.parse(savedMetrics);
                                        const cleanedMetrics = cleanupMetricsData(parsedMetrics);
                                        setVideoMetricsHistory(cleanedMetrics);
                                        console.log(`Loaded ${cleanedMetrics.length} historical metrics data points from localStorage`);
                                      } catch (error) {
                                        console.error('Error parsing saved metrics data:', error);
                                      }
                                    }
                                  }
                                })
                                .catch(error => {
                                  console.error('Error loading metrics from database:', error);
                                  toast({
                                    title: "Error",
                                    description: "Failed to load metrics from database",
                                    variant: "destructive",
                                    duration: 3000
                                  });

                                  // Fall back to localStorage if database fetch fails
                                  const savedMetrics = localStorage.getItem('videoMetricsHistory');
                                  if (savedMetrics) {
                                    try {
                                      const parsedMetrics = JSON.parse(savedMetrics);
                                      const cleanedMetrics = cleanupMetricsData(parsedMetrics);
                                      setVideoMetricsHistory(cleanedMetrics);
                                    } catch (error) {
                                      console.error('Error parsing saved metrics data:', error);
                                    }
                                  }
                                });
                            }}
                          >
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Update Now
                          </Button>
                        </div>

                        {videoMetricsHistory.length > 0 ? (
                          <div>
                            {/* Day of week analysis */}
                            <div className="mb-6">
                              <h3 className="text-sm font-medium mb-3">Performance by Day of Week</h3>
                              <div className="grid grid-cols-7 gap-2">
                                {(() => {
                                  // Group metrics by day of week
                                  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                                  const dayAbbr = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

                                  // Initialize data structure for each day
                                  const dayData = dayNames.map((_, index) => ({
                                    day: index,
                                    name: dayNames[index],
                                    abbr: dayAbbr[index],
                                    newCount: 0,
                                    trendingCount: 0,
                                    dataPoints: 0
                                  }));

                                  // Filter metrics based on selected day
                                  let filteredMetrics = [...videoMetricsHistory];

                                  if (selectedDay !== 'week') {
                                    const now = new Date();
                                    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                                    let startDate;

                                    if (selectedDay === 'today') {
                                      startDate = today;
                                    } else if (selectedDay === 'yesterday') {
                                      startDate = new Date(today);
                                      startDate.setDate(today.getDate() - 1);
                                    } else {
                                      // Extract day number from format "day-X"
                                      const dayNum = parseInt(selectedDay.split('-')[1]);
                                      startDate = new Date(today);
                                      startDate.setDate(today.getDate() - dayNum);
                                    }

                                    const endDate = new Date(startDate);
                                    endDate.setDate(startDate.getDate() + 1);

                                    filteredMetrics = videoMetricsHistory.filter(item => {
                                      const itemDate = new Date(item.timestamp);
                                      return itemDate >= startDate && itemDate < endDate;
                                    });
                                  }

                                  // Aggregate data by day of week
                                  filteredMetrics.forEach(item => {
                                    const date = new Date(item.timestamp);
                                    const dayOfWeek = date.getDay();

                                    dayData[dayOfWeek].newCount += item.newVideosCount;
                                    dayData[dayOfWeek].trendingCount += item.trendingVideosCount;
                                    dayData[dayOfWeek].dataPoints += 1;
                                  });

                                  // Calculate averages
                                  dayData.forEach(day => {
                                    if (day.dataPoints > 0) {
                                      day.newCount = Math.round(day.newCount / day.dataPoints);
                                      day.trendingCount = Math.round(day.trendingCount / day.dataPoints);
                                    }
                                  });

                                  // Find max values for scaling
                                  const maxNewCount = Math.max(...dayData.map(d => d.newCount)) || 1;
                                  const maxTrendingCount = Math.max(...dayData.map(d => d.trendingCount)) || 1;

                                  // Get today's day of week
                                  const today = new Date().getDay();

                                  return dayData.map(day => (
                                    <div
                                      key={day.day}
                                      className={`p-2 border rounded-lg ${day.day === today ? 'bg-primary/10 border-primary/30' : 'bg-muted/20'}`}
                                    >
                                      <div className="text-center mb-2">
                                        <span className="text-xs font-medium">{day.abbr}</span>
                                        {day.day === today && (
                                          <span className="text-xs text-primary ml-1">(Today)</span>
                                        )}
                                      </div>

                                      <div className="space-y-3">
                                        <div>
                                          <div className="flex justify-between items-center text-xs mb-1">
                                            <span className="text-foreground">New</span>
                                            <span className="font-medium text-foreground">{day.newCount}</span>
                                          </div>
                                          <div className="h-1.5 bg-muted rounded-full">
                                            <div
                                              className="h-full bg-green-500 rounded-full"
                                              style={{ width: `${(day.newCount / maxNewCount) * 100}%` }}
                                            ></div>
                                          </div>
                                        </div>

                                        <div>
                                          <div className="flex justify-between items-center text-xs mb-1">
                                            <span className="text-foreground">Trending</span>
                                            <span className="font-medium text-foreground">{day.trendingCount}</span>
                                          </div>
                                          <div className="h-1.5 bg-muted rounded-full">
                                            <div
                                              className="h-full bg-amber-500 rounded-full"
                                              style={{ width: `${(day.trendingCount / maxTrendingCount) * 100}%` }}
                                            ></div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ));
                                })()}
                              </div>
                            </div>

                            {/* Time of day analysis */}
                            <div>
                              <h3 className="text-sm font-medium mb-3">Performance by Time of Day</h3>
                              <div className="border rounded-lg p-4">
                                <div className="grid grid-cols-6 gap-2 mb-2">
                                  {(() => {
                                    // Define time blocks
                                    const timeBlocks = [
                                      { name: 'Early Morning', hours: [5, 6, 7, 8], label: '5-8 AM' },
                                      { name: 'Morning', hours: [9, 10, 11], label: '9-11 AM' },
                                      { name: 'Midday', hours: [12, 13, 14], label: '12-2 PM' },
                                      { name: 'Afternoon', hours: [15, 16, 17], label: '3-5 PM' },
                                      { name: 'Evening', hours: [18, 19, 20, 21], label: '6-9 PM' },
                                      { name: 'Night', hours: [22, 23, 0, 1, 2, 3, 4], label: '10PM-4AM' }
                                    ];

                                    // Initialize data for each time block
                                    const timeData = timeBlocks.map(block => ({
                                      ...block,
                                      newCount: 0,
                                      trendingCount: 0,
                                      dataPoints: 0
                                    }));

                                    // Filter metrics based on selected day
                                    let filteredMetrics = [...videoMetricsHistory];

                                    if (selectedDay !== 'week') {
                                      const now = new Date();
                                      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                                      let startDate;

                                      if (selectedDay === 'today') {
                                        startDate = today;
                                      } else if (selectedDay === 'yesterday') {
                                        startDate = new Date(today);
                                        startDate.setDate(today.getDate() - 1);
                                      } else {
                                        // Extract day number from format "day-X"
                                        const dayNum = parseInt(selectedDay.split('-')[1]);
                                        startDate = new Date(today);
                                        startDate.setDate(today.getDate() - dayNum);
                                      }

                                      const endDate = new Date(startDate);
                                      endDate.setDate(startDate.getDate() + 1);

                                      filteredMetrics = videoMetricsHistory.filter(item => {
                                        const itemDate = new Date(item.timestamp);
                                        return itemDate >= startDate && itemDate < endDate;
                                      });
                                    }

                                    // Aggregate data by time of day
                                    filteredMetrics.forEach(item => {
                                      const date = new Date(item.timestamp);
                                      const hour = date.getHours();

                                      // Find matching time block
                                      const blockIndex = timeData.findIndex(block =>
                                        block.hours.includes(hour)
                                      );

                                      if (blockIndex !== -1) {
                                        timeData[blockIndex].newCount += item.newVideosCount;
                                        timeData[blockIndex].trendingCount += item.trendingVideosCount;
                                        timeData[blockIndex].dataPoints += 1;
                                      }
                                    });

                                    // Calculate averages
                                    timeData.forEach(block => {
                                      if (block.dataPoints > 0) {
                                        block.newCount = Math.round(block.newCount / block.dataPoints);
                                        block.trendingCount = Math.round(block.trendingCount / block.dataPoints);
                                      }
                                    });

                                    // Find max values for scaling
                                    const maxNewCount = Math.max(...timeData.map(d => d.newCount)) || 1;
                                    const maxTrendingCount = Math.max(...timeData.map(d => d.trendingCount)) || 1;

                                    // Get current time block
                                    const currentHour = new Date().getHours();
                                    const currentBlockIndex = timeData.findIndex(block =>
                                      block.hours.includes(currentHour)
                                    );

                                    return timeData.map((block, index) => (
                                      <div
                                        key={block.name}
                                        className={`p-2 border rounded-lg ${index === currentBlockIndex ? 'bg-primary/10 border-primary/30' : 'bg-muted/20'}`}
                                      >
                                        <div className="text-center mb-2">
                                          <span className="text-xs font-medium">{block.label}</span>
                                          {index === currentBlockIndex && (
                                            <span className="text-xs text-primary ml-1">(Now)</span>
                                          )}
                                        </div>

                                        <div className="space-y-3">
                                          <div>
                                            <div className="flex justify-between items-center text-xs mb-1">
                                              <span className="text-foreground">New</span>
                                              <span className="font-medium text-foreground">{block.newCount}</span>
                                            </div>
                                            <div className="h-1.5 bg-muted rounded-full">
                                              <div
                                                className="h-full bg-green-500 rounded-full"
                                                style={{ width: `${(block.newCount / maxNewCount) * 100}%` }}
                                              ></div>
                                            </div>
                                          </div>

                                          <div>
                                            <div className="flex justify-between items-center text-xs mb-1">
                                              <span className="text-foreground">Trending</span>
                                              <span className="font-medium text-foreground">{block.trendingCount}</span>
                                            </div>
                                            <div className="h-1.5 bg-muted rounded-full">
                                              <div
                                                className="h-full bg-amber-500 rounded-full"
                                                style={{ width: `${(block.trendingCount / maxTrendingCount) * 100}%` }}
                                              ></div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    ));
                                  })()}
                                </div>

                                <div className="text-xs mt-4 p-2 border rounded-lg bg-muted/10">
                                  <p className="text-foreground font-medium">Note:</p>
                                  <p className="text-foreground mt-1">• Charts show average values for each time period based on available data.</p>
                                  <p className="text-foreground mt-1">• Highlighted blocks indicate the current day/time.</p>
                                  <p className="text-foreground mt-1">• Data shown for: <span className="font-medium">{selectedDay === 'week' ? 'Last 7 days' : selectedDay === 'today' ? 'Today' : selectedDay === 'yesterday' ? '-1 ' + getDayNameWithOffset(1).split(' ')[1] : getDayNameWithOffset(parseInt(selectedDay.split('-')[1]))}</span></p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-[200px] border rounded-lg">
                            <Clock className="h-12 w-12 text-muted-foreground mb-4" />
                            <p className="text-muted-foreground">No metrics data available</p>
                            <p className="text-sm text-muted-foreground mt-1">Metrics need to be collected to analyze seasonal trends</p>
                            <Button
                              variant="outline"
                              size="sm"
                              className="mt-4"
                              onClick={collectMetricsData}
                            >
                              Collect Data Now
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Momentum Analysis Dashboard */}
                  <Card className="col-span-1 md:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5 text-primary" />
                        Momentum Analysis
                      </CardTitle>
                      <CardDescription>
                        Track videos with accelerating view counts and engagement
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {displayedVideos.length > 0 ? (
                          <div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                              <div className="p-4 border rounded-lg bg-muted/20">
                                <div className="flex items-center justify-between mb-2">
                                  <h3 className="font-medium text-sm">Explosive Growth</h3>
                                  <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
                                    <span className="font-bold">{displayedVideos.filter(video => {
                                      if (previousVphValues[video.id]) {
                                        const prevVphData = previousVphValues[video.id];
                                        const previousVph = prevVphData.previousValue || 0;
                                        const currentVph = video.vph || 0;
                                        const momentumDelta = currentVph - previousVph;
                                        // Higher threshold for explosive growth
                                        const momentumThreshold = Math.max(5, previousVph * 0.05);
                                        return momentumDelta > 0 && Math.abs(momentumDelta) >= momentumThreshold;
                                      }
                                      return false;
                                    }).length}</span>
                                  </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground">Videos with &gt;5% VPH increase</p>
                              </div>

                              <div className="p-4 border rounded-lg bg-muted/20">
                                <div className="flex items-center justify-between mb-2">
                                  <h3 className="font-medium text-sm">Strong Momentum</h3>
                                  <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">
                                    <span className="font-bold">{displayedVideos.filter(video => {
                                      if (previousVphValues[video.id]) {
                                        const prevVphData = previousVphValues[video.id];
                                        const previousVph = prevVphData.previousValue || 0;
                                        const currentVph = video.vph || 0;
                                        const momentumDelta = currentVph - previousVph;
                                        // Medium threshold for strong momentum
                                        const explosiveThreshold = Math.max(5, previousVph * 0.05);
                                        const strongThreshold = Math.max(2, previousVph * 0.01);
                                        return momentumDelta > 0 &&
                                               Math.abs(momentumDelta) >= strongThreshold &&
                                               Math.abs(momentumDelta) < explosiveThreshold;
                                      }
                                      return false;
                                    }).length}</span>
                                  </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground">Videos with 1-5% VPH increase</p>
                              </div>

                              <div className="p-4 border rounded-lg bg-muted/20">
                                <div className="flex items-center justify-between mb-2">
                                  <h3 className="font-medium text-sm">Declining</h3>
                                  <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                                    <span className="font-bold">{displayedVideos.filter(video => {
                                      if (previousVphValues[video.id]) {
                                        const prevVphData = previousVphValues[video.id];
                                        const previousVph = prevVphData.previousValue || 0;
                                        const currentVph = video.vph || 0;
                                        const momentumDelta = currentVph - previousVph;
                                        return momentumDelta < 0;
                                      }
                                      return false;
                                    }).length}</span>
                                  </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground">Videos with decreasing VPH</p>
                              </div>
                            </div>

                            <div className="border rounded-lg overflow-hidden">
                              <div className="bg-muted/30 px-4 py-2 border-b">
                                <h3 className="font-medium">Top Momentum Videos</h3>
                              </div>
                              <div className="divide-y max-h-[300px] overflow-y-auto">
                                {displayedVideos
                                  .filter(video => previousVphValues[video.id])
                                  .sort((a, b) => {
                                    const aData = previousVphValues[a.id];
                                    const bData = previousVphValues[b.id];
                                    if (!aData || !bData) return 0;

                                    const aMomentum = (a.vph || 0) - (aData.previousValue || 0);
                                    const bMomentum = (b.vph || 0) - (bData.previousValue || 0);

                                    // Sort by momentum percentage (relative to previous VPH)
                                    const aPercentage = aData.previousValue ? aMomentum / aData.previousValue : 0;
                                    const bPercentage = bData.previousValue ? bMomentum / bData.previousValue : 0;

                                    return bPercentage - aPercentage;
                                  })
                                  .slice(0, 5)
                                  .map(video => (
                                    <div key={video.id} className="p-3 hover:bg-muted/10">
                                      <div className="flex items-start gap-3">
                                        <div className="w-[120px] h-[68px] rounded overflow-hidden flex-shrink-0">
                                          <YoutubeThumbnail
                                            videoId={video.id}
                                            thumbnail={video.thumbnailUrl}
                                            title={video.title}
                                            className="w-full h-full object-cover"
                                          />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                          <h4 className="font-medium text-sm line-clamp-2">{video.title}</h4>
                                          <div className="flex items-center gap-2 mt-1">
                                            <span className="text-xs text-muted-foreground">{video.channelTitle}</span>
                                          </div>
                                          <div className="flex items-center gap-3 mt-2">
                                            <div className="flex items-center gap-1">
                                              <Eye className="h-3 w-3 text-muted-foreground" />
                                              <span className="text-xs">{video.viewCount?.toLocaleString()}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                              <Activity className="h-3 w-3 text-muted-foreground" />
                                              <span className="text-xs">{video.vph?.toLocaleString()} VPH</span>
                                            </div>
                                            {previousVphValues[video.id] && (
                                              <div className="flex items-center gap-1">
                                                <Zap className="h-3 w-3 text-amber-500" />
                                                <span className={`text-xs font-medium ${
                                                  (video.vph || 0) > (previousVphValues[video.id].previousValue || 0)
                                                    ? 'text-green-600'
                                                    : 'text-red-600'
                                                }`}>
                                                  {((video.vph || 0) - (previousVphValues[video.id].previousValue || 0)).toLocaleString()}
                                                  {' '}
                                                  ({previousVphValues[video.id].previousValue
                                                    ? (((video.vph || 0) - (previousVphValues[video.id].previousValue || 0)) / previousVphValues[video.id].previousValue * 100).toFixed(1)
                                                    : 0}%)
                                                </span>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-[200px] border rounded-lg">
                            <Zap className="h-12 w-12 text-muted-foreground mb-4" />
                            <p className="text-muted-foreground">No video data available</p>
                            <p className="text-sm text-muted-foreground mt-1">Videos need to be loaded to analyze momentum</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Content Pattern Recognition */}
                  <Card className="col-span-1 md:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5 text-primary" />
                        Content Pattern Recognition
                      </CardTitle>
                      <CardDescription>
                        Analyze video titles and performance by content type
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {displayedVideos.length > 0 ? (
                          <div>
                            {/* Common keywords analysis */}
                            <div className="mb-6">
                              <h3 className="text-sm font-medium mb-3">Common Keywords in Titles</h3>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                                {(() => {
                                  // Extract and count keywords from titles
                                  const keywordCounts: {[key: string]: number} = {};
                                  const stopWords = ['a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'as', 'of', 'from'];

                                  displayedVideos.forEach(video => {
                                    if (!video.title) return;

                                    // Extract words, remove punctuation, convert to lowercase
                                    const words = video.title
                                      .toLowerCase()
                                      .replace(/[^\w\s]/g, '')
                                      .split(/\s+/)
                                      .filter(word => word.length > 3 && !stopWords.includes(word));

                                    words.forEach(word => {
                                      keywordCounts[word] = (keywordCounts[word] || 0) + 1;
                                    });
                                  });

                                  // Sort keywords by frequency
                                  const sortedKeywords = Object.entries(keywordCounts)
                                    .filter(([_, count]) => count > 1) // Only show keywords that appear more than once
                                    .sort((a, b) => b[1] - a[1])
                                    .slice(0, 8); // Show top 8 keywords

                                  return sortedKeywords.map(([keyword, count]) => (
                                    <div key={keyword} className="p-2 border rounded-lg bg-muted/20">
                                      <div className="flex items-center justify-between">
                                        <span className="font-medium text-sm">{keyword}</span>
                                        <Badge variant="outline" className="bg-slate-100 text-slate-800 border-slate-200">
                                          {count}
                                        </Badge>
                                      </div>
                                    </div>
                                  ));
                                })()}
                              </div>
                            </div>

                            {/* Performance by video length */}
                            <div>
                              <h3 className="text-sm font-medium mb-3">Performance by Video Length</h3>
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                {(() => {
                                  // Categorize videos by duration
                                  const shorts = displayedVideos.filter(v => v.duration && v.duration < 60);
                                  const medium = displayedVideos.filter(v => v.duration && v.duration >= 60 && v.duration < 600);
                                  const long = displayedVideos.filter(v => v.duration && v.duration >= 600);

                                  // Calculate average VPH for each category
                                  const calculateAvgVph = (videos: YoutubeVideo[]) => {
                                    if (videos.length === 0) return 0;
                                    const totalVph = videos.reduce((sum, v) => sum + (v.vph || 0), 0);
                                    return Math.round(totalVph / videos.length);
                                  };

                                  const shortsAvgVph = calculateAvgVph(shorts);
                                  const mediumAvgVph = calculateAvgVph(medium);
                                  const longAvgVph = calculateAvgVph(long);

                                  // Find max for relative scaling
                                  const maxAvgVph = Math.max(shortsAvgVph, mediumAvgVph, longAvgVph) || 1;

                                  const categories = [
                                    { name: 'Shorts (<1 min)', count: shorts.length, avgVph: shortsAvgVph, percentage: (shortsAvgVph / maxAvgVph) * 100 },
                                    { name: 'Medium (1-10 min)', count: medium.length, avgVph: mediumAvgVph, percentage: (mediumAvgVph / maxAvgVph) * 100 },
                                    { name: 'Long (>10 min)', count: long.length, avgVph: longAvgVph, percentage: (longAvgVph / maxAvgVph) * 100 }
                                  ];

                                  return categories.map(category => (
                                    <div key={category.name} className="p-3 border rounded-lg">
                                      <div className="flex justify-between items-center mb-2">
                                        <h4 className="font-medium text-sm">{category.name}</h4>
                                        <span className="text-xs text-muted-foreground">{category.count} videos</span>
                                      </div>
                                      <div className="h-2 bg-muted rounded-full mb-2">
                                        <div
                                          className="h-full bg-primary rounded-full"
                                          style={{ width: `${category.percentage}%` }}
                                        ></div>
                                      </div>
                                      <div className="text-sm font-medium text-right">
                                        {category.avgVph.toLocaleString()} avg VPH
                                      </div>
                                    </div>
                                  ));
                                })()}
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-[200px] border rounded-lg">
                            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                            <p className="text-muted-foreground">No video data available</p>
                            <p className="text-sm text-muted-foreground mt-1">Videos need to be loaded to analyze content patterns</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Channel Performance Comparison */}
                  <Card className="col-span-1 md:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Users className="h-5 w-5 text-primary" />
                        Channel Performance Comparison
                      </CardTitle>
                      <CardDescription>
                        Compare performance metrics across channels
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {channels.length > 0 ? (
                          <div>
                            <div className="overflow-x-auto border rounded-lg">
                              <table className="w-full border-collapse">
                                <thead>
                                  <tr className="border-b bg-muted/30">
                                    <th className="text-left py-3 px-4 text-sm font-medium text-foreground">Channel</th>
                                    <th className="text-right py-3 px-4 text-sm font-medium text-foreground">Videos</th>
                                    <th className="text-right py-3 px-4 text-sm font-medium text-foreground">Avg VPH</th>
                                    <th className="text-right py-3 px-4 text-sm font-medium text-foreground">New (6h)</th>
                                    <th className="text-right py-3 px-4 text-sm font-medium text-foreground">Trending</th>
                                    <th className="text-right py-3 px-4 text-sm font-medium text-foreground">Health Score</th>
                                  </tr>
                                </thead>
                                <tbody className="divide-y">
                                  {channels.map(channel => {

                                    // Get videos for this channel
                                    const channelVideos = displayedVideos.filter(v => v.channelId === channel.channelId);

                                    // Calculate metrics
                                    const videoCount = channelVideos.length;

                                    // Average VPH
                                    const totalVph = channelVideos.reduce((sum, v) => sum + (v.vph || 0), 0);
                                    const avgVph = videoCount > 0 ? Math.round(totalVph / videoCount) : 0;

                                    // New videos in last 6 hours
                                    const sixHoursAgo = new Date(Date.now() - 6 * 60 * 60 * 1000);
                                    const newVideosCount = channelVideos.filter(v => {
                                      const publishedDate = new Date(v.publishedAt);
                                      return publishedDate > sixHoursAgo;
                                    }).length;

                                    // Trending videos
                                    const trendingVideosCount = channelVideos.filter(v => {
                                      if (previousVphValues[v.id]) {
                                        const prevVphData = previousVphValues[v.id];
                                        const previousVph = prevVphData.previousValue || 0;
                                        const currentVph = v.vph || 0;
                                        const momentumDelta = currentVph - previousVph;
                                        const momentumThreshold = Math.max(2, previousVph * 0.01);
                                        const timeSincePrevious = prevVphData.timestamp - prevVphData.previousTimestamp;
                                        const minutesSincePrevious = timeSincePrevious / (1000 * 60);
                                        return momentumDelta > 0 && Math.abs(momentumDelta) >= momentumThreshold && minutesSincePrevious >= 5;
                                      }
                                      return false;
                                    }).length;

                                    // Calculate health score (0-100)
                                    // Based on: new videos, trending videos, and average VPH
                                    const newVideoScore = Math.min(newVideosCount * 20, 40); // Max 40 points for new videos
                                    const trendingVideoScore = Math.min(trendingVideosCount * 15, 30); // Max 30 points for trending

                                    // VPH score - relative to highest VPH channel
                                    const maxAvgVph = Math.max(...channels.map(c => {
                                      const cVideos = displayedVideos.filter(v => v.channelId === c.channelId);
                                      if (cVideos.length === 0) return 0;
                                      const cTotalVph = cVideos.reduce((sum, v) => sum + (v.vph || 0), 0);
                                      return Math.round(cTotalVph / cVideos.length);
                                    })) || 1;

                                    const vphScore = Math.round((avgVph / maxAvgVph) * 30); // Max 30 points for VPH

                                    const healthScore = newVideoScore + trendingVideoScore + vphScore;

                                    // Get health score color
                                    const getHealthColor = (score: number) => {
                                      if (score >= 80) return 'text-green-600';
                                      if (score >= 60) return 'text-emerald-600';
                                      if (score >= 40) return 'text-amber-600';
                                      if (score >= 20) return 'text-orange-600';
                                      return 'text-red-600';
                                    };

                                    return (
                                      <tr key={channel.id} className="hover:bg-muted/20 border-b border-border/40">
                                        <td className="py-3 px-4">
                                          <div className="flex items-center gap-2">
                                            <div className="w-8 h-8 rounded-full overflow-hidden bg-muted border border-border flex-shrink-0">
                                              <ChannelThumbnail
                                                thumbnail={channel.thumbnail || channel.thumbnailUrl}
                                                title={channel.channelTitle || channel.title}
                                                className="w-full h-full object-cover"
                                              />
                                            </div>
                                            <span className="font-medium text-sm text-foreground line-clamp-1">
                                              {channel.channelTitle || channel.title || `Channel ${channel.id}`}
                                            </span>
                                          </div>
                                        </td>
                                        <td className="py-3 px-4 text-right text-sm text-foreground">{videoCount}</td>
                                        <td className="py-3 px-4 text-right text-sm text-foreground">{avgVph.toLocaleString()}</td>
                                        <td className="py-3 px-4 text-right text-sm text-foreground">
                                          {newVideosCount > 0 ? (
                                            <span className="font-medium text-green-600">{newVideosCount}</span>
                                          ) : (
                                            <span>{newVideosCount}</span>
                                          )}
                                        </td>
                                        <td className="py-3 px-4 text-right text-sm text-foreground">
                                          {trendingVideosCount > 0 ? (
                                            <span className="font-medium text-amber-600">{trendingVideosCount}</span>
                                          ) : (
                                            <span>{trendingVideosCount}</span>
                                          )}
                                        </td>
                                        <td className="py-3 px-4 text-right">
                                          <div className="flex items-center justify-end gap-2">
                                            <div className="w-16 bg-muted rounded-full h-1.5">
                                              <div
                                                className={`h-full rounded-full ${getHealthColor(healthScore).replace('text-', 'bg-')}`}
                                                style={{ width: `${healthScore}%` }}
                                              ></div>
                                            </div>
                                            <span className={`font-medium text-sm ${getHealthColor(healthScore)}`}>
                                              {healthScore}
                                            </span>
                                          </div>
                                        </td>
                                      </tr>
                                    );
                                  })}
                                </tbody>
                              </table>
                            </div>

                            <div className="mt-4 p-3 border rounded-lg bg-muted/10">
                              <h4 className="text-sm font-medium mb-2 text-foreground">Channel Health Score Explanation</h4>
                              <p className="text-xs text-foreground">The Health Score (0-100) is calculated based on:</p>
                              <ul className="text-xs text-foreground mt-1 space-y-1 list-disc pl-5">
                                <li><span className="font-medium">New Videos (40%)</span>: Recent uploads in the last 6 hours</li>
                                <li><span className="font-medium">Trending Videos (30%)</span>: Videos with positive momentum</li>
                                <li><span className="font-medium">Average VPH (30%)</span>: Views per hour relative to other channels</li>
                              </ul>
                              <div className="flex items-center gap-2 mt-2">
                                <div className="flex items-center gap-1">
                                  <div className="w-3 h-3 rounded-full bg-green-600"></div>
                                  <span className="text-xs">80-100: Excellent</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-3 h-3 rounded-full bg-emerald-600"></div>
                                  <span className="text-xs">60-79: Good</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-3 h-3 rounded-full bg-amber-600"></div>
                                  <span className="text-xs">40-59: Average</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-3 h-3 rounded-full bg-orange-600"></div>
                                  <span className="text-xs">20-39: Poor</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-3 h-3 rounded-full bg-red-600"></div>
                                  <span className="text-xs">0-19: Critical</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-[200px] border rounded-lg bg-muted/5">
                            <Users className="h-12 w-12 text-primary/60 mb-4" />
                            <p className="text-foreground font-medium">No channel data available</p>
                            <p className="text-sm text-foreground mt-1">Add YouTube channels to compare their performance metrics</p>
                            <div className="mt-4">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setAddChannelDialogOpen(true)}
                                className="flex items-center gap-2"
                              >
                                <Plus className="h-4 w-4" />
                                Add Channel
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Engagement Quality Metrics */}
                  <Card className="col-span-1 md:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5 text-primary" />
                        Engagement Quality Metrics
                      </CardTitle>
                      <CardDescription>
                        Analyze engagement beyond raw view counts
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {displayedVideos.length > 0 ? (
                          <div>
                            {/* Engagement quality score calculation */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                              <div>
                                <h3 className="text-sm font-medium mb-3">Top Engagement Quality Videos</h3>
                                <div className="space-y-3">
                                  {displayedVideos
                                    .filter(video => video.likeCount && video.viewCount && video.commentCount)
                                    .map(video => {
                                      // Calculate engagement quality score
                                      const likeRatio = video.likeCount ? (video.likeCount / video.viewCount) * 100 : 0;
                                      const commentRatio = video.commentCount ? (video.commentCount / video.viewCount) * 100 : 0;

                                      // Weight: 60% likes, 40% comments
                                      const engagementScore = (likeRatio * 0.6) + (commentRatio * 0.4);

                                      return {
                                        video,
                                        engagementScore,
                                        likeRatio,
                                        commentRatio
                                      };
                                    })
                                    .sort((a, b) => b.engagementScore - a.engagementScore)
                                    .slice(0, 5)
                                    .map(({ video, engagementScore, likeRatio, commentRatio }) => (
                                      <div key={video.id} className="p-3 border rounded-lg hover:bg-muted/10">
                                        <div className="flex items-start gap-3">
                                          <div className="w-[80px] h-[45px] rounded overflow-hidden flex-shrink-0">
                                            <YoutubeThumbnail
                                              videoId={video.id}
                                              thumbnail={video.thumbnailUrl}
                                              title={video.title}
                                              className="w-full h-full object-cover"
                                            />
                                          </div>
                                          <div className="flex-1 min-w-0">
                                            <h4 className="font-medium text-sm line-clamp-1">{video.title}</h4>
                                            <div className="flex items-center gap-3 mt-1">
                                              <div className="flex items-center gap-1">
                                                <span className="text-xs font-medium">Score:</span>
                                                <span className="text-xs font-bold text-green-600">{engagementScore.toFixed(2)}</span>
                                              </div>
                                              <div className="flex items-center gap-1">
                                                <span className="text-xs">L: {likeRatio.toFixed(2)}%</span>
                                              </div>
                                              <div className="flex items-center gap-1">
                                                <span className="text-xs">C: {commentRatio.toFixed(2)}%</span>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                </div>
                              </div>

                              <div>
                                <h3 className="text-sm font-medium mb-3">Engagement Distribution</h3>
                                <div className="space-y-4">
                                  {(() => {
                                    // Calculate engagement metrics across all videos
                                    const videosWithData = displayedVideos.filter(v =>
                                      v.likeCount !== undefined &&
                                      v.viewCount !== undefined &&
                                      v.commentCount !== undefined
                                    );

                                    if (videosWithData.length === 0) {
                                      return (
                                        <div className="text-sm text-muted-foreground p-4 border rounded-lg text-center">
                                          No engagement data available
                                        </div>
                                      );
                                    }

                                    // Calculate average engagement metrics
                                    const totalViews = videosWithData.reduce((sum, v) => sum + (v.viewCount || 0), 0);
                                    const totalLikes = videosWithData.reduce((sum, v) => sum + (v.likeCount || 0), 0);
                                    const totalComments = videosWithData.reduce((sum, v) => sum + (v.commentCount || 0), 0);

                                    const avgLikeRatio = totalViews > 0 ? (totalLikes / totalViews) * 100 : 0;
                                    const avgCommentRatio = totalViews > 0 ? (totalComments / totalViews) * 100 : 0;

                                    // Categorize videos by engagement quality
                                    const getEngagementScore = (video: YoutubeVideo) => {
                                      const likeRatio = video.likeCount && video.viewCount ? (video.likeCount / video.viewCount) * 100 : 0;
                                      const commentRatio = video.commentCount && video.viewCount ? (video.commentCount / video.viewCount) * 100 : 0;
                                      return (likeRatio * 0.6) + (commentRatio * 0.4);
                                    };

                                    const highEngagement = videosWithData.filter(v => getEngagementScore(v) > avgLikeRatio * 1.5);
                                    const mediumEngagement = videosWithData.filter(v => {
                                      const score = getEngagementScore(v);
                                      return score >= avgLikeRatio * 0.5 && score <= avgLikeRatio * 1.5;
                                    });
                                    const lowEngagement = videosWithData.filter(v => getEngagementScore(v) < avgLikeRatio * 0.5);

                                    const categories = [
                                      { name: 'High Engagement', count: highEngagement.length, percentage: (highEngagement.length / videosWithData.length) * 100, color: 'bg-green-500' },
                                      { name: 'Medium Engagement', count: mediumEngagement.length, percentage: (mediumEngagement.length / videosWithData.length) * 100, color: 'bg-amber-500' },
                                      { name: 'Low Engagement', count: lowEngagement.length, percentage: (lowEngagement.length / videosWithData.length) * 100, color: 'bg-red-500' }
                                    ];

                                    return (
                                      <>
                                        <div className="grid grid-cols-2 gap-4 mb-4">
                                          <div className="p-3 border rounded-lg">
                                            <div className="text-xs text-muted-foreground mb-1">Average Like Ratio</div>
                                            <div className="text-lg font-bold">{avgLikeRatio.toFixed(2)}%</div>
                                          </div>
                                          <div className="p-3 border rounded-lg">
                                            <div className="text-xs text-muted-foreground mb-1">Average Comment Ratio</div>
                                            <div className="text-lg font-bold">{avgCommentRatio.toFixed(2)}%</div>
                                          </div>
                                        </div>

                                        <div className="space-y-3">
                                          {categories.map(category => (
                                            <div key={category.name} className="space-y-1">
                                              <div className="flex justify-between items-center">
                                                <span className="text-sm">{category.name}</span>
                                                <span className="text-sm font-medium">{category.count} videos ({category.percentage.toFixed(1)}%)</span>
                                              </div>
                                              <div className="h-2 bg-muted rounded-full">
                                                <div
                                                  className={`h-full ${category.color} rounded-full`}
                                                  style={{ width: `${category.percentage}%` }}
                                                ></div>
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      </>
                                    );
                                  })()}
                                </div>
                              </div>
                            </div>

                            <div className="border-t pt-4">
                              <h3 className="text-sm font-medium mb-3">Engagement Quality Formula</h3>
                              <div className="text-sm p-3 border rounded-lg bg-muted/10">
                                <p className="text-foreground font-medium">Engagement Score = (Like Ratio × 0.6) + (Comment Ratio × 0.4)</p>
                                <p className="mt-2 text-foreground">Where:</p>
                                <ul className="list-disc pl-5 mt-1 space-y-1">
                                  <li className="text-foreground">Like Ratio = (Likes ÷ Views) × 100</li>
                                  <li className="text-foreground">Comment Ratio = (Comments ÷ Views) × 100</li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-[200px] border rounded-lg">
                            <Activity className="h-12 w-12 text-muted-foreground mb-4" />
                            <p className="text-muted-foreground">No video data available</p>
                            <p className="text-sm text-muted-foreground mt-1">Videos need to be loaded to analyze engagement</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Predictive Analytics Dashboard */}
                  <Card className="col-span-1 md:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Sparkles className="h-5 w-5 text-primary" />
                        Predictive Analytics
                      </CardTitle>
                      <CardDescription>
                        View count predictions based on early performance
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {displayedVideos.length > 0 ? (
                          <div>
                            <div className="mb-6">
                              <h3 className="text-sm font-medium mb-3">24-Hour View Predictions</h3>
                              <div className="space-y-3">
                                {displayedVideos
                                  .filter(video => {
                                    // Only include videos published in the last 12 hours
                                    const publishedDate = new Date(video.publishedAt);
                                    const twelveHoursAgo = new Date(Date.now() - 12 * 60 * 60 * 1000);
                                    return publishedDate > twelveHoursAgo && video.vph && video.viewCount;
                                  })
                                  .sort((a, b) => {
                                    // Sort by predicted views (descending)
                                    const aVph = a.vph || 0;
                                    const bVph = b.vph || 0;
                                    const aPublished = new Date(a.publishedAt).getTime();
                                    const bPublished = new Date(b.publishedAt).getTime();
                                    const aHoursSincePublished = (Date.now() - aPublished) / (1000 * 60 * 60);
                                    const bHoursSincePublished = (Date.now() - bPublished) / (1000 * 60 * 60);

                                    // Predict 24-hour views based on current VPH and hours since published
                                    const aPredicted = (a.viewCount || 0) + (aVph * (24 - Math.min(24, aHoursSincePublished)));
                                    const bPredicted = (b.viewCount || 0) + (bVph * (24 - Math.min(24, bHoursSincePublished)));

                                    return bPredicted - aPredicted;
                                  })
                                  .slice(0, 5)
                                  .map(video => {
                                    const publishedDate = new Date(video.publishedAt).getTime();
                                    const hoursSincePublished = (Date.now() - publishedDate) / (1000 * 60 * 60);
                                    const vph = video.vph || 0;
                                    const currentViews = video.viewCount || 0;

                                    // Predict 24-hour views
                                    const predicted24hViews = currentViews + (vph * (24 - Math.min(24, hoursSincePublished)));

                                    // Calculate confidence based on hours since published
                                    // Higher confidence for videos with more hours of data
                                    const confidencePercentage = Math.min(100, (hoursSincePublished / 6) * 100);

                                    // Get confidence level text
                                    const getConfidenceLevel = (percentage: number) => {
                                      if (percentage >= 80) return 'High';
                                      if (percentage >= 40) return 'Medium';
                                      return 'Low';
                                    };

                                    // Get confidence color
                                    const getConfidenceColor = (percentage: number) => {
                                      if (percentage >= 80) return 'text-green-600';
                                      if (percentage >= 40) return 'text-amber-600';
                                      return 'text-red-600';
                                    };

                                    return (
                                      <div key={video.id} className="p-3 border rounded-lg hover:bg-muted/10">
                                        <div className="flex items-start gap-3">
                                          <div className="w-[80px] h-[45px] rounded overflow-hidden flex-shrink-0">
                                            <YoutubeThumbnail
                                              videoId={video.id}
                                              thumbnail={video.thumbnailUrl}
                                              title={video.title}
                                              className="w-full h-full object-cover"
                                            />
                                          </div>
                                          <div className="flex-1 min-w-0">
                                            <h4 className="font-medium text-sm line-clamp-1">{video.title}</h4>
                                            <div className="flex items-center gap-2 mt-1">
                                              <span className="text-xs text-muted-foreground">
                                                {Math.round(hoursSincePublished)}h since published
                                              </span>
                                            </div>
                                            <div className="flex items-center gap-3 mt-2">
                                              <div className="flex items-center gap-1">
                                                <Eye className="h-3 w-3 text-muted-foreground" />
                                                <span className="text-xs">{currentViews.toLocaleString()} views</span>
                                              </div>
                                              <div className="flex items-center gap-1">
                                                <Sparkles className="h-3 w-3 text-primary" />
                                                <span className="text-xs font-medium">
                                                  {Math.round(predicted24hViews).toLocaleString()} predicted
                                                </span>
                                              </div>
                                              <div className="flex items-center gap-1">
                                                <span className={`text-xs font-medium ${getConfidenceColor(confidencePercentage)}`}>
                                                  {getConfidenceLevel(confidencePercentage)} confidence
                                                </span>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    );
                                  })}
                              </div>
                            </div>

                            <div className="border-t pt-4">
                              <h3 className="text-sm font-medium mb-3">Prediction Methodology</h3>
                              <div className="text-sm p-3 border rounded-lg bg-muted/10">
                                <p className="text-foreground font-medium">24-Hour View Prediction = Current Views + (VPH × Remaining Hours)</p>
                                <p className="mt-2 text-foreground">Where:</p>
                                <ul className="list-disc pl-5 mt-1 space-y-1">
                                  <li className="text-foreground">VPH = Current Views Per Hour</li>
                                  <li className="text-foreground">Remaining Hours = 24 - Hours Since Published (max 24)</li>
                                  <li className="text-foreground">Confidence increases with more hours of data</li>
                                </ul>
                                <p className="mt-3 text-xs text-foreground font-medium">Note: Predictions are estimates based on current performance and may not reflect actual future views.</p>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-[200px] border rounded-lg">
                            <Sparkles className="h-12 w-12 text-muted-foreground mb-4" />
                            <p className="text-muted-foreground">No video data available</p>
                            <p className="text-sm text-muted-foreground mt-1">Videos need to be loaded to generate predictions</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>



                  {/* Competitive Intelligence Dashboard */}
                  <Card className="col-span-1 md:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <ArrowUpRight className="h-5 w-5 text-primary" />
                        Competitive Intelligence
                      </CardTitle>
                      <CardDescription>
                        Track performance across competing channels in the same niche
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {channels.length > 1 ? (
                          <div>
                            {/* Channel comparison metrics */}
                            <div className="mb-6">
                              <h3 className="text-sm font-medium mb-3">Channel Performance Comparison</h3>
                              <div className="border rounded-lg overflow-hidden">
                                <div className="overflow-x-auto">
                                  <table className="w-full">
                                    <thead>
                                      <tr className="bg-muted/30">
                                        <th className="text-left py-2 px-3 text-xs font-medium">Channel</th>
                                        <th className="text-right py-2 px-3 text-xs font-medium">Videos</th>
                                        <th className="text-right py-2 px-3 text-xs font-medium">Avg VPH</th>
                                        <th className="text-right py-2 px-3 text-xs font-medium">Avg Views</th>
                                        <th className="text-right py-2 px-3 text-xs font-medium">Engagement</th>
                                        <th className="text-right py-2 px-3 text-xs font-medium">Market Share</th>
                                      </tr>
                                    </thead>
                                    <tbody className="divide-y">
                                      {(() => {
                                        // Calculate metrics for each channel
                                        const channelMetrics = channels.map(channel => {
                                          const channelVideos = displayedVideos.filter(v => v.channelId === channel.channelId);

                                          // Skip channels with no videos
                                          if (channelVideos.length === 0) {
                                            return {
                                              channel,
                                              videoCount: 0,
                                              avgVph: 0,
                                              avgViews: 0,
                                              engagement: 0,
                                              totalViews: 0
                                            };
                                          }

                                          // Calculate metrics
                                          const totalVph = channelVideos.reduce((sum, v) => sum + (v.vph || 0), 0);
                                          const totalViews = channelVideos.reduce((sum, v) => sum + (v.viewCount || 0), 0);

                                          // Calculate engagement (likes + comments per view)
                                          const videosWithEngagement = channelVideos.filter(v =>
                                            v.viewCount && (v.likeCount || v.commentCount)
                                          );

                                          let engagement = 0;
                                          if (videosWithEngagement.length > 0) {
                                            const totalLikes = videosWithEngagement.reduce((sum, v) => sum + (v.likeCount || 0), 0);
                                            const totalComments = videosWithEngagement.reduce((sum, v) => sum + (v.commentCount || 0), 0);
                                            const totalEngagementViews = videosWithEngagement.reduce((sum, v) => sum + (v.viewCount || 0), 0);

                                            engagement = totalEngagementViews > 0
                                              ? ((totalLikes + totalComments) / totalEngagementViews) * 100
                                              : 0;
                                          }

                                          return {
                                            channel,
                                            videoCount: channelVideos.length,
                                            avgVph: totalVph / channelVideos.length,
                                            avgViews: totalViews / channelVideos.length,
                                            engagement,
                                            totalViews
                                          };
                                        }).filter(metrics => metrics.videoCount > 0);

                                        // Calculate total views across all channels for market share
                                        const totalViewsAllChannels = channelMetrics.reduce((sum, m) => sum + m.totalViews, 0);

                                        // Sort by market share (total views)
                                        return channelMetrics
                                          .sort((a, b) => b.totalViews - a.totalViews)
                                          .map(metrics => {
                                            const marketShare = totalViewsAllChannels > 0
                                              ? (metrics.totalViews / totalViewsAllChannels) * 100
                                              : 0;

                                            return (
                                              <tr key={metrics.channel.id} className="hover:bg-muted/10">
                                                <td className="py-2 px-3">
                                                  <div className="flex items-center gap-2">
                                                    <div className="w-5 h-5 rounded-full overflow-hidden bg-muted">
                                                      <ChannelThumbnail
                                                        thumbnail={metrics.channel.thumbnail || metrics.channel.thumbnailUrl}
                                                        title={metrics.channel.channelTitle || metrics.channel.title}
                                                        className="w-full h-full object-cover"
                                                      />
                                                    </div>
                                                    <span className="text-xs font-medium">
                                                      {metrics.channel.channelTitle || metrics.channel.title || `Channel ${metrics.channel.id}`}
                                                    </span>
                                                  </div>
                                                </td>
                                                <td className="py-2 px-3 text-right text-xs">{metrics.videoCount}</td>
                                                <td className="py-2 px-3 text-right text-xs">{Math.round(metrics.avgVph).toLocaleString()}</td>
                                                <td className="py-2 px-3 text-right text-xs">{Math.round(metrics.avgViews).toLocaleString()}</td>
                                                <td className="py-2 px-3 text-right text-xs">{metrics.engagement.toFixed(2)}%</td>
                                                <td className="py-2 px-3 text-right">
                                                  <div className="flex items-center justify-end gap-2">
                                                    <div className="w-16 bg-muted rounded-full h-1.5">
                                                      <div
                                                        className="h-full bg-primary rounded-full"
                                                        style={{ width: `${marketShare}%` }}
                                                      ></div>
                                                    </div>
                                                    <span className="text-xs font-medium">{marketShare.toFixed(1)}%</span>
                                                  </div>
                                                </td>
                                              </tr>
                                            );
                                          });
                                      })()}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </div>

                            {/* Content gap analysis */}
                            <div>
                              <h3 className="text-sm font-medium mb-3">Content Gap Analysis</h3>
                              <div className="border rounded-lg p-4">
                                <div className="space-y-4">
                                  {(() => {
                                    // Analyze video titles across channels to identify content gaps
                                    // Extract common keywords/topics from titles
                                    const keywordsByChannel: Record<string, Record<string, number>> = {};

                                    // Common words to exclude
                                    const stopWords = ['a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'as', 'of', 'from', 'how', 'why', 'what', 'when', 'where', 'who', 'this', 'that', 'these', 'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'should', 'can', 'could', 'may', 'might', 'must', 'shall', 'should'];

                                    // Initialize keyword counts for each channel
                                    channels.forEach(channel => {
                                      keywordsByChannel[channel.id] = {};
                                    });

                                    // Count keywords for each channel
                                    displayedVideos.forEach(video => {
                                      if (!video.title || !video.channelId) return;

                                      const channelId = channels.find(c => c.channelId === video.channelId)?.id;
                                      if (!channelId || !keywordsByChannel[channelId]) return;

                                      // Extract words, remove punctuation, convert to lowercase
                                      const words = video.title
                                        .toLowerCase()
                                        .replace(/[^\w\s]/g, '')
                                        .split(/\s+/)
                                        .filter(word => word.length > 3 && !stopWords.includes(word));

                                      words.forEach(word => {
                                        keywordsByChannel[channelId][word] = (keywordsByChannel[channelId][word] || 0) + 1;
                                      });
                                    });

                                    // Find content gaps (keywords that some channels use but others don't)
                                    const allKeywords = new Set<string>();
                                    Object.values(keywordsByChannel).forEach(keywords => {
                                      Object.keys(keywords).forEach(keyword => {
                                        if (keywords[keyword] >= 2) { // Only consider keywords that appear at least twice
                                          allKeywords.add(keyword);
                                        }
                                      });
                                    });

                                    // Calculate content gaps
                                    const contentGaps: {
                                      keyword: string;
                                      usedBy: string[];
                                      notUsedBy: string[];
                                      totalUses: number;
                                    }[] = [];

                                    allKeywords.forEach(keyword => {
                                      const usedBy: string[] = [];
                                      const notUsedBy: string[] = [];
                                      let totalUses = 0;

                                      channels.forEach(channel => {
                                        if (keywordsByChannel[channel.id]?.[keyword]) {
                                          usedBy.push(channel.channelTitle);
                                          totalUses += keywordsByChannel[channel.id][keyword];
                                        } else {
                                          notUsedBy.push(channel.channelTitle);
                                        }
                                      });

                                      // Only include if it's a gap (some channels use it, others don't)
                                      if (usedBy.length > 0 && notUsedBy.length > 0) {
                                        contentGaps.push({ keyword, usedBy, notUsedBy, totalUses });
                                      }
                                    });

                                    // Sort by total uses (descending)
                                    contentGaps.sort((a, b) => b.totalUses - a.totalUses);

                                    if (contentGaps.length === 0) {
                                      return (
                                        <div className="text-sm text-foreground text-center p-4 border rounded-lg bg-muted/10">
                                          No significant content gaps found between channels
                                        </div>
                                      );
                                    }

                                    return (
                                      <div className="space-y-3">
                                        <p className="text-xs text-foreground p-2 bg-muted/10 rounded-md mb-2">
                                          <span className="font-medium">Content gaps</span> represent topics that some channels cover but others don't
                                        </p>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                          {contentGaps.slice(0, 6).map(gap => (
                                            <div key={gap.keyword} className="p-3 border rounded-lg">
                                              <div className="flex justify-between items-center mb-2">
                                                <h4 className="font-medium text-sm capitalize">{gap.keyword}</h4>
                                                <span className="text-xs text-muted-foreground">{gap.totalUses} uses</span>
                                              </div>

                                              <div className="space-y-1">
                                                <div className="flex items-start gap-2">
                                                  <span className="text-xs text-green-600 font-medium whitespace-nowrap">Used by:</span>
                                                  <span className="text-xs">{gap.usedBy.join(', ')}</span>
                                                </div>

                                                <div className="flex items-start gap-2">
                                                  <span className="text-xs text-red-600 font-medium whitespace-nowrap">Not used by:</span>
                                                  <span className="text-xs">{gap.notUsedBy.join(', ')}</span>
                                                </div>
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    );
                                  })()}
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-[200px] border rounded-lg bg-muted/5">
                            <ArrowUpRight className="h-12 w-12 text-primary/60 mb-4" />
                            <p className="text-foreground font-medium">Not enough channels for comparison</p>
                            <p className="text-sm text-foreground mt-1">Add multiple channels to enable competitive analysis</p>
                            <div className="mt-4">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setAddChannelDialogOpen(true)}
                                className="flex items-center gap-2"
                              >
                                <Plus className="h-4 w-4" />
                                Add Channel
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>


    </div>
  );
}

// VideoMetricsChart component - separated to prevent hooks issues
const VideoMetricsChart = memo(({ videoMetricsHistory, selectedDay }: {
  videoMetricsHistory: {
    timestamp: number;
    newVideosCount: number;
    trendingVideosCount: number;
  }[];
  selectedDay: string;
}) => {
  // Filter and prepare data outside of render
  const chartData = useMemo(() => {
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    const today = new Date(now);
    today.setHours(0, 0, 0, 0);

    const weekAgo = new Date(now);
    weekAgo.setDate(weekAgo.getDate() - 7);

    // Filter data based on selected timeframe
    let filteredData = [];

    if (selectedDay === "yesterday") {
      filteredData = videoMetricsHistory.filter(item => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= yesterday && itemDate < today;
      });
    } else if (selectedDay === "today") {
      filteredData = videoMetricsHistory.filter(item => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= today;
      });
    } else if (selectedDay.startsWith("day-")) {
      // Handle specific day selection (day-2, day-3, etc.)
      const daysAgo = parseInt(selectedDay.split("-")[1]);
      const targetDay = new Date(now);
      targetDay.setDate(targetDay.getDate() - daysAgo);
      targetDay.setHours(0, 0, 0, 0);

      const nextDay = new Date(targetDay);
      nextDay.setDate(nextDay.getDate() + 1);

      filteredData = videoMetricsHistory.filter(item => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= targetDay && itemDate < nextDay;
      });
    } else if (selectedDay === "week") {
      filteredData = videoMetricsHistory.filter(item => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= weekAgo;
      });
    }

    // Make sure we have enough data points for the chart to display properly
    // If we have very few points, add interpolated points to create a smoother line
    let optimizedData = filteredData;

    // If we have fewer than 3 data points, we need to ensure the chart can still display properly
    if (filteredData.length >= 1 && filteredData.length < 3) {
      // If we only have one point, duplicate it with a slight offset to create a line
      if (filteredData.length === 1) {
        const point = filteredData[0];
        // Create a second point 5 minutes after the first
        const secondPoint = {
          ...point,
          timestamp: point.timestamp + (5 * 60 * 1000) // 5 minutes later
        };
        optimizedData = [point, secondPoint];
      }
      // If we have two points, make sure they're far enough apart
      else if (filteredData.length === 2) {
        const timeDiff = Math.abs(filteredData[1].timestamp - filteredData[0].timestamp);
        // If points are too close, add a third point to help form a line
        if (timeDiff < 5 * 60 * 1000) { // Less than 5 minutes apart
          const thirdPoint = {
            ...filteredData[1],
            timestamp: filteredData[1].timestamp + (5 * 60 * 1000) // 5 minutes after the second point
          };
          optimizedData = [...filteredData, thirdPoint];
        }
      }
    }
    // For larger datasets, we can optimize by reducing points if needed
    else if (filteredData.length > 100) {
      const MAX_CHART_POINTS = 100; // Increased limit for more detailed rendering
      const interval = Math.ceil(filteredData.length / MAX_CHART_POINTS);
      optimizedData = filteredData.filter((_, index) => index % interval === 0);

      // Always include the last point for accurate current state
      if (filteredData.length > 0 && optimizedData[optimizedData.length - 1] !== filteredData[filteredData.length - 1]) {
        optimizedData.push(filteredData[filteredData.length - 1]);
      }
    }

    // Pre-format time strings to avoid doing it during render
    return optimizedData.map(item => ({
      ...item,
      time: new Date(item.timestamp).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }));
  }, [videoMetricsHistory, selectedDay]);

  return (
    <ChartContainer
      config={{
        new: {
          label: "New Videos",
          color: "#22c55e" // green-500
        },
        trending: {
          label: "Trending Videos",
          color: "#f59e0b" // amber-500
        }
      }}
    >
      <RechartsPrimitive.LineChart
        data={chartData}
        margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
      >
        {/* Simplified grid for better performance */}
        <RechartsPrimitive.CartesianGrid
          strokeDasharray="3 3"
          vertical={false}
          stroke="var(--border)"
        />
        <RechartsPrimitive.XAxis
          dataKey="time"
          tick={{ fontSize: 12, fill: "var(--foreground)" }}
          interval={chartData.length > 20 ? "preserveStartEnd" : 0}
          minTickGap={20}
        />
        <RechartsPrimitive.YAxis
          tick={{ fontSize: 12, fill: "var(--foreground)" }}
          width={30}
          allowDecimals={false}
        />
        <ChartTooltip
          contentStyle={{
            backgroundColor: 'var(--background)',
            border: '1px solid var(--border)',
            color: 'var(--foreground)'
          }}
          itemStyle={{ color: 'var(--foreground)' }}
          labelStyle={{ color: 'var(--foreground)' }}
        />
        <RechartsPrimitive.Legend
          formatter={(value) => <span style={{ color: 'var(--foreground)' }}>{value}</span>}
        />
        <RechartsPrimitive.Line
          type="monotone"
          dataKey="newVideosCount"
          name="New Videos"
          stroke="var(--color-new)"
          strokeWidth={2}
          dot={chartData.length < 10} // Show dots for small datasets
          activeDot={{ r: 4 }}
          isAnimationActive={false}
          connectNulls={true} // Connect across null values
        />
        <RechartsPrimitive.Line
          type="monotone"
          dataKey="trendingVideosCount"
          name="Trending Videos"
          stroke="var(--color-trending)"
          strokeWidth={2}
          dot={chartData.length < 10} // Show dots for small datasets
          activeDot={{ r: 4 }}
          isAnimationActive={false}
          connectNulls={true} // Connect across null values
        />
      </RechartsPrimitive.LineChart>
    </ChartContainer>
  );
});