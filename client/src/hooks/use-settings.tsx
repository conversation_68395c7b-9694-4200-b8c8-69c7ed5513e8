import { useQuery, useMutation } from "@tanstack/react-query";
import { UserSettings } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export function useSettings() {
  const { toast } = useToast();

  const {
    data: settings,
    isLoading,
    error,
    refetch,
  } = useQuery<UserSettings>({
    queryKey: ["/api/settings"],
    staleTime: 5000, // Cache for 5 seconds to reduce redundant API calls
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    onSuccess: (data) => {
      // Apply dark mode immediately when settings are loaded
      if (data && data.darkMode !== undefined) {
        console.log('useSettings: Applying dark mode from loaded settings:', data.darkMode);
        if (data.darkMode) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      }
    },
  });

  const updateSettings = useMutation({
    mutationFn: async (newSettings: Partial<UserSettings>) => {
      // Check if this is a partial update (only specific settings)
      const isPartialUpdate = Object.keys(newSettings).length < 10; // Arbitrary threshold

      if (isPartialUpdate) {
        // For partial updates, handle special cases
        const processedSettings = { ...newSettings };

        // Explicitly handle autoAnalyzeOnRefresh as a boolean
        if ('autoAnalyzeOnRefresh' in processedSettings) {
          // Force to a boolean value
          const boolValue = processedSettings.autoAnalyzeOnRefresh === true;
          processedSettings.autoAnalyzeOnRefresh = boolValue;
          console.log('Partial update - setting autoAnalyzeOnRefresh to:', boolValue, 'type:', typeof boolValue);
        }

        // For partial updates, only send the specified settings
        return apiRequest("PUT", "/api/settings", processedSettings)
          .then(res => res.json());
      }

      // For full updates, proceed with the complete settings object

      // Create a clean copy of the settings to send to the server
      // This ensures we don't send any extra properties that might cause issues
      const cleanSettings: UserSettings = {
        darkMode: newSettings.darkMode,
        searchKeywords: Array.isArray(newSettings.searchKeywords) ?
          // Filter out empty strings and ensure each keyword is trimmed
          newSettings.searchKeywords.map(k => k.trim()).filter(k => k !== "") : [],
        minViewsPerHour: newSettings.minViewsPerHour || 10,
        removeDuplicates: newSettings.removeDuplicates,
        excludeWords: Array.isArray(newSettings.excludeWords) ?
          // Filter out empty strings and ensure each word is trimmed
          newSettings.excludeWords.map(w => w.trim()).filter(w => w !== "") : [],
        autoRefreshInterval: newSettings.autoRefreshInterval || 0,
        // preferredPlayback setting removed as it was not functioning properly
        lastRefreshTime: newSettings.lastRefreshTime,
        parallelApiCalls: newSettings.parallelApiCalls,
        watchedVideos: Array.isArray(newSettings.watchedVideos) ? newSettings.watchedVideos : [],
        useInAppPlayer: newSettings.useInAppPlayer,
        previewSound: newSettings.previewSound,
        playbackSound: newSettings.playbackSound,
        // Default settings removed to prevent sort reversion
        defaultDurationFilter: newSettings.defaultDurationFilter || 'all',
        defaultUnwatchedFilter: newSettings.defaultUnwatchedFilter !== undefined ? newSettings.defaultUnwatchedFilter : false,
        // Include home page setting
        homePage: newSettings.homePage,
        // Include Ollama model setting
        ollamaModel: newSettings.ollamaModel || 'llama3',
        // Include auto analyze on refresh setting - explicitly handle as boolean
        autoAnalyzeOnRefresh: newSettings.autoAnalyzeOnRefresh === true,
        // Include active keyword group if it exists
        ...(newSettings.activeKeywordGroupId ? { activeKeywordGroupId: newSettings.activeKeywordGroupId } : {}),
        // Include these if they exist in the original settings
        ...(newSettings.role ? { role: newSettings.role } : {}),
        ...(newSettings.isAdmin !== undefined ? { isAdmin: newSettings.isAdmin } : {}),
      };

      // Log the cleaned settings for debugging
      // Settings prepared for server

      try {
        const res = await apiRequest("PUT", "/api/settings", cleanSettings);
        if (!res.ok) {
          throw new Error(`Failed to update settings: ${res.status} ${res.statusText}`);
        }

        const updatedSettings = await res.json();

        // Force a refetch of the settings to ensure the UI is updated
        setTimeout(() => {
          queryClient.invalidateQueries(["/api/settings"]);
        }, 200);

        return updatedSettings;
      } catch (error) {
        console.error('Error updating settings:', error);
        throw error;
      }
    },
    onSuccess: (data, variables, context) => {
      console.log('Settings update success, updating cache');
      // Immediately update the cache with the new settings
      queryClient.setQueryData(["/api/settings"], data);

      // Apply dark mode immediately if it was changed
      if (data.darkMode !== undefined) {
        console.log('updateSettings onSuccess: Applying dark mode:', data.darkMode);
        if (data.darkMode) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }

        // Also store in localStorage as a backup
        localStorage.setItem('darkMode', data.darkMode ? 'true' : 'false');
      }

      // Check if this is a silent update (used for watch page filter changes)
      // The context parameter is passed from the mutation options
      if (context && context.silent) {
        console.log('Silent settings update - skipping toast and page reload');
        return;
      }

      // Check if this is a watch page filter change
      // We don't want to show a toast for these changes
      const watchPageFilters = [
        'defaultSortMethod',
        'defaultDurationFilter',
        'defaultUnwatchedFilter'
      ];

      // Check if only watch page filters were changed
      // For partial updates, we need to check if all keys are watch page filters
      const isPartialUpdate = Object.keys(variables).length < 10; // Same threshold as in mutationFn
      const onlyWatchPageFiltersChanged = isPartialUpdate &&
        Object.keys(variables).every(key => watchPageFilters.includes(key));

      // Skip toast for watch page filter changes
      if (onlyWatchPageFiltersChanged) {
        console.log('Watch page filter change - skipping toast');
      } else {
        // Show success toast for other changes
        toast({
          title: "Settings updated",
          description: "Your preferences have been saved.",
        });
      }

      // Check if we need to reload the page
      // We don't want to reload for certain settings changes:
      // 1. darkMode changes (handled above)
      // 2. defaultSortMethod changes (handled by the watch page)
      // 3. defaultDurationFilter changes (handled by the watch page)
      // 4. defaultUnwatchedFilter changes (handled by the watch page)
      const noReloadSettings = [
        'darkMode',
        'defaultSortMethod',
        'defaultDurationFilter',
        'defaultUnwatchedFilter',
        'ollamaModel', // No need to reload when changing Ollama model
        'selectedPromptId', // No need to reload when changing selected prompt
        'ollamaAnalysisPrompt', // No need to reload when changing analysis prompt
        'autoAnalyzeOnRefresh' // No need to reload when changing auto analyze setting
        // Note: 'homePage' is not in this list because it requires a page reload
      ];

      // For partial updates, we need to check if any key is not in the no-reload list
      const needsReload = isPartialUpdate ?
        // For partial updates, check if any key is not in the no-reload list
        Object.keys(variables).some(key => !noReloadSettings.includes(key)) :
        // For full updates, check if any non-reload setting was changed
        Object.keys(variables).some(key =>
          !noReloadSettings.includes(key) &&
          JSON.stringify(variables[key]) !== JSON.stringify(data[key])
        );

      console.log('Settings update - needs reload:', needsReload);

      if (needsReload) {
        console.log('Reloading page due to settings change');
        setTimeout(() => {
          window.location.reload();
        }, 1000); // Give the toast time to show
      } else {
        console.log('No page reload needed for this settings change');
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update settings",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteData = useMutation({
    mutationFn: async () => {
      await apiRequest("DELETE", "/api/data");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/settings"] });
      queryClient.invalidateQueries({ queryKey: ["/api/videos"] });
      toast({
        title: "Data deleted",
        description: "All your data has been cleared.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete data",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Function to mark a video as watched
  const markVideoAsWatched = useMutation({
    mutationFn: async (videoId: string) => {
      // Get current settings
      const currentSettings = queryClient.getQueryData<UserSettings>(['/api/settings']);
      if (!currentSettings) {
        throw new Error('Settings not found');
      }

      // Check if video is already marked as watched
      if (currentSettings.watchedVideos?.includes(videoId)) {
        console.log(`Video ${videoId} is already marked as watched`);
        return currentSettings;
      }

      // Add video to watched videos
      const watchedVideos = [...(currentSettings.watchedVideos || []), videoId];

      // Update settings with new watched videos
      const updatedSettings = {
        ...currentSettings,
        watchedVideos
      };

      // Send update to server
      const res = await apiRequest('PUT', '/api/settings', {
        watchedVideos
      });

      if (!res.ok) {
        throw new Error(`Failed to mark video as watched: ${res.status} ${res.statusText}`);
      }

      const result = await res.json();
      return result;
    },
    onSuccess: (data) => {
      // Update cache with new settings
      queryClient.setQueryData(['/api/settings'], data);
    },
    onError: (error: Error) => {
      console.error('Error marking video as watched:', error);
    }
  });

  // Function to remove a video from watched list
  const markVideoAsUnwatched = useMutation({
    mutationFn: async (videoId: string) => {
      // Get current settings
      const currentSettings = queryClient.getQueryData<UserSettings>(['/api/settings']);
      if (!currentSettings) {
        throw new Error('Settings not found');
      }

      // Remove video from watched videos
      const watchedVideos = (currentSettings.watchedVideos || []).filter(id => id !== videoId);

      // Update settings with new watched videos
      const updatedSettings = {
        ...currentSettings,
        watchedVideos
      };

      // Send update to server
      const res = await apiRequest('PUT', '/api/settings', {
        watchedVideos
      });

      if (!res.ok) {
        throw new Error(`Failed to mark video as unwatched: ${res.status} ${res.statusText}`);
      }

      const result = await res.json();
      return result;
    },
    onSuccess: (data) => {
      // Update cache with new settings
      queryClient.setQueryData(['/api/settings'], data);
    },
    onError: (error: Error) => {
      console.error('Error marking video as unwatched:', error);
    }
  });

  return {
    settings,
    isLoading,
    error,
    updateSettings,
    deleteData,
    refetch,
    markVideoAsWatched,
    markVideoAsUnwatched,
  };
}
