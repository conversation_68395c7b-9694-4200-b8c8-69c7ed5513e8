// Modified version of server/routes.ts with additional logging
import express, { type Express } from "express";
import { createServer, type Server } from "http";

console.log('Starting routes test...');

export async function registerRoutes(app: Express): Promise<Server> {
  console.log('Registering routes...');
  
  // Simple route for testing
  app.get('/api/test', (req, res) => {
    console.log('Test route accessed');
    res.json({ message: 'Server is running!' });
  });
  
  console.log('Routes registered successfully');
  return createServer(app);
}

// Test the function
const app = express();
registerRoutes(app)
  .then(server => {
    console.log('Server created successfully');
    const port = 5005;
    server.listen(port, () => {
      console.log(`Test server running on port ${port}`);
    });
  })
  .catch(error => {
    console.error('Error creating server:', error);
  });
