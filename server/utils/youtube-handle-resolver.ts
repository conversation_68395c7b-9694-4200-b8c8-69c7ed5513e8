import axios from 'axios';
import * as cheerio from 'cheerio';

/**
 * Resolves a YouTube handle to a channel ID by scraping the YouTube page
 * @param handle YouTube handle (with or without @)
 * @returns The channel ID or null if not found
 */
export async function resolveYoutubeHandle(handle: string): Promise<string | null> {
  try {
    // Ensure handle starts with @
    const normalizedHandle = handle.startsWith('@') ? handle : `@${handle}`;
    
    // Construct the YouTube URL for the handle
    const url = `https://www.youtube.com/${normalizedHandle}`;
    console.log(`Attempting to resolve YouTube handle: ${normalizedHandle} from URL: ${url}`);
    
    // Fetch the page
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9',
      }
    });
    
    // Load the HTML into cheerio
    const $ = cheerio.load(response.data);
    
    // Method 1: Try to find channel ID in meta tags
    const canonicalUrl = $('link[rel="canonical"]').attr('href');
    if (canonicalUrl && canonicalUrl.includes('/channel/')) {
      const channelId = canonicalUrl.split('/channel/')[1].split(/[?#]/)[0];
      console.log(`Found channel ID in canonical URL: ${channelId}`);
      return channelId;
    }
    
    // Method 2: Look for channel ID in the page content
    // YouTube often includes the channel ID in the page HTML
    const htmlContent = response.data;
    
    // Look for patterns like "externalId":"UC..."
    const externalIdMatch = htmlContent.match(/"externalId":"(UC[a-zA-Z0-9_-]{22})"/);
    if (externalIdMatch && externalIdMatch[1]) {
      console.log(`Found channel ID in externalId: ${externalIdMatch[1]}`);
      return externalIdMatch[1];
    }
    
    // Look for patterns like "channelId":"UC..."
    const channelIdMatch = htmlContent.match(/"channelId":"(UC[a-zA-Z0-9_-]{22})"/);
    if (channelIdMatch && channelIdMatch[1]) {
      console.log(`Found channel ID in channelId: ${channelIdMatch[1]}`);
      return channelIdMatch[1];
    }
    
    // Method 3: Look for RSS feed links
    const feedLink = $('link[type="application/rss+xml"]').attr('href');
    if (feedLink && feedLink.includes('channel_id=')) {
      const channelId = feedLink.split('channel_id=')[1].split(/[?#&]/)[0];
      console.log(`Found channel ID in RSS feed link: ${channelId}`);
      return channelId;
    }
    
    // If we couldn't find the channel ID
    console.log(`Could not find channel ID for handle: ${normalizedHandle}`);
    return null;
  } catch (error) {
    console.error(`Error resolving YouTube handle ${handle}:`, error);
    return null;
  }
}
