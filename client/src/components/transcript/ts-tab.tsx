import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, RefreshCw, Copy, Download, Folder, Youtube, Trash2 } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

interface TsTabProps {
  isActive: boolean;
  onRefresh?: (refreshTime: Date) => void;
}

interface TsChannel {
  id: string;
  channelId: string;
  channelTitle: string;
  channelUrl: string;
  thumbnail?: string;
  videoLimit: number;
  lastRefreshTime?: Date;
  videoCount?: number;
}

interface TsVideo {
  id: string;
  title: string;
  thumbnail: string;
  channelId: string;
  channelTitle: string;
  viewCount: number;
  publishedAt: Date;
  duration?: string;
  transcriptPath?: string;
  hasTranscript: boolean;
}

export function TsTab({ isActive, onRefresh }: TsTabProps) {
  const { toast } = useToast();
  const [channels, setChannels] = useState<TsChannel[]>([]);
  const [videos, setVideos] = useState<TsVideo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState<string>('all');
  const [newChannelUrl, setNewChannelUrl] = useState('');
  const [newChannelLimit, setNewChannelLimit] = useState<number>(50);
  const [isAddingChannel, setIsAddingChannel] = useState(false);
  const [addChannelDialogOpen, setAddChannelDialogOpen] = useState(false);

  // Load channels and videos on component mount
  useEffect(() => {
    if (isActive) {
      loadChannels();
      loadVideos();
    }
  }, [isActive]);

  // Reload videos when selected channel changes
  useEffect(() => {
    if (isActive) {
      loadVideos();
    }
  }, [selectedChannel, isActive]);

  const loadChannels = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/ts/channels', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch channels');
      }

      const data = await response.json();
      setChannels(data);
    } catch (error) {
      console.error('Error loading channels:', error);
      toast({
        title: "Error",
        description: "Failed to load channels",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadVideos = async () => {
    try {
      const url = selectedChannel === 'all'
        ? '/api/ts/videos'
        : `/api/ts/videos?channelId=${selectedChannel}`;

      const response = await fetch(url, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch videos');
      }

      const data = await response.json();
      setVideos(data);
    } catch (error) {
      console.error('Error loading videos:', error);
      toast({
        title: "Error",
        description: "Failed to load videos",
        variant: "destructive",
      });
    }
  };

  const addChannel = async () => {
    if (!newChannelUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter a YouTube channel URL",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsAddingChannel(true);

      const response = await fetch('/api/ts/channels', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          channelUrl: newChannelUrl,
          videoLimit: newChannelLimit
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add channel');
      }

      toast({
        title: "Success",
        description: "Channel added successfully",
      });

      setNewChannelUrl('');
      setNewChannelLimit(50);
      setAddChannelDialogOpen(false);
      loadChannels();
      
    } catch (error) {
      console.error('Error adding channel:', error);
      toast({
        title: "Error",
        description: "Failed to add channel",
        variant: "destructive",
      });
    } finally {
      setIsAddingChannel(false);
    }
  };

  const refreshChannel = async (channelId: string) => {
    try {
      setIsRefreshing(true);

      const response = await fetch(`/api/ts/channels/${channelId}/refresh`, {
        method: 'POST',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to refresh channel');
      }

      toast({
        title: "Success",
        description: "Channel refresh started",
      });

      // Reload videos after refresh
      loadVideos();

    } catch (error) {
      console.error('Error refreshing channel:', error);
      toast({
        title: "Error",
        description: "Failed to refresh channel",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const refreshAllChannels = async () => {
    try {
      setIsRefreshing(true);

      // Refresh all channels sequentially
      for (const channel of channels) {
        await fetch(`/api/ts/channels/${channel.id}/refresh`, {
          method: 'POST',
          credentials: 'include'
        });
      }

      toast({
        title: "Success",
        description: "Global refresh completed",
      });

      // Reload videos after refresh
      loadVideos();

      if (onRefresh) {
        onRefresh(new Date());
      }
      
    } catch (error) {
      console.error('Error refreshing all channels:', error);
      toast({
        title: "Error",
        description: "Failed to refresh channels",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const copyTranscript = async (videoId: string) => {
    try {
      // TODO: Implement transcript copying
      toast({
        title: "Success",
        description: "Transcript copied to clipboard",
      });
    } catch (error) {
      console.error('Error copying transcript:', error);
      toast({
        title: "Error",
        description: "Failed to copy transcript",
        variant: "destructive",
      });
    }
  };

  const openTranscriptFolder = () => {
    // TODO: Implement opening transcript folder
    toast({
      title: "Info",
      description: "Opening transcript folder...",
    });
  };

  const filteredVideos = selectedChannel === 'all' 
    ? videos 
    : videos.filter(video => video.channelId === selectedChannel);

  return (
    <div className="space-y-4">
      {/* Header with controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold">Transcript Manager</h2>
          <Badge variant="outline">{channels.length} channels</Badge>
          <Badge variant="outline">{videos.length} videos</Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={openTranscriptFolder}
            className="flex items-center gap-2"
          >
            <Folder className="h-4 w-4" />
            Open Folder
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={refreshAllChannels}
            disabled={isRefreshing}
            className="flex items-center gap-2"
          >
            {isRefreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh All
          </Button>
          
          <Dialog open={addChannelDialogOpen} onOpenChange={setAddChannelDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Channel
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add YouTube Channel</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Channel URL</label>
                  <Input
                    placeholder="https://www.youtube.com/@channelname"
                    value={newChannelUrl}
                    onChange={(e) => setNewChannelUrl(e.target.value)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Video Limit</label>
                  <Select value={newChannelLimit.toString()} onValueChange={(value) => setNewChannelLimit(parseInt(value))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="20">Last 20 videos</SelectItem>
                      <SelectItem value="50">Last 50 videos</SelectItem>
                      <SelectItem value="100">Last 100 videos</SelectItem>
                      <SelectItem value="200">Last 200 videos</SelectItem>
                      <SelectItem value="300">Last 300 videos</SelectItem>
                      <SelectItem value="500">Last 500 videos</SelectItem>
                      <SelectItem value="0">All videos</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setAddChannelDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={addChannel} disabled={isAddingChannel}>
                    {isAddingChannel ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Add Channel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Channel filter */}
      <div className="flex items-center gap-2">
        <label className="text-sm font-medium">Filter by channel:</label>
        <Select value={selectedChannel} onValueChange={setSelectedChannel}>
          <SelectTrigger className="w-64">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Channels</SelectItem>
            {channels.map((channel) => (
              <SelectItem key={channel.id} value={channel.channelId}>
                {channel.channelTitle}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Main content */}
      <Tabs defaultValue="videos" className="w-full">
        <TabsList>
          <TabsTrigger value="videos">Videos & Transcripts</TabsTrigger>
          <TabsTrigger value="channels">Manage Channels</TabsTrigger>
        </TabsList>

        <TabsContent value="videos" className="space-y-4">
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading videos...</span>
            </div>
          ) : filteredVideos.length > 0 ? (
            <ScrollArea className="h-[calc(100vh-400px)]">
              <div className="grid gap-4">
                {filteredVideos.map((video) => (
                  <Card key={video.id} className="hover:bg-muted/30 transition-colors">
                    <CardContent className="p-4">
                      <div className="flex gap-4">
                        {/* Thumbnail */}
                        <div className="flex-shrink-0">
                          <img
                            src={video.thumbnail}
                            alt={video.title}
                            className="w-32 h-18 object-cover rounded-md cursor-pointer"
                            onClick={() => window.open(`https://www.youtube.com/watch?v=${video.id}`, '_blank')}
                          />
                        </div>
                        
                        {/* Video info */}
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-sm line-clamp-2 mb-1">{video.title}</h3>
                          <p className="text-xs text-muted-foreground mb-2">{video.channelTitle}</p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span>{video.viewCount.toLocaleString()} views</span>
                            <span>{new Date(video.publishedAt).toLocaleDateString()}</span>
                            {video.duration && <span>{video.duration}</span>}
                          </div>
                          
                          {/* Transcript status and actions */}
                          <div className="flex items-center gap-2 mt-3">
                            <Badge variant={video.hasTranscript ? "default" : "secondary"}>
                              {video.hasTranscript ? "Transcript Available" : "No Transcript"}
                            </Badge>
                            
                            {video.hasTranscript && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => copyTranscript(video.id)}
                                className="flex items-center gap-1"
                              >
                                <Copy className="h-3 w-3" />
                                Copy
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <Youtube className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No videos found</p>
              <p className="text-sm mt-2">Add some channels to get started</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="channels" className="space-y-4">
          {channels.length > 0 ? (
            <div className="grid gap-4">
              {channels.map((channel) => (
                <Card key={channel.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {channel.thumbnail && (
                          <img
                            src={channel.thumbnail}
                            alt={channel.channelTitle}
                            className="w-12 h-12 rounded-full"
                          />
                        )}
                        <div>
                          <h3 className="font-medium">{channel.channelTitle}</h3>
                          <p className="text-sm text-muted-foreground">
                            Limit: {channel.videoLimit === 0 ? 'All videos' : `${channel.videoLimit} videos`}
                          </p>
                          {channel.lastRefreshTime && (
                            <p className="text-xs text-muted-foreground">
                              Last refresh: {channel.lastRefreshTime.toLocaleString()}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {channel.videoCount || 0} videos
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => refreshChannel(channel.channelId)}
                          disabled={isRefreshing}
                        >
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <Youtube className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No channels added yet</p>
              <p className="text-sm mt-2">Click "Add Channel" to get started</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
