import Database from 'better-sqlite3';

/**
 * Migration to fix financial amount formatting in the database
 * Converts values like "$2.90 thousand" to "$2,900"
 */
function runMigration() {
  console.log('Running migration: Fixing financial amount formatting');
  
  try {
    // Connect to the database
    const db = new Database('data.db');
    
    // Get all videos with financial amounts that need fixing
    const videos = db.prepare(`
      SELECT id, financial_amount FROM youtube_videos 
      WHERE financial_amount LIKE '%thousand%' 
      OR financial_amount LIKE '%million%' 
      OR financial_amount LIKE '%billion%' 
      OR financial_amount LIKE '%trillion%'
    `).all();
    
    console.log(`Found ${videos.length} videos with financial amounts to fix`);
    
    let updatedCount = 0;
    
    // Prepare the update statement
    const updateStmt = db.prepare(`
      UPDATE youtube_videos SET financial_amount = ? WHERE id = ?
    `);
    
    // Process each video
    db.transaction(() => {
      for (const video of videos) {
        const fixedAmount = convertToNumericFormat(video.financial_amount);
        
        if (fixedAmount !== video.financial_amount) {
          updateStmt.run(fixedAmount, video.id);
          updatedCount++;
        }
      }
    })();
    
    console.log(`Updated ${updatedCount} financial amounts in the database`);
    
    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error fixing financial amounts:', error);
    throw error;
  }
}

/**
 * Convert formatted amount with magnitude words to standard numeric format
 * e.g., "$2.90 thousand" -> "$2,900"
 */
function convertToNumericFormat(formattedAmount: string): string {
  if (!formattedAmount) return formattedAmount;
  
  const lowerAmount = formattedAmount.toLowerCase();
  
  // Extract the numeric part
  const numericMatch = lowerAmount.match(/\$?(\d+(?:\.\d+)?)/);
  if (!numericMatch) return formattedAmount;
  
  const numericValue = parseFloat(numericMatch[1]);
  let finalValue = numericValue;
  
  // Apply multiplier based on magnitude words
  if (lowerAmount.includes('thousand')) {
    finalValue = numericValue * 1000;
  } else if (lowerAmount.includes('million')) {
    finalValue = numericValue * 1000000;
  } else if (lowerAmount.includes('billion')) {
    finalValue = numericValue * 1000000000;
  } else if (lowerAmount.includes('trillion')) {
    finalValue = numericValue * 1000000000000;
  }
  
  // Format with standard numeric format and commas
  return `$${finalValue.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  })}`;
}

// Run the migration
runMigration();
