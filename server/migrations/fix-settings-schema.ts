/**
 * Migration to fix the settings table schema by removing Ollama columns
 * and adding OpenRouter columns if they don't exist
 */

import Database from 'better-sqlite3';
import path from 'path';

async function main() {
  console.log('Starting migration to fix settings schema...');

  try {
    // Connect to the database
    const dbPath = path.resolve(process.cwd(), 'data.db');
    console.log(`Using database at: ${dbPath}`);
    const db = new Database(dbPath);

    // Check if the settings table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'").get();
    if (!tableExists) {
      console.log('Settings table does not exist. No migration needed.');
      return;
    }

    // Get the current schema
    const columnsInfo = db.prepare('PRAGMA table_info(settings)').all();
    console.log(`Found ${columnsInfo.length} columns in settings table`);

    // Check if the ollama_model column exists
    const hasOllamaModel = columnsInfo.some((col: any) => col.name === 'ollama_model');
    const hasOpenRouterModel = columnsInfo.some((col: any) => col.name === 'openrouter_model');

    if (hasOllamaModel) {
      console.log('Found ollama_model column, creating new settings table without it');

      // Create a new settings table without the Ollama columns
      db.exec(`
        CREATE TABLE settings_new (
          user_id INTEGER PRIMARY KEY,
          dark_mode BOOLEAN,
          search_keywords TEXT,
          min_views_per_hour INTEGER DEFAULT 10,
          remove_duplicates BOOLEAN DEFAULT 1,
          exclude_words TEXT DEFAULT '[]',
          auto_refresh_interval INTEGER DEFAULT 0,
          preferred_playback TEXT DEFAULT 'in_app',
          last_refresh_time TEXT DEFAULT NULL,
          parallel_api_calls BOOLEAN DEFAULT 1,
          watched_videos TEXT DEFAULT '[]',
          use_in_app_player BOOLEAN DEFAULT 1,
          preview_sound BOOLEAN DEFAULT 0,
          playback_sound BOOLEAN DEFAULT 1,
          active_keyword_group_id INTEGER DEFAULT NULL,
          default_sort_method TEXT DEFAULT 'popularity',
          default_duration_filter TEXT DEFAULT 'all',
          default_unwatched_filter BOOLEAN DEFAULT 0,
          home_page TEXT DEFAULT 'trendy',
          selected_prompt_id INTEGER,
          auto_analyze_on_refresh BOOLEAN DEFAULT 1,
          openrouter_model TEXT DEFAULT 'google/gemini-2.0-flash-exp:free',
          openrouter_analysis_prompt TEXT,
          FOREIGN KEY (user_id) REFERENCES users(id),
          FOREIGN KEY (active_keyword_group_id) REFERENCES keyword_groups(id)
        );
      `);

      // Copy data from the old table to the new one
      // We need to handle the case where openrouter columns might not exist in the old table
      if (hasOpenRouterModel) {
        db.exec(`
          INSERT INTO settings_new (
            user_id, dark_mode, search_keywords, min_views_per_hour, remove_duplicates,
            exclude_words, auto_refresh_interval, preferred_playback, last_refresh_time,
            parallel_api_calls, watched_videos, use_in_app_player, preview_sound, playback_sound,
            active_keyword_group_id, default_sort_method, default_duration_filter,
            default_unwatched_filter, home_page, selected_prompt_id, auto_analyze_on_refresh,
            openrouter_model, openrouter_analysis_prompt
          )
          SELECT
            user_id, dark_mode, search_keywords, min_views_per_hour, remove_duplicates,
            exclude_words, auto_refresh_interval, preferred_playback, last_refresh_time,
            parallel_api_calls, watched_videos, use_in_app_player, preview_sound, playback_sound,
            active_keyword_group_id, default_sort_method, default_duration_filter,
            default_unwatched_filter, home_page, selected_prompt_id, auto_analyze_on_refresh,
            openrouter_model, openrouter_analysis_prompt
          FROM settings;
        `);
      } else {
        // If openrouter columns don't exist, use default values
        db.exec(`
          INSERT INTO settings_new (
            user_id, dark_mode, search_keywords, min_views_per_hour, remove_duplicates,
            exclude_words, auto_refresh_interval, preferred_playback, last_refresh_time,
            parallel_api_calls, watched_videos, use_in_app_player, preview_sound, playback_sound,
            active_keyword_group_id, default_sort_method, default_duration_filter,
            default_unwatched_filter, home_page, selected_prompt_id, auto_analyze_on_refresh
          )
          SELECT
            user_id, dark_mode, search_keywords, min_views_per_hour, remove_duplicates,
            exclude_words, auto_refresh_interval, preferred_playback, last_refresh_time,
            parallel_api_calls, watched_videos, use_in_app_player, preview_sound, playback_sound,
            active_keyword_group_id, default_sort_method, default_duration_filter,
            default_unwatched_filter, home_page, selected_prompt_id, auto_analyze_on_refresh
          FROM settings;
        `);
      }

      // Drop the old table and rename the new one
      db.exec(`
        DROP TABLE settings;
        ALTER TABLE settings_new RENAME TO settings;
      `);

      console.log('Successfully migrated settings table');
    } else if (!hasOpenRouterModel) {
      console.log('Adding OpenRouter columns to settings table');

      // Add OpenRouter columns if they don't exist
      db.exec(`
        ALTER TABLE settings ADD COLUMN openrouter_model TEXT DEFAULT 'google/gemini-2.0-flash-exp:free';
        ALTER TABLE settings ADD COLUMN openrouter_analysis_prompt TEXT;
      `);

      console.log('Successfully added OpenRouter columns to settings table');
    } else {
      console.log('Settings table schema is already up to date');
    }

    // Verify the new schema
    const newColumnsInfo = db.prepare('PRAGMA table_info(settings)').all();
    console.log(`Settings table now has ${newColumnsInfo.length} columns`);
    console.log('Column names:', newColumnsInfo.map((col: any) => col.name).join(', '));

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
  }
}

main();
