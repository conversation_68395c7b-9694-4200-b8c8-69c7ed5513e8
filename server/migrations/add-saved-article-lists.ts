import Database from 'better-sqlite3';
import { format } from 'date-fns';

// This migration adds tables for saved article lists and saved articles

async function runMigration() {
  console.log('Starting migration to add saved article lists tables...');
  
  // Connect to the database
  const dbPath = process.env.DATABASE_URL || 'data.db';
  console.log(`Using database at: ${dbPath}`);
  const db = new Database(dbPath);
  
  try {
    // Create saved_article_lists table
    db.exec(`
      CREATE TABLE IF NOT EXISTS saved_article_lists (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      );
    `);
    console.log('Created saved_article_lists table');

    // Create saved_articles table
    db.exec(`
      CREATE TABLE IF NOT EXISTS saved_articles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        list_id INTEGER NOT NULL,
        feed_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        link TEXT NOT NULL,
        published_at TEXT NOT NULL,
        content TEXT,
        scraped_content TEXT,
        added_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (list_id) REFERENCES saved_article_lists(id) ON DELETE CASCADE,
        FOREIGN KEY (feed_id) REFERENCES rss_feeds(id) ON DELETE CASCADE
      );
    `);
    console.log('Created saved_articles table');

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    db.close();
  }
}

// Run the migration
runMigration().catch(console.error);
