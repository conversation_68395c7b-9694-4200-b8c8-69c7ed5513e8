import { YoutubeVideo } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Activity, Code, RefreshCw } from "lucide-react";
import { useFinancialAnalysis } from "@/hooks/use-financial-analysis";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSettings } from "@/hooks/use-settings";

interface OllamaAnalysisTabProps {
  video: YoutubeVideo;
  onAnalysisComplete?: () => void;
}

export function OllamaAnalysisTab({ video, onAnalysisComplete }: OllamaAnalysisTabProps) {
  const { analyzeVideoWithOllama } = useFinancialAnalysis();
  const { settings, updateSettings } = useSettings();
  const { toast } = useToast();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentVideo, setCurrentVideo] = useState<YoutubeVideo>(video);
  const [rawData, setRawData] = useState<string>("");
  const [rawInput, setRawInput] = useState<{prompt: string, systemPrompt: string}>({prompt: "", systemPrompt: ""});
  const [analysisError, setAnalysisError] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("formatted");
  const [selectedModel, setSelectedModel] = useState<string>("");

  // Available Ollama models (hardcoded like in settings page)
  const availableModels = [
    { name: 'phi4-mini:latest' },
    { name: 'llama3:8b' },
    { name: 'llama3.2:latest' },
    { name: 'phi4:latest' },
    { name: 'tinyllama:latest' },
    { name: 'deepseek-r1:latest' }
  ];

  // Update currentVideo when the prop changes
  useEffect(() => {
    setCurrentVideo(prevVideo => {
      // Only update if the video ID is the same but other properties have changed
      // This prevents unnecessary re-renders and state resets
      if (prevVideo.id === video.id) {
        console.log('OllamaAnalysisTab: Same video ID, merging with current state');
        return {
          ...prevVideo,
          ...video,
          // Preserve these fields if they exist in the current state but not in the new props
          ollamaPriorityTag: video.ollamaPriorityTag || prevVideo.ollamaPriorityTag,
          ollamaBenefitAmounts: video.ollamaBenefitAmounts || prevVideo.ollamaBenefitAmounts,
          ollamaExpectedArrivalDate: video.ollamaExpectedArrivalDate || prevVideo.ollamaExpectedArrivalDate,
          ollamaEligiblePeople: video.ollamaEligiblePeople || prevVideo.ollamaEligiblePeople,
          ollamaProofOrSource: video.ollamaProofOrSource || prevVideo.ollamaProofOrSource,
          ollamaActionsToClaim: video.ollamaActionsToClaim || prevVideo.ollamaActionsToClaim,
          ollamaReasonForPriority: video.ollamaReasonForPriority || prevVideo.ollamaReasonForPriority,
          ollamaViralPotential: video.ollamaViralPotential || prevVideo.ollamaViralPotential,
          ollamaModelUsed: video.ollamaModelUsed || prevVideo.ollamaModelUsed
        };
      }
      // If it's a different video, replace the state completely
      console.log('OllamaAnalysisTab: New video, replacing state');
      return video;
    });
    console.log('OllamaAnalysisTab: Video updated:', video);
    console.log('OllamaAnalysisTab: Ollama model used:', video.ollamaModelUsed);

    // Set the selected model based on the video's model or the global settings
    if (video.ollamaModelUsed && !video.ollamaModelUsed.includes('Error')) {
      setSelectedModel(video.ollamaModelUsed);
    } else if (settings?.ollamaModel) {
      setSelectedModel(settings.ollamaModel);
    } else {
      setSelectedModel('llama3.2:latest'); // Default fallback
    }
  }, [video, settings]);

  // Check if we have Ollama analysis data
  const hasOllamaAnalysis = (
    (currentVideo.ollamaPriorityTag !== undefined &&
     currentVideo.ollamaPriorityTag !== null &&
     currentVideo.ollamaPriorityTag !== '') ||
    (currentVideo.ollamaModelUsed !== undefined &&
     currentVideo.ollamaModelUsed !== null &&
     currentVideo.ollamaModelUsed !== '') ||
    (currentVideo.ollamaBenefitAmounts !== undefined &&
     currentVideo.ollamaBenefitAmounts !== null &&
     Array.isArray(currentVideo.ollamaBenefitAmounts) &&
     currentVideo.ollamaBenefitAmounts.length > 0) ||
    (currentVideo.hasFinancialAnalysis === true)
  );

  // Log benefit amounts for debugging
  useEffect(() => {
    if (currentVideo.ollamaBenefitAmounts) {
      console.log('OllamaAnalysisTab: Benefit amounts type:', typeof currentVideo.ollamaBenefitAmounts);
      console.log('OllamaAnalysisTab: Is array:', Array.isArray(currentVideo.ollamaBenefitAmounts));
      console.log('OllamaAnalysisTab: Benefit amounts:', currentVideo.ollamaBenefitAmounts);
    }
  }, [currentVideo.ollamaBenefitAmounts]);

  // Check if we have valid analysis data (not just an error state)
  const hasValidAnalysis = (
    hasOllamaAnalysis &&
    currentVideo.ollamaModelUsed &&
    !currentVideo.ollamaModelUsed.includes('Error') &&
    currentVideo.financialScore !== undefined &&
    currentVideo.financialScore !== null
  );

  // Debug log to check if we have Ollama analysis data
  console.log('OllamaAnalysisTab: hasOllamaAnalysis:', hasOllamaAnalysis, {
    ollamaPriorityTag: currentVideo.ollamaPriorityTag,
    financialScore: currentVideo.financialScore,
    ollamaModelUsed: currentVideo.ollamaModelUsed,
    ollamaBenefitAmounts: currentVideo.ollamaBenefitAmounts,
    hasFinancialAnalysis: currentVideo.hasFinancialAnalysis
  });

  // Additional debug log to help diagnose issues
  if (!hasOllamaAnalysis && currentVideo.hasFinancialAnalysis) {
    console.warn('OllamaAnalysisTab: Video has financialAnalysis but not recognized as Ollama analysis:', {
      videoId: currentVideo.id,
      title: currentVideo.title,
      hasFinancialAnalysis: currentVideo.hasFinancialAnalysis,
      financialScore: currentVideo.financialScore,
      financialCategory: currentVideo.financialCategory
    });
  }

  // Persist analysis data in localStorage as a backup
  useEffect(() => {
    if (hasOllamaAnalysis) {
      console.log('OllamaAnalysisTab: Persisting analysis data to localStorage');
      try {
        const analysisData = {
          ollamaPriorityTag: currentVideo.ollamaPriorityTag,
          ollamaBenefitAmounts: currentVideo.ollamaBenefitAmounts,
          ollamaExpectedArrivalDate: currentVideo.ollamaExpectedArrivalDate,
          ollamaEligiblePeople: currentVideo.ollamaEligiblePeople,
          ollamaProofOrSource: currentVideo.ollamaProofOrSource,
          ollamaActionsToClaim: currentVideo.ollamaActionsToClaim,
          ollamaReasonForPriority: currentVideo.ollamaReasonForPriority,
          ollamaViralPotential: currentVideo.ollamaViralPotential,
          ollamaModelUsed: currentVideo.ollamaModelUsed,
          financialScore: currentVideo.financialScore
        };
        localStorage.setItem(`ollama-analysis-${currentVideo.id}`, JSON.stringify(analysisData));
      } catch (error) {
        console.error('OllamaAnalysisTab: Error persisting analysis data to localStorage:', error);
      }
    }
  }, [currentVideo, hasOllamaAnalysis]);

  // Restore analysis data from localStorage if needed
  useEffect(() => {
    if (!hasOllamaAnalysis) {
      console.log('OllamaAnalysisTab: Checking localStorage for analysis data');
      try {
        const savedData = localStorage.getItem(`ollama-analysis-${currentVideo.id}`);
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          console.log('OllamaAnalysisTab: Found saved analysis data:', parsedData);
          setCurrentVideo(prevVideo => ({
            ...prevVideo,
            ...parsedData
          }));
        }
      } catch (error) {
        console.error('OllamaAnalysisTab: Error restoring analysis data from localStorage:', error);
      }
    }
  }, [currentVideo.id, hasOllamaAnalysis]);

  // Fetch raw data when the component mounts or when the video changes
  useEffect(() => {
    if (hasOllamaAnalysis) {
      fetchRawData();
    }
  }, [currentVideo.id, hasOllamaAnalysis]);

  // Fetch raw data from the server
  const fetchRawData = async () => {
    try {
      console.log('OllamaAnalysisTab: Fetching raw data for video:', currentVideo.id);
      const response = await apiRequest("GET", `/api/videos/${currentVideo.id}/raw-analysis`);

      // Check if the response is OK
      if (response.ok) {
        // Try to parse the response as JSON
        try {
          const data = await response.json();
          console.log('OllamaAnalysisTab: Raw data fetched successfully');
          console.log('OllamaAnalysisTab: Model used in raw data:', data.modelUsed);

          // Extract the prompt and system prompt
          if (data.prompt) {
            setRawInput({
              prompt: data.prompt,
              systemPrompt: data.systemPrompt || ''
            });

            // Remove prompt and systemPrompt from the data to avoid duplication
            const { prompt, systemPrompt, ...outputData } = data;
            setRawData(JSON.stringify(outputData, null, 2));
          } else {
            setRawData(JSON.stringify(data, null, 2));
          }

          // Update the current video with data from the raw analysis
          // This ensures the formatted tab shows the correct data
          if (data.benefitInfo) {
            console.log('OllamaAnalysisTab: Updating currentVideo with data from raw analysis');
            console.log('OllamaAnalysisTab: Raw benefit amounts:', data.benefitInfo.benefitAmounts);

            setCurrentVideo(prevVideo => ({
              ...prevVideo,
              ollamaBenefitAmounts: data.benefitInfo.benefitAmounts || [],
              ollamaExpectedArrivalDate: data.benefitInfo.expectedArrivalDate || '',
              ollamaEligiblePeople: data.benefitInfo.eligiblePeople || '',
              ollamaProofOrSource: data.benefitInfo.proofOrSource || '',
              ollamaActionsToClaim: data.benefitInfo.actionsToClaim || '',
              ollamaPriorityTag: data.classification?.priorityTag || prevVideo.ollamaPriorityTag || '',
              ollamaReasonForPriority: data.classification?.reasonForPriority || '',
              ollamaViralPotential: data.classification?.viralPotential || '',
              ollamaModelUsed: data.modelUsed || prevVideo.ollamaModelUsed || ''
            }));
          }

          // Extract error message if present
          if (data.classification && data.classification.reasonForPriority &&
              data.classification.reasonForPriority.includes('Analysis failed:')) {
            const errorMsg = data.classification.reasonForPriority.replace('Analysis failed:', '').trim();
            setAnalysisError(errorMsg);
          } else {
            setAnalysisError("");
          }
        } catch (parseError) {
          // Handle JSON parsing errors
          console.error('Error parsing raw analysis data:', parseError);
          const responseText = await response.text();
          console.error('Raw response text:', responseText.substring(0, 500) + '...');

          setRawData("Error parsing raw analysis data. Server returned invalid JSON.");
          setAnalysisError(`JSON parse error: ${parseError.message}`);
          setRawInput({
            prompt: "Error: Could not load prompt data",
            systemPrompt: "Error: Could not load system prompt data"
          });
        }
      } else {
        // Handle non-OK responses
        console.error('Failed to fetch raw analysis data, status:', response.status);

        try {
          // Try to parse the error response as JSON
          const errorData = await response.json();
          console.error('Error response data:', errorData);
          setRawData(`Failed to fetch raw analysis data: ${JSON.stringify(errorData, null, 2)}`);
          setAnalysisError(errorData.error || errorData.message || "Unknown server error");
        } catch (parseError) {
          // If we can't parse as JSON, get the raw text
          const errorText = await response.text();
          console.error('Error response text:', errorText.substring(0, 500) + '...');
          setRawData(`Failed to fetch raw analysis data: ${response.status}`);
          setAnalysisError(`Server returned status ${response.status}`);
        }

        setRawInput({
          prompt: "Error: Could not load prompt data",
          systemPrompt: "Error: Could not load system prompt data"
        });
      }
    } catch (error) {
      // Handle network or other errors
      console.error('Error fetching raw analysis data:', error);
      setRawData("Error fetching raw analysis data");
      setAnalysisError(error instanceof Error ? error.message : "Unknown error");
      setRawInput({
        prompt: "Error: Could not load prompt data",
        systemPrompt: "Error: Could not load system prompt data"
      });
    }
  };

  // Handle changing the Ollama model
  const handleModelChange = async (model: string) => {
    console.log('OllamaAnalysisTab: Changing model to:', model);
    setSelectedModel(model);

    // Update the global settings with the new model
    if (settings) {
      const updatedSettings = {
        ...settings,
        ollamaModel: model
      };

      try {
        // Update settings in the database
        await fetch('/api/settings', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedSettings),
        });
        console.log('OllamaAnalysisTab: Model setting saved successfully');
      } catch (error) {
        console.error('OllamaAnalysisTab: Error saving model setting:', error);
      }
    }
  };

  // Handle Ollama financial analysis
  const handleAnalyzeWithOllama = async () => {
    console.log('OllamaAnalysisTab: handleAnalyzeWithOllama called for video:', currentVideo.id);
    console.log('OllamaAnalysisTab: Video has transcription:', currentVideo.hasTranscription);
    console.log('OllamaAnalysisTab: Using model:', selectedModel);

    // Check if transcription is available
    if (!currentVideo.hasTranscription) {
      console.log('OllamaAnalysisTab: No transcription available, showing toast');
      toast({
        title: "Transcription Required",
        description: "A transcription is required for Ollama analysis. Please load the transcription first.",
        variant: "destructive",
      });
      return;
    }

    // Start analysis
    console.log('OllamaAnalysisTab: Starting Ollama analysis for video:', currentVideo.id);
    setIsAnalyzing(true);
    try {
      console.log('OllamaAnalysisTab: Calling analyzeVideoWithOllama.mutateAsync with model:', selectedModel);
      const result = await analyzeVideoWithOllama.mutateAsync({
        videoId: currentVideo.id,
        model: selectedModel
      });
      console.log('OllamaAnalysisTab: Ollama analysis result:', result);

      // Fetch the updated video data
      const videoResponse = await apiRequest("GET", `/api/youtube-channels/videos/${currentVideo.id}`);
      if (videoResponse.ok) {
        const updatedVideo = await videoResponse.json();
        console.log('OllamaAnalysisTab: Updated video data:', updatedVideo);
        console.log('OllamaAnalysisTab: Ollama model used in updated video:', updatedVideo.ollamaModelUsed);

        // Create a new object with default values for all Ollama fields
        const updatedVideoWithDefaults = {
          ...updatedVideo,
          // Ensure these fields are present to trigger UI updates
          ollamaPriorityTag: updatedVideo.ollamaPriorityTag || 'Unknown',
          ollamaBenefitAmounts: updatedVideo.ollamaBenefitAmounts || [],
          ollamaExpectedArrivalDate: updatedVideo.ollamaExpectedArrivalDate || '',
          ollamaEligiblePeople: updatedVideo.ollamaEligiblePeople || '',
          ollamaProofOrSource: updatedVideo.ollamaProofOrSource || '',
          ollamaActionsToClaim: updatedVideo.ollamaActionsToClaim || '',
          ollamaReasonForPriority: updatedVideo.ollamaReasonForPriority || '',
          ollamaViralPotential: updatedVideo.ollamaViralPotential || '',
          ollamaModelUsed: updatedVideo.ollamaModelUsed || 'Unknown'
        };

        // Force a refresh of the component with the updated data
        setCurrentVideo(updatedVideoWithDefaults);

        // Call the callback if provided
        if (onAnalysisComplete) {
          onAnalysisComplete();
        }

        // Log the updated state to verify
        console.log('OllamaAnalysisTab: Updated currentVideo state with Ollama data');

        // Invalidate queries to ensure the UI is updated
        queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${currentVideo.id}`] });
        queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${currentVideo.id}/financial-analysis`] });
        queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/all-videos`] });

        // Show a toast notification
        toast({
          title: "Ollama Analysis Complete",
          description: `Analysis completed with score: ${result.analysis?.score || updatedVideo.financialScore || 'N/A'}/100`,
        });
      } else {
        console.error('OllamaAnalysisTab: Failed to fetch updated video data:', videoResponse.status);
        toast({
          title: "Update Failed",
          description: "Analysis completed but failed to update the UI. Please refresh the page.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('OllamaAnalysisTab: Error analyzing with Ollama:', error);
      console.error('OllamaAnalysisTab: Error details:', error instanceof Error ? error.message : 'Unknown error');
      let errorMessage = "Failed to analyze with Ollama LLM. Please make sure Ollama is running and try again.";
      if (error instanceof Error) {
        if (error.message.includes('Ollama server is not running')) {
          errorMessage = "Ollama server is not running. Please start Ollama and try again.";
        } else if (error.message.includes('Ollama error')) {
          errorMessage = error.message;
        }
      }

      // Even if there's an error, we should still update the UI with any partial data
      try {
        // Fetch the video data to see if any analysis was saved
        const videoResponse = await apiRequest("GET", `/api/youtube-channels/videos/${currentVideo.id}`);
        if (videoResponse.ok) {
          const updatedVideo = await videoResponse.json();
          console.log('OllamaAnalysisTab: Video data after error:', updatedVideo);

          // If we have any Ollama data, update the UI
          if (updatedVideo.ollamaModelUsed) {
            setCurrentVideo({
              ...updatedVideo,
              // Ensure these fields are present to trigger UI updates
              ollamaPriorityTag: updatedVideo.ollamaPriorityTag || 'Doubtful',
              ollamaBenefitAmounts: updatedVideo.ollamaBenefitAmounts || [],
              ollamaExpectedArrivalDate: updatedVideo.ollamaExpectedArrivalDate || '',
              ollamaEligiblePeople: updatedVideo.ollamaEligiblePeople || '',
              ollamaProofOrSource: updatedVideo.ollamaProofOrSource || '',
              ollamaActionsToClaim: updatedVideo.ollamaActionsToClaim || '',
              ollamaReasonForPriority: updatedVideo.ollamaReasonForPriority || 'Analysis failed',
              ollamaViralPotential: updatedVideo.ollamaViralPotential || 'Low',
              ollamaModelUsed: updatedVideo.ollamaModelUsed || 'Unknown (Error)'
            });
          }
        }
      } catch (fetchError) {
        console.error('OllamaAnalysisTab: Error fetching video data after analysis error:', fetchError);
      }

      toast({
        title: "Ollama Analysis Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  if (!hasOllamaAnalysis) {
    return (
      <div className="p-4 text-center">
        <p className="text-muted-foreground mb-4">No Ollama LLM analysis available for this video.</p>
        <div className="flex flex-col items-center gap-2">
          <Button
            onClick={handleAnalyzeWithOllama}
            disabled={isAnalyzing}
            className="mx-auto"
          >
            {isAnalyzing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                Analyzing...
              </>
            ) : (
              <>
                <Activity className="h-4 w-4 mr-2" />
                Analyze with Ollama LLM
              </>
            )}
          </Button>

          <Button
            onClick={async () => {
              try {
                const response = await apiRequest('GET', '/api/ollama/test-connection');
                if (response.ok) {
                  const data = await response.json();
                  if (data.models && Array.isArray(data.models) && data.models.length > 0) {
                    alert(`Ollama server is running with models: ${data.models.join(', ')}`);
                  } else {
                    alert('Ollama server is running, but no models were found. Please install at least one model.');
                  }
                } else {
                  let errorMessage = 'Failed to connect to Ollama server. Please make sure Ollama is running.';
                  try {
                    const errorData = await response.json();
                    if (errorData.message) {
                      errorMessage = errorData.message;
                    }
                    if (errorData.error) {
                      errorMessage += '\n\n' + errorData.error;
                    }
                  } catch (parseError) {
                    console.error('Error parsing error response:', parseError);
                  }
                  alert(errorMessage);
                }
              } catch (error) {
                console.error('Error testing Ollama connection:', error);
                alert(`Error testing Ollama connection: ${error.message || 'Unknown error'}\n\nMake sure Ollama is running on your machine at http://localhost:11434`);
              }
            }}
            variant="outline"
            size="sm"
            className="mx-auto mt-2"
          >
            Test Ollama Connection
          </Button>

          <p className="text-sm mt-4">
            Make sure you have Ollama installed and running on your local machine.
          </p>
        </div>
      </div>
    );
  }

  // Get priority tag color
  const getPriorityTagColor = (tag?: string) => {
    if (!tag) return "bg-gray-200 text-gray-800";

    switch (tag) {
      case "Urgent - High Certainty":
        return "bg-green-100 text-green-800 border-green-300";
      case "Anticipated":
        return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "Doubtful":
        return "bg-red-100 text-red-800 border-red-300";
      case "Low Benefit":
        return "bg-gray-100 text-gray-800 border-gray-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  // Helper function to safely parse raw data
  const getParsedRawData = () => {
    if (!rawData) return null;
    try {
      return JSON.parse(rawData);
    } catch (error) {
      console.error('Error parsing raw data:', error);
      return null;
    }
  };

  // Get the parsed raw data
  const parsedRawData = getParsedRawData();

  return (
    <div className="space-y-6 p-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">Benefits Analysis</h2>
          <p className="text-muted-foreground">
            Analyzed using Ollama LLM
            {currentVideo.ollamaModelUsed && !currentVideo.ollamaModelUsed.includes('Error')
              ? ` (Model: ${currentVideo.ollamaModelUsed})`
              : ''}
          </p>
          {parsedRawData?.modelUsed && parsedRawData.modelUsed !== currentVideo.ollamaModelUsed && (
            <p className="text-xs text-amber-600 mt-1">
              Note: Raw analysis shows model: {parsedRawData.modelUsed}
            </p>
          )}
          {currentVideo.ollamaModelUsed && !currentVideo.ollamaModelUsed.includes('Error') && (
            <p className="text-xs text-muted-foreground mt-1">
              Analysis performed using the prompt template configured in Settings → AI Settings
            </p>
          )}
          <div className="text-xs text-muted-foreground mt-1">
            {!currentVideo.ollamaModelUsed && 'Model information not available'}
            {currentVideo.ollamaModelUsed && currentVideo.ollamaModelUsed.includes('Error') && (
              <div className="flex flex-col gap-1">
                <span className="text-red-500">
                  Analysis failed with model: {currentVideo.ollamaModelUsed.replace(' (Error - Analysis Failed)', '')}
                </span>
                <div className="flex gap-2">
                  <Button
                    onClick={handleAnalyzeWithOllama}
                    variant="outline"
                    size="sm"
                    className="mt-1 text-xs"
                    disabled={isAnalyzing}
                  >
                    {isAnalyzing ? 'Retrying...' : 'Retry Analysis'}
                  </Button>
                  <Button
                    onClick={async () => {
                      try {
                        const response = await apiRequest('GET', '/api/ollama/test-connection');
                        if (response.ok) {
                          const data = await response.json();
                          if (data.models && Array.isArray(data.models) && data.models.length > 0) {
                            alert(`Ollama server is running with models: ${data.models.join(', ')}`);
                          } else {
                            alert('Ollama server is running, but no models were found. Please install at least one model.');
                          }
                        } else {
                          let errorMessage = 'Failed to connect to Ollama server. Please make sure Ollama is running.';
                          try {
                            const errorData = await response.json();
                            if (errorData.message) {
                              errorMessage = errorData.message;
                            }
                            if (errorData.error) {
                              errorMessage += '\n\n' + errorData.error;
                            }
                          } catch (parseError) {
                            console.error('Error parsing error response:', parseError);
                          }
                          alert(errorMessage);
                        }
                      } catch (error) {
                        console.error('Error testing Ollama connection:', error);
                        alert(`Error testing Ollama connection: ${error.message || 'Unknown error'}\n\nMake sure Ollama is running on your machine at http://localhost:11434`);
                      }
                    }}
                    variant="outline"
                    size="sm"
                    className="mt-1 text-xs"
                  >
                    Test Ollama Connection
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-2">
          {hasValidAnalysis || parsedRawData?.classification?.score ? (
            <>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Score:</span>
                <Badge variant="outline" className="text-lg font-bold">
                  {currentVideo.financialScore || parsedRawData?.classification?.score || 0}/100
                </Badge>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Priority:</span>
                <Badge
                  variant="outline"
                  className={`${getPriorityTagColor(currentVideo.ollamaPriorityTag || parsedRawData?.classification?.priorityTag)}`}
                >
                  {currentVideo.ollamaPriorityTag || parsedRawData?.classification?.priorityTag || "Unknown"}
                </Badge>
              </div>
            </>
          ) : currentVideo.ollamaModelUsed && currentVideo.ollamaModelUsed.includes('Error') ? (
            <div className="text-red-500 font-medium">
              Analysis failed - no score available
              <div className="text-xs mt-1">
                {analysisError ? (
                  <span>Reason: {analysisError}</span>
                ) : currentVideo.ollamaReasonForPriority && currentVideo.ollamaReasonForPriority.includes('Analysis failed:') ? (
                  <span>{currentVideo.ollamaReasonForPriority.replace('Analysis failed:', 'Reason:')}</span>
                ) : (
                  <span>Check the Raw Output tab for more details</span>
                )}
              </div>
            </div>
          ) : (
            <div className="text-muted-foreground">
              No analysis data available
            </div>
          )}

          <div className="flex flex-col gap-2 mt-2">
            <div className="flex gap-2">
              <Button
                onClick={handleAnalyzeWithOllama}
                disabled={isAnalyzing}
                variant="outline"
                size="sm"
              >
                {isAnalyzing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Activity className="h-4 w-4 mr-2" />
                    Reanalyze
                  </>
                )}
              </Button>
              <Button
                onClick={async () => {
                  try {
                    const response = await apiRequest('GET', '/api/ollama/test-connection');
                    if (response.ok) {
                      const data = await response.json();
                      if (data.models && Array.isArray(data.models) && data.models.length > 0) {
                        alert(`Ollama server is running with models: ${data.models.join(', ')}`);
                      } else {
                        alert('Ollama server is running, but no models were found. Please install at least one model.');
                      }
                    } else {
                      let errorMessage = 'Failed to connect to Ollama server. Please make sure Ollama is running.';
                      try {
                        const errorData = await response.json();
                        if (errorData.message) {
                          errorMessage = errorData.message;
                        }
                        if (errorData.error) {
                          errorMessage += '\n\n' + errorData.error;
                        }
                      } catch (parseError) {
                        console.error('Error parsing error response:', parseError);
                      }
                      alert(errorMessage);
                    }
                  } catch (error) {
                    console.error('Error testing Ollama connection:', error);
                    alert(`Error testing Ollama connection: ${error.message || 'Unknown error'}\n\nMake sure Ollama is running on your machine at http://localhost:11434`);
                  }
                }}
                variant="outline"
                size="sm"
              >
                Test Connection
              </Button>
            </div>

            <div className="flex items-center gap-2 mt-2">
              <span className="text-sm font-medium">Model:</span>
              <Select
                value={selectedModel}
                onValueChange={handleModelChange}
                disabled={isAnalyzing}
              >
                <SelectTrigger className="h-8 w-[180px]">
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  {availableModels.map((model) => (
                    <SelectItem key={model.name} value={model.name}>
                      {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => handleAnalyzeWithOllama()}
                disabled={isAnalyzing}
                title="Analyze with selected model"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="formatted">Formatted</TabsTrigger>
          <TabsTrigger value="raw">Raw Output</TabsTrigger>
          <TabsTrigger value="input">Raw Input</TabsTrigger>
        </TabsList>

        <TabsContent value="formatted">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Benefit Details</CardTitle>
                <CardDescription>Financial benefit information extracted from the video</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Benefit Amounts</h3>
                  {currentVideo.ollamaBenefitAmounts &&
                   Array.isArray(currentVideo.ollamaBenefitAmounts) &&
                   currentVideo.ollamaBenefitAmounts.length > 0 ? (
                    <ul className="mt-1 list-disc list-inside">
                      {currentVideo.ollamaBenefitAmounts.map((amount, index) => (
                        <li key={index} className="text-lg font-semibold">{amount}</li>
                      ))}
                    </ul>
                  ) : parsedRawData?.benefitInfo?.benefitAmounts?.length > 0 ? (
                    <ul className="mt-1 list-disc list-inside">
                      {parsedRawData.benefitInfo.benefitAmounts.map((amount, index) => (
                        <li key={index} className="text-lg font-semibold">{amount}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-muted-foreground italic">No specific amounts mentioned</p>
                  )}
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Expected Arrival Date</h3>
                  <p className="text-lg">
                    {currentVideo.ollamaExpectedArrivalDate ||
                     parsedRawData?.benefitInfo?.expectedArrivalDate ||
                     "Not specified"}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Eligible Recipients</h3>
                  <p className="text-lg">
                    {currentVideo.ollamaEligiblePeople ||
                     parsedRawData?.benefitInfo?.eligiblePeople ||
                     "Not specified"}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Verification & Actions</CardTitle>
                <CardDescription>Source information and steps to claim the benefit</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Proof or Source</h3>
                  <p className="text-lg">
                    {currentVideo.ollamaProofOrSource ||
                     parsedRawData?.benefitInfo?.proofOrSource ||
                     "Not specified"}
                  </p>
                  {parsedRawData?.benefitInfo?.proofOrSource === "" && (
                    <p className="text-xs text-muted-foreground mt-1">
                      (Empty value in raw data)
                    </p>
                  )}
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Actions to Claim</h3>
                  <p className="text-lg">
                    {currentVideo.ollamaActionsToClaim ||
                     parsedRawData?.benefitInfo?.actionsToClaim ||
                     "Not specified"}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Reason for Priority</h3>
                  <p className="text-lg">
                    {currentVideo.ollamaReasonForPriority ||
                     parsedRawData?.classification?.reasonForPriority ||
                     "Not specified"}
                  </p>
                  {parsedRawData?.classification?.reasonForPriority === "" && (
                    <p className="text-xs text-muted-foreground mt-1">
                      (Empty value in raw data)
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Viral Potential</CardTitle>
              <CardDescription>Analysis of the content's viral characteristics</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-lg">
                {currentVideo.ollamaViralPotential ||
                 parsedRawData?.classification?.viralPotential ||
                 "No viral potential analysis available"}
              </p>
            </CardContent>
          </Card>

          <div className="mt-4 text-xs text-muted-foreground">
            <p>Note: This formatted view shows data from both the video object and the raw analysis data.</p>
            <p>Empty fields in the raw data are shown as "Not specified" but may actually be empty strings in the data.</p>
          </div>
        </TabsContent>

        <TabsContent value="raw">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5" />
                Raw Analysis Output
              </CardTitle>
              <CardDescription>The raw JSON data returned from the Ollama LLM</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[500px] text-sm">
                {rawData || "No raw data available. Try refreshing the analysis."}
              </pre>
              <Button
                onClick={fetchRawData}
                variant="outline"
                size="sm"
                className="mt-4"
              >
                Refresh Raw Data
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="input">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5" />
                Raw Prompt Input
              </CardTitle>
              <CardDescription>The prompt sent to the Ollama LLM for analysis</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-sm font-medium mb-2">System Prompt</h3>
                <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[200px] text-sm">
                  {rawInput.systemPrompt || "No system prompt available."}
                </pre>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">Main Prompt (with transcript)</h3>
                <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[500px] text-sm">
                  {rawInput.prompt || "No prompt available. Try refreshing the analysis."}
                </pre>
              </div>

              <Button
                onClick={fetchRawData}
                variant="outline"
                size="sm"
              >
                Refresh Raw Data
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
