# TXT Tab - Script Writing & Transcript Management

## Overview

The TXT tab is a comprehensive script writing and transcript management system integrated into your YouTube application. It provides tools for creating, editing, managing, and analyzing video scripts and transcripts with advanced features like highlighting, ChatGPT integration, and analytics.

## Features

### 1. Manual Script Writing
- **Rich Text Editor**: Create and edit video scripts with a user-friendly interface
- **Video URL Linking**: Associate scripts with YouTube video URLs
- **Color-Coded Highlighting**: Highlight effective segments with 8 different colors
- **Auto-Save**: Automatic saving of changes with timestamps
- **Export Options**: Export scripts in TXT, JSON, and HTML formats

### 2. Existing Transcripts Viewer
- **Multi-Source Integration**: View transcripts from RSS, YTR, Video Feed, and Realtime tabs
- **Search & Filter**: Find transcripts by content, title, or source type
- **Import Functionality**: Import existing transcripts into your script library
- **Source Tracking**: See which tab each transcript originated from

### 3. ChatGPT Integration
- **AI Processing**: Process transcripts with ChatGPT for content improvement
- **Predefined Prompts**: Use built-in prompts for common tasks
- **Custom Prompts**: Create your own prompts for specific needs
- **Processing History**: Track all ChatGPT processing results
- **Export Results**: Copy or download AI-generated content

### 4. Prompts Management
- **Prompt Library**: Manage a collection of reusable prompts
- **Categories**: Organize prompts by type (script-writing, analysis, etc.)
- **Default Prompts**: Pre-built prompts for common video script tasks
- **Import/Export**: Share prompt collections between users

### 5. Analytics Dashboard
- **Usage Statistics**: Track transcript creation and highlighting patterns
- **Color Analysis**: See which highlight colors you use most
- **Source Distribution**: Understand where your transcripts come from
- **Activity Timeline**: View recent changes and updates
- **Insights**: AI-powered recommendations for workflow improvement

## Database Schema

### Tables Created
1. **txt_transcripts**: Store user-created transcripts
2. **txt_highlights**: Store text highlighting data
3. **txt_prompts**: Store ChatGPT prompts

### Key Fields
- `title`: Transcript title
- `content`: Full transcript text
- `video_url`: Associated YouTube video URL
- `source_type`: Origin of transcript (manual, imported, etc.)
- `highlights`: Color-coded text segments
- `prompts`: Reusable ChatGPT prompts

## API Endpoints

### Transcripts
- `GET /api/txt-tab/transcripts` - List all transcripts
- `POST /api/txt-tab/transcripts` - Create new transcript
- `GET /api/txt-tab/transcripts/:id` - Get specific transcript
- `PUT /api/txt-tab/transcripts/:id` - Update transcript
- `DELETE /api/txt-tab/transcripts/:id` - Delete transcript

### Highlights
- `GET /api/txt-tab/transcripts/:id/highlights` - Get transcript highlights
- `POST /api/txt-tab/highlights` - Create new highlight
- `DELETE /api/txt-tab/highlights/:id` - Delete highlight

### Prompts
- `GET /api/txt-tab/prompts` - List all prompts
- `POST /api/txt-tab/prompts` - Create new prompt
- `PUT /api/txt-tab/prompts/:id` - Update prompt
- `DELETE /api/txt-tab/prompts/:id` - Delete prompt

### Utilities
- `GET /api/txt-tab/existing-transcripts` - Get transcripts from other tabs
- `POST /api/txt-tab/import-transcript` - Import transcript from other sources
- `POST /api/txt-tab/chatgpt/process` - Process transcript with ChatGPT
- `POST /api/txt-tab/bulk-import` - Import multiple transcripts
- `GET /api/txt-tab/export` - Export all transcripts

## Default Prompts

The system includes 4 default prompts for new users:

1. **Script Improvement**: Analyze and suggest improvements for better engagement
2. **Content Summarization**: Create concise summaries with key takeaways
3. **Audience Engagement Analysis**: Identify strong and weak engagement points
4. **SEO Title & Description**: Generate SEO-optimized titles and descriptions

## Highlighting System

### Available Colors
- Yellow (#ffff00)
- Green (#00ff00)
- Blue (#00bfff)
- Pink (#ff69b4)
- Orange (#ffa500)
- Purple (#9370db)
- Red (#ff6b6b)
- Cyan (#00ffff)

### Usage
1. Select text in the editor
2. Choose a highlight color
3. Click "Highlight Selected"
4. View highlights in the analytics dashboard

## Export Formats

### TXT Format
Plain text with title and content

### JSON Format
Structured data including:
- Title and content
- Video URL
- Highlights data
- Timestamps

### HTML Format
Formatted HTML with:
- Styled layout
- Highlighted segments
- Metadata footer

## ChatGPT Integration

### Setup Required
The ChatGPT integration requires connection to an external ChatGPT server. The system is designed to work with your existing ChatGPT server project located at:
`/Users/<USER>/Documents/augment-projects/ChatGPT Server`

### Processing Flow
1. Select a transcript
2. Choose a predefined prompt or enter custom prompt
3. Process with ChatGPT
4. Review and export results
5. Track processing history

## Analytics Insights

The analytics dashboard provides:
- **Total Statistics**: Transcript count, highlights, averages
- **Usage Patterns**: Most used colors and sources
- **Recent Activity**: Timeline of changes
- **Recommendations**: AI-powered workflow suggestions

## Best Practices

### Script Writing
1. Use descriptive titles for easy identification
2. Link video URLs for reference
3. Highlight effective segments for future use
4. Export important scripts for backup

### Highlighting Strategy
1. Use consistent colors for similar content types
2. Yellow for key points
3. Green for successful hooks
4. Red for areas needing improvement

### Prompt Management
1. Create specific prompts for your content style
2. Organize prompts by category
3. Test prompts with different transcript types
4. Share successful prompts with team members

## Future Enhancements

Potential improvements for the TXT tab:
1. **Collaboration**: Multi-user editing and sharing
2. **Version Control**: Track script changes over time
3. **Templates**: Pre-built script structures
4. **Performance Analytics**: Correlate highlights with video metrics
5. **Advanced AI**: More sophisticated content analysis
6. **Integration**: Connect with video editing software

## Troubleshooting

### Common Issues
1. **Highlights not saving**: Check transcript is saved first
2. **ChatGPT not responding**: Verify external server connection
3. **Import failing**: Check source transcript format
4. **Export not working**: Verify browser download permissions

### Support
For technical issues or feature requests, check the application logs and ensure all dependencies are properly installed.
