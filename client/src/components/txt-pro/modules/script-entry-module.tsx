import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Save, Plus, X, Edit, Trash2, FileText, Link, Tag, CheckCircle } from 'lucide-react';
import { useWorkflow } from '../workflow-state';

interface Script {
  id: string;
  title: string;
  sourceVideoUrl?: string;
  transcript: string;
  tags: string[];
  dateAdded: Date;
  editHistory?: Array<{ date: Date; changes: string }>;
}

interface ScriptEntryModuleProps {
  scripts: Script[];
  setScripts: React.Dispatch<React.SetStateAction<Script[]>>;
  selectedScript: Script | null;
  setSelectedScript: React.Dispatch<React.SetStateAction<Script | null>>;
  toast: any;
}

const ScriptEntryModule: React.FC<ScriptEntryModuleProps> = ({
  scripts,
  setScripts,
  selectedScript,
  setSelectedScript,
  toast
}) => {
  const { workflowState, updateStepData, markStepComplete } = useWorkflow();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    sourceVideoUrl: '',
    transcript: '',
    tags: [] as string[]
  });
  const [newTag, setNewTag] = useState('');

  // Initialize form when editing existing script
  React.useEffect(() => {
    if (selectedScript && isEditing) {
      setFormData({
        title: selectedScript.title,
        sourceVideoUrl: selectedScript.sourceVideoUrl || '',
        transcript: selectedScript.transcript,
        tags: [...selectedScript.tags]
      });
    }
  }, [selectedScript, isEditing]);

  const handleSaveScript = () => {
    if (!formData.title.trim() || !formData.transcript.trim()) {
      toast({
        title: "Validation Error",
        description: "Title and transcript are required",
        variant: "destructive"
      });
      return;
    }

    const scriptData: Script = {
      id: selectedScript?.id || `script_${Date.now()}`,
      title: formData.title.trim(),
      sourceVideoUrl: formData.sourceVideoUrl.trim() || undefined,
      transcript: formData.transcript.trim(),
      tags: formData.tags,
      dateAdded: selectedScript?.dateAdded || new Date(),
      editHistory: selectedScript?.editHistory || []
    };

    if (selectedScript) {
      // Update existing script
      setScripts(prev => prev.map(script => 
        script.id === selectedScript.id ? {
          ...scriptData,
          editHistory: [
            ...(script.editHistory || []),
            { date: new Date(), changes: 'Script updated' }
          ]
        } : script
      ));
      setSelectedScript(scriptData);
      toast({
        title: "Script Updated",
        description: `"${scriptData.title}" has been updated successfully`
      });
    } else {
      // Create new script
      setScripts(prev => [...prev, scriptData]);
      setSelectedScript(scriptData);
      toast({
        title: "Script Saved",
        description: `"${scriptData.title}" has been saved successfully`
      });
    }

    setIsEditing(false);
    resetForm();
  };

  const handleDeleteScript = () => {
    if (!selectedScript) return;
    
    if (window.confirm(`Are you sure you want to delete "${selectedScript.title}"?`)) {
      setScripts(prev => prev.filter(script => script.id !== selectedScript.id));
      setSelectedScript(null);
      setIsEditing(false);
      resetForm();
      toast({
        title: "Script Deleted",
        description: "Script has been deleted successfully"
      });
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      sourceVideoUrl: '',
      transcript: '',
      tags: []
    });
    setNewTag('');
  };

  const handleNewScript = () => {
    setSelectedScript(null);
    setIsEditing(true);
    resetForm();
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleSelectForWorkflow = (script: Script) => {
    setSelectedScript(script);
    updateStepData('script-selection', { selectedScript: script });
    markStepComplete('script-selection');
    toast({
      title: "Script Selected for Workflow",
      description: `"${script.title}" is now your source script. Proceed to the next step!`,
      duration: 3000
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Script & Transcript Entry
              </CardTitle>
              <CardDescription>
                Store, organize, and manage your video text transcripts with metadata
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleNewScript} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                New Script
              </Button>
              {selectedScript && !isEditing && (
                <>
                  <Button onClick={() => setIsEditing(true)} variant="outline" size="sm">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button onClick={handleDeleteScript} variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {isEditing ? (
            // Edit Form
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Script Title *</Label>
                  <Input
                    id="title"
                    placeholder="e.g., SSA Payment Schedule Update - July 2025"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sourceUrl">Source Video URL (Optional)</Label>
                  <div className="relative">
                    <Link className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="sourceUrl"
                      placeholder="https://youtube.com/watch?v=..."
                      value={formData.sourceVideoUrl}
                      onChange={(e) => setFormData(prev => ({ ...prev, sourceVideoUrl: e.target.value }))}
                      className="pl-8"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="transcript">Text Transcript *</Label>
                <Textarea
                  id="transcript"
                  placeholder="Paste or type your video transcript here..."
                  value={formData.transcript}
                  onChange={(e) => setFormData(prev => ({ ...prev, transcript: e.target.value }))}
                  rows={12}
                  className="min-h-[300px]"
                />
              </div>

              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Tag className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Add tags (e.g., SSA Payment, Stimulus Update)"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={handleKeyPress}
                      className="pl-8"
                    />
                  </div>
                  <Button onClick={handleAddTag} variant="outline" size="sm">
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X 
                        className="h-3 w-3 cursor-pointer hover:text-destructive" 
                        onClick={() => handleRemoveTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <Button onClick={handleSaveScript}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Script
                </Button>
                <Button onClick={() => setIsEditing(false)} variant="outline">
                  Cancel
                </Button>
              </div>
            </div>
          ) : selectedScript ? (
            // View Mode
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Title</Label>
                  <p className="text-sm mt-1">{selectedScript.title}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Date Added</Label>
                  <p className="text-sm mt-1">{new Date(selectedScript.dateAdded).toLocaleDateString()}</p>
                </div>
              </div>

              {selectedScript.sourceVideoUrl && (
                <div>
                  <Label className="text-sm font-medium">Source Video URL</Label>
                  <p className="text-sm mt-1">
                    <a 
                      href={selectedScript.sourceVideoUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {selectedScript.sourceVideoUrl}
                    </a>
                  </p>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium">Tags</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedScript.tags.map(tag => (
                    <Badge key={tag} variant="secondary">{tag}</Badge>
                  ))}
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Transcript</Label>
                <div className="mt-2 p-4 bg-muted/30 rounded-lg max-h-96 overflow-y-auto">
                  <p className="text-sm whitespace-pre-wrap">{selectedScript.transcript}</p>
                </div>
              </div>

              {/* Workflow Integration */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-blue-900">Use in Workflow</h4>
                    <p className="text-sm text-blue-700">
                      Select this script as the source for your new script generation workflow
                    </p>
                  </div>
                  <Button onClick={() => handleSelectForWorkflow(selectedScript)}>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Select for Workflow
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            // No Script Selected
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Script Selected</h3>
              <p className="text-muted-foreground mb-4">
                Select a script from the sidebar or create a new one to get started
              </p>
              <Button onClick={handleNewScript}>
                <Plus className="h-4 w-4 mr-2" />
                Create New Script
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ScriptEntryModule;
