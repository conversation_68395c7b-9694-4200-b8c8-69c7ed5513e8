{"model": "deepseek-r1:latest", "prompt": "You are a financial benefit analyzer specialized in identifying financial benefits mentioned in YouTube video transcripts.\n\nYour task is to extract specific financial benefit information from the provided content and classify it based on certainty and value.\n\nFocus ONLY on financial benefits that viewers might receive (like stimulus checks, tax credits, government payments, Social Security benefits, etc.). Ignore general financial advice or market analysis that doesn't involve direct benefits to individuals.\n\nAnalyze the following YouTube video content:\n\nTITLE: \"Breaking: Trump's Social Security Executive Order\"\n\nDESCRIPTION: \"Today, the president signed an executive order otherwise known as presidential memorandum focused on Social Security and who can receive Social Security benefits going forward as well as who will also be removed from receiving Social Security benefits. This includes all benefits administered by the Social Security administration according to the memorandum. This will impact a few million people who are either currently receiving Social Security benefits or planning on receiving Social Security benefits.\"\n\nTRANSCRIPT:\n[00:01] all right just when we thought it\n[00:02] couldn't possibly get any more weird\n[00:04] with social security it just got even\n[00:06] more weird today based on what the\n[00:08] president just signed which was a\n[00:10] memorandum focused on social security so\n[00:14] I want to share with you the details of\n[00:15] exactly what this all means i'm sure a\n[00:17] lot of us are going to be wondering a\n[00:19] lot of questions around this and this\n[00:21] going to be impacting well a few million\n[00:23] people based on the numbers as of right\n[00:24] now so I want to share with you exactly\n[00:26] what was signed just a short time ago\n[00:28] out of the president again a\n[00:30] presidential memorandum focused on\n[00:32] social security okay does that sound\n[00:35] pretty interesting certainly does well\n[00:36] I'll share with you the details about\n[00:38] that now also I want to share with you\n[00:40] something else that Doge just did the\n[00:42] other day as well that is also impacting\n[00:44] a lot of people and that goes handinhand\n[00:46] with what just happened today uh out of\n[00:49] the presidency so got a lot of big\n[00:51] things here right wow i mean seriously\n[00:53] this is nuts i know I say that literally\n[00:55] every single day pretty much in every\n[00:56] video and pretty much in every single\n[00:58] live stream but um it's just getting\n[00:59] more weird by the day here and there's a\n[01:01] lot of changes around social security\n\nExtract the following information:\n1. All financial benefit amounts mentioned (e.g., $1200, $600)\n2. Type of benefit (Direct Money / Tax Saving / Program Support / Stimulus)\n3. Duration of benefit (One-time / Monthly / Yearly / Temporary)\n4. Who is eligible to receive the benefit\n5. How to apply or what actions to take to receive the benefit\n6. Expected arrival timeline for the benefit (if mentioned)\n7. Source or proof mentioned (Act name, authority, bill, article, etc.)\n\nThen classify the content into one of these categories:\n- \"Urgent_High_Benefit\": Clear proof, official legislation, very likely to be real.\n- \"Anticipated_Benefit\": Strong indication, not yet confirmed but likely.\n- \"Doubtful_Benefit\": Sounds exciting but lacks official proof or reliable source.\n\nAssign a credibility score (0-100):\n- 80-100: Clear date, amount, eligible group, and valid proof (govt source, program)\n- 50-79: Talk of upcoming benefits, not finalized yet, but possibly valid\n- 20-49: Claims unrealistic or have no proof, vague terms like \"could happen soon\"\n- 0-19: Small amount or hard-to-access, low relevance to general users\n\nExplain why this video might go viral (virality reason).\nIf there are any reasons to doubt the information, explain them.\n\nIMPORTANT: Return ONLY a JSON object with the following structure:\n\n{\n  \"title\": \"\",\n  \"video_url\": \"https://youtube.com/watch?v=a8_9TraQrrc\",\n  \"financial_benefits\": [\n    {\n      \"amount\": \"\",\n      \"type\": \"\",\n      \"duration\": \"\",\n      \"eligibility\": \"\",\n      \"how_to_apply\": \"\",\n      \"expected_arrival\": \"\",\n      \"proof_source\": \"\"\n    }\n  ],\n  \"classification\": \"\",\n  \"credibility_score\": 0,\n  \"virality_reason\": \"\",\n  \"reason_for_doubt\": \"\"\n}\n\nIf multiple benefits are mentioned, include each as a separate object in the financial_benefits array.\nIf no financial benefits are mentioned, return an empty array for financial_benefits, a classification of \"Doubtful_Benefit\", and a credibility score of 0.\nDo not include any explanations, notes, or text outside of the JSON structure.", "stream": false, "format": "json"}