#!/bin/bash

# Start the server using the admin settings port
cd "$(dirname "$0")"

# Quick check for node_modules directory
if [ ! -d "node_modules" ]; then
  echo "node_modules directory not found. Installing dependencies..."
  npm install --no-fund

  # Create marker file to track installation time
  mkdir -p node_modules
  echo "$(date)" > node_modules/.dependency-check-marker

  echo "Dependencies installed successfully."
else
  # Check if package.json is newer than our marker file
  MARKER_FILE="node_modules/.dependency-check-marker"

  if [ ! -f "$MARKER_FILE" ] || [ package.json -nt "$MARKER_FILE" ]; then
    echo "Detected changes in package.json. Starting server with dependency check..."
    # Let the application handle dependency checking
  else
    echo "Dependencies appear to be up to date."
  fi
fi

# Start the server (dependency checking will happen in the application)
npm run dev:admin
