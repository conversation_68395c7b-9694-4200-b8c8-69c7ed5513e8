/**
 * Migration script to remove Ollama-related columns from the database
 *
 * This script removes all Ollama-related columns from the youtube_videos and settings tables
 * since they are no longer needed after migrating to OpenRouter.
 */

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

async function runMigration() {
  console.log('Starting migration to remove Ollama columns...');

  // Determine the database path
  const dbPath = path.resolve(process.cwd(), 'data.db');

  // Check if the database file exists
  if (!fs.existsSync(dbPath)) {
    console.error(`Database file not found at ${dbPath}`);
    return;
  }

  try {
    // Connect to the database
    const db = new Database(dbPath);

    // Check if the youtube_videos table exists
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='youtube_videos'").all();
    if (tables.length === 0) {
      console.log('youtube_videos table does not exist. No migration needed.');
      db.close();
      return;
    }

    // Get the list of columns in the youtube_videos table
    const tableInfo = db.prepare('PRAGMA table_info(youtube_videos)').all();

    // Check if the Ollama columns exist
    const ollamaColumns = tableInfo
      .filter(column => column.name.startsWith('ollama_'))
      .map(column => column.name);

    if (ollamaColumns.length === 0) {
      console.log('No Ollama columns found in youtube_videos table. No migration needed for this table.');
    } else {
      console.log(`Found ${ollamaColumns.length} Ollama columns to remove from youtube_videos table: ${ollamaColumns.join(', ')}`);

      // Create a new table without the Ollama columns
      const columnsToKeep = tableInfo
        .filter(column => !column.name.startsWith('ollama_'))
        .map(column => column.name);

      // Create a temporary table with the desired schema
      console.log('Creating temporary table without Ollama columns...');
      db.exec(`
        CREATE TABLE youtube_videos_new (
          ${columnsToKeep.map(col => {
            const colInfo = tableInfo.find(c => c.name === col);
            return `${col} ${colInfo.type}${colInfo.notnull ? ' NOT NULL' : ''}${colInfo.dflt_value ? ` DEFAULT ${colInfo.dflt_value}` : ''}${colInfo.pk ? ' PRIMARY KEY' : ''}`;
          }).join(', ')}
        );
      `);

      // Copy data from the old table to the new table
      console.log('Copying data to new table without Ollama columns...');
      db.exec(`
        INSERT INTO youtube_videos_new
        SELECT ${columnsToKeep.join(', ')}
        FROM youtube_videos;
      `);

      // Drop the old table and rename the new one
      console.log('Replacing old table with new table...');
      db.exec(`
        DROP TABLE youtube_videos;
        ALTER TABLE youtube_videos_new RENAME TO youtube_videos;
      `);

      // Add back the foreign key constraint
      console.log('Adding foreign key constraint...');
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_youtube_videos_user_id ON youtube_videos(user_id);
      `);

      console.log('Successfully removed Ollama columns from youtube_videos table');
    }

    // Now check the settings table
    const settingsInfo = db.prepare('PRAGMA table_info(settings)').all();

    // Check if the Ollama columns exist in settings
    const ollamaSettingsColumns = settingsInfo
      .filter(column => column.name.startsWith('ollama_'))
      .map(column => column.name);

    if (ollamaSettingsColumns.length === 0) {
      console.log('No Ollama columns found in settings table. No migration needed for this table.');
    } else {
      console.log(`Found ${ollamaSettingsColumns.length} Ollama columns to remove from settings table: ${ollamaSettingsColumns.join(', ')}`);

      // Create a new table without the Ollama columns
      const settingsColumnsToKeep = settingsInfo
        .filter(column => !column.name.startsWith('ollama_'))
        .map(column => column.name);

      // Create a temporary table with the desired schema
      console.log('Creating temporary settings table without Ollama columns...');
      db.exec(`
        CREATE TABLE settings_new (
          ${settingsColumnsToKeep.map(col => {
            const colInfo = settingsInfo.find(c => c.name === col);
            return `${col} ${colInfo.type}${colInfo.notnull ? ' NOT NULL' : ''}${colInfo.dflt_value ? ` DEFAULT ${colInfo.dflt_value}` : ''}${colInfo.pk ? ' PRIMARY KEY' : ''}`;
          }).join(', ')}
        );
      `);

      // Copy data from the old table to the new table
      console.log('Copying data to new settings table without Ollama columns...');
      db.exec(`
        INSERT INTO settings_new
        SELECT ${settingsColumnsToKeep.join(', ')}
        FROM settings;
      `);

      // Drop the old table and rename the new one
      console.log('Replacing old settings table with new table...');
      db.exec(`
        DROP TABLE settings;
        ALTER TABLE settings_new RENAME TO settings;
      `);

      // Add back the foreign key constraint
      console.log('Adding foreign key constraint to settings table...');
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_settings_user_id ON settings(user_id);
      `);

      console.log('Successfully removed Ollama columns from settings table');
    }

    console.log('Migration completed successfully');

    // Close the database connection
    db.close();

    // Note: We're not self-deleting the migration script
    // because __filename is not available in ESM modules
    console.log('Migration script completed successfully');

  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
}

// Run the migration
runMigration();
