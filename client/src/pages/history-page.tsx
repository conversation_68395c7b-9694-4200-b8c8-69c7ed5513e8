import { useState, useEffect } from "react";
import { useSettings } from "@/hooks/use-settings";
import { useVideos } from "@/hooks/use-videos";
import { VideoGrid } from "@/components/video/video-grid";
import { Sidebar } from "@/components/navigation/sidebar";
import { Button } from "@/components/ui/button";
import { Trash2, History, Loader2 } from "lucide-react";
import { Video } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import { Toaster } from "@/components/ui/toaster";

export default function HistoryPage() {
  const { settings, updateSettings, isLoading: settingsLoading } = useSettings();
  const { videos, isLoading: videosLoading } = useVideos();
  const { toast } = useToast();
  const [watchedVideos, setWatchedVideos] = useState<Video[]>([]);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Filter videos to only show watched videos
  useEffect(() => {
    if (videos && settings?.watchedVideos) {
      const watched = videos.filter(video => 
        settings.watchedVideos?.includes(video.id)
      );
      setWatchedVideos(watched);
    }
  }, [videos, settings?.watchedVideos]);

  // Handle clearing watch history
  const handleClearHistory = () => {
    if (window.confirm("Are you sure you want to clear your watch history?")) {
      updateSettings.mutate({
        ...settings,
        watchedVideos: []
      });
      toast({
        title: "Watch history cleared",
        description: "Your watch history has been cleared."
      });
    }
  };

  const isLoading = settingsLoading || videosLoading;

  return (
    <div className="flex h-screen overflow-hidden">
      <Toaster />
      <Sidebar />
      
      <main className="flex-1 overflow-auto">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                <History className="h-5 w-5" />
              </Button>
              <h1 className="text-2xl font-bold">Watch History</h1>
            </div>
            
            <Button 
              variant="outline" 
              className="text-destructive hover:bg-destructive/10"
              onClick={handleClearHistory}
              disabled={!settings?.watchedVideos?.length}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear History
            </Button>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : watchedVideos.length > 0 ? (
            <VideoGrid videos={watchedVideos} />
          ) : (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <History className="h-12 w-12 text-muted-foreground mb-4" />
              <h2 className="text-xl font-semibold mb-2">No watch history</h2>
              <p className="text-muted-foreground max-w-md">
                Videos you watch will appear here. Watch videos to start building your history.
              </p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
