import { useState, useRef } from "react";
import { KeywordGroup } from "@shared/schema";
import "./draggable.css";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check, ChevronDown, ChevronUp, Pencil, Plus, RefreshCw, Trash2, X, GripVertical } from "lucide-react";
import { useSettings } from "@/hooks/use-settings";
import { formatRefreshTime } from "@/lib/date-format";

interface DraggableKeywordGroupProps {
  group: KeywordGroup;
  expandedGroups: Record<string, boolean>;
  toggleGroupExpansion: (groupId: string) => void;
  openEditDialog: (group: KeywordGroup) => void;
  openDeleteDialog: (group: KeywordGroup) => void;
  handleRefreshGroup: (groupId: number) => void;
  handleSetActiveGroup: (groupId: number | null) => void;
  openAddKeywordDialog: (groupId: number | null) => void;
  handleRemoveGroupKeyword: (groupId: number, keyword: string) => void;
  refreshGroupVideos: any;
  setActiveGroup: any;
  isDragging: boolean;
  dragHandleProps: any;
}

export function DraggableKeywordGroup({
  group,
  expandedGroups,
  toggleGroupExpansion,
  openEditDialog,
  openDeleteDialog,
  handleRefreshGroup,
  handleSetActiveGroup,
  openAddKeywordDialog,
  handleRemoveGroupKeyword,
  refreshGroupVideos,
  setActiveGroup,
  isDragging,
  dragHandleProps
}: DraggableKeywordGroupProps) {
  const { settings } = useSettings();

  return (
    <Card
      className={`draggable-item ${settings?.activeKeywordGroupId === group.id ? "border-primary" : ""} ${isDragging ? "opacity-50" : ""}`}
    >
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <div {...dragHandleProps} className="cursor-grab p-1 hover:bg-muted rounded-md transition-colors flex items-center justify-center bg-muted/30 h-6 w-6">
              <GripVertical className="h-4 w-4 text-primary" />
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-6 w-6"
              onClick={() => toggleGroupExpansion(group.id.toString())}
            >
              {expandedGroups[group.id.toString()] ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
            <span>{group.name}</span>
          </div>
          {settings?.activeKeywordGroupId === group.id && (
            <Badge variant="outline" className="ml-2">
              <Check className="h-3 w-3 mr-1" />
              Active
            </Badge>
          )}
        </CardTitle>
        <CardDescription className="font-sans">
          Last refreshed: <span className="font-semibold">{formatRefreshTime(group.lastRefreshTime)}</span>
        </CardDescription>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div>
            <div className="flex justify-between items-center mb-1">
              <div className="flex items-center gap-2">
                <h4 className="text-sm font-semibold">Keywords:</h4>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    openAddKeywordDialog(group.id);
                  }}
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
              <Badge variant="outline">{group.keywords.length}</Badge>
            </div>
            {expandedGroups[group.id.toString()] ? (
              <div className="flex flex-wrap gap-2 mt-2">
                {group.keywords.map((keyword) => (
                  <Badge key={keyword} variant="secondary" className="group relative pr-6">
                    {keyword}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveGroupKeyword(group.id, keyword);
                      }}
                      className="absolute right-1 opacity-0 group-hover:opacity-100 hover:text-destructive transition-opacity"
                      aria-label={`Remove keyword ${keyword}`}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">
                {group.keywords.length} keywords
              </div>
            )}
          </div>

          {group.excludeWords && group.excludeWords.length > 0 && (
            <div>
              <div className="flex justify-between items-center mb-1">
                <h4 className="text-sm font-semibold">Exclude Words:</h4>
                <Badge variant="outline">{group.excludeWords.length}</Badge>
              </div>
              {expandedGroups[group.id.toString()] ? (
                <div className="flex flex-wrap gap-2 mt-2">
                  {group.excludeWords.map((word) => (
                    <Badge key={word} variant="outline" className="group relative pr-6">
                      {word}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle remove exclude word
                        }}
                        className="absolute right-1 opacity-0 group-hover:opacity-100 hover:text-destructive transition-opacity"
                        aria-label={`Remove exclude word ${word}`}
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  {group.excludeWords.length} exclude words
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => openEditDialog(group)}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => openDeleteDialog(group)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => handleRefreshGroup(group.id)}
            disabled={refreshGroupVideos.isPending}
          >
            <RefreshCw className={`h-4 w-4 ${refreshGroupVideos.isPending ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        <Button
          variant={settings?.activeKeywordGroupId === group.id ? "default" : "outline"}
          onClick={() => handleSetActiveGroup(group.id)}
          disabled={setActiveGroup.isPending}
        >
          {settings?.activeKeywordGroupId === group.id ? "Active" : "Set Active"}
        </Button>
      </CardFooter>
    </Card>
  );
}
