import Database from 'better-sqlite3';
import path from 'path';

/**
 * This migration ensures the auto_analyze_on_refresh column exists in the settings table
 * and is properly configured to store boolean values.
 */
export async function runMigration() {
  console.log('Starting migration to fix auto_analyze_on_refresh column...');

  try {
    // Connect to the database
    const dbPath = path.resolve(process.cwd(), 'data.db');
    console.log(`Using database at: ${dbPath}`);
    const db = new Database(dbPath);

    // Check if the settings table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'").get();
    if (!tableExists) {
      console.log('Settings table does not exist. No migration needed.');
      db.close();
      return;
    }

    // Get the current schema
    const columnsInfo = db.prepare('PRAGMA table_info(settings)').all();
    console.log(`Found ${columnsInfo.length} columns in settings table`);

    // Check if the auto_analyze_on_refresh column exists
    const autoAnalyzeColumn = columnsInfo.find((col: any) => col.name === 'auto_analyze_on_refresh');
    
    if (!autoAnalyzeColumn) {
      console.log('Adding auto_analyze_on_refresh column to settings table...');
      
      // Add the column
      db.prepare(`ALTER TABLE settings ADD COLUMN auto_analyze_on_refresh BOOLEAN DEFAULT 1`).run();
      console.log('Added auto_analyze_on_refresh column to settings table');
    } else {
      console.log('auto_analyze_on_refresh column already exists');
      
      // Ensure all existing rows have a valid value for auto_analyze_on_refresh
      console.log('Ensuring all rows have a valid value for auto_analyze_on_refresh...');
      db.prepare(`UPDATE settings SET auto_analyze_on_refresh = 1 WHERE auto_analyze_on_refresh IS NULL`).run();
      console.log('Updated any NULL values in auto_analyze_on_refresh column');
    }

    // Create a test query to verify the column works
    const testQuery = db.prepare(`
      SELECT user_id, auto_analyze_on_refresh 
      FROM settings 
      LIMIT 5
    `).all();
    
    console.log('Sample data from settings table:');
    console.log(testQuery);

    // Close the database connection
    db.close();
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
}

// Run the migration when this script is executed directly
runMigration().catch(console.error);
