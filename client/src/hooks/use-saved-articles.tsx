import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { SavedArticleList, SavedArticle, RssFeedItem } from '@shared/schema';
import { useToast } from './use-toast';
import { apiRequest } from '@/lib/queryClient';

export function useSavedArticleLists() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all saved article lists
  const {
    data: lists = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['/api/saved-articles'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/saved-articles');
      return res.json();
    }
  });

  // Create a new saved article list
  const createList = useMutation({
    mutationFn: async ({ name }: { name: string }) => {
      const res = await apiRequest('POST', '/api/saved-articles', { name });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/saved-articles'] });
      toast({
        title: 'List created',
        description: 'Your saved article list has been created successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to create list: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Update a saved article list
  const updateList = useMutation({
    mutationFn: async ({ listId, name }: { listId: number; name: string }) => {
      const res = await apiRequest('PATCH', `/api/saved-articles/${listId}`, { name });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/saved-articles'] });
      toast({
        title: 'List updated',
        description: 'Your saved article list has been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update list: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Delete a saved article list
  const deleteList = useMutation({
    mutationFn: async ({ listId }: { listId: number }) => {
      await apiRequest('DELETE', `/api/saved-articles/${listId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/saved-articles'] });
      toast({
        title: 'List deleted',
        description: 'Your saved article list has been deleted successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to delete list: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  return {
    lists,
    isLoading,
    error,
    refetch,
    createList,
    updateList,
    deleteList,
  };
}

export function useSavedArticles(listId: number | null) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all saved articles in a list
  const {
    data: articles = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: [`/api/saved-articles/${listId}/articles`],
    queryFn: async () => {
      if (!listId) return [];
      const res = await apiRequest('GET', `/api/saved-articles/${listId}/articles`);
      return res.json();
    },
    enabled: !!listId
  });

  // Add an article to a saved list
  const addArticle = useMutation({
    mutationFn: async ({ listId, articleId }: { listId: number; articleId: number }) => {
      const res = await apiRequest('POST', `/api/saved-articles/${listId}/articles`, { articleId });
      return res.json();
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [`/api/saved-articles/${variables.listId}/articles`] });
      queryClient.invalidateQueries({ queryKey: ['/api/saved-articles'] });
      toast({
        title: 'Article saved',
        description: 'The article has been added to your saved list.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to save article: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Remove an article from a saved list
  const removeArticle = useMutation({
    mutationFn: async ({ listId, articleId }: { listId: number; articleId: number }) => {
      await apiRequest('DELETE', `/api/saved-articles/${listId}/articles/${articleId}`);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [`/api/saved-articles/${variables.listId}/articles`] });
      queryClient.invalidateQueries({ queryKey: ['/api/saved-articles'] });
      toast({
        title: 'Article removed',
        description: 'The article has been removed from your saved list.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to remove article: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Generate PDF from saved articles
  const generatePdf = async (listId: number) => {
    try {
      // Create a direct download link to the PDF endpoint
      const downloadUrl = `/api/saved-articles/${listId}/pdf`;
      window.open(downloadUrl, '_blank');

      return true;
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate PDF',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Generate text from saved articles
  const generateText = async (listId: number) => {
    try {
      const res = await apiRequest('GET', `/api/saved-articles/${listId}/text`);
      const data = await res.json();
      return data.text;
    } catch (error) {
      console.error('Error generating text:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate text',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Export articles from a list
  const exportArticles = async (listId: number) => {
    try {
      // Create a direct download link to the export endpoint
      const downloadUrl = `/api/saved-articles/${listId}/export`;
      window.open(downloadUrl, '_blank');

      toast({
        title: 'Export started',
        description: 'Your articles are being exported as a JSON file.',
      });

      return true;
    } catch (error) {
      console.error('Error exporting articles:', error);
      toast({
        title: 'Error',
        description: 'Failed to export articles',
        variant: 'destructive',
      });
      return false;
    }
  };

  // Import articles to a list
  const importArticles = useMutation({
    mutationFn: async ({ listId, importData }: { listId: number; importData: any }) => {
      const res = await apiRequest('POST', `/api/saved-articles/${listId}/import`, importData);

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || 'Failed to import articles');
      }

      return res.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: [`/api/saved-articles/${variables.listId}/articles`] });
      queryClient.invalidateQueries({ queryKey: ['/api/saved-articles'] });
      toast({
        title: 'Import successful',
        description: `Imported ${data.imported} articles. ${data.failed > 0 ? `Failed to import ${data.failed} articles.` : ''}`,
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to import articles: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  return {
    articles,
    isLoading,
    error,
    refetch,
    addArticle,
    removeArticle,
    generatePdf,
    generateText,
    exportArticles,
    importArticles,
  };
}
