import { DollarSign } from "lucide-react";

interface FinancialBenefitProps {
  openRouterBenefitAmounts?: string[] | null;
  financialAmount?: string;
  financialScore?: number;
  openRouterRawData?: string;
}

export function FinancialBenefit({ openRouterBenefitAmounts, financialAmount, financialScore, openRouterRawData }: FinancialBenefitProps) {
  // This component has been renamed to "Benefits" in the UI
  // Debug logging - import logger dynamically to avoid circular dependencies
  import('@/lib/logger').then(({ logger }) => {
    logger.verbose('FinancialBenefit: financialScore:', financialScore);
    logger.verbose('FinancialBenefit: openRouterBenefitAmounts:', openRouterBenefitAmounts);
    logger.verbose('FinancialBenefit: financialAmount:', financialAmount);
    logger.verbose('FinancialBenefit: openRouterRawData available:', !!openRouterRawData);
  });

  // Determine the display value - directly use the benefit amounts without any processing
  let displayValue = 'N/A';
  let hasBenefitData = false;

  // Try to extract benefit amounts from openRouterRawData
  if (openRouterRawData) {
    try {
      const parsedData = JSON.parse(openRouterRawData);
      if (parsedData.extractedInfo?.benefitAmounts && Array.isArray(parsedData.extractedInfo.benefitAmounts) && parsedData.extractedInfo.benefitAmounts.length > 0) {
        displayValue = parsedData.extractedInfo.benefitAmounts.join(', ');
        hasBenefitData = true;
        import('@/lib/logger').then(({ logger }) => {
          logger.verbose('Using benefit amounts from openRouterRawData:', displayValue);
        });
      }
    } catch (error) {
      import('@/lib/logger').then(({ logger }) => {
        logger.error('Error parsing openRouterRawData:', error);
      });
    }
  }

  // If we still don't have benefit amounts, try the other sources
  if (!hasBenefitData) {
    // First try to use openRouterBenefitAmounts if it's a valid array with values
    if (openRouterBenefitAmounts && Array.isArray(openRouterBenefitAmounts) && openRouterBenefitAmounts.length > 0) {
      // Join all benefit amounts with commas
      displayValue = openRouterBenefitAmounts.join(', ');
      hasBenefitData = true;
      import('@/lib/logger').then(({ logger }) => {
        logger.verbose('Using openRouterBenefitAmounts:', displayValue);
      });
    }

    // Finally fall back to financialAmount if it exists
    else if (financialAmount && financialAmount.trim() !== '') {
      // If we have a financial amount but no benefit amounts, we should still show the financial amount
      // This is the most common case for batch analysis
      displayValue = financialAmount;
      hasBenefitData = true;
      import('@/lib/logger').then(({ logger }) => {
        logger.verbose('Using financialAmount:', displayValue);
      });
    }
  }

  // If we still have N/A, check if we have any data at all
  if (!hasBenefitData) {
    import('@/lib/logger').then(({ logger }) => {
      logger.verbose('No benefit amounts found, showing N/A');
    });
  }

  // Format the score for display - only show a score if we have actual benefit data
  const formattedScore = hasBenefitData && financialScore !== undefined ? `${Math.round(financialScore)}` : 'N/A';

  // Determine the color for the score based on its value
  const getScoreColor = (score?: number, hasData: boolean = true) => {
    if (!hasData || score === undefined) return '#888888';

    if (score >= 80) return '#22c55e'; // Green
    if (score >= 60) return '#84cc16'; // Lime
    if (score >= 40) return '#eab308'; // Yellow
    if (score >= 20) return '#f97316'; // Orange
    return '#ef4444'; // Red
  };

  const scoreColor = getScoreColor(financialScore, hasBenefitData);

  // Apply styles directly based on current mode
  const containerStyle = {
    backgroundColor: '#1e1e1e',
    borderColor: '#333333',
    padding: '0.5rem',
    marginTop: '0.5rem',
    borderRadius: '0.375rem',
    borderWidth: '1px',
    borderStyle: 'solid',
  };

  const iconStyle = {
    color: '#60a5fa',
    height: '1rem',
    width: '1rem',
  };

  const textStyle = {
    color: '#ffffff',
    fontSize: '0.875rem',
    fontWeight: 500,
  };

  return (
    <div style={containerStyle}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <DollarSign style={iconStyle} />
          <span style={textStyle}>
            Benefits: {displayValue}
          </span>
        </div>
        <div
          style={{
            backgroundColor: scoreColor,
            color: scoreColor === '#ef4444' || scoreColor === '#f97316' ? 'white' : 'black',
            padding: '0.15rem 0.4rem',
            borderRadius: '0.25rem',
            fontSize: '0.75rem',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {formattedScore}
        </div>
      </div>
    </div>
  );
}
