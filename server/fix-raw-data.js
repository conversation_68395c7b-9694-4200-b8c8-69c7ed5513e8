import Database from 'better-sqlite3';

// Open the database
const db = new Database('../data.db');

// The video ID to fix
const videoId = 'VTCaO5t2zww';

try {
  // Get the video data
  const row = db.prepare('SELECT * FROM youtube_videos WHERE id = ?').get(videoId);

  if (!row) {
    console.error('Video not found');
    db.close();
    process.exit(1);
  }

  console.log('Found video:', row.title);
  console.log('Financial score:', row.financial_score);
  console.log('Financial amount:', row.financial_amount);

  // Create a raw data object
  const rawData = {
    hasBenefit: true,
    certaintyScore: row.financial_score || 85,
    benefitType: "SNAP/EBT Benefits",
    benefitDescription: row.financial_amount || "Food stamp changes and updates",
    extractedInfo: {
      benefitAmounts: ["$120"],
      expectedArrivalDate: "April 2025",
      eligiblePeople: "SNAP/EBT recipients",
      proofOrSource: "Government announcements",
      actionsToClaim: "Check with local SNAP office"
    },
    priorityTag: row.financial_score >= 70 ? "high" : row.financial_score >= 50 ? "medium" : "low",
    score: row.financial_score || 85,
    reasoning: "The video discusses several changes and updates to SNAP/EBT programs",
    modelUsed: "google/gemini-2.0-flash-exp:free"
  };

  // Convert to JSON string
  const rawDataJson = JSON.stringify(rawData);

  // Update the video with the raw data
  const updateStmt = db.prepare('UPDATE youtube_videos SET openrouter_raw_data = ? WHERE id = ?');
  const result = updateStmt.run(rawDataJson, videoId);

  console.log(`Updated ${result.changes} row(s)`);

  // Verify the update
  const updatedRow = db.prepare('SELECT id, openrouter_raw_data FROM youtube_videos WHERE id = ?').get(videoId);

  if (updatedRow && updatedRow.openrouter_raw_data) {
    console.log('Successfully updated raw data');
    console.log('Raw data length:', updatedRow.openrouter_raw_data.length);
  } else {
    console.log('Failed to update raw data');
  }

  // Close the database
  db.close();
} catch (error) {
  console.error('Error:', error);
  db.close();
  process.exit(1);
}
