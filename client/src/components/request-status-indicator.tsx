import { useEffect } from 'react';
import { useRequestStatus, RequestStatus } from '@/lib/request-status';
import { Loader2, CheckCircle, XCircle, AlertCircle, Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface RequestStatusIndicatorProps {
  requestId?: string;
  onCompleted?: (result: any) => void;
  onFailed?: (error: string) => void;
  className?: string;
  showCancel?: boolean;
}

export function RequestStatusIndicator({
  requestId,
  onCompleted,
  onFailed,
  className,
  showCancel = true
}: RequestStatusIndicatorProps) {
  const { request, loading, error, completed, cancelRequest } = useRequestStatus(requestId);
  const { toast } = useToast();

  // Call onCompleted when request is completed
  useEffect(() => {
    if (request?.status === RequestStatus.COMPLETED && onCompleted) {
      onCompleted(request.result);
    }
  }, [request, onCompleted]);

  // Call onFailed when request fails
  useEffect(() => {
    if (
      (request?.status === RequestStatus.FAILED || 
       request?.status === RequestStatus.CANCELLED || 
       request?.status === RequestStatus.TIMEOUT) && 
      onFailed
    ) {
      onFailed(request.error || 'Request failed');
    }
  }, [request, onFailed]);

  // Show error toast if there's an error checking status
  useEffect(() => {
    if (error) {
      toast({
        title: 'Error checking request status',
        description: error,
        variant: 'destructive'
      });
    }
  }, [error, toast]);

  if (!requestId) return null;

  // If loading and no request yet, show loading spinner
  if (loading && !request) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm">Checking request status...</span>
      </div>
    );
  }

  // If no request found, show error
  if (!request) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <AlertCircle className="h-4 w-4 text-destructive" />
        <span className="text-sm text-destructive">Request not found</span>
      </div>
    );
  }

  // Render based on request status
  return (
    <div className={cn("flex items-center gap-2", className)}>
      {request.status === RequestStatus.PENDING && (
        <>
          <Clock className="h-4 w-4 text-muted-foreground" />
          <Badge variant="outline" className="text-xs">Pending</Badge>
          {showCancel && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={cancelRequest}
              disabled={loading}
              className="h-6 px-2 text-xs"
            >
              Cancel
            </Button>
          )}
        </>
      )}
      
      {request.status === RequestStatus.PROCESSING && (
        <>
          <Loader2 className="h-4 w-4 animate-spin text-primary" />
          <Badge variant="secondary" className="text-xs">Processing</Badge>
          {showCancel && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={cancelRequest}
              disabled={loading}
              className="h-6 px-2 text-xs"
            >
              Cancel
            </Button>
          )}
        </>
      )}
      
      {request.status === RequestStatus.COMPLETED && (
        <>
          <CheckCircle className="h-4 w-4 text-green-500" />
          <Badge variant="success" className="bg-green-500 text-white text-xs">Completed</Badge>
        </>
      )}
      
      {request.status === RequestStatus.FAILED && (
        <>
          <XCircle className="h-4 w-4 text-destructive" />
          <Badge variant="destructive" className="text-xs">Failed</Badge>
          <span className="text-xs text-destructive">{request.error}</span>
        </>
      )}
      
      {request.status === RequestStatus.CANCELLED && (
        <>
          <AlertCircle className="h-4 w-4 text-amber-500" />
          <Badge variant="outline" className="text-xs border-amber-500 text-amber-500">Cancelled</Badge>
        </>
      )}
      
      {request.status === RequestStatus.TIMEOUT && (
        <>
          <Clock className="h-4 w-4 text-amber-500" />
          <Badge variant="outline" className="text-xs border-amber-500 text-amber-500">Timeout</Badge>
        </>
      )}
    </div>
  );
}
