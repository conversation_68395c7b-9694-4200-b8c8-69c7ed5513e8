import React from 'react';

interface PeakTimingEntry {
  timestamp: number;
  dayOfWeek: string;
  hour: number;
  viewCount: number;
  marketShare: number;
  isPeak: boolean;
}

interface PeakTimingAnalysisProps {
  channelName: string;
  currentViews: number;
  currentMarketShare: number;
}

export const PeakTimingAnalysis = React.memo(function PeakTimingAnalysis({
  channelName,
  currentViews,
  currentMarketShare
}: PeakTimingAnalysisProps) {
  const [selectedWeekday, setSelectedWeekday] = React.useState<string>('Sunday');
  const now = Date.now();

  // Function to save peak timing data
  const savePeakTimingData = React.useCallback(() => {
    const storageKey = 'peak_timing_history';
    const currentHour = new Date(now).getHours();
    const currentDayOfWeek = new Date(now).toLocaleDateString('en-US', { weekday: 'long' });
    
    const currentEntry: PeakTimingEntry = {
      timestamp: now,
      dayOfWeek: currentDayOfWeek,
      hour: currentHour,
      viewCount: currentViews,
      marketShare: currentMarketShare,
      isPeak: false // Will be calculated later
    };

    try {
      const existingHistory = JSON.parse(localStorage.getItem(storageKey) || '[]');
      
      // Check if we already have data for this hour today
      const oneHourAgo = now - (60 * 60 * 1000);
      const hasRecentEntry = existingHistory.some((entry: PeakTimingEntry) => 
        entry.timestamp > oneHourAgo && 
        entry.dayOfWeek === currentDayOfWeek &&
        entry.hour === currentHour
      );

      if (!hasRecentEntry && currentViews > 0) {
        // Add new entry
        const updatedHistory = [...existingHistory, currentEntry];
        
        // Keep only last 14 days of data
        const fourteenDaysAgo = now - (14 * 24 * 60 * 60 * 1000);
        const filteredHistory = updatedHistory.filter((entry: PeakTimingEntry) => 
          entry.timestamp > fourteenDaysAgo
        );
        
        // Calculate peaks for each day of the week
        const historyWithPeaks = calculatePeaks(filteredHistory);
        
        localStorage.setItem(storageKey, JSON.stringify(historyWithPeaks));
        console.log('Peak timing data saved:', currentEntry);
      }
    } catch (error) {
      console.error('Error saving peak timing data:', error);
    }
  }, [currentViews, currentMarketShare, now]);

  // Calculate peak times for each day
  const calculatePeaks = (history: PeakTimingEntry[]): PeakTimingEntry[] => {
    const dayGroups = history.reduce((groups, entry) => {
      if (!groups[entry.dayOfWeek]) {
        groups[entry.dayOfWeek] = [];
      }
      groups[entry.dayOfWeek].push(entry);
      return groups;
    }, {} as Record<string, PeakTimingEntry[]>);

    // For each day, find the top 3 peak hours
    Object.keys(dayGroups).forEach(dayOfWeek => {
      const dayEntries = dayGroups[dayOfWeek];
      
      // Group by hour and calculate average views
      const hourlyAverages = dayEntries.reduce((hourGroups, entry) => {
        if (!hourGroups[entry.hour]) {
          hourGroups[entry.hour] = [];
        }
        hourGroups[entry.hour].push(entry.viewCount);
        return hourGroups;
      }, {} as Record<number, number[]>);

      // Calculate average for each hour
      const hourlyAvgs = Object.entries(hourlyAverages).map(([hour, views]) => ({
        hour: parseInt(hour),
        avgViews: views.reduce((sum, v) => sum + v, 0) / views.length
      }));

      // Sort by average views and mark top 3 as peaks
      const sortedHours = hourlyAvgs.sort((a, b) => b.avgViews - a.avgViews);
      const peakHours = new Set(sortedHours.slice(0, 3).map(h => h.hour));

      // Update entries to mark peaks
      dayEntries.forEach(entry => {
        entry.isPeak = peakHours.has(entry.hour);
      });
    });

    return history;
  };

  // Save data when component mounts or data changes
  React.useEffect(() => {
    if (currentViews > 0) {
      savePeakTimingData();
    }
  }, [savePeakTimingData]);

  // Load and process peak timing data
  const peakTimingData = React.useMemo(() => {
    const storageKey = 'peak_timing_history';
    let savedHistory: PeakTimingEntry[] = [];
    
    try {
      savedHistory = JSON.parse(localStorage.getItem(storageKey) || '[]');
    } catch (error) {
      console.error('Error loading peak timing history:', error);
    }

    // Group by day of week
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const groupedData = weekdays.reduce((groups, day) => {
      groups[day] = savedHistory.filter(entry => entry.dayOfWeek === day);
      return groups;
    }, {} as Record<string, PeakTimingEntry[]>);

    // Calculate peak hours for each day
    const peakAnalysis = weekdays.map(day => {
      const dayEntries = groupedData[day];
      
      if (dayEntries.length === 0) {
        return {
          dayOfWeek: day,
          peakHours: [],
          totalEntries: 0,
          avgViews: 0,
          avgMarketShare: 0
        };
      }

      // Group by hour
      const hourlyData = dayEntries.reduce((hourGroups, entry) => {
        if (!hourGroups[entry.hour]) {
          hourGroups[entry.hour] = [];
        }
        hourGroups[entry.hour].push(entry);
        return hourGroups;
      }, {} as Record<number, PeakTimingEntry[]>);

      // Calculate averages for each hour
      const hourlyStats = Object.entries(hourlyData).map(([hour, entries]) => ({
        hour: parseInt(hour),
        avgViews: entries.reduce((sum, e) => sum + e.viewCount, 0) / entries.length,
        avgMarketShare: entries.reduce((sum, e) => sum + e.marketShare, 0) / entries.length,
        entryCount: entries.length,
        isPeak: entries.some(e => e.isPeak)
      }));

      // Sort by average views to find peaks
      const sortedHours = hourlyStats.sort((a, b) => b.avgViews - a.avgViews);
      const peakHours = sortedHours.slice(0, 3);

      return {
        dayOfWeek: day,
        peakHours,
        totalEntries: dayEntries.length,
        avgViews: dayEntries.reduce((sum, e) => sum + e.viewCount, 0) / dayEntries.length,
        avgMarketShare: dayEntries.reduce((sum, e) => sum + e.marketShare, 0) / dayEntries.length
      };
    });

    return peakAnalysis;
  }, []);

  // Get data for selected weekday
  const selectedDayData = peakTimingData.find(day => day.dayOfWeek === selectedWeekday);

  // Format hour for display
  const formatHour = (hour: number) => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:00 ${period}`;
  };

  return (
    <div className="space-y-3">
      {/* Weekday Selector */}
      <div className="flex flex-wrap gap-1 justify-center">
        {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map((day) => (
          <button
            key={day}
            onClick={() => setSelectedWeekday(day)}
            className={`px-2 py-1 text-xs rounded transition-all duration-200 ${
              selectedWeekday === day
                ? 'bg-blue-500 text-white font-bold'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            {day.slice(0, 3)}
          </button>
        ))}
      </div>

      {/* Selected Day Peak Analysis */}
      {selectedDayData && (
        <div className="bg-gray-800 rounded p-3">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-bold text-blue-400">{selectedWeekday} Peak Analysis</h4>
            <span className="text-xs text-gray-400">
              {selectedDayData.totalEntries} data points
            </span>
          </div>

          {selectedDayData.peakHours.length > 0 ? (
            <div className="space-y-2">
              {/* Peak Hours */}
              <div>
                <div className="text-xs text-blue-300 mb-1">Peak Hours (Top 3):</div>
                <div className="grid grid-cols-3 gap-2">
                  {selectedDayData.peakHours.map((peak, index) => (
                    <div
                      key={peak.hour}
                      className={`p-2 rounded text-center ${
                        index === 0 ? 'bg-yellow-500/20 border border-yellow-500/40' :
                        index === 1 ? 'bg-orange-500/20 border border-orange-500/40' :
                        'bg-red-500/20 border border-red-500/40'
                      }`}
                    >
                      <div className="text-xs font-bold">
                        #{index + 1} Peak
                      </div>
                      <div className="text-sm font-bold">
                        {formatHour(peak.hour)}
                      </div>
                      <div className="text-xs text-gray-300">
                        {Math.round(peak.avgViews)} views
                      </div>
                      <div className="text-xs text-cyan-400">
                        {peak.avgMarketShare.toFixed(1)}%
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Day Summary */}
              <div className="border-t border-gray-600 pt-2">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="text-center">
                    <div className="text-gray-400">Avg Views</div>
                    <div className="font-bold text-green-400">
                      {Math.round(selectedDayData.avgViews)}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-gray-400">Avg Market Share</div>
                    <div className="font-bold text-cyan-400">
                      {selectedDayData.avgMarketShare.toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-400 py-4">
              <div className="text-sm">No peak data available for {selectedWeekday}</div>
              <div className="text-xs mt-1">Data will be collected over the next 14 days</div>
            </div>
          )}
        </div>
      )}

      {/* Weekly Overview */}
      <div className="bg-gray-800 rounded p-2">
        <div className="text-xs text-blue-300 mb-2">Weekly Peak Overview:</div>
        <div className="grid grid-cols-7 gap-1">
          {peakTimingData.map((day) => (
            <div
              key={day.dayOfWeek}
              className={`p-1 rounded text-center cursor-pointer transition-colors ${
                selectedWeekday === day.dayOfWeek
                  ? 'bg-blue-500/30 border border-blue-500/50'
                  : 'bg-gray-700 hover:bg-gray-600'
              }`}
              onClick={() => setSelectedWeekday(day.dayOfWeek)}
            >
              <div className="text-xs font-medium">
                {day.dayOfWeek.slice(0, 3)}
              </div>
              <div className="text-xs text-gray-300">
                {day.totalEntries > 0 ? `${day.totalEntries}` : '-'}
              </div>
              {day.peakHours.length > 0 && (
                <div className="text-xs text-yellow-400">
                  {formatHour(day.peakHours[0].hour).split(' ')[0]}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});
