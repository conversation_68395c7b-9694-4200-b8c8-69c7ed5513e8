.user-management-container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.page-title {
    color: #333;
    margin-bottom: 2rem;
}

.user-table {
    width: 100%;
    border-collapse: collapse;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-table th, .user-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.user-table th {
    background-color: #f5f5f5;
    font-weight: 600;
}

.btn-delete {
    background-color: #ff4444;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-delete:hover:not(:disabled) {
    background-color: #cc0000;
}

.confirmation-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-confirm {
    background-color: #ff4444;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.btn-cancel {
    background-color: #777;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.loading {
    text-align: center;
    padding: 2rem;
    font-size: 1.2rem;
    color: #666;
}

.error {
    color: #ff4444;
    text-align: center;
    padding: 2rem;
    font-size: 1.2rem;
}

.unauthorized {
    color: #ff4444;
    text-align: center;
    padding: 2rem;
    font-size: 1.2rem;
}

