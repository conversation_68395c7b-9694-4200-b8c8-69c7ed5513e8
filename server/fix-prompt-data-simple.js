const Database = require('better-sqlite3');

// Open the database
const db = new Database('../data.db');

// The video ID to fix
const videoId = 'VTCaO5t2zww';

try {
  // Get the video data
  const video = db.prepare('SELECT * FROM youtube_videos WHERE id = ?').get(videoId);

  if (!video) {
    console.error('Video not found');
    db.close();
    process.exit(1);
  }

  console.log('Found video:', video.title);
  console.log('Financial score:', video.financial_score);
  console.log('Financial amount:', video.financial_amount);
  console.log('Has transcription:', <PERSON><PERSON><PERSON>(video.has_transcription));
  console.log('Transcription length:', video.transcription ? video.transcription.length : 0);

  // Create a simple prompt for the video
  const prompt = `You are a financial analyst AI. Your task is to analyze YouTube video transcripts and extract structured data about financial benefits mentioned in the video. Focus on whether these benefits are real, anticipated, or doubtful, and assign priority and credibility scores accordingly. Return the final output as a structured JSON.

**GOAL:** Help the audience get the **maximum real financial benefit** by filtering, summarizing, scoring, and verifying the claims.

Here is what you should extract:

1. Benefit Type: What kind of financial benefit is mentioned (stimulus check, tax credit, etc.)
2. Benefit Description: A brief summary of the benefit
3. Extracted Information:
   - Benefit Amounts: Dollar amounts mentioned
   - Expected Arrival Date: When the benefit is expected to arrive
   - Eligible People: Who qualifies for the benefit
   - Proof or Source: What evidence supports this benefit's existence
   - Actions to Claim: What steps people need to take to get the benefit

**VIDEO INFORMATION:**
TITLE: "${video.title}"
DESCRIPTION: "${video.description || 'Not provided'}"

**TRANSCRIPT:**
${video.transcription ? video.transcription.substring(0, 1000) + '...' : 'No transcription available'}`;

  console.log('Generated prompt (first 100 chars):', prompt.substring(0, 100) + '...');

  // Get the existing raw data
  let rawData;
  try {
    rawData = JSON.parse(video.openrouter_raw_data || '{}');
    console.log('Parsed existing raw data');
  } catch (error) {
    console.error('Error parsing existing raw data, creating new object');
    rawData = {};
  }

  // Add the prompt and system prompt to the raw data
  rawData.prompt = prompt;
  rawData.systemPrompt = 'You are a financial benefit analyzer specialized in identifying financial benefits mentioned in YouTube video transcripts.\nYour task is to extract specific financial benefit information and classify the content based on certainty and value.\nFocus only on financial benefits that viewers might receive (like stimulus checks, tax credits, government payments, etc.).\nIgnore general financial advice or market analysis that doesn\'t involve direct benefits to individuals.';
  
  // Make sure other required fields are present
  if (!rawData.hasBenefit) {
    rawData.hasBenefit = true;
  }
  
  if (!rawData.certaintyScore) {
    rawData.certaintyScore = video.financial_score || 85;
  }
  
  if (!rawData.score) {
    rawData.score = video.financial_score || 85;
  }
  
  if (!rawData.benefitType) {
    rawData.benefitType = "SNAP/EBT Benefits";
  }
  
  if (!rawData.benefitDescription) {
    rawData.benefitDescription = video.financial_amount || "Food stamp changes and updates";
  }
  
  if (!rawData.extractedInfo) {
    rawData.extractedInfo = {
      benefitAmounts: ["$120"],
      expectedArrivalDate: "April 2025",
      eligiblePeople: "SNAP/EBT recipients",
      proofOrSource: "Government announcements",
      actionsToClaim: "Check with local SNAP office"
    };
  }
  
  if (!rawData.priorityTag) {
    rawData.priorityTag = video.financial_score >= 70 ? "high" : video.financial_score >= 50 ? "medium" : "low";
  }
  
  if (!rawData.reasoning) {
    rawData.reasoning = "The video discusses several changes and updates to SNAP/EBT programs";
  }
  
  if (!rawData.modelUsed) {
    rawData.modelUsed = "google/gemini-2.0-flash-exp:free";
  }

  // Convert to JSON string
  const rawDataJson = JSON.stringify(rawData, null, 2);
  console.log('Updated raw data (first 100 chars):', rawDataJson.substring(0, 100) + '...');

  // Update the video with the raw data
  const updateStmt = db.prepare('UPDATE youtube_videos SET openrouter_raw_data = ? WHERE id = ?');
  const result = updateStmt.run(rawDataJson, videoId);

  console.log(`Updated ${result.changes} row(s)`);

  // Verify the update
  const updatedRow = db.prepare('SELECT id, openrouter_raw_data FROM youtube_videos WHERE id = ?').get(videoId);
  
  if (updatedRow && updatedRow.openrouter_raw_data) {
    console.log('Successfully updated raw data');
    console.log('Raw data length:', updatedRow.openrouter_raw_data.length);
    
    // Parse the updated raw data to verify it has the prompt
    try {
      const parsedData = JSON.parse(updatedRow.openrouter_raw_data);
      console.log('Prompt included:', Boolean(parsedData.prompt));
      console.log('System prompt included:', Boolean(parsedData.systemPrompt));
    } catch (error) {
      console.error('Error parsing updated raw data:', error);
    }
  } else {
    console.log('Failed to update raw data');
  }

  // Close the database
  db.close();
} catch (error) {
  console.error('Error:', error);
  db.close();
  process.exit(1);
}
