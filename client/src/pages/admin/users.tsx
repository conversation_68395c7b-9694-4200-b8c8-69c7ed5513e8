// Existing users.tsx content

// Add debug log at the top of the file
console.log('[UsersPage] Loading, admin status:', useAuth().isAdmin);
import useFetch from '../../hooks/useFetch';
import { useNavigate } from 'react-router-dom';

interface User {
    id: string;
    name: string;
    email: string;
    role: string;
}

const UserManagementPage: React.FC = () => {
    const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);
    const navigate = useNavigate();

    const { 
        data: users = [], 
        loading, 
        error,
        fetchData: fetchUsers,
        deleteData: deleteUser
    } = useFetch<User[]>('/api/admin/users');

    const { data: currentUser } = useFetch('/api/auth/me');

    if (!currentUser || currentUser.role !== 'admin') {
        navigate('/');
        return null;
    }

    const handleDelete = async (userId: string) => {
        try {
            await deleteUser(`/api/admin/users/${userId}`);
            fetchUsers(); // Refresh list after deletion
        } catch (error) {
            console.error('Failed to delete user:', error);
        } finally {
            setDeleteConfirm(null);
        }
    };

    if (loading) {
        return <div className="loading-spinner">Loading users...</div>;
    }

    if (error) {
        return <div className="error-message">Error: {error.message}</div>;
    }

    return (
        <div className="user-management">
            <h1>User Management</h1>
            <table className="user-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {users.map(user => (
                        <tr key={user.id} className="user-row">
                            <td>{user.id}</td>
                            <td>{user.name}</td>
                            <td>{user.email}</td>
                            <td>{user.role}</td>
                            <td className="action-cell">
                                <button 
                                    className="delete-btn"
                                    onClick={() => setDeleteConfirm(user.id)}
                                >
                                    Delete
                                </button>
                                {deleteConfirm === user.id && (
                                    <div className="confirm-dialog">
                                        <p>Are you sure you want to delete this user?</p>
                                        <button 
                                            className="confirm-btn"
                                            onClick={() => handleDelete(user.id)}
                                        >
                                            Confirm
                                        </button>
                                        <button 
                                            className="cancel-btn"
                                            onClick={() => setDeleteConfirm(null)}
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default UserManagementPage;

