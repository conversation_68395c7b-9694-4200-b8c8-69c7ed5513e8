import { QueryClientProvider } from "@tanstack/react-query";
import { Route, Switch } from "wouter";
import { ToasterProvider } from "@/ui/Toaster";
import { queryClient } from "./lib/queryClient";
import { AuthProvider, useAuth } from "./hooks/use-auth";
import { useSettings } from "./hooks/use-settings";
import { ProtectedRoute } from "./lib/protected-route";
import AuthPage from "./pages/auth-page";
import WatchPage from "./pages/watch-page";
import YoutubePage from "./pages/youtube-page";
import SettingsPage from "./pages/settings-page";
import PlaylistsPage from "./pages/playlists-page";
import HistoryPage from "./pages/history-page";
import AdminSettingsPage from "./pages/admin-settings-page";
import NotFound from "./pages/not-found";
import TasksPage from "./pages/tasks-page";
import DashboardPage from "./pages/admin/dashboard-page";
import UsersPage from "./pages/admin/users-page";
import RssDashboardPage from "./pages/rss-dashboard-page";
import { AdminSidebar } from "./components/admin/admin-sidebar";
import { FloatingVideoProvider } from "@/context/floating-video-context";
import { FloatingVideoPlayer } from "@/components/video/floating-video-player";
import { CastQueueProvider } from "@/context/cast-queue-context";
import { BackgroundTasksProvider } from "@/hooks/use-background-tasks";
import ErrorBoundary from "./components/error-boundary";
import { ThemeInitializer } from "./components/theme-initializer";
import { NetworkStatusIndicator } from "@/components/network-status-indicator";

function Router() {
  const { user } = useAuth();
  const isAdmin = user?.isAdmin || user?.role === 'admin';
  const { settings } = useSettings();

  // Import logger dynamically to avoid circular dependencies
  import('./lib/logger').then(({ logger }) => {
    logger.debug('[Router] User:', user);
    logger.debug('[Router] isAdmin:', isAdmin);
    logger.debug('[Router] Home page setting:', settings?.homePage);
  });

  // Determine which component to use for the home page
  const HomePageComponent = settings?.homePage === 'youtube' ? YoutubePage : WatchPage;

  return (
    <Switch>
      <ProtectedRoute path="/" component={HomePageComponent} />
      <ProtectedRoute path="/trendy" component={WatchPage} />
      <ProtectedRoute path="/youtube" component={YoutubePage} />
      <ProtectedRoute path="/playlists" component={PlaylistsPage} />
      <ProtectedRoute path="/history" component={HistoryPage} />
      <ProtectedRoute path="/settings" component={SettingsPage} />
      <ProtectedRoute path="/tasks" component={TasksPage} />
      <ProtectedRoute path="/admin/settings" component={AdminSettingsPage} />
      <ProtectedRoute path="/rss-dashboard" component={RssDashboardPage} />
      {isAdmin && <ProtectedRoute path="/admin/dashboard" component={DashboardPage} />}
      {isAdmin && <ProtectedRoute path="/admin/users" component={UsersPage} />}
      <Route path="/auth" component={AuthPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <BackgroundTasksProvider>
            <CastQueueProvider>
              <FloatingVideoProvider>
                <ToasterProvider>
                  {/* Initialize theme as early as possible */}
                  <ThemeInitializer />
                  <AdminLayout>
                    <ErrorBoundary>
                      <Router />
                    </ErrorBoundary>
                  </AdminLayout>
                  <ErrorBoundary>
                    <FloatingVideoPlayer />
                  </ErrorBoundary>
                </ToasterProvider>
              </FloatingVideoProvider>
            </CastQueueProvider>
          </BackgroundTasksProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

function AdminLayout({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();

  // Check for both isAdmin property and role === 'admin'
  const isUserAdmin = user?.isAdmin || user?.role === 'admin';

  // Import logger dynamically to avoid circular dependencies
  import('./lib/logger').then(({ logger }) => {
    logger.debug('[AdminLayout] User:', user);
    logger.debug('[AdminLayout] isUserAdmin:', isUserAdmin);
  });

  return (
    <div className="flex h-screen">
      {isUserAdmin && <AdminSidebar />}
      <div className="flex-1 overflow-auto relative">
        {/* Network status indicator - only show when there's an issue */}
        <div className="absolute bottom-2 right-2 z-50 opacity-60 hover:opacity-100 transition-opacity">
          <NetworkStatusIndicator
            showLabel={false}
            showResetButton={false}
            hideWhenOnline={true}
          />
        </div>
        {children}
      </div>
    </div>
  );
}

export default App;
