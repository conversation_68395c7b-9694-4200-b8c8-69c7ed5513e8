import express from 'express';
import { storage } from '../storage';
import { z } from 'zod';
import dotenv from 'dotenv';
import path from 'path';

// Load VidIQ environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env.vidiq') });

const router = express.Router();

// Schema for adding a new API key
const addApiKeySchema = z.object({
  token: z.string().min(1, "Token is required"),
  auth_token: z.string().optional(),
  cookie: z.string().optional(),
  name: z.string().optional(),
  is_active: z.boolean().default(true),
});

// Schema for updating an API key
const updateApiKeySchema = z.object({
  token: z.string().optional(),
  auth_token: z.string().optional(),
  cookie: z.string().optional(),
  name: z.string().optional(),
  is_active: z.boolean().optional(),
});

// Get all API keys
router.get('/', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  // Allow all authenticated users to access API keys

  try {
    const apiKeys = await storage.getAllApiKeys();

    // Remove sensitive data before sending to client
    const safeApiKeys = apiKeys.map(key => ({
      id: key.id,
      name: key.name,
      is_active: key.is_active,
      last_used: key.last_used,
      created_at: key.created_at,
      exhausted_until: key.exhausted_until,
      // Include a masked version of the token for display
      masked_token: key.token ? `${key.token.substring(0, 5)}...${key.token.substring(key.token.length - 5)}` : '',
    }));

    res.json(safeApiKeys);
  } catch (error) {
    console.error('Error fetching API keys:', error);
    res.status(500).json({ message: "Failed to fetch API keys" });
  }
});

// Add a new API key
router.post('/', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  // Allow all authenticated users to add API keys

  try {
    // Validate request body
    const validatedData = addApiKeySchema.parse(req.body);

    // Extract API key details from the input
    // This handles various formats of input (raw token, curl command, etc.)
    const extractedData = extractApiKeyDetails(validatedData.token);

    // Create the API key
    const newApiKey = await storage.addApiKey({
      token: extractedData.token,
      auth_token: extractedData.auth_token || validatedData.auth_token,
      cookie: extractedData.cookie || validatedData.cookie,
      name: validatedData.name || extractedData.name,
      is_active: validatedData.is_active,
    });

    // Return a safe version without sensitive data
    const safeApiKey = {
      id: newApiKey.id,
      name: newApiKey.name,
      is_active: newApiKey.is_active,
      created_at: newApiKey.created_at,
      masked_token: `${newApiKey.token.substring(0, 5)}...${newApiKey.token.substring(newApiKey.token.length - 5)}`,
    };

    res.status(201).json(safeApiKey);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Validation error", errors: error.errors });
    }

    console.error('Error adding API key:', error);
    res.status(500).json({ message: "Failed to add API key" });
  }
});

// Update an API key
router.put('/:id', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  // Allow all authenticated users to update API keys

  const id = parseInt(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ message: "Invalid API key ID" });
  }

  try {
    // Validate request body
    const validatedData = updateApiKeySchema.parse(req.body);

    // Check if the API key exists
    const existingKey = await storage.getApiKey(id);
    if (!existingKey) {
      return res.status(404).json({ message: "API key not found" });
    }

    // If token is provided, extract API key details
    let extractedData = {};
    if (validatedData.token) {
      extractedData = extractApiKeyDetails(validatedData.token);
    }

    // Update the API key
    const updatedApiKey = await storage.updateApiKey(id, {
      ...validatedData,
      ...extractedData,
    });

    if (!updatedApiKey) {
      return res.status(404).json({ message: "API key not found" });
    }

    // Return a safe version without sensitive data
    const safeApiKey = {
      id: updatedApiKey.id,
      name: updatedApiKey.name,
      is_active: updatedApiKey.is_active,
      last_used: updatedApiKey.last_used,
      created_at: updatedApiKey.created_at,
      exhausted_until: updatedApiKey.exhausted_until,
      masked_token: `${updatedApiKey.token.substring(0, 5)}...${updatedApiKey.token.substring(updatedApiKey.token.length - 5)}`,
    };

    res.json(safeApiKey);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Validation error", errors: error.errors });
    }

    console.error(`Error updating API key ${id}:`, error);
    res.status(500).json({ message: "Failed to update API key" });
  }
});

// Delete an API key
router.delete('/:id', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  // Allow all authenticated users to delete API keys

  const id = parseInt(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ message: "Invalid API key ID" });
  }

  try {
    // Check if the API key exists
    const existingKey = await storage.getApiKey(id);
    if (!existingKey) {
      return res.status(404).json({ message: "API key not found" });
    }

    // Delete the API key
    await storage.deleteApiKey(id);

    res.status(204).end();
  } catch (error) {
    console.error(`Error deleting API key ${id}:`, error);
    res.status(500).json({ message: "Failed to delete API key" });
  }
});

// Get default API key from environment variables
router.get('/default', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const token = process.env.VIDIQ_TOKEN;
    const authToken = process.env.VIDIQ_AUTH;
    const cookie = process.env.VIDIQ_COOKIE;

    if (!token) {
      return res.status(404).json({ message: "No default API key found in environment variables" });
    }

    // Create a safe version of the default key
    const safeDefaultKey = {
      id: 0, // Use 0 to indicate it's the default key
      name: "Default API Key (Environment)",
      is_active: true, // Default to active
      created_at: new Date().toISOString(),
      is_default: true,
      masked_token: token ? `${token.substring(0, 5)}...${token.substring(token.length - 5)}` : '',
    };

    // Set the content type explicitly
    res.setHeader('Content-Type', 'application/json');
    res.send(JSON.stringify(safeDefaultKey));
  } catch (error) {
    console.error('Error fetching default API key:', error);
    res.status(500).json({ message: "Failed to fetch default API key" });
  }
});

// Test default API key
router.post('/default/test', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const token = process.env.VIDIQ_TOKEN;
    const authToken = process.env.VIDIQ_AUTH;
    const cookie = process.env.VIDIQ_COOKIE;

    if (!token) {
      return res.status(404).json({ message: "No default API key found in environment variables" });
    }

    // Create a temporary API key object for testing
    const defaultKey = {
      token,
      auth_token: authToken || token,
      cookie: cookie || ''
    };

    // Test the default API key
    const testResult = await testApiKey(defaultKey);

    res.json({ success: testResult.success, message: testResult.message });
  } catch (error) {
    console.error('Error testing default API key:', error);
    res.status(500).json({ message: "Failed to test default API key" });
  }
});

// Toggle default API key active status
router.put('/default/toggle', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const token = process.env.VIDIQ_TOKEN;
    if (!token) {
      return res.status(404).json({ message: "No default API key found in environment variables" });
    }

    // In a real implementation, we would update the environment variable or a database setting
    // For now, we'll just return a success response to simulate toggling
    res.json({
      success: true,
      message: "Default API key status toggled",
      is_active: req.body.is_active
    });
  } catch (error) {
    console.error('Error toggling default API key:', error);
    res.status(500).json({ message: "Failed to toggle default API key" });
  }
});

// Delete default API key
router.delete('/default', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const token = process.env.VIDIQ_TOKEN;
    if (!token) {
      return res.status(404).json({ message: "No default API key found in environment variables" });
    }

    // In a real implementation, we would remove the environment variable or update a database setting
    // For now, we'll just return a success response to simulate deletion
    res.json({
      success: true,
      message: "Default API key deleted"
    });
  } catch (error) {
    console.error('Error deleting default API key:', error);
    res.status(500).json({ message: "Failed to delete default API key" });
  }
});

// Test an API key
router.post('/:id/test', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  // Allow all authenticated users to test API keys

  const id = parseInt(req.params.id);
  if (isNaN(id)) {
    return res.status(400).json({ message: "Invalid API key ID" });
  }

  try {
    // Check if the API key exists
    const apiKey = await storage.getApiKey(id);
    if (!apiKey) {
      return res.status(404).json({ message: "API key not found" });
    }

    // Test the API key by making a simple request to VidIQ API
    const testResult = await testApiKey(apiKey);

    res.json({ success: testResult.success, message: testResult.message });
  } catch (error) {
    console.error(`Error testing API key ${id}:`, error);
    res.status(500).json({ message: "Failed to test API key" });
  }
});

// Helper function to extract API key details from various input formats
function extractApiKeyDetails(input: string): { token: string, auth_token?: string, cookie?: string, name?: string } {
  // Default result with just the token
  const result: { token: string, auth_token?: string, cookie?: string, name?: string } = {
    token: input.trim()
  };

  // Check if it's a curl command
  if (input.startsWith('curl ')) {
    // Extract Bearer token
    const bearerMatch = input.match(/--header\s+'Authorization:\s+Bearer\s+([^']+)'/);
    if (bearerMatch && bearerMatch[1]) {
      result.token = bearerMatch[1].trim();
    }

    // Extract auth token
    const authMatch = input.match(/--header\s+'x-vidiq-auth:\s+([^']+)'/);
    if (authMatch && authMatch[1]) {
      result.auth_token = authMatch[1].trim();
    }

    // Extract cookie
    const cookieMatch = input.match(/--header\s+'Cookie:\s+([^']+)'/);
    if (cookieMatch && cookieMatch[1]) {
      result.cookie = cookieMatch[1].trim();
    }

    // Set a name based on the token
    result.name = `API Key (${result.token.substring(0, 8)}...)`;
  } else if (input.startsWith('Bearer ')) {
    // If it's just a Bearer token
    result.token = input.substring(7).trim();
  }

  return result;
}

// Helper function to test an API key
async function testApiKey(apiKey: any): Promise<{ success: boolean, message: string }> {
  try {
    // Make a simple request to VidIQ API
    const response = await fetch('https://api.vidiq.com/v0/trendy?q[]=test&c=US&l=en&limit=1', {
      method: 'GET',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Authorization': `Bearer ${apiKey.token}`,
        'Referer': 'https://app.vidiq.com/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        'X-TimeZone': 'Asia/Calcutta',
        'X-Vidiq-Client': 'web 1a7277ec05e25b41808d7ed49f9a3f3c6ff86254',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'x-vidiq-auth': apiKey.auth_token || apiKey.token,
        'Cookie': apiKey.cookie || ''
      }
    });

    if (response.ok) {
      return { success: true, message: "API key is working correctly" };
    } else {
      return { success: false, message: `API key test failed: ${response.status} ${response.statusText}` };
    }
  } catch (error) {
    console.error('Error testing API key:', error);
    return { success: false, message: `Error testing API key: ${error.message}` };
  }
}

export default router;
