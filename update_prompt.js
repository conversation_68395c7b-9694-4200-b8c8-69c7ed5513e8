const Database = require('better-sqlite3');

/**
 * <PERSON><PERSON><PERSON> to update the default Ollama prompt
 */
function updatePrompt() {
  console.log('Updating default Ollama prompt');
  
  try {
    // Connect to the database
    const db = new Database('data.db');
    
    // Check if the ollama_prompts table exists
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='ollama_prompts'").all();
    if (tables.length === 0) {
      console.log('ollama_prompts table does not exist. No update needed.');
      db.close();
      return;
    }
    
    console.log('ollama_prompts table exists, proceeding with update.');
    
    // The new optimized prompt
    const optimizedPrompt = `You are a financial benefit analyzer specialized in identifying financial benefits mentioned in YouTube video transcripts.

Your task is to extract specific financial benefit information from the provided content and classify it based on certainty and value.

Focus ONLY on financial benefits that viewers might receive (like stimulus checks, tax credits, government payments, Social Security benefits, etc.). Ignore general financial advice or market analysis that doesn't involve direct benefits to individuals.

Analyze the following YouTube video content:

TITLE: "[title]"

DESCRIPTION: "[description]"

TRANSCRIPT:
[Paste transcript here]

Extract the following information:
1. All financial benefit amounts mentioned (e.g., $1200, $600)
2. Type of benefit (Direct Money / Tax Saving / Program Support / Stimulus)
3. Duration of benefit (One-time / Monthly / Yearly / Temporary)
4. Who is eligible to receive the benefit
5. How to apply or what actions to take to receive the benefit
6. Expected arrival timeline for the benefit (if mentioned)
7. Source or proof mentioned (Act name, authority, bill, article, etc.)

Then classify the content into one of these categories:
- "Urgent_High_Benefit": Clear proof, official legislation, very likely to be real.
- "Anticipated_Benefit": Strong indication, not yet confirmed but likely.
- "Doubtful_Benefit": Sounds exciting but lacks official proof or reliable source.

Assign a credibility score (0-100):
- 80-100: Clear date, amount, eligible group, and valid proof (govt source, program)
- 50-79: Talk of upcoming benefits, not finalized yet, but possibly valid
- 20-49: Claims unrealistic or have no proof, vague terms like "could happen soon"
- 0-19: Small amount or hard-to-access, low relevance to general users

IMPORTANT: Return ONLY a JSON object with the following structure:

{
  "title": "",
  "video_url": "https://youtube.com/watch?v=[video_id]",
  "financial_benefits": [
    {
      "amount": "",
      "type": "",
      "duration": "",
      "eligibility": "",
      "how_to_apply": "",
      "expected_arrival": "",
      "proof_source": ""
    }
  ],
  "classification": "",
  "credibility_score": 0,
  "virality_reason": "",
  "reason_for_doubt": ""
}

If multiple benefits are mentioned, include each as a separate object in the financial_benefits array.
If no financial benefits are mentioned, return an empty array for financial_benefits, a classification of "Doubtful_Benefit", and a credibility score of 0.
Do not include any explanations, notes, or text outside of the JSON structure.`;
    
    // Update all default prompts
    const updateStmt = db.prepare(`
      UPDATE ollama_prompts
      SET prompt_text = ?
      WHERE name = 'Default Financial Analysis' AND is_default = 1
    `);
    
    const result = updateStmt.run(optimizedPrompt);
    console.log(`Updated ${result.changes} default prompts`);
    
    // Also update the settings table for users who are using the default prompt
    const updateSettingsStmt = db.prepare(`
      UPDATE settings
      SET ollama_analysis_prompt = ?
      WHERE ollama_analysis_prompt LIKE '%You are a financial analyst AI%'
    `);
    
    const settingsResult = updateSettingsStmt.run(optimizedPrompt);
    console.log(`Updated ${settingsResult.changes} user settings with the new default prompt`);
    
    // Close the database connection
    db.close();
    console.log('Update completed successfully');
  } catch (error) {
    console.error('Error updating prompt:', error);
    throw error;
  }
}

// Run the update
updatePrompt();
