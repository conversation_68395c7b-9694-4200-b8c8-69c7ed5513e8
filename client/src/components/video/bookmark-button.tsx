import { useState } from 'react';
import { Video } from '@shared/schema';
import { Button } from '@/components/ui/button';
import { Bookmark, Check, Plus, X, Cast, ListVideo } from 'lucide-react';
import { useCastQueue } from '@/context/cast-queue-context';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { usePlaylists } from '@/hooks/use-playlists';

interface BookmarkButtonProps {
  video: Video;
}

export function BookmarkButton({ video }: BookmarkButtonProps) {
  const { playlists, createPlaylist, addVideoToPlaylist } = usePlaylists();
  const { addToQueue, isInQueue, state: castQueueState } = useCastQueue();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState('');
  const [newPlaylistDescription, setNewPlaylistDescription] = useState('');

  const handleAddToPlaylist = async (playlistId: number) => {
    await addVideoToPlaylist.mutateAsync({ playlistId, video });
  };

  const handleCreatePlaylist = async () => {
    if (!newPlaylistName.trim()) return;

    const playlist = await createPlaylist.mutateAsync({
      name: newPlaylistName,
      description: newPlaylistDescription,
    });

    // Add the video to the newly created playlist
    await addVideoToPlaylist.mutateAsync({ playlistId: playlist.id, video });

    // Reset form and close dialog
    setNewPlaylistName('');
    setNewPlaylistDescription('');
    setIsDialogOpen(false);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="bg-black/50 hover:bg-black/70 text-white rounded-full"
          onClick={(e) => e.stopPropagation()}
        >
          <Bookmark className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64" align="end">
        <div className="space-y-2">
          <h4 className="font-medium">Add to playlist</h4>

          {/* Cast Queue Option */}
          <div className="pb-2 border-b">
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={() => addToQueue(video, { silent: false })}
              disabled={isInQueue(video.id)}
            >
              <Cast className="h-4 w-4 mr-2" />
              {isInQueue(video.id) ? 'Already in Cast Queue' : 'Add to Cast Queue'}
            </Button>

            {castQueueState.queue.length > 0 && (
              <div className="text-xs text-muted-foreground mt-1 pl-2">
                <ListVideo className="h-3 w-3 inline mr-1" />
                {castQueueState.queue.length} {castQueueState.queue.length === 1 ? 'video' : 'videos'} in queue
              </div>
            )}
          </div>

          <div className="max-h-48 overflow-y-auto space-y-1 pt-2">
            {playlists.map((playlist) => (
              <Button
                key={playlist.id}
                variant="ghost"
                className="w-full justify-start"
                onClick={() => handleAddToPlaylist(playlist.id)}
              >
                <Plus className="h-4 w-4 mr-2" />
                {playlist.name}
              </Button>
            ))}
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="w-full mt-2">
                Create new playlist
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create new playlist</DialogTitle>
                <DialogDescription>
                  Create a new playlist to save your favorite videos.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    placeholder="My Playlist"
                    value={newPlaylistName}
                    onChange={(e) => setNewPlaylistName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description (optional)</Label>
                  <Input
                    id="description"
                    placeholder="A collection of my favorite videos"
                    value={newPlaylistDescription}
                    onChange={(e) => setNewPlaylistDescription(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreatePlaylist}>Create</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </PopoverContent>
    </Popover>
  );
}
