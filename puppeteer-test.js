import puppeteer from 'puppeteer';

async function scrapeXcomWithPuppeteer(url) {
  console.log('Starting Puppeteer to scrape X.com content for:', url);

  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();

    // Set a mobile user agent
    await page.setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1');

    // Set viewport to mobile size
    await page.setViewport({
      width: 375,
      height: 812,
      isMobile: true
    });

    console.log('Navigating to URL:', url);
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

    // Wait for tweet content to load
    await page.waitForSelector('[data-testid="tweetText"]', { timeout: 10000 })
      .catch(() => console.log('Tweet text selector not found'));

    console.log('Page loaded, taking screenshot...');
    await page.screenshot({ path: 'xcom-screenshot.png' });
    console.log('Screenshot saved to xcom-screenshot.png');

    // Get the tweet content
    const tweetContent = await page.evaluate(() => {
      const tweetTextElement = document.querySelector('[data-testid="tweetText"]');
      return tweetTextElement ? tweetTextElement.innerHTML : null;
    });

    console.log('Tweet content found:', !!tweetContent);
    if (tweetContent) {
      console.log('Tweet content length:', tweetContent.length);
      console.log('Tweet content preview:', tweetContent.substring(0, 100));

      // Get author info
      const authorName = await page.evaluate(() => {
        const nameElement = document.querySelector('[data-testid="User-Name"]');
        return nameElement ? nameElement.textContent : '';
      });

      const authorUsername = await page.evaluate(() => {
        const usernameElement = document.querySelector('[data-testid="User-Username"]');
        return usernameElement ? usernameElement.textContent : '';
      });

      // Construct the content
      let fullContent = '';
      if (authorName) fullContent += `<h2>${authorName}</h2>`;
      if (authorUsername) fullContent += `<p class="tweet-username">${authorUsername}</p>`;
      fullContent += `<div class="tweet-content">${tweetContent}</div>`;
      fullContent += `<p class="source-note"><small>Content retrieved from X.com (Twitter) via Puppeteer</small></p>`;

      return fullContent;
    } else {
      console.log('No tweet content found');
      return null;
    }
  } catch (error) {
    console.error('Error scraping with Puppeteer:', error);
    return null;
  } finally {
    await browser.close();
    console.log('Browser closed');
  }
}

// Test the function with the provided URL
async function testScraping() {
  const url = 'https://x.com/unusual_whales/status/1919461341313827020';
  console.log('Testing X.com scraping for URL:', url);

  try {
    const content = await scrapeXcomWithPuppeteer(url);
    console.log('Scraping result:', content ? 'Success' : 'Failed');
    if (content) {
      console.log('Content length:', content.length);
      console.log('Content preview:', content.substring(0, 200));
    }
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
testScraping();
