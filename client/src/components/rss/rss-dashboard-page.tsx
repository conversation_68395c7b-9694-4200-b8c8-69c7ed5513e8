import React, { useState, useEffect } from 'react';
import { RssFeedItem } from '@shared/schema';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, BarChart, PieChart, Cloud, ArrowLeft, Calendar, Filter, RefreshCw, Clock, Plus, X } from 'lucide-react';
import * as RechartsPrimitive from 'recharts';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { HighlightCounts, FinancialTermType, highlightFinancialTerms } from './financial-term-highlighter';
import { ScrollArea } from '@/components/ui/scroll-area';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';

// Common words to exclude from keyword analysis
const COMMON_WORDS = new Set([
  // Articles, pronouns, prepositions
  'a', 'an', 'the', 'this', 'that', 'these', 'those', 'it', 'its', 'is', 'are', 'was', 'were',
  'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'can', 'could', 'will',
  'would', 'should', 'may', 'might', 'must', 'shall', 'to', 'of', 'for', 'with', 'in', 'on',
  'at', 'by', 'from', 'about', 'as', 'into', 'like', 'through', 'after', 'over', 'between',
  'out', 'up', 'down', 'off', 'and', 'or', 'but', 'if', 'while', 'because', 'so', 'though',
  'since', 'when', 'where', 'how', 'what', 'who', 'whom', 'which', 'why', 'i', 'you', 'he',
  'she', 'we', 'they', 'them', 'their', 'his', 'her', 'our', 'my', 'your', 'not', 'no', 'yes',

  // Common verbs
  'said', 'says', 'say', 'see', 'get', 'go', 'know', 'make', 'made', 'take', 'took', 'come', 'came',
  'give', 'gave', 'use', 'used', 'find', 'found', 'tell', 'told', 'ask', 'asked', 'seem', 'seemed',
  'try', 'tried', 'call', 'called', 'need', 'needed', 'feel', 'felt', 'become', 'became', 'leave',
  'left', 'put', 'keep', 'kept', 'begin', 'began', 'begun', 'show', 'showed', 'shown', 'hear',
  'heard', 'let', 'help', 'helped', 'talk', 'talked', 'turn', 'turned', 'start', 'started',
  'want', 'wanted', 'look', 'looked', 'run', 'ran', 'move', 'moved', 'live', 'lived', 'believe',
  'believed', 'hold', 'held', 'bring', 'brought', 'happen', 'happened', 'write', 'wrote',
  'written', 'sit', 'sat', 'stand', 'stood', 'lose', 'lost', 'pay', 'paid', 'meet', 'met',
  'include', 'included', 'continue', 'continued', 'set', 'learn', 'learned', 'change', 'changed',
  'lead', 'led', 'understand', 'understood', 'watch', 'watched', 'follow', 'followed',

  // Numbers and time words
  'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten',
  'first', 'second', 'third', 'fourth', 'fifth', 'time', 'day', 'week', 'month', 'year',
  'today', 'yesterday', 'tomorrow', 'now', 'then', 'soon', 'later', 'early', 'late',

  // Other common words
  'just', 'also', 'very', 'really', 'quite', 'even', 'only', 'back', 'well', 'still',
  'too', 'own', 'any', 'some', 'many', 'much', 'more', 'most', 'other', 'another',
  'new', 'old', 'good', 'bad', 'great', 'best', 'better', 'worse', 'worst',
  'high', 'low', 'long', 'short', 'small', 'big', 'large', 'little', 'few',
  'way', 'thing', 'part', 'kind', 'sort', 'type', 'form', 'style', 'example',
  'case', 'point', 'fact', 'issue', 'matter', 'problem', 'question', 'subject'
]);

// US States with full names and abbreviations
const US_STATE_MAP: Record<string, string> = {
  'alabama': 'AL',
  'alaska': 'AK',
  'arizona': 'AZ',
  'arkansas': 'AR',
  'california': 'CA',
  'colorado': 'CO',
  'connecticut': 'CT',
  'delaware': 'DE',
  'florida': 'FL',
  'georgia': 'GA',
  'hawaii': 'HI',
  'idaho': 'ID',
  'illinois': 'IL',
  'indiana': 'IN',
  'iowa': 'IA',
  'kansas': 'KS',
  'kentucky': 'KY',
  'louisiana': 'LA',
  'maine': 'ME',
  'maryland': 'MD',
  'massachusetts': 'MA',
  'michigan': 'MI',
  'minnesota': 'MN',
  'mississippi': 'MS',
  'missouri': 'MO',
  'montana': 'MT',
  'nebraska': 'NE',
  'nevada': 'NV',
  'new hampshire': 'NH',
  'new jersey': 'NJ',
  'new mexico': 'NM',
  'new york': 'NY',
  'north carolina': 'NC',
  'north dakota': 'ND',
  'ohio': 'OH',
  'oklahoma': 'OK',
  'oregon': 'OR',
  'pennsylvania': 'PA',
  'rhode island': 'RI',
  'south carolina': 'SC',
  'south dakota': 'SD',
  'tennessee': 'TN',
  'texas': 'TX',
  'utah': 'UT',
  'vermont': 'VT',
  'virginia': 'VA',
  'washington': 'WA',
  'west virginia': 'WV',
  'wisconsin': 'WI',
  'wyoming': 'WY',
  'district of columbia': 'DC'
};

// Create a set of all state names and abbreviations for quick lookup
const US_STATES = new Set([
  ...Object.keys(US_STATE_MAP),
  ...Object.values(US_STATE_MAP).map(abbr => abbr.toLowerCase()),
  'dc' // Add lowercase DC
]);

// Important topics to specifically identify
const IMPORTANT_TOPICS = new Set([
  // Social Security related
  'social security', 'ssa', 'retirement', 'disability', 'ssdi', 'ssi', 'supplemental security income',
  'benefits', 'cola', 'cost of living adjustment', 'medicare', 'medicaid', 'survivor', 'survivors',

  // Bills, Acts, Proposals
  'act', 'bill', 'law', 'legislation', 'amendment', 'proposal', 'reform', 'plan', 'program',
  'policy', 'regulation', 'rule', 'statute', 'code', 'ordinance', 'provision', 'measure',

  // Government entities
  'congress', 'senate', 'house', 'representative', 'senator', 'committee', 'subcommittee',
  'administration', 'government', 'federal', 'state', 'local', 'agency', 'department',
  'treasury', 'irs', 'internal revenue service', 'tax', 'taxes', 'taxation'
]);

// Interface for keyword data
interface KeywordData {
  keyword: string;
  count: number;
  positiveCount: number;
  negativeCount: number;
  neutralCount: number;
  articles: number[];
}

// Interface for sentiment data
interface SentimentData {
  type: string;
  count: number;
}

// Time filter options
type TimeFilter = 'all' | 'today' | 'yesterday' | 'last7days' | 'custom';
type KeywordFilter = 'all' | 'states' | 'topics' | 'custom';

export function RssDashboardPage() {
  const [activeTab, setActiveTab] = useState('keywords');
  const [allFeedItems, setAllFeedItems] = useState<RssFeedItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<RssFeedItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefreshed, setLastRefreshed] = useState<Date | null>(null);
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('all');
  const [keywordFilter, setKeywordFilter] = useState<KeywordFilter>('all');
  const [keywordCount, setKeywordCount] = useState(50); // Number of keywords to display
  const [excludedKeywords, setExcludedKeywords] = useState<string[]>([]);
  const [newExcludedKeyword, setNewExcludedKeyword] = useState('');
  const [selectedKeyword, setSelectedKeyword] = useState<string | null>(null);
  const [relatedKeywords, setRelatedKeywords] = useState<KeywordData[]>([]);
  const [showRelatedKeywords, setShowRelatedKeywords] = useState(false);
  const [selectedState, setSelectedState] = useState<string | null>(null);
  const [stateKeywords, setStateKeywords] = useState<KeywordData[]>([]);
  const [showStateKeywords, setShowStateKeywords] = useState(false);
  const { toast } = useToast();

  // Function to fetch all feed items
  const fetchAllFeedItems = async (isInitialLoad = false) => {
    try {
      if (isInitialLoad) {
        setIsLoading(true);
      } else {
        setIsRefreshing(true);
      }

      // First, get all feeds
      const feedsResponse = await apiRequest('GET', '/api/rss-feeds');
      const feeds = await feedsResponse.json();

      let allItems: RssFeedItem[] = [];

      // Fetch items from all feeds
      for (const feed of feeds) {
        const response = await apiRequest('GET', `/api/rss-feeds/${feed.id}/items`);

        if (!response.ok) {
          console.error(`Failed to fetch items for feed ${feed.id}: ${response.status} ${response.statusText}`);
          continue;
        }

        const items = await response.json();
        allItems = [...allItems, ...items];
      }

      console.log(`Total items collected for dashboard: ${allItems.length}`);
      setAllFeedItems(allItems);
      setFilteredItems(allItems); // Initially, filtered items are the same as all items
      setLastRefreshed(new Date());

      if (!isInitialLoad) {
        toast({
          title: 'Success',
          description: `Dashboard refreshed with ${allItems.length} articles`,
        });
      }
    } catch (error) {
      console.error('Error fetching all feed items:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch RSS feed items for dashboard',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Handle manual refresh
  const handleRefresh = () => {
    fetchAllFeedItems(false);
  };

  // Handle adding a keyword to the exclusion list
  const handleAddExcludedKeyword = () => {
    if (!newExcludedKeyword.trim()) return;

    const keyword = newExcludedKeyword.trim().toLowerCase();
    if (!excludedKeywords.includes(keyword)) {
      setExcludedKeywords([...excludedKeywords, keyword]);
      setNewExcludedKeyword('');

      toast({
        title: 'Keyword excluded',
        description: `"${keyword}" will now be excluded from keyword analysis`,
      });
    }
  };

  // Handle removing a keyword from the exclusion list
  const handleRemoveExcludedKeyword = (keyword: string) => {
    setExcludedKeywords(excludedKeywords.filter(k => k !== keyword));

    toast({
      title: 'Keyword restored',
      description: `"${keyword}" will now be included in keyword analysis`,
    });
  };

  // Handle keyword selection to show related keywords
  const handleKeywordSelect = (keyword: string) => {
    setSelectedKeyword(keyword);
    findRelatedKeywords(keyword);
    setShowRelatedKeywords(true);

    // If we're not already on the keywords tab, switch to it
    if (activeTab !== 'keywords') {
      setActiveTab('keywords');
    }
  };

  // Find related keywords (long-tail keywords) for a given keyword
  const findRelatedKeywords = (keyword: string) => {
    if (!filteredItems || filteredItems.length === 0) {
      setRelatedKeywords([]);
      return;
    }

    // Create a map to store related keyword data
    const relatedKeywordMap = new Map<string, KeywordData>();

    // Find all articles that contain the selected keyword
    const relevantArticles = filteredItems.filter(item => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = title + ' ' + content + ' ' + scrapedContent;
      const textContent = tempDiv.textContent || '';

      // Check if the article contains the keyword
      return textContent.toLowerCase().includes(keyword.toLowerCase());
    });

    if (relevantArticles.length === 0) {
      setRelatedKeywords([]);
      return;
    }

    // Process each relevant article to find related keywords
    relevantArticles.forEach((item) => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = title + ' ' + content + ' ' + scrapedContent;
      const textContent = tempDiv.textContent || '';

      // Analyze sentiment
      const { counts } = highlightFinancialTerms(textContent);

      // Extract phrases that contain the keyword (long-tail keywords)
      const text = textContent.toLowerCase();
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

      // Find sentences containing the keyword
      const relevantSentences = sentences.filter(sentence =>
        sentence.includes(keyword.toLowerCase())
      );

      // Extract potential long-tail keywords from relevant sentences
      relevantSentences.forEach(sentence => {
        // Split sentence into words
        const words = sentence.split(/\s+/).filter(w => w.length > 0);

        // Find the position of the keyword in the sentence
        const keywordIndices = words.reduce((indices, word, index) => {
          if (word === keyword.toLowerCase() || word.includes(keyword.toLowerCase())) {
            indices.push(index);
          }
          return indices;
        }, [] as number[]);

        // For each occurrence of the keyword, extract phrases of 2-4 words including the keyword
        keywordIndices.forEach(keywordIndex => {
          // Generate phrases of different lengths
          for (let phraseLength = 2; phraseLength <= 4; phraseLength++) {
            // Generate phrases with the keyword at different positions
            for (let keywordPosition = 0; keywordPosition < phraseLength; keywordPosition++) {
              // Calculate the start index for this phrase
              const startIndex = Math.max(0, keywordIndex - keywordPosition);

              // Check if we have enough words to form the phrase
              if (startIndex + phraseLength <= words.length) {
                // Extract the phrase
                const phrase = words.slice(startIndex, startIndex + phraseLength).join(' ');

                // Skip if the phrase is just the keyword itself or too short
                if (phrase === keyword.toLowerCase() || phrase.length < keyword.length + 3) {
                  continue;
                }

                // Skip if the phrase contains common words only
                if (phrase.split(/\s+/).every(word => COMMON_WORDS.has(word))) {
                  continue;
                }

                // Add to related keywords map
                if (!relatedKeywordMap.has(phrase)) {
                  relatedKeywordMap.set(phrase, {
                    keyword: phrase,
                    count: 0,
                    positiveCount: 0,
                    negativeCount: 0,
                    neutralCount: 0,
                    articles: []
                  });
                }

                const data = relatedKeywordMap.get(phrase)!;
                data.count += 1;
                data.positiveCount += counts.positive;
                data.negativeCount += counts.negative;
                data.neutralCount += counts.neutral;

                // Add article ID if not already included
                if (!data.articles.includes(item.id)) {
                  data.articles.push(item.id);
                }
              }
            }
          }
        });
      });
    });

    // Convert map to array, filter out rare phrases, and sort by count
    const relatedKeywordsArray = Array.from(relatedKeywordMap.values())
      .filter(data => data.count > 1 && data.articles.length > 1) // Filter out phrases that appear only once or in only one article
      .sort((a, b) => b.count - a.count);

    setRelatedKeywords(relatedKeywordsArray);
  };

  // Close the related keywords view
  const handleCloseRelatedKeywords = () => {
    setShowRelatedKeywords(false);
    setSelectedKeyword(null);
  };

  // Handle state selection to show state-specific keywords
  const handleStateSelect = (stateName: string) => {
    setSelectedState(stateName);
    findStateKeywords(stateName);
    setShowStateKeywords(true);

    // If we're not already on the states tab, switch to it
    if (activeTab !== 'states') {
      setActiveTab('states');
    }
  };

  // Find keywords associated with a specific state
  const findStateKeywords = (stateName: string) => {
    if (!filteredItems || filteredItems.length === 0) {
      setStateKeywords([]);
      return;
    }

    // Extract state name without abbreviation
    const stateNameOnly = stateName.split(' (')[0].toLowerCase();
    const stateAbbr = stateName.match(/\(([A-Z]{2})\)/)?.[1]?.toLowerCase() || '';

    // Find all articles that mention the selected state
    const relevantArticles = filteredItems.filter(item => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = title + ' ' + content + ' ' + scrapedContent;
      const textContent = tempDiv.textContent || '';
      const text = textContent.toLowerCase();

      // Check if the article mentions the state by name or abbreviation
      const stateNameRegex = new RegExp(`\\b${stateNameOnly}\\b`, 'i');
      const stateAbbrRegex = new RegExp(`\\b${stateAbbr}\\b`, 'i');

      return stateNameRegex.test(text) || stateAbbrRegex.test(text);
    });

    if (relevantArticles.length === 0) {
      setStateKeywords([]);
      return;
    }

    // Create a map to store keyword data
    const keywordMap = new Map<string, KeywordData>();

    // Process each relevant article to find keywords
    relevantArticles.forEach((item) => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = title + ' ' + content + ' ' + scrapedContent;
      const textContent = tempDiv.textContent || '';

      // Analyze sentiment
      const { counts } = highlightFinancialTerms(textContent);

      // Extract words, convert to lowercase, and remove punctuation
      const words = textContent.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 2 && !COMMON_WORDS.has(word));

      // Count word frequencies
      const wordCounts = new Map<string, number>();
      words.forEach(word => {
        // Skip the state name itself and its abbreviation
        if (word === stateNameOnly || word === stateAbbr) {
          return;
        }
        wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
      });

      // Add to keyword map
      wordCounts.forEach((count, word) => {
        if (!keywordMap.has(word)) {
          keywordMap.set(word, {
            keyword: word,
            count: 0,
            positiveCount: 0,
            negativeCount: 0,
            neutralCount: 0,
            articles: []
          });
        }

        const data = keywordMap.get(word)!;
        data.count += count;
        data.positiveCount += counts.positive;
        data.negativeCount += counts.negative;
        data.neutralCount += counts.neutral;

        // Add article ID if not already included
        if (!data.articles.includes(item.id)) {
          data.articles.push(item.id);
        }
      });
    });

    // Convert map to array, filter out rare words, and sort by count
    const stateKeywordsArray = Array.from(keywordMap.values())
      .filter(data => data.count > 1 && data.articles.length > 1) // Filter out words that appear only once or in only one article
      .sort((a, b) => b.count - a.count);

    setStateKeywords(stateKeywordsArray);
  };

  // Close the state keywords view
  const handleCloseStateKeywords = () => {
    setShowStateKeywords(false);
    setSelectedState(null);
  };

  // Fetch all feed items when the component mounts
  useEffect(() => {
    fetchAllFeedItems(true);
  }, []);

  // Apply time filter to feed items
  useEffect(() => {
    if (allFeedItems.length === 0) return;

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const last7days = new Date(today);
    last7days.setDate(last7days.getDate() - 7);

    let filtered: RssFeedItem[];

    switch (timeFilter) {
      case 'today':
        filtered = allFeedItems.filter(item => {
          const itemDate = new Date(item.publishedAt);
          return itemDate >= today;
        });
        break;
      case 'yesterday':
        filtered = allFeedItems.filter(item => {
          const itemDate = new Date(item.publishedAt);
          return itemDate >= yesterday && itemDate < today;
        });
        break;
      case 'last7days':
        filtered = allFeedItems.filter(item => {
          const itemDate = new Date(item.publishedAt);
          return itemDate >= last7days;
        });
        break;
      case 'all':
      default:
        filtered = [...allFeedItems];
        break;
    }

    setFilteredItems(filtered);
  }, [allFeedItems, timeFilter]);

  // Extract and analyze keywords from filtered feed items
  const keywordData = React.useMemo(() => {
    if (!filteredItems || filteredItems.length === 0) return [];

    const keywordMap = new Map<string, KeywordData>();

    // Process each feed item
    filteredItems.forEach((item) => {
      // Combine title and content for analysis
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      // Get the text content without HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = title + ' ' + content + ' ' + scrapedContent;
      const textContent = tempDiv.textContent || '';

      // Analyze sentiment
      const { counts } = highlightFinancialTerms(textContent);

      // Extract words, convert to lowercase, and remove punctuation
      const words = textContent.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 2 && !COMMON_WORDS.has(word));

      // Count word frequencies
      const wordCounts = new Map<string, number>();
      words.forEach(word => {
        wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
      });

      // Add to keyword map
      wordCounts.forEach((count, word) => {
        if (!keywordMap.has(word)) {
          keywordMap.set(word, {
            keyword: word,
            count: 0,
            positiveCount: 0,
            negativeCount: 0,
            neutralCount: 0,
            articles: []
          });
        }

        const data = keywordMap.get(word)!;
        data.count += count;
        data.positiveCount += counts.positive;
        data.negativeCount += counts.negative;
        data.neutralCount += counts.neutral;

        // Add article index if not already included
        if (!data.articles.includes(item.id)) {
          data.articles.push(item.id);
        }
      });
    });

    // Convert map to array
    let keywords = Array.from(keywordMap.values())
      .filter(data => data.count > 1) // Filter out words that appear only once
      .filter(data => !excludedKeywords.includes(data.keyword.toLowerCase())); // Filter out excluded keywords

    // Apply keyword filter
    switch (keywordFilter) {
      case 'states':
        keywords = keywords.filter(data => US_STATES.has(data.keyword.toLowerCase()));
        break;
      case 'topics':
        keywords = keywords.filter(data => {
          // Check if the keyword is in the important topics list
          // or if it's part of a multi-word topic
          return IMPORTANT_TOPICS.has(data.keyword.toLowerCase()) ||
                 Array.from(IMPORTANT_TOPICS).some(topic =>
                   topic.includes(data.keyword.toLowerCase()));
        });
        break;
      case 'all':
      default:
        // No additional filtering
        break;
    }

    // Sort and limit the number of keywords
    return keywords
      .sort((a, b) => b.count - a.count)
      .slice(0, keywordCount); // Take top N keywords
  }, [filteredItems, keywordFilter, keywordCount, excludedKeywords]);

  // Calculate overall sentiment data
  const sentimentData = React.useMemo(() => {
    if (!filteredItems || filteredItems.length === 0) return [];

    let totalPositive = 0;
    let totalNegative = 0;
    let totalNeutral = 0;

    filteredItems.forEach(item => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = title + ' ' + content + ' ' + scrapedContent;
      const textContent = tempDiv.textContent || '';

      const { counts } = highlightFinancialTerms(textContent);

      totalPositive += counts.positive;
      totalNegative += counts.negative;
      totalNeutral += counts.neutral;
    });

    return [
      { type: 'Positive', count: totalPositive },
      { type: 'Negative', count: totalNegative },
      { type: 'Neutral', count: totalNeutral }
    ];
  }, [filteredItems]);

  // Prepare data for keyword chart
  const keywordChartData = React.useMemo(() => {
    return keywordData.slice(0, 20).map(data => ({
      keyword: data.keyword,
      count: data.count,
      articles: data.articles.length,
      positiveRatio: data.positiveCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0,
      negativeRatio: data.negativeCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0,
      neutralRatio: data.neutralCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0
    }));
  }, [keywordData]);

  // Prepare data for related keywords chart
  const relatedKeywordChartData = React.useMemo(() => {
    if (!relatedKeywords || relatedKeywords.length === 0) return [];

    return relatedKeywords.slice(0, 20).map(data => ({
      keyword: data.keyword,
      count: data.count,
      articles: data.articles.length,
      positiveRatio: data.positiveCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0,
      negativeRatio: data.negativeCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0,
      neutralRatio: data.neutralCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0
    }));
  }, [relatedKeywords]);

  // Prepare data for state keywords chart
  const stateKeywordChartData = React.useMemo(() => {
    if (!stateKeywords || stateKeywords.length === 0) return [];

    return stateKeywords.slice(0, 20).map(data => ({
      keyword: data.keyword,
      count: data.count,
      articles: data.articles.length,
      positiveRatio: data.positiveCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0,
      negativeRatio: data.negativeCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0,
      neutralRatio: data.neutralCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0
    }));
  }, [stateKeywords]);

  // Extract state-specific data
  const stateData = React.useMemo(() => {
    if (!filteredItems || filteredItems.length === 0) return [];

    // Create a map to store data for each state
    const stateMap = new Map<string, KeywordData>();

    // Initialize with all states (even those with no mentions)
    Object.keys(US_STATE_MAP).forEach(stateName => {
      const stateAbbr = US_STATE_MAP[stateName];
      stateMap.set(stateName, {
        keyword: stateName,
        count: 0,
        positiveCount: 0,
        negativeCount: 0,
        neutralCount: 0,
        articles: []
      });
    });

    // Process each feed item
    filteredItems.forEach((item) => {
      // Combine title and content for analysis
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      // Get the text content without HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = title + ' ' + content + ' ' + scrapedContent;
      const textContent = tempDiv.textContent || '';

      // Analyze sentiment
      const { counts } = highlightFinancialTerms(textContent);

      // Extract words, convert to lowercase
      const text = textContent.toLowerCase();

      // Check for each state name and abbreviation
      Object.keys(US_STATE_MAP).forEach(stateName => {
        const stateAbbr = US_STATE_MAP[stateName].toLowerCase();

        // Check for full state name
        const stateNameRegex = new RegExp(`\\b${stateName}\\b`, 'i');
        const stateAbbrRegex = new RegExp(`\\b${stateAbbr}\\b`, 'i');

        if (stateNameRegex.test(text) || stateAbbrRegex.test(text)) {
          const data = stateMap.get(stateName)!;
          data.count += 1;
          data.positiveCount += counts.positive;
          data.negativeCount += counts.negative;
          data.neutralCount += counts.neutral;

          // Add article index if not already included
          if (!data.articles.includes(item.id)) {
            data.articles.push(item.id);
          }
        }
      });
    });

    // Convert map to array, filter out states with no mentions, and sort by count
    return Array.from(stateMap.values())
      .filter(data => data.count > 0)
      .sort((a, b) => b.count - a.count);
  }, [filteredItems]);

  // Prepare data for state chart
  const stateChartData = React.useMemo(() => {
    return stateData.map(data => {
      const stateName = data.keyword.charAt(0).toUpperCase() + data.keyword.slice(1);
      const stateAbbr = US_STATE_MAP[data.keyword];

      // Calculate sentiment values for the stacked bar
      const total = data.positiveCount + data.negativeCount + data.neutralCount || 1;
      const positiveRatio = data.positiveCount / total || 0;
      const negativeRatio = data.negativeCount / total || 0;
      const neutralRatio = data.neutralCount / total || 0;

      return {
        state: `${stateName} (${stateAbbr})`,
        count: data.count,
        articles: data.articles.length,
        positiveRatio,
        negativeRatio,
        neutralRatio,
        // Add absolute values for the stacked bar chart
        positive: Math.round(data.count * positiveRatio),
        negative: Math.round(data.count * negativeRatio),
        neutral: Math.round(data.count * neutralRatio),
        // Add raw counts for tooltips
        positiveCount: data.positiveCount,
        negativeCount: data.negativeCount,
        neutralCount: data.neutralCount
      };
    });
  }, [stateData]);

  return (
    <div className="p-4 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white flex items-center">
          <BarChart className="h-6 w-6 mr-2 text-primary" />
          RSS Feed Analytics Dashboard
        </h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing || isLoading}
            className="flex items-center"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh Data'}
          </Button>
          <Button variant="outline" onClick={() => window.history.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Feeds
          </Button>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <p className="text-white text-lg">
          Analyze trending topics and sentiment across all your RSS feeds
        </p>
        {lastRefreshed && (
          <div className="flex items-center text-sm text-muted-foreground">
            <Clock className="h-4 w-4 mr-1" />
            Last refreshed: {lastRefreshed.toLocaleString()}
          </div>
        )}
      </div>

      {/* Filter controls */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-sm flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Time Period
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Select
              value={timeFilter}
              onValueChange={(value) => setTimeFilter(value as TimeFilter)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select time period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="yesterday">Yesterday</SelectItem>
                <SelectItem value="last7days">Last 7 Days</SelectItem>
              </SelectContent>
            </Select>
            <div className="mt-2 text-xs text-muted-foreground">
              {timeFilter === 'all' ? (
                <span>Showing all {allFeedItems.length} articles</span>
              ) : (
                <span>Showing {filteredItems.length} of {allFeedItems.length} articles</span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-sm flex items-center">
              <Filter className="h-4 w-4 mr-2" />
              Keyword Filter
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Select
              value={keywordFilter}
              onValueChange={(value) => setKeywordFilter(value as KeywordFilter)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Filter keywords" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Keywords</SelectItem>
                <SelectItem value="states">US States</SelectItem>
                <SelectItem value="topics">Important Topics</SelectItem>
              </SelectContent>
            </Select>
            <div className="mt-2 text-xs text-muted-foreground">
              {keywordFilter === 'states' ? (
                <span>Showing only US state keywords</span>
              ) : keywordFilter === 'topics' ? (
                <span>Showing only important topic keywords</span>
              ) : (
                <span>Showing all keywords</span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-sm flex items-center">
              <BarChart className="h-4 w-4 mr-2" />
              Display Options
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="keyword-count">Keywords to show: {keywordCount}</Label>
                </div>
                <Slider
                  id="keyword-count"
                  min={10}
                  max={200}
                  step={10}
                  value={[keywordCount]}
                  onValueChange={(value) => setKeywordCount(value[0])}
                />
              </div>

              <div className="space-y-2 pt-2 border-t border-border">
                <Label htmlFor="excluded-keywords">Exclude Keywords</Label>
                <div className="flex gap-2">
                  <Input
                    id="excluded-keywords"
                    value={newExcludedKeyword}
                    onChange={(e) => setNewExcludedKeyword(e.target.value)}
                    placeholder="Enter keyword to exclude"
                    className="flex-1"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddExcludedKeyword();
                      }
                    }}
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleAddExcludedKeyword}
                    disabled={!newExcludedKeyword.trim()}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                {excludedKeywords.length > 0 && (
                  <div className="mt-2">
                    <Label className="text-xs text-muted-foreground mb-1 block">
                      Excluded Keywords ({excludedKeywords.length}):
                    </Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {excludedKeywords.map((keyword) => (
                        <Badge key={keyword} variant="secondary" className="flex items-center gap-1">
                          {keyword}
                          <button
                            onClick={() => handleRemoveExcludedKeyword(keyword)}
                            className="text-muted-foreground hover:text-foreground transition-colors"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      ) : (
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="keywords">Top Keywords</TabsTrigger>
            <TabsTrigger value="states">US States</TabsTrigger>
            <TabsTrigger value="sentiment">Sentiment Analysis</TabsTrigger>
            <TabsTrigger value="trends">Trending Topics</TabsTrigger>
          </TabsList>

          <TabsContent value="keywords" className="space-y-4">
            {showRelatedKeywords && selectedKeyword ? (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle className="text-white flex items-center">
                      Related Keywords for "{selectedKeyword}"
                    </CardTitle>
                    <CardDescription className="text-white">
                      Long-tail keywords and phrases related to "{selectedKeyword}"
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCloseRelatedKeywords}
                    className="ml-auto"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to All Keywords
                  </Button>
                </CardHeader>
                <CardContent>
                  {relatedKeywords.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-[200px] text-center">
                      <p className="text-white text-lg mb-2">No related keywords found</p>
                      <p className="text-muted-foreground">Try selecting a different keyword or adjusting your filters</p>
                    </div>
                  ) : (
                    <div className="h-[500px]">
                      <ChartContainer
                        config={{
                          count: { color: "#3b82f6" },
                          articles: { color: "#10b981" },
                          positive: { color: "#4ade80" },
                          negative: { color: "#f87171" },
                          neutral: { color: "#94a3b8" }
                        }}
                      >
                        <RechartsPrimitive.BarChart
                          data={relatedKeywordChartData}
                          layout="vertical"
                          margin={{ top: 20, right: 30, left: 150, bottom: 20 }}
                        >
                          <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                          <RechartsPrimitive.XAxis type="number" />
                          <RechartsPrimitive.YAxis
                            type="category"
                            dataKey="keyword"
                            width={150}
                            tick={{ fontSize: 12 }}
                          />
                          <RechartsPrimitive.Tooltip
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                const data = payload[0].payload;
                                return (
                                  <div className="bg-background border border-border p-2 rounded-md shadow-md">
                                    <p className="font-bold text-white">{data.keyword}</p>
                                    <p className="text-sm text-white">Occurrences: {data.count}</p>
                                    <p className="text-sm text-white">Articles: {data.articles}</p>
                                    <div className="mt-2">
                                      <p className="text-xs text-white">Sentiment:</p>
                                      <div className="flex items-center mt-1">
                                        <div className="w-2 h-2 rounded-full bg-green-400 mr-1"></div>
                                        <p className="text-xs text-white">Positive: {Math.round(data.positiveRatio * 100)}%</p>
                                      </div>
                                      <div className="flex items-center mt-1">
                                        <div className="w-2 h-2 rounded-full bg-red-400 mr-1"></div>
                                        <p className="text-xs text-white">Negative: {Math.round(data.negativeRatio * 100)}%</p>
                                      </div>
                                      <div className="flex items-center mt-1">
                                        <div className="w-2 h-2 rounded-full bg-gray-400 mr-1"></div>
                                        <p className="text-xs text-white">Neutral: {Math.round(data.neutralRatio * 100)}%</p>
                                      </div>
                                    </div>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                          <RechartsPrimitive.Legend />
                          <RechartsPrimitive.Bar
                            dataKey="count"
                            name="Occurrences"
                            fill="var(--color-count)"
                            // Custom bar to show sentiment as color
                            shape={(props) => {
                              const { x, y, width, height, payload } = props;
                              const positiveRatio = payload.positiveRatio;
                              const negativeRatio = payload.negativeRatio;
                              const neutralRatio = payload.neutralRatio;

                              // Determine dominant sentiment
                              let fillColor = "#3b82f6"; // Default blue
                              if (positiveRatio > negativeRatio && positiveRatio > neutralRatio) {
                                fillColor = "#4ade80"; // Green for positive
                              } else if (negativeRatio > positiveRatio && negativeRatio > neutralRatio) {
                                fillColor = "#f87171"; // Red for negative
                              } else if (neutralRatio > positiveRatio && neutralRatio > negativeRatio) {
                                fillColor = "#94a3b8"; // Gray for neutral
                              }

                              return (
                                <g>
                                  <rect x={x} y={y} width={width} height={height} fill={fillColor} />

                                  {/* Add sentiment indicators */}
                                  <rect x={x + width - 6} y={y} width={6} height={height * positiveRatio} fill="#4ade80" />
                                  <rect x={x + width - 6} y={y + height * positiveRatio} width={6} height={height * negativeRatio} fill="#f87171" />
                                  <rect
                                    x={x + width - 6}
                                    y={y + height * positiveRatio + height * negativeRatio}
                                    width={6}
                                    height={height * neutralRatio}
                                    fill="#94a3b8"
                                  />
                                </g>
                              );
                            }}
                          />
                          <RechartsPrimitive.Bar dataKey="articles" name="Articles" fill="var(--color-articles)" />
                        </RechartsPrimitive.BarChart>
                      </ChartContainer>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="text-white">Keyword Frequency</CardTitle>
                  <CardDescription className="text-white">
                    Most common keywords across all RSS feeds (click on any keyword to see related phrases)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[500px]">
                    <ChartContainer
                      config={{
                        count: { color: "#3b82f6" },
                        articles: { color: "#10b981" },
                        positive: { color: "#4ade80" },
                        negative: { color: "#f87171" },
                        neutral: { color: "#94a3b8" }
                      }}
                    >
                      <RechartsPrimitive.BarChart
                        data={keywordChartData}
                        layout="vertical"
                        margin={{ top: 20, right: 30, left: 100, bottom: 20 }}
                      >
                        <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                        <RechartsPrimitive.XAxis type="number" />
                        <RechartsPrimitive.YAxis
                          type="category"
                          dataKey="keyword"
                          width={100}
                          tick={(props) => {
                            const { x, y, payload } = props;
                            return (
                              <g transform={`translate(${x},${y})`}>
                                <text
                                  x={-3}
                                  y={0}
                                  dy={4}
                                  textAnchor="end"
                                  fill="#fff"
                                  fontSize={12}
                                  className="cursor-pointer hover:font-bold hover:fill-primary transition-all"
                                  onClick={() => handleKeywordSelect(payload.value)}
                                >
                                  {payload.value}
                                </text>
                              </g>
                            );
                          }}
                        />
                        <RechartsPrimitive.Tooltip
                          content={({ active, payload }) => {
                            if (active && payload && payload.length) {
                              const data = payload[0].payload;
                              return (
                                <div className="bg-background border border-border p-2 rounded-md shadow-md">
                                  <p className="font-bold text-white">{data.keyword}</p>
                                  <p className="text-sm text-white">Occurrences: {data.count}</p>
                                  <p className="text-sm text-white">Articles: {data.articles}</p>
                                  <div className="mt-2">
                                    <p className="text-xs text-white">Sentiment:</p>
                                    <div className="flex items-center mt-1">
                                      <div className="w-2 h-2 rounded-full bg-green-400 mr-1"></div>
                                      <p className="text-xs text-white">Positive: {Math.round(data.positiveRatio * 100)}%</p>
                                    </div>
                                    <div className="flex items-center mt-1">
                                      <div className="w-2 h-2 rounded-full bg-red-400 mr-1"></div>
                                      <p className="text-xs text-white">Negative: {Math.round(data.negativeRatio * 100)}%</p>
                                    </div>
                                    <div className="flex items-center mt-1">
                                      <div className="w-2 h-2 rounded-full bg-gray-400 mr-1"></div>
                                      <p className="text-xs text-white">Neutral: {Math.round(data.neutralRatio * 100)}%</p>
                                    </div>
                                  </div>
                                  <div className="mt-2 pt-2 border-t border-border">
                                    <button
                                      onClick={() => handleKeywordSelect(data.keyword)}
                                      className="text-xs text-primary hover:text-primary/80 transition-colors"
                                    >
                                      Click to see related keywords
                                    </button>
                                  </div>
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <RechartsPrimitive.Legend />
                        <RechartsPrimitive.Bar
                          dataKey="count"
                          name="Occurrences"
                          fill="var(--color-count)"
                          // Custom bar to show sentiment as color
                          shape={(props) => {
                            const { x, y, width, height, payload } = props;
                            const positiveRatio = payload.positiveRatio;
                            const negativeRatio = payload.negativeRatio;
                            const neutralRatio = payload.neutralRatio;

                            // Determine dominant sentiment
                            let fillColor = "#3b82f6"; // Default blue
                            if (positiveRatio > negativeRatio && positiveRatio > neutralRatio) {
                              fillColor = "#4ade80"; // Green for positive
                            } else if (negativeRatio > positiveRatio && negativeRatio > neutralRatio) {
                              fillColor = "#f87171"; // Red for negative
                            } else if (neutralRatio > positiveRatio && neutralRatio > negativeRatio) {
                              fillColor = "#94a3b8"; // Gray for neutral
                            }

                            return (
                              <g>
                                <rect
                                  x={x}
                                  y={y}
                                  width={width}
                                  height={height}
                                  fill={fillColor}
                                  className="cursor-pointer hover:opacity-80 transition-opacity"
                                  onClick={() => handleKeywordSelect(payload.keyword)}
                                />

                                {/* Add sentiment indicators */}
                                <rect x={x + width - 6} y={y} width={6} height={height * positiveRatio} fill="#4ade80" />
                                <rect x={x + width - 6} y={y + height * positiveRatio} width={6} height={height * negativeRatio} fill="#f87171" />
                                <rect
                                  x={x + width - 6}
                                  y={y + height * positiveRatio + height * negativeRatio}
                                  width={6}
                                  height={height * neutralRatio}
                                  fill="#94a3b8"
                                />
                              </g>
                            );
                          }}
                        />
                        <RechartsPrimitive.Bar
                          dataKey="articles"
                          name="Articles"
                          fill="var(--color-articles)"
                          onClick={(data) => handleKeywordSelect(data.keyword)}
                          className="cursor-pointer"
                        />
                      </RechartsPrimitive.BarChart>
                    </ChartContainer>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="states" className="space-y-4">
            {showStateKeywords && selectedState ? (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle className="text-white flex items-center">
                      Trending Keywords in {selectedState}
                    </CardTitle>
                    <CardDescription className="text-white">
                      Most common keywords in articles mentioning {selectedState}
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCloseStateKeywords}
                    className="ml-auto"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to All States
                  </Button>
                </CardHeader>
                <CardContent>
                  {stateKeywords.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-[200px] text-center">
                      <p className="text-white text-lg mb-2">No trending keywords found for {selectedState}</p>
                      <p className="text-muted-foreground">Try selecting a different state or adjusting your filters</p>
                    </div>
                  ) : (
                    <div className="h-[500px]">
                      <ChartContainer
                        config={{
                          count: { color: "#3b82f6" },
                          articles: { color: "#10b981" },
                          positive: { color: "#4ade80" },
                          negative: { color: "#f87171" },
                          neutral: { color: "#94a3b8" }
                        }}
                      >
                        <RechartsPrimitive.BarChart
                          data={stateKeywordChartData}
                          layout="vertical"
                          margin={{ top: 20, right: 30, left: 150, bottom: 20 }}
                        >
                          <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                          <RechartsPrimitive.XAxis type="number" />
                          <RechartsPrimitive.YAxis
                            type="category"
                            dataKey="keyword"
                            width={150}
                            tick={{ fontSize: 12 }}
                          />
                          <RechartsPrimitive.Tooltip
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                const data = payload[0].payload;
                                return (
                                  <div className="bg-background border border-border p-2 rounded-md shadow-md">
                                    <p className="font-bold text-white">{data.keyword}</p>
                                    <p className="text-sm text-white">Occurrences: {data.count}</p>
                                    <p className="text-sm text-white">Articles: {data.articles}</p>
                                    <div className="mt-2">
                                      <p className="text-xs text-white">Sentiment:</p>
                                      <div className="flex items-center mt-1">
                                        <div className="w-2 h-2 rounded-full bg-green-400 mr-1"></div>
                                        <p className="text-xs text-white">Positive: {Math.round(data.positiveRatio * 100)}%</p>
                                      </div>
                                      <div className="flex items-center mt-1">
                                        <div className="w-2 h-2 rounded-full bg-red-400 mr-1"></div>
                                        <p className="text-xs text-white">Negative: {Math.round(data.negativeRatio * 100)}%</p>
                                      </div>
                                      <div className="flex items-center mt-1">
                                        <div className="w-2 h-2 rounded-full bg-gray-400 mr-1"></div>
                                        <p className="text-xs text-white">Neutral: {Math.round(data.neutralRatio * 100)}%</p>
                                      </div>
                                    </div>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                          <RechartsPrimitive.Legend />
                          <RechartsPrimitive.Bar
                            dataKey="count"
                            name="Occurrences"
                            fill="var(--color-count)"
                            // Custom bar to show sentiment as color
                            shape={(props) => {
                              const { x, y, width, height, payload } = props;
                              const positiveRatio = payload.positiveRatio;
                              const negativeRatio = payload.negativeRatio;
                              const neutralRatio = payload.neutralRatio;

                              // Determine dominant sentiment
                              let fillColor = "#3b82f6"; // Default blue
                              if (positiveRatio > negativeRatio && positiveRatio > neutralRatio) {
                                fillColor = "#4ade80"; // Green for positive
                              } else if (negativeRatio > positiveRatio && negativeRatio > neutralRatio) {
                                fillColor = "#f87171"; // Red for negative
                              } else if (neutralRatio > positiveRatio && neutralRatio > negativeRatio) {
                                fillColor = "#94a3b8"; // Gray for neutral
                              }

                              return (
                                <g>
                                  <rect x={x} y={y} width={width} height={height} fill={fillColor} />

                                  {/* Add sentiment indicators */}
                                  <rect x={x + width - 6} y={y} width={6} height={height * positiveRatio} fill="#4ade80" />
                                  <rect x={x + width - 6} y={y + height * positiveRatio} width={6} height={height * negativeRatio} fill="#f87171" />
                                  <rect
                                    x={x + width - 6}
                                    y={y + height * positiveRatio + height * negativeRatio}
                                    width={6}
                                    height={height * neutralRatio}
                                    fill="#94a3b8"
                                  />
                                </g>
                              );
                            }}
                          />
                          <RechartsPrimitive.Bar dataKey="articles" name="Articles" fill="var(--color-articles)" />
                        </RechartsPrimitive.BarChart>
                      </ChartContainer>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="text-white">US States Mentioned in Articles</CardTitle>
                  <CardDescription className="text-white">
                    Frequency and sentiment analysis of US states mentioned in articles (click on any state to see trending keywords)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {stateData.length === 0 ? (
                    <div className="flex justify-center items-center h-[200px]">
                      <p className="text-white text-lg">No state mentions found in the current time period</p>
                    </div>
                  ) : (
                    <div className="h-[600px] overflow-hidden">
                      <ChartContainer
                        config={{
                          count: { color: "#3b82f6" },
                          articles: { color: "#10b981" },
                          positive: { color: "#4ade80" },
                          negative: { color: "#f87171" },
                          neutral: { color: "#94a3b8" }
                        }}
                      >
                        <RechartsPrimitive.ResponsiveContainer width="100%" height={600}>
                          <RechartsPrimitive.BarChart
                            data={stateChartData}
                            layout="vertical"
                            margin={{ top: 20, right: 30, left: 120, bottom: 20 }}
                            barGap={2}
                            barSize={10}
                          >
                            <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                            <RechartsPrimitive.XAxis type="number" />
                            <RechartsPrimitive.YAxis
                              type="category"
                              dataKey="state"
                              width={120}
                              tick={(props) => {
                                const { x, y, payload } = props;
                                return (
                                  <g transform={`translate(${x},${y})`}>
                                    <text
                                      x={-3}
                                      y={0}
                                      dy={4}
                                      textAnchor="end"
                                      fill="#fff"
                                      fontSize={12}
                                      className="cursor-pointer hover:font-bold hover:fill-primary transition-all"
                                      onClick={() => handleStateSelect(payload.value)}
                                    >
                                      {payload.value}
                                    </text>
                                  </g>
                                );
                              }}
                            />
                            <RechartsPrimitive.Tooltip
                              content={({ active, payload }) => {
                                if (active && payload && payload.length) {
                                  const data = payload[0].payload;

                                  // Determine which bar is being hovered
                                  const barName = payload[0].name;

                                  return (
                                    <div className="bg-background border border-border p-3 rounded-md shadow-md">
                                      <p className="font-bold text-white text-lg">{data.state}</p>

                                      <div className="grid grid-cols-2 gap-x-4 gap-y-2 mt-2">
                                        <p className="text-sm text-white">Mentions:</p>
                                        <p className="text-sm text-white font-bold">{data.count}</p>

                                        <p className="text-sm text-white">Articles:</p>
                                        <p className="text-sm text-white font-bold">{data.articles}</p>
                                      </div>

                                      <div className="mt-3 border-t border-border pt-2">
                                        <p className="text-sm text-white font-medium mb-2">Sentiment Breakdown:</p>

                                        <div className="grid grid-cols-3 gap-2">
                                          <div className={`p-2 rounded ${barName === 'Positive Sentiment' ? 'bg-green-900/30 ring-1 ring-green-400' : ''}`}>
                                            <p className="text-xs text-white text-center">Positive</p>
                                            <p className="text-sm text-green-400 font-bold text-center">{Math.round(data.positiveRatio * 100)}%</p>
                                            <p className="text-xs text-white text-center">({data.positiveCount} terms)</p>
                                          </div>

                                          <div className={`p-2 rounded ${barName === 'Negative Sentiment' ? 'bg-red-900/30 ring-1 ring-red-400' : ''}`}>
                                            <p className="text-xs text-white text-center">Negative</p>
                                            <p className="text-sm text-red-400 font-bold text-center">{Math.round(data.negativeRatio * 100)}%</p>
                                            <p className="text-xs text-white text-center">({data.negativeCount} terms)</p>
                                          </div>

                                          <div className={`p-2 rounded ${barName === 'Neutral Sentiment' ? 'bg-gray-700/50 ring-1 ring-gray-400' : ''}`}>
                                            <p className="text-xs text-white text-center">Neutral</p>
                                            <p className="text-sm text-gray-400 font-bold text-center">{Math.round(data.neutralRatio * 100)}%</p>
                                            <p className="text-xs text-white text-center">({data.neutralCount} terms)</p>
                                          </div>
                                        </div>
                                      </div>

                                      <div className="mt-3 pt-2 border-t border-border">
                                        <button
                                          onClick={() => handleStateSelect(data.state)}
                                          className="text-xs text-primary hover:text-primary/80 transition-colors"
                                        >
                                          Click to see trending keywords in {data.state}
                                        </button>
                                      </div>
                                    </div>
                                  );
                                }
                                return null;
                              }}
                            />
                            <RechartsPrimitive.Legend />

                            {/* First bar: Mentions count */}
                            <RechartsPrimitive.Bar
                              dataKey="count"
                              name="Mentions"
                              fill="#3b82f6"
                              radius={[4, 4, 4, 4]}
                              onClick={(data) => handleStateSelect(data.state)}
                              className="cursor-pointer"
                            />

                            {/* Second bar: Articles count */}
                            <RechartsPrimitive.Bar
                              dataKey="articles"
                              name="Articles"
                              fill="#10b981"
                              radius={[4, 4, 4, 4]}
                              onClick={(data) => handleStateSelect(data.state)}
                              className="cursor-pointer"
                            />

                            {/* Third bar: Sentiment breakdown */}
                            <RechartsPrimitive.Bar
                              dataKey="positive"
                              name="Positive Sentiment"
                              stackId="sentiment"
                              fill="#4ade80"
                              radius={[4, 0, 0, 4]}
                              onClick={(data) => handleStateSelect(data.state)}
                              className="cursor-pointer"
                            />

                            <RechartsPrimitive.Bar
                              dataKey="negative"
                              name="Negative Sentiment"
                              stackId="sentiment"
                              fill="#f87171"
                              onClick={(data) => handleStateSelect(data.state)}
                              className="cursor-pointer"
                            />

                            <RechartsPrimitive.Bar
                              dataKey="neutral"
                              name="Neutral Sentiment"
                              stackId="sentiment"
                              fill="#94a3b8"
                              radius={[0, 4, 4, 0]}
                              onClick={(data) => handleStateSelect(data.state)}
                              className="cursor-pointer"
                            />

                          </RechartsPrimitive.BarChart>
                        </RechartsPrimitive.ResponsiveContainer>
                      </ChartContainer>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="sentiment" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-white">Sentiment Distribution</CardTitle>
                <CardDescription className="text-white">
                  Distribution of positive, negative, and neutral financial terms
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[500px]">
                  <ChartContainer
                    config={{
                      positive: { color: "#4ade80" },
                      negative: { color: "#f87171" },
                      neutral: { color: "#94a3b8" }
                    }}
                  >
                    <RechartsPrimitive.PieChart>
                      <RechartsPrimitive.Pie
                        data={sentimentData}
                        dataKey="count"
                        nameKey="type"
                        cx="50%"
                        cy="50%"
                        outerRadius={200}
                        fill="#8884d8"
                        label
                      >
                        {sentimentData.map((entry, index) => {
                          let color = "#94a3b8"; // Default neutral color
                          if (entry.type === "Positive") color = "#4ade80";
                          if (entry.type === "Negative") color = "#f87171";
                          return <RechartsPrimitive.Cell key={`cell-${index}`} fill={color} />;
                        })}
                      </RechartsPrimitive.Pie>
                      <RechartsPrimitive.Tooltip />
                      <RechartsPrimitive.Legend />
                    </RechartsPrimitive.PieChart>
                  </ChartContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-white">Top Positive Topics</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[500px]">
                    <div className="space-y-2">
                      {keywordData
                        .filter(data => data.positiveCount > data.negativeCount)
                        .sort((a, b) => b.positiveCount - a.positiveCount)
                        .slice(0, 20)
                        .map((data, index) => (
                          <div key={index} className="flex justify-between items-center p-2 border-b border-border">
                            <span className="text-white">{data.keyword}</span>
                            <div className="flex items-center">
                              <span className="text-green-400 font-medium mr-2">{data.positiveCount}</span>
                              <span className="text-xs text-muted-foreground">
                                ({Math.round((data.positiveCount / (data.positiveCount + data.negativeCount + data.neutralCount)) * 100)}%)
                              </span>
                            </div>
                          </div>
                        ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-white">Top Negative Topics</CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[500px]">
                    <div className="space-y-2">
                      {keywordData
                        .filter(data => data.negativeCount > data.positiveCount)
                        .sort((a, b) => b.negativeCount - a.negativeCount)
                        .slice(0, 20)
                        .map((data, index) => (
                          <div key={index} className="flex justify-between items-center p-2 border-b border-border">
                            <span className="text-white">{data.keyword}</span>
                            <div className="flex items-center">
                              <span className="text-red-400 font-medium mr-2">{data.negativeCount}</span>
                              <span className="text-xs text-muted-foreground">
                                ({Math.round((data.negativeCount / (data.positiveCount + data.negativeCount + data.neutralCount)) * 100)}%)
                              </span>
                            </div>
                          </div>
                        ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
