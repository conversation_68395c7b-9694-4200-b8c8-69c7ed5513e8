#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Read the file
const filePath = path.join('src', 'pages', 'youtube-page.tsx');
const content = fs.readFileSync(filePath, 'utf8');

// Find the position of the financial analysis dialog
const dialogStartIndex = content.indexOf('{/* Financial Analysis Dialog */}');
const dialogEndIndex = content.indexOf('</Dialog>', dialogStartIndex);
const dialogEndLineIndex = content.indexOf('\n', dialogEndIndex) + 1;

// Extract the dialog content
const dialogContent = content.substring(dialogStartIndex, dialogEndLineIndex);

// Remove the dialog from the original content
let newContent = content.substring(0, dialogStartIndex - 1) + 
                content.substring(dialogEndLineIndex);

// Find the position to insert the dialog component
const componentEndIndex = newContent.lastIndexOf('export default function YoutubePage');
const componentEndPosition = newContent.indexOf('}', componentEndIndex);

// Create the dialog component
const dialogComponent = `
// Financial Analysis Dialog
function FinancialAnalysisDialog({ isOpen, onOpenChange, currentVideo, transcript, analysisResults }) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Financial Term Analysis
            {currentVideo && (
              <Badge variant="outline" className="ml-2 text-xs">
                {currentVideo.title}
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Financial terms are highlighted: <span className="text-green-400">positive</span>, <span className="text-red-400">negative</span>, <span className="text-gray-400">neutral</span>
          </DialogDescription>
        </DialogHeader>
        
        {/* Financial Analysis Summary */}
        <div className="flex items-center gap-4 mb-4 p-2 border border-border rounded">
          <div className="text-sm font-medium">Financial Term Analysis:</div>
          <div className="flex gap-4">
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-green-400"></div>
              <span className="text-white">Positive: </span>
              <span className="font-bold text-green-400">{analysisResults.counts.positive}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-red-400"></div>
              <span className="text-white">Negative: </span>
              <span className="font-bold text-red-400">{analysisResults.counts.negative}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-gray-400"></div>
              <span className="text-white">Neutral: </span>
              <span className="font-bold text-gray-400">{analysisResults.counts.neutral}</span>
            </div>
          </div>
        </div>
        
        {/* Transcript Content */}
        <ScrollArea className="h-[55vh]">
          {analysisResults.highlightedContent ? (
            <div 
              className="p-4 whitespace-pre-wrap"
              dangerouslySetInnerHTML={{ __html: analysisResults.highlightedContent }}
            />
          ) : (
            <div className="p-4 whitespace-pre-wrap">
              {transcript}
            </div>
          )}
        </ScrollArea>
        
        <DialogFooter>
          <Button 
            onClick={() => onOpenChange(false)}
            variant="outline"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
`;

// Find the position to insert the dialog component call
const returnStartIndex = newContent.indexOf('return (', componentEndIndex);
const returnEndIndex = newContent.indexOf('</div>', returnStartIndex);
const returnEndLineIndex = newContent.indexOf('\n', returnEndIndex) + 1;

// Insert the dialog component call
const beforeReturn = newContent.substring(0, returnEndLineIndex);
const afterReturn = newContent.substring(returnEndLineIndex);

// Create the final content
const finalContent = beforeReturn + 
                    `    {/* Render Financial Analysis Dialog */}
    <FinancialAnalysisDialog 
      isOpen={isFinancialAnalysisDialogOpen}
      onOpenChange={setIsFinancialAnalysisDialogOpen}
      currentVideo={currentVideoForAnalysis}
      transcript={currentTranscriptForAnalysis}
      analysisResults={financialAnalysisResults}
    />` + 
                    afterReturn;

// Insert the dialog component
const finalContentWithComponent = finalContent.substring(0, componentEndPosition + 1) + 
                                 dialogComponent + 
                                 finalContent.substring(componentEndPosition + 1);

// Write the file
fs.writeFileSync(filePath, finalContentWithComponent, 'utf8');

console.log('Fixed youtube-page.tsx');
