// New countdown timer implementation
useEffect(() => {
  // Always clear any existing countdown timer first to prevent duplicates
  if (countdownTimerRef.current) {
    clearInterval(countdownTimerRef.current);
    countdownTimerRef.current = null;
    logger.verbose("Cleared existing countdown timer");
  }

  // Only set up timer if auto-refresh is enabled and we're on the active tab
  if (!autoRefreshEnabled || !isActive || isPaused) {
    logger.verbose("Not setting up countdown timer - conditions not met");
    setCountdownDisplay("--:--");
    return;
  }

  logger.verbose("Setting up new countdown display timer");

  // Calculate the next refresh time based on the current time and interval
  const calculateNextRefreshTime = () => {
    const now = new Date();
    const nextTime = new Date(now.getTime() + autoRefreshInterval * 1000);
    return nextTime;
  };

  // Only set the next refresh time if it's not already set or if a refresh is in progress
  if (!nextRefreshTime || refreshInProgressRef.current) {
    const newNextRefreshTime = calculateNextRefreshTime();
    setNextRefreshTime(newNextRefreshTime);
    logger.verbose(`Setting next refresh time to: ${newNextRefreshTime.toLocaleTimeString()}`);

    // Also update the countdown display immediately
    const totalSeconds = autoRefreshInterval;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    setCountdownDisplay(display);
  }

  // Function to update the countdown display
  const updateCountdown = () => {
    try {        
      // Safety check - if nextRefreshTime is not set, set it now
      if (!nextRefreshTime) {
        const newNextRefreshTime = calculateNextRefreshTime();
        setNextRefreshTime(newNextRefreshTime);
        logger.verbose(`Setting missing next refresh time to: ${newNextRefreshTime.toLocaleTimeString()}`);
        return; // Return early and let the next tick handle the countdown
      }

      const now = new Date();
      const diffMs = nextRefreshTime.getTime() - now.getTime();

      if (diffMs <= 0) {          
        // Just update the display - the actual refresh will be triggered by the main timer
        const newNextRefreshTime = calculateNextRefreshTime();
        setNextRefreshTime(newNextRefreshTime);
        logger.verbose(`Setting next refresh time to: ${newNextRefreshTime.toLocaleTimeString()}`);

        // Update the countdown display immediately
        const totalSeconds = autoRefreshInterval;
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        setCountdownDisplay(display);

        return;
      }

      // Calculate minutes and seconds
      const totalSeconds = Math.floor(diffMs / 1000);
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;

      // Format the countdown display (MM:SS)
      const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      setCountdownDisplay(display);

      // Log the countdown tick for debugging (but not too frequently to avoid log spam)
      if (seconds % 10 === 0 || seconds <= 5) {
        logger.verbose(`Countdown tick: ${display} (${totalSeconds}s remaining)`);
      }
    } catch (error) {
      logger.error("Error in countdown timer:", error);
    }
  };

  // Update the countdown immediately
  setTimeout(updateCountdown, 0);

  // Set up a timer to update the countdown every second
  const timer = setInterval(updateCountdown, 1000);
  countdownTimerRef.current = timer;
  logger.verbose(`Set up new countdown timer with ID: ${timer}`);

  // Clean up on unmount or when dependencies change
  return () => {
    logger.verbose("Cleaning up countdown timer");
    clearInterval(timer);
    countdownTimerRef.current = null;
  };
}, [autoRefreshEnabled, autoRefreshInterval, isActive, isPaused, nextRefreshTime]);
