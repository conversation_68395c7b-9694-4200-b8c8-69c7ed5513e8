import React, { useMemo } from 'react';
import { RssFeedItem } from '@shared/schema';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import * as RechartsPrimitive from 'recharts';
import { ChartContainer } from '@/components/ui/chart';
import { format, startOfDay, startOfWeek, addDays, addHours, isSameDay, isSameHour } from 'date-fns';
import { highlightFinancialTerms } from '../financial-term-highlighter';

interface TemporalAnalysisProps {
  feedItems: RssFeedItem[];
  timeRange?: 'day' | 'week' | 'month';
  onOpenArticle?: (articleId: number) => void;
  onSaveArticle?: (articleId: number) => void;
  onOpenInBrowser?: (articleId: number) => void;
}

interface HourlyData {
  hour: string;
  count: number;
  positive: number;
  negative: number;
  neutral: number;
}

interface DailyData {
  day: string;
  count: number;
  positive: number;
  negative: number;
  neutral: number;
}

export function TemporalAnalysis({
  feedItems,
  timeRange = 'week',
  onOpenArticle,
  onSaveArticle,
  onOpenInBrowser
}: TemporalAnalysisProps) {
  // Generate publication time heatmap data
  const hourlyData = useMemo(() => {
    if (!feedItems || feedItems.length === 0) return [];

    // Initialize data for each hour (0-23)
    const hourlyMap = new Map<number, HourlyData>();
    for (let i = 0; i < 24; i++) {
      hourlyMap.set(i, {
        hour: `${i}:00`,
        count: 0,
        positive: 0,
        negative: 0,
        neutral: 0
      });
    }

    // Process each feed item
    feedItems.forEach(item => {
      if (!item.publishedAt) return;

      const publishDate = new Date(item.publishedAt);
      const hour = publishDate.getHours();

      // Get content and strip HTML tags
      const content = item.content || '';
      const strippedContent = content
        .replace(/<[^>]*>/g, ' ') // Remove HTML tags
        .replace(/&[a-z]+;/g, ' ') // Remove HTML entities like &nbsp;
        .replace(/style="[^"]*"/g, ' ') // Remove style attributes
        .replace(/class="[^"]*"/g, ' ') // Remove class attributes
        .replace(/data-[a-z-]+="[^"]*"/g, ' '); // Remove data attributes

      // Analyze sentiment
      const { counts } = highlightFinancialTerms(strippedContent);

      // Update hourly data
      const data = hourlyMap.get(hour)!;
      data.count += 1;
      data.positive += counts.positive;
      data.negative += counts.negative;
      data.neutral += counts.neutral;
    });

    // Convert map to array and format for chart
    return Array.from(hourlyMap.values())
      .map(data => ({
        ...data,
        hour: `${data.hour}`
      }));
  }, [feedItems]);

  // Generate daily publication data
  const dailyData = useMemo(() => {
    if (!feedItems || feedItems.length === 0) return [];

    // Determine date range based on timeRange
    const now = new Date();
    let startDate: Date;

    if (timeRange === 'day') {
      startDate = startOfDay(now);
    } else if (timeRange === 'week') {
      startDate = startOfWeek(now);
    } else {
      // Month - go back 30 days
      startDate = startOfDay(new Date(now));
      startDate.setDate(startDate.getDate() - 30);
    }

    // Initialize data for each day in the range
    const dailyMap = new Map<string, DailyData>();
    let currentDate = startDate;

    while (currentDate <= now) {
      const dateKey = format(currentDate, 'yyyy-MM-dd');
      dailyMap.set(dateKey, {
        day: format(currentDate, 'MMM dd'),
        count: 0,
        positive: 0,
        negative: 0,
        neutral: 0
      });

      currentDate = addDays(currentDate, 1);
    }

    // Process each feed item
    feedItems.forEach(item => {
      if (!item.publishedAt) return;

      const publishDate = new Date(item.publishedAt);
      const dateKey = format(publishDate, 'yyyy-MM-dd');

      // Skip if outside our date range
      if (!dailyMap.has(dateKey)) return;

      // Get content and strip HTML tags
      const content = item.content || '';
      const strippedContent = content
        .replace(/<[^>]*>/g, ' ') // Remove HTML tags
        .replace(/&[a-z]+;/g, ' ') // Remove HTML entities like &nbsp;
        .replace(/style="[^"]*"/g, ' ') // Remove style attributes
        .replace(/class="[^"]*"/g, ' ') // Remove class attributes
        .replace(/data-[a-z-]+="[^"]*"/g, ' '); // Remove data attributes

      // Analyze sentiment
      const { counts } = highlightFinancialTerms(strippedContent);

      // Update daily data
      const data = dailyMap.get(dateKey)!;
      data.count += 1;
      data.positive += counts.positive;
      data.negative += counts.negative;
      data.neutral += counts.neutral;
    });

    // Convert map to array and sort by date
    return Array.from(dailyMap.values())
      .sort((a, b) => {
        const dateA = new Date(a.day);
        const dateB = new Date(b.day);
        return dateA.getTime() - dateB.getTime();
      });
  }, [feedItems, timeRange]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-white">Temporal Analysis</CardTitle>
        <CardDescription className="text-white">
          Publication patterns by time of day and day of week
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-12">
          {/* Hourly Publication Heatmap */}
          <div>
            <h3 className="text-white text-lg mb-4">Publication Time Heatmap</h3>
            <div className="h-[300px]">
              <ChartContainer
                config={{
                  count: { color: "#3b82f6" },
                  positive: { color: "#4ade80" },
                  negative: { color: "#f87171" },
                  neutral: { color: "#94a3b8" }
                }}
              >
                <RechartsPrimitive.BarChart
                  data={hourlyData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 40 }}
                >
                  <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" />
                  <RechartsPrimitive.XAxis dataKey="hour" />
                  <RechartsPrimitive.YAxis />
                  <RechartsPrimitive.Tooltip />
                  <RechartsPrimitive.Legend wrapperStyle={{ paddingTop: "10px" }} />
                  <RechartsPrimitive.Bar dataKey="count" name="Articles" fill="var(--color-count)" />
                </RechartsPrimitive.BarChart>
              </ChartContainer>
            </div>
          </div>

          {/* Daily Publication Trend */}
          <div className="mt-8">
            <h3 className="text-white text-lg mb-4">Daily Publication Trend</h3>
            <div className="h-[300px]">
              <ChartContainer
                config={{
                  count: { color: "#3b82f6" },
                  positive: { color: "#4ade80" },
                  negative: { color: "#f87171" },
                  neutral: { color: "#94a3b8" }
                }}
              >
                <RechartsPrimitive.ComposedChart
                  data={dailyData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 40 }}
                >
                  <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" />
                  <RechartsPrimitive.XAxis dataKey="day" />
                  <RechartsPrimitive.YAxis yAxisId="left" />
                  <RechartsPrimitive.YAxis yAxisId="right" orientation="right" />
                  <RechartsPrimitive.Tooltip />
                  <RechartsPrimitive.Legend wrapperStyle={{ paddingTop: "10px" }} />
                  <RechartsPrimitive.Bar dataKey="count" name="Articles" fill="var(--color-count)" yAxisId="left" />
                  <RechartsPrimitive.Line type="monotone" dataKey="positive" name="Positive" stroke="var(--color-positive)" yAxisId="right" />
                  <RechartsPrimitive.Line type="monotone" dataKey="negative" name="Negative" stroke="var(--color-negative)" yAxisId="right" />
                </RechartsPrimitive.ComposedChart>
              </ChartContainer>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
