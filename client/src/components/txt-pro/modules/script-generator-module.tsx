import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { 
  Wand2, 
  Save, 
  Star, 
  Copy, 
  Plus, 
  Edit, 
  Trash2, 
  Settings,
  Clock,
  DollarSign,
  Calendar,
  FileText,
  Zap,
  Target,
  Crown
} from 'lucide-react';

interface ScriptOption {
  id: string;
  label: string;
  description: string;
  category: 'structure' | 'content' | 'timing' | 'financial' | 'engagement' | 'custom';
  type: 'checkbox' | 'text' | 'number' | 'select';
  options?: string[];
  defaultValue?: any;
  isCustom?: boolean;
}

interface ScriptPreset {
  id: string;
  name: string;
  description: string;
  selectedOptions: { [key: string]: any };
  customContent: {
    financialInfo: string;
    dates: string;
    additionalInfo: string;
    scriptLength: number;
  };
  rating: number;
  feedback: string;
  dateCreated: Date;
  lastUsed?: Date;
  useCount: number;
}

const ScriptGeneratorModule: React.FC = () => {
  const { toast } = useToast();
  const [presets, setPresets] = useState<ScriptPreset[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<ScriptPreset | null>(null);
  const [selectedOptions, setSelectedOptions] = useState<{ [key: string]: any }>({});
  const [customContent, setCustomContent] = useState({
    financialInfo: '',
    dates: '',
    additionalInfo: '',
    scriptLength: 6
  });
  const [isCreatingPreset, setIsCreatingPreset] = useState(false);
  const [presetForm, setPresetForm] = useState({
    name: '',
    description: '',
    rating: 5
  });
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [customOptions, setCustomOptions] = useState<ScriptOption[]>([]);

  const defaultOptions: ScriptOption[] = [
    // Structure Options
    {
      id: 'no-intro',
      label: 'No Introduction',
      description: 'Skip the opening introduction and jump straight to content',
      category: 'structure',
      type: 'checkbox',
      defaultValue: false
    },
    {
      id: 'no-outro',
      label: 'No Outro/Conclusion',
      description: 'End abruptly without traditional closing',
      category: 'structure',
      type: 'checkbox',
      defaultValue: false
    },
    {
      id: 'hook-style',
      label: 'Hook Style',
      description: 'Type of opening hook to use',
      category: 'structure',
      type: 'select',
      options: ['Urgent Question', 'Shocking Statistic', 'Personal Story', 'Breaking News', 'Direct Statement'],
      defaultValue: 'Urgent Question'
    },
    {
      id: 'cta-frequency',
      label: 'Call-to-Action Frequency',
      description: 'How often to include CTAs throughout the script',
      category: 'engagement',
      type: 'select',
      options: ['Beginning Only', 'Middle Only', 'End Only', 'Beginning & End', 'Throughout'],
      defaultValue: 'Beginning & End'
    },

    // Content Options
    {
      id: 'include-scam-warning',
      label: 'Include Scam Warning',
      description: 'Add standard scam protection information',
      category: 'content',
      type: 'checkbox',
      defaultValue: true
    },
    {
      id: 'include-eligibility',
      label: 'Include Eligibility Criteria',
      description: 'Explain who qualifies for the benefits discussed',
      category: 'content',
      type: 'checkbox',
      defaultValue: true
    },
    {
      id: 'include-application-steps',
      label: 'Include Application Steps',
      description: 'Provide step-by-step application guidance',
      category: 'content',
      type: 'checkbox',
      defaultValue: false
    },
    {
      id: 'tone-style',
      label: 'Tone Style',
      description: 'Overall tone and approach for the script',
      category: 'content',
      type: 'select',
      options: ['Urgent & Direct', 'Empathetic & Reassuring', 'Authoritative & Informative', 'Conversational & Friendly', 'Serious & Professional'],
      defaultValue: 'Urgent & Direct'
    },

    // Timing Options
    {
      id: 'disclose-timing',
      label: 'Disclose Financial Information Timing',
      description: 'When to reveal specific dollar amounts',
      category: 'timing',
      type: 'select',
      options: ['Immediately', 'After Hook', 'Mid-Script', 'Near End', 'Throughout'],
      defaultValue: 'After Hook'
    },
    {
      id: 'retention-hooks',
      label: 'Retention Hooks',
      description: 'Add "stick around" elements throughout',
      category: 'timing',
      type: 'checkbox',
      defaultValue: true
    },

    // Financial Options
    {
      id: 'specific-amounts',
      label: 'Include Specific Dollar Amounts',
      description: 'Use exact figures rather than general terms',
      category: 'financial',
      type: 'checkbox',
      defaultValue: true
    },
    {
      id: 'payment-schedule',
      label: 'Include Payment Schedule',
      description: 'Explain when payments will be received',
      category: 'financial',
      type: 'checkbox',
      defaultValue: true
    },
    {
      id: 'tax-implications',
      label: 'Discuss Tax Implications',
      description: 'Address whether benefits are taxable',
      category: 'financial',
      type: 'checkbox',
      defaultValue: false
    },

    // Engagement Options
    {
      id: 'rhetorical-questions',
      label: 'Use Rhetorical Questions',
      description: 'Include engaging questions throughout',
      category: 'engagement',
      type: 'checkbox',
      defaultValue: true
    },
    {
      id: 'personal-examples',
      label: 'Include Personal Examples',
      description: 'Use relatable scenarios and case studies',
      category: 'engagement',
      type: 'checkbox',
      defaultValue: true
    },
    {
      id: 'urgency-level',
      label: 'Urgency Level',
      description: 'How urgent should the messaging feel',
      category: 'engagement',
      type: 'select',
      options: ['Low', 'Medium', 'High', 'Critical'],
      defaultValue: 'High'
    }
  ];

  // Load presets from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('txtpro_script_presets');
    if (saved) {
      setPresets(JSON.parse(saved));
    } else {
      // Initialize with default presets
      const defaultPresets = getDefaultPresets();
      setPresets(defaultPresets);
      localStorage.setItem('txtpro_script_presets', JSON.stringify(defaultPresets));
    }

    const savedCustomOptions = localStorage.getItem('txtpro_custom_options');
    if (savedCustomOptions) {
      setCustomOptions(JSON.parse(savedCustomOptions));
    }
  }, []);

  // Save presets to localStorage
  useEffect(() => {
    localStorage.setItem('txtpro_script_presets', JSON.stringify(presets));
  }, [presets]);

  useEffect(() => {
    localStorage.setItem('txtpro_custom_options', JSON.stringify(customOptions));
  }, [customOptions]);

  const getDefaultPresets = (): ScriptPreset[] => [
    {
      id: 'slick-v1-preset',
      name: 'SlickV1 Complete',
      description: 'Your original proven approach with all elements',
      selectedOptions: {
        'hook-style': 'Urgent Question',
        'include-scam-warning': true,
        'include-eligibility': true,
        'tone-style': 'Urgent & Direct',
        'disclose-timing': 'After Hook',
        'retention-hooks': true,
        'specific-amounts': true,
        'payment-schedule': true,
        'rhetorical-questions': true,
        'personal-examples': true,
        'urgency-level': 'High',
        'cta-frequency': 'Throughout'
      },
      customContent: {
        financialInfo: 'Social Security payments, SSI benefits, SSDI amounts, VA compensation',
        dates: 'Payment dates, application deadlines, effective dates',
        additionalInfo: 'Scam warnings, contact information updates, banking verification',
        scriptLength: 7
      },
      rating: 9,
      feedback: 'High engagement, proven conversion',
      dateCreated: new Date(),
      useCount: 0
    },
    {
      id: 'quick-update-preset',
      name: 'Quick News Update',
      description: 'Fast-paced breaking news style',
      selectedOptions: {
        'no-intro': true,
        'hook-style': 'Breaking News',
        'tone-style': 'Urgent & Direct',
        'disclose-timing': 'Immediately',
        'specific-amounts': true,
        'urgency-level': 'Critical',
        'cta-frequency': 'End Only'
      },
      customContent: {
        financialInfo: 'Latest payment amounts, new benefit rates',
        dates: 'Effective immediately, next payment date',
        additionalInfo: 'Subscribe for updates',
        scriptLength: 4
      },
      rating: 8,
      feedback: 'Great for breaking news',
      dateCreated: new Date(),
      useCount: 0
    }
  ];

  const allOptions = [...defaultOptions, ...customOptions];

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-purple-200 bg-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5 text-purple-600" />
            Advanced Script Generator
          </CardTitle>
          <CardDescription className="text-purple-700">
            💡 <strong>Create custom scripts:</strong> Select exactly what to include, save as presets, and rate performance. Build your perfect script template with precise control over every element.
          </CardDescription>
          <div className="flex gap-2 mt-3">
            <Badge variant="secondary" className="text-xs">
              🎛️ Customizable options
            </Badge>
            <Badge variant="secondary" className="text-xs">
              💾 Save as presets
            </Badge>
            <Badge variant="secondary" className="text-xs">
              ⭐ Rate performance
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Preset Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">📋 Script Presets</CardTitle>
              <CardDescription>
                Load saved configurations or start fresh
              </CardDescription>
            </div>
            <Button onClick={() => setIsCreatingPreset(true)} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Save Current as Preset
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {presets.map(preset => (
              <Card
                key={preset.id}
                className={`hover:shadow-md transition-shadow cursor-pointer ${
                  selectedPreset?.id === preset.id ? 'ring-2 ring-purple-500 bg-purple-50' : ''
                }`}
                onClick={() => {
                  setSelectedPreset(preset);
                  setSelectedOptions(preset.selectedOptions);
                  setCustomContent(preset.customContent);

                  // Update use count
                  setPresets(prev => prev.map(p =>
                    p.id === preset.id
                      ? { ...p, useCount: p.useCount + 1, lastUsed: new Date() }
                      : p
                  ));

                  toast({
                    title: "Preset Loaded",
                    description: `${preset.name} configuration has been applied`
                  });
                }}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-base flex items-center gap-2">
                        {preset.name}
                        {preset.rating >= 8 && <Crown className="h-4 w-4 text-yellow-500" />}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">{preset.description}</p>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 text-yellow-500 fill-current" />
                      <span className="text-xs">{preset.rating}/10</span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {preset.customContent.scriptLength} min script
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Target className="h-3 w-3" />
                      {Object.keys(preset.selectedOptions).length} options selected
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Zap className="h-3 w-3" />
                      Used {preset.useCount} times
                    </div>
                    {preset.feedback && (
                      <p className="text-xs text-blue-700 bg-blue-50 p-1 rounded">
                        "{preset.feedback}"
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Script Configuration */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column: Options */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">🎛️ Script Options</CardTitle>
            <CardDescription>
              Select what to include in your script
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96">
              <div className="space-y-6">
                {['structure', 'content', 'timing', 'financial', 'engagement'].map(category => (
                  <div key={category} className="space-y-3">
                    <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground">
                      {category.charAt(0).toUpperCase() + category.slice(1)} Options
                    </h4>
                    <div className="space-y-3">
                      {allOptions
                        .filter(option => option.category === category)
                        .map(option => (
                          <div key={option.id} className="space-y-2">
                            <div className="flex items-start space-x-2">
                              {option.type === 'checkbox' && (
                                <Checkbox
                                  id={option.id}
                                  checked={selectedOptions[option.id] || false}
                                  onCheckedChange={(checked) =>
                                    setSelectedOptions(prev => ({ ...prev, [option.id]: checked }))
                                  }
                                />
                              )}
                              <div className="flex-1">
                                <Label
                                  htmlFor={option.id}
                                  className="text-sm font-medium cursor-pointer"
                                >
                                  {option.label}
                                </Label>
                                <p className="text-xs text-muted-foreground">
                                  {option.description}
                                </p>
                              </div>
                            </div>

                            {option.type === 'select' && (
                              <Select
                                value={selectedOptions[option.id] || option.defaultValue}
                                onValueChange={(value) =>
                                  setSelectedOptions(prev => ({ ...prev, [option.id]: value }))
                                }
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {option.options?.map(opt => (
                                    <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )}
                          </div>
                        ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Right Column: Custom Content */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">📝 Custom Content</CardTitle>
            <CardDescription>
              Add specific information for your script
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="scriptLength" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Script Length: {customContent.scriptLength} minutes
              </Label>
              <Slider
                id="scriptLength"
                min={2}
                max={15}
                step={0.5}
                value={[customContent.scriptLength]}
                onValueChange={(value) =>
                  setCustomContent(prev => ({ ...prev, scriptLength: value[0] }))
                }
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="financialInfo" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Financial Information to Include
              </Label>
              <Textarea
                id="financialInfo"
                placeholder="e.g., $1,400 stimulus payments, $200 Social Security increase, COLA adjustments..."
                value={customContent.financialInfo}
                onChange={(e) =>
                  setCustomContent(prev => ({ ...prev, financialInfo: e.target.value }))
                }
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="dates" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Important Dates
              </Label>
              <Textarea
                id="dates"
                placeholder="e.g., January 15th payment date, March 1st deadline, Q2 2024 implementation..."
                value={customContent.dates}
                onChange={(e) =>
                  setCustomContent(prev => ({ ...prev, dates: e.target.value }))
                }
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="additionalInfo" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Additional Information
              </Label>
              <Textarea
                id="additionalInfo"
                placeholder="e.g., Contact SSA to update info, check eligibility requirements, avoid scams..."
                value={customContent.additionalInfo}
                onChange={(e) =>
                  setCustomContent(prev => ({ ...prev, additionalInfo: e.target.value }))
                }
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Generate Script */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">🚀 Generate Your Script</CardTitle>
          <CardDescription>
            Create a custom ChatGPT prompt based on your selections
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button
              onClick={() => {
                const prompt = generateCustomPrompt();
                setGeneratedPrompt(prompt);
                toast({
                  title: "🎉 Custom Prompt Generated!",
                  description: "Your personalized script prompt is ready to copy",
                  duration: 4000
                });
              }}
              className="bg-purple-600 hover:bg-purple-700"
              size="lg"
            >
              <Wand2 className="h-4 w-4 mr-2" />
              Generate Custom Prompt
            </Button>

            {generatedPrompt && (
              <Button
                onClick={async () => {
                  try {
                    await navigator.clipboard.writeText(generatedPrompt);
                    toast({
                      title: "✅ Copied to Clipboard!",
                      description: "Your custom prompt is ready to paste into ChatGPT",
                      duration: 5000
                    });
                  } catch (error) {
                    const textArea = document.createElement('textarea');
                    textArea.value = generatedPrompt;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);

                    toast({
                      title: "✅ Copied to Clipboard!",
                      description: "Your custom prompt is ready to paste into ChatGPT",
                      duration: 5000
                    });
                  }
                }}
                className="bg-green-600 hover:bg-green-700"
                size="lg"
              >
                <Copy className="h-4 w-4 mr-2" />
                📋 Copy to Clipboard
              </Button>
            )}
          </div>

          {generatedPrompt && (
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-2">✅ Custom Prompt Generated!</h4>
                <p className="text-sm text-green-800">
                  Your personalized script prompt includes {Object.keys(selectedOptions).filter(key => selectedOptions[key]).length} selected options
                  and is configured for a {customContent.scriptLength}-minute script.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <Label className="text-sm font-medium">📋 Prompt Preview:</Label>
                  <Badge variant="secondary" className="text-xs">
                    {generatedPrompt.length} characters
                  </Badge>
                </div>
                <ScrollArea className="h-32">
                  <pre className="text-xs whitespace-pre-wrap text-gray-700">
                    {generatedPrompt.substring(0, 500)}...
                  </pre>
                </ScrollArea>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">📝 Next Steps:</h4>
                <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                  <li>Click "📋 Copy to Clipboard" above</li>
                  <li>Open ChatGPT in a new browser tab</li>
                  <li>Paste the prompt and press Enter</li>
                  <li>ChatGPT will generate your custom script</li>
                  <li>Come back to rate this preset's performance</li>
                </ol>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Preset Creation Modal */}
      {isCreatingPreset && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="text-lg text-yellow-800">💾 Save as New Preset</CardTitle>
            <CardDescription className="text-yellow-700">
              Save your current configuration for future use
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="presetName">Preset Name *</Label>
                <Input
                  id="presetName"
                  placeholder="e.g., Quick Breaking News"
                  value={presetForm.name}
                  onChange={(e) => setPresetForm(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="presetRating">Expected Performance Rating</Label>
                <div className="flex items-center gap-2">
                  <Slider
                    id="presetRating"
                    min={1}
                    max={10}
                    step={1}
                    value={[presetForm.rating]}
                    onValueChange={(value) =>
                      setPresetForm(prev => ({ ...prev, rating: value[0] }))
                    }
                    className="flex-1"
                  />
                  <span className="text-sm font-medium w-8">{presetForm.rating}/10</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="presetDescription">Description</Label>
              <Textarea
                id="presetDescription"
                placeholder="Describe when and how to use this preset..."
                value={presetForm.description}
                onChange={(e) => setPresetForm(prev => ({ ...prev, description: e.target.value }))}
                rows={2}
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={() => {
                  if (!presetForm.name.trim()) {
                    toast({
                      title: "Name Required",
                      description: "Please enter a name for your preset",
                      variant: "destructive"
                    });
                    return;
                  }

                  const newPreset: ScriptPreset = {
                    id: `preset_${Date.now()}`,
                    name: presetForm.name.trim(),
                    description: presetForm.description.trim(),
                    selectedOptions: { ...selectedOptions },
                    customContent: { ...customContent },
                    rating: presetForm.rating,
                    feedback: '',
                    dateCreated: new Date(),
                    useCount: 0
                  };

                  setPresets(prev => [...prev, newPreset]);
                  setPresetForm({ name: '', description: '', rating: 5 });
                  setIsCreatingPreset(false);

                  toast({
                    title: "Preset Saved!",
                    description: `"${newPreset.name}" has been added to your presets`,
                    duration: 3000
                  });
                }}
                className="bg-yellow-600 hover:bg-yellow-700"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Preset
              </Button>
              <Button onClick={() => setIsCreatingPreset(false)} variant="outline">
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  // Helper function to generate custom prompt
  function generateCustomPrompt(): string {
    let prompt = `You are a highly experienced and compassionate financial expert, specializing in government benefit programs and financial relief for individuals on fixed incomes, including Social Security, Supplemental Security Income (SSI), Social Security Disability Insurance (SSDI), and Veterans Affairs (VA) beneficiaries.

Create a ${customContent.scriptLength}-minute script with the following specifications:

STRUCTURE REQUIREMENTS:`;

    // Add structure options
    if (selectedOptions['no-intro']) {
      prompt += `\n• NO INTRODUCTION - Jump straight into the main content`;
    }
    if (selectedOptions['no-outro']) {
      prompt += `\n• NO CONCLUSION - End abruptly without traditional closing`;
    }
    if (selectedOptions['hook-style']) {
      prompt += `\n• OPENING HOOK: Use a ${selectedOptions['hook-style']} style opening`;
    }

    prompt += `\n\nCONTENT REQUIREMENTS:`;

    // Add content options
    if (selectedOptions['include-scam-warning']) {
      prompt += `\n• Include clear scam warnings about fake government contacts`;
    }
    if (selectedOptions['include-eligibility']) {
      prompt += `\n• Explain eligibility criteria clearly`;
    }
    if (selectedOptions['include-application-steps']) {
      prompt += `\n• Provide step-by-step application guidance`;
    }
    if (selectedOptions['tone-style']) {
      prompt += `\n• TONE: ${selectedOptions['tone-style']}`;
    }

    // Add timing requirements
    if (selectedOptions['disclose-timing']) {
      prompt += `\n\nTIMING REQUIREMENTS:`;
      prompt += `\n• Reveal financial information: ${selectedOptions['disclose-timing']}`;
    }
    if (selectedOptions['retention-hooks']) {
      prompt += `\n• Include retention hooks throughout to keep viewers watching`;
    }

    // Add financial requirements
    if (selectedOptions['specific-amounts'] || selectedOptions['payment-schedule'] || selectedOptions['tax-implications']) {
      prompt += `\n\nFINANCIAL INFORMATION:`;
      if (selectedOptions['specific-amounts']) {
        prompt += `\n• Use specific dollar amounts rather than general terms`;
      }
      if (selectedOptions['payment-schedule']) {
        prompt += `\n• Include detailed payment schedule information`;
      }
      if (selectedOptions['tax-implications']) {
        prompt += `\n• Address tax implications of benefits`;
      }
    }

    // Add engagement requirements
    if (selectedOptions['rhetorical-questions'] || selectedOptions['personal-examples'] || selectedOptions['urgency-level']) {
      prompt += `\n\nENGAGEMENT ELEMENTS:`;
      if (selectedOptions['rhetorical-questions']) {
        prompt += `\n• Use rhetorical questions throughout`;
      }
      if (selectedOptions['personal-examples']) {
        prompt += `\n• Include relatable personal examples and case studies`;
      }
      if (selectedOptions['urgency-level']) {
        prompt += `\n• Urgency level: ${selectedOptions['urgency-level']}`;
      }
    }

    // Add CTA requirements
    if (selectedOptions['cta-frequency']) {
      prompt += `\n\nCALL-TO-ACTION: ${selectedOptions['cta-frequency']}`;
    }

    // Add custom content
    if (customContent.financialInfo.trim()) {
      prompt += `\n\nSPECIFIC FINANCIAL INFORMATION TO INCLUDE:\n${customContent.financialInfo}`;
    }
    if (customContent.dates.trim()) {
      prompt += `\n\nIMPORTANT DATES TO MENTION:\n${customContent.dates}`;
    }
    if (customContent.additionalInfo.trim()) {
      prompt += `\n\nADDITIONAL INFORMATION TO INCLUDE:\n${customContent.additionalInfo}`;
    }

    prompt += `\n\nGenerate a complete, engaging script that follows all the above requirements. Make it informative, actionable, and appropriate for the target audience of government benefit recipients.`;

    return prompt;
  }
};

export default ScriptGeneratorModule;
