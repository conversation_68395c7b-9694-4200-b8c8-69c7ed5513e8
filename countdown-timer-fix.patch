--- src/components/realtime-auto-refresh.tsx
+++ src/components/realtime-auto-refresh.tsx
@@ -536,7 +536,7 @@
 
   // New countdown timer implementation
   useEffect(() => {
-    // Always clear any existing timer first to prevent duplicates
+    // Always clear any existing countdown timer first to prevent duplicates
     if (countdownTimerRef.current) {
       clearInterval(countdownTimerRef.current);
       countdownTimerRef.current = null;
@@ -550,7 +550,7 @@
       return;
     }
 
-    logger.verbose("Setting up new countdown timer");
+    logger.verbose("Setting up new countdown display timer");
 
     // Calculate the next refresh time based on the current time and interval
     const calculateNextRefreshTime = () => {
@@ -559,7 +559,7 @@
       return nextTime;
     };
 
-    // Set the next refresh time if it's not already set or if a refresh is in progress
+    // Only set the next refresh time if it's not already set or if a refresh is in progress
     if (!nextRefreshTime || refreshInProgressRef.current) {
       const newNextRefreshTime = calculateNextRefreshTime();
       setNextRefreshTime(newNextRefreshTime);
@@ -576,7 +576,7 @@
 
     // Function to update the countdown display
     const updateCountdown = () => {
-      try {
+      try {        
         // Safety check - if nextRefreshTime is not set, set it now
         if (!nextRefreshTime) {
           const newNextRefreshTime = calculateNextRefreshTime();
@@ -588,17 +588,8 @@
         const now = new Date();
         const diffMs = nextRefreshTime.getTime() - now.getTime();
 
-        if (diffMs <= 0) {
-          // Time's up, trigger a refresh if not already in progress
-          if (!refreshInProgressRef.current) {
-            logger.verbose("Countdown reached 0, triggering refresh");
-            handleRefresh().catch(err => {
-              logger.error("Error during countdown-triggered refresh:", err);
-            });
-          }
-
-          // Calculate the next refresh time
-          const newNextRefreshTime = calculateNextRefreshTime();
+        if (diffMs <= 0) {          
+          // Just update the display - the actual refresh will be triggered by the main timer
           setNextRefreshTime(newNextRefreshTime);
           logger.verbose(`Setting next refresh time to: ${newNextRefreshTime.toLocaleTimeString()}`);
 
@@ -617,7 +608,7 @@
         const minutes = Math.floor(totalSeconds / 60);
         const seconds = totalSeconds % 60;
 
-        // Format the countdown display
+        // Format the countdown display (MM:SS)
         const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
         setCountdownDisplay(display);
 
@@ -631,7 +622,7 @@
     };
 
     // Update the countdown immediately
-    updateCountdown();
+    setTimeout(updateCountdown, 0);
 
     // Set up a timer to update the countdown every second
     const timer = setInterval(updateCountdown, 1000);
@@ -644,7 +635,7 @@
       clearInterval(timer);
       countdownTimerRef.current = null;
     };
-  }, [autoRefreshEnabled, autoRefreshInterval, isActive, isPaused, handleRefresh, nextRefreshTime]);
