from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time

# Set up Chrome options
chrome_options = Options()
chrome_options.add_argument("--headless")  # Run in headless mode
chrome_options.add_argument("--disable-gpu")
chrome_options.add_argument("--window-size=1920,1080")
chrome_options.add_argument("--disable-extensions")
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")

# Initialize the Chrome driver
print("Initializing Chrome driver...")
driver = webdriver.Chrome(options=chrome_options)

try:
    # Navigate to the application
    print("Navigating to the application...")
    driver.get("http://localhost:5001")

    # Print the title
    print(f"Page title: {driver.title}")

    # Take a screenshot
    driver.save_screenshot("login_page.png")
    print("Saved screenshot of login page")

    # Wait for the login page to load
    try:
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h1[contains(text(), 'Trendy')]"))
        )
        print("Login page loaded successfully")
    except Exception as e:
        print(f"Error waiting for login page: {e}")
        driver.save_screenshot("login_page_error.png")
        raise

    # Log in with admin credentials
    print("Logging in with admin credentials...")
    # Find the username and password inputs using their labels
    username_input = driver.find_element(By.XPATH, "//label[contains(text(), 'Username')]/following::input[1]")
    password_input = driver.find_element(By.XPATH, "//label[contains(text(), 'Password')]/following::input[1]")
    login_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Login')]")

    username_input.send_keys("kedar")
    password_input.send_keys("1")
    login_button.click()

    # Wait for the main page to load
    try:
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//a[contains(text(), 'Settings')]"))
        )
        print("Main page loaded successfully")
        driver.save_screenshot("main_page.png")
    except Exception as e:
        print(f"Error waiting for main page: {e}")
        driver.save_screenshot("main_page_error.png")
        raise

    # Go to the Settings page
    print("Navigating to Settings page...")
    # Find the settings link in the sidebar
    settings_link = driver.find_element(By.XPATH, "//a[contains(@href, '/settings')]")
    settings_link.click()

    # Wait for the Settings page to load
    try:
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h1[contains(text(), 'Settings')]"))
        )
        print("Settings page loaded successfully")
        driver.save_screenshot("settings_page.png")
    except Exception as e:
        print(f"Error waiting for settings page: {e}")
        driver.save_screenshot("settings_page_error.png")
        raise

    # Test 1: Check toggle colors in dark mode
    print("Testing toggle colors in dark mode...")
    # Ensure dark mode is enabled
    try:
        dark_mode_toggle = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Dark Mode')]/following::button[@role='switch']"))
        )

        # Check if dark mode is enabled, if not, enable it
        if dark_mode_toggle.get_attribute("data-state") != "checked":
            dark_mode_toggle.click()
            print("Enabled dark mode")
        else:
            print("Dark mode is already enabled")

        driver.save_screenshot("dark_mode_enabled.png")
    except Exception as e:
        print(f"Error with dark mode toggle: {e}")
        driver.save_screenshot("dark_mode_toggle_error.png")
        raise

    # Click on the Playback tab
    try:
        playback_tab = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Playback')]"))
        )
        playback_tab.click()
        print("Clicked on Playback tab")
        time.sleep(1)  # Wait for tab content to load
    except Exception as e:
        print(f"Error clicking on Playback tab: {e}")
        driver.save_screenshot("playback_tab_error.png")
        raise

    # Find the playback sound toggle
    try:
        playback_sound_toggle = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Playback Sound')]/following::button[@role='switch']"))
        )
        print("Found playback sound toggle")
    except Exception as e:
        print(f"Error finding playback sound toggle: {e}")
        driver.save_screenshot("playback_sound_toggle_error.png")
        raise

    # Test 2: Toggle playback sound OFF
    print("Testing playback sound toggle...")
    current_state = playback_sound_toggle.get_attribute("data-state")
    print(f"Current playback sound state: {current_state}")

    # Toggle it OFF if it's ON
    if current_state == "checked":
        playback_sound_toggle.click()
        print("Toggled playback sound OFF")
        driver.save_screenshot("playback_sound_toggled_off.png")
    else:
        # Toggle it ON if it's OFF, then OFF again to test the toggle
        playback_sound_toggle.click()
        print("Toggled playback sound ON")
        time.sleep(1)
        playback_sound_toggle.click()
        print("Toggled playback sound OFF again")
        driver.save_screenshot("playback_sound_toggled_off.png")

    # Wait for the change to be saved
    print("Waiting for changes to be saved...")
    time.sleep(2)
    time.sleep(2)  # Wait for the save to complete
    driver.save_screenshot("after_save_changes.png")

    # Refresh the page to verify persistence
    driver.refresh()

    # Wait for the page to reload
    try:
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//h1[contains(text(), 'Settings')]"))
        )
        print("Page reloaded successfully")
        driver.save_screenshot("page_reloaded.png")
    except Exception as e:
        print(f"Error waiting for page reload: {e}")
        driver.save_screenshot("page_reload_error.png")
        raise

    # Click on the Playback tab again after refresh
    try:
        playback_tab = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Playback')]"))
        )
        playback_tab.click()
        print("Clicked on Playback tab after refresh")
        time.sleep(1)  # Wait for tab content to load
    except Exception as e:
        print(f"Error clicking on Playback tab after refresh: {e}")
        driver.save_screenshot("playback_tab_refresh_error.png")
        raise

    # Check if the playback sound toggle is still OFF
    try:
        playback_sound_toggle = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(text(), 'Playback Sound')]/following::button[@role='switch']"))
        )

        current_state = playback_sound_toggle.get_attribute("data-state")
        print(f"Playback sound state after refresh: {current_state}")

        if current_state == "unchecked":
            print("SUCCESS: Playback sound toggle persisted its OFF state")
        else:
            print("FAILURE: Playback sound toggle did not persist its OFF state")

        driver.save_screenshot("playback_sound_persistence.png")
    except Exception as e:
        print(f"Error checking playback sound persistence: {e}")
        driver.save_screenshot("playback_sound_persistence_error.png")
        raise

    print("Test completed successfully!")

except Exception as e:
    print(f"An error occurred: {e}")
    driver.save_screenshot("error.png")

finally:
    # Close the browser
    print("Closing the browser...")
    driver.quit()
