const { authenticateUser } = require('../middleware/auth');

/**
 * Setup routes for video metrics
 * @param {Express} app - Express app
 * @param {SQLiteStorage} storage - SQLite storage instance
 */
function setupVideoMetricsRoutes(app, storage) {
  // Save video metrics
  app.post('/api/video-metrics', authenticateUser, async (req, res) => {
    try {
      // Double-check authentication
      if (!req.isAuthenticated() || !req.user || !req.user.id) {
        console.error('User not properly authenticated for video metrics');
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const userId = req.user.id;
      const { timestamp, newVideosCount, trendingVideosCount } = req.body;

      if (!timestamp || newVideosCount === undefined || trendingVideosCount === undefined) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      await storage.saveVideoMetrics(userId, {
        timestamp,
        newVideosCount,
        trendingVideosCount
      });

      res.status(200).json({ success: true });
    } catch (error) {
      console.error('Error saving video metrics:', error);
      res.status(500).json({ error: 'Failed to save video metrics' });
    }
  });

  // Get video metrics
  app.get('/api/video-metrics', authenticateUser, async (req, res) => {
    try {
      // Double-check authentication
      if (!req.isAuthenticated() || !req.user || !req.user.id) {
        console.error('User not properly authenticated for video metrics');
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const userId = req.user.id;
      const startTimestamp = req.query.startTimestamp ? parseInt(req.query.startTimestamp) : undefined;
      const endTimestamp = req.query.endTimestamp ? parseInt(req.query.endTimestamp) : undefined;

      const metrics = await storage.getVideoMetrics(userId, startTimestamp, endTimestamp);

      res.status(200).json(metrics);
    } catch (error) {
      console.error('Error getting video metrics:', error);
      res.status(500).json({ error: 'Failed to get video metrics' });
    }
  });
}

module.exports = { setupVideoMetricsRoutes };
