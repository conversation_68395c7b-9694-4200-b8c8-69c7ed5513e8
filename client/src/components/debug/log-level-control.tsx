import React, { useState, useEffect } from 'react';
import { LogLevel, getLogLevel, setLogLevel } from '@/lib/logger';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { BugIcon } from 'lucide-react';

/**
 * A component that allows changing the log level during development
 */
export function LogLevelControl() {
  const [logLevel, setLogLevelState] = useState<LogLevel>(getLogLevel());
  
  // Update localStorage when log level changes
  useEffect(() => {
    try {
      if (logLevel === LogLevel.VERBOSE) {
        localStorage.setItem('debug-mode', 'verbose');
      } else if (logLevel === LogLevel.DEBUG) {
        localStorage.setItem('debug-mode', 'true');
      } else {
        localStorage.setItem('debug-mode', 'false');
      }
    } catch (e) {
      // Ignore localStorage errors
    }
  }, [logLevel]);
  
  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }
  
  const handleSetLogLevel = (level: LogLevel) => {
    setLogLevel(level);
    setLogLevelState(level);
  };
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <BugIcon className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem 
          onClick={() => handleSetLogLevel(LogLevel.NONE)}
          className={logLevel === LogLevel.NONE ? "bg-muted" : ""}
        >
          None
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => handleSetLogLevel(LogLevel.ERROR)}
          className={logLevel === LogLevel.ERROR ? "bg-muted" : ""}
        >
          Errors Only
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => handleSetLogLevel(LogLevel.WARN)}
          className={logLevel === LogLevel.WARN ? "bg-muted" : ""}
        >
          Warnings & Errors
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => handleSetLogLevel(LogLevel.INFO)}
          className={logLevel === LogLevel.INFO ? "bg-muted" : ""}
        >
          Info (Default)
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => handleSetLogLevel(LogLevel.DEBUG)}
          className={logLevel === LogLevel.DEBUG ? "bg-muted" : ""}
        >
          Debug
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => handleSetLogLevel(LogLevel.VERBOSE)}
          className={logLevel === LogLevel.VERBOSE ? "bg-muted" : ""}
        >
          Verbose
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default LogLevelControl;
