/**
 * Add Raw Data to Videos
 * 
 * This script adds raw data to videos that don't have it.
 */

import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file path and directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Connect to the database
const dbPath = path.resolve(__dirname, '../data.db');
console.log(`Connecting to database at ${dbPath}`);
const db = new Database(dbPath);

try {
  console.log('Starting to add raw data to videos...');

  // Begin transaction
  db.exec('BEGIN TRANSACTION');

  // Get all videos with financial analysis but no raw data
  const videos = db.prepare(`
    SELECT id, financial_score, financial_category, financial_amount, 
           openrouter_priority_tag, openrouter_reason_for_priority,
           openrouter_benefit_amounts, openrouter_expected_arrival_date,
           openrouter_eligible_people, openrouter_proof_or_source,
           openrouter_actions_to_claim, openrouter_model_used
    FROM youtube_videos 
    WHERE has_financial_analysis = 1 AND openrouter_raw_data IS NULL
  `).all();

  console.log(`Found ${videos.length} videos with financial analysis but no raw data`);

  let updatedCount = 0;

  // Process each video
  for (const video of videos) {
    try {
      // Create a raw data object
      const rawData = {
        promptName: 'default',
        modelUsed: video.openrouter_model_used || 'Unknown',
        hasBenefit: video.financial_score > 30,
        score: video.financial_score || 0,
        certaintyScore: video.financial_score || 0,
        benefitType: video.financial_category || 'Unknown',
        benefitDescription: video.financial_amount || 'No description available',
        priorityTag: video.openrouter_priority_tag || 'none',
        reasoning: video.openrouter_reason_for_priority || '',
        extractedInfo: {
          benefitAmounts: [],
          expectedArrivalDate: video.openrouter_expected_arrival_date || '',
          eligiblePeople: video.openrouter_eligible_people || '',
          proofOrSource: video.openrouter_proof_or_source || '',
          actionsToClaim: video.openrouter_actions_to_claim || ''
        }
      };

      // Try to parse benefit amounts
      try {
        if (video.openrouter_benefit_amounts) {
          rawData.extractedInfo.benefitAmounts = typeof video.openrouter_benefit_amounts === 'string' 
            ? JSON.parse(video.openrouter_benefit_amounts) 
            : video.openrouter_benefit_amounts;
        }
      } catch (error) {
        console.error(`Error parsing benefit amounts for video ${video.id}:`, error);
      }

      // Update the raw data in the database
      const updateStmt = db.prepare(`
        UPDATE youtube_videos
        SET openrouter_raw_data = ?
        WHERE id = ?
      `);
      
      updateStmt.run(JSON.stringify(rawData), video.id);
      updatedCount++;
      console.log(`Added raw data for video ${video.id}`);
    } catch (error) {
      console.error(`Error processing video ${video.id}:`, error);
    }
  }

  // Commit the transaction
  db.exec('COMMIT');
  console.log(`Added raw data to ${updatedCount} videos`);
  console.log('Raw data addition completed successfully');

} catch (error) {
  console.error('Error adding raw data to videos:', error);
  db.exec('ROLLBACK');
  process.exit(1);
} finally {
  db.close();
}
