import { useSettings } from "@/hooks/use-settings";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { userSettingsSchema, UserSettings } from "@shared/schema";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useEffect, useState, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { Sidebar } from "@/components/navigation/sidebar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ApiKeyManager } from "@/components/settings/api-key-manager";
import { OpenRouterApiKeyManager } from "@/components/settings/openrouter-api-key-manager";

import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Link } from "wouter";
import { Loader2, Cast, Pencil } from "lucide-react";
import PromptsManager from "@/components/PromptsManager";

const AUTO_REFRESH_OPTIONS = [
  { value: "0", label: "Disabled" },
  { value: "60", label: "1 hour" },
  { value: "240", label: "4 hours" },
  { value: "360", label: "6 hours" },
  { value: "720", label: "12 hours" },
  { value: "1440", label: "1 day" },
  { value: "2880", label: "2 days" },
];

export default function SettingsPage() {
  const { settings, updateSettings, deleteData, isLoading } = useSettings();
  const { toast } = useToast();
  // Add state to manage the active tab
  const [activeTab, setActiveTab] = useState("general");
  // Add state to track if we're in the AI tab to prevent unnecessary renders
  const [isAiTabActive, setIsAiTabActive] = useState(false);
  // Add a key to force re-render of the OllamaPromptsManager component
  const [promptsManagerKey, setPromptsManagerKey] = useState(0);

  const form = useForm<UserSettings>({
    resolver: zodResolver(userSettingsSchema),
    values: settings || {
      darkMode: true,
      searchKeywords: [],
      minViewsPerHour: 10,
      removeDuplicates: true,
      excludeWords: [],
      autoRefreshInterval: 0,
      // preferredPlayback setting removed as it was not functioning properly
      parallelApiCalls: true, // Now enabled by default
      lastRefreshTime: null,
      useInAppPlayer: true,
      previewSound: false,
      playbackSound: true,
      watchedVideos: [],
      homePage: "trendy",
      // Default sort and filter settings removed - we use localStorage instead
      openRouterModel: "google/gemini-2.0-flash-exp:free", // Default OpenRouter model - most reliable
      autoAnalyzeOnRefresh: true, // Auto-analyze videos on channel refresh by default
    },
  });

  // Update isAiTabActive when the tab changes and reset form values
  useEffect(() => {
    setIsAiTabActive(activeTab === 'ai');

    // When switching to AI tab, make sure form values are in sync with settings
    if (activeTab === 'ai' && settings) {
      console.log('Syncing AI tab with settings:', settings);
      // Ollama settings removed as we use OpenRouter
      form.setValue('openRouterModel', settings.openRouterModel || 'google/gemini-2.0-flash-exp:free');
      form.setValue('selectedPromptId', settings.selectedPromptId || null);

      // Force re-render of the OllamaPromptsManager component
      setPromptsManagerKey(prev => prev + 1);
    }
  }, [activeTab, settings, form]);

  // Initialize form values when settings are loaded
  useEffect(() => {
    if (settings) {
      console.log('Initializing form with settings:', settings);

      // Create a clean copy of settings with proper boolean conversions
      const cleanSettings = {
        ...settings,
        // Ensure boolean values are properly converted
        autoAnalyzeOnRefresh: settings.autoAnalyzeOnRefresh === true,
        darkMode: settings.darkMode === true,
        removeDuplicates: settings.removeDuplicates === true,
        useInAppPlayer: settings.useInAppPlayer === true,
        previewSound: settings.previewSound === true,
        playbackSound: settings.playbackSound === true,
        // Ensure string values have defaults
        openRouterModel: settings.openRouterModel || 'google/gemini-2.0-flash-exp:free',
        homePage: settings.homePage || 'trendy',
        // Ensure null values are properly handled
        selectedPromptId: settings.selectedPromptId || null
      };

      // Reset the form with clean settings
      form.reset(cleanSettings);

      console.log('Form reset with clean settings:', cleanSettings);

      // Force re-render of the OllamaPromptsManager component if we're on the AI tab
      if (activeTab === 'ai') {
        setPromptsManagerKey(prev => prev + 1);
      }
    }
  }, [settings, form, activeTab]);

  // Function to save settings manually using direct fetch API
  const saveSettings = useCallback(() => {
    const data = form.getValues();

    // Get all current settings
    const allSettings = settings || {};

    // Create a complete settings object with all fields
    const formattedData = {
      ...allSettings,
      ...data,
      searchKeywords: settings?.searchKeywords || [],
      excludeWords: settings?.excludeWords || [],
      playbackSound: data.playbackSound === false ? false : true,
      // Ollama model removed as we use OpenRouter
      openRouterModel: data.openRouterModel || 'google/gemini-2.0-flash-exp:free',
      // Make sure we include homePage and other critical settings
      homePage: data.homePage,
      darkMode: data.darkMode !== undefined ? data.darkMode : allSettings.darkMode !== undefined ? allSettings.darkMode : false,
      useInAppPlayer: data.useInAppPlayer !== undefined ? data.useInAppPlayer : allSettings.useInAppPlayer !== undefined ? allSettings.useInAppPlayer : true,
      // Explicitly include autoAnalyzeOnRefresh to ensure it's saved
      // Explicitly handle autoAnalyzeOnRefresh as a boolean
      autoAnalyzeOnRefresh: data.autoAnalyzeOnRefresh === true ? true : false,
      // Explicitly include selectedPromptId to ensure it's saved
      // Convert empty string to null, but preserve numeric values
      selectedPromptId: data.selectedPromptId === '' ? null :
                        data.selectedPromptId === undefined ? null :
                        data.selectedPromptId
    };

    // Log the settings being saved
    console.log('Saving settings:', formattedData);

    // Save the current tab before updating settings
    const currentTab = activeTab;

    // Direct API call to save settings
    fetch('/api/settings', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formattedData),
    })
    .then(response => response.json())
    .then(data => {
      console.log('Settings saved successfully:', data);
      // Make sure we stay on the same tab after settings are updated
      setActiveTab(currentTab);
    })
    .catch(error => {
      console.error('Error saving settings:', error);
    });
  }, [form, settings, activeTab, setActiveTab]);

  // Add an effect to preserve the active tab when settings are updated
  useEffect(() => {
    // This effect runs when settings are updated
    // We don't need to do anything here, just making sure the activeTab state is preserved
  }, [settings, activeTab]);

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex-1 overflow-auto">
        <div className="container max-w-3xl mx-auto px-6 py-8">
          <h1 className="text-3xl font-bold mb-6">Settings</h1>

          <Tabs
            value={activeTab}
            onValueChange={(value) => {
              // If we're already on the AI tab and trying to go to it again, do nothing
              if (value === 'ai' && activeTab === 'ai') {
                return;
              }
              setActiveTab(value);
            }}
            className="mb-8"
          >
            <TabsList className="mb-4 w-full md:w-auto">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="playback">Playback</TabsTrigger>
              <TabsTrigger value="display">Display</TabsTrigger>
              <TabsTrigger value="ai">AI Settings</TabsTrigger>
              <TabsTrigger value="api-keys">API Keys</TabsTrigger>
            </TabsList>

            <TabsContent value="general">
              <Form {...form}>
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    // Form submission is now handled automatically
                  }}
                  className="space-y-6 bg-card p-6 rounded-lg shadow-sm border border-border/50"
                >
                  <div className="grid gap-6 md:grid-cols-2">
                    {/* Search Keywords removed - now managed in Keyword Groups */}
                    <FormField
                      control={form.control}
                      name="minViewsPerHour"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Views Per Hour</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              {...field}
                              onChange={(e) => {
                                field.onChange(parseInt(e.target.value));
                                saveSettings();
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {/* Exclude Words removed - now managed in Keyword Groups */}
                    <FormField
                      control={form.control}
                      name="autoRefreshInterval"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Auto Refresh Interval</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(parseInt(value));
                              saveSettings();
                            }}
                            value={String(field.value || 0)}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select interval" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {AUTO_REFRESH_OPTIONS.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="removeDuplicates"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Remove Duplicates</FormLabel>
                            <FormDescription>Filter out duplicate videos</FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={(checked) => {
                                console.log('Remove duplicates changed to:', checked);

                                // Update the form value
                                field.onChange(checked);
                                form.setValue('removeDuplicates', checked);

                                // Save settings automatically
                                saveSettings();
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="homePage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Home Page</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              saveSettings();
                            }}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select default home page" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="trendy">Trendy</SelectItem>
                              <SelectItem value="youtube">YouTube</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>Page to show when you first log in</FormDescription>
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Default Watch Page Settings removed - we use localStorage instead */}

                  <div className="flex justify-between pt-8 mt-4 border-t">
                    <div className="flex gap-2">
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive">Delete Data</Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This will delete all your saved data including videos and settings.
                              This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => deleteData.mutate()}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>

                      {settings?.isAdmin && (
                        <Link href="/admin/settings">
                          <Button variant="outline">Server Settings</Button>
                        </Link>
                      )}
                    </div>

                    {updateSettings.isPending && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving changes...
                      </div>
                    )}
                    {updateSettings.isSuccess && !updateSettings.isPending && (
                      <div className="flex items-center text-sm text-green-500">
                        <span className="mr-2">✓</span>
                        Changes saved
                      </div>
                    )}
                  </div>
                </form>
              </Form>
            </TabsContent>

            <TabsContent value="playback">
              <Form {...form}>
                <form
                  className="space-y-6 bg-card p-6 rounded-lg shadow-sm border border-border/50"
                >
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="useInAppPlayer"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Hover Preview</FormLabel>
                            <FormDescription>Show video preview when hovering over thumbnails</FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={(checked) => {
                                console.log('Hover preview changed to:', checked);

                                // Update the form value
                                field.onChange(checked);
                                form.setValue('useInAppPlayer', checked);

                                // Save settings automatically
                                saveSettings();
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="previewSound"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Preview Sound</FormLabel>
                            <FormDescription>Enable sound for video previews</FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={(checked) => {
                                console.log('Preview sound changed to:', checked);

                                // Update the form value
                                field.onChange(checked);
                                form.setValue('previewSound', checked);

                                // Save settings automatically
                                saveSettings();
                              }}
                              disabled={!form.watch("useInAppPlayer")}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="playbackSound"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Playback Sound</FormLabel>
                            <FormDescription>Enable sound when playing videos</FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value === true}
                              onCheckedChange={(checked) => {
                                console.log('Playback sound changed to:', checked);

                                // Force update the form value
                                field.onChange(checked);
                                form.setValue('playbackSound', checked);

                                // Save settings automatically
                                saveSettings();
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </form>
              </Form>
            </TabsContent>

            <TabsContent value="display">
              <Form {...form}>
                <form
                  className="space-y-6 bg-card p-6 rounded-lg shadow-sm border border-border/50"
                >
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="darkMode"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Dark Mode</FormLabel>
                            <FormDescription>Enable dark mode appearance</FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={(checked) => {
                                console.log('Dark mode changed to:', checked);

                                // Update the form value
                                field.onChange(checked);
                                form.setValue('darkMode', checked);

                                // Save settings automatically
                                saveSettings();

                                // Toggle dark mode class on document immediately
                                if (checked) {
                                  document.documentElement.classList.add('dark');
                                } else {
                                  document.documentElement.classList.remove('dark');
                                }
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </form>
              </Form>
            </TabsContent>

            <TabsContent value="ai">
              {isAiTabActive && (
                <Form {...form}>
                  <div className="space-y-6 bg-card p-6 rounded-lg shadow-sm border border-border/50">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-semibold">AI Analysis Settings</h2>
                      <Button
                        onClick={() => {
                          // Get current form values
                          const formValues = form.getValues();
                          console.log('Saving AI settings:', formValues);

                          // Get all current settings
                          const allSettings = settings || {};

                          // Create a complete settings object with proper boolean conversions
                          const completeSettings = {
                            ...allSettings,
                            // AI-specific settings
                            openRouterModel: formValues.openRouterModel || 'google/gemini-2.0-flash-exp:free',
                            selectedPromptId: formValues.selectedPromptId || null,
                            // Ensure boolean values are properly converted
                            autoAnalyzeOnRefresh: formValues.autoAnalyzeOnRefresh === true,
                            // Include other critical settings with proper type handling
                            homePage: formValues.homePage || 'trendy',
                            darkMode: formValues.darkMode === true,
                            playbackSound: formValues.playbackSound === true,
                            useInAppPlayer: formValues.useInAppPlayer === true,
                            previewSound: formValues.previewSound === true,
                            removeDuplicates: formValues.removeDuplicates === true
                          };

                          // Show saving indicator
                          toast({
                            title: "Saving settings...",
                            description: "Please wait while your settings are saved.",
                          });

                          // Direct API call to save settings
                          fetch('/api/settings', {
                            method: 'PUT',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(completeSettings),
                          })
                          .then(response => {
                            if (!response.ok) {
                              throw new Error('Failed to save settings');
                            }
                            return response.json();
                          })
                          .then(data => {
                            console.log('Settings saved successfully:', data);

                            // Show success toast
                            toast({
                              title: "Settings saved",
                              description: "Your AI settings have been saved successfully.",
                              variant: "success",
                            });

                            // Update form with returned values to ensure consistency
                            const cleanData = {
                              ...data,
                              autoAnalyzeOnRefresh: data.autoAnalyzeOnRefresh === true,
                              openRouterModel: data.openRouterModel || 'google/gemini-2.0-flash-exp:free',
                              selectedPromptId: data.selectedPromptId || null
                            };

                            form.reset(cleanData);
                          })
                          .catch(error => {
                            console.error('Error saving settings:', error);

                            // Show error toast
                            toast({
                              title: "Error saving settings",
                              description: error.message || "An unknown error occurred",
                              variant: "destructive",
                            });
                          });
                        }}
                        type="button"
                      >
                        Save Settings
                      </Button>
                    </div>
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="autoAnalyzeOnRefresh"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Auto-Analyze on Channel Refresh</FormLabel>
                            <FormDescription>Automatically run AI analysis when refreshing channels</FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value === true}
                              onCheckedChange={(checked) => {
                                // Explicitly convert to boolean
                                const boolValue = checked === true;

                                // Update the form value
                                field.onChange(boolValue);

                                // Get all current settings
                                const allSettings = settings || {};

                                // Create a complete settings object with all required fields
                                const completeSettings = {
                                  ...allSettings,
                                  autoAnalyzeOnRefresh: boolValue,
                                  // Include other critical settings to ensure they're not lost
                                  homePage: allSettings.homePage || 'trendy',
                                  darkMode: allSettings.darkMode !== undefined ? allSettings.darkMode : false,
                                  playbackSound: allSettings.playbackSound !== undefined ? allSettings.playbackSound : true,
                                  useInAppPlayer: allSettings.useInAppPlayer !== undefined ? allSettings.useInAppPlayer : true,
                                  openRouterModel: allSettings.openRouterModel || 'google/gemini-2.0-flash-exp:free',
                                  // Include any selected prompt ID
                                  selectedPromptId: allSettings.selectedPromptId || null,
                                };

                                // Direct API call to save settings
                                fetch('/api/settings', {
                                  method: 'PUT',
                                  headers: {
                                    'Content-Type': 'application/json',
                                  },
                                  body: JSON.stringify(completeSettings),
                                })
                                .then(response => {
                                  if (!response.ok) {
                                    throw new Error('Failed to save settings');
                                  }
                                  return response.json();
                                })
                                .then(data => {
                                  // Ensure the form value matches what the server returned
                                  const serverValue = data.autoAnalyzeOnRefresh === true;
                                  if (serverValue !== boolValue) {
                                    console.warn('Server returned different value than what was sent');
                                    form.setValue('autoAnalyzeOnRefresh', serverValue);
                                  }
                                })
                                .catch(error => {
                                  console.error('Error saving auto-analyze setting:', error);
                                  // Revert to original value on error
                                  form.setValue('autoAnalyzeOnRefresh', field.value === true);
                                });
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="openRouterModel"
                      render={({ field }) => {
                        const [availableModels, setAvailableModels] = useState<{name: string}[]>([]);
                        const [isLoadingModels, setIsLoadingModels] = useState(false);
                        const [modelError, setModelError] = useState<string | null>(null);
                        const [editDialogOpen, setEditDialogOpen] = useState(false);
                        const [customModelInput, setCustomModelInput] = useState("");
                        const [fetchedModels, setFetchedModels] = useState<{name: string}[]>([]);
                        const [isFetchingModels, setIsFetchingModels] = useState(false);
                        const [modelSearchQuery, setModelSearchQuery] = useState("");

                        // Fetch available models from OpenRouter API
                        const fetchOpenRouterModels = useCallback(async () => {
                          try {
                            setIsFetchingModels(true);
                            const response = await fetch('/api/openrouter/models');
                            if (!response.ok) {
                              throw new Error('Failed to fetch models');
                            }
                            const data = await response.json();
                            setFetchedModels(data);
                            console.log('Fetched models:', data);

                            // Get the current model value
                            const currentModel = field.value || "google/gemini-2.0-flash-exp:free";
                            console.log('Current model value:', currentModel);

                            // Make sure the current model is in the available models list
                            const existingModelNames = new Set(availableModels.map(model => model.name));
                            const newModels = data.filter(model => !existingModelNames.has(model.name));

                            // Check if current model is in either list
                            const currentModelInExisting = existingModelNames.has(currentModel);
                            const currentModelInNew = newModels.some(model => model.name === currentModel);

                            // If current model is not in either list, add it
                            let updatedModels = [...availableModels];
                            if (!currentModelInExisting && !currentModelInNew) {
                              updatedModels = [{ name: currentModel }, ...updatedModels];
                              console.log('Added current model to available models:', currentModel);
                            }

                            // Add new models from API
                            if (newModels.length > 0) {
                              updatedModels = [...updatedModels, ...newModels];
                              console.log('Added new models from API:', newModels.map(m => m.name));
                            }

                            // Update available models if changes were made
                            if (!currentModelInExisting || newModels.length > 0) {
                              setAvailableModels(updatedModels);
                              console.log('Updated available models:', updatedModels.map(m => m.name));
                            }
                          } catch (error) {
                            console.error('Error fetching models:', error);
                            setModelError('Failed to fetch models. Using default list.');

                            // Even if fetch fails, make sure current model is in the list
                            const currentModel = field.value || "google/gemini-2.0-flash-exp:free";
                            const modelExists = availableModels.some(model => model.name === currentModel);
                            if (!modelExists) {
                              const updatedModels = [{ name: currentModel }, ...availableModels];
                              setAvailableModels(updatedModels);
                              console.log('Added current model to available models after fetch error:', currentModel);
                            }
                          } finally {
                            setIsFetchingModels(false);
                          }
                        }, [availableModels, field.value]);

                        // Initialize models list with hardcoded models and current model
                        useEffect(() => {
                          // Set the models directly - only Gemini 2.0 Flash
                          const defaultModels = [
                            { name: 'google/gemini-2.0-flash-exp:free' }
                          ];

                          // Get the current model from settings
                          const currentModel = field.value || "google/gemini-2.0-flash-exp:free";

                          // Check if current model is in default models
                          const currentModelInDefaults = defaultModels.some(model => model.name === currentModel);

                          // Create final models list with current model at the top if it's not in defaults
                          let finalModels = [...defaultModels];
                          if (!currentModelInDefaults) {
                            finalModels = [{ name: currentModel }, ...finalModels];
                          }

                          setAvailableModels(finalModels);
                          setIsLoadingModels(false);

                          // If no model is selected, use the first one
                          if (!field.value) {
                            field.onChange(defaultModels[0].name);

                            // Get all current settings
                            const allSettings = settings || {};

                            // Create a complete settings object
                            const completeSettings = {
                              ...allSettings,
                              openRouterModel: defaultModels[0].name,
                              // Make sure we include homePage and other critical settings
                              homePage: allSettings.homePage || 'trendy',
                              darkMode: allSettings.darkMode !== undefined ? allSettings.darkMode : false,
                              playbackSound: allSettings.playbackSound !== undefined ? allSettings.playbackSound : true,
                              useInAppPlayer: allSettings.useInAppPlayer !== undefined ? allSettings.useInAppPlayer : true,
                              // Explicitly include autoAnalyzeOnRefresh to ensure it's saved
                              autoAnalyzeOnRefresh: allSettings.autoAnalyzeOnRefresh === true ? true : false
                            };

                            // Direct API call to save settings
                            fetch('/api/settings', {
                              method: 'PUT',
                              headers: {
                                'Content-Type': 'application/json',
                              },
                              body: JSON.stringify(completeSettings),
                            })
                            .then(response => response.json())
                            .catch(error => {
                              console.error('Error saving default model:', error);
                            });
                          }
                        }, [field, updateSettings]);

                        // State for error message and loading state
                        const [saveError, setSaveError] = useState<string | null>(null);
                        const [isSaving, setIsSaving] = useState(false);

                        // Handle custom model save
                        const handleSaveCustomModel = () => {
                          if (!customModelInput.trim()) {
                            return;
                          }

                          // Reset error state
                          setSaveError(null);
                          setIsSaving(true);
                          console.log('Saving custom model:', customModelInput);

                          // Add the custom model to the available models list if it's not already there
                          const modelExists = availableModels.some(model => model.name === customModelInput);
                          if (!modelExists) {
                            // Create a new array with the custom model at the beginning for visibility
                            const updatedModels = [{ name: customModelInput }, ...availableModels];
                            setAvailableModels(updatedModels);
                            console.log('Added custom model to available models:', updatedModels);
                          }

                          // Update the form value AFTER updating the available models
                          field.onChange(customModelInput);
                          console.log('Updated form value with custom model:', customModelInput);

                          // Get all current settings
                          const allSettings = settings || {};

                          // Create a complete settings object
                          const completeSettings = {
                            ...allSettings,
                            openRouterModel: customModelInput,
                            // Make sure we include homePage and other critical settings
                            homePage: allSettings.homePage || 'trendy',
                            darkMode: allSettings.darkMode !== undefined ? allSettings.darkMode : false,
                            playbackSound: allSettings.playbackSound !== undefined ? allSettings.playbackSound : true,
                            useInAppPlayer: allSettings.useInAppPlayer !== undefined ? allSettings.useInAppPlayer : true,
                            // Explicitly include autoAnalyzeOnRefresh to ensure it's saved
                            autoAnalyzeOnRefresh: allSettings.autoAnalyzeOnRefresh === true ? true : false
                          };

                          // Direct API call to save settings
                          fetch('/api/settings', {
                            method: 'PUT',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(completeSettings),
                          })
                          .then(response => response.json())
                          .then(data => {
                            console.log('Custom model saved successfully:', data);
                            setIsSaving(false);

                            // Force a refresh of the form with the new model
                            if (data && data.openRouterModel) {
                              form.setValue('openRouterModel', data.openRouterModel);
                            }

                            // Close the dialog
                            setEditDialogOpen(false);

                            // Force a UI refresh
                            setTimeout(() => {
                              const event = new Event('change');
                              document.dispatchEvent(event);
                            }, 100);

                            // Test the model with a simple API call to verify it works
                            fetch('/api/openrouter/test-connection?model=' + encodeURIComponent(customModelInput))
                              .then(response => response.json())
                              .then(testData => {
                                if (!testData.success) {
                                  console.warn('Model may not be valid:', testData.message);
                                  // Show a toast notification to the user
                                  toast({
                                    title: "Model Connection Issue",
                                    description: "There was a problem connecting to OpenRouter. Your model was saved, but may not work correctly.",
                                    variant: "destructive",
                                  });
                                } else if (testData.modelTested && !testData.modelTestSuccessful) {
                                  console.warn('Model test failed:', testData.modelTestError);

                                  // Check if this is a model availability error
                                  if (testData.isModelAvailabilityError) {
                                    // Show a more specific toast notification for model availability issues
                                    toast({
                                      title: "Model Unavailable",
                                      description: `The model "${customModelInput}" appears to be unavailable. Please try a different model from the list.`,
                                      variant: "destructive",
                                    });

                                    // Reopen the dialog to let the user select a different model
                                    setTimeout(() => {
                                      setEditDialogOpen(true);
                                    }, 1000);
                                  } else {
                                    // Show a general warning for other test failures
                                    toast({
                                      title: "Model Test Failed",
                                      description: `The model "${customModelInput}" was saved but failed a test. It may not work correctly for analysis.`,
                                      variant: "warning",
                                    });
                                  }
                                }
                              })
                              .catch(testError => {
                                console.warn('Error testing model:', testError);
                                // Show a toast notification to the user
                                toast({
                                  title: "Model Test Error",
                                  description: "There was an error testing the model. It was saved but may not work correctly.",
                                  variant: "destructive",
                                });
                              });
                          })
                          .catch(error => {
                            console.error('Error saving custom model:', error);
                            setIsSaving(false);
                            setSaveError('Failed to save model. Please try again.');
                          });
                        };

                        // Open edit dialog and initialize with current model
                        const handleOpenEditDialog = () => {
                          // If the current model is the problematic one, default to a reliable model
                          if (field.value === 'thudm/glm-z1-32b:free') {
                            setCustomModelInput("google/gemini-2.0-flash-exp:free");
                          } else {
                            setCustomModelInput(field.value || "google/gemini-2.0-flash-exp:free");
                          }
                          setModelSearchQuery(""); // Reset search query
                          fetchOpenRouterModels();
                          setEditDialogOpen(true);
                        };

                        return (
                          <FormItem>
                            <FormLabel>AI Model</FormLabel>
                            <div className="flex items-center gap-2">
                              <div className="flex-1">
                                <Select
                                  onValueChange={(value) => {
                                    console.log('Model selection changed to:', value);

                                    // Update the form value
                                    field.onChange(value);

                                    // Show a toast notification
                                    toast({
                                      title: "Updating model...",
                                      description: "Saving your model selection",
                                    });

                                    // Get all current settings
                                    const allSettings = settings || {};

                                    // Create a complete settings object with proper boolean conversions
                                    const completeSettings = {
                                      ...allSettings,
                                      openRouterModel: value,
                                      // Ensure boolean values are properly converted
                                      autoAnalyzeOnRefresh: form.getValues('autoAnalyzeOnRefresh') === true,
                                      // Include other critical settings with proper type handling
                                      homePage: allSettings.homePage || 'trendy',
                                      darkMode: allSettings.darkMode === true,
                                      playbackSound: allSettings.playbackSound === true,
                                      useInAppPlayer: allSettings.useInAppPlayer === true,
                                      // Ensure null values are properly handled
                                      selectedPromptId: form.getValues('selectedPromptId') || null
                                    };

                                    // Direct API call to save settings
                                    fetch('/api/settings', {
                                      method: 'PUT',
                                      headers: {
                                        'Content-Type': 'application/json',
                                      },
                                      body: JSON.stringify(completeSettings),
                                    })
                                    .then(response => {
                                      if (!response.ok) {
                                        throw new Error('Failed to save model selection');
                                      }
                                      return response.json();
                                    })
                                    .then(data => {
                                      console.log('Model selection saved successfully:', data);

                                      // Show success toast
                                      toast({
                                        title: "Model updated",
                                        description: `Model set to ${value}`,
                                        variant: "success",
                                      });

                                      // Update form with returned value to ensure consistency
                                      if (data.openRouterModel && data.openRouterModel !== value) {
                                        field.onChange(data.openRouterModel);
                                      }
                                    })
                                    .catch(error => {
                                      console.error('Error saving model setting:', error);

                                      // Show error toast
                                      toast({
                                        title: "Error updating model",
                                        description: error.message || "An unknown error occurred",
                                        variant: "destructive",
                                      });

                                      // Revert to previous value
                                      field.onChange(allSettings.openRouterModel || 'google/gemini-2.0-flash-exp:free');
                                    });
                                  }}
                                  value={field.value || "google/gemini-2.0-flash-exp:free"}
                                  disabled={isLoadingModels}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select AI model" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {availableModels.map((model) => (
                                      <SelectItem key={model.name} value={model.name}>
                                        {model.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={handleOpenEditDialog}
                                className="flex-shrink-0"
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                            </div>
                            <FormDescription>
                              Select the AI model to use for benefits analysis.
                              {field.value === 'thudm/glm-z1-32b:free' && (
                                <div className="mt-2 text-destructive text-xs">
                                  Warning: This model has known availability issues. Please select a different model from the recommended list.
                                </div>
                              )}
                            </FormDescription>

                            {/* Edit Model Dialog */}
                            <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>Edit AI Model</DialogTitle>
                                  <DialogDescription>
                                    Enter a custom model identifier or select from available models.
                                  </DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4 py-4">
                                  <div className="space-y-2">
                                    <Label htmlFor="custom-model">Custom Model Identifier</Label>
                                    <Input
                                      id="custom-model"
                                      placeholder="e.g., anthropic/claude-3-opus:beta"
                                      value={customModelInput}
                                      onChange={(e) => setCustomModelInput(e.target.value)}
                                    />
                                    <p className="text-sm text-muted-foreground">
                                      Enter the full model identifier as shown in OpenRouter.
                                    </p>
                                  </div>

                                  {/* Recommended Models Section */}
                                  <div className="space-y-2 mt-4">
                                    <Label>Recommended Models</Label>
                                    <div className="grid grid-cols-1 gap-2">
                                      {[
                                        { name: 'google/gemini-2.0-flash-exp:free', description: 'Fast and reliable (Recommended)' }
                                      ].map((model) => (
                                        <Button
                                          key={model.name}
                                          variant="outline"
                                          className={`justify-start h-auto py-2 px-3 text-left ${customModelInput === model.name ? 'border-primary' : ''}`}
                                          onClick={() => {
                                            setCustomModelInput(model.name);
                                          }}
                                        >
                                          <div>
                                            <div className="font-medium">{model.name}</div>
                                            <div className="text-xs text-muted-foreground">{model.description}</div>
                                          </div>
                                        </Button>
                                      ))}
                                    </div>
                                  </div>

                                  {fetchedModels.length > 0 && (
                                    <div className="space-y-2 mt-4">
                                      <Label>All Available Models</Label>
                                      <div className="mb-2">
                                        <Input
                                          placeholder="Search models..."
                                          value={modelSearchQuery}
                                          onChange={(e) => setModelSearchQuery(e.target.value)}
                                        />
                                      </div>
                                      <div className="max-h-60 overflow-y-auto border rounded-md p-2">
                                        {fetchedModels
                                          .filter(model =>
                                            model.name.toLowerCase().includes(modelSearchQuery.toLowerCase())
                                          )
                                          .map((model) => (
                                            <div
                                              key={model.name}
                                              className={`p-2 hover:bg-muted rounded-md cursor-pointer ${customModelInput === model.name ? 'bg-muted font-medium' : ''}`}
                                              onClick={() => setCustomModelInput(model.name)}
                                              onDoubleClick={() => {
                                                setCustomModelInput(model.name);
                                                handleSaveCustomModel();
                                              }}
                                              title="Click to select, double-click to select and save"
                                            >
                                              {model.name}
                                            </div>
                                          ))}
                                        {fetchedModels.filter(model =>
                                          model.name.toLowerCase().includes(modelSearchQuery.toLowerCase())
                                        ).length === 0 && (
                                          <div className="p-2 text-muted-foreground text-center">
                                            No models found matching "{modelSearchQuery}"
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}

                                  {isFetchingModels && (
                                    <div className="flex items-center justify-center py-2">
                                      <Loader2 className="h-5 w-5 animate-spin mr-2" />
                                      <span>Loading models...</span>
                                    </div>
                                  )}
                                </div>
                                {saveError && (
                                  <div className="bg-destructive/15 text-destructive p-3 rounded-md mb-3">
                                    {saveError}
                                  </div>
                                )}
                                <DialogFooter>
                                  <Button variant="outline" onClick={() => setEditDialogOpen(false)} disabled={isSaving}>
                                    Cancel
                                  </Button>
                                  <Button
                                    onClick={handleSaveCustomModel}
                                    disabled={!customModelInput.trim() || isSaving}
                                  >
                                    {isSaving ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Saving...
                                      </>
                                    ) : (
                                      'Save Model'
                                    )}
                                  </Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                          </FormItem>
                        );
                      }}
                    />
                  </div>

                  <div className="col-span-2 mt-6">
                    <div className="mb-6">
                      <PromptsManager
                        key={`prompts-manager-${settings?.selectedPromptId || 'none'}-${promptsManagerKey}`}
                        selectedPromptId={settings?.selectedPromptId || null}
                        onPromptSelected={(promptId) => {
                          // Set the form value
                          form.setValue('selectedPromptId', promptId);

                          // Save settings immediately when a prompt is selected
                          // Get all current settings
                          const allSettings = settings || {};

                          // Create a complete settings object
                          const completeSettings = {
                            ...allSettings,
                            selectedPromptId: promptId,
                            // Make sure we include homePage and other critical settings
                            homePage: allSettings.homePage || 'trendy',
                            darkMode: allSettings.darkMode !== undefined ? allSettings.darkMode : false,
                            playbackSound: allSettings.playbackSound !== undefined ? allSettings.playbackSound : true,
                            useInAppPlayer: allSettings.useInAppPlayer !== undefined ? allSettings.useInAppPlayer : true,
                            // Explicitly include autoAnalyzeOnRefresh to ensure it's saved
                            autoAnalyzeOnRefresh: allSettings.autoAnalyzeOnRefresh === true ? true : false
                          };

                          // Direct API call to save settings
                          fetch('/api/settings', {
                            method: 'PUT',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(completeSettings),
                          })
                          .then(response => response.json())
                          .then(data => {
                            console.log('Prompt selection saved successfully:', data);
                            // Make sure we stay on the AI tab
                            setActiveTab('ai');
                            // Force re-render of the OllamaPromptsManager component
                            setPromptsManagerKey(prev => prev + 1);
                          })
                          .catch(error => {
                            console.error('Error saving prompt selection:', error);
                          });
                        }}
                        onPromptTextChanged={(promptText) => {
                          // Update the form value but don't auto-save
                          form.setValue('ollamaAnalysisPrompt', promptText);
                        }}
                      />
                    </div>

                    {/* Removed redundant AI Analysis Prompt textarea - prompts are now managed through the OllamaPromptsManager component above */}
                  </div>

                  <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                    <h3 className="text-lg font-medium mb-2">About AI Integration</h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      Trendy uses OpenRouter API to analyze YouTube video transcripts for benefits.
                      This analysis happens in the background after transcripts are fetched.
                    </p>
                    <p className="text-sm text-muted-foreground mb-2">
                      The selected model will be used for all analyses and will be displayed in the AI Analysis tab for each video.
                      The prompt template includes a clear marker where the video transcript will be inserted during analysis.
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Currently using Google's Gemini 2.0 Flash model via OpenRouter for all AI analysis.
                    </p>
                  </div>
                </div>
              </Form>
              )}
            </TabsContent>

            <TabsContent value="api-keys">
              <div className="space-y-8">
                <div className="bg-card rounded-lg border p-6">
                  <ApiKeyManager />
                </div>

                <div className="bg-card rounded-lg border p-6">
                  <OpenRouterApiKeyManager />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
