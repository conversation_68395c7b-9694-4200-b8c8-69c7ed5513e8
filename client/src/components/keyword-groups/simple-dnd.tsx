import React, { useState } from 'react';
import { KeywordGroup } from '@shared/schema';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, ChevronDown, ChevronUp, Pencil, Plus, RefreshCw, Trash2, X, GripVertical } from "lucide-react";
import { useSettings } from "@/hooks/use-settings";
import { formatRefreshTime } from "@/lib/date-format";

interface DraggableItemProps {
  item: KeywordGroup;
  index: number;
  moveItem: (dragIndex: number, hoverIndex: number) => void;
  expandedGroups: Record<string, boolean>;
  toggleGroupExpansion: (groupId: string) => void;
  openEditDialog: (group: KeywordGroup) => void;
  openDeleteDialog?: (group: KeywordGroup) => void;
  handleRefreshGroup: (groupId: number) => void;
  handleSetActiveGroup: (groupId: number | null) => void;
  openAddKeywordDialog: (groupId: number | null) => void;
  handleRemoveKeyword: (groupId: number, keyword: string) => void;
  refreshGroupVideos: any;
  setActiveGroup: any;
  isGlobal?: boolean;
}

export const DraggableItem: React.FC<DraggableItemProps> = ({
  item,
  index,
  moveItem,
  expandedGroups,
  toggleGroupExpansion,
  openEditDialog,
  openDeleteDialog,
  handleRefreshGroup,
  handleSetActiveGroup,
  openAddKeywordDialog,
  handleRemoveKeyword,
  refreshGroupVideos,
  setActiveGroup,
  isGlobal = false
}) => {
  const { settings } = useSettings();
  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>) => {
    e.dataTransfer.setData('text/plain', index.toString());
    e.dataTransfer.effectAllowed = 'move';
    setIsDragging(true);

    // Add a delay to set the drag image
    setTimeout(() => {
      if (e.target instanceof HTMLElement) {
        const card = e.target.closest('.draggable-card');
        if (card instanceof HTMLElement) {
          const rect = card.getBoundingClientRect();
          const ghostElement = card.cloneNode(true) as HTMLElement;

          // Style the ghost element
          ghostElement.style.position = 'absolute';
          ghostElement.style.top = '-1000px';
          ghostElement.style.opacity = '0.8';
          ghostElement.style.transform = 'scale(0.8)';
          ghostElement.style.width = `${rect.width}px`;
          ghostElement.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.2)';
          ghostElement.style.borderRadius = '8px';
          ghostElement.style.overflow = 'hidden';
          ghostElement.style.pointerEvents = 'none';

          document.body.appendChild(ghostElement);
          e.dataTransfer.setDragImage(ghostElement, rect.width / 2, 30);

          // Remove the ghost element after a short delay
          setTimeout(() => {
            document.body.removeChild(ghostElement);
          }, 100);
        }
      }
    }, 10);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.currentTarget.classList.add('drag-over');
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.currentTarget.classList.remove('drag-over');
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.currentTarget.classList.remove('drag-over');
    const dragIndex = parseInt(e.dataTransfer.getData('text/plain'), 10);
    moveItem(dragIndex, index);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const groupId = isGlobal ? 'global' : item.id.toString();

  return (
    <div
      className={`draggable-item ${isDragging ? 'dragging' : ''}`}
      draggable
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onDragEnd={handleDragEnd}
    >
      <Card
        className={`draggable-card ${settings?.activeKeywordGroupId === (isGlobal ? null : item.id) ? "border-primary" : ""} ${isDragging ? "opacity-50" : ""}`}
      >
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <div className="cursor-grab p-1 hover:bg-muted rounded-md transition-colors flex items-center justify-center bg-muted/30 h-6 w-6">
                <GripVertical className="h-4 w-4 text-primary" />
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="p-0 h-6 w-6"
                onClick={() => toggleGroupExpansion(groupId)}
              >
                {expandedGroups[groupId] ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
              <span>{item.name}</span>
            </div>
            {settings?.activeKeywordGroupId === (isGlobal ? null : item.id) && (
              <Badge variant="outline" className="ml-2">
                <Check className="h-3 w-3 mr-1" />
                Active
              </Badge>
            )}
          </CardTitle>
          <CardDescription className="font-sans">
            Last refreshed: <span className="font-semibold">{formatRefreshTime(item.lastRefreshTime)}</span>
          </CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-3">
            <div>
              <div className="flex justify-between items-center mb-1">
                <div className="flex items-center gap-2">
                  <h4 className="text-sm font-semibold">Keywords:</h4>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      openAddKeywordDialog(isGlobal ? 0 : item.id);
                    }}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
                <Badge variant="outline">{item.keywords.length}</Badge>
              </div>
              {expandedGroups[groupId] ? (
                <div className="flex flex-wrap gap-2 mt-2">
                  {item.keywords.map((keyword) => (
                    <Badge key={keyword} variant="secondary" className="group relative pr-6">
                      {keyword}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveKeyword(isGlobal ? 0 : item.id, keyword);
                        }}
                        className="absolute right-1 opacity-0 group-hover:opacity-100 hover:text-destructive transition-opacity"
                        aria-label={`Remove keyword ${keyword}`}
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  {item.keywords.length} keywords
                </div>
              )}
            </div>

            {item.excludeWords && item.excludeWords.length > 0 && (
              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-semibold">Exclude Words:</h4>
                  <Badge variant="outline">{item.excludeWords.length}</Badge>
                </div>
                {expandedGroups[groupId] ? (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {item.excludeWords.map((word) => (
                      <Badge key={word} variant="outline" className="group relative pr-6">
                        {word}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle remove exclude word
                          }}
                          className="absolute right-1 opacity-0 group-hover:opacity-100 hover:text-destructive transition-opacity"
                          aria-label={`Remove exclude word ${word}`}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">
                    {item.excludeWords.length} exclude words
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => openEditDialog(item)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            {!isGlobal && openDeleteDialog && (
              <Button
                variant="outline"
                size="icon"
                onClick={() => openDeleteDialog(item)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="outline"
              size="icon"
              onClick={() => handleRefreshGroup(isGlobal ? 0 : item.id)}
              disabled={refreshGroupVideos.isPending}
            >
              <RefreshCw className={`h-4 w-4 ${refreshGroupVideos.isPending ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          <Button
            variant={settings?.activeKeywordGroupId === (isGlobal ? null : item.id) ? "default" : "outline"}
            onClick={() => handleSetActiveGroup(isGlobal ? null : item.id)}
            disabled={setActiveGroup.isPending}
          >
            {settings?.activeKeywordGroupId === (isGlobal ? null : item.id) ? "Active" : "Set Active"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

interface DraggableListProps {
  items: KeywordGroup[];
  onReorder: (items: KeywordGroup[]) => void;
  expandedGroups: Record<string, boolean>;
  toggleGroupExpansion: (groupId: string) => void;
  openEditDialog: (group: KeywordGroup) => void;
  openDeleteDialog: (group: KeywordGroup) => void;
  handleRefreshGroup: (groupId: number) => void;
  handleSetActiveGroup: (groupId: number | null) => void;
  openAddKeywordDialog: (groupId: number | null) => void;
  handleRemoveGroupKeyword: (groupId: number, keyword: string) => void;
  handleRemoveGlobalKeyword: (keyword: string) => void;
  refreshGroupVideos: any;
  refreshGlobalVideos: any;
  setActiveGroup: any;
  settings: any;
}

export const DraggableList: React.FC<DraggableListProps> = ({
  items,
  onReorder,
  expandedGroups,
  toggleGroupExpansion,
  openEditDialog,
  openDeleteDialog,
  handleRefreshGroup,
  handleSetActiveGroup,
  openAddKeywordDialog,
  handleRemoveGroupKeyword,
  handleRemoveGlobalKeyword,
  refreshGroupVideos,
  refreshGlobalVideos,
  setActiveGroup,
  settings
}) => {
  // Create a global feed pseudo-item
  const globalFeed: KeywordGroup = {
    id: 0,
    userId: 0,
    name: "Global Feed",
    keywords: settings?.searchKeywords || [],
    excludeWords: settings?.excludeWords || [],
    lastRefreshTime: settings?.lastRefreshTime || null,
    displayOrder: 0
  };

  // State to track the current order of items
  const [orderedItems, setOrderedItems] = useState<KeywordGroup[]>(items);

  // Update ordered items when items prop changes
  React.useEffect(() => {
    setOrderedItems(items);
  }, [items]);

  const moveItem = (dragIndex: number, hoverIndex: number) => {
    // Adjust indices to account for the global feed item
    const dragIndexAdjusted = dragIndex === 0 ? 0 : dragIndex - 1;
    const hoverIndexAdjusted = hoverIndex === 0 ? 0 : hoverIndex - 1;

    // Don't allow moving the global feed
    if (dragIndex === 0) return;

    // Don't allow dropping on the global feed
    if (hoverIndex === 0) return;

    // Create a new array with the updated order
    const newItems = [...orderedItems];
    const [movedItem] = newItems.splice(dragIndexAdjusted, 1);
    newItems.splice(hoverIndexAdjusted, 0, movedItem);

    // Update the display order
    const reorderedItems = newItems.map((item, index) => ({
      ...item,
      displayOrder: index
    }));

    // Update the state
    setOrderedItems(reorderedItems);

    // Add a visual effect to show the item has been moved
    setTimeout(() => {
      const items = document.querySelectorAll('.draggable-item');
      items.forEach((item, i) => {
        if (i === hoverIndex) {
          item.classList.add('just-moved');
          setTimeout(() => {
            item.classList.remove('just-moved');
          }, 500);
        }
      });
    }, 50);

    // Notify parent component
    onReorder(reorderedItems);
  };

  return (
    <div>

      <div className="grid-container">
        {/* Global Feed (always first) */}
        <DraggableItem
          item={globalFeed}
          index={0}
          moveItem={moveItem}
          expandedGroups={expandedGroups}
          toggleGroupExpansion={toggleGroupExpansion}
          openEditDialog={openEditDialog}
          handleRefreshGroup={handleRefreshGroup}
          handleSetActiveGroup={handleSetActiveGroup}
          openAddKeywordDialog={openAddKeywordDialog}
          handleRemoveKeyword={handleRemoveGlobalKeyword}
          refreshGroupVideos={refreshGlobalVideos}
          setActiveGroup={setActiveGroup}
          isGlobal={true}
        />

        {/* Keyword Groups */}
        {orderedItems.map((item, index) => (
          <DraggableItem
            key={item.id}
            item={item}
            index={index + 1} // +1 because global feed is at index 0
            moveItem={moveItem}
            expandedGroups={expandedGroups}
            toggleGroupExpansion={toggleGroupExpansion}
            openEditDialog={openEditDialog}
            openDeleteDialog={openDeleteDialog}
            handleRefreshGroup={handleRefreshGroup}
            handleSetActiveGroup={handleSetActiveGroup}
            openAddKeywordDialog={openAddKeywordDialog}
            handleRemoveKeyword={handleRemoveGroupKeyword}
            refreshGroupVideos={refreshGroupVideos}
            setActiveGroup={setActiveGroup}
          />
        ))}
      </div>
    </div>
  );
};
