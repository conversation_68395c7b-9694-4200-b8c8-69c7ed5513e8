// request-manager.ts
// This utility manages API requests with priority and throttling

// Define request priority levels
export enum RequestPriority {
  HIGH = 0,   // User-initiated actions, video loading
  MEDIUM = 1, // Manual refresh operations
  LOW = 2     // Auto-refresh operations
}

// Define request types
export enum RequestType {
  VIDEO_LOAD = 'video_load',
  VIDEO_REFRESH = 'video_refresh',
  GROUP_REFRESH = 'group_refresh',
  OTHER = 'other'
}

// Interface for request objects
interface RequestItem {
  id: string;
  type: RequestType;
  priority: RequestPriority;
  execute: () => Promise<any>;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  timestamp: number;
}

// Class to manage requests
export class RequestManager {
  private queue: RequestItem[] = [];
  private activeRequests: Set<string> = new Set();
  private maxConcurrent: number = 3; // Maximum concurrent requests
  private isProcessing: boolean = false;
  
  constructor(maxConcurrent: number = 3) {
    this.maxConcurrent = maxConcurrent;
  }
  
  // Add a request to the queue
  public addRequest(
    type: RequestType,
    priority: RequestPriority,
    execute: () => Promise<any>,
    onSuccess?: (result: any) => void,
    onError?: (error: any) => void
  ): string {
    // Generate a unique ID for the request
    const id = `${type}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    // Create the request item
    const request: RequestItem = {
      id,
      type,
      priority,
      execute,
      onSuccess,
      onError,
      timestamp: Date.now()
    };
    
    // Add to queue
    this.queue.push(request);
    
    // Sort the queue by priority and timestamp
    this.sortQueue();
    
    // Start processing if not already
    if (!this.isProcessing) {
      this.processQueue();
    }
    
    return id;
  }
  
  // Cancel a request by ID
  public cancelRequest(id: string): boolean {
    const index = this.queue.findIndex(req => req.id === id);
    
    if (index !== -1) {
      this.queue.splice(index, 1);
      return true;
    }
    
    return false;
  }
  
  // Sort the queue by priority and timestamp
  private sortQueue() {
    this.queue.sort((a, b) => {
      // First sort by priority (lower number = higher priority)
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      
      // Then sort by timestamp (older first)
      return a.timestamp - b.timestamp;
    });
  }
  
  // Process the queue
  private async processQueue() {
    if (this.queue.length === 0 || this.activeRequests.size >= this.maxConcurrent) {
      this.isProcessing = false;
      return;
    }
    
    this.isProcessing = true;
    
    // Get the next request
    const request = this.queue.shift();
    
    if (!request) {
      this.isProcessing = false;
      return;
    }
    
    // Add to active requests
    this.activeRequests.add(request.id);
    
    try {
      // Execute the request
      const result = await request.execute();
      
      // Call success callback
      if (request.onSuccess) {
        request.onSuccess(result);
      }
    } catch (error) {
      // Call error callback
      if (request.onError) {
        request.onError(error);
      }
      
      console.error(`Request ${request.id} failed:`, error);
    } finally {
      // Remove from active requests
      this.activeRequests.delete(request.id);
      
      // Continue processing the queue
      this.processQueue();
    }
    
    // Process more requests if possible
    if (this.activeRequests.size < this.maxConcurrent) {
      this.processQueue();
    }
  }
  
  // Get the current queue length
  public getQueueLength(): number {
    return this.queue.length;
  }
  
  // Get the number of active requests
  public getActiveRequestCount(): number {
    return this.activeRequests.size;
  }
  
  // Clear all requests of a specific type
  public clearRequestsByType(type: RequestType): number {
    const initialLength = this.queue.length;
    this.queue = this.queue.filter(req => req.type !== type);
    return initialLength - this.queue.length;
  }
  
  // Prioritize a specific type of request
  public prioritizeRequestType(type: RequestType, newPriority: RequestPriority): number {
    let count = 0;
    
    this.queue.forEach(req => {
      if (req.type === type && req.priority > newPriority) {
        req.priority = newPriority;
        count++;
      }
    });
    
    if (count > 0) {
      this.sortQueue();
    }
    
    return count;
  }
}

// Create and export a singleton instance
export const requestManager = new RequestManager(5); // Allow up to 5 concurrent requests

// Export a hook for React components
export function useRequestManager() {
  return requestManager;
}
