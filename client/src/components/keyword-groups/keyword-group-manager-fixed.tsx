import { useState, useRef, useEffect } from "react";
import { KeywordGroup } from "@shared/schema";
import { useKeywordGroups } from "@/hooks/use-keyword-groups";
import { useVideos } from "@/hooks/use-videos";
import { useSettings } from "@/hooks/use-settings";
import { useDragAndDrop } from "@/hooks/use-drag-and-drop";
import { DraggableKeywordGroup } from "./draggable-keyword-group";
import { DraggableGlobalFeed } from "./draggable-global-feed";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus, <PERSON>cil, Trash2, Refresh<PERSON>w, Check, Download, Upload, X, ChevronDown, ChevronUp, Eye, EyeOff } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export function KeywordGroupManager() {
  const { groups, isLoading, createGroup, updateGroup, deleteGroup, setActiveGroup, refreshGroupVideos, exportGroups, importGroups, reorderGroups } = useKeywordGroups();
  const { refresh: refreshGlobalVideos } = useVideos();
  const { settings, updateSettings } = useSettings();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<KeywordGroup | null>(null);

  const [newGroupName, setNewGroupName] = useState("");
  const [newGroupKeywords, setNewGroupKeywords] = useState("");
  const [newGroupExcludeWords, setNewGroupExcludeWords] = useState("");

  const [editGroupName, setEditGroupName] = useState("");
  const [editGroupKeywords, setEditGroupKeywords] = useState("");
  const [editGroupExcludeWords, setEditGroupExcludeWords] = useState("");

  const [importData, setImportData] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State for tracking expanded/collapsed groups
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  // Setup drag and drop
  const handleReorder = (reorderedItems: KeywordGroup[]) => {
    // Create the payload for the API
    const orderData = reorderedItems.map((item, index) => ({
      id: item.id,
      order: index
    }));
    
    // Call the API to update the order
    reorderGroups.mutate(orderData);
  };
  
  // Initialize drag and drop
  const { orderedItems, draggedItemId, getDragHandleProps } = useDragAndDrop<KeywordGroup>({
    items: groups || [],
    onReorder: handleReorder
  });

  const [showAllKeywords, setShowAllKeywords] = useState(false);

  // State for adding new keywords
  const [newKeyword, setNewKeyword] = useState("");
  const [isAddKeywordDialogOpen, setIsAddKeywordDialogOpen] = useState(false);
  const [addingKeywordToGroupId, setAddingKeywordToGroupId] = useState<number | null>(null);

  // Toggle group expansion
  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  // Open edit dialog for a group
  const openEditDialog = (group: KeywordGroup) => {
    setSelectedGroup(group);
    setEditGroupName(group.name);
    setEditGroupKeywords(group.keywords.join(", "));
    setEditGroupExcludeWords(group.excludeWords?.join(", ") || "");
    setIsEditDialogOpen(true);
  };

  // Open edit dialog for global settings
  const openEditGlobalDialog = () => {
    const globalGroup: KeywordGroup = {
      id: 0,
      userId: 0,
      name: "Global Feed",
      keywords: settings?.searchKeywords || [],
      excludeWords: settings?.excludeWords || [],
      lastRefreshTime: settings?.lastRefreshTime || null,
      displayOrder: 0
    };
    openEditDialog(globalGroup);
  };

  // Open delete dialog for a group
  const openDeleteDialog = (group: KeywordGroup) => {
    setSelectedGroup(group);
    setIsDeleteDialogOpen(true);
  };

  // Open add keyword dialog
  const openAddKeywordDialog = (groupId: number | null) => {
    setAddingKeywordToGroupId(groupId);
    setNewKeyword("");
    setIsAddKeywordDialogOpen(true);
  };

  // Handle creating a new group
  const handleCreateGroup = () => {
    const keywords = newGroupKeywords.split(",").map(k => k.trim()).filter(k => k);
    const excludeWords = newGroupExcludeWords ? newGroupExcludeWords.split(",").map(w => w.trim()).filter(w => w) : [];

    createGroup.mutate({
      name: newGroupName,
      keywords,
      excludeWords
    }, {
      onSuccess: () => {
        setIsCreateDialogOpen(false);
        setNewGroupName("");
        setNewGroupKeywords("");
        setNewGroupExcludeWords("");
      }
    });
  };

  // Handle updating a group
  const handleUpdateGroup = () => {
    if (!selectedGroup) return;

    const keywords = editGroupKeywords.split(",").map(k => k.trim()).filter(k => k);
    const excludeWords = editGroupExcludeWords ? editGroupExcludeWords.split(",").map(w => w.trim()).filter(w => w) : [];

    if (selectedGroup.id === 0) {
      // Update global settings
      updateSettings({
        searchKeywords: keywords,
        excludeWords
      });
      setIsEditDialogOpen(false);
    } else {
      // Update a keyword group
      updateGroup.mutate({
        id: selectedGroup.id,
        data: {
          name: editGroupName,
          keywords,
          excludeWords
        }
      }, {
        onSuccess: () => {
          setIsEditDialogOpen(false);
        }
      });
    }
  };

  // Handle deleting a group
  const handleDeleteGroup = () => {
    if (!selectedGroup) return;

    deleteGroup.mutate(selectedGroup.id, {
      onSuccess: () => {
        setIsDeleteDialogOpen(false);
      }
    });
  };

  // Handle setting active group
  const handleSetActiveGroup = (groupId: number | null) => {
    setActiveGroup.mutate(groupId);
  };

  // Handle refreshing a group
  const handleRefreshGroup = (groupId: number) => {
    refreshGroupVideos.mutate(groupId);
  };

  // Handle refreshing global feed
  const handleRefreshGlobal = () => {
    refreshGlobalVideos.mutate();
  };

  // Handle exporting groups
  const handleExportGroups = () => {
    exportGroups();
  };

  // Handle importing groups
  const handleImportGroups = () => {
    try {
      const data = JSON.parse(importData);
      importGroups.mutate(data, {
        onSuccess: () => {
          setIsImportDialogOpen(false);
          setImportData("");
        }
      });
    } catch (error) {
      toast({
        title: "Invalid JSON",
        description: "Please provide valid JSON data.",
        variant: "destructive",
      });
    }
  };

  // Handle file upload for import
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const content = event.target?.result as string;
        setImportData(content);
      } catch (error) {
        toast({
          title: "Error reading file",
          description: "Could not read the uploaded file.",
          variant: "destructive",
        });
      }
    };
    reader.readAsText(file);
  };

  // Handle removing a keyword from a group
  const handleRemoveGroupKeyword = (groupId: number, keyword: string) => {
    const group = groups?.find(g => g.id === groupId);
    if (!group) return;

    const updatedKeywords = group.keywords.filter(k => k !== keyword);
    updateGroup.mutate({
      id: groupId,
      data: {
        name: group.name,
        keywords: updatedKeywords,
        excludeWords: group.excludeWords
      }
    });
  };

  // Handle removing a keyword from global settings
  const handleRemoveGlobalKeyword = (keyword: string) => {
    if (!settings) return;
    
    const updatedKeywords = settings.searchKeywords.filter(k => k !== keyword);
    updateSettings({
      searchKeywords: updatedKeywords
    });
  };

  // Handle adding a keyword
  const handleAddKeyword = () => {
    if (!newKeyword.trim()) return;

    // Split by commas and trim each keyword
    const keywordsToAdd = newKeyword.split(",").map(k => k.trim()).filter(k => k);
    
    if (addingKeywordToGroupId === 0) {
      // Adding to global feed
      if (!settings) return;
      
      // Filter out keywords that already exist
      const existingKeywords = settings.searchKeywords;
      const newKeywords = keywordsToAdd.filter(keyword => !existingKeywords.includes(keyword));
      
      if (newKeywords.length > 0) {
        updateSettings({
          searchKeywords: [...existingKeywords, ...newKeywords]
        });
        
        toast({
          title: "Keywords added",
          description: newKeywords.length === 1
            ? `Added "${newKeywords[0]}" to global feed.`
            : `Added ${newKeywords.length} keywords to global feed.`
        });
      }
      
      setIsAddKeywordDialogOpen(false);
    } else {
      // Adding to a custom group
      const group = groups?.find(g => g.id === addingKeywordToGroupId);
      if (!group) return;

      // Filter out keywords that already exist
      const existingKeywords = group.keywords;
      const newKeywords = keywordsToAdd.filter(keyword => !existingKeywords.includes(keyword));
      const duplicateKeywords = keywordsToAdd.filter(keyword => existingKeywords.includes(keyword));

      // Show a toast if there are duplicate keywords
      if (duplicateKeywords.length > 0) {
        toast({
          title: duplicateKeywords.length === 1 ? "Keyword already exists" : "Some keywords already exist",
          description: duplicateKeywords.length === 1
            ? `The keyword "${duplicateKeywords[0]}" already exists in this group.`
            : `The following keywords already exist: ${duplicateKeywords.join(", ")}`,
          variant: "destructive",
        });

        // If all keywords are duplicates, close the dialog and return
        if (newKeywords.length === 0) {
          setIsAddKeywordDialogOpen(false);
          return;
        }
      }

      // Add the new keywords to the group
      const updatedKeywords = [...existingKeywords, ...newKeywords];

      updateGroup.mutate({
        id: addingKeywordToGroupId,
        data: {
          name: group.name,
          keywords: updatedKeywords,
          excludeWords: group.excludeWords
        }
      }, {
        onSuccess: () => {
          // Show success toast with the number of keywords added
          toast({
            title: "Keywords added",
            description: newKeywords.length === 1
              ? `Added 1 keyword to ${group.name}.`
              : `Added ${newKeywords.length} keywords to ${group.name}.`
          });

          setIsAddKeywordDialogOpen(false);
          setNewKeyword("");
        }
      });
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Keyword Groups</h1>
        <div className="flex gap-2">
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Group
          </Button>
          <Button variant="outline" onClick={handleExportGroups}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={() => setIsImportDialogOpen(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowAllKeywords(!showAllKeywords)}
                >
                  {showAllKeywords ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{showAllKeywords ? "Collapse all groups" : "Expand all groups"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Global Feed Card - Draggable */}
        <DraggableGlobalFeed
          expandedGroups={expandedGroups}
          toggleGroupExpansion={toggleGroupExpansion}
          openEditDialog={openEditGlobalDialog}
          handleRefreshGroup={handleRefreshGlobal}
          handleSetActiveGroup={handleSetActiveGroup}
          openAddKeywordDialog={openAddKeywordDialog}
          handleRemoveGlobalKeyword={handleRemoveGlobalKeyword}
          refreshGlobalVideos={refreshGlobalVideos}
          isDragging={draggedItemId === 'global'}
          dragHandleProps={getDragHandleProps('global', 0)}
        />

        {/* Keyword Group Cards - Draggable */}
        {orderedItems?.map((group, index) => (
          <DraggableKeywordGroup
            key={group.id}
            group={group}
            expandedGroups={expandedGroups}
            toggleGroupExpansion={toggleGroupExpansion}
            openEditDialog={openEditDialog}
            openDeleteDialog={openDeleteDialog}
            handleRefreshGroup={handleRefreshGroup}
            handleSetActiveGroup={handleSetActiveGroup}
            openAddKeywordDialog={openAddKeywordDialog}
            handleRemoveGroupKeyword={handleRemoveGroupKeyword}
            refreshGroupVideos={refreshGroupVideos}
            setActiveGroup={setActiveGroup}
            isDragging={draggedItemId === group.id}
            dragHandleProps={getDragHandleProps(group.id, index + 1)} // +1 because global feed is at index 0
          />
        ))}
      </div>

      {/* Create Group Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Keyword Group</DialogTitle>
            <DialogDescription>
              Create a new group to organize your keywords.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="name">Group Name</label>
              <Input
                id="name"
                value={newGroupName}
                onChange={(e) => setNewGroupName(e.target.value)}
                placeholder="Tech News"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="keywords">Keywords (comma-separated)</label>
              <Input
                id="keywords"
                value={newGroupKeywords}
                onChange={(e) => setNewGroupKeywords(e.target.value)}
                placeholder="tech, apple, google"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="exclude-words">Exclude Words (comma-separated)</label>
              <Input
                id="exclude-words"
                value={newGroupExcludeWords}
                onChange={(e) => setNewGroupExcludeWords(e.target.value)}
                placeholder="spam, clickbait"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateGroup} disabled={createGroup.isPending || !newGroupName || !newGroupKeywords}>
              {createGroup.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Create Group
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Group Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit {selectedGroup?.id === 0 ? "Global Feed" : "Keyword Group"}</DialogTitle>
            <DialogDescription>
              Update the {selectedGroup?.id === 0 ? "global feed" : "group"} settings.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {selectedGroup?.id !== 0 && (
              <div className="grid gap-2">
                <label htmlFor="edit-name">Group Name</label>
                <Input
                  id="edit-name"
                  value={editGroupName}
                  onChange={(e) => setEditGroupName(e.target.value)}
                />
              </div>
            )}

            <div className="grid gap-2">
              <label htmlFor="edit-keywords">Keywords (comma-separated)</label>
              <Textarea
                id="edit-keywords"
                value={editGroupKeywords}
                onChange={(e) => setEditGroupKeywords(e.target.value)}
                rows={4}
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="edit-exclude-words">Exclude Words (comma-separated)</label>
              <Textarea
                id="edit-exclude-words"
                value={editGroupExcludeWords}
                onChange={(e) => setEditGroupExcludeWords(e.target.value)}
                rows={4}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleUpdateGroup} 
              disabled={updateGroup.isPending || (selectedGroup?.id !== 0 && !editGroupName)}
            >
              {updateGroup.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Group Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Keyword Group</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedGroup?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteGroup}
              disabled={deleteGroup.isPending}
            >
              {deleteGroup.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Group
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Keyword Groups</DialogTitle>
            <DialogDescription>
              Import keyword groups from a JSON file or paste JSON data.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="import-file">Upload JSON File</Label>
              <Input
                id="import-file"
                type="file"
                accept=".json"
                ref={fileInputRef}
                onChange={handleFileUpload}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="import-data">Or Paste JSON Data</Label>
              <Textarea
                id="import-data"
                value={importData}
                onChange={(e) => setImportData(e.target.value)}
                rows={10}
                placeholder='[{"name": "Tech", "keywords": ["apple", "google"], "excludeWords": ["spam"]}]'
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleImportGroups}
              disabled={importGroups.isPending || !importData}
            >
              {importGroups.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Import Groups
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Keyword Dialog */}
      <Dialog open={isAddKeywordDialogOpen} onOpenChange={setIsAddKeywordDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Keywords</DialogTitle>
            <DialogDescription>
              Add keywords to {addingKeywordToGroupId === 0 ? "global feed" : `group "${groups?.find(g => g.id === addingKeywordToGroupId)?.name || ''}"`.trim()}.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="new-keyword">Keywords (comma-separated)</Label>
              <Input
                id="new-keyword"
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                placeholder="Enter keywords separated by commas"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddKeywordDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleAddKeyword}
              disabled={!newKeyword.trim()}
            >
              Add Keywords
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
