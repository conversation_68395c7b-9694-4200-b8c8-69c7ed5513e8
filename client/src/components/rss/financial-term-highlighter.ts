/**
 * Utility for highlighting financial terms in article content
 */

// Types of financial terms
export enum FinancialTermType {
  POSITIVE = 'positive', // Benefits, increases, etc.
  NEGATIVE = 'negative', // Cuts, decreases, etc.
  NEUTRAL = 'neutral'    // Other financial terms
}

// Interface for highlight counts
export interface HighlightCounts {
  positive: number;
  negative: number;
  neutral: number;
}

// Default positive financial terms (benefits, increases, etc.)
const defaultPositiveTerms = [
  'benefit', 'benefits', 'increase', 'increases', 'increased', 'increasing',
  'gain', 'gains', 'gained', 'gaining', 'extra', 'bonus', 'bonuses',
  'more', 'additional', 'raise', 'raises', 'raised', 'raising',
  'boost', 'boosts', 'boosted', 'boosting', 'higher', 'growth',
  'supplement', 'supplements', 'supplemental', 'supplementary',
  'payment', 'payments', 'payout', 'payouts', 'dividend', 'dividends',
  'profit', 'profits', 'profitable', 'profitability', 'earn', 'earns', 'earned', 'earning',
  'income', 'revenue', 'revenues', 'grant', 'grants', 'awarded', 'award',
  'stimulus', 'relief', 'aid', 'assistance', 'support', 'subsidy', 'subsidies',
  'credit', 'credits', 'rebate', 'rebates', 'refund', 'refunds',
  'allowance', 'allowances', 'entitlement', 'entitlements',
  'compensation', 'compensations', 'reimbursement', 'reimbursements',
  'premium', 'premiums', 'incentive', 'incentives', 'reward', 'rewards',
  'stipend', 'stipends', 'scholarship', 'scholarships', 'fellowship', 'fellowships',
  'promotion', 'promotions', 'upgrade', 'upgrades', 'enhanced', 'enhancement',
  'improved', 'improvement', 'improvements', 'augmented', 'augmentation',
  'expanded', 'expansion', 'extended', 'extension', 'amplified', 'amplification',
  'strengthened', 'strengthening', 'fortified', 'fortification',
  'enriched', 'enrichment', 'elevated', 'elevation', 'heightened', 'heightening',
  'maximized', 'maximization', 'optimized', 'optimization'
];

// Default negative financial terms (cuts, decreases, etc.)
const defaultNegativeTerms = [
  'cut', 'cuts', 'cutting', 'decrease', 'decreases', 'decreased', 'decreasing',
  'reduction', 'reductions', 'reduced', 'reducing', 'less', 'lesser', 'lower',
  'loss', 'losses', 'lost', 'losing', 'decline', 'declines', 'declined', 'declining',
  'drop', 'drops', 'dropped', 'dropping', 'fall', 'falls', 'falling', 'fell',
  'shrink', 'shrinks', 'shrank', 'shrinking', 'diminish', 'diminishes', 'diminished', 'diminishing',
  'decrease', 'decreases', 'decreased', 'decreasing', 'deduction', 'deductions',
  'penalty', 'penalties', 'fine', 'fines', 'fee', 'fees', 'charge', 'charges',
  'expense', 'expenses', 'costly', 'cost', 'costs', 'debt', 'debts', 'deficit', 'deficits',
  'liability', 'liabilities', 'obligation', 'obligations', 'burden', 'burdens',
  'payment', 'payments', 'due', 'dues', 'bill', 'bills', 'invoice', 'invoices',
  'withdrawal', 'withdrawals', 'debit', 'debits', 'deducted', 'deducting',
  'removed', 'removal', 'eliminated', 'elimination', 'suspended', 'suspension',
  'terminated', 'termination', 'cancelled', 'cancellation', 'revoked', 'revocation',
  'rescinded', 'rescission', 'retracted', 'retraction', 'withdrawn', 'withdrawal',
  'discontinued', 'discontinuation', 'ended', 'ending', 'stopped', 'stopping',
  'halted', 'halting', 'ceased', 'ceasing', 'paused', 'pausing',
  'frozen', 'freezing', 'capped', 'capping', 'limited', 'limiting',
  'restricted', 'restricting', 'constrained', 'constraining',
  'curtailed', 'curtailing', 'slashed', 'slashing', 'trimmed', 'trimming'
];

// Default neutral financial terms - only truly neutral terms, no positive/negative connotations
const defaultNeutralTerms = [
  'social security', 'irs', 'tax', 'taxes', 'finance', 'financial', 'economy', 'economic',
  'budget', 'budgetary', 'fiscal', 'monetary',
  'market', 'markets', 'stock', 'stocks', 'bond', 'bonds', 'fund', 'funds', 'etf', 'etfs',
  'retirement', 'pension', 'pensions', 'annuity', 'annuities', 'portfolio',
  'asset', 'assets', 'equity', 'equities', 'security', 'securities',
  'interest', 'rate', 'rates',
  'banking', 'bank', 'banks', 'lender', 'lenders', 'loan', 'loans', 'mortgage', 'mortgages',
  'credit', 'debit', 'deposit', 'deposits', 'transaction', 'transactions',
  'account', 'accounts', 'statement', 'statements', 'ledger', 'ledgers',
  'capital', 'capitalization', 'liquidity',
  'insurance', 'insurer', 'insurers', 'policy', 'policies',
  'medicare', 'medicaid', 'healthcare', 'health care',
  'treasury', 'federal reserve', 'fed', 'central bank', 'sec', 'securities and exchange commission',
  'fdic', 'federal deposit insurance corporation', 'cfpb', 'consumer financial protection bureau',
  'finra', 'financial industry regulatory authority', 'cftc', 'commodity futures trading commission',
  'nasdaq', 'nyse', 'dow jones', 's&p 500', 'russell 2000', 'wilshire 5000',
  'investment', 'investing', 'investor', 'investors', 'finance', 'financial', 'dollar', 'dollars',
  'currency', 'currencies', 'money', 'cash', 'checking', 'savings', 'brokerage', 'trading',
  'roth ira', 'traditional ira', '401k', '403b', 'sep ira', 'simple ira', 'hsa',
  'escrow', 'amortization', 'principal', 'collateral', 'underwriting', 'refinance',
  'compound interest', 'apr', 'apy', 'basis point', 'basis points', 'fomc', 'federal funds rate'
];

// Mutable arrays for custom terms
let positiveTerms = [...defaultPositiveTerms];
let negativeTerms = [...defaultNegativeTerms];
let neutralTerms = [...defaultNeutralTerms];

// Local storage keys for persisting custom terms
const POSITIVE_TERMS_STORAGE_KEY = 'rss-positive-financial-terms';
const NEGATIVE_TERMS_STORAGE_KEY = 'rss-negative-financial-terms';
const NEUTRAL_TERMS_STORAGE_KEY = 'rss-neutral-financial-terms';

// Load terms from local storage on module initialization
try {
  const storedPositiveTerms = localStorage.getItem(POSITIVE_TERMS_STORAGE_KEY);
  const storedNegativeTerms = localStorage.getItem(NEGATIVE_TERMS_STORAGE_KEY);
  const storedNeutralTerms = localStorage.getItem(NEUTRAL_TERMS_STORAGE_KEY);

  if (storedPositiveTerms) {
    positiveTerms = JSON.parse(storedPositiveTerms);
  }

  if (storedNegativeTerms) {
    negativeTerms = JSON.parse(storedNegativeTerms);
  }

  if (storedNeutralTerms) {
    neutralTerms = JSON.parse(storedNeutralTerms);
  }
} catch (error) {
  console.error('Error loading financial terms from local storage:', error);
  // Fall back to default terms
  positiveTerms = [...defaultPositiveTerms];
  negativeTerms = [...defaultNegativeTerms];
  neutralTerms = [...defaultNeutralTerms];
}

/**
 * Get the current positive financial terms
 * @returns Array of positive financial terms
 */
export function getPositiveTerms(): string[] {
  return [...positiveTerms]; // Return a copy to prevent direct modification
}

/**
 * Get the current negative financial terms
 * @returns Array of negative financial terms
 */
export function getNegativeTerms(): string[] {
  return [...negativeTerms]; // Return a copy to prevent direct modification
}

/**
 * Set custom positive financial terms
 * @param terms Array of positive financial terms
 */
export function setPositiveTerms(terms: string[]): void {
  positiveTerms = [...terms]; // Create a new array to ensure immutability

  // Save to local storage
  try {
    localStorage.setItem(POSITIVE_TERMS_STORAGE_KEY, JSON.stringify(positiveTerms));
  } catch (error) {
    console.error('Error saving positive financial terms to local storage:', error);
  }
}

/**
 * Set custom negative financial terms
 * @param terms Array of negative financial terms
 */
export function setNegativeTerms(terms: string[]): void {
  negativeTerms = [...terms]; // Create a new array to ensure immutability

  // Save to local storage
  try {
    localStorage.setItem(NEGATIVE_TERMS_STORAGE_KEY, JSON.stringify(negativeTerms));
  } catch (error) {
    console.error('Error saving negative financial terms to local storage:', error);
  }
}

/**
 * Get the current neutral financial terms
 * @returns Array of neutral financial terms
 */
export function getNeutralTerms(): string[] {
  return [...neutralTerms]; // Return a copy to prevent direct modification
}

/**
 * Set custom neutral financial terms
 * @param terms Array of neutral financial terms
 */
export function setNeutralTerms(terms: string[]): void {
  neutralTerms = [...terms]; // Create a new array to ensure immutability

  // Save to local storage
  try {
    localStorage.setItem(NEUTRAL_TERMS_STORAGE_KEY, JSON.stringify(neutralTerms));
  } catch (error) {
    console.error('Error saving neutral financial terms to local storage:', error);
  }
}

/**
 * Reset financial terms to defaults
 */
export function resetToDefaultTerms(): void {
  positiveTerms = [...defaultPositiveTerms];
  negativeTerms = [...defaultNegativeTerms];
  neutralTerms = [...defaultNeutralTerms];

  // Clear from local storage
  try {
    localStorage.removeItem(POSITIVE_TERMS_STORAGE_KEY);
    localStorage.removeItem(NEGATIVE_TERMS_STORAGE_KEY);
    localStorage.removeItem(NEUTRAL_TERMS_STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing financial terms from local storage:', error);
  }
}

/**
 * Highlights financial terms in article content
 * @param content The article content to highlight
 * @returns The highlighted content and counts of each type of highlight
 */
export function highlightFinancialTerms(content: string): {
  highlightedContent: string;
  counts: HighlightCounts;
} {
  if (!content) return { highlightedContent: '', counts: { positive: 0, negative: 0, neutral: 0 } };

  let highlightedContent = content;
  const counts: HighlightCounts = { positive: 0, negative: 0, neutral: 0 };

  // Create a regex pattern for positive terms - ensure we match whole words only
  const positivePattern = new RegExp(`\\b(${positiveTerms.join('|')})\\b`, 'gi');

  // Create a regex pattern for negative terms - ensure we match whole words only
  const negativePattern = new RegExp(`\\b(${negativeTerms.join('|')})\\b`, 'gi');

  // Create a regex pattern for neutral terms - ensure we match whole words only
  // For multi-word terms, we need a different approach
  const singleWordNeutralTerms = neutralTerms.filter(term => !term.includes(' '));
  const multiWordNeutralTerms = neutralTerms.filter(term => term.includes(' '));

  // Create a regex for single-word neutral terms
  const neutralPattern = singleWordNeutralTerms.length > 0
    ? new RegExp(`\\b(${singleWordNeutralTerms.join('|')})\\b`, 'gi')
    : null;

  // Track all matches to avoid double-highlighting
  const matches: { term: string; index: number; length: number; type: FinancialTermType; processed?: boolean }[] = [];

  // Find all positive term matches
  let positiveMatch;
  while ((positiveMatch = positivePattern.exec(content)) !== null) {
    matches.push({
      term: positiveMatch[0],
      index: positiveMatch.index,
      length: positiveMatch[0].length,
      type: FinancialTermType.POSITIVE
    });
    counts.positive++;
  }

  // Find all negative term matches
  let negativeMatch;
  while ((negativeMatch = negativePattern.exec(content)) !== null) {
    matches.push({
      term: negativeMatch[0],
      index: negativeMatch.index,
      length: negativeMatch[0].length,
      type: FinancialTermType.NEGATIVE
    });
    counts.negative++;
  }

  // Find all single-word neutral term matches
  if (neutralPattern) {
    let neutralMatch;
    while ((neutralMatch = neutralPattern.exec(content)) !== null) {
      matches.push({
        term: neutralMatch[0],
        index: neutralMatch.index,
        length: neutralMatch[0].length,
        type: FinancialTermType.NEUTRAL
      });
      counts.neutral++;
    }
  }

  // Find all multi-word neutral term matches
  // We need to do this separately since regex with word boundaries doesn't work well with phrases
  if (multiWordNeutralTerms.length > 0) {
    const lowerContent = content.toLowerCase();
    for (const term of multiWordNeutralTerms) {
      let startIndex = 0;
      let foundIndex;

      // Find all occurrences of this multi-word term
      while ((foundIndex = lowerContent.indexOf(term.toLowerCase(), startIndex)) !== -1) {
        // Check if it's a whole phrase (surrounded by word boundaries)
        const beforeChar = foundIndex > 0 ? lowerContent[foundIndex - 1] : ' ';
        const afterChar = foundIndex + term.length < lowerContent.length ? lowerContent[foundIndex + term.length] : ' ';

        const isWordBoundaryBefore = /\W/.test(beforeChar);
        const isWordBoundaryAfter = /\W/.test(afterChar);

        if (isWordBoundaryBefore && isWordBoundaryAfter) {
          matches.push({
            term: content.substring(foundIndex, foundIndex + term.length),
            index: foundIndex,
            length: term.length,
            type: FinancialTermType.NEUTRAL
          });
          counts.neutral++;
        }

        startIndex = foundIndex + 1; // Move past this occurrence
      }
    }
  }

  // Sort matches by index in reverse order to avoid offset issues when replacing
  matches.sort((a, b) => b.index - a.index);

  // Apply highlights - use a more careful approach to avoid text corruption
  // First, convert the content to a DOM structure to safely manipulate HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = highlightedContent;

  // Process text nodes only to avoid corrupting HTML
  function processTextNodes(node: Node) {
    if (node.nodeType === Node.TEXT_NODE && node.textContent) {
      // Process each match in this text node
      for (const match of matches) {
        // Skip if this match has already been processed
        if (match.processed) continue;

        const text = node.textContent;
        const matchIndex = text.toLowerCase().indexOf(match.term.toLowerCase());

        // If the match is found in this text node
        if (matchIndex !== -1) {
          match.processed = true; // Mark as processed

          // Split the text node into three parts: before, match, and after
          const before = text.substring(0, matchIndex);
          const matchText = text.substring(matchIndex, matchIndex + match.term.length);
          const after = text.substring(matchIndex + match.term.length);

          // Create the new nodes
          const beforeNode = document.createTextNode(before);
          const afterNode = document.createTextNode(after);

          // Create the span for the match
          const spanNode = document.createElement('span');
          if (match.type === FinancialTermType.POSITIVE) {
            spanNode.className = 'financial-term-positive';
          } else if (match.type === FinancialTermType.NEGATIVE) {
            spanNode.className = 'financial-term-negative';
          } else {
            spanNode.className = 'financial-term-neutral';
          }
          spanNode.textContent = matchText;

          // Replace the current node with the three new nodes
          const parent = node.parentNode;
          if (parent) {
            parent.insertBefore(beforeNode, node);
            parent.insertBefore(spanNode, node);
            parent.insertBefore(afterNode, node);
            parent.removeChild(node);

            // Continue processing with the "after" node
            processTextNodes(afterNode);
          }

          // Stop processing this node as it's been replaced
          return;
        }
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      // Process child nodes
      Array.from(node.childNodes).forEach(processTextNodes);
    }
  }

  // Mark all matches as not processed yet
  matches.forEach(match => match.processed = false);

  // Process the DOM
  try {
    Array.from(tempDiv.childNodes).forEach(processTextNodes);

    // Get the processed HTML
    highlightedContent = tempDiv.innerHTML;
  } catch (error) {
    console.error('Error processing financial terms:', error);
    // Fall back to the original content if there's an error
  }

  return { highlightedContent, counts };
}

/**
 * Determines the dominant financial sentiment of an article based on highlight counts
 * @param counts The counts of positive and negative highlights
 * @returns The dominant sentiment type or null if balanced/no highlights
 */
export function getDominantSentiment(counts: HighlightCounts): FinancialTermType | null {
  const { positive, negative, neutral } = counts;

  // No highlights
  if (positive === 0 && negative === 0 && neutral === 0) return null;

  // If only neutral terms are present
  if (positive === 0 && negative === 0 && neutral > 0) return FinancialTermType.NEUTRAL;

  // Determine if there's a significant difference
  // Using a threshold of at least 3 more of one type and at least 50% more
  const difference = positive - negative;
  const ratio = positive > 0 && negative > 0
    ? Math.max(positive, negative) / Math.min(positive, negative)
    : 0;

  if (difference >= 3 && ratio >= 1.5) {
    return FinancialTermType.POSITIVE;
  } else if (difference <= -3 && ratio >= 1.5) {
    return FinancialTermType.NEGATIVE;
  }

  // If neutral terms dominate (more than positive and negative combined)
  if (neutral > (positive + negative) && neutral > 0) return FinancialTermType.NEUTRAL;

  return null; // Balanced sentiment
}
