import { SQLiteStorage } from './sqliteStorage';
import { OpenRouterApiKey } from './storage';
import axios from 'axios';
import { IStorage } from './storage';

export function implementOpenRouterApiKeyMethods(storage: SQLiteStorage) {
  // Create the openrouter_api_keys table if it doesn't exist
  storage.db.exec(`
    CREATE TABLE IF NOT EXISTS openrouter_api_keys (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      token TEXT NOT NULL,
      name TEXT,
      is_active BOOLEAN DEFAULT 1,
      last_used DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      user_id INTEGER NOT NULL,
      is_default BOOLEAN DEFAULT 0,
      exhausted_until DATETIME,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );
  `);

  // Add exhausted_until column if it doesn't exist
  try {
    const tableInfo = storage.db.prepare("PRAGMA table_info(openrouter_api_keys)").all();
    const hasExhaustedUntil = tableInfo.some((column: any) => column.name === 'exhausted_until');

    if (!hasExhaustedUntil) {
      console.log('Adding exhausted_until column to openrouter_api_keys table');
      storage.db.exec(`ALTER TABLE openrouter_api_keys ADD COLUMN exhausted_until DATETIME;`);
    }
  } catch (error) {
    console.error('Error checking or adding exhausted_until column:', error);
  }

  // Get all OpenRouter API keys for a user
  storage.getOpenRouterApiKeys = async (userId: number): Promise<OpenRouterApiKey[]> => {
    try {
      // Check if rate_limit_type column exists
      const tableInfo = storage.db.prepare("PRAGMA table_info(openrouter_api_keys)").all();
      const hasRateLimitType = tableInfo.some((column: any) => column.name === 'rate_limit_type');

      // Construct the query based on whether the column exists
      const query = `
        SELECT
          id,
          substr(token, 1, 4) || '...' || substr(token, -4) as masked_token,
          name,
          is_active,
          last_used,
          created_at,
          user_id,
          is_default,
          exhausted_until${hasRateLimitType ? ',\n          rate_limit_type' : ''}
        FROM openrouter_api_keys
        WHERE user_id = ?
        ORDER BY is_default DESC, created_at DESC
      `;

      const stmt = storage.db.prepare(query);
      const keys = stmt.all(userId);

      // We already checked if rate_limit_type column exists above

      return keys.map((key: any) => ({
        id: key.id,
        token: '', // Don't return the actual token for security
        masked_token: key.masked_token,
        name: key.name,
        is_active: Boolean(key.is_active),
        last_used: key.last_used ? new Date(key.last_used) : undefined,
        created_at: new Date(key.created_at),
        user_id: key.user_id,
        is_default: Boolean(key.is_default),
        exhausted_until: key.exhausted_until ? new Date(key.exhausted_until) : undefined,
        ...(hasRateLimitType && { rate_limit_type: key.rate_limit_type || undefined })
      }));
    } catch (error) {
      console.error('Error getting OpenRouter API keys:', error);
      throw error;
    }
  };

  // Get a specific OpenRouter API key by ID
  storage.getOpenRouterApiKey = async (id: number): Promise<OpenRouterApiKey | undefined> => {
    try {
      const stmt = storage.db.prepare(`
        SELECT
          id,
          token,
          substr(token, 1, 4) || '...' || substr(token, -4) as masked_token,
          name,
          is_active,
          last_used,
          created_at,
          user_id,
          is_default
        FROM openrouter_api_keys
        WHERE id = ?
      `);

      const key = stmt.get(id);

      if (!key) return undefined;

      return {
        id: key.id,
        token: key.token,
        masked_token: key.masked_token,
        name: key.name,
        is_active: Boolean(key.is_active),
        last_used: key.last_used ? new Date(key.last_used) : undefined,
        created_at: new Date(key.created_at),
        user_id: key.user_id,
        is_default: Boolean(key.is_default)
      };
    } catch (error) {
      console.error('Error getting OpenRouter API key:', error);
      throw error;
    }
  };

  // Add a new OpenRouter API key
  storage.addOpenRouterApiKey = async (
    userId: number,
    token: string,
    name?: string,
    isActive: boolean = true
  ): Promise<OpenRouterApiKey> => {
    try {
      // Check if this is the first key for this user
      const countStmt = storage.db.prepare('SELECT COUNT(*) as count FROM openrouter_api_keys WHERE user_id = ?');
      const { count } = countStmt.get(userId);
      const isDefault = count === 0;

      const stmt = storage.db.prepare(`
        INSERT INTO openrouter_api_keys (token, name, is_active, user_id, is_default)
        VALUES (?, ?, ?, ?, ?)
      `);

      const result = stmt.run(token, name || 'OpenRouter API Key', isActive ? 1 : 0, userId, isDefault ? 1 : 0);
      const id = Number(result.lastInsertRowid);

      return {
        id,
        token: '',
        masked_token: token.substring(0, 4) + '...' + token.substring(token.length - 4),
        name: name || 'OpenRouter API Key',
        is_active: isActive,
        created_at: new Date(),
        user_id: userId,
        is_default: isDefault
      };
    } catch (error) {
      console.error('Error adding OpenRouter API key:', error);
      throw error;
    }
  };

  // Update an OpenRouter API key
  storage.updateOpenRouterApiKey = async (id: number, data: Partial<OpenRouterApiKey>): Promise<OpenRouterApiKey> => {
    try {
      const key = await storage.getOpenRouterApiKey(id);
      if (!key) {
        throw new Error('OpenRouter API key not found');
      }

      const updates: string[] = [];
      const params: any[] = [];

      if (data.token !== undefined) {
        updates.push('token = ?');
        params.push(data.token);
      }

      if (data.name !== undefined) {
        updates.push('name = ?');
        params.push(data.name);
      }

      if (data.is_active !== undefined) {
        updates.push('is_active = ?');
        params.push(data.is_active ? 1 : 0);
      }

      if (data.is_default !== undefined && data.is_default) {
        // If setting this key as default, unset all other keys for this user
        const unsetDefaultStmt = storage.db.prepare(`
          UPDATE openrouter_api_keys
          SET is_default = 0
          WHERE user_id = ? AND id != ?
        `);
        unsetDefaultStmt.run(key.user_id, id);

        updates.push('is_default = ?');
        params.push(1);
      }

      if (updates.length === 0) {
        return key;
      }

      const stmt = storage.db.prepare(`
        UPDATE openrouter_api_keys
        SET ${updates.join(', ')}
        WHERE id = ?
      `);

      params.push(id);
      stmt.run(...params);

      return await storage.getOpenRouterApiKey(id) as OpenRouterApiKey;
    } catch (error) {
      console.error('Error updating OpenRouter API key:', error);
      throw error;
    }
  };

  // Delete an OpenRouter API key
  storage.deleteOpenRouterApiKey = async (id: number): Promise<void> => {
    try {
      const key = await storage.getOpenRouterApiKey(id);
      if (!key) {
        throw new Error('OpenRouter API key not found');
      }

      const stmt = storage.db.prepare('DELETE FROM openrouter_api_keys WHERE id = ?');
      stmt.run(id);

      // If this was the default key, set another key as default if available
      if (key.is_default) {
        const nextKeyStmt = storage.db.prepare(`
          SELECT id FROM openrouter_api_keys
          WHERE user_id = ?
          ORDER BY created_at DESC
          LIMIT 1
        `);

        const nextKey = nextKeyStmt.get(key.user_id);

        if (nextKey) {
          const setDefaultStmt = storage.db.prepare(`
            UPDATE openrouter_api_keys
            SET is_default = 1
            WHERE id = ?
          `);
          setDefaultStmt.run(nextKey.id);
        }
      }
    } catch (error) {
      console.error('Error deleting OpenRouter API key:', error);
      throw error;
    }
  };

  // Test an OpenRouter API key
  storage.testOpenRouterApiKey = async (id: number): Promise<{ success: boolean; message: string; rateLimitInfo?: { detected: boolean; type?: string; resetTime?: Date } }> => {
    try {
      const key = await storage.getOpenRouterApiKey(id);
      if (!key) {
        throw new Error('OpenRouter API key not found');
      }

      // Define the base URLs to try
      const baseUrls = [
        'https://openrouter.ai',
        'https://api.openrouter.ai'
      ];

      // Define the endpoints to try
      const endpoints = [
        '/api/v1/models',
        '/api/v1/chat/completions'
      ];

      // Try each combination until one works
      let lastError = null;
      let workingUrl = '';
      let workingEndpoint = '';

      // First, test basic connectivity
      for (const baseUrl of baseUrls) {
        for (const endpoint of endpoints) {
          const url = `${baseUrl}${endpoint}`;
          try {
            console.log(`Testing OpenRouter API key with URL: ${url}`);

            // Prepare the request based on the endpoint
            const requestData = endpoint.includes('chat/completions') ? {
              model: 'openai/gpt-3.5-turbo',  // Use a reliable model for testing
              messages: [{ role: 'user', content: 'Hello' }],
              max_tokens: 1
            } : {};

            // Make the request with the required headers
            const response = await axios.post(
              url,
              requestData,
              {
                headers: {
                  'Authorization': `Bearer ${key.token}`,
                  'Content-Type': 'application/json',
                  'HTTP-Referer': 'https://trendy.local',  // Required by OpenRouter
                  'X-Title': 'Trendy App'  // Required by OpenRouter
                }
              }
            );

            // If we get here, the request was successful
            console.log(`OpenRouter API key test successful with URL: ${url}`);
            console.log(`Response status: ${response.status}`);
            console.log(`Response data:`, JSON.stringify(response.data).substring(0, 200) + '...');

            // Update last_used timestamp
            const updateStmt = storage.db.prepare(`
              UPDATE openrouter_api_keys
              SET last_used = CURRENT_TIMESTAMP
              WHERE id = ?
            `);
            updateStmt.run(id);

            // Save the working URL for rate limit testing
            workingUrl = baseUrl;
            workingEndpoint = endpoint;

            // Break out of the loops since we found a working URL
            break;
          } catch (urlError) {
            console.log(`Error testing OpenRouter API key with URL ${url}:`, urlError.message);
            if (urlError.response) {
              console.log(`Response status: ${urlError.response.status}`);
              console.log(`Response data:`, JSON.stringify(urlError.response.data || {}).substring(0, 200) + '...');

              // Check for rate limit errors
              if (urlError.response.status === 429) {
                console.log('Rate limit detected during basic connectivity test');

                // Extract rate limit information
                let rateLimitType = 'unknown';
                let resetTime = new Date();
                resetTime.setMinutes(resetTime.getMinutes() + 60); // Default 60 minutes

                try {
                  const errorData = urlError.response.data;
                  if (errorData && errorData.error && errorData.error.message) {
                    const errorMsg = errorData.error.message;

                    // Check for daily free model limit
                    if (errorMsg.includes('free-models-per-day')) {
                      rateLimitType = 'daily-free';
                      resetTime = new Date();
                      resetTime.setHours(24, 0, 0, 0); // Next day at midnight
                    }
                    // Check for per-minute limit
                    else if (errorMsg.includes('per-minute')) {
                      rateLimitType = 'per-minute';
                      resetTime = new Date();
                      resetTime.setMinutes(resetTime.getMinutes() + 2); // 2 minutes
                    }
                    // Extract specific wait time if available
                    else {
                      const waitTimeMatch = errorMsg.match(/(\d+)\s*seconds/);
                      if (waitTimeMatch && waitTimeMatch[1]) {
                        const seconds = parseInt(waitTimeMatch[1], 10);
                        resetTime = new Date();
                        resetTime.setSeconds(resetTime.getSeconds() + seconds);
                      }
                    }
                  }
                } catch (parseError) {
                  console.error('Error parsing rate limit information:', parseError);
                }

                return {
                  success: false,
                  message: `Rate limit detected: ${urlError.response.data?.error?.message || 'Unknown rate limit'}`,
                  rateLimitInfo: {
                    detected: true,
                    type: rateLimitType,
                    resetTime: resetTime
                  }
                };
              }
            }
            lastError = urlError;
            // Continue to the next URL
          }
        }

        // If we found a working URL, break out of the outer loop
        if (workingUrl) break;
      }

      // If we didn't find a working URL, throw the last error
      if (!workingUrl) {
        throw lastError || new Error('All OpenRouter API endpoints failed');
      }

      // Now, test for rate limits by making multiple rapid requests
      console.log(`Testing rate limits with working URL: ${workingUrl}${workingEndpoint}`);

      // Only test rate limits with chat completions endpoint
      if (workingEndpoint.includes('chat/completions')) {
        try {
          // Make 3 rapid requests to test rate limits
          const rateLimitTestPromises = [];
          for (let i = 0; i < 3; i++) {
            rateLimitTestPromises.push(
              axios.post(
                `${workingUrl}${workingEndpoint}`,
                {
                  model: 'openai/gpt-3.5-turbo',
                  messages: [{ role: 'user', content: `Test message ${i}` }],
                  max_tokens: 1
                },
                {
                  headers: {
                    'Authorization': `Bearer ${key.token}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://trendy.local',
                    'X-Title': 'Trendy App'
                  }
                }
              )
            );
          }

          // Wait for all promises to settle (either resolve or reject)
          const results = await Promise.allSettled(rateLimitTestPromises);

          // Check if any requests failed due to rate limits
          const rateLimitedRequests = results.filter(result =>
            result.status === 'rejected' &&
            result.reason?.response?.status === 429
          );

          if (rateLimitedRequests.length > 0) {
            console.log(`Rate limit detected: ${rateLimitedRequests.length} of ${results.length} requests were rate limited`);

            // Extract rate limit information from the first rate limited request
            const rateLimitError = rateLimitedRequests[0].reason;
            let rateLimitType = 'unknown';
            let resetTime = new Date();
            resetTime.setMinutes(resetTime.getMinutes() + 60); // Default 60 minutes

            try {
              const errorData = rateLimitError.response.data;
              if (errorData && errorData.error && errorData.error.message) {
                const errorMsg = errorData.error.message;

                // Check for daily free model limit
                if (errorMsg.includes('free-models-per-day')) {
                  rateLimitType = 'daily-free';
                  resetTime = new Date();
                  resetTime.setHours(24, 0, 0, 0); // Next day at midnight
                }
                // Check for per-minute limit
                else if (errorMsg.includes('per-minute')) {
                  rateLimitType = 'per-minute';
                  resetTime = new Date();
                  resetTime.setMinutes(resetTime.getMinutes() + 2); // 2 minutes
                }
                // Extract specific wait time if available
                else {
                  const waitTimeMatch = errorMsg.match(/(\d+)\s*seconds/);
                  if (waitTimeMatch && waitTimeMatch[1]) {
                    const seconds = parseInt(waitTimeMatch[1], 10);
                    resetTime = new Date();
                    resetTime.setSeconds(resetTime.getSeconds() + seconds);
                  }
                }
              }
            } catch (parseError) {
              console.error('Error parsing rate limit information:', parseError);
            }

            return {
              success: true, // Still mark as success since the key works
              message: `API key is valid but rate limits detected: ${rateLimitedRequests.length} of ${results.length} rapid requests were rate limited`,
              rateLimitInfo: {
                detected: true,
                type: rateLimitType,
                resetTime: resetTime
              }
            };
          }
        } catch (rateLimitTestError) {
          console.error('Error during rate limit testing:', rateLimitTestError);
          // Continue with the success response even if rate limit testing fails
        }
      }

      // If we get here, the key works and no rate limits were detected
      return {
        success: true,
        message: `API key is valid (tested with ${workingUrl}${workingEndpoint})`,
        rateLimitInfo: {
          detected: false
        }
      };
    } catch (error: any) {
      console.error('Error testing OpenRouter API key:', error);

      // Handle different error cases
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        if (error.response.status === 429) {
          // Handle rate limit errors
          let rateLimitType = 'unknown';
          let resetTime = new Date();
          resetTime.setMinutes(resetTime.getMinutes() + 60); // Default 60 minutes

          try {
            const errorData = error.response.data;
            if (errorData && errorData.error && errorData.error.message) {
              const errorMsg = errorData.error.message;

              // Check for daily free model limit
              if (errorMsg.includes('free-models-per-day')) {
                rateLimitType = 'daily-free';
                resetTime = new Date();
                resetTime.setHours(24, 0, 0, 0); // Next day at midnight
              }
              // Check for per-minute limit
              else if (errorMsg.includes('per-minute')) {
                rateLimitType = 'per-minute';
                resetTime = new Date();
                resetTime.setMinutes(resetTime.getMinutes() + 2); // 2 minutes
              }
              // Extract specific wait time if available
              else {
                const waitTimeMatch = errorMsg.match(/(\d+)\s*seconds/);
                if (waitTimeMatch && waitTimeMatch[1]) {
                  const seconds = parseInt(waitTimeMatch[1], 10);
                  resetTime = new Date();
                  resetTime.setSeconds(resetTime.getSeconds() + seconds);
                }
              }
            }
          } catch (parseError) {
            console.error('Error parsing rate limit information:', parseError);
          }

          return {
            success: false,
            message: `Rate limit detected: ${error.response.data?.error?.message || 'Unknown rate limit'}`,
            rateLimitInfo: {
              detected: true,
              type: rateLimitType,
              resetTime: resetTime
            }
          };
        } else if (error.response.status === 401 || error.response.status === 403) {
          return {
            success: false,
            message: 'Invalid API key or unauthorized'
          };
        } else {
          return {
            success: false,
            message: `API error: ${error.response.status} ${error.response.statusText}`
          };
        }
      } else if (error.request) {
        // The request was made but no response was received
        return {
          success: false,
          message: 'No response from OpenRouter API'
        };
      } else {
        // Something happened in setting up the request that triggered an Error
        return {
          success: false,
          message: error.message || 'Unknown error'
        };
      }
    }
  };

  // Get all active OpenRouter API keys (not exhausted)
  storage.getActiveOpenRouterApiKeys = async (): Promise<OpenRouterApiKey[]> => {
    try {
      const stmt = storage.db.prepare(`
        SELECT
          id,
          token,
          substr(token, 1, 4) || '...' || substr(token, -4) as masked_token,
          name,
          is_active,
          last_used,
          created_at,
          user_id,
          is_default,
          exhausted_until
        FROM openrouter_api_keys
        WHERE is_active = 1
        AND (exhausted_until IS NULL OR exhausted_until < CURRENT_TIMESTAMP)
        ORDER BY is_default DESC, last_used ASC
      `);

      const keys = stmt.all();

      return keys.map((key: any) => ({
        id: key.id,
        token: key.token,
        masked_token: key.masked_token,
        name: key.name,
        is_active: Boolean(key.is_active),
        last_used: key.last_used ? new Date(key.last_used) : undefined,
        created_at: new Date(key.created_at),
        user_id: key.user_id,
        is_default: Boolean(key.is_default),
        exhausted_until: key.exhausted_until ? new Date(key.exhausted_until) : undefined
      }));
    } catch (error) {
      console.error('Error getting active OpenRouter API keys:', error);
      throw error;
    }
  };

  // Get the next available OpenRouter API key
  storage.getNextAvailableOpenRouterApiKey = async (): Promise<OpenRouterApiKey | undefined> => {
    try {
      // First try to get the default key if it's active and not exhausted
      const defaultKeyStmt = storage.db.prepare(`
        SELECT
          id,
          token,
          substr(token, 1, 4) || '...' || substr(token, -4) as masked_token,
          name,
          is_active,
          last_used,
          created_at,
          user_id,
          is_default,
          exhausted_until
        FROM openrouter_api_keys
        WHERE is_active = 1
        AND is_default = 1
        AND (exhausted_until IS NULL OR exhausted_until < CURRENT_TIMESTAMP)
        LIMIT 1
      `);

      const defaultKey = defaultKeyStmt.get();

      if (defaultKey) {
        // Update last_used timestamp
        const updateStmt = storage.db.prepare(`
          UPDATE openrouter_api_keys
          SET last_used = CURRENT_TIMESTAMP
          WHERE id = ?
        `);
        updateStmt.run(defaultKey.id);

        return {
          id: defaultKey.id,
          token: defaultKey.token,
          masked_token: defaultKey.masked_token,
          name: defaultKey.name,
          is_active: Boolean(defaultKey.is_active),
          last_used: new Date(),
          created_at: new Date(defaultKey.created_at),
          user_id: defaultKey.user_id,
          is_default: Boolean(defaultKey.is_default),
          exhausted_until: defaultKey.exhausted_until ? new Date(defaultKey.exhausted_until) : undefined
        };
      }

      // If no default key, get the next available key
      const nextKeyStmt = storage.db.prepare(`
        SELECT
          id,
          token,
          substr(token, 1, 4) || '...' || substr(token, -4) as masked_token,
          name,
          is_active,
          last_used,
          created_at,
          user_id,
          is_default,
          exhausted_until
        FROM openrouter_api_keys
        WHERE is_active = 1
        AND (exhausted_until IS NULL OR exhausted_until < CURRENT_TIMESTAMP)
        ORDER BY last_used ASC NULLS FIRST, created_at ASC
        LIMIT 1
      `);

      const nextKey = nextKeyStmt.get();

      if (!nextKey) return undefined;

      // Update last_used timestamp
      const updateStmt = storage.db.prepare(`
        UPDATE openrouter_api_keys
        SET last_used = CURRENT_TIMESTAMP
        WHERE id = ?
      `);
      updateStmt.run(nextKey.id);

      return {
        id: nextKey.id,
        token: nextKey.token,
        masked_token: nextKey.masked_token,
        name: nextKey.name,
        is_active: Boolean(nextKey.is_active),
        last_used: new Date(),
        created_at: new Date(nextKey.created_at),
        user_id: nextKey.user_id,
        is_default: Boolean(nextKey.is_default),
        exhausted_until: nextKey.exhausted_until ? new Date(nextKey.exhausted_until) : undefined
      };
    } catch (error) {
      console.error('Error getting next available OpenRouter API key:', error);
      throw error;
    }
  };

  // Mark an OpenRouter API key as exhausted
  storage.markOpenRouterApiKeyExhausted = async (id: number, minutes: number = 60, rateLimitType: string = 'unknown'): Promise<void> => {
    try {
      const key = await storage.getOpenRouterApiKey(id);
      if (!key) {
        throw new Error('OpenRouter API key not found');
      }

      // Calculate the exhausted until time
      const exhaustedUntil = new Date();
      exhaustedUntil.setMinutes(exhaustedUntil.getMinutes() + minutes);

      // Add rate_limit_type column if it doesn't exist and check if it exists
      let hasRateLimitType = false;
      try {
        const tableInfo = storage.db.prepare("PRAGMA table_info(openrouter_api_keys)").all();
        hasRateLimitType = tableInfo.some((column: any) => column.name === 'rate_limit_type');

        if (!hasRateLimitType) {
          console.log('Adding rate_limit_type column to openrouter_api_keys table');
          storage.db.exec(`ALTER TABLE openrouter_api_keys ADD COLUMN rate_limit_type TEXT;`);
          hasRateLimitType = true; // Column should exist now
        }
      } catch (columnError) {
        console.error('Error checking or adding rate_limit_type column:', columnError);
      }

      // Construct the query based on whether the column exists
      const query = hasRateLimitType
        ? `
          UPDATE openrouter_api_keys
          SET exhausted_until = ?,
              rate_limit_type = ?,
              last_used = CURRENT_TIMESTAMP
          WHERE id = ?
        `
        : `
          UPDATE openrouter_api_keys
          SET exhausted_until = ?,
              last_used = CURRENT_TIMESTAMP
          WHERE id = ?
        `;

      const stmt = storage.db.prepare(query);

      if (hasRateLimitType) {
        stmt.run(exhaustedUntil.toISOString(), rateLimitType, id);
      } else {
        stmt.run(exhaustedUntil.toISOString(), id);
      }

      console.log(`Marked OpenRouter API key ${id} as exhausted until ${exhaustedUntil.toISOString()} (${rateLimitType})`);
    } catch (error) {
      console.error('Error marking OpenRouter API key as exhausted:', error);
      throw error;
    }
  };
}

export default implementOpenRouterApiKeyMethods;
