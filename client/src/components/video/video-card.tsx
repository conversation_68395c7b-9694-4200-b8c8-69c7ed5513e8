import { Video } from "@shared/schema";
import { Card, CardContent } from "@/components/ui/card";
import { useSettings } from "@/hooks/use-settings";
import { Eye, Clock, Info, CheckCircle, EyeOff } from "lucide-react";
import { cn } from "@/lib/utils";
import { useState, useEffect, useRef, useCallback } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useFloatingVideo } from "@/context/floating-video-context";
import { BookmarkButton } from "./bookmark-button";
import { YoutubeThumbnail } from "./youtube-thumbnail";
import { castVideo, getCurrentSession, isCastingAvailable } from "@/lib/cast";
import { getViewsPerHourColor } from "@/hooks/use-financial-analysis";
import { formatRelativeTime, formatDate } from "@/lib/date-format";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";

// Helper function to format ISO 8601 duration (PT1M30S) to human-readable format (1:30)
function formatDuration(durationStr: string): string {
  try {
    const minutesMatch = durationStr.match(/([0-9]+)M/);
    const secondsMatch = durationStr.match(/([0-9]+)S/);
    const hoursMatch = durationStr.match(/([0-9]+)H/);

    const hours = hoursMatch ? parseInt(hoursMatch[1]) : 0;
    const minutes = minutesMatch ? parseInt(minutesMatch[1]) : 0;
    const seconds = secondsMatch ? parseInt(secondsMatch[1]) : 0;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  } catch (e) {
    console.error('Error parsing duration:', durationStr, e);
    return durationStr;
  }
}

interface VideoCardProps {
  video: Video;
  onPlay: (video: Video) => void;
  pauseInfiniteScroll?: () => void; // Function to pause infinite scroll
}

export function VideoCard({ video, onPlay, pauseInfiniteScroll }: VideoCardProps) {
  const { settings, updateSettings } = useSettings();
  const { openFloatingVideo, isFloatingPlayerActive } = useFloatingVideo();
  const [isHovering, setIsHovering] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [metadataOpen, setMetadataOpen] = useState(false);
  const isMobile = useIsMobile();
  const previewTimeout = useRef<NodeJS.Timeout>();

  // Check if this video has been watched
  const isWatched = settings?.watchedVideos?.includes(video.id) || false;

  const openVideo = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();

    // Check if we're already casting
    const isCasting = isCastingAvailable() && !!getCurrentSession();

    if (isCasting && e.ctrlKey) {
      // If Ctrl+Click and we're casting, cast this video directly
      castVideo(video.id, video.title, video.thumbnail)
        .then(() => {
          console.log('Video cast successfully');
        })
        .catch(error => {
          console.error('Error casting video:', error);
          // Fallback to floating player if casting fails
          openFloatingVideo(video);
        });
    } else {
      // Open directly in floating player if not casting
      openFloatingVideo(video);
    }

    // Mark as playing to prevent thumbnail from being reset
    setIsPlaying(true);

    // Explicitly disable hover preview
    setIsHovering(false);
  }, [video, openFloatingVideo, pauseInfiniteScroll]);

  // Reference to track preview watch time
  const previewWatchTimer = useRef<NodeJS.Timeout>();
  const previewWatchStartTime = useRef<number>(0);

  // Handle preview functionality
  useEffect(() => {
    if (!settings?.useInAppPlayer) return;

    // If hover preview is disabled or a floating player is active, don't show previews
    if (isFloatingPlayerActive) return;

    const shouldPreview = (isMobile && isFocused) || (!isMobile && isHovering);

    if (shouldPreview) {
      // Start the preview
      previewTimeout.current = setTimeout(() => {
        const previewContainer = document.getElementById(`preview-${video.id}`);
        if (previewContainer) {
          previewContainer.innerHTML = `
            <div class="absolute inset-0">
              <iframe
                src="https://www.youtube.com/embed/${video.id}?enablejsapi=1&modestbranding=1&showinfo=0&fs=1&rel=0&playsinline=1&autoplay=1&mute=1"
                class="w-full h-full"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowfullscreen
              ></iframe>
            </div>
          `;

          // Start tracking watch time
          previewWatchStartTime.current = Date.now();

          // Set a timer to mark as watched after 30 seconds
          previewWatchTimer.current = setTimeout(() => {
            // Only mark as watched if not already watched
            if (!isWatched) {
              console.log(`Marking video ${video.id} as watched after 30s preview`);
              const watchedVideos = [...(settings?.watchedVideos || []), video.id];
              updateSettings.mutate({ ...settings, watchedVideos });
            }
          }, 30000); // 30 seconds
        }
      }, isMobile ? 500 : 1000); // Faster preview on mobile
    } else {
      // Clear the preview
      if (previewTimeout.current) {
        clearTimeout(previewTimeout.current);
      }

      // Clear the watch timer
      if (previewWatchTimer.current) {
        clearTimeout(previewWatchTimer.current);
        previewWatchTimer.current = undefined;
      }

      const previewContainer = document.getElementById(`preview-${video.id}`);
      if (previewContainer) {
        // Create a new YoutubeThumbnail component
        previewContainer.innerHTML = '';
        const thumbnailElement = document.createElement('div');
        thumbnailElement.className = 'w-full h-full';

        // Render the YoutubeThumbnail component
        const thumbnailImg = document.createElement('img');
        thumbnailImg.className = 'w-full h-full object-cover';
        thumbnailImg.alt = video.title;
        thumbnailImg.src = `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`;
        thumbnailImg.loading = 'lazy';

        // Add error handling for the thumbnail
        thumbnailImg.onerror = () => {
          thumbnailImg.src = `https://i.ytimg.com/vi/${video.id}/mqdefault.jpg`;
          thumbnailImg.onerror = () => {
            thumbnailImg.src = `https://i.ytimg.com/vi/${video.id}/default.jpg`;
            thumbnailImg.onerror = () => {
              thumbnailImg.src = 'https://placehold.co/480x360/222222/DDDDDD?text=No+Thumbnail';
            };
          };
        };

        thumbnailElement.appendChild(thumbnailImg);
        previewContainer.appendChild(thumbnailElement);
      }
    }

    // Cleanup function
    return () => {
      if (previewWatchTimer.current) {
        clearTimeout(previewWatchTimer.current);
      }
      if (previewTimeout.current) {
        clearTimeout(previewTimeout.current);
      }
    };
  }, [isHovering, isFocused, isMobile, video.id, settings?.useInAppPlayer, isFloatingPlayerActive]);

  // Calculate views per hour
  let viewsPerHour = 0;
  try {
    const hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
    viewsPerHour = Math.round((video.viewCount || 0) / hoursElapsed);
  } catch (e) {
    console.error('Error calculating views per hour:', e);
  }

  return (
    <Card
      className={cn(
        "overflow-hidden cursor-pointer transition-transform hover:scale-[1.02] w-full",
        isWatched && "opacity-70 grayscale hover:opacity-90 hover:grayscale-0"
      )}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
      onTouchStart={() => setIsFocused(true)}
      onTouchEnd={() => setIsFocused(false)}
      tabIndex={0}
      data-video-id={video.id}
      data-is-playing={isPlaying ? 'true' : 'false'}
      data-watched={isWatched ? 'true' : 'false'}
    >
      <div className="relative">
        <div id={`preview-${video.id}`} className="w-full aspect-video relative bg-card">
          <YoutubeThumbnail
            videoId={video.id}
            title={video.title}
            className="w-full h-full object-cover"
          />
        </div>
        {settings?.watchedVideos?.includes(video.id) && (
          <div className="absolute top-2 left-2 bg-primary px-2 py-1 rounded text-xs text-primary-foreground">
            Watched
          </div>
        )}
        {/* Add BookmarkButton for playlist functionality */}
        <BookmarkButton video={video} />
      </div>
      <CardContent className="p-4">
        <h3
          className="font-semibold leading-tight line-clamp-2 hover:text-primary cursor-pointer"
          onClick={openVideo}
        >
          {video.title}
        </h3>
        <p className="text-sm text-muted-foreground mt-1">{video.channelTitle}</p>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Eye className="w-4 h-4" />
            <span>{video.viewCount.toLocaleString()}</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            <span>
              {(() => {
                try {
                  return formatRelativeTime(new Date(video.publishedAt));
                } catch (e) {
                  return 'recently';
                }
              })()}
            </span>
          </div>
        </div>
        <div className="mt-2 flex justify-between items-center">
          <div className={`text-xs font-bold ${getViewsPerHourColor(viewsPerHour)}`}>
            <span className="text-sm">{viewsPerHour}</span> <span className="text-xs opacity-80">vph</span>
          </div>
          <div className="flex items-center gap-1">
            {/* Toggle Watched Status Button */}
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                if (isWatched) {
                  // Mark as unwatched
                  const watchedVideos = settings?.watchedVideos?.filter(id => id !== video.id) || [];
                  updateSettings.mutate({ ...settings, watchedVideos });
                } else {
                  // Mark as watched
                  const watchedVideos = [...(settings?.watchedVideos || []), video.id];
                  updateSettings.mutate({ ...settings, watchedVideos });
                }
              }}
              title={isWatched ? "Mark as unwatched" : "Mark as watched"}
            >
              {isWatched ? (
                <EyeOff className="h-4 w-4 text-muted-foreground hover:text-primary" />
              ) : (
                <Eye className="h-4 w-4 text-muted-foreground hover:text-primary" />
              )}
            </Button>

            {/* Info Button */}
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                setMetadataOpen(true);
              }}
              title="View metadata"
            >
              <Info className="h-4 w-4 text-muted-foreground hover:text-primary" />
            </Button>
          </div>
        </div>

        {/* Metadata Dialog */}
        <Dialog open={metadataOpen} onOpenChange={setMetadataOpen}>
          <DialogContent className="max-w-3xl max-h-[80vh]">
            <DialogHeader>
              <DialogTitle>Video Metadata</DialogTitle>
              <DialogDescription>
                All available metadata for this video from the VidIQ API
              </DialogDescription>
            </DialogHeader>
            <ScrollArea className="h-[60vh] mt-4">
              <div className="space-y-4">
                {/* Video Title and Basic Info */}
                <div className="border rounded-md p-3 bg-card">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-bold">Basic Info</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const basicInfo = {
                          id: video.id,
                          title: video.title,
                          url: video.url,
                          channelTitle: video.channelTitle,
                          publishedAt: video.publishedAt,
                          viewCount: video.viewCount
                        };
                        navigator.clipboard.writeText(JSON.stringify(basicInfo, null, 2));
                      }}
                    >
                      Copy
                    </Button>
                  </div>
                  <h3 className="font-bold text-lg mb-2">{video.title}</h3>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div><span className="font-medium">Video ID:</span> {video.id}</div>
                    <div><span className="font-medium">URL:</span> {video.url}</div>
                    <div><span className="font-medium">Channel:</span> {video.channelTitle}</div>
                    <div><span className="font-medium">Channel ID:</span> {video.channel_id}</div>
                    <div><span className="font-medium">Published:</span> {formatDate(new Date(video.publishedAt))}</div>
                    <div><span className="font-medium">Views/Hour:</span> {Math.round(video.vph || 0).toLocaleString()}</div>
                  </div>
                </div>

                {/* Description Section */}
                {video.description && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Description</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(video.description);
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="bg-muted p-2 rounded text-sm whitespace-pre-wrap max-h-[200px] overflow-auto">
                      {video.description || "No description available"}
                    </div>
                  </div>
                )}

                {/* Content Details Section */}
                {video.contentDetails && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Content Details</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(JSON.stringify(video.contentDetails, null, 2));
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="font-medium">Duration:</span> {video.contentDetails.duration}
                        {video.contentDetails.duration && (
                          <span className="ml-2 text-muted-foreground">
                            ({formatDuration(video.contentDetails.duration)})
                          </span>
                        )}
                      </div>
                      <div><span className="font-medium">Definition:</span> {video.contentDetails.definition}</div>
                      <div><span className="font-medium">Dimension:</span> {video.contentDetails.dimension}</div>
                      <div><span className="font-medium">Caption:</span> {video.contentDetails.caption}</div>
                      <div><span className="font-medium">Licensed:</span> {String(video.contentDetails.licensedContent)}</div>
                      <div><span className="font-medium">Projection:</span> {video.contentDetails.projection}</div>
                      {video.contentDetails.regionRestriction && (
                        <div className="col-span-2">
                          <span className="font-medium">Region Restriction:</span>
                          <span className="ml-2">
                            {video.contentDetails.regionRestriction.allowed ?
                              `Available in ${video.contentDetails.regionRestriction.allowed.length} countries` :
                              "No region restrictions"}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Statistics Section */}
                {video.statistics && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Statistics</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(JSON.stringify(video.statistics, null, 2));
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div><span className="font-medium">View Count:</span> {parseInt(video.statistics.viewCount).toLocaleString()}</div>
                      <div><span className="font-medium">Like Count:</span> {parseInt(video.statistics.likeCount).toLocaleString()}</div>
                      <div><span className="font-medium">Comment Count:</span> {parseInt(video.statistics.commentCount).toLocaleString()}</div>
                      <div><span className="font-medium">Favorite Count:</span> {parseInt(video.statistics.favoriteCount).toLocaleString()}</div>
                    </div>
                  </div>
                )}

                {/* Tags Section */}
                {video.tags && video.tags.length > 0 && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Tags ({video.tags.length})</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(JSON.stringify(video.tags, null, 2));
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1 max-h-[150px] overflow-auto p-1">
                      {video.tags.map((tag, index) => (
                        <div key={index} className="bg-muted px-2 py-1 rounded-md text-xs">{tag}</div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Matched Tags Section */}
                {video.matched_tags && video.matched_tags.length > 0 && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Matched Tags ({video.matched_tags.length})</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(JSON.stringify(video.matched_tags, null, 2));
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {video.matched_tags.map((tag, index) => (
                        <div key={index} className="bg-muted px-2 py-1 rounded-md text-xs">{tag}</div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Unmatched Tags Section */}
                {video.unmatched_tags && video.unmatched_tags.length > 0 && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Unmatched Tags ({video.unmatched_tags.length})</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(JSON.stringify(video.unmatched_tags, null, 2));
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1 max-h-[150px] overflow-auto p-1">
                      {video.unmatched_tags.map((tag, index) => (
                        <div key={index} className="bg-muted px-2 py-1 rounded-md text-xs">{tag}</div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Seen From Section */}
                {video.seen_from && video.seen_from.length > 0 && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Seen From</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(JSON.stringify(video.seen_from, null, 2));
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {video.seen_from.map((item, index) => (
                        <div key={index} className="bg-muted px-2 py-1 rounded-md text-xs">{item}</div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Related To Section */}
                {video.related_to && video.related_to.length > 0 && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Related To</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(JSON.stringify(video.related_to, null, 2));
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {video.related_to.map((item, index) => (
                        <div key={index} className="bg-muted px-2 py-1 rounded-md text-xs">{item}</div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Raw Data Section */}
                <div className="border rounded-md p-3 bg-card">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-bold">Raw Data</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(JSON.stringify(video, null, 2));
                      }}
                    >
                      Copy All
                    </Button>
                  </div>
                  <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-[200px]">
                    {JSON.stringify(video, null, 2)}
                  </pre>
                </div>
              </div>
            </ScrollArea>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}