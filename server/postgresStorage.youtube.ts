import { YoutubeChannel, YoutubeVideo } from "@shared/schema";
import { db } from "./db";
import { youtubeChannels, youtubeVideos } from "@shared/schema";
import { eq, and, desc } from "drizzle-orm";
import axios from "axios";
import cheerio from "cheerio";

// YouTube channel methods for PostgreSQL storage
export async function getYoutubeChannels(userId: number): Promise<YoutubeChannel[]> {
  return await db.select().from(youtubeChannels).where(eq(youtubeChannels.userId, userId));
}

export async function getYoutubeChannel(channelId: number): Promise<YoutubeChannel | undefined> {
  const channels = await db.select().from(youtubeChannels).where(eq(youtubeChannels.id, channelId));
  return channels.length > 0 ? channels[0] : undefined;
}

export async function createYoutubeChannel(userId: number, channel: Partial<YoutubeChannel>): Promise<YoutubeChannel> {
  // Get channel details from YouTube if not provided
  if (!channel.thumbnail || !channel.description || !channel.subscriberCount) {
    try {
      const channelDetails = await fetchChannelDetails(channel.channelUrl);
      channel = {
        ...channel,
        ...channelDetails
      };
    } catch (error) {
      console.error("Error fetching channel details:", error);
      // Continue with available data
    }
  }

  const result = await db.insert(youtubeChannels).values({
    userId,
    channelId: channel.channelId!,
    channelTitle: channel.channelTitle!,
    channelUrl: channel.channelUrl!,
    thumbnail: channel.thumbnail || null,
    description: channel.description || null,
    subscriberCount: channel.subscriberCount || 0,
    lastRefreshTime: new Date(),
    displayOrder: 0
  }).returning();

  // Immediately fetch videos for this channel
  if (result.length > 0) {
    await refreshYoutubeChannelVideos(result[0].id);
  }

  return result[0];
}

export async function updateYoutubeChannel(channelId: number, updates: Partial<YoutubeChannel>): Promise<YoutubeChannel> {
  const result = await db.update(youtubeChannels)
    .set(updates)
    .where(eq(youtubeChannels.id, channelId))
    .returning();

  return result[0];
}

export async function deleteYoutubeChannel(channelId: number): Promise<void> {
  // First delete all videos associated with this channel
  await db.delete(youtubeVideos)
    .where(eq(youtubeVideos.channelId, (await getYoutubeChannel(channelId))?.channelId || ''));

  // Then delete the channel
  await db.delete(youtubeChannels)
    .where(eq(youtubeChannels.id, channelId));
}

export async function getYoutubeChannelVideos(channelId: number): Promise<YoutubeVideo[]> {
  const channel = await getYoutubeChannel(channelId);
  if (!channel) {
    return [];
  }

  return await db.select()
    .from(youtubeVideos)
    .where(eq(youtubeVideos.channelId, channel.channelId))
    .orderBy(desc(youtubeVideos.publishedAt));
}

export async function refreshYoutubeChannelVideos(channelId: number): Promise<YoutubeVideo[]> {
  const channel = await getYoutubeChannel(channelId);
  if (!channel) {
    throw new Error("Channel not found");
  }

  try {
    // Fetch videos from YouTube
    const videos = await fetchChannelVideos(channel);

    // Save videos to database
    for (const video of videos) {
      // Check if video already exists
      const existingVideos = await db.select()
        .from(youtubeVideos)
        .where(eq(youtubeVideos.id, video.id));

      if (existingVideos.length === 0) {
        // Insert new video
        await db.insert(youtubeVideos).values({
          ...video,
          userId: channel.userId
        });
      }
    }

    // Update channel's last refresh time
    await db.update(youtubeChannels)
      .set({ lastRefreshTime: new Date() })
      .where(eq(youtubeChannels.id, channelId));

    return await getYoutubeChannelVideos(channelId);
  } catch (error) {
    console.error("Error refreshing channel videos:", error);
    throw error;
  }
}

export async function getYoutubeVideo(videoId: string): Promise<YoutubeVideo | undefined> {
  const videos = await db.select().from(youtubeVideos).where(eq(youtubeVideos.id, videoId));
  return videos.length > 0 ? videos[0] : undefined;
}

export async function updateYoutubeVideoTranscription(videoId: string, transcription: string): Promise<YoutubeVideo> {
  const result = await db.update(youtubeVideos)
    .set({
      transcription,
      hasTranscription: true
    })
    .where(eq(youtubeVideos.id, videoId))
    .returning();

  return result[0];
}

// Helper functions to fetch data from YouTube without using the API
async function fetchChannelDetails(channelUrl: string): Promise<Partial<YoutubeChannel>> {
  try {
    const response = await axios.get(channelUrl);
    const $ = cheerio.load(response.data);

    // Extract channel details
    const thumbnail = $('meta[property="og:image"]').attr('content') || '';
    const description = $('meta[property="og:description"]').attr('content') || '';

    // Try to extract subscriber count (this is challenging without the API)
    let subscriberCount = 0;
    const subscriberText = $('span[id="subscriber-count"]').text();
    if (subscriberText) {
      // Parse subscriber count from text like "1.2M subscribers"
      const match = subscriberText.match(/([0-9.]+)([KMB]?)/);
      if (match) {
        const num = parseFloat(match[1]);
        const unit = match[2];

        if (unit === 'K') subscriberCount = Math.round(num * 1000);
        else if (unit === 'M') subscriberCount = Math.round(num * 1000000);
        else if (unit === 'B') subscriberCount = Math.round(num * 1000000000);
        else subscriberCount = Math.round(num);
      }
    }

    return {
      thumbnail,
      description,
      subscriberCount
    };
  } catch (error) {
    console.error("Error fetching channel details:", error);
    return {};
  }
}

async function fetchChannelVideos(channel: YoutubeChannel): Promise<Partial<YoutubeVideo>[]> {
  try {
    console.log(`Fetching videos for channel: ${channel.channelTitle} (ID: ${channel.id}, channelId: ${channel.channelId})`);

    // Use RSS feed to get recent videos (this is more reliable than scraping)
    const channelId = channel.channelId;

    // Make sure the channel ID is in the correct format
    // YouTube channel IDs typically start with 'UC'
    if (!channelId.startsWith('UC')) {
      console.warn(`Channel ID ${channelId} doesn't start with UC, which might cause issues`);
    }

    // Try different RSS feed formats
    let response;
    let rssUrl;

    try {
      // First try the channel_id format
      rssUrl = `https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`;
      console.log(`Trying RSS feed URL: ${rssUrl}`);
      response = await axios.get(rssUrl);
      console.log(`Successfully fetched RSS feed with channel_id=${channelId}`);
    } catch (error) {
      console.warn(`Failed to fetch RSS feed with channel_id=${channelId}, trying user format`);
      // If that fails, try the user format
      try {
        rssUrl = `https://www.youtube.com/feeds/videos.xml?user=${channel.channelTitle}`;
        console.log(`Trying alternative RSS feed URL: ${rssUrl}`);
        response = await axios.get(rssUrl);
        console.log(`Successfully fetched RSS feed with user=${channel.channelTitle}`);
      } catch (userError) {
        console.error(`Failed to fetch RSS feed with user=${channel.channelTitle}`);
        throw error; // Throw the original error
      }
    }

    const $ = cheerio.load(response.data, { xmlMode: true });
    const videos: Partial<YoutubeVideo>[] = [];

    // Get the video limit from the channel settings
    const videoLimit = channel.videoLimit || 15;
    console.log(`Fetching up to ${videoLimit} videos for channel ${channel.channelTitle}`);

    // Counter for videos processed
    let videoCount = 0;

    // Log the number of entries found
    const entryCount = $('entry').length;
    console.log(`Found ${entryCount} entries in the RSS feed for channel ${channel.channelTitle}`);

    $('entry').each((i, entry) => {
      // Check if we've reached the limit
      if (videoCount >= videoLimit) {
        console.log(`Reached video limit of ${videoLimit} for channel ${channel.channelTitle}`);
        return false; // Stop processing more entries
      }

      const $entry = $(entry);

      // Extract video ID from the yt:videoId tag
      const videoId = $entry.find('yt\\:videoId').text();
      if (!videoId) {
        console.warn('Could not find video ID in entry, skipping');
        return;
      }

      // Extract other video details
      const title = $entry.find('title').text();
      const publishedAt = new Date($entry.find('published').text());
      const channelTitle = $entry.find('author > name').text() || channel.channelTitle;

      // Get thumbnail URL
      const thumbnail = `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;

      // Get video description
      const description = $entry.find('media\\:description').text() || $entry.find('content').text();

      console.log(`Found video: ${title} (ID: ${videoId}) from channel ${channelTitle}`);

      videos.push({
        id: videoId,
        title,
        thumbnail,
        channelId: channel.channelId,
        channelTitle,
        publishedAt,
        description,
        userId: channel.userId,
        viewCount: 0, // We can't get this from RSS
        duration: 'PT0S', // We can't get this from RSS
        hasTranscription: false
      });

      videoCount++;
    });

    console.log(`Successfully fetched ${videos.length} videos for channel ${channel.channelTitle}`);
    return videos;
  } catch (error) {
    console.error(`Error fetching channel videos for ${channel.channelTitle}:`, error);
    return [];
  }
}
