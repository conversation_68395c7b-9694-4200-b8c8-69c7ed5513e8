/**
 * Migration to remove the verbose_ai_logging column from the settings table
 * since verbose error logging is now always enabled
 */

import Database from 'better-sqlite3';
import path from 'path';

async function main() {
  console.log('Starting migration to remove verbose_ai_logging column...');

  try {
    // Connect to the database
    const dbPath = path.resolve(process.cwd(), 'data.db');
    console.log(`Using database at: ${dbPath}`);
    const db = new Database(dbPath);

    // Check if the settings table exists
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'").get();
    if (!tableExists) {
      console.log('Settings table does not exist. No migration needed.');
      return;
    }

    // Get the current schema
    const columnsInfo = db.prepare('PRAGMA table_info(settings)').all();
    console.log(`Found ${columnsInfo.length} columns in settings table`);

    // Check if the verbose_ai_logging column exists
    const hasVerboseAiLogging = columnsInfo.some((col: any) => col.name === 'verbose_ai_logging');

    if (hasVerboseAiLogging) {
      console.log('Found verbose_ai_logging column, creating new settings table without it');

      // Get all column names except verbose_ai_logging
      const columnsToKeep = columnsInfo
        .filter((col: any) => col.name !== 'verbose_ai_logging')
        .map((col: any) => col.name);

      // Create a new settings table without the verbose_ai_logging column
      const createTableSQL = `
        CREATE TABLE settings_new (
          ${columnsToKeep.map(colName => {
            const col = columnsInfo.find((c: any) => c.name === colName);
            return `${colName} ${col.type}${col.notnull ? ' NOT NULL' : ''}${col.dflt_value ? ` DEFAULT ${col.dflt_value}` : ''}${col.pk ? ' PRIMARY KEY' : ''}`;
          }).join(',\n          ')}
        );
      `;

      console.log('Creating new table with SQL:', createTableSQL);
      db.exec(createTableSQL);

      // Copy data from the old table to the new one
      const insertSQL = `
        INSERT INTO settings_new
        SELECT ${columnsToKeep.join(', ')}
        FROM settings;
      `;

      console.log('Copying data with SQL:', insertSQL);
      db.exec(insertSQL);

      // Drop the old table and rename the new one
      console.log('Replacing old table with new table...');
      db.exec(`
        DROP TABLE settings;
        ALTER TABLE settings_new RENAME TO settings;
      `);

      console.log('Successfully removed verbose_ai_logging column from settings table');
    } else {
      console.log('verbose_ai_logging column does not exist. No migration needed.');
    }

    // Verify the new schema
    const newColumnsInfo = db.prepare('PRAGMA table_info(settings)').all();
    console.log(`Settings table now has ${newColumnsInfo.length} columns`);
    console.log('Column names:', newColumnsInfo.map((col: any) => col.name).join(', '));

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
  }
}

main();
