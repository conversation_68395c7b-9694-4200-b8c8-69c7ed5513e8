const Database = require('better-sqlite3');

function resetAdmin() {
  try {
    console.log('Starting admin reset...');
    const db = new Database('data.db');
    
    // Check table structure
    const tableInfo = db.prepare("PRAGMA table_info(users)").all();
    console.log('Table structure:', tableInfo.map(col => col.name));
    
    const hasIsAdminColumn = tableInfo.some(col => col.name === 'is_admin');
    
    // Check if admin user exists
    const adminUser = db.prepare("SELECT * FROM users WHERE username = 'admin'").get();
    
    if (adminUser) {
      console.log('Admin user found, updating...');
      // Update admin user with plain text password and admin role
      let updateQuery = "UPDATE users SET password = '1', role = 'admin'";
      if (hasIsAdminColumn) {
        updateQuery += ", is_admin = 1";
      }
      updateQuery += " WHERE username = 'admin'";
      
      const updateResult = db.prepare(updateQuery).run();
      console.log(`Updated ${updateResult.changes} rows`);
    } else {
      console.log('Admin user not found, creating...');
      // Create admin user with plain text password and admin role
      let insertQuery = "INSERT INTO users (username, password, role";
      let valuesQuery = "VALUES ('admin', '1', 'admin'";
      
      if (hasIsAdminColumn) {
        insertQuery += ", is_admin";
        valuesQuery += ", 1";
      }
      
      insertQuery += ") " + valuesQuery + ")";
      
      const insertResult = db.prepare(insertQuery).run();
      console.log(`Inserted ${insertResult.changes} rows`);
    }
    
    // Verify admin user
    const verifyAdmin = db.prepare("SELECT * FROM users WHERE username = 'admin'").get();
    if (verifyAdmin) {
      console.log('Admin user verified:', verifyAdmin);
    } else {
      console.log('Failed to verify admin user');
    }
    
    // Create a test user
    console.log('Creating test user...');
    const testUser = db.prepare("SELECT * FROM users WHERE username = 'test'").get();
    
    if (testUser) {
      console.log('Test user found, updating...');
      let updateQuery = "UPDATE users SET password = 'test', role = 'user'";
      if (hasIsAdminColumn) {
        updateQuery += ", is_admin = 0";
      }
      updateQuery += " WHERE username = 'test'";
      
      const updateResult = db.prepare(updateQuery).run();
      console.log(`Updated ${updateResult.changes} rows`);
    } else {
      console.log('Test user not found, creating...');
      let insertQuery = "INSERT INTO users (username, password, role";
      let valuesQuery = "VALUES ('test', 'test', 'user'";
      
      if (hasIsAdminColumn) {
        insertQuery += ", is_admin";
        valuesQuery += ", 0";
      }
      
      insertQuery += ") " + valuesQuery + ")";
      
      const insertResult = db.prepare(insertQuery).run();
      console.log(`Inserted ${insertResult.changes} rows`);
    }
    
    // Verify test user
    const verifyTest = db.prepare("SELECT * FROM users WHERE username = 'test'").get();
    if (verifyTest) {
      console.log('Test user verified:', verifyTest);
    } else {
      console.log('Failed to verify test user');
    }
    
    console.log('Reset completed successfully');
  } catch (error) {
    console.error('Reset failed:', error);
  }
}

resetAdmin();
