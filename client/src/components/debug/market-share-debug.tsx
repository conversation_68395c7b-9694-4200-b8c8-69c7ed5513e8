import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { marketShareCollector } from '@/services/market-share-collector';
import { Database, Play, Pause, Trash2, RefreshCw } from 'lucide-react';

export function MarketShareDebug() {
  const [isVisible, setIsVisible] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [dailyData, setDailyData] = useState([]);
  const [hourlyData, setHourlyData] = useState([]);
  const [currentData, setCurrentData] = useState(null);

  const refreshData = () => {
    setIsRunning(marketShareCollector.isRunning());
    setDailyData(marketShareCollector.getDailyData());

    // Get today's hourly data
    const today = new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    setHourlyData(marketShareCollector.getHourlyData(today));

    // Get current data from localStorage
    try {
      const current = localStorage.getItem('current_market_share_data');
      if (current) {
        setCurrentData(JSON.parse(current));
      }
    } catch (error) {
      console.error('Error reading current data:', error);
    }
  };

  useEffect(() => {
    if (isVisible) {
      refreshData();
      const interval = setInterval(refreshData, 2000); // Refresh every 2 seconds
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const handleStart = () => {
    marketShareCollector.startCollection();
    refreshData();
  };

  const handleStop = () => {
    marketShareCollector.stopCollection();
    refreshData();
  };

  const handleCollectNow = () => {
    marketShareCollector.collectCurrentData();
    setTimeout(refreshData, 500); // Refresh after a short delay
  };

  const handleClearData = () => {
    localStorage.removeItem('market_share_hourly_history');
    localStorage.removeItem('market_share_history');
    localStorage.removeItem('current_market_share_data');
    refreshData();
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-20 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-background/80 backdrop-blur-sm"
        >
          <Database className="h-4 w-4 mr-2" />
          Data Monitor
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-20 right-4 z-50">
      <Card className="w-96 bg-background/95 backdrop-blur-sm border-2">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Market Share Data Monitor</CardTitle>
            <div className="flex gap-1">
              <Button
                onClick={refreshData}
                variant="outline"
                size="sm"
                className="h-6 w-6 p-0"
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
              <Button
                onClick={() => setIsVisible(false)}
                variant="outline"
                size="sm"
                className="h-6 w-6 p-0"
              >
                ×
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          {/* Collector Status */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Collector Status:</span>
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${isRunning ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
                <span className="text-xs font-mono">{isRunning ? 'Running' : 'Stopped'}</span>
              </div>
            </div>

            <div className="flex gap-1">
              <Button
                onClick={handleStart}
                disabled={isRunning}
                variant="outline"
                size="sm"
                className="h-6 text-xs"
              >
                <Play className="h-3 w-3 mr-1" />
                Start
              </Button>
              <Button
                onClick={handleStop}
                disabled={!isRunning}
                variant="outline"
                size="sm"
                className="h-6 text-xs"
              >
                <Pause className="h-3 w-3 mr-1" />
                Stop
              </Button>
              <Button
                onClick={handleCollectNow}
                variant="outline"
                size="sm"
                className="h-6 text-xs"
              >
                Collect Now
              </Button>
              <Button
                onClick={handleClearData}
                variant="outline"
                size="sm"
                className="h-6 text-xs text-red-600"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Current Data */}
          {currentData && (
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">Current Data:</div>
              <div className="text-xs font-mono bg-muted p-2 rounded">
                <div>HowToGuys: {currentData.howToGuysViews?.toLocaleString() || 'N/A'}</div>
                <div>Competitors: {currentData.competitorViews?.toLocaleString() || 'N/A'}</div>
                <div>Market Share: {currentData.marketShare?.toFixed(1) || 'N/A'}%</div>
                <div>Updated: {new Date(currentData.timestamp).toLocaleTimeString()}</div>
              </div>
            </div>
          )}

          {/* Data Counts */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Daily Points:</span>
              <span className={`font-mono ${dailyData.length > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {dailyData.length}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Today's Hours:</span>
              <span className={`font-mono ${hourlyData.length > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {hourlyData.length}
              </span>
            </div>
          </div>

          {/* Recent Data */}
          {dailyData.length > 0 && (
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">Recent Daily Data:</div>
              <div className="max-h-20 overflow-y-auto text-xs font-mono bg-muted p-2 rounded">
                {dailyData.slice(-3).map((item, index) => (
                  <div key={index} className="text-[10px]">
                    {item.date}: {item.marketShare?.toFixed(1)}% ({item.howToGuysViews?.toLocaleString()})
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Today's Hourly Data */}
          {hourlyData.length > 0 && (
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">Today's Hourly Data:</div>
              <div className="max-h-20 overflow-y-auto text-xs font-mono bg-muted p-2 rounded">
                {hourlyData.slice(-3).map((item, index) => (
                  <div key={index} className="text-[10px]">
                    {item.hour}:00 - {item.marketShare?.toFixed(1)}% ({item.howToGuysViews?.toLocaleString()})
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* No Data Message */}
          {dailyData.length === 0 && hourlyData.length === 0 && (
            <div className="text-xs text-center text-muted-foreground py-2">
              No data collected yet. Visit YTR tab to generate data.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
