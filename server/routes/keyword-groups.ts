import { Router } from 'express';
import { storage } from '../storage';
import { z } from 'zod';

const router = Router();

// Schema for creating/updating a keyword group
const keywordGroupSchema = z.object({
  name: z.string().min(1, "Name is required"),
  keywords: z.array(z.string()).min(1, "At least one keyword is required"),
  excludeWords: z.array(z.string()).optional(),
});

// Get all keyword groups for the current user
router.get('/', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const groups = await storage.getKeywordGroups(req.user.id);
    res.json(groups);
  } catch (error) {
    console.error('Error fetching keyword groups:', error);
    res.status(500).json({ message: "Failed to fetch keyword groups" });
  }
});

// Export keyword groups
router.get('/export', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const groups = await storage.getKeywordGroups(req.user.id);

    // Format for export (remove IDs and user-specific data)
    const exportData = groups.map(group => ({
      name: group.name,
      keywords: group.keywords,
      excludeWords: group.excludeWords || [],
    }));

    // Set headers for file download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=keyword-groups.json');

    res.json(exportData);
  } catch (error) {
    console.error('Error exporting keyword groups:', error);
    res.status(500).json({ message: "Failed to export keyword groups" });
  }
});

// Get a specific keyword group
router.get('/:id', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const groupId = parseInt(req.params.id);
    const group = await storage.getKeywordGroup(groupId);

    if (!group) {
      return res.status(404).json({ message: "Keyword group not found" });
    }

    // Check if the group belongs to the current user
    if (group.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to access this group" });
    }

    res.json(group);
  } catch (error) {
    console.error('Error fetching keyword group:', error);
    res.status(500).json({ message: "Failed to fetch keyword group" });
  }
});

// Create a new keyword group
router.post('/', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    // Validate request body
    const validatedData = keywordGroupSchema.parse(req.body);

    // Create the keyword group
    const newGroup = await storage.createKeywordGroup(req.user.id, {
      name: validatedData.name,
      keywords: validatedData.keywords,
      excludeWords: validatedData.excludeWords || [],
    });

    res.status(201).json(newGroup);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Validation error", errors: error.errors });
    }

    console.error('Error creating keyword group:', error);
    res.status(500).json({ message: "Failed to create keyword group" });
  }
});

// Update a keyword group
router.put('/:id', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const groupId = parseInt(req.params.id);
    const group = await storage.getKeywordGroup(groupId);

    if (!group) {
      return res.status(404).json({ message: "Keyword group not found" });
    }

    // Check if the group belongs to the current user
    if (group.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to update this group" });
    }

    // Validate request body
    const validatedData = keywordGroupSchema.parse(req.body);

    // Update the keyword group
    const updatedGroup = await storage.updateKeywordGroup(groupId, {
      name: validatedData.name,
      keywords: validatedData.keywords,
      excludeWords: validatedData.excludeWords || [],
    });

    res.json(updatedGroup);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Validation error", errors: error.errors });
    }

    console.error('Error updating keyword group:', error);
    res.status(500).json({ message: "Failed to update keyword group" });
  }
});

// Delete a keyword group
router.delete('/:id', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const groupId = parseInt(req.params.id);
    const group = await storage.getKeywordGroup(groupId);

    if (!group) {
      return res.status(404).json({ message: "Keyword group not found" });
    }

    // Check if the group belongs to the current user
    if (group.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to delete this group" });
    }

    // Delete the keyword group
    await storage.deleteKeywordGroup(groupId);

    res.sendStatus(204);
  } catch (error) {
    console.error('Error deleting keyword group:', error);
    res.status(500).json({ message: "Failed to delete keyword group" });
  }
});

// Set active keyword group
router.post('/:id/activate', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const groupId = parseInt(req.params.id);

    // Check if the group exists and belongs to the user
    if (groupId !== 0) { // 0 means deactivate all groups
      const group = await storage.getKeywordGroup(groupId);

      if (!group) {
        return res.status(404).json({ message: "Keyword group not found" });
      }

      if (group.userId !== req.user.id) {
        return res.status(403).json({ message: "You don't have permission to activate this group" });
      }
    }

    // Set the active group (or null to deactivate)
    await storage.setActiveKeywordGroup(req.user.id, groupId === 0 ? null : groupId);

    // Get updated settings
    const settings = await storage.getSettings(req.user.id);

    res.json({ success: true, settings });
  } catch (error) {
    console.error('Error activating keyword group:', error);
    res.status(500).json({ message: "Failed to activate keyword group" });
  }
});

// Refresh videos for a specific keyword group
router.post('/:id/refresh', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const groupId = parseInt(req.params.id);
    const group = await storage.getKeywordGroup(groupId);

    if (!group) {
      return res.status(404).json({ message: "Keyword group not found" });
    }

    // Check if the group belongs to the current user
    if (group.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to refresh this group" });
    }

    // Import the request queue manager
    const { requestQueueManager, RequestType, RequestPriority } = await import('../services/request-queue-manager');

    // Add the group refresh request to the queue with a timeout
    const request = requestQueueManager.addRequest(
      RequestType.GROUP_REFRESH,
      RequestPriority.NORMAL,
      { groupId },
      req.user.id,
      60000 // 60 second timeout
    );

    // Return immediately with the request ID
    res.json({
      requestId: request.id,
      message: "Group refresh has been queued and will be processed in the background.",
      status: request.status,
      groupId
    });
  } catch (error) {
    console.error('Error queueing keyword group refresh:', error);
    res.status(500).json({ message: "Failed to queue group refresh" });
  }
});

// Get videos for a specific keyword group
router.get('/:id/videos', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const groupId = parseInt(req.params.id);
    const group = await storage.getKeywordGroup(groupId);

    if (!group) {
      return res.status(404).json({ message: "Keyword group not found" });
    }

    // Check if the group belongs to the current user
    if (group.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to access this group's videos" });
    }

    // Get videos for the group
    const videos = await storage.getVideosByGroup(groupId);

    res.json(videos);
  } catch (error) {
    console.error('Error fetching group videos:', error);
    res.status(500).json({ message: "Failed to fetch videos" });
  }
});

// Update keyword groups order
router.post('/reorder', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    // Validate request body
    const reorderSchema = z.array(z.object({
      id: z.number(),
      order: z.number().min(0)
    }));

    const validatedData = reorderSchema.parse(req.body);

    // Update the order
    await storage.updateKeywordGroupsOrder(req.user.id, validatedData);

    // Get the updated groups
    const groups = await storage.getKeywordGroups(req.user.id);

    res.json({
      success: true,
      message: "Keyword groups order updated",
      groups
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Validation error", errors: error.errors });
    }

    console.error('Error updating keyword groups order:', error);
    res.status(500).json({ message: "Failed to update keyword groups order" });
  }
});

// Import keyword groups
router.post('/import', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const importSchema = z.array(z.object({
      name: z.string().min(1, "Name is required"),
      keywords: z.array(z.string()).min(1, "At least one keyword is required"),
      excludeWords: z.array(z.string()).optional(),
    }));

    // Validate the imported data
    const validatedData = importSchema.parse(req.body);

    // Get existing groups for this user
    const existingGroups = await storage.getKeywordGroups(req.user.id);

    // Track statistics for the response
    const stats = {
      created: 0,
      updated: 0,
      total: validatedData.length
    };

    // Import each group
    const importedGroups = [];
    for (const group of validatedData) {
      // Check if a group with this name already exists
      const existingGroup = existingGroups.find(g => g.name.toLowerCase() === group.name.toLowerCase());

      let resultGroup;

      if (existingGroup) {
        // Update the existing group by merging keywords
        console.log(`Updating existing group: ${group.name}`);

        // Merge keywords (add new ones without removing existing ones)
        const mergedKeywords = [...new Set([...existingGroup.keywords, ...group.keywords])];

        // Merge exclude words
        const mergedExcludeWords = [...new Set([
          ...(existingGroup.excludeWords || []),
          ...(group.excludeWords || [])
        ])];

        console.log(`Merged keywords: ${mergedKeywords.length} (was ${existingGroup.keywords.length}, adding ${group.keywords.length})`);

        resultGroup = await storage.updateKeywordGroup(existingGroup.id, {
          name: group.name, // Keep the original name casing
          keywords: mergedKeywords,
          excludeWords: mergedExcludeWords,
        });
        stats.updated++;
      } else {
        // Create a new group
        console.log(`Creating new group: ${group.name}`);
        resultGroup = await storage.createKeywordGroup(req.user.id, {
          name: group.name,
          keywords: group.keywords,
          excludeWords: group.excludeWords || [],
        });
        stats.created++;
      }

      importedGroups.push(resultGroup);
    }

    res.status(200).json({
      message: `Successfully imported ${stats.total} keyword groups (${stats.created} created, ${stats.updated} updated with merged keywords)`,
      groups: importedGroups,
      stats
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Validation error", errors: error.errors });
    }

    console.error('Error importing keyword groups:', error);
    res.status(500).json({ message: "Failed to import keyword groups" });
  }
});

export default router;
