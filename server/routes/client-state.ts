import express from 'express';
import { ensureAuthenticated } from '../middleware/auth';

const router = express.Router();

// Map to track idle state of clients
// Key: userId, Value: { idle: boolean, lastUpdated: Date }
const clientIdleState = new Map<number, { idle: boolean, lastUpdated: Date }>();

// Endpoint to update client idle state
router.post('/idle', ensureAuthenticated, (req, res) => {
  const userId = req.user?.id;
  const { idle } = req.body;

  if (userId === undefined) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (typeof idle !== 'boolean') {
    return res.status(400).json({ error: 'Invalid idle state' });
  }

  // Update the client state
  clientIdleState.set(userId, {
    idle,
    lastUpdated: new Date()
  });

  console.log(`Client state updated for user ${userId}: idle=${idle}`);

  return res.status(200).json({ success: true });
});

// Function to check if a user's client is idle
export function isClientIdle(userId: number): boolean {
  const state = clientIdleState.get(userId);

  if (!state) {
    // If no state is recorded, assume not idle
    return false;
  }

  // Check if the state is stale (older than 5 minutes)
  const now = new Date();
  const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

  if (state.lastUpdated < fiveMinutesAgo) {
    // If the state is stale, remove it and assume not idle
    clientIdleState.delete(userId);
    return false;
  }

  return state.idle;
}

// Clean up stale client states periodically
setInterval(() => {
  const now = new Date();
  const tenMinutesAgo = new Date(now.getTime() - 10 * 60 * 1000);

  for (const [userId, state] of clientIdleState.entries()) {
    if (state.lastUpdated < tenMinutesAgo) {
      clientIdleState.delete(userId);
      console.log(`Removed stale client state for user ${userId}`);
    }
  }
}, 5 * 60 * 1000); // Run every 5 minutes

export default router;
