import { useState, useEffect, useCallback } from 'react';
import { YoutubeVideo } from '@shared/schema';
import { logger, LogLevel } from '@/lib/logger';

// Maximum number of videos to keep in cache to prevent memory issues
const MAX_CACHE_SIZE = 100;

/**
 * A hook that provides a stable cache of videos that persists across refreshes
 * This ensures that videos don't disappear during metadata refreshes
 * Optimized for memory usage by limiting cache size
 */
export function useCachedVideos() {
  // Store videos in local state
  const [cachedVideos, setCachedVideos] = useState<YoutubeVideo[]>([]);

  // Function to update the cache with new videos
  const updateCache = useCallback((newVideos: YoutubeVideo[]) => {
    if (!newVideos || newVideos.length === 0) return;

    logger.debug(`Updating video cache with ${newVideos.length} videos`);

    setCachedVideos(prevVideos => {
      // Create a map of existing videos by ID for quick lookup
      const existingVideosMap = new Map(prevVideos.map(v => [v.id, v]));

      // Create a new array with updated videos, preserving order
      const updatedVideos = [...prevVideos];

      // Track new videos to add
      const newVideosToAdd: YoutubeVideo[] = [];

      // Update existing videos with new data
      newVideos.forEach(newVideo => {
        const existingVideo = existingVideosMap.get(newVideo.id);

        if (existingVideo) {
          // Update existing video with new data
          const index = updatedVideos.findIndex(v => v.id === newVideo.id);
          if (index >= 0) {
            updatedVideos[index] = {
              ...existingVideo,
              ...newVideo,
              // Preserve VPH data for consistent sorting
              vph: newVideo.vph || existingVideo.vph
            };
          }
        } else {
          // Add new video to the list of videos to add
          newVideosToAdd.push(newVideo);
        }
      });

      // Add new videos to the end
      const combinedVideos = [...updatedVideos, ...newVideosToAdd];

      // Limit cache size to prevent memory issues
      // Keep the most recent videos (which are typically more important)
      if (combinedVideos.length > MAX_CACHE_SIZE) {
        logger.debug(`Trimming video cache from ${combinedVideos.length} to ${MAX_CACHE_SIZE} videos to reduce memory usage`);
        return combinedVideos.slice(combinedVideos.length - MAX_CACHE_SIZE);
      }

      return combinedVideos;
    });
  }, []);

  // Function to get videos with metadata updated from a source
  const getMergedVideos = useCallback((sourceVideos: YoutubeVideo[]) => {
    if (!sourceVideos || sourceVideos.length === 0) return cachedVideos;
    if (cachedVideos.length === 0) return sourceVideos;

    logger.debug(`Merging ${sourceVideos.length} source videos with ${cachedVideos.length} cached videos`);

    // Create a map of source videos by ID for quick lookup
    const sourceVideosMap = new Map(sourceVideos.map(v => [v.id, v]));

    // Create a new array with updated videos, preserving order of cached videos
    return cachedVideos.map(cachedVideo => {
      const sourceVideo = sourceVideosMap.get(cachedVideo.id);
      if (sourceVideo) {
        // Update cached video with source data
        return {
          ...cachedVideo,
          ...sourceVideo,
          // Preserve VPH data for consistent sorting
          vph: sourceVideo.vph || cachedVideo.vph
        };
      }
      return cachedVideo;
    });
  }, [cachedVideos]);

  // Function to clear the cache
  const clearCache = useCallback(() => {
    logger.debug('Clearing video cache');
    setCachedVideos([]);
  }, []);

  return {
    cachedVideos,
    updateCache,
    getMergedVideos,
    clearCache
  };
}
