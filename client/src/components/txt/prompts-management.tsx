import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  MessageSquare, 
  Plus, 
  Edit, 
  Trash2, 
  Save,
  Copy,
  Download,
  Upload,
  Star
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface PromptsManagementProps {
  prompts: any[];
  onPromptCreated: (prompt: any) => void;
  onPromptUpdated: (prompt: any) => void;
  onPromptDeleted: (promptId: number) => void;
}

const PROMPT_CATEGORIES = [
  'general',
  'script-writing',
  'content-analysis',
  'improvement',
  'summarization',
  'style-guide',
  'audience-engagement',
  'seo-optimization'
];

const DEFAULT_PROMPTS = [
  {
    name: 'Script Improvement',
    category: 'script-writing',
    content: 'Please analyze this video script and suggest improvements for better audience engagement, clarity, and flow. Focus on:\n1. Hook effectiveness\n2. Content structure\n3. Call-to-action placement\n4. Audience retention strategies\n\nScript:\n{transcript}'
  },
  {
    name: 'Content Summarization',
    category: 'summarization',
    content: 'Create a concise summary of this video transcript, highlighting the key points and main takeaways. Format it as bullet points for easy reading.\n\nTranscript:\n{transcript}'
  },
  {
    name: 'Audience Engagement Analysis',
    category: 'audience-engagement',
    content: 'Analyze this video script for audience engagement potential. Identify:\n1. Strong engagement moments\n2. Potential drop-off points\n3. Emotional triggers\n4. Interactive elements\n5. Suggestions for improvement\n\nScript:\n{transcript}'
  },
  {
    name: 'SEO Title & Description',
    category: 'seo-optimization',
    content: 'Based on this video transcript, generate:\n1. 5 SEO-optimized title options\n2. A compelling video description\n3. Relevant tags/keywords\n4. Thumbnail text suggestions\n\nTranscript:\n{transcript}'
  }
];

export function PromptsManagement({ 
  prompts, 
  onPromptCreated, 
  onPromptUpdated, 
  onPromptDeleted 
}: PromptsManagementProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<any>(null);
  const [name, setName] = useState('');
  const [content, setContent] = useState('');
  const [category, setCategory] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [promptToDelete, setPromptToDelete] = useState<any>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  const { toast } = useToast();

  // Filter prompts
  const filteredPrompts = prompts.filter(prompt => {
    const matchesCategory = selectedCategory === 'all' || prompt.category === selectedCategory;
    const matchesSearch = !searchQuery || 
      prompt.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prompt.content.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  // Group prompts by category
  const groupedPrompts = filteredPrompts.reduce((acc, prompt) => {
    const cat = prompt.category || 'general';
    if (!acc[cat]) {
      acc[cat] = [];
    }
    acc[cat].push(prompt);
    return acc;
  }, {} as Record<string, any[]>);

  const handleCreate = () => {
    setIsCreating(true);
    setEditingPrompt(null);
    setName('');
    setContent('');
    setCategory('general');
  };

  const handleEdit = (prompt: any) => {
    setEditingPrompt(prompt);
    setIsCreating(false);
    setName(prompt.name);
    setContent(prompt.content);
    setCategory(prompt.category || 'general');
  };

  const handleSave = async () => {
    if (!name.trim() || !content.trim()) {
      toast({
        title: "Error",
        description: "Name and content are required",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSaving(true);
      
      const data = {
        name: name.trim(),
        content: content.trim(),
        category: category
      };

      let response;
      if (isCreating) {
        response = await fetch('/api/txt-tab/prompts', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
      } else {
        response = await fetch(`/api/txt-tab/prompts/${editingPrompt.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
      }

      if (response.ok) {
        const savedPrompt = await response.json();
        
        if (isCreating) {
          onPromptCreated(savedPrompt);
        } else {
          onPromptUpdated(savedPrompt);
        }
        
        // Reset form
        setIsCreating(false);
        setEditingPrompt(null);
        setName('');
        setContent('');
        setCategory('general');
      } else {
        throw new Error('Failed to save prompt');
      }
    } catch (error) {
      console.error('Error saving prompt:', error);
      toast({
        title: "Error",
        description: "Failed to save prompt",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async (prompt: any) => {
    try {
      const response = await fetch(`/api/txt-tab/prompts/${prompt.id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        onPromptDeleted(prompt.id);
      } else {
        throw new Error('Failed to delete prompt');
      }
    } catch (error) {
      console.error('Error deleting prompt:', error);
      toast({
        title: "Error",
        description: "Failed to delete prompt",
        variant: "destructive",
      });
    } finally {
      setDeleteDialogOpen(false);
      setPromptToDelete(null);
    }
  };

  const copyPrompt = async (prompt: any) => {
    try {
      await navigator.clipboard.writeText(prompt.content);
      toast({
        title: "Success",
        description: "Prompt copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy prompt",
        variant: "destructive",
      });
    }
  };

  const createDefaultPrompts = async () => {
    try {
      for (const defaultPrompt of DEFAULT_PROMPTS) {
        const response = await fetch('/api/txt-tab/prompts', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(defaultPrompt)
        });
        
        if (response.ok) {
          const savedPrompt = await response.json();
          onPromptCreated(savedPrompt);
        }
      }
      
      toast({
        title: "Success",
        description: "Default prompts created successfully",
      });
    } catch (error) {
      console.error('Error creating default prompts:', error);
      toast({
        title: "Error",
        description: "Failed to create default prompts",
        variant: "destructive",
      });
    }
  };

  const exportPrompts = () => {
    const exportData = {
      version: '1.0',
      exported_at: new Date().toISOString(),
      prompts: prompts.map(p => ({
        name: p.name,
        content: p.content,
        category: p.category
      }))
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'txt-prompts-export.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getCategoryStats = () => {
    return prompts.reduce((acc, prompt) => {
      const cat = prompt.category || 'general';
      acc[cat] = (acc[cat] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  };

  const categoryStats = getCategoryStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Prompts Management
          </h3>
          <p className="text-sm text-muted-foreground">
            Create and manage prompts for ChatGPT processing
          </p>
        </div>
        <div className="flex items-center gap-2">
          {prompts.length === 0 && (
            <Button variant="outline" onClick={createDefaultPrompts}>
              <Star className="h-4 w-4 mr-2" />
              Add Defaults
            </Button>
          )}
          <Button variant="outline" onClick={exportPrompts} disabled={prompts.length === 0}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={handleCreate}>
            <Plus className="h-4 w-4 mr-2" />
            New Prompt
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="flex flex-wrap gap-2">
        <Badge variant="outline">
          Total: {prompts.length}
        </Badge>
        {Object.entries(categoryStats).map(([cat, count]) => (
          <Badge key={cat} variant="secondary">
            {cat}: {count}
          </Badge>
        ))}
      </div>

      {/* Search and Filter */}
      <div className="flex gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search prompts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {PROMPT_CATEGORIES.map(cat => (
              <SelectItem key={cat} value={cat}>
                {cat.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Prompt List */}
        <div className="space-y-4">
          <h4 className="font-medium">Prompts Library</h4>
          
          {prompts.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-muted-foreground mb-4">No prompts created yet</p>
                <Button onClick={createDefaultPrompts}>
                  <Star className="h-4 w-4 mr-2" />
                  Create Default Prompts
                </Button>
              </CardContent>
            </Card>
          ) : (
            <ScrollArea className="h-[500px]">
              <div className="space-y-4">
                {Object.entries(groupedPrompts).map(([cat, categoryPrompts]) => (
                  <div key={cat}>
                    <h5 className="font-medium text-sm mb-2 capitalize">
                      {cat.replace('-', ' ')} ({categoryPrompts.length})
                    </h5>
                    <div className="space-y-2">
                      {categoryPrompts.map((prompt) => (
                        <Card key={prompt.id} className="hover:bg-muted/50 transition-colors">
                          <CardContent className="p-4">
                            <div className="flex justify-between items-start">
                              <div className="flex-1 min-w-0">
                                <h6 className="font-medium text-sm mb-1">{prompt.name}</h6>
                                <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                                  {prompt.content.substring(0, 100)}
                                  {prompt.content.length > 100 ? '...' : ''}
                                </p>
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline" className="text-xs">
                                    {prompt.category}
                                  </Badge>
                                  {prompt.is_default && (
                                    <Badge variant="secondary" className="text-xs">
                                      Default
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-1 ml-4">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyPrompt(prompt)}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEdit(prompt)}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setPromptToDelete(prompt);
                                    setDeleteDialogOpen(true);
                                  }}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>

        {/* Editor */}
        <div>
          <h4 className="font-medium mb-4">
            {isCreating ? 'Create New Prompt' : editingPrompt ? 'Edit Prompt' : 'Prompt Editor'}
          </h4>
          
          {(isCreating || editingPrompt) ? (
            <Card>
              <CardContent className="pt-6 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="prompt-name">Name</Label>
                  <Input
                    id="prompt-name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Enter prompt name..."
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="prompt-category">Category</Label>
                  <Select value={category} onValueChange={setCategory}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {PROMPT_CATEGORIES.map(cat => (
                        <SelectItem key={cat} value={cat}>
                          {cat.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="prompt-content">Content</Label>
                  <Textarea
                    id="prompt-content"
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    placeholder="Enter prompt content... Use {transcript} as a placeholder for the transcript content."
                    className="min-h-[200px] font-mono text-sm"
                  />
                  <p className="text-xs text-muted-foreground">
                    Tip: Use {'{transcript}'} as a placeholder where you want the transcript content to be inserted.
                  </p>
                </div>

                <div className="flex gap-2">
                  <Button onClick={handleSave} disabled={isSaving}>
                    <Save className="h-4 w-4 mr-2" />
                    {isSaving ? 'Saving...' : 'Save'}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setIsCreating(false);
                      setEditingPrompt(null);
                      setName('');
                      setContent('');
                      setCategory('general');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="pt-6 text-center">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-muted-foreground mb-4">Select a prompt to edit or create a new one</p>
                <Button onClick={handleCreate}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Prompt
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Prompt</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{promptToDelete?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => promptToDelete && handleDelete(promptToDelete)}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
