/**
 * Ollama Routes
 *
 * This file contains routes for interacting with the Ollama LLM service.
 */

import express from 'express';
import { getAvailableOllamaModels } from '../services/ollama-service';
import { storage } from '../storage';
import { analyzeTranscriptWithOllama, convertOllamaAnalysisToAppFormat } from '../services/ollama-financial-analysis';
import { createFinancialAnalysisPrompt } from '../services/ollama-financial-analysis';

const router = express.Router();

// Test Ollama connection
router.get('/test-connection', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('🤖 Ollama API: Testing Ollama connection...');

    try {
      // Set a timeout for the fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch('http://localhost:11434/api/tags', {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`🤖 Ollama API: Server returned error: ${response.status}`);
        return res.status(500).json({
          message: "Failed to connect to Ollama server",
          status: response.status,
          statusText: response.statusText
        });
      }

      const data = await response.json();

      if (!data || !data.models || !Array.isArray(data.models)) {
        console.error('🤖 Ollama API: Invalid response format from Ollama:', data);
        return res.status(500).json({
          message: "Invalid response format from Ollama server",
          error: "Make sure Ollama is running correctly"
        });
      }

      const modelNames = data.models.map((model: any) => model.name);
      console.log(`🤖 Ollama API: Server is running with ${modelNames.length} models available: ${modelNames.join(', ')}`);

      return res.json({
        status: "success",
        message: "Ollama server is running",
        models: modelNames
      });
    } catch (fetchError) {
      if (fetchError.name === 'AbortError') {
        console.error('🤖 Ollama API: Fetch request timed out');
        return res.status(500).json({
          message: "Connection to Ollama server timed out",
          error: "Make sure Ollama is running on your machine at http://localhost:11434"
        });
      }

      console.error('🤖 Ollama API: Error fetching from Ollama server:', fetchError);
      return res.status(500).json({
        message: "Failed to connect to Ollama server",
        error: "Make sure Ollama is running on your machine at http://localhost:11434",
        details: fetchError.message
      });
    }
  } catch (error) {
    console.error('🤖 Ollama API: Error testing Ollama connection:', error);
    return res.status(500).json({
      message: "Failed to connect to Ollama server",
      error: error.message || "Unknown error"
    });
  }
});

// Get installed Ollama models
router.get('/models', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('🤖 Ollama API: Fetching installed models...');
    try {
      const models = await getAvailableOllamaModels();
      console.log(`🤖 Ollama API: Returning ${models.length} installed models to client`);
      res.json(models);
    } catch (modelError) {
      console.error('🤖 Ollama API: Error fetching Ollama models:', modelError);
      res.status(500).json({
        message: "Failed to fetch Ollama models",
        error: modelError.message || "Unknown error",
        hint: "Make sure Ollama is running on your machine at http://localhost:11434"
      });
    }
  } catch (error) {
    console.error('🤖 Ollama API: Unexpected error in Ollama models endpoint:', error);
    res.status(500).json({
      message: "Failed to fetch Ollama models",
      error: error.message || "Unknown error"
    });
  }
});



// Analyze a video transcript using Ollama LLM
router.get('/analyze-youtube-video/:videoId', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { videoId } = req.params;
    console.log(`🤖 Ollama API: Analyzing YouTube video ${videoId} with Ollama LLM`);

    // Get the video details
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      console.error(`🤖 Ollama API: Video ${videoId} not found`);
      return res.status(404).json({ message: 'Video not found' });
    }

    // Log the video's Ollama model information
    console.log(`🤖 Ollama API: Video ${videoId} has Ollama model: ${video.ollamaModelUsed || 'None'}`);

    // Get the current Ollama model from settings
    try {
      const settings = await storage.getSettings(req.user.id);
      if (settings && settings.ollamaModel) {
        console.log(`🤖 Ollama API: Current Ollama model from settings: ${settings.ollamaModel}`);
      } else {
        console.log(`🤖 Ollama API: No Ollama model found in settings`);
      }
    } catch (settingsError) {
      console.error('🤖 Ollama API: Error getting user settings for Ollama model:', settingsError);
    }

    if (!video.hasTranscription) {
      console.error(`🤖 Ollama API: Video ${videoId} does not have a transcription`);
      return res.status(400).json({ message: 'Video does not have a transcription' });
    }

    // The transcription is already in the video object
    const transcription = video.transcription;

    if (!transcription) {
      console.error(`🤖 Ollama API: Transcription for video ${videoId} not found`);
      return res.status(404).json({ message: 'Transcription not found' });
    }

    console.log(`🤖 Ollama API: Found transcription for video ${videoId}, length: ${transcription.length} characters`);

    // First, check if Ollama is running
    try {
      console.log('🤖 Ollama API: Testing Ollama connection before analysis...');
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const testResponse = await fetch('http://localhost:11434/api/tags', {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!testResponse.ok) {
        console.error(`🤖 Ollama API: Server returned error: ${testResponse.status}`);
        return res.status(500).json({
          message: "Failed to connect to Ollama server",
          error: "Make sure Ollama is running on your machine at http://localhost:11434"
        });
      }

      const data = await testResponse.json();
      const modelNames = data.models.map((model: any) => model.name);
      console.log(`🤖 Ollama API: Ollama server is running with ${modelNames.length} models available: ${modelNames.join(', ')}`);
    } catch (connectionError) {
      console.error('🤖 Ollama API: Error connecting to Ollama server:', connectionError);
      return res.status(500).json({
        message: "Failed to connect to Ollama server",
        error: "Make sure Ollama is running on your machine at http://localhost:11434",
        details: connectionError.message
      });
    }

    // Get the model from query parameters or settings
    let currentOllamaModel = 'llama3.2:latest'; // Default model

    // Check if a specific model was requested in the query parameters
    const requestedModel = req.query.model as string;
    if (requestedModel) {
      currentOllamaModel = requestedModel;
      if (!currentOllamaModel.includes(':')) {
        currentOllamaModel = `${currentOllamaModel}:latest`;
      }
      console.log(`🤖 Ollama API: Using requested model from query: ${currentOllamaModel}`);
    } else {
      // Otherwise get the model from settings
      try {
        const settings = await storage.getSettings(req.user.id);
        if (settings && settings.ollamaModel) {
          currentOllamaModel = settings.ollamaModel;
          if (!currentOllamaModel.includes(':')) {
            currentOllamaModel = `${currentOllamaModel}:latest`;
          }
          console.log(`🤖 Ollama API: Current Ollama model from settings: ${currentOllamaModel}`);
        } else {
          console.log(`🤖 Ollama API: No Ollama model found in settings, using default: ${currentOllamaModel}`);
        }
      } catch (settingsError) {
        console.error('🤖 Ollama API: Error getting user settings for Ollama model:', settingsError);
      }
    }

    // Analyze the transcription using Ollama LLM
    console.log(`🤖 Ollama API: Analyzing transcription for video ${videoId} with Ollama model: ${currentOllamaModel}`);
    try {
      // Pass the model to use for analysis
      const ollamaAnalysis = await analyzeTranscriptWithOllama(
        req.user.id,
        transcription,
        video.title,
        video.description,
        undefined, // No custom prompt
        currentOllamaModel // Pass the model to use
      );

      // Double-check that the model was set correctly
      if (ollamaAnalysis.modelUsed !== currentOllamaModel) {
        console.log(`🤖 Ollama API: Model mismatch! Expected ${currentOllamaModel}, got ${ollamaAnalysis.modelUsed}. Fixing...`);
        ollamaAnalysis.modelUsed = currentOllamaModel;
      }

      // Convert Ollama analysis to app format
      const analysis = convertOllamaAnalysisToAppFormat(ollamaAnalysis);

      console.log(`🤖 Ollama API: Analysis completed for video ${videoId} with score ${analysis.score}/100`);
      console.log(`🤖 Ollama API: Using model ${ollamaAnalysis.modelUsed} for analysis`);

      // Save the analysis to the database
      console.log(`🤖 Ollama API: Saving analysis with model ${analysis.ollamaModelUsed} to database for video ${videoId}`);
      console.log(`🤖 Ollama API: Raw data length: ${analysis.ollamaRawData ? analysis.ollamaRawData.length : 0} bytes`);

      // Ensure the raw data has the correct model name
      if (analysis.ollamaRawData) {
        try {
          // Parse the raw data
          const rawData = JSON.parse(analysis.ollamaRawData);

          // Check if the model name is 'Unknown' and fix it
          if (rawData.modelUsed === 'Unknown' && analysis.ollamaModelUsed) {
            console.log(`🤖 Ollama API: Fixing model name in raw data from 'Unknown' to '${analysis.ollamaModelUsed}'`);
            rawData.modelUsed = analysis.ollamaModelUsed;

            // Update the raw data
            analysis.ollamaRawData = JSON.stringify(rawData);
          }
        } catch (error) {
          console.error('🤖 Ollama API: Error fixing model name in raw data:', error);
        }
      }

      await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
        financialScore: analysis.score,
        financialCategory: analysis.category,
        financialAmount: analysis.amount,
        financialTimeline: analysis.timeline,
        financialRecipients: analysis.recipients,
        financialSteps: analysis.steps,
        financialViralPotential: analysis.viralPotential,
        financialSkepticism: analysis.skepticism,
        financialAnalysis: analysis.analysis,
        financialTimestamps: '',
        hasFinancialAnalysis: true,
        ollamaBenefitAmounts: analysis.ollamaBenefitAmounts,
        ollamaExpectedArrivalDate: analysis.ollamaExpectedArrivalDate,
        ollamaEligiblePeople: analysis.ollamaEligiblePeople,
        ollamaProofOrSource: analysis.ollamaProofOrSource,
        ollamaActionsToClaim: analysis.ollamaActionsToClaim,
        ollamaPriorityTag: analysis.ollamaPriorityTag,
        ollamaReasonForPriority: analysis.ollamaReasonForPriority,
        ollamaViralPotential: analysis.ollamaViralPotential,
        ollamaModelUsed: analysis.ollamaModelUsed,
        ollamaPrompt: analysis.ollamaPrompt,
        ollamaSystemPrompt: analysis.ollamaSystemPrompt,
        ollamaPromptName: analysis.ollamaPromptName,
        ollamaRawData: analysis.ollamaRawData
      });

      console.log(`🤖 Ollama API: Analysis saved to database for video ${videoId}`);
      res.json({ success: true, videoId, analysis, cached: false });
    } catch (ollamaError) {
      console.error('🤖 Ollama API: Error in Ollama analysis:', ollamaError);
      // If there's an error with Ollama, pass it through to the client
      const errorMessage = ollamaError instanceof Error ? ollamaError.message : 'Unknown Ollama error';
      return res.status(500).json({
        message: errorMessage,
        hint: "Make sure Ollama is running on your machine at http://localhost:11434"
      });
    }
  } catch (error) {
    console.error('🤖 Ollama API: Error analyzing video with Ollama:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to analyze video with Ollama: ${errorMessage}` });
  }
});

// Analyze all videos in a channel using Ollama LLM
router.post('/analyze-channel/:channelId', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { channelId } = req.params;
    console.log(`🤖 Ollama API: Analyzing all videos in channel ${channelId} with Ollama LLM`);

    // Get the channel details
    const channel = await storage.getYoutubeChannel(parseInt(channelId));

    if (!channel) {
      console.error(`🤖 Ollama API: Channel ${channelId} not found`);
      return res.status(404).json({ message: 'Channel not found' });
    }

    // Get all videos for this channel
    const videos = await storage.getYoutubeChannelVideos(parseInt(channelId));
    console.log(`🤖 Ollama API: Found ${videos.length} videos for channel ${channel.channelTitle}`);

    // Get the current Ollama model from settings
    let currentOllamaModel = 'llama3.2:latest'; // Default model
    try {
      const settings = await storage.getSettings(req.user.id);
      if (settings && settings.ollamaModel) {
        currentOllamaModel = settings.ollamaModel;
        if (!currentOllamaModel.includes(':')) {
          currentOllamaModel = `${currentOllamaModel}:latest`;
        }
        console.log(`🤖 Ollama API: Current Ollama model from settings: ${currentOllamaModel}`);
      } else {
        console.log(`🤖 Ollama API: No Ollama model found in settings, using default: ${currentOllamaModel}`);
      }
    } catch (settingsError) {
      console.error('🤖 Ollama API: Error getting user settings for Ollama model:', settingsError);
    }

    // Filter videos that have transcriptions but no Ollama analysis
    const videosToAnalyze = videos.filter(video =>
      video.hasTranscription &&
      (!video.ollamaModelUsed || !video.ollamaPriorityTag || video.ollamaModelUsed !== currentOllamaModel)
    );

    console.log(`🤖 Ollama API: Found ${videosToAnalyze.length} videos with transcriptions but no Ollama analysis or wrong model`);

    // Log videos that need reanalysis due to model mismatch
    const reanalysisVideos = videos.filter(video =>
      video.hasTranscription && video.ollamaModelUsed && video.ollamaModelUsed !== currentOllamaModel
    );
    if (reanalysisVideos.length > 0) {
      console.log(`🤖 Ollama API: ${reanalysisVideos.length} videos need reanalysis due to model mismatch:`);
      reanalysisVideos.forEach(video => {
        console.log(`- ${video.id}: Current model: ${video.ollamaModelUsed}, Target model: ${currentOllamaModel}`);
      });
    }

    if (videosToAnalyze.length === 0) {
      return res.json({
        success: true,
        message: 'No videos found that need Ollama analysis',
        totalVideos: videos.length,
        analyzedVideos: 0
      });
    }

    // First, check if Ollama is running
    try {
      console.log('🤖 Ollama API: Testing Ollama connection before batch analysis...');
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const testResponse = await fetch('http://localhost:11434/api/tags', {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!testResponse.ok) {
        console.error(`🤖 Ollama API: Server returned error: ${testResponse.status}`);
        return res.status(500).json({
          message: "Failed to connect to Ollama server",
          error: "Make sure Ollama is running on your machine at http://localhost:11434"
        });
      }

      const data = await testResponse.json();
      const modelNames = data.models.map((model: any) => model.name);
      console.log(`🤖 Ollama API: Ollama server is running with ${modelNames.length} models available: ${modelNames.join(', ')}`);
    } catch (connectionError) {
      console.error('🤖 Ollama API: Error connecting to Ollama server:', connectionError);
      return res.status(500).json({
        message: "Failed to connect to Ollama server",
        error: "Make sure Ollama is running on your machine at http://localhost:11434",
        details: connectionError.message
      });
    }

    // Start the analysis process in the background
    // We'll return a response immediately and continue processing
    res.json({
      success: true,
      message: `Started Ollama analysis for ${videosToAnalyze.length} videos`,
      totalVideos: videos.length,
      videosToAnalyze: videosToAnalyze.length
    });

    // Process videos one by one
    let analyzedCount = 0;
    let errorCount = 0;

    for (const video of videosToAnalyze) {
      try {
        console.log(`🤖 Ollama API: Analyzing video ${video.id} - ${video.title}`);

        // Skip videos without transcriptions
        if (!video.hasTranscription || !video.transcription) {
          console.log(`🤖 Ollama API: Skipping video ${video.id} - no transcription available`);
          continue;
        }

        // Analyze the transcription using Ollama LLM
        console.log(`🤖 Ollama API: Analyzing video ${video.id} with model ${currentOllamaModel}`);
        const ollamaAnalysis = await analyzeTranscriptWithOllama(
          req.user.id,
          video.transcription,
          video.title,
          video.description || ''
        );

        // Double-check that the model was set correctly
        if (ollamaAnalysis.modelUsed !== currentOllamaModel) {
          console.log(`🤖 Ollama API: Model mismatch! Expected ${currentOllamaModel}, got ${ollamaAnalysis.modelUsed}. Fixing...`);
          ollamaAnalysis.modelUsed = currentOllamaModel;
        }

        // Convert Ollama analysis to app format
        const analysis = convertOllamaAnalysisToAppFormat(ollamaAnalysis);

        console.log(`🤖 Ollama API: Analysis completed for video ${video.id} with score ${analysis.score}/100`);

        // Save the analysis to the database
        console.log(`🤖 Ollama API: Saving batch analysis with model ${analysis.ollamaModelUsed} for video ${video.id}`);
        console.log(`🤖 Ollama API: Raw data length: ${analysis.ollamaRawData ? analysis.ollamaRawData.length : 0} bytes`);

        // Ensure the raw data has the correct model name
        if (analysis.ollamaRawData) {
          try {
            // Parse the raw data
            const rawData = JSON.parse(analysis.ollamaRawData);

            // Check if the model name is 'Unknown' and fix it
            if (rawData.modelUsed === 'Unknown' && analysis.ollamaModelUsed) {
              console.log(`🤖 Ollama API: Fixing model name in raw data from 'Unknown' to '${analysis.ollamaModelUsed}'`);
              rawData.modelUsed = analysis.ollamaModelUsed;

              // Update the raw data
              analysis.ollamaRawData = JSON.stringify(rawData);
            }
          } catch (error) {
            console.error('🤖 Ollama API: Error fixing model name in raw data:', error);
          }
        }

        await storage.updateYoutubeVideoFinancialAnalysis(video.id, {
          financialScore: analysis.score,
          financialCategory: analysis.category,
          financialAmount: analysis.amount,
          financialTimeline: analysis.timeline,
          financialRecipients: analysis.recipients,
          financialSteps: analysis.steps,
          financialViralPotential: analysis.viralPotential,
          financialSkepticism: analysis.skepticism,
          financialAnalysis: analysis.analysis,
          financialTimestamps: '',
          hasFinancialAnalysis: true,
          ollamaBenefitAmounts: analysis.ollamaBenefitAmounts,
          ollamaExpectedArrivalDate: analysis.ollamaExpectedArrivalDate,
          ollamaEligiblePeople: analysis.ollamaEligiblePeople,
          ollamaProofOrSource: analysis.ollamaProofOrSource,
          ollamaActionsToClaim: analysis.ollamaActionsToClaim,
          ollamaPriorityTag: analysis.ollamaPriorityTag,
          ollamaReasonForPriority: analysis.ollamaReasonForPriority,
          ollamaViralPotential: analysis.ollamaViralPotential,
          ollamaModelUsed: analysis.ollamaModelUsed,
          ollamaPrompt: analysis.ollamaPrompt,
          ollamaSystemPrompt: analysis.ollamaSystemPrompt,
          ollamaPromptName: analysis.ollamaPromptName,
          ollamaRawData: analysis.ollamaRawData
        });

        console.log(`🤖 Ollama API: Analysis saved to database for video ${video.id}`);
        analyzedCount++;
      } catch (error) {
        console.error(`🤖 Ollama API: Error analyzing video ${video.id}:`, error);
        errorCount++;
        // Continue with the next video
      }
    }

    console.log(`🤖 Ollama API: Batch analysis completed. Analyzed ${analyzedCount} videos with ${errorCount} errors`);
  } catch (error) {
    console.error('🤖 Ollama API: Error in batch analysis:', error);
    // No response here since we already sent a response
  }
});

// Debug endpoint to see what would be sent to Ollama
router.get('/debug-prompt/:videoId', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { videoId } = req.params;
    console.log(`🤖 Ollama API: Debugging prompt for video ${videoId}`);

    // Get the video details
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      console.error(`🤖 Ollama API: Video ${videoId} not found`);
      return res.status(404).json({ message: 'Video not found' });
    }

    if (!video.hasTranscription) {
      console.error(`🤖 Ollama API: Video ${videoId} does not have a transcription`);
      return res.status(400).json({ message: 'Video does not have a transcription' });
    }

    // The transcription is already in the video object
    const transcription = video.transcription;

    if (!transcription) {
      console.error(`🤖 Ollama API: Transcription for video ${videoId} not found`);
      return res.status(404).json({ message: 'Transcription not found' });
    }

    // Get user settings to check for custom prompt
    const userSettings = await storage.getSettings(req.user.id);
    let userCustomPrompt = userSettings?.ollamaAnalysisPrompt;
    let promptName = "Default Prompt";

    // If user has a selected prompt ID, use that prompt instead
    if (userSettings?.selectedPromptId) {
      try {
        const selectedPrompt = await storage.getOllamaPrompt(userSettings.selectedPromptId);
        if (selectedPrompt) {
          console.log(`🤖 Ollama API: Using selected prompt: ${selectedPrompt.name}`);
          userCustomPrompt = selectedPrompt.promptText;
          promptName = selectedPrompt.name;
        }
      } catch (error) {
        console.error('🤖 Ollama API: Error getting selected prompt:', error);
      }
    }

    // Create the analysis prompt
    const prompt = createFinancialAnalysisPrompt(
      transcription,
      video.title,
      video.description,
      userCustomPrompt
    );

    // Return the prompt and other debug info
    res.json({
      videoId,
      title: video.title,
      promptName,
      transcriptLength: transcription.length,
      promptLength: prompt.length,
      prompt: prompt,
      transcriptPlaceholderReplaced: !prompt.includes('[Paste transcript here]'),
      systemPrompt: 'You are a financial benefit analyzer specialized in identifying financial benefits mentioned in YouTube video transcripts.\nYour task is to extract specific financial benefit information and classify the content based on certainty and value.\nFocus only on financial benefits that viewers might receive (like stimulus checks, tax credits, government payments, etc.).\nIgnore general financial advice or market analysis that doesn\'t involve direct benefits to individuals.'
    });
  } catch (error) {
    console.error('🤖 Ollama API: Error debugging prompt:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to debug prompt: ${errorMessage}` });
  }
});

export default router;
