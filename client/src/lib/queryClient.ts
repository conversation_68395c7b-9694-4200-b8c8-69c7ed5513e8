import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

import { networkManager } from './network-manager';

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
  options?: RequestInit
): Promise<Response> {
  // Check if network is available before making the request
  if (!networkManager.isNetworkAvailable() && !url.includes('/ping')) {
    // If this is not a ping request and network is unavailable, throw an error
    const error = new Error('Network connection unavailable');
    error.name = 'NetworkError';
    throw error;
  }

  // Merge default options with provided options
  const fetchOptions: RequestInit = {
    method,
    headers: data ? { "Content-Type": "application/json" } : {},
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
    ...options,
  };

  // If options contains headers, merge them with the default headers
  if (options?.headers) {
    fetchOptions.headers = {
      ...(fetchOptions.headers as Record<string, string>),
      ...(options.headers as Record<string, string>),
    };
  }

  // Log the request for debugging
  console.log(`API Request: ${method} ${url}`);

  try {
    const res = await fetch(url, fetchOptions);

    // Check if we should skip error handling (useful for custom error handling)
    if (!options?.signal?.aborted) {
      await throwIfResNotOk(res);
    }

    // Notify network manager of successful request
    networkManager.handleRequestSuccess();

    return res;
  } catch (error) {
    // Notify network manager of failed request
    networkManager.handleRequestFailure(error);

    // Rethrow the error
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const res = await fetch(queryKey[0] as string, {
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: 300000, // Cache data for 5 minutes to reduce API calls (increased from 1 minute)
      retry: (failureCount, error) => {
        // Don't retry if network is unavailable
        if (error && (error.name === 'NetworkError' || !networkManager.isNetworkAvailable())) {
          return false;
        }

        // Only retry network errors up to 2 times with increasing delay
        if (failureCount < 2 && error && typeof error === 'object') {
          const errorString = String(error).toLowerCase();
          if (
            errorString.includes('network') ||
            errorString.includes('fetch') ||
            errorString.includes('connection')
          ) {
            return true;
          }
        }

        return false;
      },
      retryDelay: (attemptIndex) => {
        // Use network manager's backoff delay or calculate based on attempt
        const networkDelay = networkManager.getRetryDelay();
        if (networkDelay > 0) {
          return networkDelay;
        }

        // Default exponential backoff: 1s, 3s
        return Math.min(1000 * (2 ** attemptIndex), 30000);
      },
      // Add garbage collection time to clean up unused queries
      gcTime: 10 * 60 * 1000, // 10 minutes (increased from 5 minutes)
      // Limit the number of pages kept in memory for infinite queries
      keepPreviousData: false, // Don't keep previous data when query changes
    },
    mutations: {
      retry: (failureCount, error) => {
        // Don't retry if network is unavailable
        if (error && (error.name === 'NetworkError' || !networkManager.isNetworkAvailable())) {
          return false;
        }

        // Only retry network errors once
        if (failureCount < 1 && error && typeof error === 'object') {
          const errorString = String(error).toLowerCase();
          if (
            errorString.includes('network') ||
            errorString.includes('fetch') ||
            errorString.includes('connection')
          ) {
            return true;
          }
        }

        return false;
      },
      retryDelay: networkManager.getRetryDelay(),
    },
  },
});
