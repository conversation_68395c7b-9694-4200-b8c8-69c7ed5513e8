import { Router } from 'express';
import axios from 'axios';
import * as cheerio from 'cheerio';
import { IStorage } from '../storage';
import { Video } from '@shared/schema';
import { v4 as uuidv4 } from 'uuid';
import { fetchVideoViewCount } from '../utils/youtube-utils';

const router = Router();
let storage: IStorage;

// Simple in-memory cache for search results
interface CacheEntry {
  videos: Video[];
  timestamp: number;
}

const searchCache: Record<string, CacheEntry> = {};
const CACHE_TTL = 2 * 60 * 1000; // 2 minutes for more accurate data

// Rate limiting - per URL basis, not global
const lastRequestTime: Record<string, number> = {};
const MIN_REQUEST_INTERVAL = 15000; // Reduced to 15 seconds between requests to the same URL
const activeRequests = new Map<string, Promise<any>>(); // Track active requests to prevent duplicates

export function setupYoutubeSearchRoutes(storageInstance: IStorage) {
  storage = storageInstance;
  return router;
}

// Process YouTube search results
router.post('/', async (req, res) => {
  // Temporarily bypass authentication for testing
  // if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    // Parse the request body if it's a string
    let body = req.body;
    if (typeof body === 'string') {
      try {
        body = JSON.parse(body);
      } catch (e) {
        // Silent error - no need to log
      }
    }

    // Extract searchUrl(s) from the body
    let searchUrls: string[] = [];

    if (body && typeof body === 'object') {
      if ('searchUrl' in body) {
        // Single URL case
        if (typeof body.searchUrl === 'string') {
          searchUrls = [body.searchUrl];
        }
        // Multiple URLs case (for ALL option)
        else if (Array.isArray(body.searchUrl)) {
          searchUrls = body.searchUrl.filter(url => typeof url === 'string');
        }
      } else if (body.body && typeof body.body === 'string') {
        // Try to parse the body.body if it exists
        try {
          const parsedBody = JSON.parse(body.body);
          if (typeof parsedBody.searchUrl === 'string') {
            searchUrls = [parsedBody.searchUrl];
          } else if (Array.isArray(parsedBody.searchUrl)) {
            searchUrls = parsedBody.searchUrl.filter(url => typeof url === 'string');
          }
        } catch (e) {
          // Silent error - no need to log
        }
      }
    }

    // If no searchUrls were found, use a hardcoded one for testing
    if (searchUrls.length === 0) {
      searchUrls = ["https://www.youtube.com/results?search_query=social+security+%7C+ssi+%7C+ssdi+%7C+increase+%7C+stimulus+%7C+irs+%7C+rebate+%7C+refund+%7C+ebt+%7C+low+income&sp=CAMSAkAB"];
    }

    // Validate that these are YouTube search URLs
    const validUrls = searchUrls.filter(url => url.includes('youtube.com/results'));
    if (validUrls.length === 0) {
      return res.status(400).json({ message: "No valid YouTube search URLs provided" });
    }

    // If we're dealing with a single URL, use the original logic
    if (validUrls.length === 1) {
      const searchUrl = validUrls[0];
      const forceRefresh = body?.forceRefresh === true;
      const userId = req.user?.id || 1; // Default to user 1 for now
      const now = Date.now(); // Define now at the top of the single URL case

      console.log(`YouTube Search API: Processing single URL: ${searchUrl} (forceRefresh: ${forceRefresh})`);

      // Check database cache first (unless forcing refresh)
      if (!forceRefresh) {
        const cachedVideos = await storage.getYtrSearchResults(userId, searchUrl, 5); // 5 minutes cache
        if (cachedVideos && cachedVideos.length > 0) {
          console.log(`YouTube Search API: Using database cached results for ${searchUrl} with ${cachedVideos.length} videos`);

          // Apply live filter to cached results if needed
          let filteredVideos = cachedVideos;
          if (searchUrl.includes('sp=CAMSAkAB')) {
            console.log(`YouTube Search API: Live filter detected in cached URL, filtering cached results`);
            const originalCount = filteredVideos.length;
            filteredVideos = filteredVideos.filter(video => video.isLive);
            console.log(`YouTube Search API: Filtered ${originalCount} cached videos to ${filteredVideos.length} live videos (cached)`);
          }

          return res.json({ videos: filteredVideos });
        }

        // Fallback to in-memory cache if no database cache
        if (searchCache[searchUrl] && (now - searchCache[searchUrl].timestamp) < CACHE_TTL) {
          console.log(`YouTube Search API: Using in-memory cached results for ${searchUrl}`);
          return res.json({ videos: searchCache[searchUrl].videos });
        }
      }

      // Apply rate limiting
      const lastRequest = lastRequestTime[searchUrl] || 0;
      const timeSinceLastRequest = now - lastRequest;

      if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
        // If we have cached results, return them even if they're expired
        if (searchCache[searchUrl]) {
          let cachedVideos = searchCache[searchUrl].videos;

          // Apply live filter to cached results if needed
          if (searchUrl.includes('sp=CAMSAkAB')) {
            console.log(`YouTube Search API: Live filter detected in cached URL (rate limited), filtering cached results`);
            const originalCount = cachedVideos.length;
            cachedVideos = cachedVideos.filter(video => video.isLive);
            console.log(`YouTube Search API: Filtered ${originalCount} cached videos to ${cachedVideos.length} live videos (rate limited)`);
          }

          return res.json({ videos: cachedVideos });
        }

        // Check if there's an active request for this URL
        if (activeRequests.has(searchUrl)) {
          console.log(`YouTube Search API: Request already in progress for ${searchUrl}, waiting for completion`);
          try {
            const result = await activeRequests.get(searchUrl);
            return res.json({ videos: result });
          } catch (error) {
            console.error(`YouTube Search API: Error waiting for active request:`, error);
            // Fall through to make a new request
          }
        }

        // For YTR requests, don't block - proceed with request but add small delay
        console.log(`YouTube Search API: Rate limited for ${searchUrl}, but proceeding with request (non-blocking for YTR)`);
        // Add a small delay to be respectful, but don't block the entire request
        await new Promise(resolve => setTimeout(resolve, 1000)); // Only 1 second delay
      }

      // Create a promise for this request to track active requests
      const requestPromise = (async () => {
        try {
          // Update last request time
          lastRequestTime[searchUrl] = Date.now();

          return await performYouTubeSearch(searchUrl, req.body?.resultsLimit || 25);
        } finally {
          // Remove from active requests when done
          activeRequests.delete(searchUrl);
        }
      })();

      // Track this active request
      activeRequests.set(searchUrl, requestPromise);

      const videos = await requestPromise;

      if (videos.length > 0) {
        // Check if real-time updates are requested (optional parameter)
        const enableRealTimeUpdates = req.body?.enableRealTimeUpdates === true;

        let finalVideos = videos;
        if (enableRealTimeUpdates) {
          console.log('YouTube Search API: Real-time updates requested, fetching accurate view counts');
          finalVideos = await updateVideosWithRealTimeData(videos);
        } else {
          console.log('YouTube Search API: Using search result view counts (faster, may be slightly outdated)');
        }

        // Save to database
        await storage.saveYtrSearchResults(userId, searchUrl, finalVideos);

        // Update in-memory cache for backward compatibility
        searchCache[searchUrl] = {
          videos: finalVideos,
          timestamp: Date.now()
        };

        return res.json({ videos: finalVideos });
      }

      return res.json({ videos });
    }
    // Multiple URLs case (ALL option)
    else {
      const forceRefresh = body?.forceRefresh === true;
      const userId = req.user?.id || 1; // Default to user 1 for now

      console.log(`YouTube Search API: Processing ${validUrls.length} URLs for combined search (forceRefresh: ${forceRefresh})`);
      const now = Date.now();
      const allVideos: Video[] = [];
      const combinedCacheKey = `combined_${validUrls.sort().join('_')}`;

      // Check database cache first (unless forcing refresh)
      if (!forceRefresh) {
        // For multiple URLs, check if we have cached results for each URL
        let allCachedVideos: any[] = [];
        let allUrlsCached = true;

        for (const searchUrl of validUrls) {
          const cachedVideos = await storage.getYtrSearchResults(userId, searchUrl, 5); // 5 minutes cache
          if (cachedVideos && cachedVideos.length > 0) {
            allCachedVideos.push(...cachedVideos);
          } else {
            allUrlsCached = false;
            break;
          }
        }

        if (allUrlsCached && allCachedVideos.length > 0) {
          console.log(`YouTube Search API: Using database cached results for combined search with ${allCachedVideos.length} videos`);
          // Remove duplicates
          const uniqueVideos = removeDuplicateVideos(allCachedVideos);
          return res.json({ videos: uniqueVideos });
        }

        // Fallback to in-memory cache if no database cache
        if (searchCache[combinedCacheKey] && (now - searchCache[combinedCacheKey].timestamp) < CACHE_TTL) {
          console.log(`YouTube Search API: Using in-memory cached combined results with ${searchCache[combinedCacheKey].videos.length} videos`);
          return res.json({ videos: searchCache[combinedCacheKey].videos });
        }
      }

      // Process each URL with rate limiting
      for (const searchUrl of validUrls) {
        console.log(`YouTube Search API: Processing URL: ${searchUrl}`);

        // Check if we have a cache for this individual URL
        if (searchCache[searchUrl] && (now - searchCache[searchUrl].timestamp) < CACHE_TTL) {
          console.log(`YouTube Search API: Using cached results for ${searchUrl} with ${searchCache[searchUrl].videos.length} videos`);
          let cachedVideos = searchCache[searchUrl].videos;

          // Apply live filter to cached results if needed
          if (searchUrl.includes('sp=CAMSAkAB')) {
            console.log(`YouTube Search API: Live filter detected in cached URL (multi), filtering cached results`);
            const originalCount = cachedVideos.length;
            cachedVideos = cachedVideos.filter(video => video.isLive);
            console.log(`YouTube Search API: Filtered ${originalCount} cached videos to ${cachedVideos.length} live videos (multi cached)`);
          }

          allVideos.push(...cachedVideos);
          continue;
        }

        // Apply rate limiting
        const lastRequest = lastRequestTime[searchUrl] || 0;
        const timeSinceLastRequest = now - lastRequest;

        if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
          // If we have cached results for this URL, use them even if expired
          if (searchCache[searchUrl]) {
            console.log(`YouTube Search API: Using expired cached results for ${searchUrl} due to rate limiting`);
            let cachedVideos = searchCache[searchUrl].videos;

            // Apply live filter to cached results if needed
            if (searchUrl.includes('sp=CAMSAkAB')) {
              console.log(`YouTube Search API: Live filter detected in expired cached URL (multi), filtering cached results`);
              const originalCount = cachedVideos.length;
              cachedVideos = cachedVideos.filter(video => video.isLive);
              console.log(`YouTube Search API: Filtered ${originalCount} cached videos to ${cachedVideos.length} live videos (multi expired)`);
            }

            allVideos.push(...cachedVideos);
            continue;
          }

          console.log(`YouTube Search API: Rate limited for ${searchUrl} in multi-URL request, proceeding with small delay`);
          // Add small delay instead of blocking
          await new Promise(resolve => setTimeout(resolve, 1000)); // Only 1 second delay
        }

        try {
          // Update last request time
          lastRequestTime[searchUrl] = Date.now();
          console.log(`YouTube Search API: Fetching ${searchUrl}`);

          // Fetch the search results page
          console.log(`YouTube Search API: Fetching URL with filters (multi): ${searchUrl}`);
          const response = await axios.get(searchUrl, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Accept-Language': 'en-US,en;q=0.9',
              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
              'Accept-Encoding': 'gzip, deflate, br',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            },
            timeout: 15000 // 15 second timeout
          });

          // Parse the HTML to extract video information
          const html = response.data;
          console.log(`YouTube Search API: Response length (multi): ${html.length} characters`);

          // Check if the response contains filter-specific content
          const hasLiveContent = html.includes('isLiveNow') || html.includes('"status":"LIVE"') || html.includes('LIVE');
          console.log(`YouTube Search API: Response contains live content indicators (multi): ${hasLiveContent}`);

          // Extract videos with pagination support
          const resultsLimit = req.body?.resultsLimit || 25;
          let videos = await extractVideosWithPagination(html, searchUrl, resultsLimit);
          console.log(`YouTube Search API: Extracted ${videos.length} videos from ${searchUrl} (requested: ${resultsLimit}) for multi-URL`);

          // Filter for live videos if the URL contains live filter parameter
          if (searchUrl.includes('sp=CAMSAkAB')) {
            console.log(`YouTube Search API: Live filter detected in URL (multi), filtering for live videos only`);
            const originalCount = videos.length;
            videos = videos.filter(video => video.isLive);
            console.log(`YouTube Search API: Filtered ${originalCount} videos to ${videos.length} live videos (multi)`);
          }

          if (videos.length > 0) {
            // Update cache for this individual URL
            searchCache[searchUrl] = {
              videos,
              timestamp: Date.now()
            };

            // Add to combined results
            allVideos.push(...videos);
            console.log(`YouTube Search API: Total videos in combined results so far: ${allVideos.length}`);
          }
        } catch (error) {
          console.error(`YouTube Search API: Error fetching URL ${searchUrl}:`, error);
          // If we have a cache for this URL, use it despite the error
          if (searchCache[searchUrl]) {
            console.log(`YouTube Search API: Using cached results for ${searchUrl} after fetch error`);
            let cachedVideos = searchCache[searchUrl].videos;

            // Apply live filter to cached results if needed
            if (searchUrl.includes('sp=CAMSAkAB')) {
              console.log(`YouTube Search API: Live filter detected in error cached URL (multi), filtering cached results`);
              const originalCount = cachedVideos.length;
              cachedVideos = cachedVideos.filter(video => video.isLive);
              console.log(`YouTube Search API: Filtered ${originalCount} cached videos to ${cachedVideos.length} live videos (multi error)`);
            }

            allVideos.push(...cachedVideos);
          }
        }
      }

      console.log(`YouTube Search API: Total videos before deduplication: ${allVideos.length}`);

      // Remove duplicate videos (same video ID)
      const uniqueVideos = removeDuplicateVideos(allVideos);
      console.log(`YouTube Search API: Total unique videos after deduplication: ${uniqueVideos.length}`);

      // Check if real-time updates are requested (optional parameter)
      const enableRealTimeUpdates = req.body?.enableRealTimeUpdates === true;

      let finalUniqueVideos = uniqueVideos;
      if (enableRealTimeUpdates) {
        console.log('YouTube Search API: Real-time updates requested for combined results, fetching accurate view counts');
        finalUniqueVideos = await updateVideosWithRealTimeData(uniqueVideos);
      } else {
        console.log('YouTube Search API: Using search result view counts for combined results (faster, may be slightly outdated)');
      }

      // Don't sort here - preserve original YouTube search order
      // Let the client decide the sorting order
      // finalUniqueVideos.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());

      // Save each URL's results to database for future caching
      for (const searchUrl of validUrls) {
        const urlVideos = allVideos.filter(video =>
          // This is a simplified approach - in a real implementation, you'd track which URL each video came from
          true // For now, save all videos for each URL (will be deduplicated by the database)
        );
        if (urlVideos.length > 0) {
          await storage.saveYtrSearchResults(userId, searchUrl, urlVideos);
        }
      }

      // Cache the combined results in memory for backward compatibility
      searchCache[combinedCacheKey] = {
        videos: finalUniqueVideos,
        timestamp: Date.now()
      };

      // Log the number of videos from each channel for debugging
      const channelCounts: Record<string, number> = {};
      uniqueVideos.forEach(video => {
        const channel = video.channelTitle || 'Unknown';
        channelCounts[channel] = (channelCounts[channel] || 0) + 1;
      });
      console.log('YouTube Search API: Videos by channel:', channelCounts);

      return res.json({ videos: finalUniqueVideos });
    }
  } catch (error) {
    console.error('YouTube Search API: Error processing YouTube search:', error);

    // If we have cached results, return them on error
    const searchUrl = req.body?.searchUrl || '';
    if (searchUrl && searchCache[searchUrl]) {
      return res.json({ videos: searchCache[searchUrl].videos });
    }

    res.status(500).json({ message: "Failed to process YouTube search" });
  }
});

/**
 * Extract videos with pagination support to get more results
 */
async function extractVideosWithPagination(html: string, searchUrl: string, targetLimit: number): Promise<Video[]> {
  console.log(`YouTube Search API: Starting pagination extraction for ${targetLimit} videos`);

  // First, extract videos from the initial page
  let allVideos = extractVideosFromSearchPage(html);
  console.log(`YouTube Search API: Initial page extracted ${allVideos.length} videos`);

  // If we have enough videos, return what we have
  if (allVideos.length >= targetLimit) {
    console.log(`YouTube Search API: Target reached with initial page, returning ${targetLimit} videos`);
    return allVideos.slice(0, targetLimit);
  }

  // If target is higher than what we got, try to fetch more using scroll simulation
  console.log(`YouTube Search API: Need more videos (have: ${allVideos.length}, target: ${targetLimit}), attempting scroll-based pagination`);

  try {
    const additionalVideos = await fetchMoreVideosWithScroll(searchUrl, targetLimit, allVideos.length);

    if (additionalVideos.length > 0) {
      // Add new videos (avoiding duplicates)
      const newVideos = additionalVideos.filter(video =>
        !allVideos.some(existing => existing.id === video.id)
      );

      allVideos.push(...newVideos);
      console.log(`YouTube Search API: Added ${newVideos.length} new videos via scroll simulation, total: ${allVideos.length}`);
    } else {
      console.log(`YouTube Search API: No additional videos found via scroll simulation`);
    }
  } catch (error) {
    console.error(`YouTube Search API: Error during scroll-based pagination:`, error);
  }


  console.log(`YouTube Search API: Pagination complete. Extracted ${allVideos.length} total videos (target: ${targetLimit})`);
  return allVideos.slice(0, targetLimit);
}

/**
 * Fetch more videos using scroll simulation approach
 */
async function fetchMoreVideosWithScroll(searchUrl: string, targetLimit: number, currentCount: number): Promise<Video[]> {
  const allVideos: Video[] = [];
  const maxAttempts = 5;
  let attempts = 0;

  console.log(`YouTube Search API: Starting scroll simulation to get ${targetLimit - currentCount} more videos`);

  // Try different approaches to get more videos
  const strategies = [
    // Strategy 1: Different time filters to get more diverse results
    { param: '&sp=EgIIAQ%253D%253D', name: 'this_hour' },
    { param: '&sp=EgIIAg%253D%253D', name: 'today' },
    { param: '&sp=EgIIAw%253D%253D', name: 'this_week' },
    { param: '&sp=EgIIBA%253D%253D', name: 'this_month' },
    // Strategy 2: Different sort orders
    { param: '&sp=CAMSAhAB', name: 'view_count' },
    { param: '&sp=CAISAhAB', name: 'upload_date' },
    // Strategy 3: Try to simulate "load more" by modifying search slightly
    { param: '&gl=US', name: 'geo_us' },
    { param: '&hl=en', name: 'lang_en' }
  ];

  for (const strategy of strategies) {
    if (allVideos.length >= (targetLimit - currentCount) || attempts >= maxAttempts) {
      break;
    }

    attempts++;
    console.log(`YouTube Search API: Trying strategy ${attempts}: ${strategy.name}`);

    try {
      // Modify URL with strategy parameters
      let modifiedUrl = searchUrl;

      // Remove existing similar parameters to avoid conflicts
      if (strategy.param.includes('sp=')) {
        modifiedUrl = modifiedUrl.replace(/&sp=[^&]*/g, '');
      }
      if (strategy.param.includes('gl=')) {
        modifiedUrl = modifiedUrl.replace(/&gl=[^&]*/g, '');
      }
      if (strategy.param.includes('hl=')) {
        modifiedUrl = modifiedUrl.replace(/&hl=[^&]*/g, '');
      }

      // Add the new parameter
      modifiedUrl += strategy.param;

      console.log(`YouTube Search API: Fetching with modified URL: ${modifiedUrl}`);

      const response = await axios.get(modifiedUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        },
        timeout: 15000,
        maxRedirects: 5
      });

      const videos = extractVideosFromSearchPage(response.data);
      console.log(`YouTube Search API: Strategy ${strategy.name} extracted ${videos.length} videos`);

      // Add new videos (avoiding duplicates)
      const newVideos = videos.filter(video =>
        !allVideos.some(existing => existing.id === video.id)
      );

      if (newVideos.length > 0) {
        allVideos.push(...newVideos);
        console.log(`YouTube Search API: Added ${newVideos.length} new unique videos, total additional: ${allVideos.length}`);
      }

      // Add small delay between requests to be respectful
      await new Promise(resolve => setTimeout(resolve, 500)); // Reduced to 500ms

    } catch (error) {
      console.error(`YouTube Search API: Strategy ${strategy.name} failed:`, error.message);
      continue;
    }
  }

  console.log(`YouTube Search API: Scroll simulation complete. Found ${allVideos.length} additional unique videos`);
  return allVideos;
}



/**
 * Extract video information from YouTube search results page
 */
function extractVideosFromSearchPage(html: string): Video[] {
  const videos: Video[] = [];

  try {
    // Look for the initial data in the page
    const initialDataMatch = html.match(/var ytInitialData = (.+?);<\/script>/);

    if (!initialDataMatch || !initialDataMatch[1]) {
      // Try alternative pattern
      const alternativeMatch = html.match(/ytInitialData\s*=\s*({.+?});\s*</);
      if (!alternativeMatch || !alternativeMatch[1]) {
        return videos;
      }
    }

    // Parse the initial data
    const initialDataString = initialDataMatch ? initialDataMatch[1] : null;
    if (!initialDataString) {
      return videos;
    }

    const initialData = JSON.parse(initialDataString);

    // Navigate to the contents section where videos are stored
    const contents = initialData?.contents?.twoColumnSearchResultsRenderer?.primaryContents?.sectionListRenderer?.contents;

    if (!contents || !Array.isArray(contents)) {
      return videos;
    }

    // Find the item renderer section
    let itemSectionRenderer;
    for (const content of contents) {
      if (content.itemSectionRenderer) {
        itemSectionRenderer = content.itemSectionRenderer;
        break;
      }
    }

    if (!itemSectionRenderer || !itemSectionRenderer.contents) {
      return videos;
    }

    // Process each video in the search results
    for (const item of itemSectionRenderer.contents) {
      // Check if this is a video renderer
      const videoRenderer = item.videoRenderer;
      if (!videoRenderer) {
        continue;
      }

      // Extract video ID
      const videoId = videoRenderer.videoId;
      if (!videoId) {
        continue;
      }

      // Extract video title
      const title = videoRenderer.title?.runs?.[0]?.text;
      if (!title) {
        continue;
      }

      // Extract channel information
      const channelTitle = videoRenderer.ownerText?.runs?.[0]?.text;

      // Extract thumbnail with fallback
      let thumbnailUrl = videoRenderer.thumbnail?.thumbnails?.[0]?.url;

      // Provide fallback thumbnail if none found
      if (!thumbnailUrl) {
        thumbnailUrl = `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;
      }

      // Extract published time first (needed for live detection)
      const publishedTimeText = videoRenderer.publishedTimeText?.simpleText;

      // Extract view count with improved parsing for live streams
      let viewCount = 0;
      let isLive = false;

      // Check for live stream indicators
      const badges = videoRenderer.badges || [];
      for (const badge of badges) {
        if (badge.metadataBadgeRenderer?.label === 'LIVE' ||
            badge.metadataBadgeRenderer?.style === 'BADGE_STYLE_TYPE_LIVE_NOW' ||
            badge.metadataBadgeRenderer?.label?.toUpperCase() === 'LIVE') {
          isLive = true;
          console.log(`🔴 LIVE DETECTED via badge: ${badge.metadataBadgeRenderer?.label} for video: ${videoId}`);
          break;
        }
      }

      // Check for live indicators in thumbnail overlays
      if (!isLive) {
        isLive = videoRenderer.thumbnailOverlays?.some((overlay: any) => {
          const isLiveOverlay = overlay.thumbnailOverlayTimeStatusRenderer?.style === 'LIVE' ||
                               overlay.thumbnailOverlayTimeStatusRenderer?.text?.simpleText === 'LIVE' ||
                               overlay.thumbnailOverlayTimeStatusRenderer?.text?.simpleText?.toUpperCase() === 'LIVE';
          if (isLiveOverlay) {
            console.log(`🔴 LIVE DETECTED via thumbnail overlay for video: ${videoId}`);
          }
          return isLiveOverlay;
        }) || false;
      }

      // Check for live indicators in title
      if (!isLive && title) {
        const liveIndicators = [
          '🔴 LIVE', '🔴LIVE', 'LIVE NOW', 'LIVE STREAM', 'LIVESTREAM',
          'STREAMING LIVE', 'STREAMING NOW', '[LIVE]', '(LIVE)', 'LIVE:'
        ];
        isLive = liveIndicators.some(indicator =>
          title.toUpperCase().includes(indicator.toUpperCase())
        );
        if (isLive) {
          console.log(`🔴 LIVE DETECTED via title indicators for video: ${videoId}`);
        }
      }

      // Check for live indicators in published time text
      if (!isLive && publishedTimeText) {
        const liveTimeIndicators = ['streaming', 'live now', 'started streaming'];
        isLive = liveTimeIndicators.some(indicator =>
          publishedTimeText.toLowerCase().includes(indicator)
        );
        if (isLive) {
          console.log(`🔴 LIVE DETECTED via published time text for video: ${videoId}`);
        }
      }

      const viewCountText = videoRenderer.viewCountText?.simpleText ||
                           videoRenderer.viewCountText?.runs?.[0]?.text;

      // Check for live indicators in view count text (e.g., "19 watching")
      if (!isLive && viewCountText && viewCountText.toLowerCase().includes('watching')) {
        isLive = true;
        console.log(`🔴 LIVE DETECTED via "watching" in view count text for video: ${videoId}`);
      }

      if (viewCountText) {
        console.log(`Extracting view count from: "${viewCountText}" for video: ${videoId} (isLive: ${isLive})`);

        // Handle different view count formats
        if (viewCountText.toLowerCase().includes('no views')) {
          viewCount = 0;
        } else if (viewCountText.toLowerCase().includes('view')) {
          // Extract numbers from view count text (e.g., "1,234,567 views", "11 views", "1.2K views")
          const viewCountMatch = viewCountText.match(/[\d,\.]+/);
          if (viewCountMatch) {
            let viewStr = viewCountMatch[0].replace(/,/g, '');

            // Handle K, M, B suffixes
            if (viewCountText.toLowerCase().includes('k')) {
              viewCount = Math.round(parseFloat(viewStr) * 1000);
            } else if (viewCountText.toLowerCase().includes('m')) {
              viewCount = Math.round(parseFloat(viewStr) * 1000000);
            } else if (viewCountText.toLowerCase().includes('b')) {
              viewCount = Math.round(parseFloat(viewStr) * 1000000000);
            } else {
              viewCount = parseInt(viewStr, 10);
            }
          }
        } else if (viewCountText.toLowerCase().includes('watching')) {
          // Handle live stream viewer counts (e.g., "19 watching", "1 watching")
          const viewCountMatch = viewCountText.match(/(\d+[\d,]*)/);
          if (viewCountMatch) {
            const viewStr = viewCountMatch[1].replace(/,/g, '');
            viewCount = parseInt(viewStr, 10);
            console.log(`Detected live stream with ${viewCount} viewers for video: ${videoId}`);
          }
        } else {
          // Handle plain numbers (e.g., "19", "16", "7") - these are often live viewer counts
          console.log(`🔧 TESTING PLAIN NUMBER PARSING for "${viewCountText}"`);
          const viewCountMatch = viewCountText.match(/^(\d+[\d,]*)$/);
          if (viewCountMatch) {
            const viewStr = viewCountMatch[1].replace(/,/g, '');
            const parsedCount = parseInt(viewStr, 10);
            if (!isNaN(parsedCount) && parsedCount >= 0) {
              viewCount = parsedCount;
              console.log(`🎉 SUCCESS: Detected plain number view count: ${viewCount} for video: ${videoId}`);
            }
          } else {
            console.log(`❌ FAILED: No match for plain number pattern in "${viewCountText}"`);
          }
        }

        console.log(`Parsed view count: ${viewCount} for video: ${videoId}`);
      } else {
        console.log(`No view count text found for video: ${videoId}`);
      }

      // Calculate approximate publishedAt date based on relative time
      const publishedAt = calculatePublishedAtFromRelativeTime(publishedTimeText);

      // Extract duration
      const lengthText = videoRenderer.lengthText?.simpleText;
      const duration = lengthText ? `PT${lengthText.replace(/:/g, 'M')}S` : '';

      // Create video object
      const video: Video = {
        id: videoId,
        title,
        channelTitle: channelTitle || 'Unknown Channel',
        description: videoRenderer.detailedMetadataSnippets?.[0]?.snippetText?.runs?.map((run: any) => run.text).join('') || '',
        publishedAt,
        publishedTimeText: publishedTimeText || '', // Store the original relative time text
        thumbnailUrl: thumbnailUrl?.startsWith('//') ? `https:${thumbnailUrl}` : (thumbnailUrl || `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`),
        viewCount, // This will be updated with real-time data below
        likeCount: 0, // Not available in search results
        commentCount: 0, // Not available in search results
        isLive, // Add live stream indicator
        contentDetails: {
          duration
        },
        hasTranscription: false,
        hasFinancialAnalysis: false,
        vph: 0, // Will be calculated later if needed
        uuid: uuidv4(), // Generate a unique ID for this video
        needsRealTimeUpdate: true // Flag to indicate this needs real-time view count
      };

      videos.push(video);
    }

    return videos;
  } catch (error) {
    return videos;
  }
}

/**
 * Calculate an approximate publishedAt date based on relative time text
 */
function calculatePublishedAtFromRelativeTime(relativeTime: string | undefined): string {
  console.log(`📅 TIME PARSING: Input = "${relativeTime}"`);

  if (!relativeTime) {
    console.log(`📅 TIME PARSING: No time provided, using current time`);
    return new Date().toISOString();
  }

  const now = new Date();
  const lowerTime = relativeTime.toLowerCase();

  // Handle "Streamed X ago" format
  if (lowerTime.includes('streamed') && lowerTime.includes('ago')) {
    const cleanTime = lowerTime.replace('streamed', '').replace('ago', '').trim();
    console.log(`📅 TIME PARSING: Detected "streamed ago" format, parsing: "${cleanTime}"`);
    return parseRelativeTime(cleanTime, now);
  }

  // Handle "X ago" format
  if (lowerTime.includes('ago')) {
    const cleanTime = lowerTime.replace('ago', '').trim();
    console.log(`📅 TIME PARSING: Detected "ago" format, parsing: "${cleanTime}"`);
    return parseRelativeTime(cleanTime, now);
  }

  // Handle live streams - try to find when the stream started
  if (lowerTime.includes('live') || lowerTime.includes('streaming')) {
    console.log(`📅 TIME PARSING: Detected live stream, checking for start time in: "${relativeTime}"`);

    // Look for patterns like "Started streaming 2 hours ago" or "Live for 3 hours"
    const streamStartMatch = relativeTime.match(/(?:started|streaming|live).*?(\d+)\s*(second|minute|hour|day)s?\s*(?:ago)?/i);
    if (streamStartMatch) {
      const amount = parseInt(streamStartMatch[1], 10);
      const unit = streamStartMatch[2].toLowerCase();
      console.log(`📅 TIME PARSING: Found stream start time: ${amount} ${unit}(s) ago`);

      switch (unit) {
        case 'second':
          now.setSeconds(now.getSeconds() - amount);
          break;
        case 'minute':
          now.setMinutes(now.getMinutes() - amount);
          break;
        case 'hour':
          now.setHours(now.getHours() - amount);
          break;
        case 'day':
          now.setDate(now.getDate() - amount);
          break;
      }
      console.log(`📅 TIME PARSING: Live stream started at: ${now.toISOString()}`);
      return now.toISOString();
    }

    // If we can't find start time, default to 1 hour ago (reasonable for most live streams)
    console.log(`📅 TIME PARSING: Could not find stream start time, defaulting to 1 hour ago`);
    now.setHours(now.getHours() - 1);
    return now.toISOString();
  }

  // Handle "Premiered" format
  if (lowerTime.includes('premiered')) {
    const cleanTime = lowerTime.replace('premiered', '').trim();
    console.log(`📅 TIME PARSING: Detected "premiered" format, parsing: "${cleanTime}"`);
    return parseRelativeTime(cleanTime, now);
  }

  // Try to parse as relative time directly
  console.log(`📅 TIME PARSING: Trying direct relative time parsing for: "${relativeTime}"`);
  return parseRelativeTime(relativeTime, now);
}

/**
 * Parse relative time strings like "2 hours", "1 day", "3 weeks", etc.
 */
function parseRelativeTime(timeStr: string, baseDate: Date): string {
  const now = new Date(baseDate);
  const lowerTime = timeStr.toLowerCase().trim();

  console.log(`📅 RELATIVE PARSING: "${lowerTime}"`);

  // Extract number and unit
  const match = lowerTime.match(/(\d+)\s*(second|minute|hour|day|week|month|year)s?/);

  if (match) {
    const amount = parseInt(match[1], 10);
    const unit = match[2];

    console.log(`📅 RELATIVE PARSING: Found ${amount} ${unit}(s)`);

    switch (unit) {
      case 'second':
        now.setSeconds(now.getSeconds() - amount);
        break;
      case 'minute':
        now.setMinutes(now.getMinutes() - amount);
        break;
      case 'hour':
        now.setHours(now.getHours() - amount);
        break;
      case 'day':
        now.setDate(now.getDate() - amount);
        break;
      case 'week':
        now.setDate(now.getDate() - (amount * 7));
        break;
      case 'month':
        now.setMonth(now.getMonth() - amount);
        break;
      case 'year':
        now.setFullYear(now.getFullYear() - amount);
        break;
    }

    console.log(`📅 RELATIVE PARSING: Result = ${now.toISOString()}`);
    return now.toISOString();
  }

  // If we can't parse it, return a reasonable default (1 hour ago)
  console.log(`📅 RELATIVE PARSING: Could not parse "${lowerTime}", defaulting to 1 hour ago`);
  now.setHours(now.getHours() - 1);
  return now.toISOString();
}

/**
 * Remove duplicate videos from an array based on video ID
 */
function removeDuplicateVideos(videos: Video[]): Video[] {
  const uniqueVideos: Video[] = [];
  const seenIds = new Set<string>();

  for (const video of videos) {
    if (!seenIds.has(video.id)) {
      seenIds.add(video.id);
      uniqueVideos.push(video);
    }
  }

  return uniqueVideos;
}

/**
 * Update videos with real-time view counts for better accuracy (SELECTIVE)
 * Only updates videos that really need it to avoid hundreds of requests
 */
async function updateVideosWithRealTimeData(videos: Video[]): Promise<Video[]> {
  console.log(`YouTube Search API: Checking ${videos.length} videos for selective real-time updates`);

  // SELECTIVE UPDATE CRITERIA - only update videos that really need it
  const videosNeedingUpdate = videos.filter(video => {
    // Only update videos with very low view counts (likely to be inaccurate)
    // or videos from specific channels we care about
    const hasLowViewCount = video.viewCount < 100; // Less than 100 views
    const isFromTrackedChannel = video.channelTitle?.includes('HowToGuys') ||
                                video.channelTitle?.includes('How To Guys');
    const isVeryRecent = video.publishedTimeText?.includes('minute') ||
                        video.publishedTimeText?.includes('hour');

    return hasLowViewCount || isFromTrackedChannel || isVeryRecent;
  });

  console.log(`YouTube Search API: Found ${videosNeedingUpdate.length} videos that need real-time updates (out of ${videos.length} total)`);

  if (videosNeedingUpdate.length === 0) {
    console.log(`YouTube Search API: No videos need real-time updates, returning original data`);
    return videos;
  }

  // Limit to maximum 5 videos to avoid too many requests
  const limitedVideos = videosNeedingUpdate.slice(0, 5);
  console.log(`YouTube Search API: Limiting real-time updates to ${limitedVideos.length} videos to avoid rate limiting`);

  const updatedVideos = [...videos]; // Start with all original videos

  // Update only the selected videos
  for (const video of limitedVideos) {
    try {
      console.log(`YouTube Search API: Fetching real-time view count for ${video.id} (${video.title.substring(0, 50)}...)`);
      const realTimeViewCount = await fetchVideoViewCount(video.id);

      if (realTimeViewCount > 0 && realTimeViewCount !== video.viewCount) {
        console.log(`YouTube Search API: Updated view count for ${video.id}: ${video.viewCount} -> ${realTimeViewCount}`);

        // Find and update the video in the array
        const videoIndex = updatedVideos.findIndex(v => v.id === video.id);
        if (videoIndex !== -1) {
          updatedVideos[videoIndex] = {
            ...updatedVideos[videoIndex],
            viewCount: realTimeViewCount,
            needsRealTimeUpdate: false
          };
        }
      } else {
        console.log(`YouTube Search API: No update needed for ${video.id} (${realTimeViewCount} vs ${video.viewCount})`);
      }

      // Add delay between requests to be respectful
      await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay

    } catch (error) {
      console.warn(`YouTube Search API: Error fetching real-time view count for ${video.id}:`, error);
    }
  }

  console.log(`YouTube Search API: Finished selective real-time updates`);
  return updatedVideos;
}

// Update view count endpoint
router.post('/update-view-count', async (req, res) => {
  try {
    const { videoId, viewCount } = req.body;
    const userId = req.user?.id || 1; // Default to user 1 for now

    if (!videoId || !viewCount) {
      return res.status(400).json({ error: 'Video ID and view count required' });
    }

    // Update the view count in the database
    await storage.updateYtrVideoViewCount(userId, videoId, viewCount);

    res.json({ success: true, message: 'View count updated successfully' });
  } catch (error) {
    console.error('Error updating view count:', error);
    res.status(500).json({ error: 'Failed to update view count' });
  }
});

// Clear cache endpoint for forcing fresh data
router.post('/clear-cache', async (req, res) => {
  try {
    // Clear all cached data
    Object.keys(searchCache).forEach(key => {
      delete searchCache[key];
    });

    // Reset rate limiting
    Object.keys(lastRequestTime).forEach(key => {
      delete lastRequestTime[key];
    });

    console.log('YouTube Search API: Cache cleared successfully');
    res.json({ message: "Cache cleared successfully", timestamp: Date.now() });
  } catch (error) {
    console.error('YouTube Search API: Error clearing cache:', error);
    res.status(500).json({ message: "Failed to clear cache" });
  }
});

// Force refresh endpoint for specific URL
router.post('/force-refresh', async (req, res) => {
  try {
    const { searchUrl } = req.body;

    if (!searchUrl || typeof searchUrl !== 'string') {
      return res.status(400).json({ message: "Search URL is required" });
    }

    // Clear cache for this specific URL
    if (searchCache[searchUrl]) {
      delete searchCache[searchUrl];
      console.log(`YouTube Search API: Cache cleared for URL: ${searchUrl}`);
    }

    // Reset rate limiting for this URL
    if (lastRequestTime[searchUrl]) {
      delete lastRequestTime[searchUrl];
    }

    res.json({ message: "Cache cleared for URL", url: searchUrl, timestamp: Date.now() });
  } catch (error) {
    console.error('YouTube Search API: Error force refreshing:', error);
    res.status(500).json({ message: "Failed to force refresh" });
  }
});

// Accurate view counts endpoint - only use when you need precise data
router.post('/accurate', async (req, res) => {
  try {
    // This endpoint always enables real-time updates
    const modifiedBody = { ...req.body, enableRealTimeUpdates: true };
    req.body = modifiedBody;

    console.log('YouTube Search API: Accurate view counts requested - enabling real-time updates');

    // Call the main search logic with real-time updates enabled
    return router.handle({ ...req, method: 'POST', url: '/' }, res);
  } catch (error) {
    console.error('YouTube Search API: Error in accurate endpoint:', error);
    res.status(500).json({ message: "Failed to fetch accurate view counts" });
  }
});

/**
 * Perform YouTube search with pagination support
 */
async function performYouTubeSearch(searchUrl: string, resultsLimit: number): Promise<Video[]> {
  // Fetch the search results page
  console.log(`YouTube Search API: Fetching URL with filters: ${searchUrl}`);
  const response = await axios.get(searchUrl, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache'
    },
    timeout: 15000 // 15 second timeout
  });

  // Parse the HTML to extract video information
  const html = response.data;
  console.log(`YouTube Search API: Response length: ${html.length} characters`);

  // Check if the response contains filter-specific content
  const hasLiveContent = html.includes('isLiveNow') || html.includes('"status":"LIVE"') || html.includes('LIVE');
  console.log(`YouTube Search API: Response contains live content indicators: ${hasLiveContent}`);

  // Extract videos with pagination support
  let videos = await extractVideosWithPagination(html, searchUrl, resultsLimit);
  console.log(`YouTube Search API: Extracted ${videos.length} videos from ${searchUrl} (requested: ${resultsLimit})`);

  // Filter for live videos if the URL contains live filter parameter
  console.log(`YouTube Search API: Checking URL for live filter: ${searchUrl}`);
  if (searchUrl.includes('sp=CAMSAkAB')) {
    console.log(`YouTube Search API: Live filter detected in URL, filtering for live videos only`);
    const originalCount = videos.length;
    const liveVideos = videos.filter(video => video.isLive);
    console.log(`YouTube Search API: Filtered ${originalCount} videos to ${liveVideos.length} live videos`);
    console.log(`YouTube Search API: Live video IDs: ${liveVideos.map(v => v.id).join(', ')}`);
    videos = liveVideos;
  } else {
    console.log(`YouTube Search API: No live filter detected in URL`);
  }

  return videos;
}

export default router;
