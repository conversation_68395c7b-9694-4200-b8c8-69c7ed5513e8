import React, { useMemo, useState } from 'react';
import { RssFeedItem } from '@shared/schema';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { highlightFinancialTerms } from '../financial-term-highlighter';
import * as RechartsPrimitive from 'recharts';
import { ChartContainer } from '@/components/ui/chart';
import { KeywordDetailDialog } from './keyword-detail-dialog';

interface TopicModelingProps {
  feedItems: RssFeedItem[];
  maxTopics?: number;
  onOpenArticle?: (articleId: number) => void;
  onSaveArticle?: (articleId: number) => void;
  onOpenInBrowser?: (articleId: number) => void;
}

// Predefined topics with their associated keywords
const TOPICS = {
  'Social Security': ['social security', 'ssa', 'retirement', 'benefits', 'cola', 'cost of living'],
  'Medicare/Medicaid': ['medicare', 'medicaid', 'health', 'insurance', 'coverage', 'prescription'],
  'Taxes': ['tax', 'taxes', 'irs', 'internal revenue', 'refund', 'deduction', 'credit'],
  'Legislation': ['bill', 'law', 'act', 'congress', 'senate', 'house', 'legislation', 'amendment'],
  'Economy': ['economy', 'economic', 'inflation', 'recession', 'growth', 'market', 'stock', 'interest rate'],
  'Housing': ['housing', 'mortgage', 'rent', 'home', 'property', 'real estate', 'apartment'],
  'Employment': ['job', 'employment', 'unemployment', 'wage', 'salary', 'worker', 'labor', 'career'],
  'Veterans': ['veteran', 'va', 'military', 'service', 'disability', 'compensation'],
  'Education': ['education', 'student', 'loan', 'school', 'college', 'university', 'scholarship'],
  'Food Assistance': ['snap', 'food stamps', 'wic', 'nutrition', 'hunger', 'meal', 'food bank'],
};

interface TopicData {
  topic: string;
  count: number;
  articles: number;
  positiveCount: number;
  negativeCount: number;
  neutralCount: number;
}

export function TopicModeling({
  feedItems,
  maxTopics = 10,
  onOpenArticle,
  onSaveArticle,
  onOpenInBrowser
}: TopicModelingProps) {
  // State for the topic detail dialog
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);
  const [selectedTopicKeywords, setSelectedTopicKeywords] = useState<string[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // State for the keyword detail dialog
  const [selectedKeyword, setSelectedKeyword] = useState<string | null>(null);
  const [isKeywordDialogOpen, setIsKeywordDialogOpen] = useState(false);
  const [parentTopic, setParentTopic] = useState<string | null>(null);

  // Handle topic click
  const handleTopicClick = (topic: string) => {
    setSelectedTopic(topic);
    setSelectedTopicKeywords(TOPICS[topic as keyof typeof TOPICS] || []);
    setIsDialogOpen(true);
  };

  // Handle keyword click
  const handleKeywordClick = (keyword: string, topic: string) => {
    console.log(`Keyword clicked: ${keyword} from topic: ${topic}`);
    setSelectedKeyword(keyword);
    setParentTopic(topic);
    setIsKeywordDialogOpen(true);
  };

  // Analyze topics in feed items
  const topicData = useMemo(() => {
    if (!feedItems || feedItems.length === 0) return [];

    const topicMap = new Map<string, TopicData>();

    // Initialize topic data
    Object.keys(TOPICS).forEach(topic => {
      topicMap.set(topic, {
        topic,
        count: 0,
        articles: 0,
        positiveCount: 0,
        negativeCount: 0,
        neutralCount: 0
      });
    });

    // Process each feed item
    feedItems.forEach(item => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      // Combine all text content
      const allText = `${title} ${content} ${scrapedContent}`;

      // First, strip HTML tags and formatting elements
      const strippedText = allText
        .replace(/<[^>]*>/g, ' ') // Remove HTML tags
        .replace(/&[a-z]+;/g, ' ') // Remove HTML entities like &nbsp;
        .replace(/style="[^"]*"/g, ' ') // Remove style attributes
        .replace(/class="[^"]*"/g, ' ') // Remove class attributes
        .replace(/data-[a-z-]+="[^"]*"/g, ' '); // Remove data attributes

      // Convert to lowercase for topic matching
      const cleanedText = strippedText.toLowerCase();

      // Analyze sentiment
      const { counts } = highlightFinancialTerms(strippedText);

      // Check for each topic's keywords
      const topicsInArticle = new Set<string>();

      Object.entries(TOPICS).forEach(([topic, keywords]) => {
        let found = false;

        for (const keyword of keywords) {
          if (cleanedText.includes(keyword.toLowerCase())) {
            found = true;

            // Update topic count
            const data = topicMap.get(topic)!;
            data.count += 1;

            // Only count the article once per topic
            if (!topicsInArticle.has(topic)) {
              topicsInArticle.add(topic);
              data.articles += 1;

              // Add sentiment counts
              data.positiveCount += counts.positive;
              data.negativeCount += counts.negative;
              data.neutralCount += counts.neutral;
            }

            break;
          }
        }
      });
    });

    // Convert map to array, filter out topics with no mentions, and sort by count
    return Array.from(topicMap.values())
      .filter(data => data.count > 0)
      .sort((a, b) => b.count - a.count)
      .slice(0, maxTopics);
  }, [feedItems, maxTopics]);

  // Prepare data for the chart
  const chartData = useMemo(() => {
    return topicData.map(data => ({
      topic: data.topic,
      mentions: data.count,
      articles: data.articles,
      positive: data.positiveCount,
      negative: data.negativeCount,
      neutral: data.neutralCount
    }));
  }, [topicData]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-white">Topic Analysis</CardTitle>
        <CardDescription className="text-white">
          Key topics identified across all RSS feeds with sentiment analysis
        </CardDescription>
      </CardHeader>
      <CardContent>
        {topicData.length === 0 ? (
          <div className="flex justify-center items-center h-[200px]">
            <p className="text-white text-lg">No topics identified</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="h-[400px]">
              <ChartContainer
                config={{
                  mentions: { color: "#3b82f6" },
                  articles: { color: "#10b981" },
                  positive: { color: "#4ade80" },
                  negative: { color: "#f87171" },
                  neutral: { color: "#94a3b8" }
                }}
              >
                <RechartsPrimitive.BarChart
                  data={chartData.slice(0, 8)} // Limit to 8 topics to prevent overcrowding
                  layout="vertical"
                  margin={{ top: 20, right: 50, left: 120, bottom: 20 }}
                  barGap={8}
                  barSize={20}
                >
                  <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                  <RechartsPrimitive.XAxis
                    type="number"
                    label={{ value: 'Count', position: 'insideBottom', offset: -10 }}
                  />
                  <RechartsPrimitive.YAxis
                    type="category"
                    dataKey="topic"
                    width={120}
                    tick={{ fontSize: 12 }}
                  />
                  <RechartsPrimitive.Tooltip />
                  <RechartsPrimitive.Legend wrapperStyle={{ paddingTop: '10px' }} />
                  <RechartsPrimitive.Bar
                    dataKey="mentions"
                    name="Mentions"
                    fill="var(--color-mentions)"
                    onClick={(data) => handleTopicClick(data.topic)}
                    cursor="pointer"
                  />
                  <RechartsPrimitive.Bar
                    dataKey="articles"
                    name="Articles"
                    fill="var(--color-articles)"
                    onClick={(data) => handleTopicClick(data.topic)}
                    cursor="pointer"
                  />
                </RechartsPrimitive.BarChart>
              </ChartContainer>
            </div>

            <ScrollArea className="h-[200px] mt-4">
              <div className="space-y-3">
                {topicData.map((data, index) => (
                  <div
                    key={index}
                    className="p-3 border border-border rounded-md hover:bg-primary/10 transition-colors"
                  >
                    <div className="flex justify-between items-center mb-2">
                      <h3
                        className="text-white font-medium cursor-pointer hover:text-primary"
                        onClick={() => handleTopicClick(data.topic)}
                        title="Click for detailed analysis"
                      >
                        {data.topic}
                      </h3>
                      <div className="flex gap-2">
                        <Badge variant="outline" className="bg-primary/20">
                          {data.count} mentions
                        </Badge>
                        <Badge variant="outline" className="bg-primary/20">
                          {data.articles} articles
                        </Badge>
                      </div>
                    </div>
                    <div className="flex gap-3 mb-2">
                      <div className="flex items-center">
                        <span className="w-3 h-3 rounded-full bg-green-500 mr-1"></span>
                        <span className="text-sm text-white">{data.positiveCount}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-3 h-3 rounded-full bg-red-500 mr-1"></span>
                        <span className="text-sm text-white">{data.negativeCount}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-3 h-3 rounded-full bg-gray-500 mr-1"></span>
                        <span className="text-sm text-white">{data.neutralCount}</span>
                      </div>
                    </div>

                    {/* Keywords list */}
                    <div className="flex flex-wrap gap-2 mt-2">
                      {TOPICS[data.topic as keyof typeof TOPICS]?.slice(0, 5).map((keyword, keywordIndex) => (
                        <Badge
                          key={keywordIndex}
                          variant="outline"
                          className="bg-primary/10 hover:bg-primary/20 cursor-pointer transition-colors py-1 px-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleKeywordClick(keyword, data.topic);
                          }}
                          title={`Click to see long-tail keywords related to "${keyword}"`}
                        >
                          {keyword}
                        </Badge>
                      ))}
                      {TOPICS[data.topic as keyof typeof TOPICS]?.length > 5 && (
                        <Badge
                          variant="outline"
                          className="bg-primary/5 hover:bg-primary/15 cursor-pointer transition-colors py-1 px-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleTopicClick(data.topic);
                          }}
                        >
                          +{TOPICS[data.topic as keyof typeof TOPICS]?.length - 5} more
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </CardContent>

      {/* Topic Detail Dialog */}
      {selectedTopic && (
        <KeywordDetailDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          keyword={selectedTopic}
          feedItems={feedItems}
          type="topic"
          topicKeywords={selectedTopicKeywords}
          onOpenArticle={onOpenArticle}
          onSaveArticle={onSaveArticle}
          onOpenInBrowser={onOpenInBrowser}
        />
      )}

      {/* Keyword Detail Dialog */}
      {selectedKeyword && (
        <KeywordDetailDialog
          isOpen={isKeywordDialogOpen}
          onClose={() => setIsKeywordDialogOpen(false)}
          keyword={selectedKeyword}
          feedItems={feedItems}
          type="word"
          onOpenArticle={onOpenArticle}
          onSaveArticle={onSaveArticle}
          onOpenInBrowser={onOpenInBrowser}
        />
      )}
    </Card>
  );
}
