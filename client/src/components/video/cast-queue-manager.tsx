import React, { useState, useEffect } from 'react';
import { useCastQueue } from '@/context/cast-queue-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Cast, Trash2, Play, X, MoveUp, MoveDown, Info, ArrowUpDown, AlertCircle } from 'lucide-react';
import { useFloatingVideo } from '@/context/floating-video-context';
import { formatDistanceToNow } from 'date-fns';

export function CastQueueManager() {
  const {
    state: { queue, currentIndex, isCastingEnabled },
    removeFromQueue,
    clearQueue,
    toggleCasting,
    isQueueLoading,
    refetchQueue
  } = useCastQueue();

  // Refetch the queue when the component mounts and set up an interval to keep checking
  useEffect(() => {
    console.log('CastQueueManager mounted, refetching queue');

    // Initial fetch with a slight delay to ensure the component is fully mounted
    const initialFetchTimeout = setTimeout(() => {
      console.log('Initial fetch of cast queue');
      refetchQueue();
    }, 300);

    // Set up an interval to keep checking for updates
    const intervalId = setInterval(() => {
      console.log('Periodic refetch of cast queue');
      refetchQueue();
    }, 3000); // Check every 3 seconds

    // Clean up the interval and timeout when the component unmounts
    return () => {
      clearInterval(intervalId);
      clearTimeout(initialFetchTimeout);
    };
  }, [refetchQueue]);

  // Force a refetch when the component becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        console.log('Document became visible, refetching cast queue');
        refetchQueue();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [refetchQueue]);

  const { openFloatingVideo } = useFloatingVideo();

  const [showHelp, setShowHelp] = useState(false);

  // Always show the loading state if we're loading and the queue is empty
  if (isQueueLoading && queue.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <Cast className="h-5 w-5" />
              Cast Queue
            </CardTitle>
          </div>
          <CardDescription>
            Loading your cast queue...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            <span className="text-base font-medium text-foreground">Loading your cast queue...</span>
            <p className="text-sm text-muted-foreground text-center max-w-md">
              Your cast queue is being loaded. If this is your first time, it might take a moment to retrieve your videos.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Then show empty state if queue is empty
  if (queue.length === 0) {
    return (
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <Cast className="h-5 w-5" />
              Cast Queue
            </CardTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowHelp(!showHelp)}
              className="h-8 w-8"
            >
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>
            Add videos to your cast queue to play them in sequence.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {showHelp && (
            <Alert className="mb-4 bg-muted/50">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <p className="text-sm mb-2">How to use the Cast Queue:</p>
                <ol className="text-xs list-decimal pl-4 space-y-1">
                  <li>Click the bookmark icon on any video thumbnail</li>
                  <li>Select "Add to Cast Queue" from the dropdown</li>
                  <li>Click on any video in the queue to play it immediately</li>
                  <li>Videos in your queue will play one after another</li>
                  <li>Use the next/previous buttons on the floating player to navigate</li>
                </ol>
              </AlertDescription>
            </Alert>
          )}
          <div className="text-center py-8 text-muted-foreground">
            Your cast queue is empty. Add videos using the bookmark button on video thumbnails.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <Cast className="h-5 w-5" />
            Cast Queue
            <Badge variant="outline" className="ml-2">{queue.length} videos</Badge>
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowHelp(!showHelp)}
            className="h-8 w-8"
          >
            <Info className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription>
          Videos in your queue will play one after another.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {showHelp && (
          <Alert className="mb-4 bg-muted/50">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <p className="text-sm mb-2">How to use the Cast Queue:</p>
              <ol className="text-xs list-decimal pl-4 space-y-1">
                <li>Click the bookmark icon on any video thumbnail</li>
                <li>Select "Add to Cast Queue" from the dropdown</li>
                <li>Click on any video in the queue to play it immediately</li>
                <li>Videos in your queue will play one after another</li>
                <li>Use the next/previous buttons on the floating player to navigate</li>
              </ol>
            </AlertDescription>
          </Alert>
        )}

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="casting-mode"
              checked={isCastingEnabled}
              onCheckedChange={toggleCasting}
            />
            <Label htmlFor="casting-mode">Auto-play next video</Label>
          </div>

          {currentIndex >= 0 && currentIndex < queue.length && (
            <Badge variant="secondary" className="ml-auto">
              Now playing: {currentIndex + 1} of {queue.length}
            </Badge>
          )}
        </div>

        <ScrollArea className="h-[400px] pr-4">
          {queue.map((video, index) => (
            <div key={video.id} className="mb-3">
              <div
                className={`flex items-start gap-3 p-3 rounded-md ${
                  index === currentIndex ? 'bg-primary/10 border border-primary/30' : 'hover:bg-accent/50'
                } cursor-pointer transition-colors text-foreground hover:text-foreground`}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  openFloatingVideo(video, { isCasting: true });
                }}
                title="Click to play this video"
              >
                <div
                  className="relative flex-shrink-0 group cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    openFloatingVideo(video, { isCasting: true });
                  }}
                >
                  <Badge
                    variant="outline"
                    className="absolute -top-2 -left-2 z-10 bg-background"
                  >
                    #{index + 1}
                  </Badge>
                  <img
                    src={video.thumbnail}
                    alt={video.title}
                    className="w-40 h-24 object-cover rounded-md"
                  />
                  {index === currentIndex ? (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-md border border-primary">
                      <Play className="h-8 w-8 text-white" fill="white" />
                    </div>
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-md opacity-0 group-hover:opacity-100 transition-opacity border border-transparent group-hover:border-primary">
                      <Play className="h-8 w-8 text-white" fill="white" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0 group cursor-pointer" onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    openFloatingVideo(video, { isCasting: true });
                  }}>
                  <div className="text-sm font-medium text-foreground dark:text-white light:text-black group-hover:text-primary dark:group-hover:text-primary-foreground transition-colors">{video.title}</div>
                  <div className="text-xs text-muted-foreground dark:text-gray-300 light:text-gray-700">{video.channelTitle}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {video.viewCount ? video.viewCount.toLocaleString() : '0'} views
                    {video.publishedAt && (
                      <>
                        {' • '}
                        {(() => {
                          try {
                            return formatDistanceToNow(new Date(video.publishedAt), { addSuffix: true });
                          } catch (e) {
                            return 'recently';
                          }
                        })()}
                      </>
                    )}
                  </div>
                </div>

                <div className="flex flex-col gap-1">
                  <Button
                    variant="secondary"
                    size="icon"
                    className="h-7 w-7 bg-primary/20 hover:bg-primary/30"
                    onClick={(e) => {
                      e.stopPropagation();
                      openFloatingVideo(video, { isCasting: true });
                    }}
                    title="Play now"
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 text-destructive"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFromQueue(video.id);
                    }}
                    title="Remove from queue"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {index < queue.length - 1 && <Separator className="my-2" />}
            </div>
          ))}
        </ScrollArea>
      </CardContent>
      <CardFooter className="flex justify-between items-center border-t pt-4">
        <Button
          variant="outline"
          size="sm"
          className="text-destructive"
          onClick={clearQueue}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Clear Queue
        </Button>

        {queue.length > 0 && currentIndex >= 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const currentVideo = queue[currentIndex];
              if (currentVideo) {
                openFloatingVideo(currentVideo);
              }
            }}
          >
            <Play className="h-4 w-4 mr-2" />
            Play Current
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
