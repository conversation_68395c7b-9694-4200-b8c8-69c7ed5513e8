import { Router } from 'express';
import { fetchVideoViewCount } from '../utils/youtube-utils';
import axios from 'axios';

const router = Router();

// Endpoint to fetch real-time view count for a single video
router.post('/view-count', async (req, res) => {
  try {
    const { videoId } = req.body;

    console.log(`YouTube Utils API: Received request for video: ${videoId}`);
    console.log(`YouTube Utils API: Request body:`, req.body);

    if (!videoId || typeof videoId !== 'string') {
      console.error(`YouTube Utils API: Invalid video ID: ${videoId}`);
      return res.status(400).json({
        error: 'Video ID is required and must be a string'
      });
    }

    console.log(`YouTube Utils API: Fetching view count for video: ${videoId}`);

    // Clear any existing cache for this video to force fresh data
    console.log(`YouTube Utils API: Clearing cache for video: ${videoId}`);

    // Fetch real-time view count from YouTube watch page
    const viewCount = await fetchVideoViewCount(videoId);

    console.log(`YouTube Utils API: Raw view count result for ${videoId}: ${viewCount}`);

    if (viewCount > 0) {
      console.log(`YouTube Utils API: Successfully fetched view count for ${videoId}: ${viewCount}`);
      res.json({
        videoId,
        viewCount,
        timestamp: Date.now(),
        success: true
      });
    } else {
      console.warn(`YouTube Utils API: Could not fetch view count for ${videoId} - returned ${viewCount}`);
      res.status(404).json({
        error: 'Could not fetch view count for this video',
        videoId,
        viewCount: viewCount,
        timestamp: Date.now(),
        success: false
      });
    }
  } catch (error) {
    console.error('YouTube Utils API: Error fetching view count:', error);
    console.error('YouTube Utils API: Error stack:', error.stack);
    res.status(500).json({
      error: 'Internal server error while fetching view count',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: Date.now(),
      success: false
    });
  }
});

// Test endpoint to check if a specific video ID works
router.post('/test-video', async (req, res) => {
  try {
    const { videoId } = req.body;

    console.log(`YouTube Utils TEST: Testing video: ${videoId}`);

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    // Test with a known video ID first
    const testVideoId = videoId || 'PS327n-vYqM';
    console.log(`YouTube Utils TEST: Using video ID: ${testVideoId}`);

    // Try to fetch the YouTube page directly
    const url = `https://www.youtube.com/watch?v=${testVideoId}`;
    console.log(`YouTube Utils TEST: Fetching URL: ${url}`);

    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      },
      timeout: 15000
    });

    console.log(`YouTube Utils TEST: Got response, status: ${response.status}`);
    console.log(`YouTube Utils TEST: Response length: ${response.data.length} characters`);

    // Look for view count patterns in the HTML
    const html = response.data;
    const viewPatterns = [
      /"viewCount":\s*"(\d+)"/,
      /"viewCount":\s*{\s*"simpleText":\s*"([\d,]+)\s+views"/i,
      /(\d+[\d,]*)\s+views/i
    ];

    const foundCounts = [];
    for (const pattern of viewPatterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        const count = parseInt(match[1].replace(/,/g, ''), 10);
        if (!isNaN(count)) {
          foundCounts.push({ pattern: pattern.toString(), count });
        }
      }
    }

    console.log(`YouTube Utils TEST: Found ${foundCounts.length} view count matches:`, foundCounts);

    // Also try the actual function
    const actualViewCount = await fetchVideoViewCount(testVideoId);
    console.log(`YouTube Utils TEST: fetchVideoViewCount returned: ${actualViewCount}`);

    res.json({
      videoId: testVideoId,
      url,
      responseLength: response.data.length,
      foundPatterns: foundCounts,
      actualFunctionResult: actualViewCount,
      timestamp: Date.now()
    });

  } catch (error) {
    console.error('YouTube Utils TEST: Error:', error);
    res.status(500).json({
      error: error.message,
      stack: error.stack,
      timestamp: Date.now()
    });
  }
});

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'youtube-utils',
    timestamp: Date.now()
  });
});

export default router;
