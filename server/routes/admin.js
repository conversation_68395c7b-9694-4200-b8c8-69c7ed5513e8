const express = require('express');
const router = express.Router();
const express = require('express');
const router = express.Router();
const User = require('../models/user');
const authMiddleware = require('../middleware/auth');
const { adminRequired } = require('../middleware/roles');

// Exclude sensitive fields from user responses
const USER_PROJECTION = {
    password: 0,
    refreshToken: 0,
    __v: 0
};

/**
 * Get all users
 */
router.get('/users', authMiddleware.isAuthenticated, adminRequired, async (req, res) => {
    try {
        const users = await User.find({}, USER_PROJECTION);
        res.json({
            success: true,
            data: users
        });
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ 
            success: false,
            message: 'Failed to fetch users',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

/**
 * Delete a user
 */
router.delete('/users/:id', authMiddleware.isAuthenticated, adminRequired, async (req, res) => {
    try {
        // Prevent admin from deleting themselves
        if (req.user._id.toString() === req.params.id) {
            return res.status(400).json({
                success: false,
                message: 'Cannot delete your own admin account'
            });
        }

        const user = await User.findOneAndDelete(
            { _id: req.params.id },
            { select: USER_PROJECTION }
        );

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        res.json({
            success: true,
            message: 'User deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ 
            success: false,
            message: 'Failed to delete user',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

module.exports = router;

