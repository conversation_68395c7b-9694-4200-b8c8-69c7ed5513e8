--- src/components/realtime-auto-refresh.tsx
+++ src/components/realtime-auto-refresh.tsx
@@ -454,81 +454,65 @@
   // Main effect for auto-refresh functionality - completely rewritten for reliability
   useEffect(() => {
     // Always clean up any existing interval first to prevent duplicates
     if (autoRefreshIntervalId) {
-      logger.verbose(`Cleaning up existing auto-refresh interval ID: ${autoRefreshIntervalId}`);
+      logger.verbose(`Cleaning up existing auto-refresh timer ID: ${autoRefreshIntervalId}`);
       clearInterval(autoRefreshIntervalId);
+      clearTimeout(autoRefreshIntervalId as unknown as NodeJS.Timeout);
       setAutoRefreshIntervalId(null);
     }
 
     // Only proceed if all conditions are met
     if (!isActive || isPaused || !autoRefreshEnabled || autoRefreshInterval <= 0) {
-      logger.verbose("Not setting up auto-refresh interval - conditions not met");
+      logger.verbose("Not setting up auto-refresh timer - conditions not met");
       return;
     }
 
     logger.verbose(`Setting up NEW auto-refresh with interval: ${autoRefreshInterval} seconds`);
 
-    // Perform initial refresh if needed
-    const shouldPerformInitialRefresh = videos.length > 0;
-    const lastRefresh = lastRefreshTime ? lastRefreshTime.getTime() : 0;
-    const timeSinceLastRefresh = Date.now() - lastRefresh;
-
-    // Only trigger initial refresh if it's been significantly longer than the interval
-    if (shouldPerformInitialRefresh && (!lastRefreshTime || timeSinceLastRefresh > autoRefreshInterval * 1000 * 0.5)) {
-      logger.verbose("Scheduling initial refresh for auto-refresh");
-
-      // Use setTimeout to break the potential render cycle
-      setTimeout(() => {
-        if (!refreshInProgressRef.current) {
-          logger.verbose("Executing initial auto-refresh");
-          handleRefresh().catch(err => {
-            logger.error("Error during initial auto-refresh:", err);
-          });
-        } else {
-          logger.verbose("Skipping initial refresh - refresh already in progress");
-        }
-      }, 1000); // Slightly longer delay for initial refresh
-    }
-
-    // Create a new interval for periodic refreshes
-    const newIntervalId = setInterval(() => {
-      // Skip if refresh is already in progress
-      if (refreshInProgressRef.current) {
-        logger.verbose("Auto-refresh interval triggered but refresh already in progress, skipping");
-        return;
-      }
-
-      // Skip if conditions have changed
-      if (!isActive || !autoRefreshEnabled || isPaused) {
-        logger.verbose("Auto-refresh interval triggered but conditions no longer valid, skipping");
-        return;
-      }
-
-      logger.verbose(`Auto-refresh interval triggered at ${new Date().toLocaleTimeString()}`);
-
-      // Add a small random delay to prevent synchronization issues
-      const randomDelay = Math.floor(Math.random() * 300); // 0-300ms random delay
-
-      setTimeout(() => {
-        // Double-check conditions again after delay
-        if (!refreshInProgressRef.current && isActive && autoRefreshEnabled && !isPaused) {
-          logger.verbose("Executing scheduled auto-refresh");
-          handleRefresh().catch(err => {
-            logger.error("Error during scheduled auto-refresh:", err);
-          });
-        } else {
-          logger.verbose("Skipping scheduled auto-refresh - conditions changed");
-        }
-      }, randomDelay);
-    }, autoRefreshInterval * 1000);
-
-    // Store the new interval ID
-    logger.verbose(`New auto-refresh interval set with ID: ${newIntervalId}`);
-    setAutoRefreshIntervalId(newIntervalId);
+    // Calculate the exact next refresh time based on the current time and interval
+    // This ensures we have a consistent refresh schedule
+    const now = new Date();
+    const exactNextRefreshTime = new Date(now.getTime() + autoRefreshInterval * 1000);
+    
+    // Update the next refresh time state
+    setNextRefreshTime(exactNextRefreshTime);
+    logger.verbose(`Setting exact next refresh time to: ${exactNextRefreshTime.toLocaleTimeString()}`);
+
+    // Calculate the exact milliseconds until the next refresh
+    const msUntilNextRefresh = autoRefreshInterval * 1000;
+    
+    // Create a single timeout for the next refresh instead of an interval
+    // This ensures more precise timing and prevents drift
+    const newTimeoutId = setTimeout(() => {
+      // Skip if refresh is already in progress
+      if (refreshInProgressRef.current) {
+        logger.verbose("Auto-refresh timeout triggered but refresh already in progress, skipping");
+        return;
+      }
+
+      // Skip if conditions have changed
+      if (!isActive || !autoRefreshEnabled || isPaused) {
+        logger.verbose("Auto-refresh timeout triggered but conditions no longer valid, skipping");
+        return;
+      }
+
+      logger.verbose(`Auto-refresh timeout triggered at ${new Date().toLocaleTimeString()}`);
+
+      // Execute the refresh
+      if (!refreshInProgressRef.current && isActive && autoRefreshEnabled && !isPaused) {
+        logger.verbose("Executing scheduled auto-refresh");
+        handleRefresh().catch(err => {
+          logger.error("Error during scheduled auto-refresh:", err);
+        });
+      } else {
+        logger.verbose("Skipping scheduled auto-refresh - conditions changed");
+      }
+    }, msUntilNextRefresh);
+
+    // Store the new timeout ID (we're using the same state variable for simplicity)
+    logger.verbose(`New auto-refresh timeout set with ID: ${newTimeoutId}`);
+    setAutoRefreshIntervalId(newTimeoutId as unknown as NodeJS.Timeout);
 
     // Clean up on unmount or when dependencies change
     return () => {
-      logger.verbose(`Cleaning up auto-refresh interval ID: ${newIntervalId}`);
-      clearInterval(newIntervalId);
+      logger.verbose(`Cleaning up auto-refresh timeout ID: ${newTimeoutId}`);
+      clearTimeout(newTimeoutId);
     };
-  }, [isActive, isPaused, autoRefreshEnabled, autoRefreshInterval, videos.length, handleRefresh, lastRefreshTime]);
+  }, [isActive, isPaused, autoRefreshEnabled, autoRefreshInterval, handleRefresh]);
