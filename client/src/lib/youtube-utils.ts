/**
 * Utility functions for YouTube video processing
 */

/**
 * Check if a video is a live stream based on title and duration
 * @param title Video title
 * @param duration Video duration from contentDetails
 * @returns boolean indicating if the video is a live stream
 */
export function isLiveStream(title: string, duration?: string): boolean {
  // Check if duration is missing (common for live streams)
  if (!duration) {
    return true;
  }
  
  // Check if duration is "P0D" which indicates a live stream
  if (duration === 'P0D') {
    return true;
  }
  
  // Check for common live stream indicators in the title
  const liveIndicators = [
    '🔴 LIVE',
    '🔴LIVE',
    '🔴 LIVE:',
    '🔴LIVE:',
    '(LIVE)',
    '[LIVE]',
    'LIVE:',
    'LIVE |',
    'LIVE NOW',
    'LIVE STREAM',
    'LIVESTREAM',
    'STREAMING LIVE',
    'STREAMING NOW',
    'WATCH LIVE'
  ];
  
  return liveIndicators.some(indicator => 
    title.toUpperCase().includes(indicator.toUpperCase())
  );
}

/**
 * Check if a video is an ended live stream
 * @param title Video title
 * @param isUnplayable Whether the video is marked as unplayable
 * @returns boolean indicating if the video is an ended live stream
 */
export function isEndedLiveStream(title: string, isUnplayable?: boolean): boolean {
  // If the video is unplayable, it might be an ended live stream
  if (isUnplayable) {
    return true;
  }
  
  // Check for common ended live stream indicators in the title
  const endedLiveIndicators = [
    'REPLAY',
    'REBROADCAST',
    'RECORDED LIVE',
    'PREVIOUSLY RECORDED',
    'ENDED STREAM'
  ];
  
  return endedLiveIndicators.some(indicator => 
    title.toUpperCase().includes(indicator.toUpperCase())
  );
}

/**
 * Format view count with appropriate suffix (K, M, B)
 * @param count View count number
 * @param isLive Whether this is for a live stream
 * @param publishedAt Publication date string
 * @returns Formatted view count string
 */
export function formatViewCount(count: number, isLive: boolean = false, publishedAt?: string): string {
  if (isNaN(count)) return '0';
  
  if (isLive) {
    return `${count.toLocaleString()} watching now`;
  }
  
  // Calculate views per day if we have a published date
  if (publishedAt) {
    const publishDate = new Date(publishedAt);
    const now = new Date();
    const daysSincePublished = Math.max(1, (now.getTime() - publishDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // If the video is less than 7 days old, show views per day
    if (daysSincePublished < 7) {
      const viewsPerDay = Math.round(count / daysSincePublished);
      return `${count.toLocaleString()} (${viewsPerDay.toLocaleString()}/day)`;
    }
  }
  
  return count.toLocaleString();
}
