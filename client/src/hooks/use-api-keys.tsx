import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface ApiKey {
  id: number;
  name: string;
  masked_token: string;
  is_active: boolean;
  last_used?: string;
  created_at: string;
  exhausted_until?: string;
  is_default?: boolean;
}

export function useApiKeys() {
  const { toast } = useToast();

  // Fetch all API keys
  const {
    data: apiKeys,
    isLoading: isLoadingApiKeys,
    error: apiKeysError,
    refetch: refetchApiKeys,
  } = useQuery<ApiKey[]>({
    queryKey: ["/api/api-keys"],
    staleTime: 60000, // 1 minute
  });

  // Create a default API key from environment variables
  // Since we're having issues with the API endpoint, let's create a mock default key
  const defaultApiKey: ApiKey = {
    id: 0,
    name: "Default API Key (Environment)",
    masked_token: "*****...*****",
    is_active: true,
    created_at: new Date().toISOString(),
    is_default: true,
  };

  const isLoadingDefaultKey = false;
  const defaultKeyError = null;
  const refetchDefaultKey = () => {};


  // Add a new API key
  const addApiKey = useMutation({
    mutationFn: async (data: { token: string; name?: string; is_active?: boolean }) => {
      const res = await apiRequest("POST", "/api/api-keys", data);
      if (!res.ok) {
        throw new Error(`Failed to add API key: ${res.status} ${res.statusText}`);
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/api-keys"] });
      toast({
        title: "API key added",
        description: "Your API key has been added successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to add API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update an API key
  const updateApiKey = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<ApiKey> }) => {
      const res = await apiRequest("PUT", `/api/api-keys/${id}`, data);
      if (!res.ok) {
        throw new Error(`Failed to update API key: ${res.status} ${res.statusText}`);
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/api-keys"] });
      toast({
        title: "API key updated",
        description: "Your API key has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete an API key
  const deleteApiKey = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("DELETE", `/api/api-keys/${id}`);
      if (!res.ok) {
        throw new Error(`Failed to delete API key: ${res.status} ${res.statusText}`);
      }
      return id;
    },
    onSuccess: (id) => {
      queryClient.invalidateQueries({ queryKey: ["/api/api-keys"] });
      toast({
        title: "API key deleted",
        description: "Your API key has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Test an API key
  const testApiKey = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("POST", `/api/api-keys/${id}/test`);
      if (!res.ok) {
        throw new Error(`Failed to test API key: ${res.status} ${res.statusText}`);
      }
      return res.json();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to test API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Test the default API key
  const testDefaultApiKey = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("POST", "/api/api-keys/default/test");
      if (!res.ok) {
        throw new Error(`Failed to test default API key: ${res.status} ${res.statusText}`);
      }
      return res.json();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to test default API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Toggle default API key active status
  const toggleDefaultApiKey = useMutation({
    mutationFn: async (isActive: boolean) => {
      const res = await apiRequest("PUT", "/api/api-keys/default/toggle", { is_active: isActive });
      if (!res.ok) {
        throw new Error(`Failed to toggle default API key: ${res.status} ${res.statusText}`);
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/api-keys"] });
      toast({
        title: "Default API key updated",
        description: "Default API key status has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update default API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete the default API key
  const deleteDefaultApiKey = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("DELETE", "/api/api-keys/default");
      if (!res.ok) {
        throw new Error(`Failed to delete default API key: ${res.status} ${res.statusText}`);
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/api-keys"] });
      toast({
        title: "Default API key deleted",
        description: "The default API key has been removed from the application.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete default API key",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Combine regular and default API keys
  const allApiKeys = [...(apiKeys || [])];
  if (defaultApiKey) {
    allApiKeys.unshift(defaultApiKey);
  }

  // Determine overall loading state
  const isLoading = isLoadingApiKeys || isLoadingDefaultKey;

  // Combine refetch functions
  const refetch = () => {
    refetchApiKeys();
    refetchDefaultKey();
  };

  return {
    apiKeys: allApiKeys,
    defaultApiKey,
    isLoading,
    error: apiKeysError || defaultKeyError,
    refetch,
    addApiKey,
    updateApiKey,
    deleteApiKey,
    testApiKey,
    testDefaultApiKey,
    toggleDefaultApiKey,
    deleteDefaultApiKey,
  };
}

export default useApiKeys;
