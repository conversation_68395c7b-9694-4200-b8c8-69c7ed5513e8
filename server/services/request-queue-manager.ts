import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { isClientIdle } from '../routes/client-state';

// Request priority enum
export enum RequestPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

// Request type enum
export enum RequestType {
  VIDEO_LOAD = 'video_load',
  VIDEO_REFRESH = 'video_refresh',
  GROUP_REFRESH = 'group_refresh',
  CHANNEL_REFRESH = 'channel_refresh',
  OTHER = 'other'
}

// Request status enum
export enum RequestStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  TIMEOUT = 'timeout'
}

// Request interface
export interface Request {
  id: string;
  type: RequestType;
  priority: RequestPriority;
  status: RequestStatus;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  userId?: number;
  data: any;
  result?: any;
  error?: string;
  timeout?: number; // Timeout in milliseconds
}

// Request handler type
export type RequestHandler = (request: Request) => Promise<any>;

// Request queue manager class
export class RequestQueueManager extends EventEmitter {
  private static instance: RequestQueueManager;
  private requests: Map<string, Request> = new Map();
  private queue: string[] = [];
  private handlers: Map<RequestType, RequestHandler> = new Map();
  private running: boolean = false;
  private maxConcurrent: number = 5; // Allow more concurrent requests than tasks
  private currentRunning: number = 0;
  private timeoutIds: Map<string, NodeJS.Timeout> = new Map();

  private constructor() {
    super();
  }

  // Get singleton instance
  public static getInstance(): RequestQueueManager {
    if (!RequestQueueManager.instance) {
      RequestQueueManager.instance = new RequestQueueManager();
    }
    return RequestQueueManager.instance;
  }

  // Register a request handler
  public registerHandler(type: RequestType, handler: RequestHandler): void {
    this.handlers.set(type, handler);
    console.log(`Registered handler for request type: ${type}`);
  }

  // Add a request to the queue
  public addRequest(
    type: RequestType,
    priority: RequestPriority,
    data: any,
    userId?: number,
    timeout?: number
  ): Request {
    const id = uuidv4();
    const request: Request = {
      id,
      type,
      priority,
      status: RequestStatus.PENDING,
      createdAt: new Date(),
      userId,
      data,
      timeout
    };

    this.requests.set(id, request);
    this.queue.push(id);

    // Sort queue by priority
    this.sortQueue();

    console.log(`Added request ${id} (${type}) to queue with priority ${priority}`);

    this.emit('request:added', request);

    // Start processing if not already running
    if (!this.running) {
      this.start();
    }

    // Set up timeout if specified
    if (timeout) {
      const timeoutId = setTimeout(() => {
        this.handleRequestTimeout(id);
      }, timeout);
      this.timeoutIds.set(id, timeoutId);
    }

    return request;
  }

  // Handle request timeout
  private handleRequestTimeout(id: string): void {
    const request = this.requests.get(id);
    if (!request) return;

    // Only handle if still pending or processing
    if (request.status === RequestStatus.PENDING || request.status === RequestStatus.PROCESSING) {
      console.log(`Request ${id} (${request.type}) timed out after ${request.timeout}ms`);

      // Update request status
      request.status = RequestStatus.TIMEOUT;
      request.error = `Request timed out after ${request.timeout}ms`;
      request.completedAt = new Date();
      this.requests.set(id, request);

      // Remove from queue if still pending
      if (request.status === RequestStatus.PENDING) {
        const index = this.queue.indexOf(id);
        if (index !== -1) {
          this.queue.splice(index, 1);
        }
      }

      // If it was processing, decrement the running count
      if (request.status === RequestStatus.PROCESSING) {
        this.currentRunning = Math.max(0, this.currentRunning - 1);
      }

      this.emit('request:timeout', request);
    }

    // Clear the timeout ID
    this.timeoutIds.delete(id);
  }

  // Get a request by ID
  public getRequest(id: string): Request | undefined {
    return this.requests.get(id);
  }

  // Get all requests for a user
  public getUserRequests(userId: number): Request[] {
    return Array.from(this.requests.values())
      .filter(request => request.userId === userId);
  }

  // Cancel a request
  public cancelRequest(id: string): boolean {
    const request = this.requests.get(id);
    if (!request) return false;

    if (request.status === RequestStatus.PENDING || request.status === RequestStatus.PROCESSING) {
      // Store original status to check if it was processing
      const wasProcessing = request.status === RequestStatus.PROCESSING;

      // Remove from queue if pending
      const index = this.queue.indexOf(id);
      if (index !== -1) {
        this.queue.splice(index, 1);
      }

      // Mark as cancelled
      request.status = RequestStatus.CANCELLED;
      request.completedAt = new Date();
      request.error = 'Request was cancelled';
      this.requests.set(id, request);
      this.emit('request:cancelled', request);

      // If it was processing, decrement the running count
      if (wasProcessing) {
        this.currentRunning = Math.max(0, this.currentRunning - 1);
      }

      // Clear any timeout
      if (this.timeoutIds.has(id)) {
        clearTimeout(this.timeoutIds.get(id)!);
        this.timeoutIds.delete(id);
      }

      return true;
    }

    return false;
  }

  // Start processing the queue
  public start(): void {
    if (this.running) return;

    this.running = true;
    this.processQueue();
    console.log('Request queue manager started');
  }

  // Stop processing the queue
  public stop(): void {
    this.running = false;
    console.log('Request queue manager stopped');
  }

  // Process the next request in the queue
  private async processQueue(): Promise<void> {
    if (!this.running || this.queue.length === 0 || this.currentRunning >= this.maxConcurrent) {
      // Use a longer delay (1000ms) when idle to reduce CPU usage
      setTimeout(() => this.processQueue(), 1000);
      return;
    }

    // Get the next request without removing it yet
    const requestId = this.queue[0];
    if (!requestId) {
      setTimeout(() => this.processQueue(), 1000);
      return;
    }

    const request = this.requests.get(requestId);
    if (!request) {
      // Remove invalid request
      this.queue.shift();
      setTimeout(() => this.processQueue(), 100);
      return;
    }

    // Check if the user's client is idle
    if (request.userId && isClientIdle(request.userId)) {
      // If this is a refresh request and the user is idle, delay processing
      if (
        request.type === RequestType.VIDEO_REFRESH ||
        request.type === RequestType.GROUP_REFRESH ||
        request.type === RequestType.CHANNEL_REFRESH
      ) {
        // Use a much longer delay for refresh requests when the client is idle
        setTimeout(() => this.processQueue(), 5000); // 5 seconds
        return;
      }
    }

    // Now remove the request from the queue
    this.queue.shift();

    // Update request status
    request.status = RequestStatus.PROCESSING;
    request.startedAt = new Date();
    this.requests.set(requestId, request);
    this.currentRunning++;

    this.emit('request:processing', request);
    console.log(`Processing request ${requestId} (${request.type})`);

    // Process next request after a short delay to prevent CPU spikes
    // Only check for more tasks if we have capacity and tasks in queue
    if (this.queue.length > 0 && this.currentRunning < this.maxConcurrent) {
      setTimeout(() => this.processQueue(), 50);
    }

    try {
      // Get handler for this request type
      const handler = this.handlers.get(request.type);
      if (!handler) {
        throw new Error(`No handler registered for request type: ${request.type}`);
      }

      // Execute the request handler
      const result = await handler(request);

      // Update request with result
      const completedRequest = this.requests.get(requestId);
      if (completedRequest) {
        completedRequest.status = RequestStatus.COMPLETED;
        completedRequest.result = result;
        completedRequest.completedAt = new Date();
        this.requests.set(requestId, completedRequest);
        this.emit('request:completed', completedRequest);
        console.log(`Completed request ${requestId} (${request.type})`);
      }
    } catch (error) {
      // Update request with error
      const failedRequest = this.requests.get(requestId);
      if (failedRequest) {
        failedRequest.status = RequestStatus.FAILED;
        failedRequest.error = error.message || 'Unknown error';
        failedRequest.completedAt = new Date();
        this.requests.set(requestId, failedRequest);
        this.emit('request:failed', failedRequest);
        console.error(`Failed request ${requestId} (${request.type}):`, error);
      }
    } finally {
      this.currentRunning--;

      // Clear any timeout
      if (this.timeoutIds.has(requestId)) {
        clearTimeout(this.timeoutIds.get(requestId)!);
        this.timeoutIds.delete(requestId);
      }

      // Check if we need to process more requests
      if (this.queue.length > 0 && this.running) {
        // Check if the next request is for an idle user
        const nextRequestId = this.queue[0];
        if (nextRequestId) {
          const nextRequest = this.requests.get(nextRequestId);
          if (nextRequest && nextRequest.userId && isClientIdle(nextRequest.userId)) {
            // If the user is idle, use a longer delay
            setTimeout(() => this.processQueue(), 1000);
            return;
          }
        }

        // Use a short delay to prevent CPU spikes
        setTimeout(() => this.processQueue(), 100); // Increased from 50ms
      } else {
        // Use a longer delay when queue is empty
        setTimeout(() => this.processQueue(), 2000); // Increased from 500ms
      }
    }
  }

  // Sort queue by priority
  private sortQueue(): void {
    this.queue.sort((a, b) => {
      const requestA = this.requests.get(a);
      const requestB = this.requests.get(b);
      if (!requestA || !requestB) return 0;

      // First sort by priority (higher number = higher priority)
      if (requestA.priority !== requestB.priority) {
        return requestB.priority - requestA.priority;
      }

      // Then sort by creation time (older first)
      return requestA.createdAt.getTime() - requestB.createdAt.getTime();
    });
  }

  // Prioritize video loading requests
  public prioritizeVideoLoading(): void {
    // Find all video loading requests in the queue
    const videoLoadRequestIds = this.queue.filter(id => {
      const request = this.requests.get(id);
      return request && request.type === RequestType.VIDEO_LOAD;
    });

    // Boost their priority to CRITICAL
    videoLoadRequestIds.forEach(id => {
      const request = this.requests.get(id);
      if (request) {
        request.priority = RequestPriority.CRITICAL;
        this.requests.set(id, request);
      }
    });

    // Re-sort the queue
    this.sortQueue();
  }

  // Deprioritize refresh requests when video loading is happening
  public deprioritizeRefreshRequests(): void {
    // Find all refresh requests in the queue
    const refreshRequestIds = this.queue.filter(id => {
      const request = this.requests.get(id);
      return request && (
        request.type === RequestType.VIDEO_REFRESH ||
        request.type === RequestType.GROUP_REFRESH ||
        request.type === RequestType.CHANNEL_REFRESH
      );
    });

    // Lower their priority to LOW
    refreshRequestIds.forEach(id => {
      const request = this.requests.get(id);
      if (request) {
        request.priority = RequestPriority.LOW;
        this.requests.set(id, request);
      }
    });

    // Re-sort the queue
    this.sortQueue();
  }

  // Clean up old requests
  public cleanupRequests(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = new Date().getTime();
    for (const [id, request] of this.requests.entries()) {
      if (
        (request.status === RequestStatus.COMPLETED ||
         request.status === RequestStatus.FAILED ||
         request.status === RequestStatus.CANCELLED ||
         request.status === RequestStatus.TIMEOUT) &&
        request.completedAt &&
        request.completedAt.getTime() + maxAge < now
      ) {
        this.requests.delete(id);
      }
    }
  }
}

// Export singleton instance
export const requestQueueManager = RequestQueueManager.getInstance();
