import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
// AdminSidebar is now handled by the App.tsx layout
import { Loader2, Database, Users, Video, Folder, List, BarChart } from "lucide-react";
import { getViewsPerHourColor } from "@/hooks/use-financial-analysis";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";

// Refined dashboard stats interface focused on insightful data
interface DashboardStats {
  // Summary stats
  summary: {
    totalUsers: number;
    totalVideos: number;
    totalKeywordGroups: number;
    totalPlaylists: number;
    databaseSize: number; // MB
  };

  // User insights
  userInsights: {
    activeUsers: Array<{
      id: number;
      username: string;
      videoCount: number;
    }>;
    averageVideosPerUser: number;
  };

  // Video insights
  videoInsights: {
    trendingVideos: Array<{
      id: string;
      title: string;
      viewsPerHour: number;
    }>;
    shortsPercentage: number;
    regularPercentage: number;
    videoGrowth: Array<{
      date: string;
      count: number;
    }>;
  };

  // Content insights
  contentInsights: {
    topKeywordGroups: Array<{
      id: number;
      name: string;
      videoCount: number;
    }>;
  };
}

export default function DashboardPage() {
  const {
    data: stats,
    isLoading,
    error,
  } = useQuery<DashboardStats>({
    queryKey: ["/api/admin/stats"],
    queryFn: async () => {
      try {
        const res = await apiRequest("GET", "/api/admin/stats");
        return res.json();
      } catch (error) {
        // If the endpoint doesn't exist yet, return mock data
        console.warn("Admin stats endpoint not implemented, using mock data");
        return {
          summary: {
            totalUsers: 3, // Accurate user count
            totalVideos: 120,
            totalKeywordGroups: 8,
            totalPlaylists: 3,
            databaseSize: 5 // MB
          },
          userInsights: {
            activeUsers: [
              { id: 1, username: 'admin', videoCount: 45 },
              { id: 2, username: 'kedar', videoCount: 40 },
              { id: 3, username: 'testadmin', videoCount: 35 }
            ],
            averageVideosPerUser: 40
          },
          videoInsights: {
            trendingVideos: [
              { id: 'abc123', title: 'Top Trending Video', viewsPerHour: 150 },
              { id: 'def456', title: 'Second Trending Video', viewsPerHour: 120 },
              { id: 'ghi789', title: 'Third Trending Video', viewsPerHour: 90 }
            ],
            shortsPercentage: 35,
            regularPercentage: 65,
            videoGrowth: [
              { date: '2023-05-01', count: 10 },
              { date: '2023-05-02', count: 15 },
              { date: '2023-05-03', count: 12 },
              { date: '2023-05-04', count: 20 },
              { date: '2023-05-05', count: 18 }
            ]
          },
          contentInsights: {
            topKeywordGroups: [
              { id: 1, name: 'Technology', videoCount: 30 },
              { id: 2, name: 'Gaming', videoCount: 25 },
              { id: 3, name: 'Music', videoCount: 20 }
            ]
          }
        };
      }
    },
  });

  if (isLoading) {
    return (
      <div className="p-8 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="text-red-500">Error loading dashboard: {(error as Error).message}</div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="videos">Videos</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
        </TabsList>

        {/* Overview Tab - Key metrics at a glance */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Users
                </CardTitle>
                <CardDescription>Registered accounts</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold">{stats?.summary?.totalUsers || 0}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <Video className="h-5 w-5" />
                  Videos
                </CardTitle>
                <CardDescription>Total videos tracked</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold">{stats?.summary?.totalVideos || 0}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <Folder className="h-5 w-5" />
                  Keyword Groups
                </CardTitle>
                <CardDescription>Content categories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold">{stats?.summary?.totalKeywordGroups || 0}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Database
                </CardTitle>
                <CardDescription>Storage used</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold">{stats?.summary?.databaseSize || 0} MB</div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Video className="h-5 w-5" />
                Video Distribution
              </CardTitle>
              <CardDescription>Shorts vs. Regular Videos</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">Shorts ({stats?.videoInsights?.shortsPercentage || 0}%)</h3>
                  <Progress value={stats?.videoInsights?.shortsPercentage || 0} className="h-4 mb-4" />
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Regular Videos ({stats?.videoInsights?.regularPercentage || 0}%)</h3>
                  <Progress value={stats?.videoInsights?.regularPercentage || 0} className="h-4 mb-4" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Users Tab - User activity and engagement */}
        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Activity
              </CardTitle>
              <CardDescription>Most active users by video count</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.userInsights?.activeUsers?.map((user) => (
                  <div key={user.id} className="flex items-center">
                    <div className="w-24 font-medium truncate">{user.username}</div>
                    <div className="flex-1 mx-2">
                      <Progress
                        value={(user.videoCount / (stats?.summary?.totalVideos || 1)) * 100}
                        className="h-3"
                      />
                    </div>
                    <div className="w-16 text-sm text-right">{user.videoCount} videos</div>
                  </div>
                ))}
              </div>

              <div className="mt-6 pt-4 border-t">
                <p className="text-sm text-muted-foreground">
                  Average videos per user: <span className="font-medium">{stats?.userInsights?.averageVideosPerUser || 0}</span>
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Videos Tab - Video performance and trends */}
        <TabsContent value="videos" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Video className="h-5 w-5" />
                Trending Videos
              </CardTitle>
              <CardDescription>Videos with highest views per hour</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.videoInsights?.trendingVideos?.map((video) => (
                  <div key={video.id} className="p-3 border rounded-lg">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium truncate" title={video.title}>
                        {video.title.length > 40 ? `${video.title.substring(0, 40)}...` : video.title}
                      </h3>
                      <div className={`text-sm font-bold ${getViewsPerHourColor(video.viewsPerHour)}`}>
                        <span className="text-base">{video.viewsPerHour}</span> <span className="text-xs opacity-80">vph</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart className="h-5 w-5" />
                Video Growth
              </CardTitle>
              <CardDescription>New videos added over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-60 flex items-end justify-between">
                {stats?.videoInsights?.videoGrowth?.map((day) => {
                  const maxCount = Math.max(...(stats?.videoInsights?.videoGrowth?.map(d => d.count) || [1]));
                  const height = (day.count / maxCount) * 100;
                  return (
                    <div key={day.date} className="flex flex-col items-center">
                      <div
                        className="w-12 bg-primary rounded-t"
                        style={{ height: `${height}%`, minHeight: '10px' }}
                      />
                      <div className="text-xs mt-2">{new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</div>
                      <div className="text-xs font-medium">{day.count}</div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Tab - Keyword performance */}
        <TabsContent value="content" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Folder className="h-5 w-5" />
                Top Keyword Groups
              </CardTitle>
              <CardDescription>Most used keyword groups by video count</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.contentInsights?.topKeywordGroups?.map((group) => (
                  <div key={group.id} className="flex items-center">
                    <div className="w-32 font-medium truncate" title={group.name}>{group.name}</div>
                    <div className="flex-1 mx-2">
                      <Progress
                        value={(group.videoCount / (stats?.summary?.totalVideos || 1)) * 100}
                        className="h-3"
                      />
                    </div>
                    <div className="w-16 text-sm text-right">{group.videoCount} videos</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
