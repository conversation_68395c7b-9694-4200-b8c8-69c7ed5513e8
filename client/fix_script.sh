#!/bin/bash

# Create a backup
cp src/pages/youtube-page.tsx src/pages/youtube-page.tsx.bak

# Find the line with the closing div and main tags
MAIN_CLOSE_LINE=$(grep -n "</main>" src/pages/youtube-page.tsx | cut -d':' -f1)
DIV_CLOSE_LINE=$((MAIN_CLOSE_LINE + 1))
RETURN_END_LINE=$((DIV_CLOSE_LINE + 1))

# Extract the part before the return statement
head -n $((MAIN_CLOSE_LINE - 1)) src/pages/youtube-page.tsx > part1.txt

# Extract the dialog part
tail -n +$((RETURN_END_LINE + 1)) src/pages/youtube-page.tsx | head -n 65 > part2.txt

# Extract the part after the dialog
tail -n +$((RETURN_END_LINE + 66)) src/pages/youtube-page.tsx > part3.txt

# Create the fixed file
cat > src/pages/youtube-page.tsx << 'EOL'
$(cat part1.txt)
      </main>
    </div>
  );
}

// Financial Analysis Dialog Component
function FinancialAnalysisDialog({ 
  isOpen, 
  onOpenChange, 
  currentVideo, 
  transcript, 
  analysisResults 
}) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
$(cat part2.txt | tail -n +2 | head -n 63)
    </Dialog>
  );
}

$(cat part3.txt)
EOL

# Clean up temporary files
rm part1.txt part2.txt part3.txt

echo "Fix applied!"
