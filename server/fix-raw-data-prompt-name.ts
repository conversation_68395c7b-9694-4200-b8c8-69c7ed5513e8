/**
 * Fix Raw Data Prompt Name
 *
 * This script adds the prompt name to the raw data for existing videos.
 */

import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file path and directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Connect to the database
const dbPath = path.resolve(__dirname, '../data.db');
console.log(`Connecting to database at ${dbPath}`);
const db = new Database(dbPath);

try {
  console.log('Starting to fix raw data prompt names...');

  // Begin transaction
  db.exec('BEGIN TRANSACTION');

  // Get all videos with raw data
  const videos = db.prepare(`
    SELECT id, openrouter_raw_data
    FROM youtube_videos
    WHERE openrouter_raw_data IS NOT NULL
  `).all();

  console.log(`Found ${videos.length} videos with raw data`);

  let updatedCount = 0;

  // Process each video
  for (const video of videos) {
    try {
      // Check if we have OpenRouter raw data
      if (video.openrouter_raw_data) {
        try {
          const rawData = JSON.parse(video.openrouter_raw_data);

          // Add prompt name if it doesn't exist
          if (!rawData.promptName) {
            rawData.promptName = 'default';

            // Update the raw data in the database
            const updateStmt = db.prepare(`
              UPDATE youtube_videos
              SET openrouter_raw_data = ?
              WHERE id = ?
            `);

            updateStmt.run(JSON.stringify(rawData), video.id);
            updatedCount++;
            console.log(`Updated OpenRouter raw data for video ${video.id}`);
          }
        } catch (error) {
          console.error(`Error processing OpenRouter raw data for video ${video.id}:`, error);
        }
      }
    } catch (error) {
      console.error(`Error processing video ${video.id}:`, error);
    }
  }

  // Commit the transaction
  db.exec('COMMIT');
  console.log(`Updated ${updatedCount} videos with prompt names`);
  console.log('Raw data prompt names fixed successfully');

} catch (error) {
  console.error('Error fixing raw data prompt names:', error);
  db.exec('ROLLBACK');
  process.exit(1);
} finally {
  db.close();
}
