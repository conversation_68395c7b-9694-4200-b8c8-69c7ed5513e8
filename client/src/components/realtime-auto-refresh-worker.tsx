import { useState, useEffect, useRef, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Pause, Play, RefreshCw } from "lucide-react";
import { Video } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import { useLogger } from "@/hooks/use-logger";
import { useRefreshWorker } from '@/lib/worker-manager';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface AutoRefreshProps {
  isActive: boolean;
  onRefresh: () => Promise<any>;
  videos: Video[];
  updatePreviousVphValues?: (videos: Video[]) => void;
  updateCache?: () => void;
  selectedGroupId?: string | number | null;
}

export function RealtimeAutoRefreshWorker({
  isActive,
  onRefresh,
  videos,
  updatePreviousVphValues,
  updateCache,
  selectedGroupId
}: AutoRefreshProps) {
  // Get the refresh worker
  const refreshWorker = useRefreshWorker();

  // State for auto-refresh settings
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState<boolean>(() => {
    // Try to load auto-refresh state from localStorage
    const savedState = localStorage.getItem('realtimeAutoRefreshEnabled');
    // If there's a saved state, use it; otherwise, default to true
    return savedState !== null ? savedState === 'true' : true;
  });

  // State for auto-refresh interval (in seconds)
  const [autoRefreshInterval, setAutoRefreshInterval] = useState<number>(() => {
    // Try to load interval from localStorage
    const savedInterval = localStorage.getItem('realtimeAutoRefreshInterval');
    // If there's a saved interval, use it; otherwise, default to 30 seconds
    return savedInterval !== null ? parseInt(savedInterval, 10) : 30;
  });

  // State for pause/resume
  const [isPaused, setIsPaused] = useState<boolean>(false);

  // State for tracking refresh in progress
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // State for tracking last refresh time
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);

  // State for countdown display
  const [countdown, setCountdown] = useState<number>(autoRefreshInterval);

  // Ref to track if a refresh is in progress
  const refreshInProgressRef = useRef<boolean>(false);

  // Ref to store the interval ID
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);

  // Ref to store the countdown interval ID
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Get toast and logger
  const { toast } = useToast();
  const logger = useLogger();

  // Function to calculate suggested refresh interval based on video activity
  const calculateSuggestedRefreshInterval = useCallback((videos: Video[]) => {
    if (videos.length === 0) return 30; // Default to 30 seconds

    // Calculate average views per hour
    const totalVph = videos.reduce((sum, video) => sum + (video.viewsPerHour || 0), 0);
    const avgVph = totalVph / videos.length;

    // Adjust interval based on activity level
    if (avgVph > 1000) return 10; // Very high activity
    if (avgVph > 500) return 15; // High activity
    if (avgVph > 200) return 20; // Moderate activity
    if (avgVph > 100) return 25; // Low activity
    return 30; // Default
  }, []);

  // Function to start the countdown timer
  const startCountdown = useCallback(() => {
    // Clear any existing countdown
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
    }

    // Set initial countdown value
    setCountdown(autoRefreshInterval);

    // Start a new countdown
    countdownIntervalRef.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          return autoRefreshInterval; // Reset when it reaches 0
        }
        return prev - 1;
      });
    }, 1000);
  }, [autoRefreshInterval]);

  // Set up worker event listeners
  useEffect(() => {
    // Handle refresh started
    const onRefreshStarted = () => {
      logger.verbose("Worker refresh started");
      setIsRefreshing(true);
      refreshInProgressRef.current = true;
    };

    // Handle refresh completed
    const onRefreshCompleted = (data: any) => {
      logger.verbose("Worker refresh completed", data);
      setIsRefreshing(false);
      refreshInProgressRef.current = false;
      setLastRefreshTime(new Date());

      // Update cache and previous VPH values
      if (updateCache) updateCache();
      if (updatePreviousVphValues && videos) updatePreviousVphValues(videos);

      // Restart countdown
      startCountdown();

      // Show success toast
      toast({
        title: "Refresh Completed",
        description: "Video feed has been updated with the latest data.",
        duration: 3000
      });
    };

    // Handle group refresh started
    const onGroupRefreshStarted = (data: any) => {
      logger.verbose(`Worker group refresh started for group ${data.groupId}`);
      setIsRefreshing(true);
      refreshInProgressRef.current = true;
    };

    // Handle group refresh completed
    const onGroupRefreshCompleted = (data: any) => {
      logger.verbose(`Worker group refresh completed for group ${data.groupId}`, data);
      setIsRefreshing(false);
      refreshInProgressRef.current = false;
      setLastRefreshTime(new Date());

      // Update cache and previous VPH values
      if (updateCache) updateCache();
      if (updatePreviousVphValues && videos) updatePreviousVphValues(videos);

      // Restart countdown
      startCountdown();

      // Show success toast
      toast({
        title: "Group Refresh Completed",
        description: `Videos for group ${data.groupId} have been updated.`,
        duration: 3000
      });
    };

    // Handle errors
    const onError = (data: any) => {
      logger.error("Worker error", data);
      setIsRefreshing(false);
      refreshInProgressRef.current = false;

      // Show error toast
      toast({
        title: "Refresh Failed",
        description: data.error || "An unknown error occurred during refresh.",
        variant: "destructive",
        duration: 5000
      });

      // Restart countdown
      startCountdown();
    };

    // Register event listeners
    const unsubscribeRefreshStarted = refreshWorker.on('refresh-started', onRefreshStarted);
    const unsubscribeRefreshCompleted = refreshWorker.on('refresh-completed', onRefreshCompleted);
    const unsubscribeGroupRefreshStarted = refreshWorker.on('group-refresh-started', onGroupRefreshStarted);
    const unsubscribeGroupRefreshCompleted = refreshWorker.on('group-refresh-completed', onGroupRefreshCompleted);
    const unsubscribeError = refreshWorker.on('error', onError);

    // Clean up event listeners on unmount
    return () => {
      unsubscribeRefreshStarted();
      unsubscribeRefreshCompleted();
      unsubscribeGroupRefreshStarted();
      unsubscribeGroupRefreshCompleted();
      unsubscribeError();
    };
  }, [refreshWorker, logger, toast, updateCache, updatePreviousVphValues, videos, startCountdown]);

  // Effect to handle auto-refresh
  useEffect(() => {
    // Only set up auto-refresh if enabled and active
    if (!autoRefreshEnabled || !isActive || isPaused) {
      // Clear any existing interval
      if (intervalIdRef.current) {
        clearInterval(intervalIdRef.current);
        intervalIdRef.current = null;
      }
      return;
    }

    logger.verbose(`Setting up auto-refresh with interval: ${autoRefreshInterval} seconds`);

    // Start countdown
    startCountdown();

    // Check if the app is idle (using the global flag set by idle detector)
    const isAppIdle = window.__APP_IDLE_STATE__ === true;

    // Use a longer interval if the app is idle
    const effectiveInterval = isAppIdle ? autoRefreshInterval * 3 : autoRefreshInterval;

    if (isAppIdle) {
      logger.verbose(`App is idle, using longer refresh interval: ${effectiveInterval} seconds`);
    }

    // Set up interval for auto-refresh
    intervalIdRef.current = setInterval(() => {
      // Only proceed if not already refreshing
      if (!refreshInProgressRef.current) {
        // Check if app is idle again (might have changed since interval was set up)
        const currentlyIdle = window.__APP_IDLE_STATE__ === true;

        if (currentlyIdle) {
          logger.verbose("Auto-refresh interval triggered while app is idle");

          // When idle, only refresh if it's been a long time since last refresh
          const now = new Date();
          const lastRefresh = lastRefreshTime || new Date(0);
          const timeSinceLastRefresh = now.getTime() - lastRefresh.getTime();

          // If it's been less than 2x the interval, skip this refresh
          if (timeSinceLastRefresh < autoRefreshInterval * 2 * 1000) {
            logger.verbose(`Skipping auto-refresh while idle - last refresh was only ${Math.round(timeSinceLastRefresh/1000)}s ago`);
            return;
          }
        }

        logger.verbose("Auto-refresh interval triggered");

        // Use the worker to refresh videos
        if (selectedGroupId) {
          refreshWorker.refreshGroupVideos(selectedGroupId);
        } else {
          refreshWorker.refreshVideos();
        }
      } else {
        logger.verbose("Skipping auto-refresh - refresh already in progress");
      }
    }, effectiveInterval * 1000);

    // Clean up on unmount or when dependencies change
    return () => {
      if (intervalIdRef.current) {
        clearInterval(intervalIdRef.current);
        intervalIdRef.current = null;
      }

      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
      }
    };
  }, [autoRefreshEnabled, isActive, isPaused, autoRefreshInterval, logger, startCountdown, selectedGroupId, refreshWorker]);

  // Effect to save settings to localStorage
  useEffect(() => {
    localStorage.setItem('realtimeAutoRefreshEnabled', autoRefreshEnabled.toString());
    localStorage.setItem('realtimeAutoRefreshInterval', autoRefreshInterval.toString());
  }, [autoRefreshEnabled, autoRefreshInterval]);

  // Effect for background throttling
  useEffect(() => {
    // Function to handle visibility change
    const handleVisibilityChange = () => {
      if (!autoRefreshEnabled || !isActive) return;

      if (document.hidden) {
        // Page is not visible, pause the countdown
        if (countdownIntervalRef.current) {
          clearInterval(countdownIntervalRef.current);
          countdownIntervalRef.current = null;
        }

        // Pause the auto-refresh interval
        if (intervalIdRef.current) {
          clearInterval(intervalIdRef.current);
          intervalIdRef.current = null;
        }

        logger.verbose("Page hidden, paused auto-refresh");

        // Notify the server that the client is idle
        try {
          fetch('/api/client-state/idle', {
            method: 'POST',
            credentials: 'include',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ idle: true })
          }).catch(() => {}); // Ignore errors
        } catch (e) {
          // Ignore errors
        }
      } else {
        // Page is visible again, restart the countdown and interval
        startCountdown();

        // Check if the app was already marked as idle by the idle detector
        const isAppIdle = window.__APP_IDLE_STATE__ === true;

        // Only resume auto-refresh if not paused and not idle
        if (!intervalIdRef.current && !isPaused && !isAppIdle) {
          // Use a longer interval if the app was just made visible
          const effectiveInterval = autoRefreshInterval * 1.5; // Use 1.5x interval initially

          intervalIdRef.current = setInterval(() => {
            if (!refreshInProgressRef.current) {
              logger.verbose("Auto-refresh interval triggered after visibility change");

              // Use the worker to refresh videos
              if (selectedGroupId) {
                refreshWorker.refreshGroupVideos(selectedGroupId);
              } else {
                refreshWorker.refreshVideos();
              }
            }
          }, effectiveInterval * 1000);

          logger.verbose(`Page visible again, resumed auto-refresh with interval ${effectiveInterval}s`);

          // Notify the server that the client is active
          try {
            fetch('/api/client-state/idle', {
              method: 'POST',
              credentials: 'include',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ idle: false })
            }).catch(() => {}); // Ignore errors
          } catch (e) {
            // Ignore errors
          }
        } else if (isAppIdle) {
          logger.verbose("Page visible but app is idle, not resuming auto-refresh");
        }
      }
    };

    // Add event listener for visibility change
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Clean up on unmount
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [autoRefreshEnabled, isActive, isPaused, autoRefreshInterval, logger, startCountdown, selectedGroupId, refreshWorker]);

  // Handle manual refresh button click
  const handleManualRefresh = useCallback(() => {
    if (refreshInProgressRef.current) {
      logger.verbose("Manual refresh requested but refresh already in progress");
      return;
    }

    logger.verbose("Manual refresh requested");

    // Use the worker to refresh videos
    if (selectedGroupId) {
      refreshWorker.refreshGroupVideos(selectedGroupId);
    } else {
      refreshWorker.refreshVideos();
    }

    // Reset countdown
    startCountdown();
  }, [logger, selectedGroupId, refreshWorker, startCountdown]);

  // Handle toggle auto-refresh
  const handleToggleAutoRefresh = useCallback(() => {
    setAutoRefreshEnabled(prev => !prev);

    // If enabling, start countdown and perform initial refresh
    if (!autoRefreshEnabled) {
      startCountdown();

      // Perform initial refresh if not already refreshing
      if (!refreshInProgressRef.current) {
        setTimeout(() => {
          if (selectedGroupId) {
            refreshWorker.refreshGroupVideos(selectedGroupId);
          } else {
            refreshWorker.refreshVideos();
          }
        }, 100);
      }
    }
  }, [autoRefreshEnabled, startCountdown, selectedGroupId, refreshWorker]);

  // Handle pause/resume
  const handlePauseResume = useCallback(() => {
    setIsPaused(prev => !prev);

    if (isPaused) {
      // Resuming - start countdown and set up interval
      startCountdown();

      if (!intervalIdRef.current && autoRefreshEnabled && isActive) {
        intervalIdRef.current = setInterval(() => {
          if (!refreshInProgressRef.current) {
            logger.verbose("Auto-refresh interval triggered after resume");

            // Use the worker to refresh videos
            if (selectedGroupId) {
              refreshWorker.refreshGroupVideos(selectedGroupId);
            } else {
              refreshWorker.refreshVideos();
            }
          }
        }, autoRefreshInterval * 1000);
      }
    } else {
      // Pausing - clear intervals
      if (intervalIdRef.current) {
        clearInterval(intervalIdRef.current);
        intervalIdRef.current = null;
      }

      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
      }
    }
  }, [isPaused, startCountdown, autoRefreshEnabled, isActive, logger, autoRefreshInterval, selectedGroupId, refreshWorker]);

  // Handle interval change
  const handleIntervalChange = useCallback((value: number[]) => {
    const newInterval = value[0];
    setAutoRefreshInterval(newInterval);

    // Restart countdown with new interval
    startCountdown();

    // Clear and reset interval with new value
    if (intervalIdRef.current) {
      clearInterval(intervalIdRef.current);

      if (autoRefreshEnabled && isActive && !isPaused) {
        intervalIdRef.current = setInterval(() => {
          if (!refreshInProgressRef.current) {
            logger.verbose("Auto-refresh interval triggered after interval change");

            // Use the worker to refresh videos
            if (selectedGroupId) {
              refreshWorker.refreshGroupVideos(selectedGroupId);
            } else {
              refreshWorker.refreshVideos();
            }
          }
        }, newInterval * 1000);
      }
    }
  }, [autoRefreshEnabled, isActive, isPaused, logger, startCountdown, selectedGroupId, refreshWorker]);

  // Format time for display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // If not active, don't render anything
  if (!isActive) return null;

  return (
    <div className="flex flex-col gap-2 p-2 border rounded-lg bg-card">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Switch
            checked={autoRefreshEnabled}
            onCheckedChange={handleToggleAutoRefresh}
            id="auto-refresh-toggle"
          />
          <Label htmlFor="auto-refresh-toggle" className="cursor-pointer">
            Auto-Refresh
          </Label>

          {autoRefreshEnabled && (
            <Badge
              variant={isPaused ? "outline" : "default"}
              className={cn(
                "ml-2",
                isRefreshing ? "bg-yellow-500 text-yellow-950" : ""
              )}
            >
              {isRefreshing
                ? "Refreshing..."
                : isPaused
                  ? "Paused"
                  : `Next: ${formatTime(countdown)}`
              }
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2">
          {autoRefreshEnabled && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handlePauseResume}
                    disabled={isRefreshing}
                  >
                    {isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {isPaused ? "Resume auto-refresh" : "Pause auto-refresh"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleManualRefresh}
                  disabled={isRefreshing}
                >
                  <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                Refresh now
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {autoRefreshEnabled && (
        <div className="flex items-center gap-4">
          <span className="text-xs text-muted-foreground w-8">10s</span>
          <Slider
            value={[autoRefreshInterval]}
            min={10}
            max={120}
            step={5}
            onValueChange={handleIntervalChange}
            disabled={isRefreshing}
            className="flex-1"
          />
          <span className="text-xs text-muted-foreground w-8">120s</span>
        </div>
      )}
    </div>
  );
}
