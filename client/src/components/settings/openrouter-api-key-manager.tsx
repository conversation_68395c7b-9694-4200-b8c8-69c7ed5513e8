import { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus, Trash2, RefreshCw, Check, X, Edit, Star, Download, Upload, FileJson } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useOpenRouterApiKeys } from "@/hooks/use-openrouter-api-keys";
import { formatDate } from '@/lib/date-format';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { OpenRouterApiKeyExportData } from "@shared/schema";

interface OpenRouterApiKey {
  id: number;
  name: string;
  masked_token: string;
  is_active: boolean;
  last_used?: string;
  created_at: string;
  is_default?: boolean;
  exhausted_until?: string;
  rate_limit_type?: string;
  token?: string;
}

export function OpenRouterApiKeyManager() {
  const { toast } = useToast();
  const {
    apiKeys,
    isLoading,
    addApiKey: addApiKeyMutation,
    updateApiKey: updateApiKeyMutation,
    deleteApiKey: deleteApiKeyMutation,
    testApiKey: testApiKeyMutation,
    exportApiKeys: exportApiKeysMutation,
    importApiKeys: importApiKeysMutation,
    resetExhaustedKeys: resetExhaustedKeysMutation,
  } = useOpenRouterApiKeys();

  // State for dialogs
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);

  // State for form inputs
  const [newKeyName, setNewKeyName] = useState("");
  const [newKeyToken, setNewKeyToken] = useState("");
  const [editingKey, setEditingKey] = useState<OpenRouterApiKey | null>(null);
  const [editKeyName, setEditKeyName] = useState("");
  const [editKeyToken, setEditKeyToken] = useState("");
  const [editKeyActive, setEditKeyActive] = useState(true);
  const [importJson, setImportJson] = useState("");
  const [importTab, setImportTab] = useState("json");

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State for test results
  const [testResults, setTestResults] = useState<Record<number | string, {
    loading: boolean,
    success?: boolean,
    message?: string,
    rateLimitInfo?: {
      detected: boolean,
      type?: string,
      resetTime?: string
    }
  }>>({});

  // Reset exhausted API keys
  const handleResetExhaustedKeys = () => {
    // Count how many keys are currently exhausted
    const exhaustedKeys = apiKeys.filter(key =>
      key.exhausted_until && new Date(key.exhausted_until) > new Date()
    );

    if (exhaustedKeys.length === 0) {
      toast({
        title: 'No Exhausted Keys',
        description: 'There are no rate-limited API keys to reset.',
      });
      return;
    }

    resetExhaustedKeysMutation.mutate(undefined, {
      onSuccess: (result) => {
        toast({
          title: 'Rate Limits Reset',
          description: result.message,
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Reset Failed',
          description: error.message || 'Failed to reset rate-limited API keys',
          variant: 'destructive',
        });
      }
    });
  };

  // Add a new API key
  const handleAddApiKey = async () => {
    if (!newKeyToken.trim()) {
      toast({
        title: "Error",
        description: "API key token is required",
        variant: "destructive",
      });
      return;
    }

    // Extract token if needed
    let token = newKeyToken.trim();
    let extractedName = newKeyName;

    addApiKeyMutation.mutate(
      {
        token: token,
        name: extractedName || "OpenRouter API Key",
        is_active: true,
      },
      {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "OpenRouter API key added successfully",
          });
          setAddDialogOpen(false);
          setNewKeyName("");
          setNewKeyToken("");
        },
        onError: (error: any) => {
          toast({
            title: "Error",
            description: error.message || "Failed to add OpenRouter API key",
            variant: "destructive",
          });
        }
      }
    );
  };

  // Delete an API key
  const handleDeleteApiKey = async (id: number) => {
    if (!confirm("Are you sure you want to delete this OpenRouter API key?")) {
      return;
    }

    deleteApiKeyMutation.mutate(id);
  };

  // Toggle API key active status
  const handleToggleApiKeyActive = async (id: number, isActive: boolean) => {
    updateApiKeyMutation.mutate({
      id,
      data: { is_active: !isActive }
    });
  };

  // Set an API key as default
  const handleSetAsDefault = async (id: number) => {
    updateApiKeyMutation.mutate({
      id,
      data: { is_default: true }
    });
  };

  // Open edit dialog for an API key
  const handleOpenEditDialog = (key: OpenRouterApiKey) => {
    setEditingKey(key);
    setEditKeyName(key.name || `API Key ${key.id}`);
    setEditKeyToken(""); // Don't pre-fill the token for security reasons
    setEditKeyActive(key.is_active);
    setEditDialogOpen(true);
  };

  // Handle saving edited API key
  const handleSaveEditedApiKey = () => {
    if (!editingKey) return;

    const updateData: Partial<OpenRouterApiKey> = {
      name: editKeyName,
      is_active: editKeyActive
    };

    // Only include token if it was changed
    if (editKeyToken.trim()) {
      updateData.token = editKeyToken.trim();
    }

    updateApiKeyMutation.mutate({
      id: editingKey.id,
      data: updateData
    }, {
      onSuccess: () => {
        setEditDialogOpen(false);
        setEditingKey(null);
        setEditKeyName("");
        setEditKeyToken("");
      }
    });
  };

  // Test an API key
  const handleTestApiKey = async (id: number) => {
    setTestResults(prev => ({
      ...prev,
      [id]: { loading: true }
    }));

    testApiKeyMutation.mutate(id, {
      onSuccess: (result) => {
        // Check if the result includes rate limit information
        const hasRateLimit = result.rateLimitInfo && result.rateLimitInfo.detected;

        // If rate limit was detected during testing, mark the key as rate limited in the database
        if (hasRateLimit && result.rateLimitInfo?.resetTime) {
          // This will be handled by the server automatically
          console.log(`Rate limit detected for key ${id}:`, result.rateLimitInfo);
        }

        setTestResults(prev => ({
          ...prev,
          [id]: {
            loading: false,
            success: result.success,
            message: result.message,
            rateLimitInfo: result.rateLimitInfo
          }
        }));

        // Clear test result after 10 seconds (longer to allow reading rate limit info)
        setTimeout(() => {
          setTestResults(prev => {
            const newResults = { ...prev };
            delete newResults[id];
            return newResults;
          });
        }, 10000);
      },
      onError: (error) => {
        setTestResults(prev => ({
          ...prev,
          [id]: {
            loading: false,
            success: false,
            message: "Failed to test API key"
          }
        }));
      }
    });
  };

  // Handle exporting API keys
  const handleExportApiKeys = async () => {
    try {
      const result = await exportApiKeysMutation.mutateAsync();

      // Create a JSON blob and download it
      const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `openrouter-api-keys-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Export successful",
        description: `Exported ${result.keys.length} OpenRouter API keys`,
      });
    } catch (error) {
      console.error('Error exporting API keys:', error);
      toast({
        title: "Export failed",
        description: error.message || "Failed to export OpenRouter API keys",
        variant: "destructive",
      });
    }
  };

  // Handle importing API keys from JSON
  const handleImportApiKeys = async () => {
    try {
      if (!importJson.trim()) {
        toast({
          title: "Import failed",
          description: "Please enter valid JSON data",
          variant: "destructive",
        });
        return;
      }

      // Parse the JSON
      let data: OpenRouterApiKeyExportData;
      try {
        data = JSON.parse(importJson);
      } catch (e) {
        toast({
          title: "Invalid JSON",
          description: "The provided JSON is not valid",
          variant: "destructive",
        });
        return;
      }

      // Validate the structure
      if (!data.keys || !Array.isArray(data.keys)) {
        toast({
          title: "Invalid format",
          description: "The JSON must contain a 'keys' array",
          variant: "destructive",
        });
        return;
      }

      // Import the keys
      await importApiKeysMutation.mutateAsync({ keys: data.keys });
      setImportDialogOpen(false);
      setImportJson("");
      setImportTab("json");
    } catch (error) {
      console.error('Error importing API keys:', error);
      toast({
        title: "Import failed",
        description: error.message || "Failed to import OpenRouter API keys",
        variant: "destructive",
      });
    }
  };

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        setImportJson(content);
      } catch (error) {
        console.error('Error reading file:', error);
        toast({
          title: "File read error",
          description: "Could not read the uploaded file",
          variant: "destructive",
        });
      }
    };
    reader.readAsText(file);
  };

  // Render component
  return (
    <div className="space-y-4">
      {/* Header section */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">OpenRouter API Keys</h3>
          <div className="flex space-x-2">
            {/* Export button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportApiKeys}
              disabled={apiKeys.length === 0 || exportApiKeysMutation.isPending}
            >
              {exportApiKeysMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              Export
            </Button>

            {/* Import button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setImportDialogOpen(true)}
            >
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>

            {/* Reset exhausted keys button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleResetExhaustedKeys()}
              disabled={resetExhaustedKeysMutation.isPending}
            >
              {resetExhaustedKeysMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Reset Rate Limits
            </Button>

            {/* Add key dialog */}
            <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add API Key
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New OpenRouter API Key</DialogTitle>
                  <DialogDescription>
                    Add a new OpenRouter API key to use with the application. You can get your API key from <a href="https://openrouter.ai/keys" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">OpenRouter.ai</a>.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name (Optional)</Label>
                    <Input
                      id="name"
                      placeholder="My OpenRouter API Key"
                      value={newKeyName}
                      onChange={(e) => setNewKeyName(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="token">API Key Token</Label>
                    <Input
                      id="token"
                      placeholder="sk-or-v1-..."
                      value={newKeyToken}
                      onChange={(e) => setNewKeyToken(e.target.value)}
                      type="password"
                    />
                    <p className="text-sm text-muted-foreground">
                      Your OpenRouter API key typically starts with "sk-or-v1-".
                    </p>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddApiKey} disabled={addApiKeyMutation.isPending}>
                    {addApiKeyMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    Add API Key
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Manage your OpenRouter API keys. You can add, edit, or delete keys, including the default key. The default key (marked with a star) will be used for API requests.
        </p>
      </div>

      {/* Edit API Key Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit OpenRouter API Key</DialogTitle>
            <DialogDescription>
              Update your OpenRouter API key details. Leave the token field empty if you don't want to change the token.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                placeholder="My OpenRouter API Key"
                value={editKeyName}
                onChange={(e) => setEditKeyName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-token">New API Key Token (Optional)</Label>
              <Input
                id="edit-token"
                placeholder="Leave empty to keep the current token"
                value={editKeyToken}
                onChange={(e) => setEditKeyToken(e.target.value)}
                type="password"
              />
              <p className="text-xs text-muted-foreground">
                Leave empty to keep the current token.
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Label htmlFor="edit-active" className="cursor-pointer">Active</Label>
              <Switch
                id="edit-active"
                checked={editKeyActive}
                onCheckedChange={setEditKeyActive}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveEditedApiKey}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Main content - Loading state */}
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : apiKeys.length === 0 ? (
        // Empty state
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-muted-foreground">
              <p>No OpenRouter API keys found. Add your first API key to get started.</p>
              <p className="mt-2 text-sm">
                You can get an API key from <a href="https://openrouter.ai/keys" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">OpenRouter.ai</a>
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        // API keys list
        <div className="space-y-4">
          {apiKeys.map((key) => (
            <Card
              key={key.id}
              className={`${key.is_default ? "border-blue-500" : ""} ${key.exhausted_until && new Date(key.exhausted_until) > new Date() ? "border-amber-500" : ""}`}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">{key.name || `API Key ${key.id}`}</CardTitle>
                      {key.is_default && (
                        <Badge variant="outline" className="text-blue-500 border-blue-500 flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          Default
                        </Badge>
                      )}
                    </div>
                    <CardDescription className="font-mono text-xs mt-1">
                      {key.masked_token}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-2">
                      <Badge variant={key.is_active ? "default" : "outline"}>
                        {key.is_active ? "Active" : "Inactive"}
                      </Badge>
                      {key.exhausted_until && new Date(key.exhausted_until) > new Date() && (
                        <Badge variant="outline" className="text-amber-500 border-amber-500">
                          Rate Limited
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pb-2 pt-0">
                <div className="text-sm text-muted-foreground grid grid-cols-2 gap-2">
                  <div>
                    <span className="font-medium">Created:</span> {formatDate(key.created_at)}
                  </div>
                  <div>
                    <span className="font-medium">Last used:</span> {formatDate(key.last_used)}
                  </div>
                  {key.exhausted_until && new Date(key.exhausted_until) > new Date() && (
                    <div className="col-span-2 text-amber-500">
                      <span className="font-medium">Rate limited until:</span> {formatDate(key.exhausted_until)}
                      {key.rate_limit_type && (
                        <span className="ml-2 text-xs">({key.rate_limit_type})</span>
                      )}
                    </div>
                  )}
                </div>
                {testResults[key.id] && (
                  <div className={`mt-2 text-sm ${testResults[key.id].success ? "text-green-500" : "text-red-500"}`}>
                    {testResults[key.id].loading ? (
                      <div className="flex items-center">
                        <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                        Testing API key...
                      </div>
                    ) : (
                      <div className="space-y-1">
                        <div className="flex items-center">
                          {testResults[key.id].success ? (
                            <Check className="h-3 w-3 mr-2" />
                          ) : (
                            <X className="h-3 w-3 mr-2" />
                          )}
                          {testResults[key.id].message}
                        </div>

                        {/* Show rate limit information if detected */}
                        {testResults[key.id].rateLimitInfo?.detected && (
                          <div className="ml-5 text-amber-500 text-xs">
                            <div>
                              <span className="font-medium">Rate limit type:</span> {testResults[key.id].rateLimitInfo?.type || "Unknown"}
                            </div>
                            {testResults[key.id].rateLimitInfo?.resetTime && (
                              <div>
                                <span className="font-medium">Reset time:</span> {formatDate(testResults[key.id].rateLimitInfo?.resetTime)}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
              <CardFooter className="pt-2">
                <div className="flex justify-between w-full">
                  <div className="flex items-center space-x-2">
                    <Label htmlFor={`active-${key.id}`} className="cursor-pointer">Active</Label>
                    <Switch
                      id={`active-${key.id}`}
                      checked={key.is_active}
                      onCheckedChange={() => handleToggleApiKeyActive(key.id, key.is_active)}
                    />
                  </div>
                  <div className="flex space-x-2">
                    {!key.is_default && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetAsDefault(key.id)}
                        title="Set as default API key"
                      >
                        <Star className="h-4 w-4 mr-2" />
                        Set Default
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTestApiKey(key.id)}
                      disabled={testResults[key.id]?.loading}
                    >
                      {testResults[key.id]?.loading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4 mr-2" />
                      )}
                      Test
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleOpenEditDialog(key)}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteApiKey(key.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Import API Keys Dialog */}
      <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Import OpenRouter API Keys</DialogTitle>
            <DialogDescription>
              Import OpenRouter API keys from a JSON file or paste JSON directly.
            </DialogDescription>
          </DialogHeader>

          <Tabs value={importTab} onValueChange={setImportTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="json">Paste JSON</TabsTrigger>
              <TabsTrigger value="file">Upload File</TabsTrigger>
            </TabsList>

            <TabsContent value="json" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="import-json">Paste JSON</Label>
                <Textarea
                  id="import-json"
                  placeholder='{"version":"1.0","keys":[{"name":"My Key","token":"sk-or-v1-...","is_active":true}]}'
                  value={importJson}
                  onChange={(e) => setImportJson(e.target.value)}
                  className="min-h-[200px] font-mono text-sm"
                />
                <p className="text-sm text-muted-foreground">
                  Paste a valid JSON object containing OpenRouter API keys.
                </p>
              </div>
            </TabsContent>

            <TabsContent value="file" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="import-file">Upload JSON File</Label>
                <div className="border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center">
                  <FileJson className="h-8 w-8 mb-2 text-muted-foreground" />
                  <p className="mb-2 text-sm font-medium">Click to upload or drag and drop</p>
                  <p className="text-xs text-muted-foreground mb-4">JSON file (.json)</p>
                  <input
                    type="file"
                    id="import-file"
                    accept=".json,application/json"
                    onChange={handleFileUpload}
                    ref={fileInputRef}
                    className="hidden"
                  />
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="w-full max-w-xs"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Select File
                  </Button>
                  {importJson && (
                    <p className="mt-2 text-sm text-green-600">File loaded successfully</p>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setImportDialogOpen(false);
              setImportJson("");
              setImportTab("json");
            }}>
              Cancel
            </Button>
            <Button
              onClick={handleImportApiKeys}
              disabled={!importJson.trim() || importApiKeysMutation.isPending}
            >
              {importApiKeysMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Check className="h-4 w-4 mr-2" />
              )}
              Import Keys
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default OpenRouterApiKeyManager;
