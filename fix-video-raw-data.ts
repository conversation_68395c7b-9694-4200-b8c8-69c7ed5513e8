import Database from 'better-sqlite3';

/**
 * <PERSON>ript to fix OpenRouter raw data for a specific video
 */
async function fixVideoRawData() {
  console.log('Starting to fix OpenRouter raw data for specific video...');

  try {
    // Connect to the database
    const db = new Database('../data.db');

    // Get the specific video
    const videoId = 'VTCaO5t2zww'; // The video ID from the logs
    const video = db.prepare(`
      SELECT id, title, financial_score, financial_amount
      FROM youtube_videos
      WHERE id = ?
    `).get(videoId);

    if (!video) {
      console.error(`Video ${videoId} not found`);
      db.close();
      return;
    }

    console.log(`Found video: ${video.id} - ${video.title}`);
    console.log(`Financial score: ${video.financial_score}`);
    console.log(`Financial amount: ${video.financial_amount}`);

    // Create a simple raw data object based on the existing financial data
    const rawData = {
      hasBenefit: video.financial_score > 30,
      certaintyScore: video.financial_score,
      benefitType: "SNAP/EBT Benefits",
      benefitDescription: video.financial_amount || "Food stamp changes and updates",
      extractedInfo: {
        benefitAmounts: ["$120"],
        expectedArrivalDate: "April 2025",
        eligiblePeople: "SNAP/EBT recipients",
        proofOrSource: "Government announcements",
        actionsToClaim: "Check with local SNAP office"
      },
      priorityTag: video.financial_score >= 70 ? "high" : video.financial_score >= 50 ? "medium" : "low",
      score: video.financial_score,
      reasoning: "The video discusses several changes and updates to SNAP/EBT programs",
      modelUsed: "google/gemini-2.0-flash-exp:free"
    };

    // Convert to JSON string
    const rawDataJson = JSON.stringify(rawData, null, 2);
    console.log('Created raw data:', rawDataJson);

    // Update the video with the raw data
    const updateStmt = db.prepare(`
      UPDATE youtube_videos
      SET openrouter_raw_data = ?
      WHERE id = ?
    `);

    const result = updateStmt.run(rawDataJson, videoId);
    console.log(`Update result: ${result.changes} rows affected`);

    // Verify the update
    const updatedVideo = db.prepare(`
      SELECT id, openrouter_raw_data
      FROM youtube_videos
      WHERE id = ?
    `).get(videoId);

    if (updatedVideo && updatedVideo.openrouter_raw_data) {
      console.log('Successfully updated raw data for video');
    } else {
      console.log('Failed to update raw data for video');
    }

    console.log('Finished fixing OpenRouter raw data');
    db.close();
  } catch (error) {
    console.error('Error fixing OpenRouter raw data:', error);
  }
}

// Run the function
fixVideoRawData();
