import { Video } from "@shared/schema";

/**
 * Calculate an engagement score for a video based on multiple factors
 * @param video The video to calculate the score for
 * @returns A score between 0 and 1, where higher is more engaging
 */
export const calculateEngagementScore = (video: Video): number => {
  try {
    // Primary factors with weights
    const weights = {
      viewsPerHour: 0.40,  // Most important - indicates current momentum
      recency: 0.30,       // Fresher content gets priority
      viewCount: 0.30      // Overall popularity
    };

    // Calculate hours since publication
    let hoursElapsed = 1;
    try {
      if (video.publishedAt) {
        hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
      }
    } catch (e) {
      console.error('Error calculating hours elapsed:', e);
    }

    // Calculate views per hour
    const viewsPerHour = (video.viewCount || 0) / hoursElapsed;

    // Normalize values to 0-1 range
    const normalizedScores = {
      viewsPerHour: Math.min(viewsPerHour / 1000, 1),  // Cap at 1000 views/hour
      recency: Math.max(0, 1 - (hoursElapsed / (7 * 24))),  // 1.0 for new, 0.0 for week-old
      viewCount: Math.min((video.viewCount || 0) / 1000000, 1)  // Cap at 1M views
    };

    // Calculate weighted score
    return Object.keys(weights).reduce((score, factor) => {
      return score + (normalizedScores[factor as keyof typeof normalizedScores] * weights[factor as keyof typeof weights]);
    }, 0);
  } catch (e) {
    console.error('Error calculating engagement score:', e);
    return 0;
  }
};

/**
 * Calculate momentum score based on views per hour
 * @param video The video to calculate the score for
 * @returns A score between 0 and 1, where higher is more momentum
 */
export const calculateMomentumScore = (video: Video): number => {
  try {
    // Calculate hours since publication
    let hoursElapsed = 1;
    try {
      if (video.publishedAt) {
        hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
      }
    } catch (e) {
      console.error('Error calculating hours elapsed for momentum:', e);
    }

    // Calculate views per hour (velocity)
    const viewsPerHour = (video.viewCount || 0) / hoursElapsed;

    // Normalize to 0-1 (assuming 1000+ views/hour is very high)
    return Math.min(viewsPerHour / 1000, 1);
  } catch (e) {
    console.error('Error calculating momentum score:', e);
    return 0;
  }
};

/**
 * Analyze title quality based on patterns that indicate valuable content
 * @param video The video to analyze
 * @returns A score between 0 and 1, where higher indicates better title quality
 */
export const analyzeTitleQuality = (video: Video): number => {
  const title = video.title.toLowerCase();

  // Check for clickbait patterns
  const clickbaitPatterns = [
    /you won't believe/i,
    /shocking/i,
    /mind-blowing/i,
    /\?\?\?+/,
    /!{3,}/
  ];

  const clickbaitScore = clickbaitPatterns.reduce((score, pattern) =>
    pattern.test(title) ? score + 0.2 : score, 0);

  // Check for educational/valuable content indicators
  const valuePatterns = [
    /how to/i,
    /learn/i,
    /guide/i,
    /tutorial/i,
    /explained/i,
    /review/i,
    /analysis/i
  ];

  const valueScore = valuePatterns.reduce((score, pattern) =>
    pattern.test(title) ? score + 0.15 : score, 0);

  // Check for specificity (specific titles often indicate better content)
  const specificityScore = title.length > 40 ? 0.2 : 0;

  // Calculate final score (higher is better)
  return Math.min(Math.max(0.5 + valueScore + specificityScore - clickbaitScore, 0), 1);
};

/**
 * Detect evergreen content based on sustained views over time
 * @param video The video to analyze
 * @returns A score between 0 and 1, where higher indicates more evergreen content
 */
export const detectEvergreen = (video: Video): number => {
  try {
    // Calculate days since publication
    let daysElapsed = 1;
    try {
      if (video.publishedAt) {
        daysElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60 * 24));
      }
    } catch (e) {
      console.error('Error calculating days elapsed for evergreen:', e);
    }

    // Calculate sustained views per day
    const viewsPerDay = (video.viewCount || 0) / daysElapsed;

    // Older videos with high views per day are likely evergreen
    if (daysElapsed > 90) { // 3+ months old
      // Normalize to 0-1 (assuming 1000+ views/day for an old video is excellent)
      const sustainedPopularity = Math.min(viewsPerDay / 1000, 1);
      return sustainedPopularity;
    }

    // Newer videos can't be classified as evergreen yet
    return Math.min(viewsPerDay / 5000, 0.5); // Cap newer videos at 0.5
  } catch (e) {
    console.error('Error detecting evergreen:', e);
    return 0;
  }
};

/**
 * Calculate trending velocity based on recent performance
 * @param video The video to analyze
 * @returns A score between 0 and 1, where higher indicates stronger trending
 */
export const calculateTrendingVelocity = (video: Video): number => {
  try {
    // Calculate hours since publication
    let hoursElapsed = 1;
    try {
      if (video.publishedAt) {
        hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
      }
    } catch (e) {
      console.error('Error calculating hours elapsed for trending velocity:', e);
    }

    // Recent videos (less than 48 hours old)
    if (hoursElapsed < 48) {
      // Calculate views per hour
      const viewsPerHour = (video.viewCount || 0) / hoursElapsed;

      // Normalize to 0-1 (assuming 500+ views/hour is very high)
      return Math.min(viewsPerHour / 500, 1);
    }

    // Older videos get lower trending scores
    return Math.min(((video.viewCount || 0) / hoursElapsed) / 200, 1);
  } catch (e) {
    console.error('Error calculating trending velocity:', e);
    return 0;
  }
};

/**
 * Calculate a "fresh and trending" score that balances recency and views per hour
 * @param video The video to calculate the score for
 * @returns A score between 0 and 1, where higher is more engaging
 */
export const calculateFreshTrendingScore = (video: Video): number => {
  try {
    let hoursElapsed = 1;
    try {
      if (video.publishedAt) {
        hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
      }
    } catch (e) {
      console.error('Error calculating hours elapsed for fresh trending:', e);
    }

    const viewsPerHour = (video.viewCount || 0) / hoursElapsed;

    // Normalize values
    const recencyScore = Math.max(0, 1 - (Math.min(hoursElapsed, 168) / 168)); // 1.0 for new, 0.0 for week-old
    const trendingScore = Math.min(viewsPerHour / 1000, 1); // Cap at 1000 views/hour

    // Weight: 40% recency, 60% trending
    return (recencyScore * 0.4) + (trendingScore * 0.6);
  } catch (e) {
    console.error('Error calculating fresh trending score:', e);
    return 0;
  }
};

/**
 * Calculate a fresh and valuable score combining recency and quality
 * @param video The video to analyze
 * @returns A score between 0 and 1, where higher indicates fresher and more valuable content
 */
export const calculateFreshValueScore = (video: Video): number => {
  try {
    // Calculate recency (1.0 for new, declining with age)
    let daysElapsed = 1;
    try {
      if (video.publishedAt) {
        daysElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60 * 24));
      }
    } catch (e) {
      console.error('Error calculating days elapsed for fresh value:', e);
    }

    const recencyScore = Math.max(0, 1 - (daysElapsed / 14)); // 2-week window

    // Calculate engagement velocity
    const viewsPerDay = (video.viewCount || 0) / daysElapsed;
    const velocityScore = Math.min(viewsPerDay / 10000, 1);

    // Calculate title quality
    const qualityScore = analyzeTitleQuality(video);

    // Combine with weights
    return (recencyScore * 0.3) + (velocityScore * 0.4) + (qualityScore * 0.3);
  } catch (e) {
    console.error('Error calculating fresh value score:', e);
    return 0;
  }
};

/**
 * Calculate a comprehensive quality score
 * @param video The video to analyze
 * @returns A score between 0 and 1, where higher indicates better overall quality
 */
export const calculateComprehensiveQuality = (video: Video): number => {
  // Calculate individual metrics
  const momentumScore = calculateMomentumScore(video);
  const titleQualityScore = analyzeTitleQuality(video);
  const evergreenScore = detectEvergreen(video);
  const trendingScore = calculateTrendingVelocity(video);

  // Weights for different factors
  const weights = {
    momentum: 0.25,
    titleQuality: 0.30,
    evergreen: 0.20,
    trending: 0.25
  };

  // Calculate weighted score
  return (
    momentumScore * weights.momentum +
    titleQualityScore * weights.titleQuality +
    evergreenScore * weights.evergreen +
    trendingScore * weights.trending
  );
};

/**
 * Calculate views per hour for a video
 * @param video The video to calculate views per hour for
 * @returns The number of views per hour
 */
export const calculateViewsPerHour = (video: Video): number => {
  try {
    let hoursElapsed = 1;
    try {
      if (video.publishedAt) {
        hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
      }
    } catch (e) {
      console.error('Error calculating hours elapsed for views per hour:', e);
    }

    return (video.viewCount || 0) / hoursElapsed;
  } catch (e) {
    console.error('Error calculating views per hour:', e);
    return 0;
  }
};

/**
 * Enhance a video with additional engagement metrics
 * @param video The video to enhance
 * @returns The video with additional engagement metrics
 */
export const enhanceVideo = (video: Video) => {
  try {
    const engagementScore = calculateEngagementScore(video);
    const freshTrendingScore = calculateFreshTrendingScore(video);
    const momentumScore = calculateMomentumScore(video);
    const titleQualityScore = analyzeTitleQuality(video);
    const evergreenScore = detectEvergreen(video);
    const trendingScore = calculateTrendingVelocity(video);
    const qualityScore = calculateComprehensiveQuality(video);
    const freshValueScore = calculateFreshValueScore(video);
    const viewsPerHour = calculateViewsPerHour(video);

    return {
      ...video,
      engagementScore,
      freshTrendingScore,
      momentumScore,
      titleQualityScore,
      evergreenScore,
      trendingScore,
      qualityScore,
      freshValueScore,
      viewsPerHour
    };
  } catch (e) {
    console.error('Error enhancing video:', e);
    return video;
  }
};

/**
 * Sort videos based on the specified sort method
 * @param videos The videos to sort
 * @param sortMethod The method to sort by
 * @returns The sorted videos
 */
export const sortVideos = (videos: Video[], sortMethod: string): Video[] => {
  try {
    // Make a copy of the videos array to avoid mutating the original
    const videosCopy = [...videos];

    // Enhance videos with additional metrics
    const enhancedVideos = videosCopy.map(enhanceVideo);

    console.log(`Sorting ${enhancedVideos.length} videos using method: ${sortMethod}`);

    // If no videos, return empty array
    if (enhancedVideos.length === 0) {
      console.log('No videos to sort');
      return [];
    }

    // Log the first video before sorting
    if (enhancedVideos.length > 0) {
      console.log('First video before sorting:', {
        title: enhancedVideos[0].title,
        publishedAt: enhancedVideos[0].publishedAt,
        viewCount: enhancedVideos[0].viewCount
      });
    }

  // Sort based on selected method
  // Helper function to perform sort and log results
  const performSort = (sortFn: (a: any, b: any) => number, label: string) => {
    try {
      console.log(`Performing ${label} sort`);
      const result = [...enhancedVideos].sort(sortFn);

      // Log a sample of the sorted results
      if (result.length > 0) {
        console.log(`${label} sort - First item:`, {
          title: result[0].title,
          publishedAt: result[0].publishedAt,
          viewCount: result[0].viewCount,
          ...(result[0] as any)[label + 'Score'] && { score: (result[0] as any)[label + 'Score'] }
        });

        if (result.length > 1) {
          console.log(`${label} sort - Last item:`, {
            title: result[result.length - 1].title,
            publishedAt: result[result.length - 1].publishedAt,
            viewCount: result[result.length - 1].viewCount,
            ...(result[result.length - 1] as any)[label + 'Score'] && { score: (result[result.length - 1] as any)[label + 'Score'] }
          });
        }
      }

      return result;
    } catch (e) {
      console.error(`Error performing ${label} sort:`, e);
      return enhancedVideos;
    }
  };

  try {
    switch (sortMethod) {
      case "engagement":
        return performSort((a, b) => ((b as any).engagementScore || 0) - ((a as any).engagementScore || 0), "engagement");
      case "fresh_trending":
        return performSort((a, b) => ((b as any).freshTrendingScore || 0) - ((a as any).freshTrendingScore || 0), "freshTrending");
      case "trending":
        return performSort((a, b) => ((b as any).trendingScore || 0) - ((a as any).trendingScore || 0), "trending");
      case "momentum":
        return performSort((a, b) => ((b as any).momentumScore || 0) - ((a as any).momentumScore || 0), "momentum");
      case "evergreen":
        return performSort((a, b) => ((b as any).evergreenScore || 0) - ((a as any).evergreenScore || 0), "evergreen");
      case "quality":
        return performSort((a, b) => ((b as any).qualityScore || 0) - ((a as any).qualityScore || 0), "quality");
      case "fresh_valuable":
        return performSort((a, b) => ((b as any).freshValueScore || 0) - ((a as any).freshValueScore || 0), "freshValue");
      case "views_per_hour":
        return performSort((a, b) => ((b as any).viewsPerHour || 0) - ((a as any).viewsPerHour || 0), "viewsPerHour");
      case "date_asc":
        return performSort((a, b) => {
          try {
            return new Date(a.publishedAt || 0).getTime() - new Date(b.publishedAt || 0).getTime();
          } catch (e) {
            return 0;
          }
        }, "dateAsc");
      case "date_desc":
        return performSort((a, b) => {
          try {
            return new Date(b.publishedAt || 0).getTime() - new Date(a.publishedAt || 0).getTime();
          } catch (e) {
            return 0;
          }
        }, "dateDesc");
      case "views_asc":
        return performSort((a, b) => (a.viewCount || 0) - (b.viewCount || 0), "viewsAsc");
      case "views_desc":
        return performSort((a, b) => (b.viewCount || 0) - (a.viewCount || 0), "viewsDesc");
      // No duration sorting
      default:
        console.log(`Unknown sort method: ${sortMethod}, returning unsorted videos`);
        return enhancedVideos;
    }
  } catch (e) {
    console.error('Error in sortVideos switch statement:', e);
    return enhancedVideos;
  }
} catch (e) {
  console.error('Error in sortVideos function:', e);
  return videos;
}
};
