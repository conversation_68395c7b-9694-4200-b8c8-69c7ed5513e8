import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

// Hook to test OpenRouter connection
export const useTestOpenRouterConnection = () => {
  return useQuery({
    queryKey: ['openrouter-connection-test'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/openrouter/test-connection');
      return await response.json();
    },
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });
};

// Hook to get available OpenRouter models
export const useOpenRouterModels = () => {
  return useQuery({
    queryKey: ['openrouter-models'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/openrouter/models');
      return await response.json();
    },
    retry: 1,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
  });
};

// Hook to analyze a video with OpenRouter
export const useAnalyzeVideoWithOpenRouter = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (videoId: string) => {
      // First try the task-based approach
      try {
        console.log('Queueing OpenRouter analysis task for video:', videoId);
        const response = await apiRequest('POST', '/api/tasks/analyze-openrouter', { videoId });
        if (!response.ok) {
          console.log('Task-based analysis failed, falling back to direct API call');
          throw new Error('Task creation failed');
        }
        const taskData = await response.json();
        console.log('OpenRouter analysis task created:', taskData);
        return { taskId: taskData.id, message: 'Analysis task queued successfully' };
      } catch (taskError) {
        // Fall back to direct API call
        console.log('Falling back to direct OpenRouter API call for video:', videoId);
        const response = await apiRequest('GET', `/api/openrouter/analyze-youtube-video/${videoId}`);
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'OpenRouter error: Failed to analyze video');
        }
        return await response.json();
      }
    },
    onError: (error) => {
      console.error('Error analyzing video with OpenRouter:', error);
      toast({
        title: 'AI Analysis Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive',
      });
    }
  });
};

// Hook to analyze all videos in a channel with OpenRouter
export const useAnalyzeChannelWithOpenRouter = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (channelId: number) => {
      const response = await apiRequest('POST', `/api/openrouter/analyze-channel/${channelId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'OpenRouter error: Failed to analyze channel');
      }
      return await response.json();
    },
    onError: (error) => {
      console.error('Error analyzing channel with OpenRouter:', error);
      toast({
        title: 'AI Channel Analysis Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: 'destructive',
      });
    }
  });
};
