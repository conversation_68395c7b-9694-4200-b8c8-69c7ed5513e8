/* MUI Dialog Dark Mode Overrides - Specific selectors */

/* Dialog Paper */
.dark .MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation24.MuiDialog-paper.MuiDialog-paperScrollPaper.MuiDialog-paperWidthMd.MuiDialog-paperFullWidth,
.dark div[class*="MuiPaper-root"][class*="MuiDialog-paper"],
.dark .css-1peo4km-MuiPaper-root-MuiDialog-paper {
  background-color: #1e1e1e !important;
  color: white !important;
  box-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0.5), 
              0px 24px 38px 3px rgba(0, 0, 0, 0.4), 
              0px 9px 46px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Dialog Title */
.dark .MuiTypography-root.MuiTypography-h6.MuiDialogTitle-root,
.dark .css-rvoa5x-MuiTypography-root-MuiDialogTitle-root {
  background-color: #1e1e1e !important;
  color: white !important;
  border-bottom: 1px solid #333 !important;
}

/* Dialog Content */
.dark .MuiDialogContent-root,
.dark .css-kw13he-MuiDialogContent-root {
  background-color: #1e1e1e !important;
  color: white !important;
}

/* Dialog Actions */
.dark .MuiDialogActions-root.MuiDialogActions-spacing,
.dark .css-15fu35s-MuiDialogActions-root {
  background-color: #1e1e1e !important;
  border-top: 1px solid #333 !important;
}

/* Form Controls */
.dark .MuiFormControl-root.MuiFormControl-marginDense.MuiFormControl-fullWidth.MuiTextField-root,
.dark .MuiFormControl-root.MuiFormControl-fullWidth.MuiTextField-root,
.dark .css-1fksspf-MuiFormControl-root-MuiTextField-root,
.dark .css-dzmwfx-MuiFormControl-root-MuiTextField-root,
.dark .css-1iw3t7y-MuiFormControl-root {
  color: white !important;
}

/* Input Labels */
.dark .MuiFormLabel-root.MuiInputLabel-root.MuiInputLabel-formControl.MuiInputLabel-animated.MuiInputLabel-shrink.MuiInputLabel-outlined.MuiFormLabel-colorPrimary.MuiFormLabel-filled,
.dark .css-113d811-MuiFormLabel-root-MuiInputLabel-root {
  color: white !important;
}

/* Input Base */
.dark .MuiInputBase-root.MuiOutlinedInput-root.MuiInputBase-colorPrimary.MuiInputBase-fullWidth.MuiInputBase-formControl,
.dark .MuiInputBase-root.MuiOutlinedInput-root.MuiInputBase-colorPrimary.MuiInputBase-fullWidth.MuiInputBase-formControl.MuiInputBase-multiline,
.dark .css-1blp12k-MuiInputBase-root-MuiOutlinedInput-root,
.dark .css-zsp0bh-MuiInputBase-root-MuiOutlinedInput-root {
  background-color: #2a2a2a !important;
  color: white !important;
}

/* Input */
.dark .MuiInputBase-input.MuiOutlinedInput-input,
.dark .MuiInputBase-input.MuiOutlinedInput-input.MuiInputBase-inputMultiline,
.dark .css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input,
.dark .css-w4nesw-MuiInputBase-input-MuiOutlinedInput-input {
  color: white !important;
}

/* Outlined Input Notch */
.dark .MuiOutlinedInput-notchedOutline,
.dark .css-1ll44ll-MuiOutlinedInput-notchedOutline {
  border-color: #555 !important;
}

/* Legend */
.dark .css-w1u3ce {
  color: white !important;
}

/* Checkbox */
.dark input[type="checkbox"] {
  accent-color: #3b82f6 !important;
}

/* Label */
.dark label {
  color: white !important;
}

/* Buttons */
.dark .MuiButtonBase-root.MuiButton-root.MuiButton-text.MuiButton-textPrimary.MuiButton-sizeMedium.MuiButton-textSizeMedium.MuiButton-colorPrimary,
.dark .css-1uent87-MuiButtonBase-root-MuiButton-root {
  color: white !important;
}

.dark .MuiButtonBase-root.MuiButton-root.MuiButton-contained.MuiButton-containedPrimary.MuiButton-sizeMedium.MuiButton-containedSizeMedium.MuiButton-colorPrimary,
.dark .css-74d805-MuiButtonBase-root-MuiButton-root {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Hover states */
.dark .MuiButtonBase-root.MuiButton-root:hover {
  opacity: 0.9 !important;
}

/* Focus states */
.dark .MuiInputBase-root.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #3b82f6 !important;
  border-width: 2px !important;
}

/* Textarea */
.dark textarea.MuiInputBase-input.MuiOutlinedInput-input.MuiInputBase-inputMultiline {
  color: white !important;
}
