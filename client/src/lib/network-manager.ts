// network-manager.ts
// This utility manages network connectivity and prevents excessive retries

// No toast import needed, we'll handle toasts in components

// Network status types
export enum NetworkStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  RECONNECTING = 'reconnecting',
  UNSTABLE = 'unstable'
}

// Interface for network state
interface NetworkState {
  status: NetworkStatus;
  lastOnlineTime: number | null;
  lastOfflineTime: number | null;
  reconnectAttempts: number;
  failedRequests: number;
  consecutiveFailures: number;
  backoffDelay: number;
  isReconnecting: boolean;
  isBackoffActive: boolean;
  backoffEndTime: number | null;
}

// Class to manage network connectivity
export class NetworkManager {
  private static instance: NetworkManager;
  private state: NetworkState;
  private listeners: Set<(status: NetworkStatus) => void>;
  private reconnectTimer: number | null = null;
  private checkConnectionTimer: number | null = null;
  private toastShown: boolean = false;
  private toastId: string | null = null;

  // Maximum number of reconnect attempts before giving up
  private readonly MAX_RECONNECT_ATTEMPTS = 5;

  // Maximum backoff delay in milliseconds (5 minutes)
  private readonly MAX_BACKOFF_DELAY = 5 * 60 * 1000;

  // Initial backoff delay in milliseconds (5 seconds)
  private readonly INITIAL_BACKOFF_DELAY = 5 * 1000;

  // Backoff multiplier (exponential backoff)
  private readonly BACKOFF_MULTIPLIER = 2;

  // Threshold for consecutive failures to trigger backoff
  private readonly FAILURE_THRESHOLD = 3;

  private constructor() {
    this.state = {
      status: navigator.onLine ? NetworkStatus.ONLINE : NetworkStatus.OFFLINE,
      lastOnlineTime: navigator.onLine ? Date.now() : null,
      lastOfflineTime: navigator.onLine ? null : Date.now(),
      reconnectAttempts: 0,
      failedRequests: 0,
      consecutiveFailures: 0,
      backoffDelay: this.INITIAL_BACKOFF_DELAY,
      isReconnecting: false,
      isBackoffActive: false,
      backoffEndTime: null
    };

    this.listeners = new Set();

    // Set up event listeners for online/offline events
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // Initial check
    this.checkConnection();
  }

  // Get singleton instance
  public static getInstance(): NetworkManager {
    if (!NetworkManager.instance) {
      NetworkManager.instance = new NetworkManager();
    }
    return NetworkManager.instance;
  }

  // Get current network status
  public getStatus(): NetworkStatus {
    return this.state.status;
  }

  // Check if network is available for requests
  public isNetworkAvailable(): boolean {
    // If we're in backoff mode, check if the backoff period has ended
    if (this.state.isBackoffActive && this.state.backoffEndTime) {
      if (Date.now() >= this.state.backoffEndTime) {
        // Backoff period has ended, reset backoff state
        this.resetBackoff();
      } else {
        // Still in backoff period
        return false;
      }
    }

    return this.state.status === NetworkStatus.ONLINE;
  }

  // Get the time to wait before retrying (in milliseconds)
  public getRetryDelay(): number {
    return this.state.backoffDelay;
  }

  // Register a listener for network status changes
  public addListener(listener: (status: NetworkStatus) => void): void {
    this.listeners.add(listener);
  }

  // Remove a listener
  public removeListener(listener: (status: NetworkStatus) => void): void {
    this.listeners.delete(listener);
  }

  // Handle successful request
  public handleRequestSuccess(): void {
    // Reset consecutive failures counter
    this.state.consecutiveFailures = 0;

    // If we were in unstable state, go back to online
    if (this.state.status === NetworkStatus.UNSTABLE) {
      this.updateStatus(NetworkStatus.ONLINE);
    }
  }

  // Handle failed request
  public handleRequestFailure(error: any): void {
    // Increment failed requests counter
    this.state.failedRequests++;
    this.state.consecutiveFailures++;

    // Check if the error is a network error
    const isNetworkError = this.isNetworkError(error);

    // If we have consecutive failures above threshold, enter backoff mode
    if (this.state.consecutiveFailures >= this.FAILURE_THRESHOLD && isNetworkError) {
      this.enterBackoffMode();
    }

    // If we're online but getting network errors, mark as unstable
    if (this.state.status === NetworkStatus.ONLINE && isNetworkError) {
      this.updateStatus(NetworkStatus.UNSTABLE);
      this.checkConnection();
    }
  }

  // Reset the network manager state
  public reset(): void {
    this.state.reconnectAttempts = 0;
    this.state.failedRequests = 0;
    this.state.consecutiveFailures = 0;
    this.resetBackoff();

    // Update status based on navigator.onLine
    this.updateStatus(navigator.onLine ? NetworkStatus.ONLINE : NetworkStatus.OFFLINE);

    // Clear any pending timers
    if (this.reconnectTimer !== null) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.checkConnectionTimer !== null) {
      window.clearTimeout(this.checkConnectionTimer);
      this.checkConnectionTimer = null;
    }
  }

  // Private methods

  // Handle online event
  private handleOnline(): void {
    this.state.lastOnlineTime = Date.now();
    this.updateStatus(NetworkStatus.ONLINE);
    this.resetBackoff();

    // Hide offline toast if shown
    if (this.toastShown && this.toastId) {
      // We'll handle toasts in the components that use this manager
      console.info('Connection Restored: Your internet connection has been restored.');
      this.toastShown = false;
      this.toastId = null;
    }
  }

  // Handle offline event
  private handleOffline(): void {
    this.state.lastOfflineTime = Date.now();
    this.updateStatus(NetworkStatus.OFFLINE);

    // Show offline toast if not already shown
    if (!this.toastShown) {
      this.toastId = Date.now().toString();
      // We'll handle toasts in the components that use this manager
      console.warn('Connection Lost: Your internet connection appears to be offline. Some features may be unavailable.');
      this.toastShown = true;
    }
  }

  // Update network status and notify listeners
  private updateStatus(status: NetworkStatus): void {
    if (this.state.status !== status) {
      this.state.status = status;

      // Notify all listeners
      this.listeners.forEach(listener => {
        try {
          listener(status);
        } catch (error) {
          console.error('Error in network status listener:', error);
        }
      });
    }
  }

  // Check connection by making a ping request
  private checkConnection(): void {
    // Clear any existing timer
    if (this.checkConnectionTimer !== null) {
      window.clearTimeout(this.checkConnectionTimer);
      this.checkConnectionTimer = null;
    }

    // If we're already offline, no need to check
    if (this.state.status === NetworkStatus.OFFLINE) {
      return;
    }

    // Make a ping request to check connection
    fetch('/ping', {
      method: 'HEAD',
      cache: 'no-store',
      headers: { 'Cache-Control': 'no-cache' }
    })
      .then(() => {
        // Connection is good
        this.updateStatus(NetworkStatus.ONLINE);
        this.state.consecutiveFailures = 0;
      })
      .catch(() => {
        // Connection failed
        this.state.consecutiveFailures++;

        if (this.state.consecutiveFailures >= this.FAILURE_THRESHOLD) {
          this.updateStatus(NetworkStatus.OFFLINE);
        } else {
          this.updateStatus(NetworkStatus.UNSTABLE);
        }

        // Schedule another check with exponential backoff
        const delay = Math.min(
          this.INITIAL_BACKOFF_DELAY * Math.pow(this.BACKOFF_MULTIPLIER, this.state.consecutiveFailures - 1),
          this.MAX_BACKOFF_DELAY
        );

        this.checkConnectionTimer = window.setTimeout(() => {
          this.checkConnection();
        }, delay);
      });
  }

  // Enter backoff mode to prevent excessive retries
  private enterBackoffMode(): void {
    // Only enter backoff mode if not already in it
    if (!this.state.isBackoffActive) {
      this.state.isBackoffActive = true;

      // Calculate backoff delay with exponential backoff
      this.state.backoffDelay = Math.min(
        this.INITIAL_BACKOFF_DELAY * Math.pow(this.BACKOFF_MULTIPLIER, this.state.consecutiveFailures - 1),
        this.MAX_BACKOFF_DELAY
      );

      // Set backoff end time
      this.state.backoffEndTime = Date.now() + this.state.backoffDelay;

      console.log(`Entering network backoff mode for ${this.state.backoffDelay / 1000} seconds`);

      // Log instead of showing toast (we'll handle toasts in components)
      console.warn(`Connection Issues: Reducing network requests for ${Math.round(this.state.backoffDelay / 1000)} seconds due to connection issues.`);
    }
  }

  // Reset backoff state
  private resetBackoff(): void {
    this.state.isBackoffActive = false;
    this.state.backoffDelay = this.INITIAL_BACKOFF_DELAY;
    this.state.backoffEndTime = null;
  }

  // Check if an error is a network-related error
  private isNetworkError(error: any): boolean {
    if (!error) return false;

    // Check for common network error patterns
    const errorString = String(error).toLowerCase();
    const networkErrorPatterns = [
      'network error',
      'failed to fetch',
      'network request failed',
      'connection refused',
      'connection reset',
      'connection timed out',
      'timeout',
      'abort',
      'net::err',
      'disconnected'
    ];

    return networkErrorPatterns.some(pattern => errorString.includes(pattern));
  }
}

// Export a singleton instance
export const networkManager = NetworkManager.getInstance();

// React hook for using the network manager
export function useNetworkManager() {
  return networkManager;
}
