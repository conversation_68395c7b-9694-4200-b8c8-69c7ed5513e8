import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Video } from '@shared/schema';
import { useToast } from './use-toast';
import { apiRequest, queryClient } from '@/lib/queryClient';

// Define playlist types
export interface Playlist {
  id: number;
  userId: number;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PlaylistVideo {
  id: number;
  playlistId: number;
  videoId: string;
  title: string;
  thumbnail: string;
  channelTitle: string;
  viewCount: number;
  publishedAt: Date;
  addedAt: Date;
}

export function usePlaylists(selectedPlaylistId: number | null = null) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all playlists
  const {
    data: playlists = [],
    isLoading,
    error,
    refetch,
  } = useQuery<Playlist[]>({
    queryKey: ['/api/playlists'],
  });

  // Create a new playlist
  const createPlaylist = useMutation({
    mutationFn: async ({ name, description }: { name: string; description?: string }) => {
      const res = await apiRequest('POST', '/api/playlists', { name, description });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/playlists'] });
      toast({
        title: 'Playlist created',
        description: 'Your playlist has been created successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to create playlist: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Update a playlist
  const updatePlaylist = useMutation({
    mutationFn: async ({ id, name, description }: { id: number; name?: string; description?: string }) => {
      const res = await apiRequest('PUT', `/api/playlists/${id}`, { name, description });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/playlists'] });
      toast({
        title: 'Playlist updated',
        description: 'Your playlist has been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update playlist: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Delete a playlist
  const deletePlaylist = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest('DELETE', `/api/playlists/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/playlists'] });
      toast({
        title: 'Playlist deleted',
        description: 'Your playlist has been deleted successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to delete playlist: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Get videos in a playlist
  const playlistVideosQuery = useQuery<PlaylistVideo[]>({
    queryKey: ['/api/playlist-videos', selectedPlaylistId],
    queryFn: async () => {
      if (!selectedPlaylistId) return [];
      const res = await apiRequest('GET', `/api/playlists/${selectedPlaylistId}/videos`);
      return res.json();
    },
    enabled: !!selectedPlaylistId,
  });

  // Add a video to a playlist
  const addVideoToPlaylist = useMutation({
    mutationFn: async ({ playlistId, video }: { playlistId: number; video: Video }) => {
      const res = await apiRequest('POST', `/api/playlists/${playlistId}/videos`, { video });
      return res.json();
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [`/api/playlists/${variables.playlistId}/videos`] });
      toast({
        title: 'Video added',
        description: 'The video has been added to your playlist.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to add video to playlist: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Remove a video from a playlist
  const removeVideoFromPlaylist = useMutation({
    mutationFn: async ({ playlistId, videoId }: { playlistId: number; videoId: string }) => {
      await apiRequest('DELETE', `/api/playlists/${playlistId}/videos/${videoId}`);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [`/api/playlists/${variables.playlistId}/videos`] });
      toast({
        title: 'Video removed',
        description: 'The video has been removed from your playlist.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to remove video from playlist: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Extract the playlist videos data and loading state
  const {
    data: playlistVideos = [],
    isLoading: isLoadingVideos,
    refetch: refetchVideos
  } = playlistVideosQuery;

  // Function to get videos for a specific playlist
  const getPlaylistVideos = async (playlistId: number) => {
    if (!playlistId) return [];
    const res = await apiRequest('GET', `/api/playlists/${playlistId}/videos`);
    return res.json();
  };

  return {
    playlists,
    isLoading,
    error,
    refetch,
    createPlaylist,
    updatePlaylist,
    deletePlaylist,
    getPlaylistVideos,
    playlistVideos,
    isLoadingVideos,
    refetchVideos,
    addVideoToPlaylist,
    removeVideoFromPlaylist,
  };
}
