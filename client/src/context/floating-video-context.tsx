import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { Video } from '@shared/schema';

// Define the shape of our floating video state
interface FloatingVideoState {
  isOpen: boolean;
  video: Video | null;
  position: { x: number; y: number };
  size: { width: number; height: number };
  isMinimized: boolean;
}

// Define the context type
interface FloatingVideoContextType {
  state: FloatingVideoState;
  openFloatingVideo: (video: Video) => void;
  closeFloatingVideo: () => void;
  updatePosition: (position: { x: number; y: number }) => void;
  updateSize: (size: { width: number; height: number }) => void;
  toggleMinimize: () => void;
  isControlsVisible: boolean;
  setControlsVisible: (visible: boolean) => void;
  isFloatingPlayerActive: boolean;
  playNextVideo: () => void;
  currentFeedVideos: Video[];
  setCurrentFeedVideos: (videos: Video[]) => void;
}

// Storage keys
const STORAGE_KEY = 'floating_video_state';
const CHANNEL_NAME = 'floating_video_channel';

// Get default state from localStorage or use these defaults
const getDefaultState = (): FloatingVideoState => {
  try {
    const savedState = localStorage.getItem(STORAGE_KEY);
    if (savedState) {
      const parsedState = JSON.parse(savedState);
      // Only use position and size from saved state
      return {
        isOpen: false,
        video: null,
        position: parsedState.position || { x: 20, y: 20 },
        size: parsedState.size || { width: 320, height: 180 }, // 16:9 aspect ratio
        isMinimized: false,
      };
    }
  } catch (error) {
    console.error('Failed to parse floating video state from localStorage', error);
  }

  // Default state if nothing in localStorage
  return {
    isOpen: false,
    video: null,
    position: { x: 20, y: 20 },
    size: { width: 320, height: 180 }, // 16:9 aspect ratio
    isMinimized: false,
  };
};

// Create the context with a default value
const FloatingVideoContext = createContext<FloatingVideoContextType>({
  state: getDefaultState(),
  openFloatingVideo: () => {},
  closeFloatingVideo: () => {},
  updatePosition: () => {},
  updateSize: () => {},
  toggleMinimize: () => {},
  isControlsVisible: false,
  setControlsVisible: () => {},
  isFloatingPlayerActive: false,
  playNextVideo: () => {},
  currentFeedVideos: [],
  setCurrentFeedVideos: () => {},
});



// Provider component
export function FloatingVideoProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<FloatingVideoState>(getDefaultState());
  const [isControlsVisible, setControlsVisible] = useState(false);
  const [broadcastChannel, setBroadcastChannel] = useState<BroadcastChannel | null>(null);
  const [currentFeedVideos, setCurrentFeedVideos] = useState<Video[]>([]);

  // Initialize BroadcastChannel for cross-tab communication
  useEffect(() => {
    // Check if BroadcastChannel is supported
    if ('BroadcastChannel' in window) {
      const channel = new BroadcastChannel(CHANNEL_NAME);

      channel.onmessage = (event) => {
        if (event.data.type === 'STATE_UPDATE') {
          setState(event.data.state);
        }
      };

      setBroadcastChannel(channel);

      // Load state from localStorage on mount
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        try {
          const parsedState = JSON.parse(savedState);
          setState(parsedState);
        } catch (error) {
          console.error('Failed to parse floating video state from localStorage', error);
        }
      }

      return () => {
        channel.close();
      };
    } else {
      console.warn('BroadcastChannel API is not supported in this browser');

      // Fallback to just localStorage for browsers without BroadcastChannel
      const savedState = localStorage.getItem(STORAGE_KEY);
      if (savedState) {
        try {
          const parsedState = JSON.parse(savedState);
          setState(parsedState);
        } catch (error) {
          console.error('Failed to parse floating video state from localStorage', error);
        }
      }

      // Set up localStorage event listener for cross-tab communication fallback
      const handleStorageChange = (e: StorageEvent) => {
        if (e.key === STORAGE_KEY && e.newValue) {
          try {
            const newState = JSON.parse(e.newValue);
            setState(newState);
          } catch (error) {
            console.error('Failed to parse floating video state from storage event', error);
          }
        }
      };

      window.addEventListener('storage', handleStorageChange);
      return () => {
        window.removeEventListener('storage', handleStorageChange);
      };
    }
  }, []);

  // Save state to localStorage and broadcast to other tabs
  const updateStateAndBroadcast = (newState: FloatingVideoState) => {
    setState(newState);

    // Save to localStorage
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newState));
    } catch (error) {
      console.error('Failed to save floating video state to localStorage', error);
    }

    // Broadcast to other tabs
    if (broadcastChannel) {
      broadcastChannel.postMessage({
        type: 'STATE_UPDATE',
        state: newState,
      });
    }
  };

  // Open the floating video player
  const openFloatingVideo = (video: Video) => {
    console.log(`Opening floating video: ${video.title}`);

    updateStateAndBroadcast({
      ...state,
      isOpen: true,
      video,
      isMinimized: false,
    });
  };

  // Close the floating video player
  const closeFloatingVideo = () => {
    updateStateAndBroadcast({
      ...state,
      isOpen: false,
      video: null,
    });
  };

  // Update the position of the floating player
  const updatePosition = (position: { x: number; y: number }) => {
    updateStateAndBroadcast({
      ...state,
      position,
    });
  };

  // Update the size of the floating player
  const updateSize = (size: { width: number; height: number }) => {
    updateStateAndBroadcast({
      ...state,
      size,
    });
  };

  // Toggle minimize state
  const toggleMinimize = () => {
    updateStateAndBroadcast({
      ...state,
      isMinimized: !state.isMinimized,
    });
  };

  // Compute if floating player is active
  const isFloatingPlayerActive = state.isOpen && state.video !== null;

  // Play the next video in the feed
  const playNextVideo = useCallback(() => {
    console.log('=== playNextVideo called ===');
    console.log('Current video ID:', state.video?.id);
    console.log('Current video title:', state.video?.title);
    console.log('Feed videos count:', currentFeedVideos.length);
    console.log('Feed videos sample:', currentFeedVideos.slice(0, 3).map(v => ({ id: v.id, title: v.title })));

    if (!state.video || currentFeedVideos.length === 0) {
      console.log('No current video or empty feed, returning');
      return;
    }

    // Find the current video in the feed
    const currentIndex = currentFeedVideos.findIndex(v => v.id === state.video?.id);
    console.log('Current video index in feed:', currentIndex);

    if (currentIndex === -1) {
      console.log('Current video not found in feed, returning');
      return;
    }

    if (currentIndex === currentFeedVideos.length - 1) {
      console.log('Current video is the last video in feed, returning');
      return;
    }

    // Get the next video
    const nextVideo = currentFeedVideos[currentIndex + 1];
    console.log('Next video ID:', nextVideo.id);
    console.log('Next video title:', nextVideo.title);

    if (nextVideo) {
      console.log('Opening next video:', nextVideo.title);
      openFloatingVideo(nextVideo);
    }
  }, [state.video, currentFeedVideos, openFloatingVideo]);

  return (
    <FloatingVideoContext.Provider
      value={{
        state,
        openFloatingVideo,
        closeFloatingVideo,
        updatePosition,
        updateSize,
        toggleMinimize,
        isControlsVisible,
        setControlsVisible,
        isFloatingPlayerActive,
        playNextVideo,
        currentFeedVideos,
        setCurrentFeedVideos,
      }}
    >
      {children}
    </FloatingVideoContext.Provider>
  );
}

// Custom hook to use the floating video context
export function useFloatingVideo() {
  const context = useContext(FloatingVideoContext);
  if (!context) {
    throw new Error('useFloatingVideo must be used within a FloatingVideoProvider');
  }
  return context;
}
