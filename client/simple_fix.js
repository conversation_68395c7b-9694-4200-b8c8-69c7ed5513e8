#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Read the file
const filePath = path.join('src', 'pages', 'youtube-page.tsx');
const content = fs.readFileSync(filePath, 'utf8');

// Find the problematic section
const problemStart = content.indexOf('    </div>');
const problemEnd = content.indexOf('    {/* Financial Analysis Dialog */}');

// Create the fixed content
const fixedContent = content.substring(0, problemStart + 10) + 
                    '  );\n}\n' + 
                    content.substring(problemEnd);

// Write the file
fs.writeFileSync(filePath, fixedContent, 'utf8');

console.log('Fixed youtube-page.tsx');
