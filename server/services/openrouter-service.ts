/**
 * OpenRouter Service
 *
 * This service provides functions for interacting with the OpenRouter API.
 */

import { storage } from '../storage';
import { openRouterKeyManager } from './openrouter-key-manager';

// OpenRouter API URLs
const OPENROUTER_API_URLS = [
  'https://openrouter.ai/api/v1',
  'https://api.openrouter.ai/api/v1'
];

// Required headers for OpenRouter API
const OPENROUTER_REQUIRED_HEADERS = {
  'HTTP-Referer': 'https://trendy.local',
  'X-Title': 'Trendy App'
};

// Interface for OpenRouter model information
interface OpenRouterModel {
  id: string;
  name: string;
  description?: string;
  context_length: number;
  pricing: {
    prompt: number;
    completion: number;
  };
}

// Interface for OpenRouter chat completion request
interface OpenRouterChatRequest {
  model: string;
  messages: {
    role: 'system' | 'user' | 'assistant';
    content: string | Array<{type: string, text?: string, image_url?: {url: string}}>;
  }[];
  temperature?: number;
  top_p?: number;
  top_k?: number;
  max_tokens?: number;
}

// Interface for OpenRouter chat completion response
interface OpenRouterChatResponse {
  id: string;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * Get a list of available OpenRouter models
 * @returns Array of available model objects
 */
export async function getAvailableOpenRouterModels(): Promise<{name: string}[]> {
  // Return default models without making API calls
  return [
    { name: 'google/gemini-2.0-flash-exp:free' },
    { name: 'anthropic/claude-3-5-sonnet:beta' },
    { name: 'anthropic/claude-3-opus:beta' },
    { name: 'anthropic/claude-3-haiku:beta' },
    { name: 'meta-llama/llama-3-70b-instruct:nitro' }
  ];
}

/**
 * Get the user's selected OpenRouter model from settings
 * @param userId User ID to get settings for
 * @returns The selected model name
 */
export async function getUserSelectedOpenRouterModel(userId: number): Promise<string> {
  try {
    // Define fallback models in order of preference
    const fallbackModels = [
      'google/gemini-2.0-flash-exp:free',
      'anthropic/claude-3-5-sonnet:beta',
      'meta-llama/llama-3-70b-instruct:nitro'
    ];

    // Get user settings
    const settings = await storage.getSettings(userId);
    if (!settings) {
      // Use gemini-2.0-flash as the default model
      const defaultModel = 'google/gemini-2.0-flash-exp:free';
      return defaultModel;
    }

    if (!settings.openRouterModel) {
      // Use gemini-2.0-flash as the default model
      const defaultModel = 'google/gemini-2.0-flash-exp:free';

      // Update the settings with the default model
      try {
        await storage.updateSettings(userId, {
          ...settings,
          openRouterModel: defaultModel
        });
      } catch (updateError) {
        // Silent error - no need to log
      }
      return defaultModel;
    }

    return settings.openRouterModel;
  } catch (error) {
    // Return a default model on error
    return 'google/gemini-2.0-flash-exp:free';
  }
}

/**
 * Generate a response from the OpenRouter API
 * @param model Model name to use
 * @param prompt The prompt to send to the model
 * @deprecated systemPrompt parameter removed as requested by user
 * @returns The generated response
 */
export async function generateOpenRouterResponse(
  model: string,
  prompt: string,
  // systemPrompt parameter removed as requested by user
): Promise<string> {
  // Return a mock response instead of making API calls
  return "This is a mock response from the OpenRouter API. The actual API call has been disabled to reduce unnecessary requests.";
}



/**
 * Generate a JSON response from the OpenRouter API
 * @param model Model name to use
 * @param prompt The prompt to send to the model
 * @deprecated systemPrompt parameter removed as requested by user
 * @returns The generated JSON response
 */
export async function generateOpenRouterJsonResponse<T>(
  model: string,
  prompt: string,
  // systemPrompt parameter removed as requested by user
): Promise<T> {
  // Return a mock JSON response
  return {
    message: "This is a mock JSON response. The actual API call has been disabled to reduce unnecessary requests."
  } as unknown as T;
}
