import Database from 'better-sqlite3';

/**
 * Migration script to add the home_page column to the settings table
 */
function runMigration() {
  console.log('Running migration: Adding home_page column to settings table');
  
  try {
    // Connect to the database
    const db = new Database('data.db');
    
    // Check if the column already exists
    const tableInfo = db.prepare('PRAGMA table_info(settings)').all();
    const columnExists = tableInfo.some(column => column.name === 'home_page');
    
    if (columnExists) {
      console.log('Column home_page already exists in settings table');
      return;
    }
    
    // Add the home_page column with a default value of 'trendy'
    db.exec(`
      ALTER TABLE settings 
      ADD COLUMN home_page TEXT DEFAULT 'trendy';
    `);
    
    console.log('Successfully added home_page column to settings table');
    
    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
}

// Run the migration
runMigration();
