import Database from 'better-sqlite3';

/**
 * Migration script to add the video_limit column to the youtube_channels table
 */
function runMigration() {
  console.log('Running migration: Adding video_limit column to youtube_channels table');
  
  try {
    // Connect to the database
    const db = new Database('data.db');
    
    // Check if the column already exists
    const tableInfo = db.prepare('PRAGMA table_info(youtube_channels)').all();
    const columnExists = tableInfo.some(column => column.name === 'video_limit');
    
    if (columnExists) {
      console.log('Column video_limit already exists in youtube_channels table');
      return;
    }
    
    // Add the video_limit column with a default value of 15
    db.exec(`
      ALTER TABLE youtube_channels 
      ADD COLUMN video_limit INTEGER DEFAULT 15;
    `);
    
    console.log('Successfully added video_limit column to youtube_channels table');
    
    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
}

// Run the migration
runMigration();
