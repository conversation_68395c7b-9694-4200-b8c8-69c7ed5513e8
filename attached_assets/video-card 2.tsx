import { Video } from "@shared/schema";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { useSettings } from "@/hooks/use-settings";
import { Eye, Clock } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useState, useEffect, useRef } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

interface VideoCardProps {
  video: Video;
}

export function VideoCard({ video }: VideoCardProps) {
  const { settings, updateSettings } = useSettings();
  const [isHovering, setIsHovering] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const isMobile = useIsMobile();
  const previewTimeout = useRef<NodeJS.Timeout>();

  // Function to create embedded player URL with appropriate parameters
  const getEmbedUrl = (videoId: string) => {
    return `https://www.youtube.com/embed/${videoId}?enablejsapi=1&modestbranding=1&showinfo=0&fs=1&rel=0&playsinline=1&autoplay=1&mute=1`;
  };

  const openVideo = (e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(video.url, '_blank');
  };

  // Get high quality thumbnail
  const getThumbnailUrl = (videoId: string) => {
    return `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;
  };

  // Handle preview functionality
  useEffect(() => {
    if (!settings?.useInAppPlayer) return;

    const shouldPreview = (isMobile && isFocused) || (!isMobile && isHovering);

    if (shouldPreview) {
      previewTimeout.current = setTimeout(() => {
        const previewContainer = document.getElementById(`preview-${video.id}`);
        if (previewContainer) {
          previewContainer.innerHTML = `
            <div class="absolute inset-0">
              <iframe
                src="${getEmbedUrl(video.id)}"
                class="w-full h-full"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowfullscreen
              ></iframe>
            </div>
          `;
        }
      }, 1000); // 1 second delay before preview starts
    } else {
      if (previewTimeout.current) {
        clearTimeout(previewTimeout.current);
      }
      const previewContainer = document.getElementById(`preview-${video.id}`);
      if (previewContainer) {
        previewContainer.innerHTML = '';
      }
    }

    return () => {
      if (previewTimeout.current) {
        clearTimeout(previewTimeout.current);
      }
    };
  }, [isHovering, isFocused, isMobile, video.id, settings?.useInAppPlayer]);

  // Calculate views per hour
  const hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
  const viewsPerHour = Math.round(video.viewCount / hoursElapsed);

  return (
    <Card 
      className="overflow-hidden cursor-pointer transition-transform hover:scale-[1.02]"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
    >
      <div className="relative">
        <div id={`preview-${video.id}`} className="w-full aspect-video relative">
          <img 
            src={getThumbnailUrl(video.id)} 
            alt={video.title}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        </div>
        {settings?.watchedVideos.includes(video.id) && (
          <div className="absolute top-2 right-2 bg-primary px-2 py-1 rounded text-xs text-primary-foreground">
            Watched
          </div>
        )}
      </div>
      <CardHeader className="p-4">
        <h3 
          className="font-semibold leading-tight line-clamp-2 hover:text-primary cursor-pointer"
          onClick={openVideo}
        >
          {video.title}
        </h3>
        <p className="text-sm text-muted-foreground mt-1">{video.channelTitle}</p>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Eye className="w-4 h-4" />
            <span>{video.viewCount.toLocaleString()}</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            <span>{formatDistanceToNow(new Date(video.publishedAt))} ago</span>
          </div>
        </div>
        <div className="mt-2 text-xs font-medium">
          {viewsPerHour} views/hour
        </div>
      </CardContent>
    </Card>
  );
}