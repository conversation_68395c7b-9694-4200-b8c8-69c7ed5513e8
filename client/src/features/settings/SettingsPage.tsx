import React, { useState, useEffect } from 'react';
import { Form, Button, Alert, Container, Row, Col } from 'react-bootstrap';
import axios from 'axios';
import { UserSettings } from '@shared/schema';
import { useAuth } from '../../providers/AuthProvider';

export default function SettingsPage() {
  const { user } = useAuth();
  const [settings, setSettings] = useState<UserSettings>({
    darkMode: true,
    searchKeywords: [],
    minViewsPerHour: 10,
    removeDuplicates: true,
    excludeWords: [],
    autoRefreshInterval: 0,
    preferredPlayback: 'in_app',
    lastRefreshTime: null,
    parallelApiCalls: true, // Now enabled by default
    watchedVideos: [],
    useInAppPlayer: true
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (user?.id) {
      loadSettings();
    }
  }, [user?.id]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const { data } = await axios.get('/api/settings');
      setSettings(data);
      setError('');
    } catch (err) {
      setError('Failed to load settings');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? Number(value) : value
    }));
  };

  const handleArrayChange = (name: keyof UserSettings, value: string[]) => {
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      await axios.put('/api/settings', settings);
      setSuccess('Settings saved successfully');
      setError('');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('Failed to save settings');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteData = async () => {
    if (window.confirm('Are you sure you want to delete all your data? This cannot be undone.')) {
      try {
        setLoading(true);
        await axios.delete('/api/data');
        await loadSettings(); // Refresh settings after deletion
        setSuccess('All data has been reset to defaults');
        setError('');
        setTimeout(() => setSuccess(''), 3000);
      } catch (err) {
        setError('Failed to delete data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <Container className="py-4">
      <h2>Settings</h2>
      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {loading ? (
        <p>Loading settings...</p>
      ) : (
        <Form onSubmit={handleSubmit}>
          <Form.Group as={Row} className="mb-3" controlId="darkMode">
            <Form.Label column sm={2}>Dark Mode</Form.Label>
            <Col sm={10}>
              <Form.Check
                type="switch"
                name="darkMode"
                checked={settings.darkMode}
                onChange={handleChange}
              />
            </Col>
          </Form.Group>

          <Form.Group as={Row} className="mb-3" controlId="searchKeywords">
            <Form.Label column sm={2}>Search Keywords</Form.Label>
            <Col sm={10}>
              <Form.Control
                as="textarea"
                rows={3}
                name="searchKeywords"
                value={settings.searchKeywords.join(', ')}
                onChange={(e) => handleArrayChange('searchKeywords', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
              />
              <Form.Text className="text-muted">
                Comma separated list of keywords to search for
              </Form.Text>
            </Col>
          </Form.Group>

          <Form.Group as={Row} className="mb-3" controlId="minViewsPerHour">
            <Form.Label column sm={2}>Minimum Views per Hour</Form.Label>
            <Col sm={10}>
              <Form.Control
                type="number"
                name="minViewsPerHour"
                value={settings.minViewsPerHour}
                onChange={handleChange}
              />
            </Col>
          </Form.Group>

          <Form.Group as={Row} className="mb-3" controlId="removeDuplicates">
            <Form.Label column sm={2}>Remove Duplicates</Form.Label>
            <Col sm={10}>
              <Form.Check
                type="switch"
                name="removeDuplicates"
                checked={settings.removeDuplicates}
                onChange={handleChange}
              />
            </Col>
          </Form.Group>

          <Form.Group as={Row} className="mb-3" controlId="excludeWords">
            <Form.Label column sm={2}>Exclude Words</Form.Label>
            <Col sm={10}>
              <Form.Control
                as="textarea"
                rows={3}
                name="excludeWords"
                value={settings.excludeWords.join(', ')}
                onChange={(e) => handleArrayChange('excludeWords', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
              />
            </Col>
          </Form.Group>

          <Form.Group as={Row} className="mb-3" controlId="autoRefreshInterval">
            <Form.Label column sm={2}>Auto Refresh (minutes)</Form.Label>
            <Col sm={10}>
              <Form.Control
                type="number"
                name="autoRefreshInterval"
                value={settings.autoRefreshInterval}
                onChange={handleChange}
              />
              <Form.Text className="text-muted">
                Set to 0 to disable auto refresh
              </Form.Text>
            </Col>
          </Form.Group>

          <Form.Group as={Row} className="mb-3" controlId="preferredPlayback">
            <Form.Label column sm={2}>Preferred Playback</Form.Label>
            <Col sm={10}>
              <Form.Select
                name="preferredPlayback"
                value={settings.preferredPlayback}
                onChange={(e) => handleChange(e as React.ChangeEvent<HTMLInputElement>)}
              >
                <option value="in_app">In App Player</option>
                <option value="youtube">Youtube Website</option>
              </Form.Select>
            </Col>
          </Form.Group>

          {/* Parallel API Calls option removed - now enabled by default */}

          <Form.Group as={Row} className="mb-3" controlId="useInAppPlayer">
            <Form.Label column sm={2}>Use In-App Player</Form.Label>
            <Col sm={10}>
              <Form.Check
                type="switch"
                name="useInAppPlayer"
                checked={settings.useInAppPlayer}
                onChange={handleChange}
              />
            </Col>
          </Form.Group>

          <Row className="mt-4">
            <Col sm={{ span: 10, offset: 2 }}>
              <Button variant="primary" type="submit" disabled={loading}>
                {loading ? 'Saving...' : 'Save Settings'}
              </Button>

              <Button
                variant="danger"
                className="ms-3"
                onClick={handleDeleteData}
                disabled={loading}
              >
                {loading ? 'Processing...' : 'Reset All Data'}
              </Button>
            </Col>
          </Row>
        </Form>
      )}
    </Container>
  );
}

