/* MUI Dark Mode Overrides */

/* Fix for MUI Paper component in dark mode */
.dark .MuiPaper-root,
.dark div[class*="MuiPaper-root"],
.dark .css-l385z7-MuiPaper-root {
  background-color: #1e1e1e !important;
  color: white !important;
  box-shadow: none !important;
}

/* Fix for MUI Typography in dark mode */
.dark .MuiTypography-root {
  color: hsl(var(--card-foreground)) !important;
}

/* Fix for MUI IconButton in dark mode */
.dark .MuiIconButton-root {
  color: hsl(var(--card-foreground)) !important;
}

/* Fix for MUI IconButton hover in dark mode */
.dark .MuiIconButton-root:hover {
  background-color: hsl(var(--muted)) !important;
}

/* Fix for MUI Dialog in dark mode */
.dark .MuiDialog-paper,
.dark .MuiPaper-root.MuiDialog-paper,
.dark div[class*="MuiDialog-paper"] {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5) !important;
}

/* Fix for MUI TextField in dark mode */
.dark .MuiInputBase-root,
.dark .MuiOutlinedInput-root,
.dark .MuiInputBase-input,
.dark .MuiInputLabel-root {
  color: hsl(var(--card-foreground)) !important;
}

.dark .MuiInputBase-root,
.dark .MuiOutlinedInput-root {
  background-color: hsl(var(--muted)) !important;
}

.dark .MuiInputLabel-root {
  color: hsl(var(--card-foreground)) !important;
  opacity: 0.8;
}

.dark .MuiOutlinedInput-notchedOutline {
  border-color: hsl(var(--border)) !important;
}

/* Fix for MUI Select in dark mode */
.dark .MuiSelect-select {
  color: hsl(var(--card-foreground)) !important;
  background-color: hsl(var(--muted)) !important;
}

/* Fix for MUI MenuItem in dark mode */
.dark .MuiMenuItem-root {
  color: hsl(var(--card-foreground)) !important;
}

/* Fix for MUI Menu in dark mode */
.dark .MuiMenu-paper {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

/* Fix for MUI Checkbox in dark mode */
.dark .MuiCheckbox-root {
  color: hsl(var(--card-foreground)) !important;
}

/* Fix for regular checkboxes in MUI dialogs */
.dark input[type="checkbox"] {
  accent-color: hsl(var(--primary)) !important;
}

.dark label {
  color: hsl(var(--card-foreground)) !important;
}

/* Fix for MUI Button in dark mode */
.dark .MuiButton-root {
  color: hsl(var(--card-foreground)) !important;
}

/* Fix for MUI Button contained in dark mode */
.dark .MuiButton-contained {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

/* Fix for MUI Button outlined in dark mode */
.dark .MuiButton-outlined {
  border-color: hsl(var(--border)) !important;
  color: hsl(var(--card-foreground)) !important;
}

/* Fix for MUI Tooltip in dark mode */
.dark .MuiTooltip-tooltip {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
}

/* Fix for MUI Divider in dark mode */
.dark .MuiDivider-root {
  background-color: hsl(var(--border)) !important;
}

/* Fix for MUI List items in dark mode */
.dark .MuiListItem-root {
  color: hsl(var(--card-foreground)) !important;
}

/* Fix for MUI List item hover in dark mode */
.dark .MuiListItem-root:hover {
  background-color: hsl(var(--muted)) !important;
}

/* Fix for MUI Dialog content in dark mode */
.dark .MuiDialogContent-root {
  color: hsl(var(--card-foreground)) !important;
  background-color: hsl(var(--card)) !important;
}

/* Fix for MUI Dialog title in dark mode */
.dark .MuiDialogTitle-root {
  color: hsl(var(--card-foreground)) !important;
  background-color: hsl(var(--card)) !important;
  border-bottom: 1px solid hsl(var(--border)) !important;
}

/* Fix for MUI Dialog actions in dark mode */
.dark .MuiDialogActions-root {
  background-color: hsl(var(--card)) !important;
  border-top: 1px solid hsl(var(--border)) !important;
}

/* Fix for MUI Dialog content text in dark mode */
.dark .MuiDialogContentText-root {
  color: hsl(var(--card-foreground)) !important;
  opacity: 0.8;
}

/* Fix for MUI Snackbar in dark mode */
.dark .MuiSnackbarContent-root {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
}

/* Fix for MUI Alert in dark mode */
.dark .MuiAlert-root {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
}

/* Fix for Financial Benefit component */
.dark .bg-blue-50 {
  background-color: #1e1e1e !important;
}

.dark .border-blue-100 {
  border-color: #333333 !important;
}

.dark [class*="bg-blue-50"] {
  background-color: #1e1e1e !important;
}

.dark [class*="border-blue-100"] {
  border-color: #333333 !important;
}
