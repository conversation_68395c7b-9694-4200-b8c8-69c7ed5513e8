import React, { useState, useEffect } from 'react';
import { useBackgroundTasks, TaskStatus } from '@/hooks/use-background-tasks';
import { TaskStatusList } from '@/components/ui/task-status';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RefreshCw, ArrowLeft, StopCircle, Trash2, Loader2 } from 'lucide-react';
import { useLocation } from 'wouter';
import { Sidebar } from '@/components/navigation/sidebar';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

export default function TasksPage() {
  const { tasks, cancelTask, refetchTasks, cancelAllTasks, clearTaskHistory, retryFailedTasks } = useBackgroundTasks();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [isStoppingAll, setIsStoppingAll] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [pulseActive, setPulseActive] = useState(false);

  // Filter tasks by status
  const activeTasks = tasks.filter(task =>
    task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING
  );

  const completedTasks = tasks.filter(task =>
    task.status === TaskStatus.COMPLETED
  );

  const failedTasks = tasks.filter(task =>
    task.status === TaskStatus.FAILED || task.status === TaskStatus.CANCELLED
  );

  // Pulse animation for active tasks
  useEffect(() => {
    if (activeTasks.length > 0) {
      // Create a pulsing effect every 2 seconds
      const interval = setInterval(() => {
        setPulseActive(prev => !prev);
      }, 2000);

      return () => clearInterval(interval);
    } else {
      setPulseActive(false);
    }
  }, [activeTasks.length]);

  // Log the filtered tasks for debugging
  console.log('Tasks page:', {
    total: tasks.length,
    active: activeTasks.length,
    completed: completedTasks.length,
    failed: failedTasks.length,
    activeTaskDetails: activeTasks
  });

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar - Always present, either full or mini */}
      <Sidebar />

      <div className="flex-1 overflow-auto">
        <div className="container mx-auto py-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-4">
              <Button onClick={() => setLocation('/youtube')} variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to YouTube
              </Button>
              <h1 className="text-2xl font-bold">Background Tasks</h1>
            </div>
            <div className="flex gap-2">
              {activeTasks.length > 0 && (
                <Button
                  onClick={() => {
                    setIsStoppingAll(true);
                    cancelAllTasks()
                      .then((success) => {
                        if (success) {
                          // Success toast is already shown by the hook
                          // Force refresh the task list
                          setTimeout(() => refetchTasks(), 500);
                        } else {
                          toast({
                            title: "No Tasks Stopped",
                            description: "Failed to stop tasks. Check console for details."
                          });
                        }
                      })
                      .catch((error) => {
                        console.error('Error in stop all button handler:', error);
                        toast({
                          title: "Error",
                          description: "Failed to stop tasks: " + error.message,
                          variant: "destructive"
                        });
                      })
                      .finally(() => {
                        setTimeout(() => {
                          setIsStoppingAll(false);
                          refetchTasks(); // Refresh again after button state changes
                        }, 1000);
                      });
                  }}
                  variant="destructive"
                  size="sm"
                  className={cn(
                    "transition-all",
                    pulseActive && "animate-pulse bg-red-700 border-red-500",
                    isStoppingAll && "opacity-80 cursor-not-allowed"
                  )}
                  disabled={isStoppingAll}
                >
                  {isStoppingAll ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Stopping...
                    </>
                  ) : (
                    <>
                      <StopCircle className="h-4 w-4 mr-2" />
                      Stop All ({activeTasks.length})
                    </>
                  )}
                </Button>
              )}
              {failedTasks.length > 0 && (
                <Button
                  onClick={() => {
                    setIsRetrying(true);
                    retryFailedTasks()
                      .then((success) => {
                        if (success) {
                          // Success toast is already shown by the hook
                          // Force refresh the task list
                          setTimeout(() => refetchTasks(), 500);
                        } else {
                          toast({
                            title: "No Tasks Retried",
                            description: "Failed to retry tasks. Check console for details."
                          });
                        }
                      })
                      .catch((error) => {
                        console.error('Error in retry button handler:', error);
                        toast({
                          title: "Error",
                          description: "Failed to retry tasks: " + error.message,
                          variant: "destructive"
                        });
                      })
                      .finally(() => {
                        setTimeout(() => {
                          setIsRetrying(false);
                          refetchTasks(); // Refresh again after button state changes
                        }, 1000);
                      });
                  }}
                  variant="outline"
                  size="sm"
                  className={cn(
                    "bg-amber-100 hover:bg-amber-200 text-amber-800 border-amber-300",
                    isRetrying && "opacity-80 cursor-not-allowed"
                  )}
                  disabled={isRetrying}
                >
                  {isRetrying ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Retrying...
                    </>
                  ) : (
                    <>
                      <RefreshCw className={cn("h-4 w-4 mr-2", failedTasks.length > 0 && pulseActive && "animate-pulse text-red-600")} />
                      Retry Failed ({failedTasks.length})
                    </>
                  )}
                </Button>
              )}

              {(completedTasks.length > 0 || failedTasks.length > 0) && (
                <Button
                  onClick={() => {
                    setIsClearing(true);
                    clearTaskHistory()
                      .then((success) => {
                        if (success) {
                          // Success toast is already shown by the hook
                          // Force refresh the task list
                          setTimeout(() => refetchTasks(), 500);
                        } else {
                          toast({
                            title: "No Tasks Cleared",
                            description: "Failed to clear tasks. Check console for details."
                          });
                        }
                      })
                      .catch((error) => {
                        console.error('Error in clear button handler:', error);
                        toast({
                          title: "Error",
                          description: "Failed to clear tasks: " + error.message,
                          variant: "destructive"
                        });
                      })
                      .finally(() => {
                        setTimeout(() => {
                          setIsClearing(false);
                          refetchTasks(); // Refresh again after button state changes
                        }, 1000);
                      });
                  }}
                  variant="outline"
                  size="sm"
                  className={cn(
                    isClearing && "opacity-80 cursor-not-allowed"
                  )}
                  disabled={isClearing}
                >
                  {isClearing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Clearing...
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear All History ({completedTasks.length + failedTasks.length})
                    </>
                  )}
                </Button>
              )}
              <Button onClick={refetchTasks} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Task Status Overview</CardTitle>
          <CardDescription>
            Monitor the status of all background tasks in the application
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-950 rounded-md p-4 border border-blue-900">
              <h3 className="font-medium text-blue-300">Active Tasks</h3>
              <p className="text-2xl font-bold text-white">{activeTasks.length}</p>
            </div>
            <div className="bg-green-950 rounded-md p-4 border border-green-900">
              <h3 className="font-medium text-green-300">Completed Tasks</h3>
              <p className="text-2xl font-bold text-white">{completedTasks.length}</p>
            </div>
            <div className="bg-red-950 rounded-md p-4 border border-red-900">
              <h3 className="font-medium text-red-300">Failed Tasks</h3>
              <p className="text-2xl font-bold text-white">{failedTasks.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="active" className="w-full">
        <TabsList className="mb-4 h-12 w-full justify-start">
          <TabsTrigger value="active" className="h-10 px-6 text-base">
            Active ({activeTasks.length})
            {activeTasks.length > 0 && pulseActive && (
              <span className="ml-2 h-2 w-2 rounded-full bg-red-500 animate-pulse"></span>
            )}
          </TabsTrigger>
          <TabsTrigger value="completed" className="h-10 px-6 text-base">Completed ({completedTasks.length})</TabsTrigger>
          <TabsTrigger value="failed" className="h-10 px-6 text-base">Failed ({failedTasks.length})</TabsTrigger>
          <TabsTrigger value="all" className="h-10 px-6 text-base">All Tasks ({tasks.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          {activeTasks.length > 0 ? (
            <TaskStatusList tasks={activeTasks} onCancel={cancelTask} showDetails={true} />
          ) : (
            <p className="text-gray-500">No active tasks</p>
          )}
        </TabsContent>

        <TabsContent value="completed">
          {completedTasks.length > 0 ? (
            <TaskStatusList tasks={completedTasks} showDetails={true} />
          ) : (
            <p className="text-gray-500">No completed tasks</p>
          )}
        </TabsContent>

        <TabsContent value="failed">
          {failedTasks.length > 0 ? (
            <TaskStatusList tasks={failedTasks} showDetails={true} />
          ) : (
            <p className="text-gray-500">No failed tasks</p>
          )}
        </TabsContent>

        <TabsContent value="all">
          <TaskStatusList tasks={tasks} onCancel={cancelTask} showDetails={true} />
        </TabsContent>
      </Tabs>
        </div>
      </div>
    </div>
  );
}
