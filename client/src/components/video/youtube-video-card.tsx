import { YoutubeVideo } from "@shared/schema";
import { Card, CardContent } from "@/components/ui/card";
import { useSettings } from "@/hooks/use-settings";
import { Eye, Clock, Info, EyeOff, FileText, Radio, Scissors, Play, DollarSign, BarChart, Copy, Clock3, Search, RefreshCw, Activity } from "lucide-react";
import { Input } from "@/components/ui/input";
import { isLiveStream, isEndedLiveStream } from "@/lib/video-type";
import { useBackgroundTasks } from "@/hooks/use-background-tasks";
import { getVideoType } from "@/lib/video-type";
import { cn } from "@/lib/utils";
import { useState, useEffect, useRef, useCallback } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useFloatingVideo } from "@/context/floating-video-context";
import { formatRelativeTime, formatDate } from "@/lib/date-format";
import { BookmarkButton } from "./bookmark-button";
import { YoutubeThumbnail } from "./youtube-thumbnail";
import { FinancialBenefit } from "./financial-benefit";
import { castVideo, getCurrentSession, isCastingAvailable } from "@/lib/cast";
import { useFinancialAnalysis, getFinancialCategoryBadge, formatFinancialScore, getFinancialScoreColor, getViewsPerHourColor } from "@/hooks/use-financial-analysis";
import { AIAnalysisTab } from "@/components/financial-analysis/ai-analysis-tab";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { FinancialTranscriptDialog } from "./financial-transcript-dialog";

// Helper function to convert a timestamp string (MM:SS) to seconds
function convertTimestampToSeconds(timestamp: string): number {
  if (!timestamp) return 0;

  try {
    // Handle different timestamp formats
    // Format 1: MM:SS
    if (/^\d+:\d+$/.test(timestamp)) {
      const [minutes, seconds] = timestamp.split(':').map(Number);
      return minutes * 60 + seconds;
    }

    // Format 2: HH:MM:SS
    if (/^\d+:\d+:\d+$/.test(timestamp)) {
      const [hours, minutes, seconds] = timestamp.split(':').map(Number);
      return hours * 3600 + minutes * 60 + seconds;
    }

    // Format 3: [MM:SS]
    if (/^\[\d+:\d+\]$/.test(timestamp)) {
      const timeStr = timestamp.substring(1, timestamp.length - 1);
      const [minutes, seconds] = timeStr.split(':').map(Number);
      return minutes * 60 + seconds;
    }

    // If it's just a number, assume it's already in seconds
    if (!isNaN(Number(timestamp))) {
      return Number(timestamp);
    }

    // Try to extract the first timestamp-like pattern from the text
    const timestampMatch = timestamp.match(/\d+:\d+/);
    if (timestampMatch) {
      const [minutes, seconds] = timestampMatch[0].split(':').map(Number);
      return minutes * 60 + seconds;
    }
  } catch (error) {
    console.error('Error converting timestamp to seconds:', error);
  }

  return 0;
}

// Helper function to format ISO 8601 duration (PT1M30S) to human-readable format (1:30)
function formatDuration(durationStr: string): string {
  try {
    const minutesMatch = durationStr.match(/([0-9]+)M/);
    const secondsMatch = durationStr.match(/([0-9]+)S/);
    const hoursMatch = durationStr.match(/([0-9]+)H/);

    const hours = hoursMatch ? parseInt(hoursMatch[1]) : 0;
    const minutes = minutesMatch ? parseInt(minutesMatch[1]) : 0;
    const seconds = secondsMatch ? parseInt(secondsMatch[1]) : 0;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  } catch (e) {
    console.error('Error parsing duration:', durationStr, e);
    return durationStr;
  }
}

// Helper function to format view count in a user-friendly way
function formatViewCount(count: number, isLive: boolean = false, publishedAt?: string): string {
  if (!count || isNaN(count) || count <= 0) {
    return isLive ? "0 watching now" : "0 views";
  }

  // Check if the view count is suspiciously high
  let isSuspicious = false;

  if (publishedAt) {
    const pubDate = new Date(publishedAt);
    const hoursElapsed = Math.max(1, (Date.now() - pubDate.getTime()) / (1000 * 60 * 60));
    const daysElapsed = hoursElapsed / 24;

    if (isLive) {
      // For live streams, more than 100K viewers is unusual
      if (count > 100000) {
        isSuspicious = true;
      }
    } else {
      // For regular videos, check based on age
      if (daysElapsed < 1 && count > 100000) {
        // Very new videos (less than 1 day old)
        isSuspicious = true;
      } else if (daysElapsed < 7 && count > 1000000) {
        // New videos (1-7 days old)
        isSuspicious = true;
      } else if (count > 5000000) {
        // Older videos with extremely high view counts
        isSuspicious = true;
      }
    }
  } else if (count > 5000000) {
    // If we don't have publish date, use a conservative threshold
    isSuspicious = true;
  }

  // Format the count
  let formattedCount = "";
  if (count >= 1000000000) {
    formattedCount = `${(count / 1000000000).toFixed(1)}B`;
  } else if (count >= 1000000) {
    formattedCount = `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    formattedCount = `${(count / 1000).toFixed(1)}K`;
  } else {
    formattedCount = `${count}`;
  }

  // Add suffix and warning indicator if needed
  if (isLive) {
    formattedCount += " watching now";
  } else {
    formattedCount += " views";
  }

  if (isSuspicious) {
    formattedCount += " ⚠️";
  }

  return formattedCount;
}

// Helper function to format date in a user-friendly way using our IST formatter
function formatPublishedDate(date: Date): string {
  try {
    return formatDate(date);
  } catch (e) {
    console.error('Error formatting date:', e);
    return 'Unknown date';
  }
}



interface YoutubeVideoCardProps {
  video: YoutubeVideo;
  onTranscriptRequest: () => Promise<string | null>;
  hasTranscription?: boolean;
}

export function YoutubeVideoCard({ video, onTranscriptRequest, hasTranscription = false }: YoutubeVideoCardProps) {
  const { settings, updateSettings } = useSettings();
  const { openFloatingVideo, isFloatingPlayerActive } = useFloatingVideo();
  const { toast } = useToast();
  const { analyzeVideo, analyzeVideoWithOpenRouter } = useFinancialAnalysis();
  const { tasks } = useBackgroundTasks();
  const [isHovering, setIsHovering] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [metadataOpen, setMetadataOpen] = useState(false);
  const [transcriptOpen, setTranscriptOpen] = useState(false);
  const [financialTranscriptOpen, setFinancialTranscriptOpen] = useState(false);

  // Check if a transcription is currently being processed for this video
  const isPendingTranscription = (videoId: string) => {
    return tasks.some(task =>
      (task.status === 'pending' || task.status === 'running') &&
      task.type === 'fetch-transcription' &&
      task.data?.videoId === videoId
    );
  };
  // Financial analysis is now integrated into the transcript dialog
  const [transcription, setTranscription] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [financialAnalysis, setFinancialAnalysis] = useState<any>(null);
  const [isLoadingTranscript, setIsLoadingTranscript] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<number[]>([]);
  const [currentResultIndex, setCurrentResultIndex] = useState(-1);
  const isMobile = useIsMobile();
  const previewTimeout = useRef<NodeJS.Timeout>();

  // Calculate views per hour
  const publishedDate = new Date(video.publishedAt);
  const hoursElapsed = Math.max(1, (Date.now() - publishedDate.getTime()) / (1000 * 60 * 60));
  const viewsPerHour = Math.round(video.viewCount / hoursElapsed);

  // Check if this video has been watched
  const isWatched = settings?.watchedVideos?.includes(video.id) || false;

  const openVideo = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();

    // Mark as playing to prevent thumbnail from being reset
    setIsPlaying(true);

    // Check if we're already casting
    const isCasting = isCastingAvailable() && !!getCurrentSession();

    if (isCasting && e.ctrlKey) {
      // If Ctrl+Click and we're casting, cast this video directly
      castVideo(video.id, video.title, video.thumbnail)
        .then(() => {
          console.log('Video cast successfully');
        })
        .catch(error => {
          console.error('Error casting video:', error);
          // Fallback to floating player if casting fails
          openFloatingVideo(video);
        });
    } else {
      // Open directly in floating player if not casting
      openFloatingVideo(video);
    }

    // Explicitly disable hover preview
    setIsHovering(false);
  }, [video, openFloatingVideo, setIsPlaying, setIsHovering]);

  // Handle seeking to a specific timestamp in the floating video player
  const handleSeekToTimestamp = (timestampSeconds: number) => {
    console.log(`Attempting to seek to ${timestampSeconds} seconds`);

    // Ensure we have a valid timestamp
    if (isNaN(timestampSeconds) || timestampSeconds < 0) {
      console.error(`Invalid timestamp: ${timestampSeconds}`);
      toast({
        title: "Error",
        description: "Invalid timestamp. Please try again.",
        variant: "destructive"
      });
      return;
    }

    // Open the video in the floating player if not already open
    if (!isPlaying) {
      console.log(`Opening video ${video.id} in floating player`);
      openFloatingVideo(video);

      // Add a small delay to ensure the player is ready before seeking
      setTimeout(() => {
        sendSeekEvent(timestampSeconds);
      }, 1500);
    } else {
      // If already playing, seek immediately
      sendSeekEvent(timestampSeconds);
    }
  };

  // Helper function to send the seek event
  const sendSeekEvent = (timestampSeconds: number) => {
    console.log(`Sending seek event for ${video.id} to ${timestampSeconds}s`);

    // Use a custom event to communicate with the floating player
    const seekEvent = new CustomEvent('seek-floating-video', {
      detail: {
        videoId: video.id,
        timestamp: timestampSeconds
      }
    });

    // Dispatch the event to the window
    window.dispatchEvent(seekEvent);

    // Show a toast notification
    toast({
      title: "Seeking to timestamp",
      description: `Jumping to ${Math.floor(timestampSeconds / 60)}:${(timestampSeconds % 60).toString().padStart(2, '0')}`
    });
  };

  // Handle financial analysis
  const handleAnalyzeFinancial = async () => {
    console.log('YoutubeVideoCard: handleAnalyzeFinancial called for video:', video.id);
    console.log('YoutubeVideoCard: Video has financial analysis:', video.hasFinancialAnalysis);
    console.log('YoutubeVideoCard: Video has transcription:', video.hasTranscription);

    if (video.hasFinancialAnalysis) {
      // Analysis already exists, just open the dialog
      console.log('YoutubeVideoCard: Video already has financial analysis, opening dialog');
      setFinancialAnalysisOpen(true);
      return;
    }

    // Check if transcription is available
    if (!video.hasTranscription) {
      console.log('YoutubeVideoCard: No transcription available, showing toast');
      toast({
        title: "Transcription Required",
        description: "A transcription is required for financial analysis. Please load the transcription first.",
        variant: "destructive",
      });
      return;
    }

    // Start analysis
    console.log('YoutubeVideoCard: Starting financial analysis for video:', video.id);
    setIsAnalyzing(true);
    try {
      console.log('YoutubeVideoCard: Calling analyzeVideo.mutateAsync');
      const result = await analyzeVideo.mutateAsync(video.id);
      console.log('YoutubeVideoCard: Analysis result:', result);
      // Open transcript dialog with financial analysis
      setTranscriptOpen(true);
    } catch (error) {
      console.error('YoutubeVideoCard: Error analyzing financial benefits:', error);
      console.error('YoutubeVideoCard: Error details:', error instanceof Error ? error.message : 'Unknown error');
      toast({
        title: "Analysis Failed",
        description: "Failed to analyze financial benefits. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Handle AI financial analysis
  const handleAnalyzeWithAI = async () => {
    console.log('YoutubeVideoCard: handleAnalyzeWithAI called for video:', video.id);
    console.log('YoutubeVideoCard: Video has transcription:', video.hasTranscription);

    // Check if transcription is available
    if (!video.hasTranscription) {
      console.log('YoutubeVideoCard: No transcription available, showing toast');
      toast({
        title: "Transcription Required",
        description: "A transcription is required for AI analysis. Please load the transcription first.",
        variant: "destructive",
      });
      return;
    }

    // Start analysis
    console.log('YoutubeVideoCard: Starting AI analysis for video:', video.id);
    setIsAnalyzing(true);
    try {
      console.log('YoutubeVideoCard: Calling analyzeVideoWithOpenRouter.mutateAsync');
      const result = await analyzeVideoWithOpenRouter.mutateAsync(video.id);
      console.log('YoutubeVideoCard: AI analysis result:', result);
      // Open transcript dialog with AI tab selected
      setTranscriptOpen(true);
      // Select the AI tab
      setTimeout(() => {
        const aiTab = document.querySelector('[data-value="ai"]');
        if (aiTab) {
          (aiTab as HTMLElement).click();
        }
      }, 100);
    } catch (error) {
      console.error('YoutubeVideoCard: Error analyzing with AI:', error);
      console.error('YoutubeVideoCard: Error details:', error instanceof Error ? error.message : 'Unknown error');
      toast({
        title: "AI Analysis Failed",
        description: "Failed to analyze with AI. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Function to fetch financial analysis
  const fetchFinancialAnalysis = useCallback(async () => {
    if (!video.hasTranscription) return null;

    try {
      const res = await apiRequest("GET", `/api/youtube-channels/videos/${video.id}/financial-analysis`);

      if (!res.ok) {
        if (res.status !== 404) { // Don't show error for 404 (no analysis yet)
          const errorText = await res.text();
          throw new Error(`API returned ${res.status}: ${errorText}`);
        }
        return null;
      }

      const data = await res.json();
      setFinancialAnalysis(data);
      return data;
    } catch (error) {
      console.error('Error fetching financial analysis:', error);
      return null;
    }
  }, [video.id, video.hasTranscription]);

  // Function to reanalyze the video
  const handleReanalyzeVideo = useCallback(async () => {
    if (!video.hasTranscription) {
      toast({
        title: "No Transcription Available",
        description: "This video needs a transcription before it can be analyzed.",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);
    try {
      // Call the analyze-video endpoint
      const res = await apiRequest("POST", "/api/youtube-channels/analyze-video", { videoId: video.id });

      if (!res.ok) {
        const errorText = await res.text();
        throw new Error(`API returned ${res.status}: ${errorText}`);
      }

      const data = await res.json();
      setFinancialAnalysis(data.analysis);

      // Refresh the video data
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${video.id}`] });

      toast({
        title: "Financial Analysis Complete",
        description: `Analysis completed with score: ${data.analysis.score}/100`,
      });
    } catch (error) {
      console.error('Error analyzing video:', error);
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  }, [video.id, video.hasTranscription, toast]);

  // Handle loading transcription and financial analysis
  const handleLoadTranscription = async (analyzeAfterLoad = false, openDialog = true, openFinancialDialog = false) => {
    console.log('YoutubeVideoCard: handleLoadTranscription called with analyzeAfterLoad =', analyzeAfterLoad, 'openDialog =', openDialog, 'openFinancialDialog =', openFinancialDialog);

    // Check if transcription is being processed in the background
    if (isPendingTranscription(video.id)) {
      console.log('YoutubeVideoCard: Transcription is being processed in the background for video:', video.id);
      setTranscription('Transcription is being processed in the background. Please check back later.');
      if (openDialog) {
        setTranscriptOpen(true);
      } else if (openFinancialDialog) {
        setFinancialTranscriptOpen(true);
      }
      return;
    }

    // First, fetch financial analysis if available
    if (video.hasFinancialAnalysis) {
      await fetchFinancialAnalysis();
    }

    if (transcription !== null) {
      // Transcription already loaded
      console.log('YoutubeVideoCard: Transcription already loaded for video:', video.id);

      // Open the appropriate dialog if requested
      if (openDialog) {
        setTranscriptOpen(true);
      } else if (openFinancialDialog) {
        setFinancialTranscriptOpen(true);
      }

      // If we need to analyze after loading, do it
      if (analyzeAfterLoad) {
        handleAnalyzeFinancial();
      }
      return;
    }

    setIsLoadingTranscript(true);
    try {
      console.log('YoutubeVideoCard: Requesting transcription for video:', video.id);
      const result = await onTranscriptRequest();
      console.log('YoutubeVideoCard: Received transcription result:', result);

      // Check if result is an object with a taskId (background task created)
      if (result && typeof result === 'object' && result.taskId) {
        console.log(`YoutubeVideoCard: Transcription task created with ID: ${result.taskId} for video ${video.id}`);
        const message = 'Transcription is being processed in the background. Please check back later.';
        setTranscription(message);
        if (openDialog) {
          setTranscriptOpen(true);
        } else if (openFinancialDialog) {
          setFinancialTranscriptOpen(true);
        }

        // Show a toast notification
        toast({
          title: "Transcription Requested",
          description: "The transcription is being fetched in the background. You will be notified when it is ready.",
        });

        // Refresh the tasks list to show the new task
        queryClient.invalidateQueries({ queryKey: ['tasks'] });
        return;
      }

      // Check if result is an object with a transcription property
      if (result && typeof result === 'object' && typeof result.transcription === 'string') {
        console.log(`YoutubeVideoCard: Got transcription of length ${result.transcription.length} for video ${video.id}`);
        setTranscription(result.transcription);

        if (analyzeAfterLoad) {
          console.log('YoutubeVideoCard: Transcription loaded, now analyzing financial benefits');
          // Wait a moment for the transcription to be saved in the database
          setTimeout(() => handleAnalyzeFinancial(), 500);
        } else {
          // Open appropriate dialog if requested
          if (openDialog) {
            setTranscriptOpen(true);
          } else if (openFinancialDialog) {
            setFinancialTranscriptOpen(true);
          }
        }
        return;
      }

      // Handle string result (for backward compatibility)
      if (result && typeof result === 'string') {
        // Check if the result is an error message
        if (result.includes('not found in your subscriptions') ||
            result.includes('Transcription not available') ||
            result.includes('Unable to fetch transcription') ||
            result.includes('being processed in the background')) {
          console.warn(`YoutubeVideoCard: Transcription not available for video ${video.id}`);
          setTranscription(result);
          if (openDialog) {
            setTranscriptOpen(true);
          }
          // Show a toast notification
          toast({
            title: "Transcription Not Available",
            description: "This video doesn't have an available transcription.",
            variant: "destructive",
          });
        } else {
          console.log(`YoutubeVideoCard: Setting transcription of length ${result.length} for video ${video.id}`);
          setTranscription(result);
          if (analyzeAfterLoad) {
            console.log('YoutubeVideoCard: Transcription loaded, now analyzing financial benefits');
            // Wait a moment for the transcription to be saved in the database
            setTimeout(() => handleAnalyzeFinancial(), 500);
          } else {
            // Open appropriate dialog if requested
            if (openDialog) {
              setTranscriptOpen(true);
            } else if (openFinancialDialog) {
              setFinancialTranscriptOpen(true);
            }
          }
        }
      } else {
        console.error('YoutubeVideoCard: No transcription returned for video:', video.id);
        const errorMessage = 'No transcription available for this video.';
        console.log(`YoutubeVideoCard: Setting error message for video ${video.id}`);
        setTranscription(errorMessage);
        if (openDialog) {
          setTranscriptOpen(true);
        } else if (openFinancialDialog) {
          setFinancialTranscriptOpen(true);
        }
      }
    } catch (error) {
      console.error('YoutubeVideoCard: Error loading transcription:', error);
      const errorMessage = 'Failed to load transcription. Please try again later.';
      console.log(`YoutubeVideoCard: Setting error message for video ${video.id}`);
      setTranscription(errorMessage);
      if (openDialog) {
        setTranscriptOpen(true);
      } else if (openFinancialDialog) {
        setFinancialTranscriptOpen(true);
      }

      // Financial analysis is now integrated into the transcript dialog
    } finally {
      setIsLoadingTranscript(false);
    }
  };

  // Function to handle transcript search
  const handleTranscriptSearch = useCallback(() => {
    if (!searchQuery.trim() || !transcription) {
      setSearchResults([]);
      setCurrentResultIndex(-1);
      return;
    }

    const results: number[] = [];
    const lines = transcription.split('\n');
    const query = searchQuery.toLowerCase();

    // Find all occurrences of the search query in the transcript
    lines.forEach((line, index) => {
      if (line.toLowerCase().includes(query)) {
        results.push(index);
      }
    });

    setSearchResults(results);
    setCurrentResultIndex(results.length > 0 ? 0 : -1);

    // Scroll to the first result if found
    if (results.length > 0) {
      setTimeout(() => {
        const element = document.getElementById(`transcript-line-${results[0]}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    }
  }, [searchQuery, transcription]);

  // Navigate to the next search result
  const goToNextResult = useCallback(() => {
    if (searchResults.length === 0) return;

    const nextIndex = (currentResultIndex + 1) % searchResults.length;
    setCurrentResultIndex(nextIndex);

    const element = document.getElementById(`transcript-line-${searchResults[nextIndex]}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [searchResults, currentResultIndex]);

  // Navigate to the previous search result
  const goToPrevResult = useCallback(() => {
    if (searchResults.length === 0) return;

    const prevIndex = (currentResultIndex - 1 + searchResults.length) % searchResults.length;
    setCurrentResultIndex(prevIndex);

    const element = document.getElementById(`transcript-line-${searchResults[prevIndex]}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [searchResults, currentResultIndex]);

  // Reset search when transcript dialog is closed
  useEffect(() => {
    if (!transcriptOpen) {
      setSearchQuery("");
      setSearchResults([]);
      setCurrentResultIndex(-1);
    }
  }, [transcriptOpen]);

  // Run search when query changes
  useEffect(() => {
    if (searchQuery.trim() && transcription) {
      const debounceTimer = setTimeout(() => {
        handleTranscriptSearch();
      }, 300);

      return () => clearTimeout(debounceTimer);
    } else if (!searchQuery.trim()) {
      setSearchResults([]);
      setCurrentResultIndex(-1);
    }
  }, [searchQuery, transcription, handleTranscriptSearch]);

  // Use a ref for the video card
  const videoCardRef = useRef<HTMLDivElement>(null);

  // Optimize transcription loading - only load when user clicks the button
  // This significantly reduces memory usage by not pre-loading transcriptions

  // Handle preview functionality - using direct HTML approach
  useEffect(() => {
    if (!settings?.useInAppPlayer) return;
    if (isFloatingPlayerActive) return;

    const shouldPreview = (isMobile && isFocused) || (!isMobile && isHovering);

    if (shouldPreview && !isPlaying) {
      // Start the preview with a delay
      previewTimeout.current = setTimeout(() => {
        const previewContainer = document.getElementById(`preview-${video.id}`);
        if (previewContainer && !isPlaying) {
          // Use direct HTML string with perfect positioning
          previewContainer.innerHTML = `
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; background-color: black;">
              <iframe
                src="https://www.youtube.com/embed/${video.id}?autoplay=1&mute=1&controls=1&modestbranding=1&rel=0&showinfo=0&fs=0&iv_load_policy=3&color=white&playsinline=1"
                style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: 0;"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
              ></iframe>
            </div>
          `;
        }
      }, 800);
    } else {
      // Clear the timeout
      if (previewTimeout.current) {
        clearTimeout(previewTimeout.current);
        previewTimeout.current = undefined;
      }

      // Reset to thumbnail if not playing
      const previewContainer = document.getElementById(`preview-${video.id}`);
      if (previewContainer && !isPlaying) {
        // Create a new YoutubeThumbnail component
        previewContainer.innerHTML = '';
        const thumbnailElement = document.createElement('div');
        thumbnailElement.className = 'w-full h-full';

        // Render the YoutubeThumbnail component
        const thumbnailImg = document.createElement('img');
        thumbnailImg.className = 'w-full h-full object-cover';
        thumbnailImg.alt = video.title;
        thumbnailImg.src = `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`;
        thumbnailImg.loading = 'lazy';

        // Add error handling for the thumbnail
        thumbnailImg.onerror = () => {
          thumbnailImg.src = `https://i.ytimg.com/vi/${video.id}/mqdefault.jpg`;
          thumbnailImg.onerror = () => {
            thumbnailImg.src = `https://i.ytimg.com/vi/${video.id}/default.jpg`;
            thumbnailImg.onerror = () => {
              thumbnailImg.src = 'https://placehold.co/480x360/222222/DDDDDD?text=No+Thumbnail';
            };
          };
        };

        thumbnailElement.appendChild(thumbnailImg);
        previewContainer.appendChild(thumbnailElement);
      }
    }

    return () => {
      if (previewTimeout.current) {
        clearTimeout(previewTimeout.current);
        previewTimeout.current = undefined;
      }
    };
  }, [isHovering, isFocused, isMobile, video.id, settings?.useInAppPlayer, isFloatingPlayerActive, isPlaying, video.thumbnail, video.title]);

  // Effect to fetch financial analysis when component mounts
  useEffect(() => {
    if (video.hasTranscription && video.hasFinancialAnalysis) {
      fetchFinancialAnalysis();
    }
  }, [video.id, video.hasTranscription, video.hasFinancialAnalysis, fetchFinancialAnalysis]);

  return (
    <Card
      ref={videoCardRef}
      className={cn(
        "overflow-hidden transition-all duration-200 h-full flex flex-col",
        isWatched && "opacity-70"
      )}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
    >
      <div className="relative aspect-video bg-muted overflow-hidden">
        <div id={`preview-${video.id}`} className="w-full h-full cursor-pointer absolute inset-0" onClick={openVideo}>
          <YoutubeThumbnail
            videoId={video.id}
            title={video.title}
            className="w-full h-full object-cover"
          />
        </div>
        {/* Duration */}
        <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 text-xs rounded">
          {video.contentDetails?.duration ? formatDuration(video.contentDetails.duration) : ""}
        </div>

        {/* Video Type Badge */}
        {video.contentDetails?.duration && (
          <div className="absolute top-2 right-12"> {/* Moved to the left of the bookmark button */}
            {getVideoType(video.title, video.contentDetails?.duration) === 'short' && (
              <div className="bg-red-500/90 text-white px-2 py-0.5 text-xs rounded-sm font-medium flex items-center">
                <Scissors className="h-3 w-3 mr-1" />
                Short
              </div>
            )}
            {getVideoType(video.title, video.contentDetails?.duration) === 'live' && (
              <div className="bg-red-600/90 text-white px-2 py-0.5 text-xs rounded-sm font-medium flex items-center">
                <Radio className="h-3 w-3 mr-1" />
                Live
              </div>
            )}
          </div>
        )}

        {/* Removed duplicate video type badges - we're using the ones above */}
        {isWatched && (
          <div className="absolute top-2 left-2 bg-primary px-2 py-1 rounded text-xs text-primary-foreground">
            Watched
          </div>
        )}
        {/* Add BookmarkButton for playlist functionality - positioned in the same place as in watch page */}
        <div className="absolute top-2 right-2">
          <BookmarkButton video={video} />
        </div>
      </div>
      <CardContent className="p-4 flex-grow flex flex-col">
        <h3
          className="font-semibold leading-tight line-clamp-2 hover:text-primary cursor-pointer"
          onClick={openVideo}
        >
          {video.title}
        </h3>
        <p className="text-sm text-muted-foreground mt-1">{video.channelTitle}</p>

        {/* Financial Analysis Summary */}
        {video.hasFinancialAnalysis && (
          <>
            <FinancialBenefit
              openRouterBenefitAmounts={video.openRouterBenefitAmounts}
              financialAmount={video.financialAmount}
              financialScore={video.financialScore}
              openRouterRawData={video.openRouterRawData}
            />
          </>
        )}

        <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
          <div className="flex items-center gap-1">
            {isLiveStream(video.title || '', video.contentDetails?.duration) && !isEndedLiveStream(video.title || '', video.isUnplayable) ? (
              <>
                <Radio className="w-4 h-4 text-red-500 animate-pulse" />
                <span className="text-red-500 font-medium">
                  {video.statistics?.viewCount
                    ? formatViewCount(parseInt(video.statistics.viewCount), true, video.publishedAt)
                    : formatViewCount(parseInt(video.viewCount.toString()), true, video.publishedAt)}
                </span>
              </>
            ) : (
              <>
                <Eye className="w-4 h-4" />
                <span>
                  {video.statistics?.viewCount
                    ? formatViewCount(parseInt(video.statistics.viewCount), false, video.publishedAt)
                    : formatViewCount(parseInt(video.viewCount.toString()), false, video.publishedAt)}
                  {isEndedLiveStream(video.title || '', video.isUnplayable) && (
                    <span className="ml-1 text-xs text-muted-foreground">(ended stream)</span>
                  )}
                </span>
              </>
            )}
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            <span>
              {(() => {
                try {
                  return formatRelativeTime(new Date(video.publishedAt));
                } catch (e) {
                  return 'recently';
                }
              })()}
            </span>
          </div>
        </div>
        <div className="mt-2 flex justify-between items-center mt-auto">
          <div className="flex items-center gap-2">
            <div className={`text-xs font-bold ${getViewsPerHourColor(viewsPerHour)}`}>
              <span className="text-sm">{viewsPerHour}</span> <span className="text-xs opacity-80">vph</span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            {/* Toggle Watched Status Button */}
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                if (isWatched) {
                  // Mark as unwatched
                  const watchedVideos = settings?.watchedVideos?.filter(id => id !== video.id) || [];
                  updateSettings.mutate({ ...settings, watchedVideos });
                } else {
                  // Mark as watched
                  const watchedVideos = [...(settings?.watchedVideos || []), video.id];
                  updateSettings.mutate({ ...settings, watchedVideos });
                }
              }}
              title={isWatched ? "Mark as unwatched" : "Mark as watched"}
            >
              {isWatched ? (
                <EyeOff className="h-4 w-4 text-muted-foreground hover:text-primary" />
              ) : (
                <Eye className="h-4 w-4 text-muted-foreground hover:text-primary" />
              )}
            </Button>

            {/* Combined Analysis Button */}
            {(hasTranscription || isPendingTranscription(video.id) || video.hasFinancialAnalysis) && (
              <Button
                variant="ghost"
                size="sm"
                className="p-0 h-8 px-2 relative flex items-center gap-1"
                onClick={(e) => {
                  e.stopPropagation();
                  handleLoadTranscription();
                  // Open AI analysis tab by default
                  setTimeout(() => {
                    const aiTab = document.querySelector('[data-value="ai"]');
                    if (aiTab) {
                      (aiTab as HTMLElement).click();
                    }
                  }, 100);
                }}
                disabled={isLoadingTranscript || isAnalyzing || isPendingTranscription(video.id)}
                title="View analysis and transcription"
              >
                <div className="relative flex items-center gap-1">
                  {isPendingTranscription(video.id) ? (
                    <Activity className="h-4 w-4 animate-pulse" />
                  ) : video.hasFinancialAnalysis ? (
                    <DollarSign className={cn(
                      "h-4 w-4",
                      isAnalyzing && "animate-pulse"
                    )} />
                  ) : (
                    <FileText className={cn(
                      "h-4 w-4",
                      isLoadingTranscript && "animate-pulse"
                    )} />
                  )}
                </div>
                {(isLoadingTranscript || isAnalyzing) && (
                  <span className="absolute -bottom-1 -right-1 h-2 w-2 bg-primary rounded-full animate-ping" />
                )}
                {isPendingTranscription(video.id) && (
                  <span className="absolute -bottom-1 -right-1 h-2 w-2 bg-blue-500 rounded-full animate-ping" />
                )}
                {((transcription && !transcription.includes('not found in your subscriptions') &&
                 !transcription.includes('Transcription not available') &&
                 !transcription.includes('Unable to fetch transcription')) ||
                 video.hasFinancialAnalysis) && (
                  <span className="absolute -bottom-1 -right-1 h-2 w-2 bg-green-500 rounded-full" />
                )}
              </Button>
            )}

            {/* Info Button */}
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                setMetadataOpen(true);
              }}
              title="View metadata"
            >
              <Info className="h-4 w-4 text-muted-foreground hover:text-primary" />
            </Button>

            {/* Financial Transcript Button ($) - Always show for all videos */}
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-8 px-2 relative flex items-center gap-1"
              onClick={(e) => {
                e.stopPropagation();
                // Load the transcript if not already loaded and open the financial transcript dialog
                if (!transcription) {
                  handleLoadTranscription(false, false, true);
                } else {
                  // Open the financial transcript dialog if transcript is already loaded
                  setFinancialTranscriptOpen(true);
                }
              }}
              disabled={isLoadingTranscript || isPendingTranscription(video.id)}
              title="View financial transcript analysis"
            >
              <div className="relative flex items-center gap-1">
                <DollarSign className={cn(
                  "h-4 w-4",
                  isLoadingTranscript && "animate-pulse"
                )} />
              </div>
              {isLoadingTranscript && (
                <span className="absolute -bottom-1 -right-1 h-2 w-2 bg-primary rounded-full animate-ping" />
              )}
              {isPendingTranscription(video.id) && (
                <span className="absolute -bottom-1 -right-1 h-2 w-2 bg-blue-500 rounded-full animate-ping" />
              )}
              {(transcription && !transcription.includes('not found in your subscriptions') &&
               !transcription.includes('Transcription not available') &&
               !transcription.includes('Unable to fetch transcription')) && (
                <span className="absolute -bottom-1 -right-1 h-2 w-2 bg-green-500 rounded-full" />
              )}
            </Button>
          </div>
        </div>

        {/* Metadata Dialog */}
        <Dialog open={metadataOpen} onOpenChange={setMetadataOpen}>
          <DialogContent className="max-w-3xl max-h-[80vh]">
            <DialogHeader>
              <DialogTitle>Video Details</DialogTitle>
              <DialogDescription>
                Information about this YouTube video
              </DialogDescription>
            </DialogHeader>
            <ScrollArea className="h-[60vh] mt-4">
              <div className="space-y-4">
                {/* Video Title and Basic Info */}
                <div className="border rounded-md p-3 bg-card">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-bold">Basic Info</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const basicInfo = {
                          id: video.id,
                          title: video.title,
                          channelTitle: video.channelTitle,
                          publishedAt: video.publishedAt,
                          viewCount: video.viewCount
                        };
                        navigator.clipboard.writeText(JSON.stringify(basicInfo, null, 2));
                      }}
                    >
                      Copy
                    </Button>
                  </div>
                  <h3 className="font-bold text-lg mb-2">{video.title}</h3>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div><span className="font-medium">Video ID:</span> {video.id}</div>
                    <div><span className="font-medium">URL:</span> https://youtube.com/watch?v={video.id}</div>
                    <div><span className="font-medium">Channel:</span> {video.channelTitle}</div>
                    <div><span className="font-medium">Channel ID:</span> {video.channelId || 'Unknown'}</div>
                    <div><span className="font-medium">Published:</span> {formatPublishedDate(new Date(video.publishedAt))}</div>
                    <div><span className="font-medium">Views:</span> {
                      video.statistics?.viewCount
                        ? formatViewCount(parseInt(video.statistics.viewCount), false, video.publishedAt)
                        : formatViewCount(parseInt(video.viewCount.toString()), false, video.publishedAt)
                    }</div>
                    <div><span className="font-medium">Type:</span> {getVideoType(video.title, video.contentDetails?.duration)}</div>
                    {video.statistics?.likeCount && (
                      <div><span className="font-medium">Likes:</span> {parseInt(video.statistics.likeCount).toLocaleString()}</div>
                    )}
                    {video.statistics?.commentCount && (
                      <div><span className="font-medium">Comments:</span> {parseInt(video.statistics.commentCount).toLocaleString()}</div>
                    )}
                    {video.contentDetails?.duration && (
                      <div><span className="font-medium">Duration:</span> {formatDuration(video.contentDetails.duration)}</div>
                    )}
                    {video.contentDetails?.definition && (
                      <div><span className="font-medium">Definition:</span> {video.contentDetails.definition.toUpperCase()}</div>
                    )}
                  </div>
                </div>

                {/* Statistics Section */}
                {video.statistics && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Statistics</h4>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center gap-1">
                        <Eye className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Views:</span> {
                          video.statistics?.viewCount
                            ? formatViewCount(parseInt(video.statistics.viewCount), false, video.publishedAt)
                            : formatViewCount(parseInt(video.viewCount.toString()), false, video.publishedAt)
                        }
                      </div>
                      {video.statistics.likeCount && (
                        <div className="flex items-center gap-1">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-muted-foreground">
                            <path d="M7 10v12" />
                            <path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z" />
                          </svg>
                          <span className="font-medium">Likes:</span> {parseInt(video.statistics.likeCount).toLocaleString()}
                        </div>
                      )}
                      {video.statistics.commentCount && (
                        <div className="flex items-center gap-1">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-muted-foreground">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                          </svg>
                          <span className="font-medium">Comments:</span> {parseInt(video.statistics.commentCount).toLocaleString()}
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Published:</span> {formatRelativeTime(new Date(video.publishedAt))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Description Section */}
                {video.description && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Description</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(video.description || '');
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="bg-muted p-2 rounded text-sm whitespace-pre-wrap max-h-[200px] overflow-auto">
                      {video.description || "No description available"}
                    </div>
                  </div>
                )}

                {/* Tags Section */}
                {video.tags && video.tags.length > 0 && (
                  <div className="border rounded-md p-3 bg-card">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-bold">Tags</h4>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(video.tags?.join(', ') || '');
                          toast({
                            title: "Copied",
                            description: "Tags copied to clipboard",
                          });
                        }}
                      >
                        Copy
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {video.tags?.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Transcription Section */}
                <div className="border rounded-md p-3 bg-card">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-bold">Transcription</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleLoadTranscription}
                      disabled={isLoadingTranscript}
                    >
                      {transcription ? "View" : "Load Transcription"}
                    </Button>
                  </div>
                  <div className="text-sm">
                    {hasTranscription ? (
                      <p className="text-green-600">Transcription available</p>
                    ) : (
                      <p className="text-muted-foreground">Transcription may be available</p>
                    )}
                  </div>
                </div>
              </div>
            </ScrollArea>
          </DialogContent>
        </Dialog>

        {/* Transcription Dialog */}
        <Dialog open={transcriptOpen} onOpenChange={setTranscriptOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                {transcription && (transcription.includes('not found in your subscriptions') ||
                 transcription.includes('Transcription not available') ||
                 transcription.includes('Unable to fetch transcription')) ? (
                  <>
                    Transcription Unavailable
                    <Badge variant="outline" className="ml-2 text-xs bg-destructive/10 text-destructive border-destructive/20">
                      Not Available
                    </Badge>
                  </>
                ) : (
                  <>
                    Video Transcription
                    <Badge variant="outline" className="ml-2 text-xs">
                      With Timestamps
                    </Badge>
                  </>
                )}
              </DialogTitle>
              <DialogDescription>
                {video.title}
              </DialogDescription>
            </DialogHeader>


            <Tabs defaultValue="ai" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="transcription">Transcription</TabsTrigger>
                <TabsTrigger value="ai" className={!video.openRouterPriorityTag ? "opacity-50" : ""}>
                  AI Analysis {video.openRouterPriorityTag && <Activity className="h-3 w-3 ml-1" />}
                </TabsTrigger>
                <TabsTrigger value="video">Video</TabsTrigger>
              </TabsList>

              {/* Search bar moved inside the transcription tab */}
              <TabsContent value="transcription" className="mt-4">
                {/* Search bar for transcription */}
                {transcription && !transcription.includes('not found in your subscriptions') &&
                 !transcription.includes('Transcription not available') &&
                 !transcription.includes('Unable to fetch transcription') && (
                  <div className="mb-2 flex items-center gap-2">
                    <div className="relative flex-1">
                      <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        placeholder="Search in transcript..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleTranscriptSearch();
                          }
                        }}
                        className="pl-8 pr-16"
                      />
                      {searchResults.length > 0 && (
                        <div className="absolute right-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">
                          {currentResultIndex + 1} of {searchResults.length}
                        </div>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleTranscriptSearch}
                      disabled={!searchQuery.trim()}
                    >
                      Search
                    </Button>
                    {searchResults.length > 0 && (
                      <>
                        <Button variant="outline" size="icon" onClick={goToPrevResult}>
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-up"><path d="m18 15-6-6-6 6"/></svg>
                        </Button>
                        <Button variant="outline" size="icon" onClick={goToNextResult}>
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>
                        </Button>
                      </>
                    )}
                  </div>
                )}
                <ScrollArea className="h-[55vh]">
                  {isLoadingTranscript ? (
                    <div className="space-y-4 p-4">
                      <div className="flex justify-center mb-4">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      </div>
                      <p className="text-center text-muted-foreground">Loading transcription for {video.title}...</p>
                      <p className="text-center text-xs text-muted-foreground">Video ID: {video.id}</p>
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-5/6" />
                      </div>
                    </div>
                  ) : transcription ? (
                    <div className="p-2">
                      {/* Check if transcription contains error messages */}
                      {(transcription.includes('not found in your subscriptions') ||
                        transcription.includes('Transcription not available') ||
                        transcription.includes('Unable to fetch transcription')) ? (
                        <div className="p-4 border border-destructive/30 bg-destructive/10 rounded-md">
                          <h3 className="text-lg font-semibold mb-2 text-destructive">Transcription Not Available</h3>
                          <div className="text-sm space-y-2">
                            {transcription.split('\n').map((line, index) => (
                              <p key={index}>{line}</p>
                            ))}
                            <div className="pt-4">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => window.open(`https://www.youtube.com/watch?v=${video.id}`, '_blank')}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                Watch on YouTube
                              </Button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        /* Normal transcription with timestamps */
                        <div>
                          {transcription.split('\n').map((line, index) => {
                            // Check if the line has a timestamp format [MM:SS]
                            const timestampMatch = line.match(/^\[(\d{2}:\d{2})\]\s*(.*)$/);

                            // Check if this line is a search result
                            const isSearchResult = searchResults.includes(index);
                            const isCurrentResult = index === searchResults[currentResultIndex];

                            // Function to highlight search terms in text
                            const highlightSearchTerm = (text: string) => {
                              if (!searchQuery.trim()) return text;

                              const parts = text.split(new RegExp(`(${searchQuery})`, 'gi'));
                              return parts.map((part, i) =>
                                part.toLowerCase() === searchQuery.toLowerCase() ?
                                  <span key={i} className="bg-yellow-200 dark:bg-yellow-800 text-black dark:text-white font-medium px-0.5 rounded">{part}</span> :
                                  part
                              );
                            };

                            if (timestampMatch) {
                              const [_, timestamp, text] = timestampMatch;
                              return (
                                <div
                                  id={`transcript-line-${index}`}
                                  key={index}
                                  className={`mb-2 flex ${isSearchResult ? 'bg-muted/50 rounded' : ''} ${isCurrentResult ? 'bg-primary/20 rounded' : ''}`}
                                >
                                  <button
                                    className="text-xs font-mono bg-muted hover:bg-primary/20 text-muted-foreground hover:text-primary rounded px-1 py-0.5 mr-2 w-12 text-center flex-shrink-0 cursor-pointer transition-colors"
                                    onClick={() => {
                                      // Convert MM:SS to seconds
                                      const [minutes, seconds] = timestamp.split(':').map(Number);
                                      const timeInSeconds = minutes * 60 + seconds;

                                      // Update the iframe src to include the start time
                                      const iframe = document.querySelector(`iframe[src*="${video.id}"]`) as HTMLIFrameElement;
                                      if (iframe) {
                                        // Set the current tab to video
                                        const videoTab = document.querySelector('[data-state="active"][value="video"]') as HTMLElement;
                                        if (!videoTab) {
                                          const videoTabTrigger = document.querySelector('[value="video"]') as HTMLElement;
                                          if (videoTabTrigger) videoTabTrigger.click();
                                        }

                                        // Update iframe src with timestamp
                                        const currentSrc = iframe.src;
                                        const newSrc = currentSrc.includes('?')
                                          ? currentSrc.replace(/([?&])start=\d+/, '$1start=' + timeInSeconds) + (currentSrc.includes('start=') ? '' : `&start=${timeInSeconds}`)
                                          : `${currentSrc}?start=${timeInSeconds}`;
                                        iframe.src = newSrc;
                                      }
                                    }}
                                    title="Click to jump to this timestamp in the video"
                                  >
                                    {timestamp}
                                  </button>
                                  <div className="text-sm">{highlightSearchTerm(text)}</div>
                                </div>
                              );
                            } else {
                              // For lines without timestamps (like title, etc.)
                              return (
                                <div
                                  id={`transcript-line-${index}`}
                                  key={index}
                                  className={`mb-2 text-sm ${isSearchResult ? 'bg-muted/50 rounded' : ''} ${isCurrentResult ? 'bg-primary/20 rounded' : ''}`}
                                >
                                  {highlightSearchTerm(line)}
                                </div>
                              );
                            }
                          })}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground mb-4">No transcription loaded yet</p>
                      <Button onClick={handleLoadTranscription}>
                        Load Transcription
                      </Button>
                    </div>
                  )}
                </ScrollArea>

                {/* Copy buttons - moved outside the ScrollArea but inside the TabsContent */}
                {transcription && (
                  <div className="mt-4 flex justify-end gap-2">
                    {/* Only show copy buttons for valid transcriptions */}
                    {!transcription.includes('not found in your subscriptions') &&
                     !transcription.includes('Transcription not available') &&
                     !transcription.includes('Unable to fetch transcription') ? (
                      <>
                        {/* Copy with timestamps */}
                        <Button
                          variant="outline"
                          onClick={() => {
                            // Create a formatted transcription with title, video link, and timestamps
                            const videoTitle = video.title;
                            const videoUrl = `https://www.youtube.com/watch?v=${video.id}`;

                            // Start with the title and video link
                            let formattedText = `${videoTitle}\n${videoUrl}\n\n`;

                            // Add the timestamp transcript
                            const lines = transcription.split('\n');
                            lines.forEach(line => {
                              const timestampMatch = line.match(/^\[(\d{2}:\d{2})\]\s*(.*)$/);
                              if (timestampMatch) {
                                const [_, timestamp, text] = timestampMatch;
                                formattedText += `[${timestamp}] ${text}\n`;
                              } else if (line.trim()) {
                                formattedText += `${line}\n`;
                              }
                            });

                            navigator.clipboard.writeText(formattedText)
                              .then(() => {
                                // Show success toast with more visible styling
                                toast({
                                  title: "✅ Transcript Copied!",
                                  description: `Transcript with timestamps copied to clipboard`,
                                  variant: "default",
                                  duration: 3000,
                                });

                                // Also provide visual feedback on the button itself
                                const button = document.activeElement as HTMLButtonElement;
                                if (button) {
                                  const originalText = button.innerText;
                                  button.innerText = "✓ Copied!";
                                  setTimeout(() => {
                                    button.innerText = originalText;
                                  }, 2000);
                                }
                              })
                              .catch(err => {
                                console.error("Failed to copy transcript:", err);
                                toast({
                                  title: "❌ Copy Failed",
                                  description: "Could not copy to clipboard. Please try again.",
                                  variant: "destructive",
                                });
                              });
                          }}
                        >
                          <Clock3 className="h-4 w-4 mr-2" />
                          Copy with Timestamps
                        </Button>

                        {/* Copy without timestamps */}
                        <Button
                          variant="default"
                          onClick={() => {
                            // Create a formatted transcription with title, video link, but without timestamps
                            const videoTitle = video.title;
                            const videoUrl = `https://www.youtube.com/watch?v=${video.id}`;

                            // Start with the title and video link
                            let formattedText = `${videoTitle}\n${videoUrl}\n\n`;

                            // Add the transcript without timestamps
                            const lines = transcription.split('\n');
                            lines.forEach(line => {
                              const timestampMatch = line.match(/^\[(\d{2}:\d{2})\]\s*(.*)$/);
                              if (timestampMatch) {
                                const [_, timestamp, text] = timestampMatch;
                                formattedText += `${text}\n`; // Only add the text, not the timestamp
                              } else if (line.trim()) {
                                formattedText += `${line}\n`;
                              }
                            });

                            navigator.clipboard.writeText(formattedText)
                              .then(() => {
                                // Show success toast with more visible styling
                                toast({
                                  title: "✅ Transcript Copied!",
                                  description: `Clean transcript (without timestamps) copied to clipboard`,
                                  variant: "default",
                                  duration: 3000,
                                });

                                // Also provide visual feedback on the button itself
                                const button = document.activeElement as HTMLButtonElement;
                                if (button) {
                                  const originalText = button.innerText;
                                  button.innerText = "✓ Copied!";
                                  setTimeout(() => {
                                    button.innerText = originalText;
                                  }, 2000);
                                }
                              })
                              .catch(err => {
                                console.error("Failed to copy transcript:", err);
                                toast({
                                  title: "❌ Copy Failed",
                                  description: "Could not copy to clipboard. Please try again.",
                                  variant: "destructive",
                                });
                              });
                          }}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Transcript
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="outline"
                        onClick={() => window.open(`https://www.youtube.com/watch?v=${video.id}`, '_blank')}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Watch on YouTube
                      </Button>
                    )}
                  </div>
                )}

              </TabsContent>



              {/* AI Analysis Tab */}
              <TabsContent value="ai" className="mt-4">
                <ScrollArea className="h-[60vh]">
                  <AIAnalysisTab
                    video={video}
                    onAnalysisComplete={() => {
                      // Refresh the video data by fetching it again
                      const fetchVideoData = async () => {
                        try {
                          console.log('Fetching updated video data after AI analysis');
                          const res = await apiRequest("GET", `/api/youtube-channels/videos/${video.id}`);
                          if (res.ok) {
                            const updatedVideo = await res.json();
                            console.log('Updated video data after AI analysis:', updatedVideo);
                            // Update the video in the query cache
                            queryClient.setQueryData([`/api/youtube-channels/videos/${video.id}`], updatedVideo);

                            // Invalidate queries to ensure UI updates
                            queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${video.id}`] });
                            queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${video.id}/financial-analysis`] });
                            queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/all-videos`] });
                          }
                        } catch (error) {
                          console.error('Error refreshing video data:', error);
                        }
                      };
                      fetchVideoData();
                    }}
                  />
                </ScrollArea>
              </TabsContent>

              <TabsContent value="video" className="mt-4">
                <div className="aspect-video">
                  <iframe
                    width="100%"
                    height="100%"
                    src={`https://www.youtube.com/embed/${video.id}`}
                    title={video.title}
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  ></iframe>
                </div>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>

        {/* Financial Transcript Dialog */}
        <FinancialTranscriptDialog
          open={financialTranscriptOpen}
          onOpenChange={setFinancialTranscriptOpen}
          video={video}
          transcription={transcription}
        />
      </CardContent>
    </Card>
  );
}
