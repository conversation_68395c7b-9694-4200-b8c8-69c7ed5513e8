// use-video-loader.ts
// Hook for loading videos with priority handling

import { useState, useCallback } from 'react';
import { Video } from '@shared/schema';
import { useRequestManager, RequestPriority, RequestType } from '@/lib/request-manager';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

export function useVideoLoader() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const requestManager = useRequestManager();
  const { toast } = useToast();
  
  // Function to load a video with high priority
  const loadVideo = useCallback(async (videoId: string): Promise<Video | null> => {
    setIsLoading(true);
    setError(null);
    
    return new Promise((resolve, reject) => {
      // Add the request to the manager with HIGH priority
      requestManager.addRequest(
        RequestType.VIDEO_LOAD,
        RequestPriority.HIGH,
        async () => {
          try {
            const response = await apiRequest('GET', `/api/youtube-videos/${videoId}`);
            
            if (!response.ok) {
              throw new Error(`Failed to load video: ${response.statusText}`);
            }
            
            return await response.json();
          } catch (err: any) {
            throw err;
          }
        },
        (result) => {
          setIsLoading(false);
          resolve(result);
        },
        (err) => {
          setIsLoading(false);
          setError(err instanceof Error ? err : new Error(String(err)));
          
          toast({
            title: 'Error Loading Video',
            description: err instanceof Error ? err.message : String(err),
            variant: 'destructive'
          });
          
          reject(err);
        }
      );
    });
  }, [requestManager, toast]);
  
  // Function to load multiple videos with high priority
  const loadVideos = useCallback(async (videoIds: string[]): Promise<Video[]> => {
    setIsLoading(true);
    setError(null);
    
    return new Promise((resolve, reject) => {
      // Add the request to the manager with HIGH priority
      requestManager.addRequest(
        RequestType.VIDEO_LOAD,
        RequestPriority.HIGH,
        async () => {
          try {
            const response = await apiRequest('POST', '/api/youtube-videos/batch', {
              videoIds
            });
            
            if (!response.ok) {
              throw new Error(`Failed to load videos: ${response.statusText}`);
            }
            
            return await response.json();
          } catch (err: any) {
            throw err;
          }
        },
        (result) => {
          setIsLoading(false);
          resolve(result);
        },
        (err) => {
          setIsLoading(false);
          setError(err instanceof Error ? err : new Error(String(err)));
          
          toast({
            title: 'Error Loading Videos',
            description: err instanceof Error ? err.message : String(err),
            variant: 'destructive'
          });
          
          reject(err);
        }
      );
    });
  }, [requestManager, toast]);
  
  // Function to cancel all video loading requests
  const cancelVideoLoads = useCallback(() => {
    const cancelCount = requestManager.clearRequestsByType(RequestType.VIDEO_LOAD);
    
    if (cancelCount > 0) {
      console.log(`Cancelled ${cancelCount} video load requests`);
    }
    
    setIsLoading(false);
  }, [requestManager]);
  
  return {
    loadVideo,
    loadVideos,
    cancelVideoLoads,
    isLoading,
    error
  };
}
