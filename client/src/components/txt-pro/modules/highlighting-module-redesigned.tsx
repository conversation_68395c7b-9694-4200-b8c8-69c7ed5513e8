import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { useWorkflow } from '../workflow-state';
import { 
  Highlighter, 
  Plus, 
  Save, 
  Trash2, 
  Copy, 
  TrendingUp, 
  TrendingDown, 
  Info,
  Zap,
  Target,
  BarChart3,
  Clock
} from 'lucide-react';

interface Script {
  id: string;
  title: string;
  sourceVideoUrl?: string;
  transcript: string;
  tags: string[];
  dateAdded: Date;
}

interface Highlight {
  id: string;
  scriptId: string;
  startIndex: number;
  endIndex: number;
  color: 'red' | 'green' | 'blue' | 'yellow';
  note: string;
  analyticsNote?: string;
  timestamp?: string;
  engagementLevel: 'high' | 'medium' | 'low' | 'experimental';
  dateCreated: Date;
}

interface HighlightingModuleProps {
  scripts: Script[];
  highlights: Highlight[];
  setHighlights: React.Dispatch<React.SetStateAction<Highlight[]>>;
  selectedScript: Script | null;
  toast: any;
}

const HighlightingModuleRedesigned: React.FC<HighlightingModuleProps> = ({
  scripts,
  highlights,
  setHighlights,
  selectedScript,
  toast
}) => {
  const { updateStepData, markStepComplete } = useWorkflow();
  const [isCreatingHighlight, setIsCreatingHighlight] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [selectionRange, setSelectionRange] = useState<{ start: number; end: number } | null>(null);
  const [highlightForm, setHighlightForm] = useState({
    color: 'red' as const,
    note: '',
    analyticsNote: '',
    timestamp: '',
    engagementLevel: 'high' as const
  });
  const [filterColor, setFilterColor] = useState<string>('all');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const colorOptions = [
    { 
      value: 'red', 
      label: 'High Engagement', 
      description: 'Sections that performed exceptionally well',
      bgColor: 'bg-red-100', 
      textColor: 'text-red-800',
      icon: TrendingUp
    },
    { 
      value: 'green', 
      label: 'Decent Engagement', 
      description: 'Solid performing content worth reusing',
      bgColor: 'bg-green-100', 
      textColor: 'text-green-800',
      icon: BarChart3
    },
    { 
      value: 'blue', 
      label: 'Important Info', 
      description: 'Low engagement but crucial information',
      bgColor: 'bg-blue-100', 
      textColor: 'text-blue-800',
      icon: Info
    },
    { 
      value: 'yellow', 
      label: 'Experimental', 
      description: 'New approaches to test and iterate',
      bgColor: 'bg-yellow-100', 
      textColor: 'text-yellow-800',
      icon: Zap
    }
  ];

  const engagementOptions = [
    { value: 'high', label: 'High Engagement', icon: TrendingUp },
    { value: 'medium', label: 'Medium Engagement', icon: BarChart3 },
    { value: 'low', label: 'Low Engagement', icon: TrendingDown },
    { value: 'experimental', label: 'Experimental', icon: Zap }
  ];

  const handleTextSelection = () => {
    if (textareaRef.current && selectedScript) {
      const start = textareaRef.current.selectionStart;
      const end = textareaRef.current.selectionEnd;
      const selected = textareaRef.current.value.substring(start, end);
      
      if (selected.trim() && start !== end) {
        setSelectedText(selected.trim());
        setSelectionRange({ start, end });
        setIsCreatingHighlight(true);
      }
    }
  };

  const handleCreateHighlight = () => {
    if (!selectedScript || !selectionRange || !highlightForm.note.trim()) {
      toast({
        title: "Validation Error",
        description: "Please select text and add a note",
        variant: "destructive"
      });
      return;
    }

    const newHighlight: Highlight = {
      id: `highlight_${Date.now()}`,
      scriptId: selectedScript.id,
      startIndex: selectionRange.start,
      endIndex: selectionRange.end,
      color: highlightForm.color,
      note: highlightForm.note.trim(),
      analyticsNote: highlightForm.analyticsNote.trim(),
      timestamp: highlightForm.timestamp.trim(),
      engagementLevel: highlightForm.engagementLevel,
      dateCreated: new Date()
    };

    setHighlights(prev => [...prev, newHighlight]);
    
    // Update workflow state
    const updatedHighlights = [...highlights, newHighlight];
    updateStepData('highlight-analysis', { highlights: updatedHighlights });
    markStepComplete('highlight-analysis');

    setHighlightForm({
      color: 'red',
      note: '',
      analyticsNote: '',
      timestamp: '',
      engagementLevel: 'high'
    });
    setSelectedText('');
    setSelectionRange(null);
    setIsCreatingHighlight(false);

    toast({
      title: "Highlight Created",
      description: "Analytics highlight has been added"
    });
  };

  const handleQuickHighlight = (color: Highlight['color'], note: string) => {
    if (!selectedScript || !selectionRange) return;

    const newHighlight: Highlight = {
      id: `highlight_${Date.now()}`,
      scriptId: selectedScript.id,
      startIndex: selectionRange.start,
      endIndex: selectionRange.end,
      color,
      note,
      analyticsNote: '',
      timestamp: '',
      engagementLevel: color === 'red' ? 'high' : color === 'green' ? 'medium' : 'low',
      dateCreated: new Date()
    };

    setHighlights(prev => [...prev, newHighlight]);
    
    // Update workflow state
    const updatedHighlights = [...highlights, newHighlight];
    updateStepData('highlight-analysis', { highlights: updatedHighlights });
    markStepComplete('highlight-analysis');

    setSelectedText('');
    setSelectionRange(null);

    toast({
      title: "Quick Highlight Added",
      description: `${note} highlight created`
    });
  };

  const handleDeleteHighlight = (highlightId: string) => {
    setHighlights(prev => prev.filter(h => h.id !== highlightId));
    toast({
      title: "Highlight Deleted",
      description: "Highlight has been removed"
    });
  };

  const scriptHighlights = highlights.filter(h => h.scriptId === selectedScript?.id);
  const filteredHighlights = scriptHighlights.filter(h => 
    filterColor === 'all' || h.color === filterColor
  );

  const renderHighlightedText = () => {
    if (!selectedScript) return null;

    let text = selectedScript.transcript;
    let result = [];
    let lastIndex = 0;

    // Sort highlights by start index
    const sortedHighlights = scriptHighlights.sort((a, b) => a.startIndex - b.startIndex);

    sortedHighlights.forEach((highlight, index) => {
      // Add text before highlight
      if (highlight.startIndex > lastIndex) {
        result.push(text.substring(lastIndex, highlight.startIndex));
      }

      // Add highlighted text
      const colorOption = colorOptions.find(opt => opt.value === highlight.color);
      result.push(
        <span 
          key={highlight.id}
          className={`${colorOption?.bgColor} ${colorOption?.textColor} px-1 rounded cursor-pointer`}
          title={`${highlight.note}${highlight.analyticsNote ? ` - ${highlight.analyticsNote}` : ''}`}
        >
          {text.substring(highlight.startIndex, highlight.endIndex)}
        </span>
      );

      lastIndex = highlight.endIndex;
    });

    // Add remaining text
    if (lastIndex < text.length) {
      result.push(text.substring(lastIndex));
    }

    return result;
  };

  return (
    <div className="space-y-6">
      {/* Value Proposition Header */}
      <Card className="border-orange-200 bg-orange-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Highlighter className="h-5 w-5 text-orange-600" />
            Engagement Hotspot Analysis
          </CardTitle>
          <CardDescription className="text-orange-700">
            💡 <strong>Why use this:</strong> Mark high-performing sections from your analytics. Identify what content resonates with your audience to replicate success in future scripts.
          </CardDescription>
          <div className="flex gap-2 mt-3">
            <Badge variant="secondary" className="text-xs">
              📊 Analytics-driven insights
            </Badge>
            <Badge variant="secondary" className="text-xs">
              🎯 Replicate success patterns
            </Badge>
            <Badge variant="secondary" className="text-xs">
              ⚡ Data-backed content decisions
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Analytics Highlights ({scriptHighlights.length})</CardTitle>
              <CardDescription>
                Mark engagement hotspots from your video analytics
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button onClick={() => setIsCreatingHighlight(true)} className="bg-orange-600 hover:bg-orange-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Highlight
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Method 1: Text Selection */}
          {selectedScript && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-orange-600" />
                <Label className="font-medium">Method 1: Select Text to Highlight</Label>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <Label className="text-sm font-medium mb-2 block">
                  Select text below to mark engagement hotspots:
                </Label>
                <div className="border rounded-lg p-4 bg-white min-h-[200px] max-h-[400px] overflow-y-auto">
                  {scriptHighlights.length > 0 ? (
                    <div className="text-sm leading-relaxed whitespace-pre-wrap">
                      {renderHighlightedText()}
                    </div>
                  ) : (
                    <Textarea
                      ref={textareaRef}
                      value={selectedScript.transcript}
                      readOnly
                      onMouseUp={handleTextSelection}
                      className="min-h-[200px] cursor-text border-0 p-0 resize-none"
                      placeholder="Script transcript will appear here..."
                    />
                  )}
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  💡 Tip: Highlight any text above to mark it as an engagement hotspot
                </p>
              </div>
            </div>
          )}

          {/* Method 2: Quick Highlight Buttons */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-green-600" />
              <Label className="font-medium">Method 2: Quick Highlight Categories</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {colorOptions.map(option => {
                const IconComponent = option.icon;
                return (
                  <Card 
                    key={option.value}
                    className={`p-4 hover:shadow-md transition-shadow cursor-pointer ${option.bgColor}`}
                    onClick={() => {
                      if (selectionRange) {
                        handleQuickHighlight(option.value as Highlight['color'], option.label);
                      } else {
                        setHighlightForm(prev => ({ ...prev, color: option.value as Highlight['color'] }));
                        setIsCreatingHighlight(true);
                      }
                    }}
                  >
                    <div className="text-center">
                      <IconComponent className={`h-8 w-8 mx-auto mb-2 ${option.textColor}`} />
                      <h4 className={`font-medium ${option.textColor}`}>{option.label}</h4>
                      <p className={`text-xs ${option.textColor} opacity-80`}>{option.description}</p>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Highlight Creation Form */}
      {isCreatingHighlight && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="text-lg text-orange-800">Add Analytics Highlight</CardTitle>
            <CardDescription className="text-orange-700">
              {selectedText ? `Selected: "${selectedText.substring(0, 50)}..."` : 'Add engagement data for this section'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="highlightColor">Engagement Level</Label>
                <Select
                  value={highlightForm.color}
                  onValueChange={(value: Highlight['color']) => setHighlightForm(prev => ({ ...prev, color: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {colorOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded ${option.bgColor}`}></div>
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="timestamp">Timestamp (Optional)</Label>
                <Input
                  id="timestamp"
                  placeholder="e.g., 2:15"
                  value={highlightForm.timestamp}
                  onChange={(e) => setHighlightForm(prev => ({ ...prev, timestamp: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="highlightNote">Note *</Label>
              <Input
                id="highlightNote"
                placeholder="e.g., High retention spike here"
                value={highlightForm.note}
                onChange={(e) => setHighlightForm(prev => ({ ...prev, note: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="analyticsNote">Analytics Details (Optional)</Label>
              <Textarea
                id="analyticsNote"
                placeholder="e.g., 85% retention, 12% engagement rate, comments spike"
                value={highlightForm.analyticsNote}
                onChange={(e) => setHighlightForm(prev => ({ ...prev, analyticsNote: e.target.value }))}
                rows={2}
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={handleCreateHighlight} className="bg-orange-600 hover:bg-orange-700">
                <Save className="h-4 w-4 mr-2" />
                Save Highlight
              </Button>
              <Button onClick={() => setIsCreatingHighlight(false)} variant="outline">
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Highlights Library */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Engagement Highlights</CardTitle>
            <Select value={filterColor} onValueChange={setFilterColor}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Highlights</SelectItem>
                {colorOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {filteredHighlights.map(highlight => {
              const colorOption = colorOptions.find(opt => opt.value === highlight.color);
              const IconComponent = colorOption?.icon || TrendingUp;
              const highlightedText = selectedScript?.transcript.substring(highlight.startIndex, highlight.endIndex);

              return (
                <Card key={highlight.id} className={`${colorOption?.bgColor} border-l-4 border-l-current`}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <IconComponent className={`h-4 w-4 ${colorOption?.textColor}`} />
                          <Badge className={`${colorOption?.bgColor} ${colorOption?.textColor} border-0`}>
                            {colorOption?.label}
                          </Badge>
                          {highlight.timestamp && (
                            <Badge variant="outline" className="text-xs">
                              <Clock className="h-3 w-3 mr-1" />
                              {highlight.timestamp}
                            </Badge>
                          )}
                        </div>

                        <p className="text-sm font-medium mb-2">{highlight.note}</p>

                        <div className="bg-white/50 p-2 rounded text-sm italic mb-2">
                          "{highlightedText?.substring(0, 100)}{(highlightedText?.length || 0) > 100 ? '...' : ''}"
                        </div>

                        {highlight.analyticsNote && (
                          <p className="text-xs text-muted-foreground">
                            📊 Analytics: {highlight.analyticsNote}
                          </p>
                        )}
                      </div>

                      <Button onClick={() => handleDeleteHighlight(highlight.id)} variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {filteredHighlights.length === 0 && (
            <div className="text-center py-12">
              <Highlighter className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Highlights Yet</h3>
              <p className="text-muted-foreground mb-4">
                {selectedScript
                  ? 'Start highlighting engagement hotspots from your analytics data'
                  : 'Select a script first to add engagement highlights'
                }
              </p>
              {selectedScript && (
                <Button onClick={() => setIsCreatingHighlight(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Highlight
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default HighlightingModuleRedesigned;
