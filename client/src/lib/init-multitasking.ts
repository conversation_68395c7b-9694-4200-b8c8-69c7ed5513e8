// init-multitasking.ts
// Initialize multitasking features when the application starts

// Wrap in try-catch to prevent initialization errors from breaking the app
let multitaskingSupport = {
  webWorkers: false,
  requestIdleCallback: false,
  serviceWorker: false,
  sharedWorkers: false,
  concurrency: 1
};

try {
  // Dynamically import to prevent initialization errors
  import('./multitasking').then(({ initializeMultitasking }) => {
    try {
      // Initialize multitasking features
      multitaskingSupport = initializeMultitasking();

      // Log multitasking support
      console.log('Multitasking support initialized:', multitaskingSupport);
    } catch (error) {
      console.error('Error initializing multitasking features:', error);
    }
  }).catch(error => {
    console.error('Error importing multitasking module:', error);
  });
} catch (error) {
  console.error('Critical error in multitasking initialization:', error);
}

// Export for use in other modules
export { multitaskingSupport };
