import express from 'express';
import { storage } from '../storage';

const router = express.Router();

// Authentication middleware
router.use((req, res, next) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: 'User not authenticated' });
  }
  next();
});

// Get all search URLs for the current user
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;

    const searchUrls = await storage.getYtrSearchUrls(userId);

    // Convert to frontend format
    const formattedUrls = searchUrls.map(url => ({
      id: url.id.toString(),
      name: url.name,
      url: url.url,
      resultsLimit: url.resultsLimit || 25,
      displayOrder: url.displayOrder
    }));

    res.json(formattedUrls);
  } catch (error) {
    console.error('Error fetching YTR search URLs:', error);
    res.status(500).json({ message: 'Failed to fetch search URLs' });
  }
});

// Create a new search URL
router.post('/', async (req, res) => {
  try {
    const userId = req.user.id;

    const { name, url, resultsLimit } = req.body;

    if (!name || !url) {
      return res.status(400).json({ message: 'Name and URL are required' });
    }

    // Validate results limit
    const limit = resultsLimit ? parseInt(resultsLimit) : 25;
    if (isNaN(limit) || limit < 1 || limit > 500) {
      return res.status(400).json({ message: 'Results limit must be between 1 and 500' });
    }

    // Validate URL
    try {
      new URL(url);
    } catch (error) {
      return res.status(400).json({ message: 'Invalid URL format' });
    }

    // Check if it's a YouTube search URL
    if (!url.includes('youtube.com/results')) {
      return res.status(400).json({ message: 'URL must be a valid YouTube search URL' });
    }

    const result = await storage.saveYtrSearchUrl(userId, name, url, limit);

    res.json({
      id: result.id.toString(),
      name,
      url,
      resultsLimit: limit,
      message: 'Search URL created successfully'
    });
  } catch (error) {
    console.error('Error creating YTR search URL:', error);
    res.status(500).json({ message: 'Failed to create search URL' });
  }
});

// Update an existing search URL
router.put('/:id', async (req, res) => {
  try {
    const userId = req.user.id;

    const id = parseInt(req.params.id);
    const { name, url, resultsLimit } = req.body;

    if (!name || !url) {
      return res.status(400).json({ message: 'Name and URL are required' });
    }

    // Validate results limit if provided
    let limit;
    if (resultsLimit !== undefined) {
      limit = parseInt(resultsLimit);
      if (isNaN(limit) || limit < 1 || limit > 500) {
        return res.status(400).json({ message: 'Results limit must be between 1 and 500' });
      }
    }

    // Validate URL
    try {
      new URL(url);
    } catch (error) {
      return res.status(400).json({ message: 'Invalid URL format' });
    }

    // Check if it's a YouTube search URL
    if (!url.includes('youtube.com/results')) {
      return res.status(400).json({ message: 'URL must be a valid YouTube search URL' });
    }

    const success = await storage.updateYtrSearchUrl(userId, id, name, url, limit);

    if (!success) {
      return res.status(404).json({ message: 'Search URL not found' });
    }

    res.json({
      id: id.toString(),
      name,
      url,
      resultsLimit: limit,
      message: 'Search URL updated successfully'
    });
  } catch (error) {
    console.error('Error updating YTR search URL:', error);
    res.status(500).json({ message: 'Failed to update search URL' });
  }
});

// Delete a search URL
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.user.id;

    const id = parseInt(req.params.id);
    const success = await storage.deleteYtrSearchUrl(userId, id);

    if (!success) {
      return res.status(404).json({ message: 'Search URL not found' });
    }

    res.json({ message: 'Search URL deleted successfully' });
  } catch (error) {
    console.error('Error deleting YTR search URL:', error);
    res.status(500).json({ message: 'Failed to delete search URL' });
  }
});

// Reorder search URLs
router.post('/reorder', async (req, res) => {
  try {
    const userId = req.user.id;

    const { urlOrders } = req.body;

    if (!Array.isArray(urlOrders)) {
      return res.status(400).json({ message: 'urlOrders must be an array' });
    }

    // Convert string IDs to numbers
    const numericOrders = urlOrders.map(order => ({
      id: parseInt(order.id),
      displayOrder: order.displayOrder
    }));

    const success = await storage.reorderYtrSearchUrls(userId, numericOrders);

    if (!success) {
      return res.status(500).json({ message: 'Failed to reorder search URLs' });
    }

    res.json({ message: 'Search URLs reordered successfully' });
  } catch (error) {
    console.error('Error reordering YTR search URLs:', error);
    res.status(500).json({ message: 'Failed to reorder search URLs' });
  }
});

// Migrate localStorage data to database
router.post('/migrate', async (req, res) => {
  try {
    const userId = req.user.id;

    const { searchUrls } = req.body;

    if (!Array.isArray(searchUrls)) {
      return res.status(400).json({ message: 'searchUrls must be an array' });
    }

    const results = [];

    for (const searchUrl of searchUrls) {
      try {
        const resultsLimit = searchUrl.resultsLimit || 25;
        const result = await storage.saveYtrSearchUrl(userId, searchUrl.name, searchUrl.url, resultsLimit);
        results.push({
          id: result.id.toString(),
          name: searchUrl.name,
          url: searchUrl.url,
          resultsLimit: resultsLimit,
          success: true
        });
      } catch (error) {
        console.error(`Error migrating search URL "${searchUrl.name}":`, error);
        results.push({
          name: searchUrl.name,
          url: searchUrl.url,
          success: false,
          error: error.message
        });
      }
    }

    const successCount = results.filter(r => r.success).length;

    res.json({
      message: `Migrated ${successCount} out of ${searchUrls.length} search URLs`,
      results
    });
  } catch (error) {
    console.error('Error migrating YTR search URLs:', error);
    res.status(500).json({ message: 'Failed to migrate search URLs' });
  }
});

export default router;
