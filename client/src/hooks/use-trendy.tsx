import { useQuery, useMutation } from "@tanstack/react-query";
import { Video } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export function useTrendy() {
  const { toast } = useToast();

  const {
    data: videos = [],
    isLoading,
    error,
    refetch,
  } = useQuery<Video[]>({
    queryKey: ["/api/trendy"],
    staleTime: 60000, // 1 minute - override the global setting
  });

  const refresh = useMutation({
    mutationFn: async () => {
      // Get all keyword groups first
      const groupsRes = await apiRequest("GET", "/api/keyword-groups");
      const groups = await groupsRes.json();

      // Refresh the trendy feed
      const trendyRes = await apiRequest("POST", "/api/trendy/refresh");
      const trendyResult = await trendyRes.json();

      // Refresh each keyword group
      const refreshPromises = groups.map(async (group: any) => {
        try {
          const res = await apiRequest("POST", `/api/keyword-groups/${group.id}/refresh`);
          return res.json();
        } catch (error) {
          console.error(`Error refreshing group ${group.id}:`, error);
          return null;
        }
      });

      // Wait for all refreshes to complete
      await Promise.all(refreshPromises);

      return trendyResult;
    },
    onSuccess: () => {
      // Invalidate all relevant queries
      queryClient.invalidateQueries({ queryKey: ["/api/trendy"] });
      queryClient.invalidateQueries({ queryKey: ["/api/keyword-groups"] });
      // Invalidate all group videos queries
      queryClient.invalidateQueries({ predicate: (query) => query.queryKey[0]?.toString().includes('/api/keyword-groups/') });

      toast({
        title: "All feeds refreshed",
        description: "Your trendy feed and all keyword groups have been updated.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Refresh failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    videos,
    isLoading,
    error,
    refresh,
    refetch, // Add refetch function to allow manual refreshing
  };
}
