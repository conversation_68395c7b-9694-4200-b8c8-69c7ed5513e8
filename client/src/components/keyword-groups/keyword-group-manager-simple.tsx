import { useState, useRef, useEffect } from "react";
import { KeywordGroup } from "@shared/schema";
import { useKeywordGroups } from "@/hooks/use-keyword-groups";
import { useVideos } from "@/hooks/use-videos";
import { useSettings } from "@/hooks/use-settings";
import { DraggableList } from "./simple-dnd";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Loader2, Plus, Download, Upload, Eye, EyeOff } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import "./simple-dnd.css";

export function KeywordGroupManager() {
  const { groups, isLoading, createGroup, updateGroup, deleteGroup, setActiveGroup, refreshGroupVideos, exportGroups, importGroups, reorderGroups } = useKeywordGroups();
  const { refresh: refreshGlobalVideos } = useVideos();
  const { settings, updateSettings } = useSettings();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<KeywordGroup | null>(null);

  const [newGroupName, setNewGroupName] = useState("");
  const [newGroupKeywords, setNewGroupKeywords] = useState("");
  const [newGroupExcludeWords, setNewGroupExcludeWords] = useState("");

  const [editGroupName, setEditGroupName] = useState("");
  const [editGroupKeywords, setEditGroupKeywords] = useState("");
  const [editGroupExcludeWords, setEditGroupExcludeWords] = useState("");

  const [importData, setImportData] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State for tracking expanded/collapsed groups
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  const [showAllKeywords, setShowAllKeywords] = useState(false);

  // State for adding new keywords
  const [newKeyword, setNewKeyword] = useState("");
  const [isAddKeywordDialogOpen, setIsAddKeywordDialogOpen] = useState(false);
  const [addingKeywordToGroupId, setAddingKeywordToGroupId] = useState<number | null>(null);



  // Handle reordering of groups
  const handleReorder = (reorderedItems: KeywordGroup[]) => {
    // Create the payload for the API
    const orderData = reorderedItems.map((item, index) => ({
      id: item.id,
      order: index
    }));

    // Call the API to update the order
    reorderGroups.mutate(orderData);
  };

  // Toggle group expansion
  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  // Open edit dialog for a group
  const openEditDialog = (group: KeywordGroup) => {
    setSelectedGroup(group);
    setEditGroupName(group.name);
    setEditGroupKeywords(group.keywords.join(", "));
    setEditGroupExcludeWords(group.excludeWords?.join(", ") || "");
    setIsEditDialogOpen(true);
  };

  // Open delete dialog for a group
  const openDeleteDialog = (group: KeywordGroup) => {
    setSelectedGroup(group);
    setIsDeleteDialogOpen(true);
  };

  // Open add keyword dialog
  const openAddKeywordDialog = (groupId: number | null) => {
    setAddingKeywordToGroupId(groupId);
    setNewKeyword("");
    setIsAddKeywordDialogOpen(true);
  };

  // Handle creating a new group
  const handleCreateGroup = () => {
    const keywords = newGroupKeywords.split(",").map(k => k.trim()).filter(k => k);
    const excludeWords = newGroupExcludeWords ? newGroupExcludeWords.split(",").map(w => w.trim()).filter(w => w) : [];

    // Convert group name to uppercase
    const uppercaseName = newGroupName.toUpperCase();

    createGroup.mutate({
      name: uppercaseName,
      keywords,
      excludeWords
    }, {
      onSuccess: () => {
        setIsCreateDialogOpen(false);
        setNewGroupName("");
        setNewGroupKeywords("");
        setNewGroupExcludeWords("");
      }
    });
  };

  // Handle updating a group
  const handleUpdateGroup = () => {
    if (!selectedGroup) return;

    // Don't allow editing the global feed directly
    if (selectedGroup.id === 0) {
      toast({
        title: "Cannot edit Global Feed",
        description: "The Global Feed is a combination of all keyword groups and cannot be edited directly.",
        variant: "destructive"
      });
      setIsEditDialogOpen(false);
      return;
    }

    const keywords = editGroupKeywords.split(",").map(k => k.trim()).filter(k => k);
    const excludeWords = editGroupExcludeWords ? editGroupExcludeWords.split(",").map(w => w.trim()).filter(w => w) : [];

    // Update a keyword group
    // Convert group name to uppercase
    const uppercaseName = editGroupName.toUpperCase();

    updateGroup.mutate({
      id: selectedGroup.id,
      data: {
        name: uppercaseName,
        keywords,
        excludeWords
      }
    }, {
      onSuccess: () => {
        setIsEditDialogOpen(false);
      }
    });
  };

  // Handle deleting a group
  const handleDeleteGroup = () => {
    if (!selectedGroup) return;

    deleteGroup.mutate(selectedGroup.id, {
      onSuccess: () => {
        setIsDeleteDialogOpen(false);
      }
    });
  };

  // Handle setting active group
  const handleSetActiveGroup = (groupId: number | null) => {
    setActiveGroup.mutate(groupId);
  };

  // Handle refreshing a group
  const handleRefreshGroup = (groupId: number) => {
    refreshGroupVideos.mutate(groupId);
  };

  // Handle refreshing global feed
  const handleRefreshGlobal = () => {
    refreshGlobalVideos.mutate();
  };

  // Handle exporting groups
  const handleExportGroups = () => {
    exportGroups();
  };

  // Handle importing groups
  const handleImportGroups = () => {
    try {
      const data = JSON.parse(importData);
      importGroups.mutate(data, {
        onSuccess: () => {
          setIsImportDialogOpen(false);
          setImportData("");
        }
      });
    } catch (error) {
      toast({
        title: "Invalid JSON",
        description: "Please provide valid JSON data.",
        variant: "destructive",
      });
    }
  };

  // Handle file upload for import
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const content = event.target?.result as string;
        setImportData(content);
      } catch (error) {
        toast({
          title: "Error reading file",
          description: "Could not read the uploaded file.",
          variant: "destructive",
        });
      }
    };
    reader.readAsText(file);
  };

  // Handle removing a keyword from a group
  const handleRemoveGroupKeyword = (groupId: number, keyword: string) => {
    const group = groups?.find(g => g.id === groupId);
    if (!group) return;

    const updatedKeywords = group.keywords.filter(k => k !== keyword);
    updateGroup.mutate({
      id: groupId,
      data: {
        name: group.name,
        keywords: updatedKeywords,
        excludeWords: group.excludeWords
      }
    });
  };

  // Handle removing a keyword from global settings
  const handleRemoveGlobalKeyword = (keyword: string) => {
    if (!settings) return;

    const updatedKeywords = settings.searchKeywords.filter(k => k !== keyword);
    updateSettings({
      searchKeywords: updatedKeywords
    });
  };

  // Handle adding a keyword
  const handleAddKeyword = () => {
    if (!newKeyword.trim()) return;

    // Split by commas and trim each keyword
    const keywordsToAdd = newKeyword.split(",").map(k => k.trim()).filter(k => k);

    if (addingKeywordToGroupId === 0) {
      // Adding to global feed
      if (!settings) return;

      // Filter out keywords that already exist
      const existingKeywords = settings.searchKeywords;
      const newKeywords = keywordsToAdd.filter(keyword => !existingKeywords.includes(keyword));

      if (newKeywords.length > 0) {
        updateSettings({
          searchKeywords: [...existingKeywords, ...newKeywords]
        });

        toast({
          title: "Keywords added",
          description: newKeywords.length === 1
            ? `Added "${newKeywords[0]}" to global feed.`
            : `Added ${newKeywords.length} keywords to global feed.`
        });
      }

      setIsAddKeywordDialogOpen(false);
    } else {
      // Adding to a custom group
      const group = groups?.find(g => g.id === addingKeywordToGroupId);
      if (!group) return;

      // Filter out keywords that already exist
      const existingKeywords = group.keywords;
      const newKeywords = keywordsToAdd.filter(keyword => !existingKeywords.includes(keyword));
      const duplicateKeywords = keywordsToAdd.filter(keyword => existingKeywords.includes(keyword));

      // Show a toast if there are duplicate keywords
      if (duplicateKeywords.length > 0) {
        toast({
          title: duplicateKeywords.length === 1 ? "Keyword already exists" : "Some keywords already exist",
          description: duplicateKeywords.length === 1
            ? `The keyword "${duplicateKeywords[0]}" already exists in this group.`
            : `The following keywords already exist: ${duplicateKeywords.join(", ")}`,
          variant: "destructive",
        });

        // If all keywords are duplicates, close the dialog and return
        if (newKeywords.length === 0) {
          setIsAddKeywordDialogOpen(false);
          return;
        }
      }

      // Add the new keywords to the group
      const updatedKeywords = [...existingKeywords, ...newKeywords];

      updateGroup.mutate({
        id: addingKeywordToGroupId,
        data: {
          name: group.name,
          keywords: updatedKeywords,
          excludeWords: group.excludeWords
        }
      }, {
        onSuccess: () => {
          // Show success toast with the number of keywords added
          toast({
            title: "Keywords added",
            description: newKeywords.length === 1
              ? `Added 1 keyword to ${group.name}.`
              : `Added ${newKeywords.length} keywords to ${group.name}.`
          });

          setIsAddKeywordDialogOpen(false);
          setNewKeyword("");
        }
      });
    }
  };

  // Toggle all groups expansion
  const toggleAllGroups = () => {
    if (showAllKeywords) {
      // Collapse all groups
      setExpandedGroups({});
    } else {
      // Expand all groups
      const newExpandedGroups: Record<string, boolean> = { 'global': true };
      groups?.forEach(group => {
        newExpandedGroups[group.id.toString()] = true;
      });
      setExpandedGroups(newExpandedGroups);
    }
    setShowAllKeywords(!showAllKeywords);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Keyword Groups</h1>
        <div className="flex gap-2">
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Group
          </Button>

          <Button variant="outline" onClick={handleExportGroups}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" onClick={() => setIsImportDialogOpen(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleAllGroups}
                >
                  {showAllKeywords ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{showAllKeywords ? "Collapse all groups" : "Expand all groups"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <DraggableList
          items={groups || []}
          onReorder={handleReorder}
          expandedGroups={expandedGroups}
          toggleGroupExpansion={toggleGroupExpansion}
          openEditDialog={openEditDialog}
          openDeleteDialog={openDeleteDialog}
          handleRefreshGroup={handleRefreshGroup}
          handleSetActiveGroup={handleSetActiveGroup}
          openAddKeywordDialog={openAddKeywordDialog}
          handleRemoveGroupKeyword={handleRemoveGroupKeyword}
          handleRemoveGlobalKeyword={handleRemoveGlobalKeyword}
          refreshGroupVideos={refreshGroupVideos}
          refreshGlobalVideos={refreshGlobalVideos}
          setActiveGroup={setActiveGroup}
          settings={settings}
        />
      )}

      {/* Create Group Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Keyword Group</DialogTitle>
            <DialogDescription>
              Create a new group to organize your keywords.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="name">Group Name</label>
              <Input
                id="name"
                value={newGroupName}
                onChange={(e) => setNewGroupName(e.target.value)}
                placeholder="Tech News"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="keywords">Keywords (comma-separated)</label>
              <Input
                id="keywords"
                value={newGroupKeywords}
                onChange={(e) => setNewGroupKeywords(e.target.value)}
                placeholder="tech, apple, google"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="exclude-words">Exclude Words (comma-separated)</label>
              <Input
                id="exclude-words"
                value={newGroupExcludeWords}
                onChange={(e) => setNewGroupExcludeWords(e.target.value)}
                placeholder="spam, clickbait"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateGroup} disabled={createGroup.isPending || !newGroupName || !newGroupKeywords}>
              {createGroup.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Create Group
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Group Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit {selectedGroup?.id === 0 ? "Global Feed" : "Keyword Group"}</DialogTitle>
            <DialogDescription>
              {selectedGroup?.id === 0
                ? "The Global Feed is a combination of all keyword groups and cannot be edited directly. Please edit individual keyword groups instead."
                : "Update the group settings."}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {selectedGroup?.id !== 0 ? (
              <>
                <div className="grid gap-2">
                  <label htmlFor="edit-name">Group Name</label>
                  <Input
                    id="edit-name"
                    value={editGroupName}
                    onChange={(e) => setEditGroupName(e.target.value)}
                  />
                </div>

                <div className="grid gap-2">
                  <label htmlFor="edit-keywords">Keywords (comma-separated)</label>
                  <Textarea
                    id="edit-keywords"
                    value={editGroupKeywords}
                    onChange={(e) => setEditGroupKeywords(e.target.value)}
                    rows={4}
                  />
                </div>

                <div className="grid gap-2">
                  <label htmlFor="edit-exclude-words">Exclude Words (comma-separated)</label>
                  <Textarea
                    id="edit-exclude-words"
                    value={editGroupExcludeWords}
                    onChange={(e) => setEditGroupExcludeWords(e.target.value)}
                    rows={4}
                  />
                </div>
              </>
            ) : (
              <div className="p-4 border rounded-md bg-muted">
                <p className="text-center text-muted-foreground">
                  The Global Feed combines content from all your keyword groups.
                  To modify its content, please edit the individual keyword groups instead.
                </p>
              </div>
            )}
          </div>

          <DialogFooter>
            {selectedGroup?.id !== 0 ? (
              <>
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleUpdateGroup}
                  disabled={updateGroup.isPending || !editGroupName}
                >
                  {updateGroup.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Save Changes
                </Button>
              </>
            ) : (
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Close
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Group Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Keyword Group</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedGroup?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteGroup}
              disabled={deleteGroup.isPending}
            >
              {deleteGroup.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete Group
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Keyword Groups</DialogTitle>
            <DialogDescription>
              Import keyword groups from a JSON file or paste JSON data.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="import-file">Upload JSON File</Label>
              <Input
                id="import-file"
                type="file"
                accept=".json"
                ref={fileInputRef}
                onChange={handleFileUpload}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="import-data">Or Paste JSON Data</Label>
              <Textarea
                id="import-data"
                value={importData}
                onChange={(e) => setImportData(e.target.value)}
                rows={10}
                placeholder='[{"name": "Tech", "keywords": ["apple", "google"], "excludeWords": ["spam"]}]'
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleImportGroups}
              disabled={importGroups.isPending || !importData}
            >
              {importGroups.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Import Groups
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Keyword Dialog */}
      <Dialog open={isAddKeywordDialogOpen} onOpenChange={setIsAddKeywordDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Keywords</DialogTitle>
            <DialogDescription>
              Add keywords to {addingKeywordToGroupId === 0 ? "global feed" : `group "${groups?.find(g => g.id === addingKeywordToGroupId)?.name || ''}"`.trim()}.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="new-keyword">Keywords (comma-separated)</Label>
              <Input
                id="new-keyword"
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                placeholder="Enter keywords separated by commas"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddKeywordDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleAddKeyword}
              disabled={!newKeyword.trim()}
            >
              Add Keywords
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
