import Database from 'better-sqlite3';
import { User, Video, UserSettings, InsertUser, YoutubeChannel, YoutubeVideo, OllamaPrompt, TsChannel, TsVideo } from "@shared/schema";
import session from "express-session";
import createMemoryStore from "memorystore";
import fetch from "node-fetch";
import axios from "axios";
import * as cheerio from "cheerio";
import 'dotenv/config';
import { IStorage, VidIQApiKey } from './storage';
import { batchFetchViewCounts } from './utils/youtube-utils';
import { implementOllamaPromptMethods } from './ollama-prompts';
import { implementOpenRouterApiKeyMethods } from './openrouter-api-keys';
import { implementOpenRouterPromptMethods } from './openrouter-prompts';
import { implementRssFeedMethods } from './rss-feeds';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class SQLiteStorage implements IStorage {
    // Make fetchChannelVideos public so it can be used by the refresh-metadata endpoint
    public async fetchChannelVideos(channel: YoutubeChannel): Promise<Partial<YoutubeVideo>[]> {
        try {
            console.log(`Fetching videos for channel: ${channel.channelTitle} (ID: ${channel.id}, channelId: ${channel.channelId})`);
            // Use RSS feed to get recent videos (this is more reliable than scraping)
            const channelId = channel.channelId;

            // Make sure the channel ID is in the correct format
            // YouTube channel IDs typically start with 'UC'
            if (!channelId.startsWith('UC')) {
                console.warn(`Channel ID ${channelId} doesn't start with UC, which might cause issues`);
            }

            // Try different RSS feed formats
            let response;
            let rssUrl;

            try {
                // First try the standard RSS feed format
                rssUrl = `https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`;
                console.log(`Trying RSS feed URL: ${rssUrl}`);
                response = await axios.get(rssUrl);
            } catch (error) {
                console.error(`Error fetching from standard RSS feed: ${error.message}`);

                // Try the user uploads playlist format as a fallback
                try {
                    const playlistId = channelId.replace('UC', 'UU');
                    rssUrl = `https://www.youtube.com/feeds/videos.xml?playlist_id=${playlistId}`;
                    console.log(`Trying playlist RSS feed URL: ${rssUrl}`);
                    response = await axios.get(rssUrl);
                } catch (playlistError) {
                    console.error(`Error fetching from playlist RSS feed: ${playlistError.message}`);
                    throw new Error(`Failed to fetch videos for channel ${channel.channelTitle}: ${error.message}`);
                }
            }

            const $ = cheerio.load(response.data, { xmlMode: true });
            const videos: Partial<YoutubeVideo>[] = [];

            // Get the video limit from the channel settings
            const videoLimit = channel.videoLimit || 15;
            console.log(`Fetching up to ${videoLimit} videos for channel ${channel.channelTitle}`);

            // Counter for videos processed
            let videoCount = 0;

            $('entry').each((i, entry) => {
                // Check if we've reached the limit
                if (videoCount >= videoLimit) {
                    return false; // Stop processing more entries
                }

                const $entry = $(entry);

                // Extract video ID from the yt:videoId tag
                const videoId = $entry.find('yt\\:videoId').text();
                if (!videoId) {
                    console.warn('Could not find video ID in entry, skipping');
                    return;
                }

                // Extract other video details
                const title = $entry.find('title').text();
                const channelTitle = $entry.find('author > name').text();
                const publishedAt = new Date($entry.find('published').text());
                const updatedAt = new Date($entry.find('updated').text());
                const description = $entry.find('media\\:group > media\\:description').text() || $entry.find('content').text();

                // Extract thumbnail URL
                const thumbnailUrl = $entry.find('media\\:group > media\\:thumbnail').attr('url') ||
                                    `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;

                // Create a video object
                const video: Partial<YoutubeVideo> = {
                    id: videoId,
                    title,
                    channelId: channel.channelId,
                    channelTitle,
                    publishedAt,
                    thumbnail: thumbnailUrl,
                    description,
                    viewCount: 0, // Will be updated later
                    userId: channel.userId
                };

                videos.push(video);
                videoCount++;
            });

            console.log(`Found ${videos.length} videos in RSS feed for channel ${channel.channelTitle}`);

            // Batch fetch view counts for all videos
            if (videos.length > 0) {
                try {
                    console.log(`Fetching view counts for ${videos.length} videos`);
                    const videoIds = videos.map(v => v.id!);

                    // For the refresh-metadata endpoint, we want to get the current live viewer count for live videos
                    // This ensures we show the current number of viewers for live streams
                    // Use a larger batch size (10) for better performance
                    const viewCounts = await batchFetchViewCounts(videoIds, 10, false);

                    // Update view counts in the video objects
                    videos.forEach(video => {
                        if (video.id && viewCounts.has(video.id)) {
                            const viewCount = viewCounts.get(video.id);
                            // Check if this is a live video by looking at the title
                            const isLive = video.title && (
                                video.title.includes('🔴 LIVE') ||
                                video.title.includes('🔴LIVE') ||
                                video.title.includes('[LIVE]') ||
                                video.title.includes('(LIVE)') ||
                                video.title.includes('LIVE:') ||
                                video.title.includes('LIVE STREAM') ||
                                video.title.includes('LIVESTREAM') ||
                                video.title.includes('LIVE NOW') ||
                                video.title.includes('STREAMING NOW') ||
                                video.title.includes('STREAMING LIVE')
                            );

                            if (isLive) {
                                console.log(`Live video detected: ${video.title} (ID: ${video.id}) - Current viewers: ${viewCount}`);
                            }

                            video.viewCount = viewCount;
                        }
                    });

                    console.log(`Updated view counts for ${videos.length} videos`);
                } catch (error) {
                    console.error(`Error fetching view counts: ${error.message}`);
                    // Continue without view counts
                }
            }

            return videos;
        } catch (error) {
            console.error(`Error fetching videos for channel ${channel.channelTitle}:`, error);
            throw error;
        }
    }

    // Add method to save a YouTube video
    async saveYoutubeVideo(video: Partial<YoutubeVideo>): Promise<void> {
        try {
            const insertStmt = this.db.prepare(`
                INSERT INTO youtube_videos (
                    id, title, thumbnail, channel_id, channel_title,
                    view_count, published_at, user_id, description,
                    duration, has_transcription
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            insertStmt.run(
                video.id,
                video.title,
                video.thumbnail,
                video.channelId,
                video.channelTitle,
                video.viewCount || 0,
                video.publishedAt?.toISOString(),
                video.userId,
                video.description || null,
                video.duration || 'PT0S',
                video.hasTranscription ? 1 : 0
            );
        } catch (error) {
            console.error(`Error saving YouTube video ${video.id}:`, error);
            throw error;
        }
    }

    // YTR Search Results Management
    async saveYtrSearchResults(userId: number, searchUrl: string, videos: any[]): Promise<void> {
        try {
            const searchUrlHash = this.hashString(searchUrl);
            const now = Date.now();

            // First, delete existing results for this user and search URL
            const deleteStmt = this.db.prepare(`
                DELETE FROM ytr_search_results
                WHERE user_id = ? AND search_url_hash = ?
            `);
            deleteStmt.run(userId, searchUrlHash);

            // Insert new results
            const insertStmt = this.db.prepare(`
                INSERT INTO ytr_search_results (
                    user_id, search_url, search_url_hash, video_id, title, thumbnail,
                    channel_id, channel_title, view_count, published_at, published_time_text,
                    is_live, duration, description, last_updated, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

            for (const video of videos) {
                // Ensure thumbnail is always available
                const thumbnailUrl = video.thumbnail || video.thumbnailUrl || `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`;

                // Debug logging for thumbnail issues
                if (!thumbnailUrl || thumbnailUrl === 'undefined') {
                    console.log(`YTR Storage: WARNING - No thumbnail for video ${video.id}, using fallback`);
                }

                insertStmt.run(
                    userId,
                    searchUrl,
                    searchUrlHash,
                    video.id,
                    video.title,
                    thumbnailUrl,
                    video.channelId || '',
                    video.channelTitle || '',
                    video.viewCount || 0,
                    video.publishedAt || new Date().toISOString(),
                    video.publishedTimeText || '',
                    video.isLive ? 1 : 0,
                    video.duration || '',
                    video.description || '',
                    now,
                    now
                );
            }

            console.log(`YTR Storage: Saved ${videos.length} search results for user ${userId}, URL: ${searchUrl}`);
        } catch (error) {
            console.error(`Error saving YTR search results:`, error);
            throw error;
        }
    }

    // YTR Search URLs Management
    async saveYtrSearchUrl(userId: number, name: string, url: string, resultsLimit: number = 25): Promise<{ id: number }> {
        try {
            const now = Date.now();

            // Get the next display order
            const maxOrderStmt = this.db.prepare(`
                SELECT COALESCE(MAX(display_order), -1) + 1 as next_order
                FROM ytr_search_urls
                WHERE user_id = ?
            `);
            const { next_order } = maxOrderStmt.get(userId) as { next_order: number };

            const insertStmt = this.db.prepare(`
                INSERT INTO ytr_search_urls (user_id, name, url, results_limit, display_order, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `);

            const result = insertStmt.run(userId, name, url, resultsLimit, next_order, now, now);
            console.log(`YTR Storage: Saved search URL "${name}" for user ${userId} with results limit ${resultsLimit}`);

            return { id: result.lastInsertRowid as number };
        } catch (error) {
            console.error(`YTR Storage: Error saving search URL for user ${userId}:`, error);
            throw error;
        }
    }

    async getYtrSearchUrls(userId: number): Promise<Array<{ id: number; name: string; url: string; resultsLimit: number; displayOrder: number }>> {
        try {
            const selectStmt = this.db.prepare(`
                SELECT id, name, url, COALESCE(results_limit, 25) as resultsLimit, display_order as displayOrder
                FROM ytr_search_urls
                WHERE user_id = ?
                ORDER BY display_order ASC
            `);

            const results = selectStmt.all(userId);
            console.log(`YTR Storage: Retrieved ${results.length} search URLs for user ${userId}`);

            return results as Array<{ id: number; name: string; url: string; resultsLimit: number; displayOrder: number }>;
        } catch (error) {
            console.error(`YTR Storage: Error retrieving search URLs for user ${userId}:`, error);
            return [];
        }
    }

    async updateYtrSearchUrl(userId: number, id: number, name: string, url: string, resultsLimit?: number): Promise<boolean> {
        try {
            const now = Date.now();
            let updateStmt;
            let result;

            if (resultsLimit !== undefined) {
                updateStmt = this.db.prepare(`
                    UPDATE ytr_search_urls
                    SET name = ?, url = ?, results_limit = ?, updated_at = ?
                    WHERE id = ? AND user_id = ?
                `);
                result = updateStmt.run(name, url, resultsLimit, now, id, userId);
            } else {
                updateStmt = this.db.prepare(`
                    UPDATE ytr_search_urls
                    SET name = ?, url = ?, updated_at = ?
                    WHERE id = ? AND user_id = ?
                `);
                result = updateStmt.run(name, url, now, id, userId);
            }

            const success = result.changes > 0;

            if (success) {
                console.log(`YTR Storage: Updated search URL ${id} for user ${userId}${resultsLimit ? ` with results limit ${resultsLimit}` : ''}`);
            } else {
                console.log(`YTR Storage: No search URL found with id ${id} for user ${userId}`);
            }

            return success;
        } catch (error) {
            console.error(`YTR Storage: Error updating search URL ${id} for user ${userId}:`, error);
            return false;
        }
    }

    async deleteYtrSearchUrl(userId: number, id: number): Promise<boolean> {
        try {
            const deleteStmt = this.db.prepare(`
                DELETE FROM ytr_search_urls
                WHERE id = ? AND user_id = ?
            `);

            const result = deleteStmt.run(id, userId);
            const success = result.changes > 0;

            if (success) {
                console.log(`YTR Storage: Deleted search URL ${id} for user ${userId}`);
            } else {
                console.log(`YTR Storage: No search URL found with id ${id} for user ${userId}`);
            }

            return success;
        } catch (error) {
            console.error(`YTR Storage: Error deleting search URL ${id} for user ${userId}:`, error);
            return false;
        }
    }

    async reorderYtrSearchUrls(userId: number, urlOrders: Array<{ id: number; displayOrder: number }>): Promise<boolean> {
        try {
            const updateStmt = this.db.prepare(`
                UPDATE ytr_search_urls
                SET display_order = ?, updated_at = ?
                WHERE id = ? AND user_id = ?
            `);

            const now = Date.now();
            const transaction = this.db.transaction(() => {
                for (const { id, displayOrder } of urlOrders) {
                    updateStmt.run(displayOrder, now, id, userId);
                }
            });

            transaction();
            console.log(`YTR Storage: Reordered ${urlOrders.length} search URLs for user ${userId}`);
            return true;
        } catch (error) {
            console.error(`YTR Storage: Error reordering search URLs for user ${userId}:`, error);
            return false;
        }
    }

    async getYtrSearchResults(userId: number, searchUrl: string, maxAgeMinutes: number = 5): Promise<any[] | null> {
        try {
            const searchUrlHash = this.hashString(searchUrl);
            const cutoffTime = Date.now() - (maxAgeMinutes * 60 * 1000);

            const selectStmt = this.db.prepare(`
                SELECT * FROM ytr_search_results
                WHERE user_id = ? AND search_url_hash = ? AND last_updated > ?
                ORDER BY view_count DESC
            `);

            const results = selectStmt.all(userId, searchUrlHash, cutoffTime);

            if (results.length === 0) {
                console.log(`YTR Storage: No recent results found for user ${userId}, URL: ${searchUrl}`);
                return null;
            }

            const videos = results.map((row: any) => ({
                id: row.video_id,
                title: row.title,
                thumbnail: row.thumbnail,
                channelId: row.channel_id,
                channelTitle: row.channel_title,
                viewCount: row.view_count,
                publishedAt: row.published_at,
                publishedTimeText: row.published_time_text,
                isLive: Boolean(row.is_live),
                duration: row.duration,
                description: row.description,
                lastUpdated: row.last_updated
            }));

            console.log(`YTR Storage: Retrieved ${videos.length} cached results for user ${userId}, URL: ${searchUrl}`);
            return videos;
        } catch (error) {
            console.error(`Error retrieving YTR search results:`, error);
            return null;
        }
    }

    async updateYtrVideoViewCount(userId: number, videoId: string, newViewCount: number): Promise<void> {
        try {
            const now = Date.now();
            const updateStmt = this.db.prepare(`
                UPDATE ytr_search_results
                SET view_count = ?, last_updated = ?
                WHERE user_id = ? AND video_id = ?
            `);

            const result = updateStmt.run(newViewCount, now, userId, videoId);

            if (result.changes > 0) {
                console.log(`YTR Storage: Updated view count for video ${videoId} to ${newViewCount} for user ${userId}`);
            }
        } catch (error) {
            console.error(`Error updating YTR video view count:`, error);
            throw error;
        }
    }

    async clearOldYtrSearchResults(maxAgeHours: number = 24): Promise<void> {
        try {
            const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);
            const deleteStmt = this.db.prepare(`
                DELETE FROM ytr_search_results WHERE last_updated < ?
            `);

            const result = deleteStmt.run(cutoffTime);
            console.log(`YTR Storage: Cleaned up ${result.changes} old search results older than ${maxAgeHours} hours`);
        } catch (error) {
            console.error(`Error clearing old YTR search results:`, error);
        }
    }

    // Helper method to create a hash of a string
    private hashString(str: string): string {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    // Add method to save video metrics data
    async saveVideoMetrics(userId: number, metrics: { timestamp: number, newVideosCount: number, trendingVideosCount: number }): Promise<void> {
        try {
            const insertStmt = this.db.prepare(`
                INSERT INTO video_metrics (
                    user_id, timestamp, new_videos_count, trending_videos_count
                ) VALUES (?, ?, ?, ?)
            `);

            insertStmt.run(
                userId,
                metrics.timestamp,
                metrics.newVideosCount,
                metrics.trendingVideosCount
            );

            console.log(`Saved video metrics for user ${userId} at timestamp ${new Date(metrics.timestamp).toISOString()}`);

            // Clean up old metrics data to prevent the database from growing too large
            await this.cleanupVideoMetrics(userId);
        } catch (error) {
            console.error(`Error saving video metrics for user ${userId}:`, error);
            throw error;
        }
    }

    // Add method to get video metrics data for a user
    async getVideoMetrics(userId: number, startTimestamp?: number, endTimestamp?: number): Promise<{ timestamp: number, newVideosCount: number, trendingVideosCount: number }[]> {
        try {
            let query = `
                SELECT timestamp, new_videos_count, trending_videos_count
                FROM video_metrics
                WHERE user_id = ?
            `;

            const params: any[] = [userId];

            if (startTimestamp !== undefined) {
                query += ' AND timestamp >= ?';
                params.push(startTimestamp);
            }

            if (endTimestamp !== undefined) {
                query += ' AND timestamp <= ?';
                params.push(endTimestamp);
            }

            query += ' ORDER BY timestamp ASC';

            const stmt = this.db.prepare(query);
            const rows = stmt.all(...params);

            return rows.map((row: any) => ({
                timestamp: row.timestamp,
                newVideosCount: row.new_videos_count,
                trendingVideosCount: row.trending_videos_count
            }));
        } catch (error) {
            console.error(`Error getting video metrics for user ${userId}:`, error);
            throw error;
        }
    }

    // Add method to clean up old video metrics data while ensuring we maintain a full week of data
    async cleanupVideoMetrics(userId: number): Promise<void> {
        try {
            // Keep only the last 7 days of data, but ensure we have at least some data points
            const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

            // First check if we have any data newer than 7 days
            const checkRecentStmt = this.db.prepare(`
                SELECT COUNT(*) as count FROM video_metrics
                WHERE user_id = ? AND timestamp >= ?
            `);

            const { count: recentCount } = checkRecentStmt.get(userId, sevenDaysAgo);

            // Only delete old data if we have some recent data
            if (recentCount > 0) {
                // Delete old data
                const deleteStmt = this.db.prepare(`
                    DELETE FROM video_metrics
                    WHERE user_id = ? AND timestamp < ?
                `);

                const result = deleteStmt.run(userId, sevenDaysAgo);

                if (result.changes > 0) {
                    console.log(`Cleaned up ${result.changes} old video metrics entries for user ${userId}`);
                }
            } else {
                console.log(`Skipping cleanup for user ${userId} - no recent data found, preserving historical data`);
            }

            // Check for duplicate timestamps and remove them, keeping the entry with the highest values
            try {
                // Find duplicate timestamps
                const findDuplicatesStmt = this.db.prepare(`
                    SELECT timestamp, COUNT(*) as count
                    FROM video_metrics
                    WHERE user_id = ?
                    GROUP BY timestamp
                    HAVING count > 1
                `);

                const duplicates = findDuplicatesStmt.all(userId);

                if (duplicates.length > 0) {
                    console.log(`Found ${duplicates.length} duplicate timestamps for user ${userId}`);

                    // For each duplicate timestamp, keep only the entry with the highest values
                    this.db.transaction(() => {
                        duplicates.forEach(dup => {
                            const timestamp = dup.timestamp;

                            // Find the entry with the highest sum of new_videos_count and trending_videos_count
                            const findBestEntryStmt = this.db.prepare(`
                                SELECT id, (new_videos_count + trending_videos_count) as total_count
                                FROM video_metrics
                                WHERE user_id = ? AND timestamp = ?
                                ORDER BY total_count DESC
                                LIMIT 1
                            `);

                            const bestEntry = findBestEntryStmt.get(userId, timestamp);

                            // Delete all other entries with this timestamp
                            const deleteDuplicatesStmt = this.db.prepare(`
                                DELETE FROM video_metrics
                                WHERE user_id = ? AND timestamp = ? AND id != ?
                            `);

                            const deleteResult = deleteDuplicatesStmt.run(userId, timestamp, bestEntry.id);

                            if (deleteResult.changes > 0) {
                                console.log(`Removed ${deleteResult.changes} duplicate entries for timestamp ${new Date(timestamp).toISOString()}`);
                            }
                        });
                    })();
                }
            } catch (error) {
                console.error(`Error cleaning up duplicate timestamps for user ${userId}:`, error);
                // Continue with the rest of the cleanup even if this part fails
            }

            // Limit the total number of entries per user to prevent excessive growth
            const countStmt = this.db.prepare(`
                SELECT COUNT(*) as count FROM video_metrics
                WHERE user_id = ?
            `);

            const { count } = countStmt.get(userId);

            // Set a higher limit to ensure we have enough data points for a week
            // 2 minutes interval * 60 minutes * 24 hours * 7 days = 5040 data points for a full week
            // We'll set a limit of 5500 to allow for some buffer
            const MAX_ENTRIES = 5500;

            if (count > MAX_ENTRIES) {
                // If we have more than MAX_ENTRIES, keep only the most recent MAX_ENTRIES
                const deleteExcessStmt = this.db.prepare(`
                    DELETE FROM video_metrics
                    WHERE user_id = ? AND id NOT IN (
                        SELECT id FROM video_metrics
                        WHERE user_id = ?
                        ORDER BY timestamp DESC
                        LIMIT ?
                    )
                `);

                const excessResult = deleteExcessStmt.run(userId, userId, MAX_ENTRIES);

                if (excessResult.changes > 0) {
                    console.log(`Removed ${excessResult.changes} excess video metrics entries for user ${userId}`);
                }
            }

            // Optimize the database periodically
            if (Math.random() < 0.1) { // 10% chance to run optimization
                console.log('Running database optimization...');
                this.db.exec('VACUUM;');
                console.log('Database optimization complete');
            }
        } catch (error) {
            console.error(`Error cleaning up video metrics for user ${userId}:`, error);
            throw error;
        }
    }

    // Add method to update only video metadata
    async updateYoutubeVideoMetadata(videoId: string, updates: { viewCount?: number, title?: string, description?: string, thumbnail?: string, isUnplayable?: boolean }, skipAutoAnalysis: boolean = false): Promise<void> {
        try {
            console.log(`Updating metadata for video ${videoId} (skipAutoAnalysis: ${skipAutoAnalysis})`);

            // If we're updating the view count, first get the current view count for validation
            if (updates.viewCount !== undefined) {
                try {
                    const currentVideo = await this.getYoutubeVideo(videoId);
                    if (currentVideo) {
                        const currentViewCount = currentVideo.viewCount;
                        const newViewCount = updates.viewCount;

                        console.log(`Current view count: ${currentViewCount}, New view count: ${newViewCount}`);

                        // If the new view count is significantly lower than the current one (more than 50% decrease),
                        // it's likely more accurate, so we should accept it
                        if (newViewCount < currentViewCount * 0.5) {
                            console.log(`New view count (${newViewCount}) is significantly lower than current (${currentViewCount}), accepting as likely more accurate`);
                        }
                        // If the new view count is suspiciously higher (more than 10x increase), log a warning but still update
                        else if (newViewCount > currentViewCount * 10 && currentViewCount > 0) {
                            console.log(`⚠️ WARNING: New view count (${newViewCount}) is suspiciously higher than current (${currentViewCount}), but updating anyway`);
                        }
                    }
                } catch (error) {
                    console.error(`Error getting current view count for video ${videoId}:`, error);
                    // Continue with the update even if we couldn't get the current view count
                }
            }

            // Build the update query dynamically based on provided fields
            const updateFields: string[] = [];
            const updateValues: any[] = [];

            if (updates.viewCount !== undefined) {
                updateFields.push("view_count = ?");
                updateValues.push(updates.viewCount);
                console.log(`Updating view count to ${updates.viewCount} for video ${videoId}`);
            }

            if (updates.title !== undefined) {
                updateFields.push("title = ?");
                updateValues.push(updates.title);
            }

            if (updates.description !== undefined) {
                updateFields.push("description = ?");
                updateValues.push(updates.description);
            }

            if (updates.thumbnail !== undefined) {
                updateFields.push("thumbnail = ?");
                updateValues.push(updates.thumbnail);
            }

            // Check if the is_unplayable column exists, and add it if it doesn't
            try {
                const tableInfo = this.db.prepare('PRAGMA table_info(youtube_videos)').all();
                const hasIsUnplayable = tableInfo.some((column: any) => column.name === 'is_unplayable');

                if (!hasIsUnplayable) {
                    console.log('Adding is_unplayable column to youtube_videos table');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN is_unplayable BOOLEAN DEFAULT 0');
                }
            } catch (error) {
                console.error('Error checking or adding is_unplayable column:', error);
            }

            if (updates.isUnplayable !== undefined) {
                updateFields.push("is_unplayable = ?");
                updateValues.push(updates.isUnplayable ? 1 : 0);
                console.log(`Marking video ${videoId} as ${updates.isUnplayable ? 'unplayable' : 'playable'}`);
            }

            if (updateFields.length === 0) {
                return; // Nothing to update
            }

            // Add videoId to values for WHERE clause
            updateValues.push(videoId);

            const stmt = this.db.prepare(`
                UPDATE youtube_videos
                SET ${updateFields.join(", ")}
                WHERE id = ?
            `);

            stmt.run(...updateValues);

            // Log the update for debugging
            console.log(`Successfully updated metadata for video ${videoId}`);
        } catch (error) {
            console.error(`Error updating YouTube video metadata for ${videoId}:`, error);
            throw error;
        }
    }

    // Add method to get a YouTube video by ID
    async getYoutubeVideo(videoId: string): Promise<YoutubeVideo | undefined> {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM youtube_videos
                WHERE id = ?
            `);

            const row = stmt.get(videoId);
            if (!row) return undefined;

            // Calculate VPH (Views Per Hour)
            const publishedAt = new Date(row.published_at);
            const now = new Date();
            const hoursSincePublished = Math.max(1, (now.getTime() - publishedAt.getTime()) / (1000 * 60 * 60));
            const vph = row.view_count / hoursSincePublished;

            // Check if financial analysis columns exist
            const hasFinancialAnalysis = 'financial_score' in row;

            // Check if OpenRouter analysis columns exist
            const hasOpenRouterAnalysis = 'openrouter_model_used' in row || 'openrouter_raw_data' in row;

            // Parse OpenRouter benefit amounts if they exist
            let openRouterBenefitAmounts = [];
            if (row.openrouter_benefit_amounts) {
                try {
                    if (typeof row.openrouter_benefit_amounts === 'string') {
                        openRouterBenefitAmounts = JSON.parse(row.openrouter_benefit_amounts);
                    } else if (Array.isArray(row.openrouter_benefit_amounts)) {
                        openRouterBenefitAmounts = row.openrouter_benefit_amounts;
                    }
                } catch (e) {
                    console.error(`Error parsing openrouter_benefit_amounts for video ${row.id}:`, e);
                }
            }

            return {
                id: row.id,
                title: row.title,
                thumbnail: row.thumbnail,
                channelId: row.channel_id,
                channelTitle: row.channel_title,
                viewCount: row.view_count,
                publishedAt: new Date(row.published_at),
                userId: row.user_id,
                description: row.description,
                duration: row.duration,
                transcription: row.transcription,
                hasTranscription: Boolean(row.has_transcription),
                isUnplayable: Boolean(row.is_unplayable),
                vph: vph,
                openRouterRawData: row.openrouter_raw_data || null,

                // Include financial analysis fields if they exist
                ...(hasFinancialAnalysis && {
                    financialScore: row.financial_score,
                    financialCategory: row.financial_category,
                    financialAmount: row.financial_amount,
                    financialTimeline: row.financial_timeline,
                    financialRecipients: row.financial_recipients,
                    financialSteps: row.financial_steps,
                    financialViralPotential: row.financial_viral_potential,
                    financialSkepticism: row.financial_skepticism,
                    financialAnalysis: row.financial_analysis,
                    financialTimestamps: row.financial_timestamps,
                    hasFinancialAnalysis: Boolean(row.has_financial_analysis)
                }),

                // Include OpenRouter fields if they exist
                ...(hasOpenRouterAnalysis && {
                    openRouterBenefitAmounts,
                    openRouterExpectedArrivalDate: row.openrouter_expected_arrival_date || '',
                    openRouterEligiblePeople: row.openrouter_eligible_people || '',
                    openRouterProofOrSource: row.openrouter_proof_or_source || '',
                    openRouterActionsToClaim: row.openrouter_actions_to_claim || '',
                    openRouterPriorityTag: row.openrouter_priority_tag || '',
                    openRouterReasonForPriority: row.openrouter_reason_for_priority || '',
                    openRouterViralPotential: row.openrouter_viral_potential || '',
                    openRouterModelUsed: row.openrouter_model_used || '',
                    openRouterPrompt: row.openrouter_prompt || '',
                    openRouterSystemPrompt: row.openrouter_system_prompt || '',
                    openRouterPromptName: row.openrouter_prompt_name || ''
                })
            };
        } catch (error) {
            console.error(`Error getting YouTube video ${videoId}:`, error);
            throw error;
        }
    }


    public db: Database.Database; // Changed from private to public for testing
    readonly sessionStore: session.Store;

    constructor() {
        // Use an absolute path for the database file to ensure it's created in the correct location
        const dbPath = path.resolve(__dirname, '../data.db');
        console.log('Using database path:', dbPath);
        this.db = new Database(dbPath);
        this.initializeTables();

        const MemoryStore = createMemoryStore(session);
        this.sessionStore = new MemoryStore({
            checkPeriod: 86400000,
        });

        // Implement Ollama prompt methods
        implementOllamaPromptMethods(this);

        // Implement OpenRouter API key methods
        implementOpenRouterApiKeyMethods(this);

        // Implement OpenRouter prompt methods
        implementOpenRouterPromptMethods(this);

        // Implement RSS feed methods
        implementRssFeedMethods(this);
    }

    private initializeTables() {
        // YouTube channels table
        try {
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS youtube_channels (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    channel_id TEXT NOT NULL,
                    channel_title TEXT NOT NULL,
                    channel_url TEXT NOT NULL,
                    thumbnail TEXT,
                    description TEXT,
                    subscriber_count INTEGER,
                    last_refresh_time TEXT,
                    display_order INTEGER DEFAULT 0,
                    video_limit INTEGER DEFAULT 15,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            // YouTube videos table
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS youtube_videos (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    thumbnail TEXT NOT NULL,
                    channel_id TEXT NOT NULL,
                    channel_title TEXT NOT NULL,
                    view_count INTEGER,
                    published_at TEXT NOT NULL,
                    user_id INTEGER NOT NULL,
                    description TEXT,
                    duration TEXT,
                    transcription TEXT,
                    has_transcription BOOLEAN DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            // TS (Transcript) channels table
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS ts_channels (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    channel_id TEXT NOT NULL,
                    channel_title TEXT NOT NULL,
                    channel_url TEXT NOT NULL,
                    thumbnail TEXT,
                    description TEXT,
                    subscriber_count INTEGER,
                    video_limit INTEGER DEFAULT 50,
                    last_refresh_time TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            // TS videos table
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS ts_videos (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    thumbnail TEXT NOT NULL,
                    channel_id TEXT NOT NULL,
                    channel_title TEXT NOT NULL,
                    view_count INTEGER,
                    published_at TEXT NOT NULL,
                    user_id INTEGER NOT NULL,
                    ts_channel_id INTEGER NOT NULL,
                    description TEXT,
                    duration TEXT,
                    transcript_path TEXT,
                    has_transcript BOOLEAN DEFAULT 0,
                    transcript_downloaded_at TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (ts_channel_id) REFERENCES ts_channels(id) ON DELETE CASCADE
                );
            `);

            // Video metrics table for Data Analysis page
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS video_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    timestamp INTEGER NOT NULL,
                    new_videos_count INTEGER NOT NULL,
                    trending_videos_count INTEGER NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );

                -- Create an index on user_id and timestamp for faster queries
                CREATE INDEX IF NOT EXISTS idx_video_metrics_user_timestamp
                ON video_metrics(user_id, timestamp);
            `);

            // YTR search URLs table for storing user's saved search URLs
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS ytr_search_urls (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    url TEXT NOT NULL,
                    results_limit INTEGER DEFAULT 25,
                    display_order INTEGER DEFAULT 0,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );

                -- Create indexes for faster queries
                CREATE INDEX IF NOT EXISTS idx_ytr_urls_user_id
                ON ytr_search_urls(user_id);

                CREATE INDEX IF NOT EXISTS idx_ytr_urls_display_order
                ON ytr_search_urls(user_id, display_order);
            `);

            // Add results_limit column to existing ytr_search_urls table if it doesn't exist
            try {
                const tableInfo = this.db.prepare('PRAGMA table_info(ytr_search_urls)').all();
                const hasResultsLimit = tableInfo.some((column: any) => column.name === 'results_limit');

                if (!hasResultsLimit) {
                    this.db.exec('ALTER TABLE ytr_search_urls ADD COLUMN results_limit INTEGER DEFAULT 25');
                    console.log('Added results_limit column to ytr_search_urls table');
                }
            } catch (error) {
                console.error('Error adding results_limit column:', error);
            }

            // YTR search results table for real-time data management
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS ytr_search_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    search_url TEXT NOT NULL,
                    search_url_hash TEXT NOT NULL,
                    video_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    thumbnail TEXT NOT NULL,
                    channel_id TEXT NOT NULL,
                    channel_title TEXT NOT NULL,
                    view_count INTEGER NOT NULL,
                    published_at TEXT NOT NULL,
                    published_time_text TEXT,
                    is_live BOOLEAN DEFAULT 0,
                    duration TEXT,
                    description TEXT,
                    last_updated INTEGER NOT NULL,
                    created_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );

                -- Create indexes for faster queries
                CREATE INDEX IF NOT EXISTS idx_ytr_search_user_url_hash
                ON ytr_search_results(user_id, search_url_hash);

                CREATE INDEX IF NOT EXISTS idx_ytr_search_video_id
                ON ytr_search_results(video_id);

                CREATE INDEX IF NOT EXISTS idx_ytr_search_last_updated
                ON ytr_search_results(last_updated);
            `);
        } catch (error) {
            console.error('Error creating YouTube tables:', error);
        }
        // Create tables one by one to ensure proper error handling
        try {
            // Admin settings table
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS admin_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    server_port INTEGER DEFAULT 5001,
                    localhost_only BOOLEAN DEFAULT 0,
                    last_updated TEXT DEFAULT CURRENT_TIMESTAMP
                );
            `);

            // Users table
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE,
                    password TEXT
                );
            `);

            // Keyword groups table
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS keyword_groups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    keywords TEXT NOT NULL,
                    exclude_words TEXT DEFAULT '[]',
                    last_refresh_time TEXT DEFAULT NULL,
                    display_order INTEGER DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            // Cast queue table
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS cast_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    video_id TEXT NOT NULL,
                    video_data TEXT NOT NULL,
                    position INTEGER NOT NULL,
                    added_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            // TXT transcripts table
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS txt_transcripts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    video_url TEXT,
                    source_type TEXT DEFAULT 'manual',
                    source_id TEXT,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            // TXT highlights table
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS txt_highlights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    transcript_id INTEGER NOT NULL,
                    start_pos INTEGER NOT NULL,
                    end_pos INTEGER NOT NULL,
                    color TEXT NOT NULL DEFAULT '#ffff00',
                    note TEXT,
                    created_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (transcript_id) REFERENCES txt_transcripts(id) ON DELETE CASCADE
                );
            `);

            // TXT prompts table
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS txt_prompts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    content TEXT NOT NULL,
                    category TEXT DEFAULT 'general',
                    is_default BOOLEAN DEFAULT 0,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            // TXT PRO tables
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS txt_pro_scripts (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    source_video_url TEXT,
                    transcript TEXT NOT NULL,
                    tags TEXT NOT NULL DEFAULT '[]',
                    date_added INTEGER NOT NULL,
                    edit_history TEXT DEFAULT '[]',
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            this.db.exec(`
                CREATE TABLE IF NOT EXISTS txt_pro_chunks (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    content TEXT NOT NULL,
                    purpose TEXT NOT NULL,
                    tags TEXT NOT NULL DEFAULT '[]',
                    source_script_id TEXT,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (source_script_id) REFERENCES txt_pro_scripts(id)
                );
            `);

            this.db.exec(`
                CREATE TABLE IF NOT EXISTS txt_pro_highlights (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    script_id TEXT NOT NULL,
                    start_index INTEGER NOT NULL,
                    end_index INTEGER NOT NULL,
                    color TEXT NOT NULL,
                    note TEXT,
                    analytics_note TEXT,
                    created_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (script_id) REFERENCES txt_pro_scripts(id)
                );
            `);

            this.db.exec(`
                CREATE TABLE IF NOT EXISTS txt_pro_strategies (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    sample_phrases TEXT NOT NULL DEFAULT '[]',
                    tone TEXT,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            this.db.exec(`
                CREATE TABLE IF NOT EXISTS txt_pro_content_structures (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL,
                    category TEXT,
                    intro_format TEXT,
                    main_info_drop TEXT,
                    explanation_breakdown TEXT,
                    engagement_insert TEXT,
                    outro_cta TEXT,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            this.db.exec(`
                CREATE TABLE IF NOT EXISTS txt_pro_core_infos (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    event_date TEXT,
                    eligibility_criteria TEXT,
                    act_bill_proposal_name TEXT,
                    pay_dates TEXT,
                    program_type TEXT,
                    source_link TEXT,
                    notes TEXT,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            this.db.exec(`
                CREATE TABLE IF NOT EXISTS txt_pro_narration_styles (
                    id TEXT PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    landmark_name TEXT NOT NULL,
                    description TEXT,
                    speaker_tone TEXT,
                    common_phrases TEXT NOT NULL DEFAULT '[]',
                    timing_rhythm TEXT,
                    created_at INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `);

            // Insert default prompts if they don't exist
            this.insertDefaultTxtPrompts();

            // Add missing default prompts to existing users
            this.addMissingDefaultPrompts();

            console.log('All tables initialized successfully');
        } catch (error) {
            console.error('Error initializing tables:', error);
        }

        // Add display_order column if it doesn't exist
        try {
            const tableInfo = this.db.prepare('PRAGMA table_info(keyword_groups)').all();
            const hasDisplayOrder = tableInfo.some((column: any) => column.name === 'display_order');

            if (!hasDisplayOrder) {
                this.db.exec('ALTER TABLE keyword_groups ADD COLUMN display_order INTEGER DEFAULT 0');
                console.log('Added display_order column to keyword_groups table');
            }
        } catch (error) {
            console.error('Error checking or adding display_order column:', error);
        }

        // Add metadata columns to videos table if they don't exist
        try {
            const videosTableInfo = this.db.prepare('PRAGMA table_info(videos)').all();
            const hasDescription = videosTableInfo.some((column: any) => column.name === 'description');

            if (!hasDescription) {
                // Add all the metadata columns
                this.db.exec(`
                    ALTER TABLE videos ADD COLUMN description TEXT;
                    ALTER TABLE videos ADD COLUMN channel_id TEXT;
                    ALTER TABLE videos ADD COLUMN vph REAL;
                    ALTER TABLE videos ADD COLUMN tags TEXT;
                    ALTER TABLE videos ADD COLUMN matched_tags TEXT;
                    ALTER TABLE videos ADD COLUMN unmatched_tags TEXT;
                    ALTER TABLE videos ADD COLUMN seen_from TEXT;
                    ALTER TABLE videos ADD COLUMN related_to TEXT;
                    ALTER TABLE videos ADD COLUMN content_details TEXT;
                    ALTER TABLE videos ADD COLUMN statistics TEXT;
                `);
                console.log('Added metadata columns to videos table');
            }
        } catch (error) {
            console.error('Error checking or adding metadata columns to videos table:', error);
        }

        // Add default settings columns to settings table if they don't exist
        try {
            const settingsTableInfo = this.db.prepare('PRAGMA table_info(settings)').all();
            const hasDefaultSortMethod = settingsTableInfo.some((column: any) => column.name === 'default_sort_method');

            if (!hasDefaultSortMethod) {
                // Add all the default settings columns
                this.db.exec(`
                    ALTER TABLE settings ADD COLUMN default_sort_method TEXT DEFAULT 'popularity';
                    ALTER TABLE settings ADD COLUMN default_duration_filter TEXT DEFAULT 'all';
                    ALTER TABLE settings ADD COLUMN default_unwatched_filter INTEGER DEFAULT 0;
                `);
                console.log('Added default settings columns to settings table');
            }
        } catch (error) {
            console.error('Error checking or adding default settings columns to settings table:', error);
        }

        this.db.exec(`
            CREATE TABLE IF NOT EXISTS settings (
                user_id INTEGER PRIMARY KEY,
                dark_mode BOOLEAN,
                search_keywords TEXT,
                min_views_per_hour INTEGER DEFAULT 10,
                remove_duplicates BOOLEAN DEFAULT 1,
                exclude_words TEXT DEFAULT '[]',
                auto_refresh_interval INTEGER DEFAULT 0,
                preferred_playback TEXT DEFAULT 'in_app',
                last_refresh_time TEXT DEFAULT NULL,
                parallel_api_calls BOOLEAN DEFAULT 1,
                watched_videos TEXT DEFAULT '[]',
                use_in_app_player BOOLEAN DEFAULT 1,
                active_keyword_group_id INTEGER DEFAULT NULL,
                default_sort_method TEXT DEFAULT 'popularity',
                default_duration_filter TEXT DEFAULT 'all',
                default_unwatched_filter BOOLEAN DEFAULT 0,
                home_page TEXT DEFAULT 'trendy',
                ollama_model TEXT DEFAULT 'llama3',
                ollama_analysis_prompt TEXT,
                selected_prompt_id INTEGER,
                auto_analyze_on_refresh BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (active_keyword_group_id) REFERENCES keyword_groups(id)
            );
            CREATE TABLE IF NOT EXISTS videos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                video_id TEXT,
                title TEXT,
                thumbnail TEXT,
                channel_title TEXT,
                view_count INTEGER,
                published_at TEXT,
                user_id INTEGER,
                keyword_group_id INTEGER,
                description TEXT,
                channel_id TEXT,
                vph REAL,
                tags TEXT,
                matched_tags TEXT,
                unmatched_tags TEXT,
                seen_from TEXT,
                related_to TEXT,
                content_details TEXT,
                statistics TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (keyword_group_id) REFERENCES keyword_groups(id)
            );
            CREATE TABLE IF NOT EXISTS vidiq_api_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                token TEXT NOT NULL,
                auth_token TEXT,
                cookie TEXT,
                name TEXT,
                is_active BOOLEAN DEFAULT 1,
                last_used DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                exhausted_until DATETIME,
                user_id INTEGER,
                FOREIGN KEY (user_id) REFERENCES users(id)
            );

            CREATE TABLE IF NOT EXISTS playlists (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            );

            CREATE TABLE IF NOT EXISTS playlist_videos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                playlist_id INTEGER NOT NULL,
                video_id TEXT NOT NULL,
                title TEXT NOT NULL,
                thumbnail TEXT NOT NULL,
                channel_title TEXT NOT NULL,
                view_count INTEGER,
                published_at TEXT,
                added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE
            );
        `);

        // Add active_keyword_group_id column to settings table if it doesn't exist
        try {
            const columnsStmt = this.db.prepare("PRAGMA table_info(settings)");
            const columns = columnsStmt.all();
            const hasActiveKeywordGroupId = columns.some(col => col.name === 'active_keyword_group_id');

            if (!hasActiveKeywordGroupId) {
                console.log('Adding active_keyword_group_id column to settings table');
                this.db.exec(`ALTER TABLE settings ADD COLUMN active_keyword_group_id INTEGER DEFAULT NULL REFERENCES keyword_groups(id)`);
            }

            // Add keyword_group_id column to videos table if it doesn't exist
            const videoColumnsStmt = this.db.prepare("PRAGMA table_info(videos)");
            const videoColumns = videoColumnsStmt.all();
            const hasKeywordGroupId = videoColumns.some(col => col.name === 'keyword_group_id');

            if (!hasKeywordGroupId) {
                console.log('Adding keyword_group_id column to videos table');
                this.db.exec(`ALTER TABLE videos ADD COLUMN keyword_group_id INTEGER DEFAULT NULL REFERENCES keyword_groups(id)`);
            }

            // Add exclude_words column to keyword_groups table if it doesn't exist
            const keywordGroupsColumnsStmt = this.db.prepare("PRAGMA table_info(keyword_groups)");
            const keywordGroupsColumns = keywordGroupsColumnsStmt.all();
            const hasExcludeWords = keywordGroupsColumns.some(col => col.name === 'exclude_words');

            if (!hasExcludeWords) {
                console.log('Adding exclude_words column to keyword_groups table');
                this.db.exec(`ALTER TABLE keyword_groups ADD COLUMN exclude_words TEXT DEFAULT '[]'`);
            }
        } catch (error) {
            console.error('Error checking or adding columns:', error);
        }
    }

    async createUser(insertUser: InsertUser): Promise<User> {
        const stmt = this.db.prepare('INSERT INTO users (username, password) VALUES (?, ?)');
        const result = stmt.run(String(insertUser.username), String(insertUser.password));
        const userId = Number(result.lastInsertRowid);

        // Initialize default settings for new user
        const settingsStmt = this.db.prepare(`
            INSERT INTO settings (
                user_id, dark_mode, search_keywords, min_views_per_hour, remove_duplicates,
                exclude_words, auto_refresh_interval, preferred_playback,
                parallel_api_calls, watched_videos, use_in_app_player
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        settingsStmt.run(
            userId,
            1,  // darkMode: true
            JSON.stringify(["vlog", "funny", "fail"]),
            10, // minViewsPerHour
            1,  // removeDuplicates: true
            JSON.stringify([]), // excludeWords
            0,  // autoRefreshInterval
            'in_app', // preferredPlayback
            1,  // parallelApiCalls: true
            JSON.stringify([]), // watchedVideos
            1   // useInAppPlayer: true
        );

        // Create default "Favorites" playlist for the user
        try {
            const playlistStmt = this.db.prepare(`
                INSERT INTO playlists (user_id, name, description)
                VALUES (?, ?, ?)
            `);
            playlistStmt.run(
                userId,
                'Favorites',
                'Your favorite videos'
            );
            console.log(`Created default Favorites playlist for user ${userId}`);
        } catch (error) {
            console.error(`Failed to create default Favorites playlist for user ${userId}:`, error);
            // Continue even if playlist creation fails
        }

        return {
            id: userId,
            username: String(insertUser.username),
            password: String(insertUser.password),
            darkMode: true,
            searchKeywords: ["vlog", "funny", "fail"],
            minViewsPerHour: 10,
            removeDuplicates: true,
            excludeWords: [],
            autoRefreshInterval: 0,
            preferredPlayback: "in_app",
            lastRefreshTime: null,
            parallelApiCalls: true,
            watchedVideos: [],
            useInAppPlayer: true
        };
    }

    async getUser(id: number): Promise<User | undefined> {
        const userStmt = this.db.prepare('SELECT * FROM users WHERE id = ?');
        const user = userStmt.get(id);

        if (!user) return undefined;

        const settingsStmt = this.db.prepare('SELECT * FROM settings WHERE user_id = ?');
        const settings = settingsStmt.get(id);

        if (!settings) {
            return {
                id: user.id,
                username: user.username,
                password: user.password,
                role: user.role || 'user',
                isAdmin: user.is_admin === 1 || user.role === 'admin',
                darkMode: true,
                searchKeywords: ["vlog", "funny", "fail"],
                minViewsPerHour: 10,
                removeDuplicates: true,
                excludeWords: [],
                autoRefreshInterval: 0,
                preferredPlayback: "in_app",
                lastRefreshTime: null,
                parallelApiCalls: true,
                watchedVideos: [],
                useInAppPlayer: true
            };
        }

        return {
            id: user.id,
            username: user.username,
            password: user.password,
            role: user.role || 'user',
            isAdmin: user.is_admin === 1 || user.role === 'admin',
            darkMode: Boolean(settings.dark_mode),
            searchKeywords: JSON.parse(settings.search_keywords),
            minViewsPerHour: settings.min_views_per_hour,
            removeDuplicates: Boolean(settings.remove_duplicates),
            excludeWords: JSON.parse(settings.exclude_words),
            autoRefreshInterval: settings.auto_refresh_interval,
            preferredPlayback: settings.preferred_playback,
            lastRefreshTime: settings.last_refresh_time ? new Date(settings.last_refresh_time) : null,
            parallelApiCalls: Boolean(settings.parallel_api_calls),
            watchedVideos: JSON.parse(settings.watched_videos),
            useInAppPlayer: Boolean(settings.use_in_app_player)
        };
    }

    async getUserByUsername(username: string): Promise<User | undefined> {
        const stmt = this.db.prepare('SELECT * FROM users WHERE username = ?');
        const user = stmt.get(String(username));

        if (!user) return undefined;

        return this.getUser(user.id);
    }

    // Add a settings cache property
    private settingsCache: Map<number, { settings: UserSettings, timestamp: number }> = new Map();

    // Alias for getSettings to maintain compatibility with task-queue.ts
    async getUserSettings(userId: number): Promise<UserSettings | undefined> {
        return this.getSettings(userId);
    }

    async getSettings(userId: number): Promise<UserSettings | undefined> {
        try {
            // Check if we have a recent cache entry (less than 5 seconds old)
            const cacheEntry = this.settingsCache.get(userId);
            const now = Date.now();
            if (cacheEntry && (now - cacheEntry.timestamp) < 5000) {
                console.log(`Using cached settings for user ${userId}`);
                return cacheEntry.settings;
            }

            const user = await this.getUser(userId);
            if (!user) return undefined;

            // Get all settings directly from the settings table
            // First, check if the auto_analyze_on_refresh column exists
            const columnInfo = this.db.prepare(`PRAGMA table_info(settings)`).all();
            const autoAnalyzeColumnExists = columnInfo.some(col => col.name === 'auto_analyze_on_refresh');
            // verboseAiLogging column check removed - always enabled for errors

            // Build the SQL query based on whether the column exists
            let sql = `
                SELECT
                    dark_mode,
                    search_keywords,
                    min_views_per_hour,
                    remove_duplicates,
                    exclude_words,
                    auto_refresh_interval,
                    preferred_playback,
                    last_refresh_time,
                    parallel_api_calls,
                    watched_videos,
                    use_in_app_player,
                    preview_sound,
                    playback_sound,
                    active_keyword_group_id,
                    default_sort_method,
                    default_duration_filter,
                    default_unwatched_filter,
                    home_page,
                    selected_prompt_id,
                    openrouter_model,
                    openrouter_analysis_prompt`;

            // Add the auto_analyze_on_refresh column if it exists
            if (autoAnalyzeColumnExists) {
                sql += `,
                    auto_analyze_on_refresh`;
            }

            sql += `
                FROM settings
                WHERE user_id = ?
            `;

            const stmt = this.db.prepare(sql);

            const dbSettings = stmt.get(userId);

            if (!dbSettings) {
                // If no settings found, create default settings for this user
                console.log('No settings found for user, creating default settings');
                const defaultSettings = {
                    role: user.role,
                    isAdmin: user.isAdmin,
                    darkMode: true,
                    searchKeywords: ["vlog", "funny", "fail"],
                    minViewsPerHour: 10,
                    removeDuplicates: true,
                    excludeWords: [],
                    autoRefreshInterval: 0,
                    preferredPlayback: "in_app",
                    lastRefreshTime: null,
                    parallelApiCalls: true,
                    watchedVideos: [],
                    useInAppPlayer: true,
                    previewSound: false,
                    playbackSound: true,
                    activeKeywordGroupId: null,
                    defaultSortMethod: 'quality',
                    defaultDurationFilter: 'all',
                    defaultUnwatchedFilter: false,
                    homePage: 'trendy',
                    ollamaModel: 'llama3',
                    ollamaAnalysisPrompt: null,
                    selectedPromptId: null,
                    autoAnalyzeOnRefresh: true,
                    openRouterModel: 'google/gemini-2.0-flash-exp:free',
                    openRouterAnalysisPrompt: null
                };

                // Insert default settings into the database
                await this.updateSettings(userId, defaultSettings);

                // Return the default settings
                return defaultSettings;
            }

            // Parse JSON strings from DB
            let searchKeywords = [];
            let excludeWords = [];
            let watchedVideos = [];

            try {
                searchKeywords = dbSettings.search_keywords ? JSON.parse(dbSettings.search_keywords) : [];
            } catch (e) {
                console.error('Error parsing search_keywords:', e);
            }

            try {
                excludeWords = dbSettings.exclude_words ? JSON.parse(dbSettings.exclude_words) : [];
            } catch (e) {
                console.error('Error parsing exclude_words:', e);
            }

            try {
                watchedVideos = dbSettings.watched_videos ? JSON.parse(dbSettings.watched_videos) : [];
            } catch (e) {
                console.error('Error parsing watched_videos:', e);
            }

            // Convert DB values to proper types
            const settings: UserSettings = {
                role: user.role,
                isAdmin: user.isAdmin,
                darkMode: dbSettings.dark_mode === 1,
                searchKeywords: searchKeywords,
                minViewsPerHour: dbSettings.min_views_per_hour || 10,
                removeDuplicates: dbSettings.remove_duplicates === 1,
                excludeWords: excludeWords,
                autoRefreshInterval: dbSettings.auto_refresh_interval || 0,
                preferredPlayback: dbSettings.preferred_playback || 'in_app',
                lastRefreshTime: dbSettings.last_refresh_time ? new Date(dbSettings.last_refresh_time) : null,
                parallelApiCalls: dbSettings.parallel_api_calls === 1,
                watchedVideos: watchedVideos,
                useInAppPlayer: dbSettings.use_in_app_player === 1,
                previewSound: dbSettings.preview_sound === 1,
                playbackSound: dbSettings.playback_sound === 1,
                activeKeywordGroupId: dbSettings.active_keyword_group_id || null,
                defaultSortMethod: dbSettings.default_sort_method || 'popularity',
                defaultDurationFilter: dbSettings.default_duration_filter || 'all',
                defaultUnwatchedFilter: dbSettings.default_unwatched_filter === 1,
                homePage: dbSettings.home_page || 'trendy',
                ollamaModel: 'llama3', // Default value since column was removed
                ollamaAnalysisPrompt: null, // Default value since column was removed
                selectedPromptId: dbSettings.selected_prompt_id || null,
                // Default to true if the column doesn't exist yet
                // Force to a boolean value by comparing with 1
                autoAnalyzeOnRefresh: dbSettings.auto_analyze_on_refresh === 1,
                openRouterModel: dbSettings.openrouter_model || 'google/gemini-2.0-flash-exp:free',
                openRouterAnalysisPrompt: dbSettings.openrouter_analysis_prompt || null
            };

            // Log the OpenRouter model value being returned
            console.log('Retrieved settings with OpenRouter model:', settings.openRouterModel || 'google/gemini-2.0-flash-exp:free');

            // Log the autoAnalyzeOnRefresh value for debugging
            console.log('Retrieved autoAnalyzeOnRefresh setting:', settings.autoAnalyzeOnRefresh,
                        '(type:', typeof settings.autoAnalyzeOnRefresh,
                        ', raw DB value:', dbSettings.auto_analyze_on_refresh, ')');

            // Ensure all boolean values are properly converted
            settings.autoAnalyzeOnRefresh = settings.autoAnalyzeOnRefresh === true;
            settings.darkMode = settings.darkMode === true;
            settings.removeDuplicates = settings.removeDuplicates === true;
            settings.useInAppPlayer = settings.useInAppPlayer === true;
            settings.previewSound = settings.previewSound === true;
            settings.playbackSound = settings.playbackSound === true;
            settings.defaultUnwatchedFilter = settings.defaultUnwatchedFilter === true;
            settings.parallelApiCalls = settings.parallelApiCalls === true;

            // Additional logging to confirm the final values
            console.log('Final settings values after conversion:');
            console.log('- autoAnalyzeOnRefresh:', settings.autoAnalyzeOnRefresh, '(type:', typeof settings.autoAnalyzeOnRefresh, ')');
            console.log('- darkMode:', settings.darkMode, '(type:', typeof settings.darkMode, ')');
            console.log('- removeDuplicates:', settings.removeDuplicates, '(type:', typeof settings.removeDuplicates, ')');
            console.log('- useInAppPlayer:', settings.useInAppPlayer, '(type:', typeof settings.useInAppPlayer, ')');
            console.log('- openRouterModel:', settings.openRouterModel);

            // Store settings in cache
            this.settingsCache.set(userId, {
                settings,
                timestamp: Date.now()
            });

            return settings;
        } catch (error) {
            console.error('Error getting settings:', error);
            throw error;
        }
    }

    // Method to handle global feed settings
    private async handleGlobalFeedSettings(userId: number, keywords: string[], excludeWords: string[]): Promise<UserSettings> {
        const user = await this.getUser(userId);
        if (!user) throw new Error("User not found");

        try {
            // First check if the user has a settings row
            const checkStmt = this.db.prepare('SELECT 1 FROM settings WHERE user_id = ?');
            const hasSettings = checkStmt.get(Number(userId));

            if (!hasSettings) {
                // Create default settings with the provided keywords and exclude words
                const defaultSettings: UserSettings = {
                    role: user.role,
                    isAdmin: user.isAdmin,
                    darkMode: true,
                    searchKeywords: keywords,
                    minViewsPerHour: 10,
                    removeDuplicates: true,
                    excludeWords: excludeWords,
                    autoRefreshInterval: 0,
                    preferredPlayback: "in_app",
                    lastRefreshTime: null,
                    parallelApiCalls: false,
                    watchedVideos: [],
                    useInAppPlayer: true,
                    previewSound: false,
                    playbackSound: true,
                    activeKeywordGroupId: null
                };
                // Create settings for the user
                console.log('Creating settings for user:', userId);
                console.log('Settings to create:', defaultSettings);

                try {
                    // Check if the auto_analyze_on_refresh column exists
                    const columnInfo = this.db.prepare(`PRAGMA table_info(settings)`).all();
                    const autoAnalyzeColumnExists = columnInfo.some(col => col.name === 'auto_analyze_on_refresh');

                    // Convert boolean values to 0/1 for SQLite
                    const darkModeValue = defaultSettings.darkMode ? 1 : 0;
                    const removeDuplicatesValue = defaultSettings.removeDuplicates ? 1 : 0;
                    const parallelApiCallsValue = defaultSettings.parallelApiCalls ? 1 : 0;
                    const useInAppPlayerValue = defaultSettings.useInAppPlayer === false ? 0 : 1;
                    const previewSoundValue = defaultSettings.previewSound ? 1 : 0;
                    const playbackSoundValue = defaultSettings.playbackSound === false ? 0 : 1;
                    const defaultUnwatchedFilterValue = defaultSettings.defaultUnwatchedFilter ? 1 : 0;

                    // Build the SQL query based on whether the column exists
                    let insertSql = `
                        INSERT INTO settings (
                            user_id,
                            dark_mode,
                            search_keywords,
                            min_views_per_hour,
                            remove_duplicates,
                            exclude_words,
                            auto_refresh_interval,
                            preferred_playback,
                            last_refresh_time,
                            parallel_api_calls,
                            watched_videos,
                            use_in_app_player,
                            preview_sound,
                            playback_sound,
                            active_keyword_group_id,
                            default_sort_method,
                            default_duration_filter,
                            default_unwatched_filter,
                            home_page,
                            selected_prompt_id,
                            openrouter_model,
                            openrouter_analysis_prompt`;

                    // Add the auto_analyze_on_refresh column if it exists
                    if (autoAnalyzeColumnExists) {
                        insertSql += `,
                            auto_analyze_on_refresh`;
                    }

                    insertSql += `
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?`;

                    // Add an extra placeholder for auto_analyze_on_refresh if the column exists
                    if (autoAnalyzeColumnExists) {
                        insertSql += `, ?`;
                    }

                    insertSql += `)`;

                    const insertStmt = this.db.prepare(insertSql);

                    // Create the base parameters array
                    const baseParams = [
                        Number(userId),
                        darkModeValue,
                        JSON.stringify(defaultSettings.searchKeywords || []),
                        defaultSettings.minViewsPerHour || 10,
                        removeDuplicatesValue,
                        JSON.stringify(defaultSettings.excludeWords || []),
                        defaultSettings.autoRefreshInterval || 0,
                        defaultSettings.preferredPlayback || 'in_app',
                        defaultSettings.lastRefreshTime ? defaultSettings.lastRefreshTime.toISOString() : null,
                        parallelApiCallsValue,
                        JSON.stringify(defaultSettings.watchedVideos || []),
                        useInAppPlayerValue,
                        previewSoundValue,
                        playbackSoundValue,
                        defaultSettings.activeKeywordGroupId,
                        defaultSettings.defaultSortMethod || 'popularity',
                        defaultSettings.defaultDurationFilter || 'all',
                        defaultUnwatchedFilterValue,
                        defaultSettings.homePage || 'trendy',
                        defaultSettings.selectedPromptId || null,
                        defaultSettings.openRouterModel || 'google/gemini-2.0-flash-exp:free',
                        defaultSettings.openRouterAnalysisPrompt || null
                    ];

                    // Create a copy of the base parameters for the final params
                    let insertParams = [...baseParams];

                    // Add the auto_analyze_on_refresh parameter if the column exists
                    if (autoAnalyzeColumnExists) {
                        // Explicitly convert to boolean then to 0/1
                        // Force a strict comparison with true to ensure proper conversion
                        const autoAnalyzeValue = defaultSettings.autoAnalyzeOnRefresh === true ? 1 : 0;
                        console.log('Setting auto_analyze_on_refresh to:', autoAnalyzeValue, '(from value:', defaultSettings.autoAnalyzeOnRefresh, ', type:', typeof defaultSettings.autoAnalyzeOnRefresh, ')');
                        insertParams.push(autoAnalyzeValue);
                    }

                    console.log('SQL insert parameters:', insertParams);

                    // Execute the insert statement with the parameters
                    insertStmt.run(...insertParams);

                    // Get the updated settings from the database to ensure we return the actual saved values
                    return await this.getSettings(userId);
                } catch (error) {
                    console.error('Error creating settings:', error);
                    throw error;
                }
            } else {
                // If the user already has settings, update the keywords and exclude words
                return await this.updateFeedKeywords(userId, keywords, excludeWords);
            }
        } catch (error) {
            console.error('Error handling global feed settings:', error);
            throw error;
        }
    }

    // Method to update global feed settings with keywords and exclude words
    async updateFeedKeywords(userId: number, keywords: string[], excludeWords: string[]): Promise<UserSettings> {
        try {
            // Update only the keywords and exclude words
            const updateStmt = this.db.prepare(`
                UPDATE settings SET
                    search_keywords = ?,
                    exclude_words = ?
                WHERE user_id = ?
            `);

            updateStmt.run(
                JSON.stringify(keywords),
                JSON.stringify(excludeWords),
                Number(userId)
            );

            // Return the updated settings
            return this.getSettings(userId) as Promise<UserSettings>;
        } catch (error) {
            console.error('Error updating global feed:', error);
            throw error;
        }
    }

    async updateSettings(userId: number, settings: UserSettings): Promise<UserSettings> {
        const user = await this.getUser(userId);
        if (!user) throw new Error("User not found");

        console.log('Updating settings for user:', userId);
        console.log('Settings to update:', settings);
        console.log('useInAppPlayer value:', settings.useInAppPlayer);
        console.log('removeDuplicates value:', settings.removeDuplicates);
        console.log('searchKeywords value:', settings.searchKeywords);

        try {
            // First check if the user has a settings row
            const checkStmt = this.db.prepare('SELECT 1 FROM settings WHERE user_id = ?');
            const hasSettings = checkStmt.get(Number(userId));

            // Check if the auto_analyze_on_refresh column exists
            const columnInfo = this.db.prepare(`PRAGMA table_info(settings)`).all();
            const autoAnalyzeColumnExists = columnInfo.some(col => col.name === 'auto_analyze_on_refresh');
            // verboseAiLogging column check removed - always enabled for errors

            // Convert boolean values to 0/1 for SQLite
            const darkModeValue = settings.darkMode ? 1 : 0;
            const removeDuplicatesValue = settings.removeDuplicates ? 1 : 0;
            const parallelApiCallsValue = settings.parallelApiCalls ? 1 : 0;
            const useInAppPlayerValue = settings.useInAppPlayer === false ? 0 : 1;
            const previewSoundValue = settings.previewSound ? 1 : 0;
            const defaultUnwatchedFilterValue = settings.defaultUnwatchedFilter ? 1 : 0;

            console.log('Values being saved to DB:');
            console.log('- darkMode:', darkModeValue);
            console.log('- removeDuplicates:', removeDuplicatesValue);
            console.log('- parallelApiCalls:', parallelApiCallsValue);
            console.log('- useInAppPlayer:', useInAppPlayerValue);
            console.log('- previewSound:', previewSoundValue);
            console.log('- searchKeywords:', JSON.stringify(settings.searchKeywords));

            if (!hasSettings) {
                // If the user doesn't have a settings row, insert one
                console.log('No settings row found for user, inserting new row');

                // Build the SQL query based on whether the column exists
                let insertSql = `
                    INSERT INTO settings (
                        user_id,
                        dark_mode,
                        search_keywords,
                        min_views_per_hour,
                        remove_duplicates,
                        exclude_words,
                        auto_refresh_interval,
                        preferred_playback,
                        last_refresh_time,
                        parallel_api_calls,
                        watched_videos,
                        use_in_app_player,
                        preview_sound,
                        playback_sound,
                        active_keyword_group_id,
                        default_sort_method,
                        default_duration_filter,
                        default_unwatched_filter,
                        home_page,
                        selected_prompt_id`;

                // Add the auto_analyze_on_refresh column if it exists
                if (autoAnalyzeColumnExists) {
                    insertSql += `,
                        auto_analyze_on_refresh`;
                }

                insertSql += `
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?`;

                // Add an extra placeholder for auto_analyze_on_refresh if the column exists
                if (autoAnalyzeColumnExists) {
                    insertSql += `, ?`;
                }

                insertSql += `)`;

                const insertStmt = this.db.prepare(insertSql);

                // Convert boolean values to 0/1 for SQLite
                const playbackSoundValue = settings.playbackSound === false ? 0 : 1;

                // Log the OpenRouter model value before inserting
                console.log('Inserting settings with OpenRouter model:', settings.openRouterModel || 'google/gemini-2.0-flash-exp:free');

                // Create the base parameters array
                const baseParams = [
                    Number(userId),
                    darkModeValue,
                    JSON.stringify(settings.searchKeywords || []),
                    settings.minViewsPerHour || 10,
                    removeDuplicatesValue,
                    JSON.stringify(settings.excludeWords || []),
                    settings.autoRefreshInterval || 0,
                    settings.preferredPlayback || 'in_app',
                    settings.lastRefreshTime ? settings.lastRefreshTime.toISOString() : null,
                    parallelApiCallsValue,
                    JSON.stringify(settings.watchedVideos || []),
                    useInAppPlayerValue,
                    previewSoundValue,
                    playbackSoundValue,
                    settings.activeKeywordGroupId,
                    settings.defaultSortMethod || 'popularity',
                    settings.defaultDurationFilter || 'all',
                    defaultUnwatchedFilterValue,
                    settings.homePage,
                    // settings.ollamaModel and settings.ollamaAnalysisPrompt removed
                    settings.selectedPromptId || null
                ];

                // Create a copy of the base parameters for the final params
                let insertParams = [...baseParams];

                // Add the auto_analyze_on_refresh parameter if the column exists
                if (autoAnalyzeColumnExists) {
                    // Explicitly convert to boolean then to 0/1
                    const autoAnalyzeValue = settings.autoAnalyzeOnRefresh === true ? 1 : 0;
                    console.log('Setting auto_analyze_on_refresh to:', autoAnalyzeValue, '(from value:', settings.autoAnalyzeOnRefresh, ')');
                    insertParams.push(autoAnalyzeValue);
                }

                console.log('SQL insert parameters:', insertParams);

                // Execute the insert statement with the parameters
                insertStmt.run(...insertParams);
            } else {
                // If the user has a settings row, update it
                console.log('Updating existing settings row for user');

                // Build the SQL query based on whether the column exists
                let updateSql = `
                    UPDATE settings SET
                        dark_mode = ?,
                        search_keywords = ?,
                        min_views_per_hour = ?,
                        remove_duplicates = ?,
                        exclude_words = ?,
                        auto_refresh_interval = ?,
                        preferred_playback = ?,
                        last_refresh_time = ?,
                        parallel_api_calls = ?,
                        watched_videos = ?,
                        use_in_app_player = ?,
                        preview_sound = ?,
                        playback_sound = ?,
                        active_keyword_group_id = ?,
                        default_sort_method = ?,
                        default_duration_filter = ?,
                        default_unwatched_filter = ?,
                        home_page = ?,
                        selected_prompt_id = ?,
                        openrouter_model = ?,
                        openrouter_analysis_prompt = ?`;

                // Add the auto_analyze_on_refresh column if it exists
                if (autoAnalyzeColumnExists) {
                    updateSql += `,
                        auto_analyze_on_refresh = ?`;
                }

                updateSql += `
                    WHERE user_id = ?
                `;

                const updateStmt = this.db.prepare(updateSql);

                // Convert boolean values to 0/1 for SQLite
                // Explicitly handle the playbackSound value
                let playbackSoundValue;

                // Check if playbackSound is explicitly set to false
                if (settings.playbackSound === false || settings.playbackSound === 0 || settings.playbackSound === 'false') {
                    playbackSoundValue = 0;
                    console.log('Setting playbackSound to 0 (FALSE) in database');
                } else {
                    playbackSoundValue = 1;
                    console.log('Setting playbackSound to 1 (TRUE) in database');
                }

                // Log the raw value for debugging
                console.log('Raw playbackSound value:', settings.playbackSound, 'type:', typeof settings.playbackSound);

                console.log('Updating settings with playbackSound:', settings.playbackSound, 'value:', playbackSoundValue);

                // Log the default watch page settings before updating
                console.log('Default watch page settings being saved:');
                console.log('- defaultSortMethod:', settings.defaultSortMethod || 'popularity');
                console.log('- defaultDurationFilter:', settings.defaultDurationFilter || 'all');
                console.log('- defaultUnwatchedFilter:', defaultUnwatchedFilterValue);

                // Log the OpenRouter model value before saving
                console.log('Updating settings with OpenRouter model:', settings.openRouterModel || 'google/gemini-2.0-flash-exp:free');

                // Create the base parameters array
                const baseParams = [
                    darkModeValue,
                    JSON.stringify(settings.searchKeywords || []),
                    settings.minViewsPerHour || 10,
                    removeDuplicatesValue,
                    JSON.stringify(settings.excludeWords || []),
                    settings.autoRefreshInterval || 0,
                    settings.preferredPlayback || 'in_app',
                    settings.lastRefreshTime ? settings.lastRefreshTime.toISOString() : null,
                    parallelApiCallsValue,
                    JSON.stringify(settings.watchedVideos || []),
                    useInAppPlayerValue,
                    previewSoundValue,
                    playbackSoundValue,
                    settings.activeKeywordGroupId,
                    settings.defaultSortMethod || 'quality',
                    settings.defaultDurationFilter || 'all',
                    defaultUnwatchedFilterValue,
                    settings.homePage,
                    // settings.ollamaModel and settings.ollamaAnalysisPrompt removed
                    settings.selectedPromptId || null,
                    settings.openRouterModel || 'google/gemini-2.0-flash-exp:free',
                    settings.openRouterAnalysisPrompt
                ];

                // Create a copy of the base parameters for the final params
                let params = [...baseParams];

                // Add the auto_analyze_on_refresh parameter if the column exists
                if (autoAnalyzeColumnExists) {
                    // Explicitly convert to boolean then to 0/1
                    // Force a strict comparison with true to ensure proper conversion
                    const autoAnalyzeValue = settings.autoAnalyzeOnRefresh === true ? 1 : 0;
                    console.log('Setting auto_analyze_on_refresh to:', autoAnalyzeValue,
                                '(from value:', settings.autoAnalyzeOnRefresh,
                                ', type:', typeof settings.autoAnalyzeOnRefresh, ')');
                    params.push(autoAnalyzeValue);

                    // Log the final value being saved to the database
                    console.log('Final auto_analyze_on_refresh value being saved to DB:', autoAnalyzeValue);
                }

                // Add the user ID parameter
                params.push(Number(userId));

                console.log('SQL parameters:', params);

                // Execute the update statement with the parameters
                updateStmt.run(...params);
            }

            // Invalidate the settings cache for this user
            this.settingsCache.delete(userId);

            // Get the updated settings from the database to ensure we return the actual saved values
            const updatedSettings = await this.getSettings(userId);

            // Return the updated settings
            return updatedSettings || settings;
        } catch (error) {
            console.error('Error updating settings:', error);
            throw error;
        }
    }

    async getVideos(userId: number): Promise<Video[]> {
        try {
            // Modified to get videos from all keyword groups for the user
            // Use DISTINCT to get only unique video_id values
            const stmt = this.db.prepare(`
                SELECT DISTINCT v.*
                FROM videos v
                WHERE v.user_id = ?
                ORDER BY v.published_at DESC
            `);
            const videos = stmt.all(Number(userId));

            console.log(`Retrieved ${videos.length} videos from DB for user ${userId}`); // Debug log

            // Create a map to deduplicate videos by video_id
            const uniqueVideos = new Map();

            // Process videos and keep only the most recent instance of each video_id
            videos.forEach(v => {
                // If we haven't seen this video_id yet, or if this instance is newer, add it to the map
                if (!uniqueVideos.has(v.video_id) ||
                    new Date(v.published_at) > new Date(uniqueVideos.get(v.video_id).published_at)) {
                    uniqueVideos.set(v.video_id, v);
                }
            });

            console.log(`After deduplication: ${uniqueVideos.size} unique videos`); // Debug log

            // Convert the map values to an array and map to the Video type
            return Array.from(uniqueVideos.values()).map(v => ({
                id: v.video_id,
                url: `https://youtube.com/watch?v=${v.video_id}`,
                title: v.title,
                thumbnail: v.thumbnail,
                channelTitle: v.channel_title,
                viewCount: v.view_count,
                publishedAt: new Date(v.published_at),
                userId: v.user_id,
                keywordGroupId: v.keyword_group_id,
                watched: false,  // Add this field if it's required by the frontend
                // Include all metadata from VidIQ API
                description: v.description,
                channel_id: v.channel_id,
                vph: v.vph,
                tags: v.tags ? JSON.parse(v.tags) : [],
                matched_tags: v.matched_tags ? JSON.parse(v.matched_tags) : [],
                unmatched_tags: v.unmatched_tags ? JSON.parse(v.unmatched_tags) : [],
                seen_from: v.seen_from ? JSON.parse(v.seen_from) : [],
                related_to: v.related_to ? JSON.parse(v.related_to) : [],
                contentDetails: v.content_details ? JSON.parse(v.content_details) : null,
                statistics: v.statistics ? JSON.parse(v.statistics) : null
            }));
        } catch (error) {
            console.error('Error in getVideos:', error);
            throw error;
        }
    }

    async updateVideos(userId: number, videos: Video[]): Promise<void> {
        try {
            console.log('Updating videos for user:', userId, 'Videos:', videos); // Debug log

            const deleteStmt = this.db.prepare('DELETE FROM videos WHERE user_id = ?');
            const insertStmt = this.db.prepare(`
                INSERT INTO videos (
                    video_id, title, thumbnail, channel_title,
                    view_count, published_at, user_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `);

            this.db.transaction(() => {
                deleteStmt.run(Number(userId));
                for (const video of videos) {
                    insertStmt.run(
                        video.id,
                        video.title,
                        video.thumbnail,
                        video.channelTitle,
                        video.viewCount,
                        video.publishedAt.toISOString(),
                        userId
                    );
                }
            })();

            // Verify the update
            const updatedVideos = await this.getVideos(userId);
            console.log('Videos after update:', updatedVideos);
        } catch (error) {
            console.error('Error in updateVideos:', error);
            throw error;
        }
    }

    async refreshVideos(user: User): Promise<Video[]> {
        try {
            const headers = {
                'Accept': 'application/json, text/plain, */*',
                'Authorization': `Bearer ${process.env.VIDIQ_API_KEY || ''}`,
                'Referer': 'https://app.vidiq.com/',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
                'X-TimeZone': 'Asia/Calcutta',
                'X-Vidiq-Client': 'web 1a7277ec05e25b41808d7ed49f9a3f3c6ff86254',
                'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'x-vidiq-auth': process.env.VIDIQ_API_KEY || ''
            };

            const fetchVideos = async (keyword: string) => {
                console.log(`Fetching videos for keyword: ${keyword}`);
                try {
                    const response = await fetch(
                        `https://api.vidiq.com/v0/trendy?q[]=${encodeURIComponent(keyword)}&c=US&l=en&content_details=true`,
                        { headers }
                    );

                    if (!response.ok) {
                        const responseText = await response.text();
                        console.error(`API request failed for ${keyword}:`, {
                            status: response.status,
                            statusText: response.statusText,
                            body: responseText
                        });
                        throw new Error(`API request failed: ${response.statusText}`);
                    }

                    const data = await response.json();
                    console.log(`Received ${data.videos?.length || 0} videos for keyword: ${keyword}`);
                    return data.videos || [];
                } catch (error) {
                    console.error(`Error fetching videos for ${keyword}:`, error);
                    throw error;
                }
            };

            let allVideos: Video[] = [];

            if (user.parallelApiCalls) {
                const promises = user.searchKeywords.map(keyword => fetchVideos(keyword));
                const results = await Promise.all(promises);
                allVideos = results.flat();
            } else {
                for (const keyword of user.searchKeywords) {
                    const videos = await fetchVideos(keyword);
                    allVideos = allVideos.concat(videos);
                }
            }

            console.log(`Total videos fetched: ${allVideos.length}`);

            // Apply filters
            let filteredVideos = allVideos
                .filter(video => {
                    const hoursElapsed = Math.max(1, (Date.now() - new Date(video.published_at).getTime()) / (1000 * 60 * 60));
                    const viewsPerHour = video.views_count / hoursElapsed;
                    return viewsPerHour >= user.minViewsPerHour;
                })
                .filter(video => !user.excludeWords.some(word =>
                    video.title.toLowerCase().includes(word.toLowerCase())
                ));

            console.log(`Videos after filtering: ${filteredVideos.length}`);

            if (user.removeDuplicates) {
                const seen = new Set();
                filteredVideos = filteredVideos.filter(video => {
                    if (seen.has(video.id)) return false;
                    seen.add(video.id);
                    return true;
                });
                console.log(`Videos after deduplication: ${filteredVideos.length}`);
            }

            const processedVideos = filteredVideos.map(video => ({
                id: video.id,
                url: `https://youtube.com/watch?v=${video.id}`,
                title: video.title,
                thumbnail: `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`,
                channelTitle: video.channel_title,
                viewCount: video.views_count,
                publishedAt: new Date(video.published_at),
                userId: user.id,
                // Pass all metadata from VidIQ API
                description: video.description,
                channel_id: video.channel_id,
                vph: video.vph,
                tags: video.tags || [],
                matched_tags: video.matched_tags || [],
                unmatched_tags: video.unmatched_tags || [],
                seen_from: video.seen_from || [],
                related_to: video.related_to || [],
                contentDetails: video.content_details,
                statistics: video.statistics
            }));

            // No duration fetching

            console.log(`Processing ${processedVideos.length} videos for database update`);

            try {
                // Clear existing videos for this user
                const deleteStmt = this.db.prepare('DELETE FROM videos WHERE user_id = ?');
                const insertStmt = this.db.prepare(
                    `INSERT INTO videos (
                        video_id, title, thumbnail, channel_title, view_count, published_at, user_id,
                        description, channel_id, vph, tags, matched_tags, unmatched_tags, seen_from, related_to,
                        content_details, statistics
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
                );

                this.db.transaction(() => {
                    deleteStmt.run(user.id);
                    for (const video of processedVideos) {
                        insertStmt.run(
                            video.id,
                            video.title,
                            video.thumbnail,
                            video.channelTitle,
                            video.viewCount,
                            video.publishedAt.toISOString(),
                            video.userId,
                            video.description || null,
                            video.channel_id || null,
                            video.vph || null,
                            JSON.stringify(video.tags || []),
                            JSON.stringify(video.matched_tags || []),
                            JSON.stringify(video.unmatched_tags || []),
                            JSON.stringify(video.seen_from || []),
                            JSON.stringify(video.related_to || []),
                            JSON.stringify(video.contentDetails || null),
                            JSON.stringify(video.statistics || null)
                        );
                    }
                })();

                // Get the current settings to preserve all values
                const currentSettings = await this.getSettings(user.id);

                if (currentSettings) {
                    // Update the user's lastRefreshTime while preserving all other settings
                    await this.updateSettings(user.id, {
                        ...currentSettings,
                        lastRefreshTime: new Date(),
                    });
                } else {
                    // If no settings found, just update the lastRefreshTime
                    await this.updateSettings(user.id, {
                        ...user,
                        lastRefreshTime: new Date(),
                    });
                }

                console.log('Database update completed successfully');
                return processedVideos;
            } catch (dbError) {
                console.error('Database operation failed:', dbError);
                throw dbError;
            }
        } catch (error) {
            console.error('Error in refreshVideos:', error);
            throw error;
        }
    }

    async deleteUserData(userId: number): Promise<void> {
        try {
            this.db.transaction(() => {
                this.db.prepare('DELETE FROM videos WHERE user_id = ?').run(userId);
                this.db.prepare('DELETE FROM settings WHERE user_id = ?').run(userId);
                // Option 1: Delete user completely
                // this.db.prepare('DELETE FROM users WHERE id = ?').run(userId);

                // Option 2: Reset user to defaults (like MemStorage implementation)
                this.db.prepare(`
                    UPDATE settings SET
                        dark_mode = 1,
                        search_keywords = ?,
                        min_views_per_hour = 10,
                        remove_duplicates = 1,
                        exclude_words = '[]',
                        auto_refresh_interval = 0,
                        preferred_playback = 'in_app',
                        last_refresh_time = NULL,
                        parallel_api_calls = 1,
                        watched_videos = '[]',
                        use_in_app_player = 1,
                        preview_sound = 0,
                        playback_sound = 1,
                        ollama_model = 'llama3'
                    WHERE user_id = ?
                `).run(JSON.stringify(["vlog", "funny", "fail"]), userId);
            })();
        } catch (error) {
            console.error('Failed to delete user data:', error);
            throw error;
        }
    }

    async getAdminSettings(): Promise<AdminSettings> {
        try {
            // Check if admin settings exist
            const countStmt = this.db.prepare('SELECT COUNT(*) as count FROM admin_settings');
            const { count } = countStmt.get();

            // If no settings exist, create default settings
            if (count === 0) {
                const insertStmt = this.db.prepare('INSERT INTO admin_settings (server_port, localhost_only) VALUES (?, ?)');
                insertStmt.run(5001, 0);
            }

            // Get the settings
            const stmt = this.db.prepare('SELECT * FROM admin_settings ORDER BY id DESC LIMIT 1');
            const settings = stmt.get();

            return {
                serverPort: settings.server_port,
                localhostOnly: settings.localhost_only === 1,
                lastUpdated: settings.last_updated ? new Date(settings.last_updated) : new Date()
            };
        } catch (error) {
            console.error('Error getting admin settings:', error);
            return {
                serverPort: 5001,
                localhostOnly: false,
                lastUpdated: new Date()
            };
        }
    }

    async updateAdminSettings(settings: { serverPort: number, localhostOnly: boolean }): Promise<AdminSettings> {
        try {
            const { serverPort, localhostOnly } = settings;
            const localhostOnlyValue = localhostOnly ? 1 : 0;

            // Update the settings
            const stmt = this.db.prepare('UPDATE admin_settings SET server_port = ?, localhost_only = ?, last_updated = CURRENT_TIMESTAMP WHERE id = (SELECT id FROM admin_settings ORDER BY id DESC LIMIT 1)');
            stmt.run(serverPort, localhostOnlyValue);

            return await this.getAdminSettings();
        } catch (error) {
            console.error('Error updating admin settings:', error);
            throw error;
        }
    }

    // Admin methods
    async getAllUsers(): Promise<User[]> {
        try {
            const stmt = this.db.prepare('SELECT id, username, role FROM users');
            const users = stmt.all();

            // Set isAdmin based on role
            return users.map(user => ({
                ...user,
                isAdmin: user.role === 'admin'
            }));
        } catch (error) {
            console.error('Error getting all users:', error);
            throw error;
        }
    }

    async createUser(username: string, password: string, role: string = 'user', isAdmin: boolean = false): Promise<User> {
        // Check if username already exists
        const existingUser = await this.getUserByUsername(username);
        if (existingUser) {
            throw new Error('Username already exists');
        }

        // If isAdmin is true, set role to admin
        const finalRole = isAdmin ? 'admin' : role;

        // Insert new user
        const insertUserStmt = this.db.prepare(`
            INSERT INTO users (username, password, role)
            VALUES (?, ?, ?)
        `);

        const result = insertUserStmt.run(
            username,
            password, // In a real app, you'd hash this password
            finalRole
        );

        const userId = result.lastInsertRowid as number;

        // Create default settings for the new user
        const insertSettingsStmt = this.db.prepare(`
            INSERT INTO settings (
                user_id, dark_mode, search_keywords, min_views_per_hour,
                remove_duplicates, exclude_words, auto_refresh_interval,
                preferred_playback, parallel_api_calls, watched_videos, use_in_app_player
            ) VALUES (?, 1, ?, 10, 1, ?, 0, 'in_app', 0, ?, 1)
        `);

        insertSettingsStmt.run(
            userId,
            JSON.stringify([]),
            JSON.stringify([]),
            JSON.stringify([])
        );

        // Create default TXT prompts for the new user
        this.createDefaultTxtPromptsForUser(userId);

        return {
            id: userId,
            username,
            role,
            isAdmin
        };
    }

    async deleteUser(userId: number): Promise<void> {
        const user = await this.getUser(userId);
        if (!user) throw new Error("User not found");

        // Delete user's videos
        const deleteVideosStmt = this.db.prepare('DELETE FROM videos WHERE user_id = ?');
        deleteVideosStmt.run(Number(userId));

        // Delete user's settings
        const deleteSettingsStmt = this.db.prepare('DELETE FROM settings WHERE user_id = ?');
        deleteSettingsStmt.run(Number(userId));

        // Delete the user
        const deleteUserStmt = this.db.prepare('DELETE FROM users WHERE id = ?');
        deleteUserStmt.run(Number(userId));
    }

    async updateUser(userId: number, updates: Partial<User>): Promise<User> {
        const user = await this.getUser(userId);
        if (!user) throw new Error("User not found");

        const updateFields: string[] = [];
        const params: any[] = [];

        if (updates.password !== undefined) {
            updateFields.push('password = ?');
            params.push(updates.password);
        }

        if (updates.role !== undefined) {
            updateFields.push('role = ?');
            params.push(updates.role);
        }

        if (updateFields.length > 0) {
            params.push(Number(userId));
            const stmt = this.db.prepare(`
                UPDATE users SET
                    ${updateFields.join(', ')}
                WHERE id = ?
            `);

            stmt.run(...params);
        }

        return {
            ...user,
            ...updates
        };
    }

    async updateUserRole(userId: number, role: string, isAdmin: boolean): Promise<User> {
        const user = await this.getUser(userId);
        if (!user) throw new Error("User not found");

        // If isAdmin is true, set role to admin, otherwise use the provided role
        const finalRole = isAdmin ? 'admin' : role;

        const stmt = this.db.prepare(`
            UPDATE users SET
                role = ?
            WHERE id = ?
        `);

        stmt.run(
            finalRole,
            Number(userId)
        );

        return {
            ...user,
            role: finalRole,
            isAdmin
        };
    }

    async vacuumDatabase(): Promise<{ sizeBefore: number, sizeAfter: number, spaceReclaimed: number }> {
        try {
            // Get database size before vacuum
            const pageSize = this.db.prepare('PRAGMA page_size').get().page_size;
            const pageCountBefore = this.db.prepare('PRAGMA page_count').get().page_count;
            const sizeBefore = (pageSize * pageCountBefore) / (1024 * 1024); // Size in MB

            console.log(`Database size before vacuum: ${sizeBefore.toFixed(2)} MB`);

            // Run the VACUUM command
            console.log('Running VACUUM command...');
            this.db.exec('VACUUM');

            // Get database size after vacuum
            const pageCountAfter = this.db.prepare('PRAGMA page_count').get().page_count;
            const sizeAfter = (pageSize * pageCountAfter) / (1024 * 1024); // Size in MB
            const spaceReclaimed = sizeBefore - sizeAfter;

            console.log(`Database size after vacuum: ${sizeAfter.toFixed(2)} MB`);
            console.log(`Space reclaimed: ${spaceReclaimed.toFixed(2)} MB`);

            return {
                sizeBefore: parseFloat(sizeBefore.toFixed(2)),
                sizeAfter: parseFloat(sizeAfter.toFixed(2)),
                spaceReclaimed: parseFloat(spaceReclaimed.toFixed(2))
            };
        } catch (error) {
            console.error('Vacuum process failed:', error);
            throw error;
        }
    }

    async getAdminStats(): Promise<any> {
        try {
            // Basic stats
            const userCountStmt = this.db.prepare('SELECT COUNT(*) as count FROM users');
            const videoCountStmt = this.db.prepare('SELECT COUNT(*) as count FROM videos');
            const keywordGroupsStmt = this.db.prepare('SELECT COUNT(*) as count FROM keyword_groups');
            const playlistsStmt = this.db.prepare('SELECT COUNT(*) as count FROM playlists');

            // Database size
            const dbSizeStmt = this.db.prepare('PRAGMA page_count');
            const pageSize = this.db.prepare('PRAGMA page_size').get().page_size;
            const pageCount = dbSizeStmt.get().page_count;
            const dbSizeBytes = pageSize * pageCount;

            // Get counts
            const userCount = userCountStmt.get().count;
            const videoCount = videoCountStmt.get().count;
            const keywordGroupCount = keywordGroupsStmt.get().count;
            const playlistCount = playlistsStmt.get().count;

            // Get most active users (by video count) - use actual usernames
            const activeUsersStmt = this.db.prepare(`
                SELECT u.id, u.username, COUNT(v.id) as video_count
                FROM users u
                LEFT JOIN videos v ON u.id = v.user_id
                GROUP BY u.id
                ORDER BY video_count DESC
            `);
            const activeUsers = activeUsersStmt.all();

            // Get trending videos (by views per hour)
            const trendingVideosStmt = this.db.prepare(`
                SELECT id, title, vph
                FROM videos
                WHERE vph > 0
                ORDER BY vph DESC
                LIMIT 5
            `);
            const trendingVideos = trendingVideosStmt.all();

            // Get most used keyword groups
            const keywordGroupUsageStmt = this.db.prepare(`
                SELECT kg.id, kg.name, COUNT(v.id) as video_count
                FROM keyword_groups kg
                LEFT JOIN videos v ON v.keyword_group_id = kg.id
                GROUP BY kg.id
                ORDER BY video_count DESC
                LIMIT 5
            `);
            const keywordGroupUsage = keywordGroupUsageStmt.all();

            // Since we don't have created_at column, use mock data for video growth
            const videoGrowth = [
                { date: '2023-05-01', count: 10 },
                { date: '2023-05-02', count: 15 },
                { date: '2023-05-03', count: 12 },
                { date: '2023-05-04', count: 20 },
                { date: '2023-05-05', count: 18 }
            ];

            // Get storage usage by table (only for main tables)
            const mainTables = ['users', 'videos', 'keyword_groups', 'playlists', 'settings'];
            const tableStats = [];

            for (const tableName of mainTables) {
                try {
                    const rowCount = this.db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get().count;
                    tableStats.push({
                        name: tableName,
                        rows: rowCount
                    });
                } catch (error) {
                    console.error(`Error getting stats for table ${tableName}:`, error);
                }
            }

            // Get video statistics - simplified to avoid column issues
            const videoStatsStmt = this.db.prepare(`
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN duration < 60 THEN 1 ELSE 0 END) as shorts_count,
                    SUM(CASE WHEN duration >= 60 THEN 1 ELSE 0 END) as regular_count
                FROM videos
            `);
            const videoStats = videoStatsStmt.get();

            return {
                // Summary stats - only the essential counts
                summary: {
                    totalUsers: userCount,
                    totalVideos: videoCount,
                    totalKeywordGroups: keywordGroupCount,
                    totalPlaylists: playlistCount,
                    databaseSize: Math.round(dbSizeBytes / (1024 * 1024) * 100) / 100 // MB
                },

                // User insights - with actual usernames
                userInsights: {
                    activeUsers: activeUsers.map(user => ({
                        id: user.id,
                        username: user.username || `User ${user.id}`,
                        videoCount: user.video_count
                    })),
                    averageVideosPerUser: userCount > 0 ? Math.round((videoCount / userCount) * 10) / 10 : 0
                },

                // Video insights - focused on the most valuable metrics
                videoInsights: {
                    trendingVideos: trendingVideos.map(video => ({
                        id: video.id,
                        title: video.title || `Video ${video.id}`,
                        viewsPerHour: video.vph
                    })),
                    shortsPercentage: videoCount > 0 ? Math.round((videoStats.shorts_count / videoCount) * 100) : 0,
                    regularPercentage: videoCount > 0 ? Math.round((videoStats.regular_count / videoCount) * 100) : 0,
                    videoGrowth: videoGrowth
                },

                // Content insights - what keywords are most effective
                contentInsights: {
                    topKeywordGroups: keywordGroupUsage.map(group => ({
                        id: group.id,
                        name: group.name,
                        videoCount: group.video_count
                    }))
                }
            };
        } catch (error) {
            console.error('Error getting admin stats:', error);
            throw error;
        }
    }

    // Keyword Group Methods
    async getKeywordGroups(userId: number): Promise<KeywordGroup[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM keyword_groups
            WHERE user_id = ?
            ORDER BY display_order ASC, name ASC
        `);

        const groups = stmt.all(Number(userId));
        return groups.map(group => ({
            id: group.id,
            userId: group.user_id,
            name: group.name,
            keywords: JSON.parse(group.keywords),
            excludeWords: group.exclude_words ? JSON.parse(group.exclude_words) : [],
            lastRefreshTime: group.last_refresh_time ? new Date(group.last_refresh_time) : null,
            displayOrder: group.display_order || 0
        }));
    }

    async getKeywordGroup(groupId: number): Promise<KeywordGroup | undefined> {
        const stmt = this.db.prepare(`
            SELECT * FROM keyword_groups
            WHERE id = ?
        `);

        const group = stmt.get(Number(groupId));
        if (!group) return undefined;

        return {
            id: group.id,
            userId: group.user_id,
            name: group.name,
            keywords: JSON.parse(group.keywords),
            excludeWords: group.exclude_words ? JSON.parse(group.exclude_words) : [],
            lastRefreshTime: group.last_refresh_time ? new Date(group.last_refresh_time) : null,
            displayOrder: group.display_order || 0
        };
    }

    async createKeywordGroup(userId: number, group: InsertKeywordGroup): Promise<KeywordGroup> {
        // Get the highest display_order for this user's groups
        const orderStmt = this.db.prepare(`
            SELECT MAX(display_order) as max_order FROM keyword_groups
            WHERE user_id = ?
        `);
        const result = orderStmt.get(Number(userId));
        const nextOrder = (result.max_order || 0) + 1;

        const stmt = this.db.prepare(`
            INSERT INTO keyword_groups (user_id, name, keywords, exclude_words, display_order)
            VALUES (?, ?, ?, ?, ?)
        `);

        const insertResult = stmt.run(
            Number(userId),
            group.name,
            JSON.stringify(group.keywords),
            JSON.stringify(group.excludeWords || []),
            nextOrder
        );

        const groupId = Number(insertResult.lastInsertRowid);
        return {
            id: groupId,
            userId: Number(userId),
            name: group.name,
            keywords: group.keywords,
            excludeWords: group.excludeWords || [],
            lastRefreshTime: null,
            displayOrder: nextOrder
        };
    }

    async updateKeywordGroup(groupId: number, group: Partial<KeywordGroup>): Promise<KeywordGroup> {
        // First get the current group to merge with updates
        const currentGroup = await this.getKeywordGroup(groupId);
        if (!currentGroup) {
            throw new Error(`Keyword group with ID ${groupId} not found`);
        }

        // Build the update query dynamically based on provided fields
        const updates: string[] = [];
        const params: any[] = [];

        if (group.name !== undefined) {
            updates.push('name = ?');
            params.push(group.name);
        }

        if (group.keywords !== undefined) {
            updates.push('keywords = ?');
            params.push(JSON.stringify(group.keywords));
        }

        if (group.excludeWords !== undefined) {
            updates.push('exclude_words = ?');
            params.push(JSON.stringify(group.excludeWords));
        }

        if (group.lastRefreshTime !== undefined) {
            updates.push('last_refresh_time = ?');
            params.push(group.lastRefreshTime ? group.lastRefreshTime.toISOString() : null);
        }

        // Add the group ID as the last parameter
        params.push(Number(groupId));

        // Execute the update if there are fields to update
        if (updates.length > 0) {
            const stmt = this.db.prepare(`
                UPDATE keyword_groups
                SET ${updates.join(', ')}
                WHERE id = ?
            `);

            stmt.run(...params);
        }

        // Return the updated group
        return {
            ...currentGroup,
            ...group,
        };
    }

    async deleteKeywordGroup(groupId: number): Promise<void> {
        // First check if this group is active for any user
        const activeCheckStmt = this.db.prepare(`
            SELECT user_id FROM settings
            WHERE active_keyword_group_id = ?
        `);

        const activeUsers = activeCheckStmt.all(Number(groupId));

        // For each user with this as their active group, set active_keyword_group_id to null
        if (activeUsers.length > 0) {
            const updateStmt = this.db.prepare(`
                UPDATE settings
                SET active_keyword_group_id = NULL
                WHERE active_keyword_group_id = ?
            `);

            updateStmt.run(Number(groupId));
        }

        // Delete any videos associated with this group
        const deleteVideosStmt = this.db.prepare(`
            DELETE FROM videos
            WHERE keyword_group_id = ?
        `);

        deleteVideosStmt.run(Number(groupId));

        // Finally delete the group itself
        const deleteGroupStmt = this.db.prepare(`
            DELETE FROM keyword_groups
            WHERE id = ?
        `);

        deleteGroupStmt.run(Number(groupId));
    }

    async setActiveKeywordGroup(userId: number, groupId: number | null): Promise<void> {
        const stmt = this.db.prepare(`
            UPDATE settings
            SET active_keyword_group_id = ?
            WHERE user_id = ?
        `);

        stmt.run(
            groupId,
            Number(userId)
        );
    }

    async updateKeywordGroupsOrder(userId: number, groupOrders: { id: number, order: number }[]): Promise<void> {
        // Start a transaction
        this.db.exec('BEGIN TRANSACTION');

        try {
            const stmt = this.db.prepare(`
                UPDATE keyword_groups
                SET display_order = ?
                WHERE id = ? AND user_id = ?
            `);

            // Update each group's order
            for (const group of groupOrders) {
                stmt.run(group.order, group.id, userId);
            }

            // Commit the transaction
            this.db.exec('COMMIT');
        } catch (error) {
            // Rollback on error
            this.db.exec('ROLLBACK');
            console.error('Error updating keyword groups order:', error);
            throw error;
        }
    }

    // VidIQ API Key methods
    async getAllApiKeys(): Promise<VidIQApiKey[]> {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM vidiq_api_keys
                ORDER BY created_at DESC
            `);
            const rows = stmt.all();
            return rows.map(row => ({
                ...row,
                is_active: Boolean(row.is_active),
                last_used: row.last_used ? new Date(row.last_used) : undefined,
                created_at: new Date(row.created_at),
                exhausted_until: row.exhausted_until ? new Date(row.exhausted_until) : undefined
            }));
        } catch (error) {
            console.error('Error getting all API keys:', error);
            return [];
        }
    }

    async getActiveApiKeys(): Promise<VidIQApiKey[]> {
        try {
            const now = new Date().toISOString();
            const stmt = this.db.prepare(`
                SELECT * FROM vidiq_api_keys
                WHERE is_active = 1
                AND (exhausted_until IS NULL OR exhausted_until < ?)
                ORDER BY last_used ASC
            `);
            const rows = stmt.all(now);
            return rows.map(row => ({
                ...row,
                is_active: Boolean(row.is_active),
                last_used: row.last_used ? new Date(row.last_used) : undefined,
                created_at: new Date(row.created_at),
                exhausted_until: row.exhausted_until ? new Date(row.exhausted_until) : undefined
            }));
        } catch (error) {
            console.error('Error getting active API keys:', error);
            return [];
        }
    }

    async getApiKey(id: number): Promise<VidIQApiKey | undefined> {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM vidiq_api_keys
                WHERE id = ?
            `);
            const row = stmt.get(id);
            if (!row) return undefined;

            return {
                ...row,
                is_active: Boolean(row.is_active),
                last_used: row.last_used ? new Date(row.last_used) : undefined,
                created_at: new Date(row.created_at),
                exhausted_until: row.exhausted_until ? new Date(row.exhausted_until) : undefined
            };
        } catch (error) {
            console.error(`Error getting API key with id ${id}:`, error);
            return undefined;
        }
    }

    async addApiKey(apiKey: Omit<VidIQApiKey, 'id' | 'created_at'>): Promise<VidIQApiKey> {
        try {
            // Extract the bearer token from the full authorization header if needed
            let token = apiKey.token;
            if (token.startsWith('Bearer ')) {
                token = token.substring(7);
            }

            const stmt = this.db.prepare(`
                INSERT INTO vidiq_api_keys (token, auth_token, cookie, name, is_active, user_id)
                VALUES (?, ?, ?, ?, ?, ?)
            `);

            const result = stmt.run(
                token,
                apiKey.auth_token || null,
                apiKey.cookie || null,
                apiKey.name || `API Key ${new Date().toLocaleString()}`,
                apiKey.is_active ? 1 : 0,
                apiKey.user_id || null
            );

            console.log(`Added new VidIQ API key with ID ${result.lastInsertRowid}`);

            return this.getApiKey(result.lastInsertRowid as number) as VidIQApiKey;
        } catch (error) {
            console.error('Error adding API key:', error);
            throw error;
        }
    }

    async updateApiKey(id: number, apiKey: Partial<VidIQApiKey>): Promise<VidIQApiKey | undefined> {
        try {
            const currentKey = await this.getApiKey(id);
            if (!currentKey) return undefined;

            const updates: string[] = [];
            const params: any[] = [];

            if (apiKey.token !== undefined) {
                let token = apiKey.token;
                if (token.startsWith('Bearer ')) {
                    token = token.substring(7);
                }
                updates.push('token = ?');
                params.push(token);
            }

            if (apiKey.auth_token !== undefined) {
                updates.push('auth_token = ?');
                params.push(apiKey.auth_token);
            }

            if (apiKey.cookie !== undefined) {
                updates.push('cookie = ?');
                params.push(apiKey.cookie);
            }

            if (apiKey.name !== undefined) {
                updates.push('name = ?');
                params.push(apiKey.name);
            }

            if (apiKey.is_active !== undefined) {
                updates.push('is_active = ?');
                params.push(apiKey.is_active ? 1 : 0);
            }

            if (apiKey.last_used !== undefined) {
                updates.push('last_used = ?');
                params.push(apiKey.last_used.toISOString());
            }

            if (apiKey.exhausted_until !== undefined) {
                updates.push('exhausted_until = ?');
                params.push(apiKey.exhausted_until.toISOString());
            }

            if (apiKey.user_id !== undefined) {
                updates.push('user_id = ?');
                params.push(apiKey.user_id);
            }

            if (updates.length === 0) return currentKey;

            params.push(id);

            const stmt = this.db.prepare(`
                UPDATE vidiq_api_keys
                SET ${updates.join(', ')}
                WHERE id = ?
            `);

            stmt.run(...params);

            return this.getApiKey(id);
        } catch (error) {
            console.error(`Error updating API key with id ${id}:`, error);
            return undefined;
        }
    }

    async deleteApiKey(id: number): Promise<void> {
        try {
            const stmt = this.db.prepare(`
                DELETE FROM vidiq_api_keys
                WHERE id = ?
            `);
            stmt.run(id);
        } catch (error) {
            console.error(`Error deleting API key with id ${id}:`, error);
            throw error;
        }
    }

    async getNextAvailableApiKey(): Promise<VidIQApiKey | undefined> {
        try {
            const activeKeys = await this.getActiveApiKeys();
            if (activeKeys.length === 0) return undefined;

            // Get the first available key (ordered by last_used ASC)
            const nextKey = activeKeys[0];

            // Update the last_used timestamp
            await this.updateApiKey(nextKey.id, {
                last_used: new Date()
            });

            console.log(`Using VidIQ API key #${nextKey.id}: ${nextKey.name || 'Unnamed key'}`);
            return nextKey;
        } catch (error) {
            console.error('Error getting next available API key:', error);
            return undefined;
        }
    }

    async markApiKeyExhausted(id: number, minutes: number = 60): Promise<void> {
        try {
            const exhaustedUntil = new Date();
            exhaustedUntil.setMinutes(exhaustedUntil.getMinutes() + minutes);

            await this.updateApiKey(id, {
                exhausted_until: exhaustedUntil
            });

            console.log(`Marked VidIQ API key #${id} as exhausted until ${exhaustedUntil.toLocaleString()}`);
        } catch (error) {
            console.error(`Error marking API key ${id} as exhausted:`, error);
            throw error;
        }
    }

    // Playlist methods
    async getPlaylists(userId: number): Promise<Playlist[]> {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM playlists
                WHERE user_id = ?
                ORDER BY name ASC
            `);
            const playlists = stmt.all(Number(userId));

            return playlists.map(p => ({
                id: p.id,
                userId: p.user_id,
                name: p.name,
                description: p.description,
                createdAt: new Date(p.created_at),
                updatedAt: new Date(p.updated_at)
            }));
        } catch (error) {
            console.error('Error getting playlists:', error);
            throw error;
        }
    }

    async getPlaylist(playlistId: number): Promise<Playlist | undefined> {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM playlists
                WHERE id = ?
            `);
            const playlist = stmt.get(Number(playlistId));

            if (!playlist) return undefined;

            return {
                id: playlist.id,
                userId: playlist.user_id,
                name: playlist.name,
                description: playlist.description,
                createdAt: new Date(playlist.created_at),
                updatedAt: new Date(playlist.updated_at)
            };
        } catch (error) {
            console.error(`Error getting playlist ${playlistId}:`, error);
            throw error;
        }
    }

    async createPlaylist(userId: number, name: string, description?: string): Promise<Playlist> {
        try {
            const now = new Date().toISOString();
            const stmt = this.db.prepare(`
                INSERT INTO playlists (user_id, name, description, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            `);

            const result = stmt.run(
                Number(userId),
                name,
                description || null,
                now,
                now
            );

            const playlistId = Number(result.lastInsertRowid);

            return {
                id: playlistId,
                userId: Number(userId),
                name,
                description,
                createdAt: new Date(now),
                updatedAt: new Date(now)
            };
        } catch (error) {
            console.error(`Error creating playlist for user ${userId}:`, error);
            throw error;
        }
    }

    async updatePlaylist(playlistId: number, updates: Partial<Playlist>): Promise<Playlist> {
        try {
            const currentPlaylist = await this.getPlaylist(playlistId);
            if (!currentPlaylist) {
                throw new Error(`Playlist with ID ${playlistId} not found`);
            }

            const now = new Date().toISOString();
            const stmt = this.db.prepare(`
                UPDATE playlists
                SET name = ?, description = ?, updated_at = ?
                WHERE id = ?
            `);

            stmt.run(
                updates.name || currentPlaylist.name,
                updates.description || currentPlaylist.description || null,
                now,
                Number(playlistId)
            );

            return {
                ...currentPlaylist,
                name: updates.name || currentPlaylist.name,
                description: updates.description || currentPlaylist.description,
                updatedAt: new Date(now)
            };
        } catch (error) {
            console.error(`Error updating playlist ${playlistId}:`, error);
            throw error;
        }
    }

    async deletePlaylist(playlistId: number): Promise<void> {
        try {
            // First delete all videos in the playlist
            const deleteVideosStmt = this.db.prepare(`
                DELETE FROM playlist_videos
                WHERE playlist_id = ?
            `);
            deleteVideosStmt.run(Number(playlistId));

            // Then delete the playlist
            const deletePlaylistStmt = this.db.prepare(`
                DELETE FROM playlists
                WHERE id = ?
            `);
            deletePlaylistStmt.run(Number(playlistId));

            console.log(`Deleted playlist ${playlistId} and all its videos`);
        } catch (error) {
            console.error(`Error deleting playlist ${playlistId}:`, error);
            throw error;
        }
    }

    // Playlist video methods
    async getPlaylistVideos(playlistId: number): Promise<PlaylistVideo[]> {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM playlist_videos
                WHERE playlist_id = ?
                ORDER BY added_at DESC
            `);
            const videos = stmt.all(Number(playlistId));

            return videos.map(v => ({
                id: v.id,
                playlistId: v.playlist_id,
                videoId: v.video_id,
                title: v.title,
                thumbnail: v.thumbnail,
                channelTitle: v.channel_title,
                viewCount: v.view_count,
                publishedAt: new Date(v.published_at),
                addedAt: new Date(v.added_at)
            }));
        } catch (error) {
            console.error(`Error getting videos for playlist ${playlistId}:`, error);
            throw error;
        }
    }

    async addVideoToPlaylist(playlistId: number, video: Video): Promise<PlaylistVideo> {
        try {
            // Check if the playlist exists
            const playlist = await this.getPlaylist(playlistId);
            if (!playlist) {
                throw new Error(`Playlist with ID ${playlistId} not found`);
            }

            // Check if the video is already in the playlist
            const checkStmt = this.db.prepare(`
                SELECT * FROM playlist_videos
                WHERE playlist_id = ? AND video_id = ?
            `);
            const existingVideo = checkStmt.get(Number(playlistId), video.id);

            if (existingVideo) {
                return {
                    id: existingVideo.id,
                    playlistId: existingVideo.playlist_id,
                    videoId: existingVideo.video_id,
                    title: existingVideo.title,
                    thumbnail: existingVideo.thumbnail,
                    channelTitle: existingVideo.channel_title,
                    viewCount: existingVideo.view_count,
                    publishedAt: new Date(existingVideo.published_at),
                    addedAt: new Date(existingVideo.added_at)
                };
            }

            // Add the video to the playlist
            const now = new Date().toISOString();
            const stmt = this.db.prepare(`
                INSERT INTO playlist_videos (
                    playlist_id, video_id, title, thumbnail, channel_title, view_count, published_at, added_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `);

            const result = stmt.run(
                Number(playlistId),
                video.id,
                video.title,
                video.thumbnail,
                video.channelTitle,
                video.viewCount,
                typeof video.publishedAt === 'string' ? video.publishedAt : video.publishedAt.toISOString(),
                now
            );

            const videoId = Number(result.lastInsertRowid);

            // Update the playlist's updated_at timestamp
            const updatePlaylistStmt = this.db.prepare(`
                UPDATE playlists
                SET updated_at = ?
                WHERE id = ?
            `);
            updatePlaylistStmt.run(now, Number(playlistId));

            return {
                id: videoId,
                playlistId: Number(playlistId),
                videoId: video.id,
                title: video.title,
                thumbnail: video.thumbnail,
                channelTitle: video.channelTitle,
                viewCount: video.viewCount,
                publishedAt: video.publishedAt,
                addedAt: new Date(now)
            };
        } catch (error) {
            console.error(`Error adding video to playlist ${playlistId}:`, error);
            throw error;
        }
    }

    async removeVideoFromPlaylist(playlistId: number, videoId: string): Promise<void> {
        try {
            const now = new Date().toISOString();

            // Delete the video from the playlist
            const stmt = this.db.prepare(`
                DELETE FROM playlist_videos
                WHERE playlist_id = ? AND video_id = ?
            `);
            stmt.run(Number(playlistId), videoId);

            // Update the playlist's updated_at timestamp
            const updatePlaylistStmt = this.db.prepare(`
                UPDATE playlists
                SET updated_at = ?
                WHERE id = ?
            `);
            updatePlaylistStmt.run(now, Number(playlistId));

            console.log(`Removed video ${videoId} from playlist ${playlistId}`);
        } catch (error) {
            console.error(`Error removing video ${videoId} from playlist ${playlistId}:`, error);
            throw error;
        }
    }

    // Cast Queue Methods
    async getCastQueue(userId: number): Promise<Video[]> {
        try {
            // First, ensure the cast_queue table exists
            try {
                this.db.exec(`
                    CREATE TABLE IF NOT EXISTS cast_queue (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        video_id TEXT NOT NULL,
                        video_data TEXT NOT NULL,
                        position INTEGER NOT NULL,
                        added_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    );
                `);
            } catch (tableError) {
                console.error('Error creating cast_queue table:', tableError);
            }

            // Verify the user ID is valid
            if (!userId || isNaN(Number(userId))) {
                console.error(`Invalid user ID: ${userId}`);
                return [];
            }

            // Use a try-catch block specifically for the database query
            try {
                const stmt = this.db.prepare(`
                    SELECT video_id, video_data, position
                    FROM cast_queue
                    WHERE user_id = ?
                    ORDER BY position ASC
                `);

                const rows = stmt.all(Number(userId));

                // Parse each video data and handle potential JSON parsing errors
                const videos = rows.map(row => {
                    try {
                        return JSON.parse(row.video_data);
                    } catch (parseError) {
                        console.error(`Error parsing video data for video ${row.video_id}:`, parseError);
                        return null;
                    }
                }).filter(video => video !== null);

                return videos;
            } catch (queryError) {
                console.error(`Database error getting cast queue for user ${userId}:`, queryError);
                return [];
            }
        } catch (error) {
            console.error(`Error getting cast queue for user ${userId}:`, error);
            return [];
        }
    }

    async addToCastQueue(userId: number, video: Video): Promise<void> {
        try {
            console.log(`SQLiteStorage: Adding video ${video.id} to cast queue for user ${userId}`);

            // First, ensure the cast_queue table exists
            try {
                this.db.exec(`
                    CREATE TABLE IF NOT EXISTS cast_queue (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        video_id TEXT NOT NULL,
                        video_data TEXT NOT NULL,
                        position INTEGER NOT NULL,
                        added_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    );
                `);
                console.log('Cast queue table created or verified');
            } catch (tableError) {
                console.error('Error creating cast_queue table:', tableError);
                throw new Error(`Failed to create cast_queue table: ${tableError.message}`);
            }

            // Check if the video is already in the queue
            const checkStmt = this.db.prepare(`
                SELECT COUNT(*) as count
                FROM cast_queue
                WHERE user_id = ? AND video_id = ?
            `);

            const result = checkStmt.get(Number(userId), video.id);
            console.log(`Check if video exists in queue: count = ${result.count}`);

            if (result.count > 0) {
                // Video already in queue, don't add it again
                console.log(`Video ${video.id} already in queue for user ${userId}, skipping`);
                return;
            }

            // Get the highest position for this user's queue
            const positionStmt = this.db.prepare(`
                SELECT MAX(position) as max_position
                FROM cast_queue
                WHERE user_id = ?
            `);

            const positionResult = positionStmt.get(Number(userId));
            const nextPosition = (positionResult.max_position !== null ? positionResult.max_position : -1) + 1;
            console.log(`Next position for video in queue: ${nextPosition}`);

            // Prepare the video data for storage
            const videoData = JSON.stringify(video);
            console.log(`Prepared video data for storage, length: ${videoData.length} characters`);

            // Add the video to the queue
            const insertStmt = this.db.prepare(`
                INSERT INTO cast_queue (user_id, video_id, video_data, position)
                VALUES (?, ?, ?, ?)
            `);

            const insertResult = insertStmt.run(
                Number(userId),
                video.id,
                videoData,
                nextPosition
            );

            console.log(`Added video ${video.id} to cast queue for user ${userId}, result:`, insertResult);
        } catch (error) {
            console.error(`Error adding video to cast queue for user ${userId}:`, error);
            throw error;
        }
    }

    async removeFromCastQueue(userId: number, videoId: string): Promise<void> {
        try {
            const stmt = this.db.prepare(`
                DELETE FROM cast_queue
                WHERE user_id = ? AND video_id = ?
            `);

            stmt.run(Number(userId), videoId);
            console.log(`Removed video ${videoId} from cast queue for user ${userId}`);

            // Reorder the remaining items to ensure positions are sequential
            this.reorderCastQueue(userId);
        } catch (error) {
            console.error(`Error removing video ${videoId} from cast queue for user ${userId}:`, error);
            throw error;
        }
    }

    async clearCastQueue(userId: number): Promise<void> {
        try {
            const stmt = this.db.prepare(`
                DELETE FROM cast_queue
                WHERE user_id = ?
            `);

            stmt.run(Number(userId));
            console.log(`Cleared cast queue for user ${userId}`);
        } catch (error) {
            console.error(`Error clearing cast queue for user ${userId}:`, error);
            throw error;
        }
    }

    async updateCastQueuePosition(userId: number, position: number): Promise<void> {
        try {
            // Check if the settings table has the cast_queue_position column
            const tableInfo = this.db.prepare('PRAGMA table_info(settings)').all();
            const hasCastQueuePosition = tableInfo.some((column: any) => column.name === 'cast_queue_position');

            if (!hasCastQueuePosition) {
                // Add the column if it doesn't exist
                this.db.exec('ALTER TABLE settings ADD COLUMN cast_queue_position INTEGER DEFAULT -1');
                console.log('Added cast_queue_position column to settings table');
            }

            // Update the current position indicator for the user
            const stmt = this.db.prepare(`
                UPDATE settings
                SET cast_queue_position = ?
                WHERE user_id = ?
            `);

            stmt.run(position, Number(userId));
            console.log(`Updated cast queue position to ${position} for user ${userId}`);
        } catch (error) {
            console.error(`Error updating cast queue position for user ${userId}:`, error);
            throw error;
        }
    }

    // Helper method to reorder the cast queue after removing an item
    private reorderCastQueue(userId: number): void {
        try {
            // Get all items in the queue
            const getStmt = this.db.prepare(`
                SELECT id
                FROM cast_queue
                WHERE user_id = ?
                ORDER BY position ASC
            `);

            const items = getStmt.all(Number(userId));

            // Update positions to be sequential
            const updateStmt = this.db.prepare(`
                UPDATE cast_queue
                SET position = ?
                WHERE id = ?
            `);

            this.db.transaction(() => {
                items.forEach((item, index) => {
                    updateStmt.run(index, item.id);
                });
            })();

            console.log(`Reordered cast queue for user ${userId}`);
        } catch (error) {
            console.error(`Error reordering cast queue for user ${userId}:`, error);
        }
    }

    async getVideosByGroup(groupId: number): Promise<Video[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM videos
            WHERE keyword_group_id = ?
            ORDER BY published_at DESC
        `);

        const videos = stmt.all(Number(groupId));
        return videos.map(video => ({
            id: video.video_id,
            url: `https://youtube.com/watch?v=${video.video_id}`,
            title: video.title,
            thumbnail: video.thumbnail,
            channelTitle: video.channel_title,
            viewCount: video.view_count,
            publishedAt: new Date(video.published_at),
            userId: video.user_id,
            keywordGroupId: video.keyword_group_id,
            // Include all metadata from VidIQ API
            description: video.description,
            channel_id: video.channel_id,
            vph: video.vph,
            tags: video.tags ? JSON.parse(video.tags) : [],
            matched_tags: video.matched_tags ? JSON.parse(video.matched_tags) : [],
            unmatched_tags: video.unmatched_tags ? JSON.parse(video.unmatched_tags) : [],
            seen_from: video.seen_from ? JSON.parse(video.seen_from) : [],
            related_to: video.related_to ? JSON.parse(video.related_to) : [],
            contentDetails: video.content_details ? JSON.parse(video.content_details) : null,
            statistics: video.statistics ? JSON.parse(video.statistics) : null
        }));
    }

    async refreshGroupVideos(groupId: number, userId: number): Promise<Video[]> {
        // Get the keyword group
        const group = await this.getKeywordGroup(groupId);
        if (!group) {
            throw new Error(`Keyword group with ID ${groupId} not found`);
        }

        // Get user settings for filters
        const settings = await this.getSettings(userId);
        if (!settings) {
            throw new Error(`Settings for user ID ${userId} not found`);
        }

        const headers = {
            'Accept': 'application/json, text/plain, */*',
            'Authorization': `Bearer ${process.env.VIDIQ_API_KEY || ''}`,
            'Referer': 'https://app.vidiq.com/',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
            'X-TimeZone': 'Asia/Calcutta',
            'X-Vidiq-Client': 'web 1a7277ec05e25b41808d7ed49f9a3f3c6ff86254',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'x-vidiq-auth': process.env.VIDIQ_API_KEY || ''
        };

        const fetchVideos = async (keyword: string) => {
            console.log(`Fetching videos for keyword: ${keyword}`);
            try {
                const response = await fetch(
                    `https://api.vidiq.com/v0/trendy?q[]=${encodeURIComponent(keyword)}&c=US&l=en&content_details=true`,
                    { headers }
                );

                if (!response.ok) {
                    console.error(`API request failed for ${keyword}:`, response.statusText);
                    throw new Error(`API request failed: ${response.statusText}`);
                }

                const data = await response.json();
                console.log(`Received ${data.videos?.length || 0} videos for keyword: ${keyword}`);
                return data.videos || [];
            } catch (error) {
                console.error(`Error fetching videos for ${keyword}:`, error);
                throw error;
            }
        };

        let allVideos: any[] = [];

        if (settings.parallelApiCalls) {
            const promises = group.keywords.map(keyword => fetchVideos(keyword));
            const results = await Promise.all(promises);
            allVideos = results.flat();
        } else {
            for (const keyword of group.keywords) {
                const videos = await fetchVideos(keyword);
                allVideos = allVideos.concat(videos);
            }
        }

        // Apply filters
        let filteredVideos = allVideos
            .filter(video => {
                const hoursElapsed = Math.max(1, (Date.now() - new Date(video.published_at).getTime()) / (1000 * 60 * 60));
                const viewsPerHour = video.views_count / hoursElapsed;
                return viewsPerHour >= settings.minViewsPerHour;
            })
            .filter(video => !group.excludeWords.some(word =>
                video.title.toLowerCase().includes(word.toLowerCase())
            ));

        if (settings.removeDuplicates) {
            const seen = new Set();
            filteredVideos = filteredVideos.filter(video => {
                if (seen.has(video.id)) return false;
                seen.add(video.id);
                return true;
            });
        }

        // Delete existing videos for this group
        const deleteStmt = this.db.prepare(`
            DELETE FROM videos
            WHERE keyword_group_id = ?
        `);
        deleteStmt.run(Number(groupId));

        // Insert new videos
        const insertStmt = this.db.prepare(`
            INSERT INTO videos (
                video_id, title, thumbnail, channel_title, view_count, published_at, user_id, keyword_group_id,
                description, channel_id, vph, tags, matched_tags, unmatched_tags, seen_from, related_to,
                content_details, statistics
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const processedVideos = filteredVideos.map(video => {
            // Insert into database
            insertStmt.run(
                video.id,
                video.title,
                `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`,
                video.channel_title,
                video.views_count,
                new Date(video.published_at).toISOString(),
                Number(userId),
                Number(groupId),
                video.description || null,
                video.channel_id || null,
                video.vph || null,
                JSON.stringify(video.tags || []),
                JSON.stringify(video.matched_tags || []),
                JSON.stringify(video.unmatched_tags || []),
                JSON.stringify(video.seen_from || []),
                JSON.stringify(video.related_to || []),
                JSON.stringify(video.content_details || null),
                JSON.stringify(video.statistics || null)
            );

            // Return processed video object with all metadata
            return {
                id: video.id,
                url: `https://youtube.com/watch?v=${video.id}`,
                title: video.title,
                thumbnail: `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`,
                channelTitle: video.channel_title,
                viewCount: video.views_count,
                publishedAt: new Date(video.published_at),
                userId: Number(userId),
                keywordGroupId: Number(groupId),
                // Pass all metadata from VidIQ API
                description: video.description,
                channel_id: video.channel_id,
                vph: video.vph,
                tags: video.tags || [],
                matched_tags: video.matched_tags || [],
                unmatched_tags: video.unmatched_tags || [],
                seen_from: video.seen_from || [],
                related_to: video.related_to || [],
                contentDetails: video.content_details,
                statistics: video.statistics
            };
        });

        // No duration fetching

        // Update the last refresh time for the group
        await this.updateKeywordGroup(groupId, {
            lastRefreshTime: new Date()
        });

        return processedVideos;
    }

    // YouTube channel methods
    async getYoutubeVideos(userId: number): Promise<YoutubeVideo[]> {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM youtube_videos
                WHERE user_id = ?
                ORDER BY published_at DESC
            `);
            const rows = stmt.all(userId);

            // Check if financial analysis columns exist
            const hasFinancialAnalysis = rows.length > 0 && 'financial_score' in rows[0];

            return rows.map(row => this.mapYoutubeVideoRow(row));
        } catch (error) {
            console.error(`SQLiteStorage: Error getting YouTube videos for user ${userId}:`, error);
            throw error;
        }
    }

    async getYoutubeVideosCount(userId: number): Promise<number> {
        try {
            const stmt = this.db.prepare(`
                SELECT COUNT(*) as count FROM youtube_videos
                WHERE user_id = ?
            `);
            const result = stmt.get(userId);


            return result.count;
        } catch (error) {
            console.error('Error counting YouTube videos:', error);
            return 0;
        }
    }

    async getYoutubeVideosPaginated(userId: number, offset: number, limit: number): Promise<YoutubeVideo[]> {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM youtube_videos
                WHERE user_id = ?
                ORDER BY published_at DESC
                LIMIT ? OFFSET ?
            `);
            const rows = stmt.all(userId, limit, offset);



            return rows.map(row => this.mapYoutubeVideoRow(row));
        } catch (error) {
            console.error('Error getting paginated YouTube videos:', error);
            return [];
        }
    }

    async getAllChannelVideosPaginated(
        userId: number,
        offset: number,
        limit: number,
        options: {
            sortBy?: string,
            searchQuery?: string,
            durationFilter?: string,
            channelId?: string
        } = {}
    ): Promise<{ videos: YoutubeVideo[], totalCount: number }> {
        try {


            const {
                sortBy = 'date_desc',
                searchQuery = '',
                durationFilter = 'all',
                channelId = null
            } = options;

            // Build the WHERE clause based on filters
            let whereClause = 'WHERE v.user_id = ?';
            const queryParams: any[] = [userId];

            // Add channel filter if provided
            if (channelId) {
                whereClause += ` AND v.channel_id = ?`;
                queryParams.push(channelId);

            }

            // Add search filter if provided
            if (searchQuery && searchQuery.trim() !== '') {
                whereClause += ` AND v.title LIKE ?`;
                queryParams.push(`%${searchQuery.trim()}%`);
            }

            // Transcript filter removed

            // Add duration filter if provided
            if (durationFilter === 'shorts') {
                whereClause += ` AND (v.duration IS NOT NULL AND v.duration < '00:05:00')`;
            } else if (durationFilter === 'videos') {
                whereClause += ` AND (v.duration IS NULL OR v.duration >= '00:05:00')`;
            }

            // Determine the ORDER BY clause based on sortBy and financialSortBy
            let orderByClause = '';

            // Default financialSortBy to 'none' if not provided
            const financialSortBy = options.financialSortBy || 'none';

            // First apply financial sort if specified
            if (financialSortBy !== 'none') {
                switch (financialSortBy) {
                    case 'financial_desc':
                        orderByClause = 'ORDER BY v.financial_score DESC NULLS LAST';
                        break;
                    case 'financial_asc':
                        orderByClause = 'ORDER BY v.financial_score ASC NULLS LAST';
                        break;
                    case 'benefit_amount_desc':
                        orderByClause = 'ORDER BY v.financial_amount DESC NULLS LAST';
                        break;
                    case 'benefit_amount_asc':
                        orderByClause = 'ORDER BY v.financial_amount ASC NULLS LAST';
                        break;
                    default:
                        orderByClause = 'ORDER BY v.published_at DESC';
                }
            } else {
                // Apply primary sort
                switch (sortBy) {
                    case 'date_desc':
                        orderByClause = 'ORDER BY v.published_at DESC';
                        break;
                    case 'date_asc':
                        orderByClause = 'ORDER BY v.published_at ASC';
                        break;
                    case 'views_desc':
                        orderByClause = 'ORDER BY v.view_count DESC NULLS LAST';
                        break;
                    case 'views_asc':
                        orderByClause = 'ORDER BY v.view_count ASC NULLS LAST';
                        break;
                    case 'title':
                        orderByClause = 'ORDER BY v.title ASC';
                        break;
                    case 'vph_desc':
                        // Calculate VPH in the query
                        orderByClause = 'ORDER BY (v.view_count / MAX(1, (strftime("%s", "now") - strftime("%s", v.published_at)) / 3600.0)) DESC NULLS LAST';
                        break;
                    case 'vph_asc':
                        // Calculate VPH in the query
                        orderByClause = 'ORDER BY (v.view_count / MAX(1, (strftime("%s", "now") - strftime("%s", v.published_at)) / 3600.0)) ASC NULLS LAST';
                        break;
                    case 'has_financial':
                        orderByClause = 'ORDER BY v.has_financial_analysis DESC';
                        break;
                    case 'no_financial':
                        orderByClause = 'ORDER BY v.has_financial_analysis ASC';
                        break;
                    case 'fresh_valuable':
                        // Sort by recency only (simplified)
                        orderByClause = 'ORDER BY v.published_at DESC';
                        break;
                    default:
                        orderByClause = 'ORDER BY v.published_at DESC';
                }
            }

            // First, get the total count of videos for this user with the applied filters
            const countQuery = `
                SELECT COUNT(*) as count
                FROM youtube_videos v
                ${whereClause}
            `;
            const countStmt = this.db.prepare(countQuery);
            const countResult = countStmt.get(...queryParams);
            const totalCount = countResult ? countResult.count : 0;



            // Then get the paginated videos with all filters and sorting applied
            const query = `
                SELECT v.*, c.channel_title as channel_display_name
                FROM youtube_videos v
                LEFT JOIN youtube_channels c ON v.channel_id = c.channel_id AND v.user_id = c.user_id
                ${whereClause}
                ${orderByClause}
                LIMIT ? OFFSET ?
            `;

            // Create a copy of queryParams for the main query
            const mainQueryParams = [...queryParams, limit, offset];

            const stmt = this.db.prepare(query);
            const rows = stmt.all(...mainQueryParams);



            // No longer falling back to a simpler query with default sort
            if (rows.length === 0 && totalCount > 0) {

            }

            // Map the rows to YoutubeVideo objects, using the channel_display_name if available
            const videos = rows.map(row => {
                const video = this.mapYoutubeVideoRow(row);
                // If we have a channel_display_name, use it instead of the channelTitle
                if (row.channel_display_name) {
                    video.channelTitle = row.channel_display_name;
                }
                return video;
            });

            return {
                videos,
                totalCount
            };
        } catch (error) {
            console.error('Error getting paginated videos from all channels:', error);
            return { videos: [], totalCount: 0 };
        }
    }

    async getWatchedVideos(userId: number): Promise<string[]> {
        try {
            const stmt = this.db.prepare(`
                SELECT watched_videos FROM settings
                WHERE user_id = ?
            `);
            const result = stmt.get(userId);

            if (!result || !result.watched_videos) {
                return [];
            }

            return JSON.parse(result.watched_videos || '[]');
        } catch (error) {
            console.error('Error getting watched videos:', error);
            return [];
        }
    }

    // Helper method to map database row to YoutubeVideo object
    private mapYoutubeVideoRow(row: any): YoutubeVideo {
        // Check if financial analysis columns exist
        const hasFinancialAnalysis = 'financial_score' in row;

        // Check if OpenRouter analysis columns exist
        const hasOpenRouterAnalysis = 'openrouter_model_used' in row;

        // Parse OpenRouter benefit amounts if they exist
        let openRouterBenefitAmounts = null;
        if (row.openrouter_benefit_amounts) {
            try {
                if (typeof row.openrouter_benefit_amounts === 'string') {
                    openRouterBenefitAmounts = JSON.parse(row.openrouter_benefit_amounts);
                } else {
                    openRouterBenefitAmounts = row.openrouter_benefit_amounts;
                }
            } catch (e) {
                openRouterBenefitAmounts = [];
            }
        }

        // Calculate views per hour if not already present
        let vph = row.vph;
        if (!vph && row.view_count && row.published_at) {
            try {
                const hoursElapsed = Math.max(1, (Date.now() - new Date(row.published_at).getTime()) / (1000 * 60 * 60));
                vph = row.view_count / hoursElapsed;
            } catch (e) {
                console.error('Error calculating VPH:', e);
            }
        }

        return {
            id: row.id,
            title: row.title,
            thumbnail: row.thumbnail,
            channelId: row.channel_id,
            channelTitle: row.channel_title,
            viewCount: row.view_count,
            publishedAt: new Date(row.published_at),
            userId: row.user_id,
            description: row.description,
            duration: row.duration,
            transcription: row.transcription,
            hasTranscription: Boolean(row.has_transcription),
            // Include VPH (Views Per Hour) for sorting
            vph: vph,
            // Include raw data field
            openRouterRawData: row.openrouter_raw_data || null,
            // Include financial analysis fields if they exist
            ...(hasFinancialAnalysis && {
                financialScore: row.financial_score,
                financialCategory: row.financial_category,
                financialAmount: row.financial_amount,
                financialTimeline: row.financial_timeline,
                financialRecipients: row.financial_recipients,
                financialSteps: row.financial_steps,
                financialViralPotential: row.financial_viral_potential,
                financialSkepticism: row.financial_skepticism,
                financialAnalysis: row.financial_analysis,
                financialTimestamps: row.financial_timestamps,
                hasFinancialAnalysis: Boolean(row.has_financial_analysis)
            }),

            // Include OpenRouter fields if they exist
            ...(hasOpenRouterAnalysis && {
                openRouterBenefitAmounts: openRouterBenefitAmounts || [],
                openRouterExpectedArrivalDate: row.openrouter_expected_arrival_date || '',
                openRouterEligiblePeople: row.openrouter_eligible_people || '',
                openRouterProofOrSource: row.openrouter_proof_or_source || '',
                openRouterActionsToClaim: row.openrouter_actions_to_claim || '',
                openRouterPriorityTag: row.openrouter_priority_tag || '',
                openRouterReasonForPriority: row.openrouter_reason_for_priority || '',
                openRouterViralPotential: row.openrouter_viral_potential || '',
                openRouterModelUsed: row.openrouter_model_used || '',
                openRouterPrompt: row.openrouter_prompt || '',
                openRouterSystemPrompt: row.openrouter_system_prompt || '',
                openRouterPromptName: row.openrouter_prompt_name || ''
            })
        };
    }

    async getYoutubeChannels(userId: number): Promise<YoutubeChannel[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM youtube_channels
            WHERE user_id = ?
            ORDER BY display_order ASC
        `);
        const rows = stmt.all(userId);
        return rows.map(row => ({
            id: row.id,
            userId: row.user_id,
            channelId: row.channel_id,
            channelTitle: row.channel_title,
            channelUrl: row.channel_url,
            thumbnail: row.thumbnail,
            description: row.description,
            subscriberCount: row.subscriber_count,
            lastRefreshTime: row.last_refresh_time ? new Date(row.last_refresh_time) : undefined,
            displayOrder: row.display_order,
            videoLimit: row.video_limit || 15
        }));
    }

    async getYoutubeChannel(channelId: number): Promise<YoutubeChannel | undefined> {
        const stmt = this.db.prepare(`
            SELECT * FROM youtube_channels
            WHERE id = ?
        `);
        const row = stmt.get(channelId);
        if (!row) return undefined;

        return {
            id: row.id,
            userId: row.user_id,
            channelId: row.channel_id,
            channelTitle: row.channel_title,
            channelUrl: row.channel_url,
            thumbnail: row.thumbnail,
            description: row.description,
            subscriberCount: row.subscriber_count,
            lastRefreshTime: row.last_refresh_time ? new Date(row.last_refresh_time) : undefined,
            displayOrder: row.display_order,
            videoLimit: row.video_limit || 15
        };
    }

    async createYoutubeChannel(userId: number, channel: Partial<YoutubeChannel>): Promise<YoutubeChannel> {
        // Get channel details from YouTube if not provided
        if (!channel.thumbnail || !channel.description || !channel.subscriberCount) {
            try {
                const channelDetails = await this.fetchChannelDetails(channel.channelUrl!);
                channel = {
                    ...channel,
                    ...channelDetails
                };
            } catch (error) {
                console.error("Error fetching channel details:", error);
                // Continue with available data
            }
        }

        const stmt = this.db.prepare(`
            INSERT INTO youtube_channels (
                user_id, channel_id, channel_title, channel_url, thumbnail,
                description, subscriber_count, last_refresh_time, display_order, video_limit
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const result = stmt.run(
            userId,
            channel.channelId!,
            channel.channelTitle!,
            channel.channelUrl!,
            channel.thumbnail || null,
            channel.description || null,
            channel.subscriberCount || 0,
            new Date().toISOString(),
            0,
            channel.videoLimit || 15
        );

        const newChannelId = result.lastInsertRowid as number;

        // Immediately fetch videos for this channel
        await this.refreshYoutubeChannelVideos(newChannelId);

        return await this.getYoutubeChannel(newChannelId) as YoutubeChannel;
    }

    async updateYoutubeChannel(channelId: number, updates: Partial<YoutubeChannel>): Promise<YoutubeChannel> {
        const channel = await this.getYoutubeChannel(channelId);
        if (!channel) {
            throw new Error("Channel not found");
        }

        // Build the update query dynamically based on provided fields
        const updateFields: string[] = [];
        const updateValues: any[] = [];

        if (updates.channelId !== undefined) {
            updateFields.push("channel_id = ?");
            updateValues.push(updates.channelId);
        }

        if (updates.channelTitle !== undefined) {
            updateFields.push("channel_title = ?");
            updateValues.push(updates.channelTitle);
        }

        if (updates.channelUrl !== undefined) {
            updateFields.push("channel_url = ?");
            updateValues.push(updates.channelUrl);
        }

        if (updates.thumbnail !== undefined) {
            updateFields.push("thumbnail = ?");
            updateValues.push(updates.thumbnail);
        }

        if (updates.videoLimit !== undefined) {
            updateFields.push("video_limit = ?");
            updateValues.push(updates.videoLimit);
        }

        if (updates.description !== undefined) {
            updateFields.push("description = ?");
            updateValues.push(updates.description);
        }

        if (updates.subscriberCount !== undefined) {
            updateFields.push("subscriber_count = ?");
            updateValues.push(updates.subscriberCount);
        }

        if (updates.displayOrder !== undefined) {
            updateFields.push("display_order = ?");
            updateValues.push(updates.displayOrder);
        }

        if (updateFields.length === 0) {
            return channel;
        }

        // Add channelId to values for WHERE clause
        updateValues.push(channelId);

        const stmt = this.db.prepare(`
            UPDATE youtube_channels
            SET ${updateFields.join(", ")}
            WHERE id = ?
        `);

        stmt.run(...updateValues);

        return await this.getYoutubeChannel(channelId) as YoutubeChannel;
    }

    async deleteYoutubeChannel(channelId: number): Promise<void> {
        const channel = await this.getYoutubeChannel(channelId);
        if (!channel) {
            throw new Error("Channel not found");
        }

        // First delete all videos associated with this channel
        const deleteVideosStmt = this.db.prepare(`
            DELETE FROM youtube_videos
            WHERE channel_id = ?
        `);
        deleteVideosStmt.run(channel.channelId);

        // Then delete the channel
        const deleteChannelStmt = this.db.prepare(`
            DELETE FROM youtube_channels
            WHERE id = ?
        `);
        deleteChannelStmt.run(channelId);
    }

    async deleteYoutubeChannelData(channelId: number): Promise<void> {
        // Delete all videos for this channel
        const channel = await this.getYoutubeChannel(channelId);
        if (!channel) {
            throw new Error("Channel not found");
        }

        const deleteVideosStmt = this.db.prepare(`
            DELETE FROM youtube_videos
            WHERE channel_id = ?
        `);
        deleteVideosStmt.run(channel.channelId);
    }

    async deleteYoutubeVideo(videoId: string): Promise<void> {
        try {
            console.log(`Deleting unavailable YouTube video: ${videoId}`);
            const deleteStmt = this.db.prepare(`
                DELETE FROM youtube_videos
                WHERE id = ?
            `);
            const result = deleteStmt.run(videoId);
            console.log(`Deleted YouTube video ${videoId}, rows affected: ${result.changes}`);
        } catch (error) {
            console.error(`Error deleting YouTube video ${videoId}:`, error);
            throw error;
        }
    }

    async updateYoutubeChannelLastRefreshTime(channelId: number): Promise<void> {
        // Update the channel's last refresh time
        const updateStmt = this.db.prepare(`
            UPDATE youtube_channels
            SET last_refresh_time = ?
            WHERE id = ?
        `);
        updateStmt.run(new Date().toISOString(), channelId);
    }

    async getYoutubeChannelVideos(channelId: number): Promise<YoutubeVideo[]> {
        const channel = await this.getYoutubeChannel(channelId);
        if (!channel) {
            return [];
        }

        const stmt = this.db.prepare(`
            SELECT * FROM youtube_videos
            WHERE channel_id = ?
            ORDER BY published_at DESC
        `);
        const rows = stmt.all(channel.channelId);

        // Check if financial analysis columns exist
        const hasFinancialAnalysis = rows.length > 0 && 'financial_score' in rows[0];

        return rows.map(row => ({
            id: row.id,
            title: row.title,
            channelId: row.channel_id,
            channelTitle: row.channel_title,
            publishedAt: new Date(row.published_at),
            thumbnail: row.thumbnail,
            description: row.description,
            hasTranscription: Boolean(row.has_transcription),
            hasFinancialAnalysis: Boolean(row.has_financial_analysis),
            duration: row.duration,
            viewCount: row.view_count,
            likeCount: row.like_count,
            vph: row.vph,
            userId: row.user_id,
            financialAmount: row.financial_amount,
            financialScore: row.financial_score,
            ollamaBenefitAmounts: row.ollama_benefit_amounts ? JSON.parse(row.ollama_benefit_amounts) : null,
            openRouterBenefitAmounts: row.openrouter_benefit_amounts ? JSON.parse(row.openrouter_benefit_amounts) : null,
            openRouterRawData: row.openrouter_raw_data,
            ollamaRawData: row.ollama_raw_data,
            ollamaModelUsed: row.ollama_model_used,
            openRouterModelUsed: row.openrouter_model_used
        }));
    }

    async getYoutubeChannelsCounts(userId: number): Promise<Record<string, number>> {
        // Get all channel IDs for this user
        const channels = await this.getYoutubeChannels(userId);
        if (channels.length === 0) {
            return {};
        }

        // Build a query to count videos for each channel in a single database operation
        const channelIds = channels.map(c => c.id);

        // Use a more efficient query that counts videos per channel in one go
        const stmt = this.db.prepare(`
            SELECT yc.id as channel_id, COUNT(yv.id) as video_count
            FROM youtube_channels yc
            LEFT JOIN youtube_videos yv ON yc.channel_id = yv.channel_id
            WHERE yc.user_id = ? AND yc.id IN (${channelIds.map(() => '?').join(',')})
            GROUP BY yc.id
        `);

        // Execute the query with all channel IDs
        const rows = stmt.all(userId, ...channelIds);

        // Convert the results to a record object
        const counts: Record<string, number> = {};
        rows.forEach(row => {
            counts[row.channel_id] = row.video_count;
        });

        // Make sure all channels have a count (even if 0)
        channels.forEach(channel => {
            if (counts[channel.id] === undefined) {
                counts[channel.id] = 0;
            }
        });

        return counts;
    }

    async refreshYoutubeChannelVideos(channelId: number): Promise<{ videos: YoutubeVideo[], videosNeedingAnalysis: string[] }> {
        const channel = await this.getYoutubeChannel(channelId);
        if (!channel) {
            throw new Error("Channel not found");
        }

        try {
            // Get user settings to check for current OpenRouter model
            let currentOpenRouterModel = 'google/gemini-2.0-flash-exp:free'; // Default model
            try {
                const settings = await this.getSettings(channel.userId);
                if (settings && settings.openRouterModel) {
                    currentOpenRouterModel = settings.openRouterModel;
                    console.log(`SQLiteStorage: Current OpenRouter model from settings: ${currentOpenRouterModel}`);
                }
            } catch (settingsError) {
                console.error('Error getting user settings for OpenRouter model:', settingsError);
            }

            // Fetch videos from YouTube
            const videos = await this.fetchChannelVideos(channel);

            // Get existing videos to preserve OpenRouter analysis data
            const existingVideosStmt = this.db.prepare(`
                SELECT * FROM youtube_videos
                WHERE channel_id = ?
            `);
            const existingVideosRows = existingVideosStmt.all(channel.channelId);

            // Create a map of existing videos for quick lookup
            const existingVideosMap = new Map();
            existingVideosRows.forEach(row => {
                existingVideosMap.set(row.id, row);
            });

            console.log(`Found ${existingVideosMap.size} existing videos for channel ${channel.channelTitle}`);

            // Get all video IDs from both newly fetched videos and existing videos in the database
            const newVideoIds = videos.map(v => v.id!);
            const existingVideoIds = Array.from(existingVideosMap.keys());

            // Combine both sets of IDs, removing duplicates
            const allVideoIds = [...new Set([...newVideoIds, ...existingVideoIds])];

            console.log(`Fetching view counts for all ${allVideoIds.length} videos (${newVideoIds.length} new, ${existingVideoIds.length} existing)`);

            try {
                // For the refreshYoutubeChannelVideos method, we want to get the current live viewer count for live videos
                // This ensures we show the current number of viewers for live streams
                // Use a larger batch size (10) for better performance
                const viewCounts = await batchFetchViewCounts(allVideoIds, 10, false);

                // Update view counts in the newly fetched video objects
                videos.forEach(video => {
                    if (video.id && viewCounts.has(video.id)) {
                        const viewCount = viewCounts.get(video.id);

                        // Check if this is a live video by looking at the title
                        const isLive = video.title && (
                            video.title.includes('🔴 LIVE') ||
                            video.title.includes('🔴LIVE') ||
                            video.title.includes('[LIVE]') ||
                            video.title.includes('(LIVE)') ||
                            video.title.includes('LIVE:') ||
                            video.title.includes('LIVE STREAM') ||
                            video.title.includes('LIVESTREAM') ||
                            video.title.includes('LIVE NOW') ||
                            video.title.includes('STREAMING NOW') ||
                            video.title.includes('STREAMING LIVE')
                        );

                        if (isLive) {
                            console.log(`Live video detected in refresh method: ${video.title} (ID: ${video.id}) - Current viewers: ${viewCount}`);
                        }

                        console.log(`Updated view count for video ${video.id} from ${video.viewCount || 0} to ${viewCount}`);
                        video.viewCount = viewCount;
                    }
                });

                // Update view counts for existing videos that weren't in the newly fetched list
                for (const videoId of existingVideoIds) {
                    // Skip videos that are already in the newly fetched list
                    if (newVideoIds.includes(videoId)) continue;

                    if (viewCounts.has(videoId)) {
                        const viewCount = viewCounts.get(videoId);
                        const existingVideo = existingVideosMap.get(videoId);

                        if (existingVideo) {
                            console.log(`Updating view count for existing video ${videoId} from ${existingVideo.view_count || 0} to ${viewCount}`);

                            // Update just the view count in the database
                            const updateStmt = this.db.prepare(`
                                UPDATE youtube_videos
                                SET view_count = ?
                                WHERE id = ?
                            `);

                            updateStmt.run(viewCount, videoId);
                        }
                    }
                }

                console.log(`Successfully updated view counts for ${allVideoIds.length} videos`);
            } catch (viewCountError) {
                console.error(`Error fetching view counts: ${viewCountError.message}`);
                // Continue with existing view counts
            }

            // Track videos that need AI analysis
            const videosNeedingAnalysis: string[] = [];

            // Save videos to database
            for (const video of videos) {
                // Check if video already exists
                const existingVideo = existingVideosMap.get(video.id);

                if (!existingVideo) {
                    // Insert new video
                    console.log(`Inserting new video: ${video.id} - ${video.title}`);
                    const insertStmt = this.db.prepare(`
                        INSERT INTO youtube_videos (
                            id, title, thumbnail, channel_id, channel_title,
                            view_count, published_at, user_id, description,
                            duration, has_transcription
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `);

                    insertStmt.run(
                        video.id,
                        video.title,
                        video.thumbnail,
                        video.channelId,
                        video.channelTitle,
                        video.viewCount || 0,
                        video.publishedAt.toISOString(),
                        channel.userId,
                        video.description || null,
                        video.duration || 'PT0S',
                        video.hasTranscription ? 1 : 0
                    );

                    // If the video has a transcription, add it to the list for OpenRouter analysis
                    if (video.hasTranscription) {
                        videosNeedingAnalysis.push(video.id);
                    }
                } else {
                    // Update existing video but preserve OpenRouter analysis data
                    console.log(`Updating existing video: ${video.id} - ${video.title}`);

                    // Check if this video has OpenRouter analysis data
                    // We need to check multiple fields to ensure we don't miss any videos that need analysis
                    const hasOpenRouterAnalysis = existingVideo.openrouter_model_used &&
                        (existingVideo.openrouter_priority_tag ||
                         existingVideo.openrouter_benefit_amounts ||
                         existingVideo.openrouter_raw_data);

                    // Log the benefit amounts for debugging
                    if (existingVideo.openrouter_benefit_amounts) {
                        console.log(`Video ${video.id} has OpenRouter benefit amounts: ${existingVideo.openrouter_benefit_amounts}`);
                    }

                    console.log(`Video ${video.id} has OpenRouter analysis: ${Boolean(hasOpenRouterAnalysis)}`);
                    console.log(`Video ${video.id} model: ${existingVideo.openrouter_model_used || 'none'}, priority tag: ${existingVideo.openrouter_priority_tag || 'none'}`);
                    console.log(`Video ${video.id} has transcription: ${Boolean(existingVideo.has_transcription)}`);

                    if (hasOpenRouterAnalysis) {
                        console.log(`Preserving OpenRouter analysis data for video: ${video.id}`);

                        // Check if we need to update the model name
                        if (existingVideo.openrouter_model_used && existingVideo.openrouter_model_used !== currentOpenRouterModel) {
                            console.log(`Updating OpenRouter model for video ${video.id} from ${existingVideo.openrouter_model_used} to ${currentOpenRouterModel}`);

                            // Update video with new model name, preserving benefit amounts
                            const updateStmt = this.db.prepare(`
                                UPDATE youtube_videos
                                SET title = ?,
                                    thumbnail = ?,
                                    channel_title = ?,
                                    view_count = ?,
                                    description = ?,
                                    openrouter_model_used = ?,
                                    openrouter_benefit_amounts = ?
                                WHERE id = ?
                            `);

                            updateStmt.run(
                                video.title,
                                video.thumbnail,
                                video.channelTitle,
                                video.viewCount || 0,
                                video.description || null,
                                currentOpenRouterModel,
                                existingVideo.openrouter_benefit_amounts,
                                video.id
                            );

                            // Also update the raw data if it exists
                            if (existingVideo.openrouter_raw_data) {
                                try {
                                    const rawData = JSON.parse(existingVideo.openrouter_raw_data);
                                    rawData.modelUsed = currentOpenRouterModel;

                                    const updateRawDataStmt = this.db.prepare(`
                                        UPDATE youtube_videos
                                        SET openrouter_raw_data = ?
                                        WHERE id = ?
                                    `);

                                    updateRawDataStmt.run(JSON.stringify(rawData), video.id);
                                    console.log(`Updated raw data model for video ${video.id}`);
                                } catch (error) {
                                    console.error(`Error updating raw data for video ${video.id}:`, error);
                                }
                            }
                        } else {
                            // Update video but preserve OpenRouter fields
                            const updateStmt = this.db.prepare(`
                                UPDATE youtube_videos
                                SET title = ?,
                                    thumbnail = ?,
                                    channel_title = ?,
                                    view_count = ?,
                                    description = ?,
                                    openrouter_benefit_amounts = ?
                                WHERE id = ?
                            `);

                            updateStmt.run(
                                video.title,
                                video.thumbnail,
                                video.channelTitle,
                                video.viewCount || 0,
                                video.description || null,
                                existingVideo.openrouter_benefit_amounts,
                                video.id
                            );
                        }
                    } else {
                        // If no OpenRouter analysis data, update basic fields and add to analysis list
                        // But still preserve any existing benefit amounts
                        const updateStmt = this.db.prepare(`
                            UPDATE youtube_videos
                            SET title = ?,
                                thumbnail = ?,
                                channel_title = ?,
                                view_count = ?,
                                description = ?,
                                openrouter_benefit_amounts = COALESCE(openrouter_benefit_amounts, ?)
                            WHERE id = ?
                        `);

                        updateStmt.run(
                            video.title,
                            video.thumbnail,
                            video.channelTitle,
                            video.viewCount || 0,
                            video.description || null,
                            existingVideo.openrouter_benefit_amounts || null,
                            video.id
                        );

                        // If the video has a transcription but no OpenRouter analysis, add it to the list
                        if (existingVideo.has_transcription) {
                            videosNeedingAnalysis.push(video.id);
                        }
                    }
                }
            }

            // Update channel's last refresh time
            const updateStmt = this.db.prepare(`
                UPDATE youtube_channels
                SET last_refresh_time = ?
                WHERE id = ?
            `);
            updateStmt.run(new Date().toISOString(), channelId);

            // Get the updated videos
            const updatedVideos = await this.getYoutubeChannelVideos(channelId);

            console.log(`Channel refresh complete. Found ${videosNeedingAnalysis.length} videos needing OpenRouter analysis`);

            return {
                videos: updatedVideos,
                videosNeedingAnalysis
            };
        } catch (error) {
            console.error("Error refreshing channel videos:", error);
            throw error;
        }
    }

    async getYoutubeVideo(videoId: string): Promise<YoutubeVideo | undefined> {
        try {
            const stmt = this.db.prepare(`
                SELECT * FROM youtube_videos
                WHERE id = ?
            `);
            const row = stmt.get(videoId);
            if (!row) {
                return undefined;
            }

            return {
                id: row.id,
                title: row.title,
                thumbnail: row.thumbnail,
                channelId: row.channel_id,
                channelTitle: row.channel_title,
                viewCount: row.view_count,
                publishedAt: new Date(row.published_at),
                userId: row.user_id,
                description: row.description,
                duration: row.duration,
                transcription: row.transcription,
                hasTranscription: Boolean(row.has_transcription),
                // Include financial analysis fields if they exist
                ...('financial_score' in row && {
                    financialScore: row.financial_score,
                    financialCategory: row.financial_category,
                    financialAmount: row.financial_amount,
                    financialTimeline: row.financial_timeline,
                    financialRecipients: row.financial_recipients,
                    financialSteps: row.financial_steps,
                    financialViralPotential: row.financial_viral_potential,
                    financialSkepticism: row.financial_skepticism,
                    financialAnalysis: row.financial_analysis,
                    financialTimestamps: row.financial_timestamps,
                    hasFinancialAnalysis: Boolean(row.has_financial_analysis),

                    openRouterRawData: row.openrouter_raw_data || '',
                    // Include OpenRouter fields if they exist
                    openRouterBenefitAmounts: (() => {
                        // Parse OpenRouter benefit amounts if they exist
                        if (row.openrouter_benefit_amounts) {
                            try {
                                if (typeof row.openrouter_benefit_amounts === 'string') {
                                    return JSON.parse(row.openrouter_benefit_amounts);
                                } else if (Array.isArray(row.openrouter_benefit_amounts)) {
                                    return row.openrouter_benefit_amounts;
                                }
                            } catch (e) {
                                // Try to extract benefit amounts from openrouter_raw_data as a fallback
                                if (row.openrouter_raw_data) {
                                    try {
                                        const rawData = JSON.parse(row.openrouter_raw_data);
                                        if (rawData.extractedInfo && rawData.extractedInfo.benefitAmounts &&
                                            Array.isArray(rawData.extractedInfo.benefitAmounts)) {
                                            return rawData.extractedInfo.benefitAmounts;
                                        }
                                    } catch (rawDataError) {
                                        // Silently fail and return empty array
                                    }
                                }
                            }
                        }
                        return [];
                    })()
                })
            };
        } catch (error) {
            console.error(`SQLiteStorage: Error getting YouTube video ${videoId}:`, error);
            throw error;
        }
    }

    async updateYoutubeVideoTranscription(videoId: string, transcription: string): Promise<YoutubeVideo> {
        try {
            // First, check if the financial_score column exists
            const tableInfo = this.db.prepare('PRAGMA table_info(youtube_videos)').all();
            const hasFinancialScore = tableInfo.some(column => column.name === 'financial_score');

            // If financial analysis columns don't exist, add them
            if (!hasFinancialScore) {
                try {
                    // Add columns one by one to avoid SQLite limitations
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN financial_score REAL DEFAULT 0');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN financial_category TEXT DEFAULT NULL');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN financial_amount TEXT DEFAULT NULL');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN financial_timeline TEXT DEFAULT NULL');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN financial_recipients TEXT DEFAULT NULL');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN financial_steps TEXT DEFAULT NULL');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN financial_viral_potential TEXT DEFAULT NULL');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN financial_skepticism TEXT DEFAULT NULL');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN financial_analysis TEXT DEFAULT NULL');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN financial_timestamps TEXT DEFAULT NULL');
                    this.db.exec('ALTER TABLE youtube_videos ADD COLUMN has_financial_analysis BOOLEAN DEFAULT 0');
                } catch (error) {
                    console.error('SQLiteStorage: Error adding financial analysis columns:', error);
                }
            }

            const stmt = this.db.prepare(`
                UPDATE youtube_videos
                SET transcription = ?, has_transcription = 1
                WHERE id = ?
            `);
            stmt.run(transcription, videoId);

            const video = await this.getYoutubeVideo(videoId) as YoutubeVideo;
            return video;
        } catch (error) {
            console.error(`SQLiteStorage: Error updating transcription for video ${videoId}:`, error);
            throw error;
        }
    }

    // Get table info for a specific table
    async getTableInfo(tableName: string): Promise<{name: string, type: string}[]> {
        try {
            const tableInfo = this.db.prepare(`PRAGMA table_info(${tableName})`).all();
            return tableInfo;
        } catch (error) {
            console.error(`SQLiteStorage: Error getting table info for ${tableName}:`, error);
            return [];
        }
    }

    // Get table info for a specific table
    async getTableInfo(tableName: string): Promise<{name: string, type: string}[]> {
        try {
            const tableInfo = this.db.prepare(`PRAGMA table_info(${tableName})`).all();
            return tableInfo;
        } catch (error) {
            console.error(`SQLiteStorage: Error getting table info for ${tableName}:`, error);
            return [];
        }
    }

    async updateYoutubeVideoFinancialAnalysis(videoId: string, analysis: {
        financialScore: number;
        financialCategory: string;
        financialAmount: string;
        financialTimeline: string;
        financialRecipients: string;
        financialSteps: string;
        financialViralPotential: string;
        financialSkepticism: string;
        financialAnalysis: string;
        financialTimestamps: string;
        hasFinancialAnalysis: boolean;

        // OpenRouter fields (optional)
        openRouterBenefitAmounts?: string[];
        openRouterPromptName?: string;
        // Raw data (optional)
        openRouterRawData?: string;
    }): Promise<YoutubeVideo> {
        console.log(`\n==== TERMINAL LOG: UPDATING FINANCIAL ANALYSIS for video ${videoId} ====`);
        try {
            // Log the input analysis data
            console.log(`TERMINAL LOG: Financial score:`, analysis.financialScore);
            console.log(`TERMINAL LOG: Financial category:`, analysis.financialCategory);
            console.log(`TERMINAL LOG: Has financial analysis:`, analysis.hasFinancialAnalysis);

            // Log OpenRouter benefit amounts
            if (analysis.openRouterBenefitAmounts) {
                console.log(`TERMINAL LOG: OpenRouter benefit amounts provided:`, analysis.openRouterBenefitAmounts);
                console.log(`TERMINAL LOG: OpenRouter benefit amounts type:`, typeof analysis.openRouterBenefitAmounts);
                console.log(`TERMINAL LOG: OpenRouter benefit amounts is array:`, Array.isArray(analysis.openRouterBenefitAmounts));
                if (Array.isArray(analysis.openRouterBenefitAmounts)) {
                    console.log(`TERMINAL LOG: OpenRouter benefit amounts length:`, analysis.openRouterBenefitAmounts.length);
                }
            } else {
                console.log(`TERMINAL LOG: No OpenRouter benefit amounts provided`);
            }

            // Log OpenRouter raw data
            if (analysis.openRouterRawData) {
                console.log(`TERMINAL LOG: OpenRouter raw data provided, length:`, analysis.openRouterRawData.length);
            } else {
                console.log(`TERMINAL LOG: No OpenRouter raw data provided`);
            }

            // Prepare the benefit amounts for storage - use a single source of truth
            let benefitAmountsJson = null;

            // First try to get benefit amounts from OpenRouter (preferred)
            if (analysis.openRouterBenefitAmounts) {
                console.log(`\n==== TERMINAL LOG: PARSING OPENROUTER BENEFIT AMOUNTS for video ${videoId} ====`);
                // Handle different formats of openRouterBenefitAmounts
                let benefitAmounts = analysis.openRouterBenefitAmounts;
                console.log(`TERMINAL LOG: Initial benefit amounts:`, benefitAmounts);
                console.log(`TERMINAL LOG: Initial benefit amounts type:`, typeof benefitAmounts);
                console.log(`TERMINAL LOG: Initial benefit amounts is array:`, Array.isArray(benefitAmounts));

                if (typeof benefitAmounts === 'string') {
                    console.log(`TERMINAL LOG: Attempting to parse benefit amounts from string`);
                    try {
                        // Try to parse it as JSON first
                        benefitAmounts = JSON.parse(benefitAmounts);
                        console.log(`TERMINAL LOG: Successfully parsed benefit amounts from string:`, benefitAmounts);
                        console.log(`TERMINAL LOG: Parsed benefit amounts type:`, typeof benefitAmounts);
                        console.log(`TERMINAL LOG: Parsed benefit amounts is array:`, Array.isArray(benefitAmounts));
                    } catch (e) {
                        // If it's not valid JSON, treat it as a single string
                        console.log(`TERMINAL LOG: Failed to parse as JSON, treating as single string:`, e.message);
                        benefitAmounts = [benefitAmounts];
                        console.log(`TERMINAL LOG: Converted to array:`, benefitAmounts);
                    }
                } else if (!Array.isArray(benefitAmounts)) {
                    console.log(`TERMINAL LOG: Benefit amounts is not an array or string, converting to empty array`);
                    benefitAmounts = [];
                }

                // Only store if we have actual benefit amounts
                if (Array.isArray(benefitAmounts) && benefitAmounts.length > 0) {
                    console.log(`TERMINAL LOG: Saving OpenRouter benefit amounts for video ${videoId}:`, benefitAmounts);
                    console.log(`TERMINAL LOG: Benefit amounts length:`, benefitAmounts.length);
                    benefitAmountsJson = JSON.stringify(benefitAmounts);
                    console.log(`TERMINAL LOG: Stringified benefit amounts:`, benefitAmountsJson);
                } else {
                    console.log(`TERMINAL LOG: No valid OpenRouter benefit amounts for video ${videoId}`);
                    benefitAmountsJson = null;
                }
            }

            // If no benefit amounts provided, check if we can extract from raw data
            else if (analysis.openRouterRawData) {
                try {
                    const rawData = JSON.parse(analysis.openRouterRawData);
                    if (rawData.extractedInfo && rawData.extractedInfo.benefitAmounts &&
                        Array.isArray(rawData.extractedInfo.benefitAmounts) &&
                        rawData.extractedInfo.benefitAmounts.length > 0) {
                        console.log(`SQLiteStorage: Extracted benefit amounts from OpenRouter raw data for video ${videoId}:`, rawData.extractedInfo.benefitAmounts);
                        benefitAmountsJson = JSON.stringify(rawData.extractedInfo.benefitAmounts);
                    }
                } catch (error) {
                    console.error(`SQLiteStorage: Error extracting benefit amounts from OpenRouter raw data for video ${videoId}:`, error);
                }
            }


            // Store the benefit amounts
            const openRouterBenefitAmountsJson = benefitAmountsJson;

            // Build the SQL statement and parameters dynamically
            const columns = [
                'financial_score', 'financial_category', 'financial_amount',
                'financial_timeline', 'financial_recipients', 'financial_steps',
                'financial_viral_potential', 'financial_skepticism', 'financial_analysis',
                'financial_timestamps', 'has_financial_analysis'
            ];

            const values = [
                analysis.financialScore,
                analysis.financialCategory,
                analysis.financialAmount,
                analysis.financialTimeline,
                analysis.financialRecipients,
                analysis.financialSteps,
                analysis.financialViralPotential,
                analysis.financialSkepticism,
                analysis.financialAnalysis,
                analysis.financialTimestamps,
                analysis.hasFinancialAnalysis ? 1 : 0
            ];

            // Check for database columns
            const tableInfo = await this.getTableInfo('youtube_videos');

            // Check for OpenRouter columns
            const hasOpenRouterRawData = tableInfo.some(column => column.name === 'openrouter_raw_data');
            const hasOpenRouterBenefitAmounts = tableInfo.some(column => column.name === 'openrouter_benefit_amounts');
            const hasOpenRouterPromptName = tableInfo.some(column => column.name === 'openrouter_prompt_name');

            // Update columns as needed

            // Get the raw data
            const rawData = analysis.openRouterRawData || null;

            // Store only in openrouter_raw_data field
            if (hasOpenRouterRawData && rawData) {
                columns.push('openrouter_raw_data');
                values.push(rawData);
            }

            // Store OpenRouter benefit amounts if column exists
            if (hasOpenRouterBenefitAmounts) {
                columns.push('openrouter_benefit_amounts');
                values.push(benefitAmountsJson); // Use the same benefit amounts for consistency
            }

            // Store OpenRouter prompt name if column exists
            if (hasOpenRouterPromptName && analysis.openRouterPromptName) {
                columns.push('openrouter_prompt_name');
                values.push(analysis.openRouterPromptName || null);
            }


            // Log the columns and values for debugging
            console.log(`\n==== TERMINAL LOG: BUILDING SQL UPDATE for video ${videoId} ====`);
            console.log(`TERMINAL LOG: Updating ${columns.length} columns for video ${videoId}`);
            console.log(`TERMINAL LOG: Benefit amounts JSON to store:`, benefitAmountsJson);

            // Build the SQL statement with explicit placeholders
            const placeholders = columns.map(() => '?');
            const setClause = columns.map((col, index) => `${col} = ${placeholders[index]}`).join(',\n                ');
            const sql = `
                UPDATE youtube_videos
                SET ${setClause}
                WHERE id = ?
            `;

            // Add the video ID as the last parameter
            values.push(videoId);

            // Log the SQL and values for debugging
            console.log(`TERMINAL LOG: SQL statement: ${sql}`);
            console.log(`TERMINAL LOG: Values count: ${values.length}`);
            console.log(`TERMINAL LOG: OpenRouter benefit amounts value:`, values[values.findIndex((_, i) => columns[i] === 'openrouter_benefit_amounts')]);

            let result;
            try {
                // Prepare and execute the statement
                console.log(`\n==== TERMINAL LOG: EXECUTING SQL UPDATE for video ${videoId} ====`);
                const stmt = this.db.prepare(sql);
                result = stmt.run(...values);
                console.log(`TERMINAL LOG: Update successful, rows affected:`, result.changes);
            } catch (sqlError) {
                console.error(`SQLiteStorage: SQL error:`, sqlError);
                console.error(`SQLiteStorage: Attempting alternative update approach`);

                // Try an alternative approach with named parameters
                const namedParams = {};
                columns.forEach((col, index) => {
                    namedParams[col] = values[index];
                });
                namedParams['id'] = videoId;

                const namedSetClause = columns.map(col => `${col} = @${col}`).join(',\n                ');
                const namedSql = `
                    UPDATE youtube_videos
                    SET ${namedSetClause}
                    WHERE id = @id
                `;

                const namedStmt = this.db.prepare(namedSql);
                result = namedStmt.run(namedParams);
                console.log(`SQLiteStorage: Alternative update successful`);
            }

            console.log(`\n==== TERMINAL LOG: UPDATE COMPLETED for video ${videoId} ====`);
            console.log(`TERMINAL LOG: Financial analysis update result:`, result.changes, 'rows affected');
            console.log(`TERMINAL LOG: Financial analysis parameters:`, {
                score: analysis.financialScore,
                category: analysis.financialCategory,
                hasAnalysis: analysis.hasFinancialAnalysis
            });

            const video = await this.getYoutubeVideo(videoId) as YoutubeVideo;
            console.log(`\n==== TERMINAL LOG: RETRIEVED UPDATED VIDEO for video ${videoId} ====`);
            console.log(`TERMINAL LOG: Has financial analysis:`, video.hasFinancialAnalysis);
            console.log(`TERMINAL LOG: Financial score:`, video.financialScore);
            console.log(`TERMINAL LOG: Financial category:`, video.financialCategory);
            console.log(`TERMINAL LOG: OpenRouter benefit amounts:`, video.openRouterBenefitAmounts);
            console.log(`TERMINAL LOG: OpenRouter benefit amounts type:`, typeof video.openRouterBenefitAmounts);
            console.log(`TERMINAL LOG: OpenRouter benefit amounts is array:`, Array.isArray(video.openRouterBenefitAmounts));
            if (Array.isArray(video.openRouterBenefitAmounts)) {
                console.log(`TERMINAL LOG: OpenRouter benefit amounts length:`, video.openRouterBenefitAmounts.length);
            }
            return video;
        } catch (error) {
            console.error(`SQLiteStorage: Error updating financial analysis for video ${videoId}:`, error);
            throw error;
        }
    }

    // Helper functions to fetch data from YouTube without using the API
    private async fetchChannelDetails(channelUrl: string): Promise<Partial<YoutubeChannel>> {
        try {
            const response = await axios.get(channelUrl);
            const $ = cheerio.load(response.data);

            // Extract channel details
            const thumbnail = $('meta[property="og:image"]').attr('content') || '';
            const description = $('meta[property="og:description"]').attr('content') || '';

            // Try to extract subscriber count (this is challenging without the API)
            let subscriberCount = 0;
            const subscriberText = $('span[id="subscriber-count"]').text();
            if (subscriberText) {
                // Parse subscriber count from text like "1.2M subscribers"
                const match = subscriberText.match(/([0-9.]+)([KMB]?)/);
                if (match) {
                    const num = parseFloat(match[1]);
                    const unit = match[2];

                    if (unit === 'K') subscriberCount = Math.round(num * 1000);
                    else if (unit === 'M') subscriberCount = Math.round(num * 1000000);
                    else if (unit === 'B') subscriberCount = Math.round(num * 1000000000);
                    else subscriberCount = Math.round(num);
                }
            }

            return {
                thumbnail,
                description,
                subscriberCount
            };
        } catch (error) {
            console.error("Error fetching channel details:", error);
            return {};
        }
    }

    private async fetchChannelVideos(channel: YoutubeChannel): Promise<Partial<YoutubeVideo>[]> {
        try {
            // Use RSS feed to get recent videos (this is more reliable than scraping)
            const channelId = channel.channelId;

            // Make sure the channel ID is in the correct format
            // YouTube channel IDs typically start with 'UC'
            if (!channelId.startsWith('UC')) {
                console.warn(`Channel ID ${channelId} doesn't start with UC, which might cause issues`);
            }

            // Try different RSS feed formats
            let response;
            let rssUrl;

            try {
                // First try the channel_id format
                rssUrl = `https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`;
                response = await axios.get(rssUrl);
            } catch (error) {
                console.warn(`Failed to fetch RSS feed with channel_id=${channelId}, trying user format`);
                // If that fails, try the user format
                try {
                    rssUrl = `https://www.youtube.com/feeds/videos.xml?user=${channel.channelTitle}`;
                    response = await axios.get(rssUrl);
                } catch (userError) {
                    console.error(`Failed to fetch RSS feed with user=${channel.channelTitle}`);
                    throw error; // Throw the original error
                }
            }

            const $ = cheerio.load(response.data, { xmlMode: true });
            const videos: Partial<YoutubeVideo>[] = [];

            // Get the video limit from the channel settings
            const videoLimit = channel.videoLimit || 15;
            console.log(`Fetching up to ${videoLimit} videos for channel ${channel.channelTitle}`);

            // Counter for videos processed
            let videoCount = 0;

            $('entry').each((i, entry) => {
                // Check if we've reached the limit
                if (videoCount >= videoLimit) {
                    return false; // Stop processing more entries
                }

                const $entry = $(entry);

                // Extract video ID from the yt:videoId tag
                const videoId = $entry.find('yt\\:videoId').text();
                if (!videoId) {
                    console.warn('Could not find video ID in entry, skipping');
                    return;
                }

                // Extract other video details
                const title = $entry.find('title').text();
                const publishedAt = new Date($entry.find('published').text());
                const channelTitle = $entry.find('author > name').text() || channel.channelTitle;

                // Try to extract thumbnail URL from media:group or media:thumbnail tags
                let thumbnail = '';

                // First try to get the thumbnail from media:group > media:thumbnail
                const mediaThumbnail = $entry.find('media\\:group > media\\:thumbnail').attr('url');
                if (mediaThumbnail) {
                    thumbnail = mediaThumbnail;
                } else {
                    // Then try to get it from media:thumbnail directly
                    const directThumbnail = $entry.find('media\\:thumbnail').attr('url');
                    if (directThumbnail) {
                        thumbnail = directThumbnail;
                    } else {
                        // Fallback to the standard YouTube thumbnail URL format
                        thumbnail = `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;
                    }
                }

                // Get video description
                const description = $entry.find('media\\:description').text() || $entry.find('media\\:group > media\\:description').text() || $entry.find('content').text();

                videos.push({
                    id: videoId,
                    title,
                    thumbnail,
                    channelId: channel.channelId,
                    channelTitle,
                    publishedAt,
                    description,
                    userId: channel.userId,
                    viewCount: 0, // We'll fetch this later
                    duration: 'PT0S', // We can't get this from RSS
                    hasTranscription: false
                });

                videoCount++;
            });

            // If we got videos, update the channel title if it was just an ID before
            if (videos.length > 0 && channel.channelTitle === channel.channelId) {
                const actualChannelTitle = videos[0].channelTitle;
                if (actualChannelTitle && actualChannelTitle !== channel.channelTitle) {
                    // Update the channel title
                    const updateStmt = this.db.prepare(`
                        UPDATE youtube_channels
                        SET channel_title = ?
                        WHERE id = ?
                    `);
                    updateStmt.run(actualChannelTitle, channel.id);
                }
            }

            // Fetch view counts for all videos
            if (videos.length > 0) {
                console.log(`Fetching view counts for ${videos.length} videos`);
                try {
                    const videoIds = videos.map(video => video.id as string);

                    // For the private fetchChannelVideos method, we want to get the current live viewer count for live videos
                    // This ensures we show the current number of viewers for live streams
                    const viewCounts = await batchFetchViewCounts(videoIds, 5, false);

                    // Update view counts in the videos array
                    videos.forEach(video => {
                        if (video.id && viewCounts.has(video.id)) {
                            const viewCount = viewCounts.get(video.id) || 0;

                            // Check if this is a live video by looking at the title
                            const isLive = video.title && (
                                video.title.includes('🔴 LIVE') ||
                                video.title.includes('🔴LIVE') ||
                                video.title.includes('[LIVE]') ||
                                video.title.includes('(LIVE)') ||
                                video.title.includes('LIVE:') ||
                                video.title.includes('LIVE STREAM') ||
                                video.title.includes('LIVESTREAM') ||
                                video.title.includes('LIVE NOW') ||
                                video.title.includes('STREAMING NOW') ||
                                video.title.includes('STREAMING LIVE')
                            );

                            if (isLive) {
                                console.log(`Live video detected in private method: ${video.title} (ID: ${video.id}) - Current viewers: ${viewCount}`);
                            }

                            video.viewCount = viewCount;
                        }
                    });

                    console.log(`Successfully fetched view counts for ${videos.length} videos`);
                } catch (viewCountError) {
                    console.error("Error fetching view counts:", viewCountError);
                    // Continue with zero view counts
                }
            }

            return videos;
        } catch (error) {
            console.error("Error fetching channel videos:", error);
            return [];
        }
    }

    // TXT Tab methods
    async createTxtTranscript(userId: number, data: { title: string; content: string; videoUrl?: string; sourceType?: string; sourceId?: string }): Promise<number> {
        const now = Date.now();
        const stmt = this.db.prepare(`
            INSERT INTO txt_transcripts (user_id, title, content, video_url, source_type, source_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);
        const result = stmt.run(userId, data.title, data.content, data.videoUrl || null, data.sourceType || 'manual', data.sourceId || null, now, now);
        return result.lastInsertRowid as number;
    }

    async getTxtTranscripts(userId: number): Promise<any[]> {
        const stmt = this.db.prepare(`
            SELECT t.*,
                   COUNT(h.id) as highlight_count
            FROM txt_transcripts t
            LEFT JOIN txt_highlights h ON t.id = h.transcript_id
            WHERE t.user_id = ?
            GROUP BY t.id
            ORDER BY t.updated_at DESC
        `);
        const rows = stmt.all(userId);
        return rows.map(row => ({
            ...row,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at),
            highlightCount: row.highlight_count
        }));
    }

    async getTxtTranscript(id: number, userId: number): Promise<any | null> {
        const stmt = this.db.prepare(`
            SELECT * FROM txt_transcripts
            WHERE id = ? AND user_id = ?
        `);
        const row = stmt.get(id, userId);
        if (!row) return null;

        return {
            ...row,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        };
    }

    async updateTxtTranscript(id: number, userId: number, data: { title?: string; content?: string; videoUrl?: string }): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.title !== undefined) {
            updates.push('title = ?');
            values.push(data.title);
        }
        if (data.content !== undefined) {
            updates.push('content = ?');
            values.push(data.content);
        }
        if (data.videoUrl !== undefined) {
            updates.push('video_url = ?');
            values.push(data.videoUrl);
        }

        if (updates.length === 0) return false;

        updates.push('updated_at = ?');
        values.push(Date.now());
        values.push(id, userId);

        const stmt = this.db.prepare(`
            UPDATE txt_transcripts
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTxtTranscript(id: number, userId: number): Promise<boolean> {
        const stmt = this.db.prepare(`
            DELETE FROM txt_transcripts
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    // TXT Highlights methods
    async createTxtHighlight(userId: number, data: { transcriptId: number; startPos: number; endPos: number; color: string; note?: string }): Promise<number> {
        const stmt = this.db.prepare(`
            INSERT INTO txt_highlights (user_id, transcript_id, start_pos, end_pos, color, note, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);
        const result = stmt.run(userId, data.transcriptId, data.startPos, data.endPos, data.color, data.note || null, Date.now());
        return result.lastInsertRowid as number;
    }

    async getTxtHighlights(transcriptId: number, userId: number): Promise<any[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM txt_highlights
            WHERE transcript_id = ? AND user_id = ?
            ORDER BY start_pos ASC
        `);
        const rows = stmt.all(transcriptId, userId);
        return rows.map(row => ({
            ...row,
            createdAt: new Date(row.created_at)
        }));
    }

    async deleteTxtHighlight(id: number, userId: number): Promise<boolean> {
        const stmt = this.db.prepare(`
            DELETE FROM txt_highlights
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    // TXT Prompts methods
    async createTxtPrompt(userId: number, data: { name: string; content: string; category?: string }): Promise<number> {
        const now = Date.now();
        const stmt = this.db.prepare(`
            INSERT INTO txt_prompts (user_id, name, content, category, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        `);
        const result = stmt.run(userId, data.name, data.content, data.category || 'general', now, now);
        return result.lastInsertRowid as number;
    }

    async getTxtPrompts(userId: number): Promise<any[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM txt_prompts
            WHERE user_id = ?
            ORDER BY is_default DESC, category ASC, name ASC
        `);
        const rows = stmt.all(userId);
        return rows.map(row => ({
            ...row,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        }));
    }

    async updateTxtPrompt(id: number, userId: number, data: { name?: string; content?: string; category?: string }): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.name !== undefined) {
            updates.push('name = ?');
            values.push(data.name);
        }
        if (data.content !== undefined) {
            updates.push('content = ?');
            values.push(data.content);
        }
        if (data.category !== undefined) {
            updates.push('category = ?');
            values.push(data.category);
        }

        if (updates.length === 0) return false;

        updates.push('updated_at = ?');
        values.push(Date.now());
        values.push(id, userId);

        const stmt = this.db.prepare(`
            UPDATE txt_prompts
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTxtPrompt(id: number, userId: number): Promise<boolean> {
        const stmt = this.db.prepare(`
            DELETE FROM txt_prompts
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    // Method to get all existing transcripts from various sources
    async getAllExistingTranscripts(userId: number): Promise<any[]> {
        const transcripts: any[] = [];

        // Get YouTube video transcripts
        const youtubeStmt = this.db.prepare(`
            SELECT id, title, transcription as content, published_at, 'youtube' as source_type,
                   'https://www.youtube.com/watch?v=' || id as video_url
            FROM youtube_videos
            WHERE user_id = ? AND has_transcription = 1 AND transcription IS NOT NULL
            ORDER BY published_at DESC
        `);
        const youtubeRows = youtubeStmt.all(userId);
        transcripts.push(...youtubeRows.map(row => ({
            ...row,
            publishedAt: new Date(row.published_at),
            sourceType: 'youtube'
        })));

        // Get YTR search results with transcripts (if they exist)
        const ytrStmt = this.db.prepare(`
            SELECT video_id as id, title, description as content, published_at, 'ytr' as source_type,
                   'https://www.youtube.com/watch?v=' || video_id as video_url
            FROM ytr_search_results
            WHERE user_id = ? AND description IS NOT NULL
            ORDER BY published_at DESC
        `);
        const ytrRows = ytrStmt.all(userId);
        transcripts.push(...ytrRows.map(row => ({
            ...row,
            publishedAt: new Date(row.published_at),
            sourceType: 'ytr'
        })));

        return transcripts;
    }

    // Add missing default prompts to existing users
    private addMissingDefaultPrompts() {
        const newPrompts = [
            {
                name: 'SlickV1 - Financial Content',
                category: 'content-research',
                content: 'Transform this content into a SlickV1 financial analysis with urgent, direct tone and empathetic language:\n\n**URGENT FINANCIAL INSIGHT**\n\nI understand you\'re looking for actionable financial guidance in these uncertain times. Here\'s what you need to know RIGHT NOW:\n\n🚨 **IMMEDIATE ACTION REQUIRED:**\n[Extract 3 most urgent points from content]\n\n💡 **KEY INSIGHTS:**\n[Main financial insights from content]\n\n📊 **WHAT THIS MEANS FOR YOU:**\n[Personal impact analysis]\n\n⚡ **NEXT STEPS:**\n1. [Immediate action]\n2. [Short-term strategy]\n3. [Long-term consideration]\n\n💰 **BOTTOM LINE:**\n[Clear, actionable conclusion]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Content Research - Trend Analysis',
                category: 'content-research',
                content: 'Analyze this content for trending topics and viral potential:\n\n**TREND ANALYSIS REPORT**\n\n🔥 **TRENDING ELEMENTS:**\n- [Identify trending topics/keywords]\n- [Viral potential score: 1-10]\n- [Peak engagement timeframe]\n\n📈 **CONTENT OPTIMIZATION:**\n- [Hook improvements]\n- [Engagement triggers]\n- [Call-to-action suggestions]\n\n🎯 **TARGET AUDIENCE:**\n- [Primary demographics]\n- [Interest alignment]\n- [Engagement patterns]\n\n⚡ **ACTIONABLE INSIGHTS:**\n- [Content adaptation strategies]\n- [Platform-specific optimizations]\n- [Timing recommendations]\n\nContent:\n{transcript}'
            },
            {
                name: 'Content Research - Competitor Analysis',
                category: 'content-research',
                content: 'Perform competitive content analysis and positioning strategy:\n\n**COMPETITIVE INTELLIGENCE**\n\n🎯 **CONTENT POSITIONING:**\n- [Unique angle identification]\n- [Differentiation opportunities]\n- [Market gap analysis]\n\n📊 **PERFORMANCE INDICATORS:**\n- [Engagement potential]\n- [Shareability factors]\n- [Retention elements]\n\n🚀 **OPTIMIZATION STRATEGY:**\n- [Content enhancement suggestions]\n- [Format recommendations]\n- [Distribution tactics]\n\n💡 **COMPETITIVE ADVANTAGE:**\n- [Unique value propositions]\n- [Content superiority factors]\n- [Market positioning]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Content Research - Audience Insights',
                category: 'content-research',
                content: 'Extract deep audience insights and engagement strategies:\n\n**AUDIENCE INTELLIGENCE REPORT**\n\n👥 **AUDIENCE PROFILE:**\n- [Demographics and psychographics]\n- [Pain points and desires]\n- [Content consumption patterns]\n\n🧠 **PSYCHOLOGICAL TRIGGERS:**\n- [Emotional hooks]\n- [Motivation drivers]\n- [Decision-making factors]\n\n📱 **ENGAGEMENT STRATEGY:**\n- [Platform-specific adaptations]\n- [Optimal posting times]\n- [Content format preferences]\n\n🎯 **CONVERSION OPTIMIZATION:**\n- [Call-to-action placement]\n- [Trust-building elements]\n- [Value proposition clarity]\n\nContent:\n{transcript}'
            },
            {
                name: 'Content Research - Viral Potential',
                category: 'content-research',
                content: 'Assess and optimize content for maximum viral potential:\n\n**VIRAL POTENTIAL ASSESSMENT**\n\n🚀 **VIRALITY SCORE:** [Rate 1-10 with explanation]\n\n🔥 **VIRAL ELEMENTS:**\n- [Shareability factors]\n- [Emotional triggers]\n- [Controversy/debate potential]\n- [Relatability quotient]\n\n📈 **OPTIMIZATION RECOMMENDATIONS:**\n- [Hook enhancement]\n- [Timing optimization]\n- [Platform-specific adaptations]\n- [Hashtag strategy]\n\n⚡ **AMPLIFICATION TACTICS:**\n- [Community engagement strategies]\n- [Influencer collaboration potential]\n- [Cross-platform distribution]\n- [Trend-jacking opportunities]\n\n🎯 **SUCCESS METRICS:**\n- [KPI predictions]\n- [Engagement benchmarks]\n- [Growth projections]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Deep Impact Analysis',
                category: 'content-research',
                content: 'Perform comprehensive deep impact analysis of this content:\n\n**DEEP IMPACT ANALYSIS REPORT**\n\n🎯 **CORE IMPACT ASSESSMENT:**\n- [Primary message impact]\n- [Audience transformation potential]\n- [Behavioral change likelihood]\n- [Long-term influence score: 1-10]\n\n💡 **KEY INSIGHTS EXTRACTION:**\n- [Most powerful takeaways]\n- [Hidden value propositions]\n- [Actionable intelligence]\n- [Strategic implications]\n\n🔍 **DEPTH ANALYSIS:**\n- [Surface-level vs deep meaning]\n- [Underlying themes]\n- [Subtext identification]\n- [Cultural/social context]\n\n📊 **IMPACT AMPLIFICATION:**\n- [How to maximize reach]\n- [Engagement multiplication strategies]\n- [Authority building opportunities]\n- [Trust establishment factors]\n\n⚡ **ACTIONABLE OUTCOMES:**\n- [Immediate implementation steps]\n- [Content adaptation strategies]\n- [Follow-up content ideas]\n- [Monetization opportunities]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Latest Update Hunter',
                category: 'content-research',
                content: 'Hunt for the latest updates and breaking information in this content:\n\n**LATEST UPDATE HUNTER REPORT**\n\n🔥 **BREAKING ELEMENTS:**\n- [New information identified]\n- [Recent developments]\n- [Time-sensitive updates]\n- [Urgency level: 1-10]\n\n📅 **TIMELINE ANALYSIS:**\n- [When information was released]\n- [Update frequency patterns]\n- [Trend progression]\n- [Future prediction timeline]\n\n🎯 **UPDATE SIGNIFICANCE:**\n- [Impact on target audience]\n- [Market/industry implications]\n- [Competitive advantage potential]\n- [Action requirement urgency]\n\n📈 **TREND CORRELATION:**\n- [Connection to current trends]\n- [Emerging pattern identification]\n- [Cross-industry relevance]\n- [Viral potential of updates]\n\n⚡ **IMMEDIATE ACTIONS:**\n- [Content creation opportunities]\n- [Audience notification strategy]\n- [Platform-specific adaptations]\n- [Follow-up content planning]\n\n🔍 **VERIFICATION STATUS:**\n- [Source credibility]\n- [Information accuracy]\n- [Fact-checking requirements]\n- [Additional research needed]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Q&A Content Generator',
                category: 'content-research',
                content: 'Generate comprehensive Q&A content based on this material:\n\n**Q&A CONTENT GENERATOR**\n\n❓ **FREQUENTLY ASKED QUESTIONS:**\n1. [Most common question about the topic]\n2. [Second most important question]\n3. [Technical/detailed question]\n4. [Beginner-level question]\n5. [Advanced/expert question]\n\n🎯 **AUDIENCE-SPECIFIC Q&As:**\n\n**For Beginners:**\n- Q: [Basic understanding question]\n- A: [Simple, clear explanation]\n\n**For Intermediate Users:**\n- Q: [Implementation question]\n- A: [Step-by-step guidance]\n\n**For Advanced Users:**\n- Q: [Optimization/strategy question]\n- A: [Expert-level insights]\n\n💡 **ANTICIPATORY Q&As:**\n- Q: [Question audience might not know to ask]\n- A: [Proactive valuable information]\n\n🔥 **CONTROVERSIAL/DEBATE Q&As:**\n- Q: [Challenging or controversial aspect]\n- A: [Balanced, thoughtful response]\n\n📊 **PRACTICAL APPLICATION Q&As:**\n- Q: [How to implement/use information]\n- A: [Actionable steps and examples]\n\n⚡ **QUICK FIRE Q&As:**\n[5-10 rapid-fire questions with concise answers]\n\n🎬 **CONTENT ADAPTATION:**\n- [Video Q&A suggestions]\n- [Social media Q&A posts]\n- [Blog post Q&A sections]\n- [Live stream Q&A topics]\n\nContent to analyze:\n{transcript}'
            }
        ];

        try {
            // Get all users
            const users = this.db.prepare('SELECT id FROM users').all();

            const insertStmt = this.db.prepare(`
                INSERT OR IGNORE INTO txt_prompts (user_id, name, content, category, is_default, created_at, updated_at)
                VALUES (?, ?, ?, ?, 1, datetime('now'), datetime('now'))
            `);

            for (const user of users) {
                for (const prompt of newPrompts) {
                    insertStmt.run(user.id, prompt.name, prompt.content, prompt.category);
                }
            }

            console.log(`Added ${newPrompts.length} new default prompts to ${users.length} existing users`);
        } catch (error) {
            console.error('Error adding missing default prompts:', error);
        }
    }

    // Insert default TXT prompts for all users
    private insertDefaultTxtPrompts() {
        const defaultPrompts = [
            {
                name: 'Script Improvement',
                category: 'script-writing',
                content: 'Please analyze this video script and suggest improvements for better audience engagement, clarity, and flow. Focus on:\n1. Hook effectiveness\n2. Content structure\n3. Call-to-action placement\n4. Audience retention strategies\n\nScript:\n{transcript}'
            },
            {
                name: 'Content Summarization',
                category: 'summarization',
                content: 'Create a concise summary of this video transcript, highlighting the key points and main takeaways. Format it as bullet points for easy reading.\n\nTranscript:\n{transcript}'
            },
            {
                name: 'Audience Engagement Analysis',
                category: 'audience-engagement',
                content: 'Analyze this video script for audience engagement potential. Identify:\n1. Strong engagement moments\n2. Potential drop-off points\n3. Emotional triggers\n4. Interactive elements\n5. Suggestions for improvement\n\nScript:\n{transcript}'
            },
            {
                name: 'SEO Title & Description',
                category: 'seo-optimization',
                content: 'Based on this video transcript, generate:\n1. 5 SEO-optimized title options\n2. A compelling video description\n3. Relevant tags/keywords\n4. Thumbnail text suggestions\n\nTranscript:\n{transcript}'
            },
            {
                name: 'SlickV1 - Financial Content',
                category: 'content-research',
                content: 'Transform this content into a SlickV1 financial analysis with urgent, direct tone and empathetic language:\n\n**URGENT FINANCIAL INSIGHT**\n\nI understand you\'re looking for actionable financial guidance in these uncertain times. Here\'s what you need to know RIGHT NOW:\n\n🚨 **IMMEDIATE ACTION REQUIRED:**\n[Extract 3 most urgent points from content]\n\n💡 **KEY INSIGHTS:**\n[Main financial insights from content]\n\n📊 **WHAT THIS MEANS FOR YOU:**\n[Personal impact analysis]\n\n⚡ **NEXT STEPS:**\n1. [Immediate action]\n2. [Short-term strategy]\n3. [Long-term consideration]\n\n💰 **BOTTOM LINE:**\n[Clear, actionable conclusion]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Content Research - Trend Analysis',
                category: 'content-research',
                content: 'Analyze this content for trending topics and viral potential:\n\n**TREND ANALYSIS REPORT**\n\n🔥 **TRENDING ELEMENTS:**\n- [Identify trending topics/keywords]\n- [Viral potential score: 1-10]\n- [Peak engagement timeframe]\n\n📈 **CONTENT OPTIMIZATION:**\n- [Hook improvements]\n- [Engagement triggers]\n- [Call-to-action suggestions]\n\n🎯 **TARGET AUDIENCE:**\n- [Primary demographics]\n- [Interest alignment]\n- [Engagement patterns]\n\n⚡ **ACTIONABLE INSIGHTS:**\n- [Content adaptation strategies]\n- [Platform-specific optimizations]\n- [Timing recommendations]\n\nContent:\n{transcript}'
            },
            {
                name: 'Content Research - Competitor Analysis',
                category: 'content-research',
                content: 'Perform competitive content analysis and positioning strategy:\n\n**COMPETITIVE INTELLIGENCE**\n\n🎯 **CONTENT POSITIONING:**\n- [Unique angle identification]\n- [Differentiation opportunities]\n- [Market gap analysis]\n\n📊 **PERFORMANCE INDICATORS:**\n- [Engagement potential]\n- [Shareability factors]\n- [Retention elements]\n\n🚀 **OPTIMIZATION STRATEGY:**\n- [Content enhancement suggestions]\n- [Format recommendations]\n- [Distribution tactics]\n\n💡 **COMPETITIVE ADVANTAGE:**\n- [Unique value propositions]\n- [Content superiority factors]\n- [Market positioning]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Content Research - Audience Insights',
                category: 'content-research',
                content: 'Extract deep audience insights and engagement strategies:\n\n**AUDIENCE INTELLIGENCE REPORT**\n\n👥 **AUDIENCE PROFILE:**\n- [Demographics and psychographics]\n- [Pain points and desires]\n- [Content consumption patterns]\n\n🧠 **PSYCHOLOGICAL TRIGGERS:**\n- [Emotional hooks]\n- [Motivation drivers]\n- [Decision-making factors]\n\n📱 **ENGAGEMENT STRATEGY:**\n- [Platform-specific adaptations]\n- [Optimal posting times]\n- [Content format preferences]\n\n🎯 **CONVERSION OPTIMIZATION:**\n- [Call-to-action placement]\n- [Trust-building elements]\n- [Value proposition clarity]\n\nContent:\n{transcript}'
            },
            {
                name: 'Content Research - Viral Potential',
                category: 'content-research',
                content: 'Assess and optimize content for maximum viral potential:\n\n**VIRAL POTENTIAL ASSESSMENT**\n\n🚀 **VIRALITY SCORE:** [Rate 1-10 with explanation]\n\n🔥 **VIRAL ELEMENTS:**\n- [Shareability factors]\n- [Emotional triggers]\n- [Controversy/debate potential]\n- [Relatability quotient]\n\n📈 **OPTIMIZATION RECOMMENDATIONS:**\n- [Hook enhancement]\n- [Timing optimization]\n- [Platform-specific adaptations]\n- [Hashtag strategy]\n\n⚡ **AMPLIFICATION TACTICS:**\n- [Community engagement strategies]\n- [Influencer collaboration potential]\n- [Cross-platform distribution]\n- [Trend-jacking opportunities]\n\n🎯 **SUCCESS METRICS:**\n- [KPI predictions]\n- [Engagement benchmarks]\n- [Growth projections]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Deep Impact Analysis',
                category: 'content-research',
                content: 'Perform comprehensive deep impact analysis of this content:\n\n**DEEP IMPACT ANALYSIS REPORT**\n\n🎯 **CORE IMPACT ASSESSMENT:**\n- [Primary message impact]\n- [Audience transformation potential]\n- [Behavioral change likelihood]\n- [Long-term influence score: 1-10]\n\n💡 **KEY INSIGHTS EXTRACTION:**\n- [Most powerful takeaways]\n- [Hidden value propositions]\n- [Actionable intelligence]\n- [Strategic implications]\n\n🔍 **DEPTH ANALYSIS:**\n- [Surface-level vs deep meaning]\n- [Underlying themes]\n- [Subtext identification]\n- [Cultural/social context]\n\n📊 **IMPACT AMPLIFICATION:**\n- [How to maximize reach]\n- [Engagement multiplication strategies]\n- [Authority building opportunities]\n- [Trust establishment factors]\n\n⚡ **ACTIONABLE OUTCOMES:**\n- [Immediate implementation steps]\n- [Content adaptation strategies]\n- [Follow-up content ideas]\n- [Monetization opportunities]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Latest Update Hunter',
                category: 'content-research',
                content: 'Hunt for the latest updates and breaking information in this content:\n\n**LATEST UPDATE HUNTER REPORT**\n\n🔥 **BREAKING ELEMENTS:**\n- [New information identified]\n- [Recent developments]\n- [Time-sensitive updates]\n- [Urgency level: 1-10]\n\n📅 **TIMELINE ANALYSIS:**\n- [When information was released]\n- [Update frequency patterns]\n- [Trend progression]\n- [Future prediction timeline]\n\n🎯 **UPDATE SIGNIFICANCE:**\n- [Impact on target audience]\n- [Market/industry implications]\n- [Competitive advantage potential]\n- [Action requirement urgency]\n\n📈 **TREND CORRELATION:**\n- [Connection to current trends]\n- [Emerging pattern identification]\n- [Cross-industry relevance]\n- [Viral potential of updates]\n\n⚡ **IMMEDIATE ACTIONS:**\n- [Content creation opportunities]\n- [Audience notification strategy]\n- [Platform-specific adaptations]\n- [Follow-up content planning]\n\n🔍 **VERIFICATION STATUS:**\n- [Source credibility]\n- [Information accuracy]\n- [Fact-checking requirements]\n- [Additional research needed]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Q&A Content Generator',
                category: 'content-research',
                content: 'Generate comprehensive Q&A content based on this material:\n\n**Q&A CONTENT GENERATOR**\n\n❓ **FREQUENTLY ASKED QUESTIONS:**\n1. [Most common question about the topic]\n2. [Second most important question]\n3. [Technical/detailed question]\n4. [Beginner-level question]\n5. [Advanced/expert question]\n\n🎯 **AUDIENCE-SPECIFIC Q&As:**\n\n**For Beginners:**\n- Q: [Basic understanding question]\n- A: [Simple, clear explanation]\n\n**For Intermediate Users:**\n- Q: [Implementation question]\n- A: [Step-by-step guidance]\n\n**For Advanced Users:**\n- Q: [Optimization/strategy question]\n- A: [Expert-level insights]\n\n💡 **ANTICIPATORY Q&As:**\n- Q: [Question audience might not know to ask]\n- A: [Proactive valuable information]\n\n🔥 **CONTROVERSIAL/DEBATE Q&As:**\n- Q: [Challenging or controversial aspect]\n- A: [Balanced, thoughtful response]\n\n📊 **PRACTICAL APPLICATION Q&As:**\n- Q: [How to implement/use information]\n- A: [Actionable steps and examples]\n\n⚡ **QUICK FIRE Q&As:**\n[5-10 rapid-fire questions with concise answers]\n\n🎬 **CONTENT ADAPTATION:**\n- [Video Q&A suggestions]\n- [Social media Q&A posts]\n- [Blog post Q&A sections]\n- [Live stream Q&A topics]\n\nContent to analyze:\n{transcript}'
            }
        ];

        try {
            // Get all users
            const users = this.db.prepare('SELECT id FROM users').all();

            for (const user of users) {
                // Check if user already has default prompts
                const existingPrompts = this.db.prepare('SELECT COUNT(*) as count FROM txt_prompts WHERE user_id = ? AND is_default = 1').get(user.id);

                if (existingPrompts.count === 0) {
                    // Insert default prompts for this user
                    const insertStmt = this.db.prepare(`
                        INSERT INTO txt_prompts (user_id, name, content, category, is_default, created_at, updated_at)
                        VALUES (?, ?, ?, ?, 1, ?, ?)
                    `);

                    const now = Date.now();
                    for (const prompt of defaultPrompts) {
                        insertStmt.run(user.id, prompt.name, prompt.content, prompt.category, now, now);
                    }
                }
            }
        } catch (error) {
            console.error('Error inserting default TXT prompts:', error);
        }
    }

    // Create default TXT prompts for a specific user
    private createDefaultTxtPromptsForUser(userId: number) {
        const defaultPrompts = [
            {
                name: 'Script Improvement',
                category: 'script-writing',
                content: 'Please analyze this video script and suggest improvements for better audience engagement, clarity, and flow. Focus on:\n1. Hook effectiveness\n2. Content structure\n3. Call-to-action placement\n4. Audience retention strategies\n\nScript:\n{transcript}'
            },
            {
                name: 'Content Summarization',
                category: 'summarization',
                content: 'Create a concise summary of this video transcript, highlighting the key points and main takeaways. Format it as bullet points for easy reading.\n\nTranscript:\n{transcript}'
            },
            {
                name: 'Audience Engagement Analysis',
                category: 'audience-engagement',
                content: 'Analyze this video script for audience engagement potential. Identify:\n1. Strong engagement moments\n2. Potential drop-off points\n3. Emotional triggers\n4. Interactive elements\n5. Suggestions for improvement\n\nScript:\n{transcript}'
            },
            {
                name: 'SEO Title & Description',
                category: 'seo-optimization',
                content: 'Based on this video transcript, generate:\n1. 5 SEO-optimized title options\n2. A compelling video description\n3. Relevant tags/keywords\n4. Thumbnail text suggestions\n\nTranscript:\n{transcript}'
            },
            {
                name: 'SlickV1 - Financial Content',
                category: 'content-research',
                content: 'Transform this content into a SlickV1 financial analysis with urgent, direct tone and empathetic language:\n\n**URGENT FINANCIAL INSIGHT**\n\nI understand you\'re looking for actionable financial guidance in these uncertain times. Here\'s what you need to know RIGHT NOW:\n\n🚨 **IMMEDIATE ACTION REQUIRED:**\n[Extract 3 most urgent points from content]\n\n💡 **KEY INSIGHTS:**\n[Main financial insights from content]\n\n📊 **WHAT THIS MEANS FOR YOU:**\n[Personal impact analysis]\n\n⚡ **NEXT STEPS:**\n1. [Immediate action]\n2. [Short-term strategy]\n3. [Long-term consideration]\n\n💰 **BOTTOM LINE:**\n[Clear, actionable conclusion]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Content Research - Trend Analysis',
                category: 'content-research',
                content: 'Analyze this content for trending topics and viral potential:\n\n**TREND ANALYSIS REPORT**\n\n🔥 **TRENDING ELEMENTS:**\n- [Identify trending topics/keywords]\n- [Viral potential score: 1-10]\n- [Peak engagement timeframe]\n\n📈 **CONTENT OPTIMIZATION:**\n- [Hook improvements]\n- [Engagement triggers]\n- [Call-to-action suggestions]\n\n🎯 **TARGET AUDIENCE:**\n- [Primary demographics]\n- [Interest alignment]\n- [Engagement patterns]\n\n⚡ **ACTIONABLE INSIGHTS:**\n- [Content adaptation strategies]\n- [Platform-specific optimizations]\n- [Timing recommendations]\n\nContent:\n{transcript}'
            },
            {
                name: 'Content Research - Competitor Analysis',
                category: 'content-research',
                content: 'Perform competitive content analysis and positioning strategy:\n\n**COMPETITIVE INTELLIGENCE**\n\n🎯 **CONTENT POSITIONING:**\n- [Unique angle identification]\n- [Differentiation opportunities]\n- [Market gap analysis]\n\n📊 **PERFORMANCE INDICATORS:**\n- [Engagement potential]\n- [Shareability factors]\n- [Retention elements]\n\n🚀 **OPTIMIZATION STRATEGY:**\n- [Content enhancement suggestions]\n- [Format recommendations]\n- [Distribution tactics]\n\n💡 **COMPETITIVE ADVANTAGE:**\n- [Unique value propositions]\n- [Content superiority factors]\n- [Market positioning]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Content Research - Audience Insights',
                category: 'content-research',
                content: 'Extract deep audience insights and engagement strategies:\n\n**AUDIENCE INTELLIGENCE REPORT**\n\n👥 **AUDIENCE PROFILE:**\n- [Demographics and psychographics]\n- [Pain points and desires]\n- [Content consumption patterns]\n\n🧠 **PSYCHOLOGICAL TRIGGERS:**\n- [Emotional hooks]\n- [Motivation drivers]\n- [Decision-making factors]\n\n📱 **ENGAGEMENT STRATEGY:**\n- [Platform-specific adaptations]\n- [Optimal posting times]\n- [Content format preferences]\n\n🎯 **CONVERSION OPTIMIZATION:**\n- [Call-to-action placement]\n- [Trust-building elements]\n- [Value proposition clarity]\n\nContent:\n{transcript}'
            },
            {
                name: 'Content Research - Viral Potential',
                category: 'content-research',
                content: 'Assess and optimize content for maximum viral potential:\n\n**VIRAL POTENTIAL ASSESSMENT**\n\n🚀 **VIRALITY SCORE:** [Rate 1-10 with explanation]\n\n🔥 **VIRAL ELEMENTS:**\n- [Shareability factors]\n- [Emotional triggers]\n- [Controversy/debate potential]\n- [Relatability quotient]\n\n📈 **OPTIMIZATION RECOMMENDATIONS:**\n- [Hook enhancement]\n- [Timing optimization]\n- [Platform-specific adaptations]\n- [Hashtag strategy]\n\n⚡ **AMPLIFICATION TACTICS:**\n- [Community engagement strategies]\n- [Influencer collaboration potential]\n- [Cross-platform distribution]\n- [Trend-jacking opportunities]\n\n🎯 **SUCCESS METRICS:**\n- [KPI predictions]\n- [Engagement benchmarks]\n- [Growth projections]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Deep Impact Analysis',
                category: 'content-research',
                content: 'Perform comprehensive deep impact analysis of this content:\n\n**DEEP IMPACT ANALYSIS REPORT**\n\n🎯 **CORE IMPACT ASSESSMENT:**\n- [Primary message impact]\n- [Audience transformation potential]\n- [Behavioral change likelihood]\n- [Long-term influence score: 1-10]\n\n💡 **KEY INSIGHTS EXTRACTION:**\n- [Most powerful takeaways]\n- [Hidden value propositions]\n- [Actionable intelligence]\n- [Strategic implications]\n\n🔍 **DEPTH ANALYSIS:**\n- [Surface-level vs deep meaning]\n- [Underlying themes]\n- [Subtext identification]\n- [Cultural/social context]\n\n📊 **IMPACT AMPLIFICATION:**\n- [How to maximize reach]\n- [Engagement multiplication strategies]\n- [Authority building opportunities]\n- [Trust establishment factors]\n\n⚡ **ACTIONABLE OUTCOMES:**\n- [Immediate implementation steps]\n- [Content adaptation strategies]\n- [Follow-up content ideas]\n- [Monetization opportunities]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Latest Update Hunter',
                category: 'content-research',
                content: 'Hunt for the latest updates and breaking information in this content:\n\n**LATEST UPDATE HUNTER REPORT**\n\n🔥 **BREAKING ELEMENTS:**\n- [New information identified]\n- [Recent developments]\n- [Time-sensitive updates]\n- [Urgency level: 1-10]\n\n📅 **TIMELINE ANALYSIS:**\n- [When information was released]\n- [Update frequency patterns]\n- [Trend progression]\n- [Future prediction timeline]\n\n🎯 **UPDATE SIGNIFICANCE:**\n- [Impact on target audience]\n- [Market/industry implications]\n- [Competitive advantage potential]\n- [Action requirement urgency]\n\n📈 **TREND CORRELATION:**\n- [Connection to current trends]\n- [Emerging pattern identification]\n- [Cross-industry relevance]\n- [Viral potential of updates]\n\n⚡ **IMMEDIATE ACTIONS:**\n- [Content creation opportunities]\n- [Audience notification strategy]\n- [Platform-specific adaptations]\n- [Follow-up content planning]\n\n🔍 **VERIFICATION STATUS:**\n- [Source credibility]\n- [Information accuracy]\n- [Fact-checking requirements]\n- [Additional research needed]\n\nContent to analyze:\n{transcript}'
            },
            {
                name: 'Q&A Content Generator',
                category: 'content-research',
                content: 'Generate comprehensive Q&A content based on this material:\n\n**Q&A CONTENT GENERATOR**\n\n❓ **FREQUENTLY ASKED QUESTIONS:**\n1. [Most common question about the topic]\n2. [Second most important question]\n3. [Technical/detailed question]\n4. [Beginner-level question]\n5. [Advanced/expert question]\n\n🎯 **AUDIENCE-SPECIFIC Q&As:**\n\n**For Beginners:**\n- Q: [Basic understanding question]\n- A: [Simple, clear explanation]\n\n**For Intermediate Users:**\n- Q: [Implementation question]\n- A: [Step-by-step guidance]\n\n**For Advanced Users:**\n- Q: [Optimization/strategy question]\n- A: [Expert-level insights]\n\n💡 **ANTICIPATORY Q&As:**\n- Q: [Question audience might not know to ask]\n- A: [Proactive valuable information]\n\n🔥 **CONTROVERSIAL/DEBATE Q&As:**\n- Q: [Challenging or controversial aspect]\n- A: [Balanced, thoughtful response]\n\n📊 **PRACTICAL APPLICATION Q&As:**\n- Q: [How to implement/use information]\n- A: [Actionable steps and examples]\n\n⚡ **QUICK FIRE Q&As:**\n[5-10 rapid-fire questions with concise answers]\n\n🎬 **CONTENT ADAPTATION:**\n- [Video Q&A suggestions]\n- [Social media Q&A posts]\n- [Blog post Q&A sections]\n- [Live stream Q&A topics]\n\nContent to analyze:\n{transcript}'
            }
        ];

        try {
            const insertStmt = this.db.prepare(`
                INSERT INTO txt_prompts (user_id, name, content, category, is_default, created_at, updated_at)
                VALUES (?, ?, ?, ?, 1, ?, ?)
            `);

            const now = Date.now();
            for (const prompt of defaultPrompts) {
                insertStmt.run(userId, prompt.name, prompt.content, prompt.category, now, now);
            }
        } catch (error) {
            console.error('Error creating default TXT prompts for user:', error);
        }
    }

    // TXT PRO Scripts methods
    async createTxtProScript(userId: number, data: { id: string; title: string; sourceVideoUrl?: string; transcript: string; tags: string[]; dateAdded: Date; editHistory?: Array<{ date: Date; changes: string }> }): Promise<string> {
        const now = Date.now();
        const stmt = this.db.prepare(`
            INSERT INTO txt_pro_scripts (id, user_id, title, source_video_url, transcript, tags, date_added, edit_history, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        stmt.run(
            data.id,
            userId,
            data.title,
            data.sourceVideoUrl || null,
            data.transcript,
            JSON.stringify(data.tags),
            data.dateAdded.getTime(),
            JSON.stringify(data.editHistory || []),
            now,
            now
        );
        return data.id;
    }

    async getTxtProScripts(userId: number): Promise<any[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM txt_pro_scripts
            WHERE user_id = ?
            ORDER BY date_added DESC
        `);
        const rows = stmt.all(userId);
        return rows.map(row => ({
            ...row,
            tags: JSON.parse(row.tags),
            dateAdded: new Date(row.date_added),
            editHistory: JSON.parse(row.edit_history || '[]'),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        }));
    }

    async updateTxtProScript(id: string, userId: number, data: Partial<{ title: string; sourceVideoUrl: string; transcript: string; tags: string[]; editHistory: Array<{ date: Date; changes: string }> }>): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.title !== undefined) {
            updates.push('title = ?');
            values.push(data.title);
        }
        if (data.sourceVideoUrl !== undefined) {
            updates.push('source_video_url = ?');
            values.push(data.sourceVideoUrl);
        }
        if (data.transcript !== undefined) {
            updates.push('transcript = ?');
            values.push(data.transcript);
        }
        if (data.tags !== undefined) {
            updates.push('tags = ?');
            values.push(JSON.stringify(data.tags));
        }
        if (data.editHistory !== undefined) {
            updates.push('edit_history = ?');
            values.push(JSON.stringify(data.editHistory));
        }

        if (updates.length === 0) return false;

        updates.push('updated_at = ?');
        values.push(Date.now());
        values.push(id, userId);

        const stmt = this.db.prepare(`
            UPDATE txt_pro_scripts
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTxtProScript(id: string, userId: number): Promise<boolean> {
        const stmt = this.db.prepare(`
            DELETE FROM txt_pro_scripts
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    // TXT PRO Chunks methods
    async createTxtProChunk(userId: number, data: { id: string; name: string; description?: string; content: string; purpose: string; tags: string[]; sourceScriptId?: string }): Promise<string> {
        const now = Date.now();
        const stmt = this.db.prepare(`
            INSERT INTO txt_pro_chunks (id, user_id, name, description, content, purpose, tags, source_script_id, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        stmt.run(
            data.id,
            userId,
            data.name,
            data.description || null,
            data.content,
            data.purpose,
            JSON.stringify(data.tags),
            data.sourceScriptId || null,
            now,
            now
        );
        return data.id;
    }

    async getTxtProChunks(userId: number): Promise<any[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM txt_pro_chunks
            WHERE user_id = ?
            ORDER BY created_at DESC
        `);
        const rows = stmt.all(userId);
        return rows.map(row => ({
            ...row,
            tags: JSON.parse(row.tags),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        }));
    }

    async updateTxtProChunk(id: string, userId: number, data: Partial<{ name: string; description: string; content: string; purpose: string; tags: string[]; sourceScriptId: string }>): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.name !== undefined) {
            updates.push('name = ?');
            values.push(data.name);
        }
        if (data.description !== undefined) {
            updates.push('description = ?');
            values.push(data.description);
        }
        if (data.content !== undefined) {
            updates.push('content = ?');
            values.push(data.content);
        }
        if (data.purpose !== undefined) {
            updates.push('purpose = ?');
            values.push(data.purpose);
        }
        if (data.tags !== undefined) {
            updates.push('tags = ?');
            values.push(JSON.stringify(data.tags));
        }
        if (data.sourceScriptId !== undefined) {
            updates.push('source_script_id = ?');
            values.push(data.sourceScriptId);
        }

        if (updates.length === 0) return false;

        updates.push('updated_at = ?');
        values.push(Date.now());
        values.push(id, userId);

        const stmt = this.db.prepare(`
            UPDATE txt_pro_chunks
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTxtProChunk(id: string, userId: number): Promise<boolean> {
        const stmt = this.db.prepare(`
            DELETE FROM txt_pro_chunks
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    // TXT PRO Highlights methods
    async createTxtProHighlight(userId: number, data: { id: string; scriptId: string; startIndex: number; endIndex: number; color: string; note: string; analyticsNote?: string }): Promise<string> {
        const now = Date.now();
        const stmt = this.db.prepare(`
            INSERT INTO txt_pro_highlights (id, user_id, script_id, start_index, end_index, color, note, analytics_note, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        stmt.run(
            data.id,
            userId,
            data.scriptId,
            data.startIndex,
            data.endIndex,
            data.color,
            data.note,
            data.analyticsNote || null,
            now
        );
        return data.id;
    }

    async getTxtProHighlights(userId: number): Promise<any[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM txt_pro_highlights
            WHERE user_id = ?
            ORDER BY created_at DESC
        `);
        const rows = stmt.all(userId);
        return rows.map(row => ({
            ...row,
            createdAt: new Date(row.created_at)
        }));
    }

    async updateTxtProHighlight(id: string, userId: number, data: Partial<{ scriptId: string; startIndex: number; endIndex: number; color: string; note: string; analyticsNote: string }>): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.scriptId !== undefined) {
            updates.push('script_id = ?');
            values.push(data.scriptId);
        }
        if (data.startIndex !== undefined) {
            updates.push('start_index = ?');
            values.push(data.startIndex);
        }
        if (data.endIndex !== undefined) {
            updates.push('end_index = ?');
            values.push(data.endIndex);
        }
        if (data.color !== undefined) {
            updates.push('color = ?');
            values.push(data.color);
        }
        if (data.note !== undefined) {
            updates.push('note = ?');
            values.push(data.note);
        }
        if (data.analyticsNote !== undefined) {
            updates.push('analytics_note = ?');
            values.push(data.analyticsNote);
        }

        if (updates.length === 0) return false;

        values.push(id, userId);

        const stmt = this.db.prepare(`
            UPDATE txt_pro_highlights
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTxtProHighlight(id: string, userId: number): Promise<boolean> {
        const stmt = this.db.prepare(`
            DELETE FROM txt_pro_highlights
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    // TXT PRO Strategies methods
    async createTxtProStrategy(userId: number, data: { id: string; title: string; description?: string; samplePhrases: string[]; tone?: string }): Promise<string> {
        const now = Date.now();
        const stmt = this.db.prepare(`
            INSERT INTO txt_pro_strategies (id, user_id, title, description, sample_phrases, tone, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);
        stmt.run(
            data.id,
            userId,
            data.title,
            data.description || null,
            JSON.stringify(data.samplePhrases),
            data.tone || null,
            now,
            now
        );
        return data.id;
    }

    async getTxtProStrategies(userId: number): Promise<any[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM txt_pro_strategies
            WHERE user_id = ?
            ORDER BY created_at DESC
        `);
        const rows = stmt.all(userId);
        return rows.map(row => ({
            ...row,
            samplePhrases: JSON.parse(row.sample_phrases),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        }));
    }

    async updateTxtProStrategy(id: string, userId: number, data: Partial<{ title: string; description: string; samplePhrases: string[]; tone: string }>): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.title !== undefined) {
            updates.push('title = ?');
            values.push(data.title);
        }
        if (data.description !== undefined) {
            updates.push('description = ?');
            values.push(data.description);
        }
        if (data.samplePhrases !== undefined) {
            updates.push('sample_phrases = ?');
            values.push(JSON.stringify(data.samplePhrases));
        }
        if (data.tone !== undefined) {
            updates.push('tone = ?');
            values.push(data.tone);
        }

        if (updates.length === 0) return false;

        updates.push('updated_at = ?');
        values.push(Date.now());
        values.push(id, userId);

        const stmt = this.db.prepare(`
            UPDATE txt_pro_strategies
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTxtProStrategy(id: string, userId: number): Promise<boolean> {
        const stmt = this.db.prepare(`
            DELETE FROM txt_pro_strategies
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    // TXT PRO Content Structures methods
    async createTxtProContentStructure(userId: number, data: { id: string; name: string; category?: string; introFormat?: string; mainInfoDrop?: string; explanationBreakdown?: string; engagementInsert?: string; outroCTA?: string }): Promise<string> {
        const now = Date.now();
        const stmt = this.db.prepare(`
            INSERT INTO txt_pro_content_structures (id, user_id, name, category, intro_format, main_info_drop, explanation_breakdown, engagement_insert, outro_cta, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        stmt.run(
            data.id,
            userId,
            data.name,
            data.category || null,
            data.introFormat || null,
            data.mainInfoDrop || null,
            data.explanationBreakdown || null,
            data.engagementInsert || null,
            data.outroCTA || null,
            now,
            now
        );
        return data.id;
    }

    async getTxtProContentStructures(userId: number): Promise<any[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM txt_pro_content_structures
            WHERE user_id = ?
            ORDER BY created_at DESC
        `);
        const rows = stmt.all(userId);
        return rows.map(row => ({
            ...row,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        }));
    }

    async updateTxtProContentStructure(id: string, userId: number, data: Partial<{ name: string; category: string; introFormat: string; mainInfoDrop: string; explanationBreakdown: string; engagementInsert: string; outroCTA: string }>): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.name !== undefined) {
            updates.push('name = ?');
            values.push(data.name);
        }
        if (data.category !== undefined) {
            updates.push('category = ?');
            values.push(data.category);
        }
        if (data.introFormat !== undefined) {
            updates.push('intro_format = ?');
            values.push(data.introFormat);
        }
        if (data.mainInfoDrop !== undefined) {
            updates.push('main_info_drop = ?');
            values.push(data.mainInfoDrop);
        }
        if (data.explanationBreakdown !== undefined) {
            updates.push('explanation_breakdown = ?');
            values.push(data.explanationBreakdown);
        }
        if (data.engagementInsert !== undefined) {
            updates.push('engagement_insert = ?');
            values.push(data.engagementInsert);
        }
        if (data.outroCTA !== undefined) {
            updates.push('outro_cta = ?');
            values.push(data.outroCTA);
        }

        if (updates.length === 0) return false;

        updates.push('updated_at = ?');
        values.push(Date.now());
        values.push(id, userId);

        const stmt = this.db.prepare(`
            UPDATE txt_pro_content_structures
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTxtProContentStructure(id: string, userId: number): Promise<boolean> {
        const stmt = this.db.prepare(`
            DELETE FROM txt_pro_content_structures
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    // TXT PRO Core Infos methods
    async createTxtProCoreInfo(userId: number, data: { id: string; eventDate?: string; eligibilityCriteria?: string; actBillProposalName?: string; payDates?: string; programType?: string; sourceLink?: string; notes?: string }): Promise<string> {
        const now = Date.now();
        const stmt = this.db.prepare(`
            INSERT INTO txt_pro_core_infos (id, user_id, event_date, eligibility_criteria, act_bill_proposal_name, pay_dates, program_type, source_link, notes, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        stmt.run(
            data.id,
            userId,
            data.eventDate || null,
            data.eligibilityCriteria || null,
            data.actBillProposalName || null,
            data.payDates || null,
            data.programType || null,
            data.sourceLink || null,
            data.notes || null,
            now,
            now
        );
        return data.id;
    }

    async getTxtProCoreInfos(userId: number): Promise<any[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM txt_pro_core_infos
            WHERE user_id = ?
            ORDER BY created_at DESC
        `);
        const rows = stmt.all(userId);
        return rows.map(row => ({
            ...row,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        }));
    }

    async updateTxtProCoreInfo(id: string, userId: number, data: Partial<{ eventDate: string; eligibilityCriteria: string; actBillProposalName: string; payDates: string; programType: string; sourceLink: string; notes: string }>): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.eventDate !== undefined) {
            updates.push('event_date = ?');
            values.push(data.eventDate);
        }
        if (data.eligibilityCriteria !== undefined) {
            updates.push('eligibility_criteria = ?');
            values.push(data.eligibilityCriteria);
        }
        if (data.actBillProposalName !== undefined) {
            updates.push('act_bill_proposal_name = ?');
            values.push(data.actBillProposalName);
        }
        if (data.payDates !== undefined) {
            updates.push('pay_dates = ?');
            values.push(data.payDates);
        }
        if (data.programType !== undefined) {
            updates.push('program_type = ?');
            values.push(data.programType);
        }
        if (data.sourceLink !== undefined) {
            updates.push('source_link = ?');
            values.push(data.sourceLink);
        }
        if (data.notes !== undefined) {
            updates.push('notes = ?');
            values.push(data.notes);
        }

        if (updates.length === 0) return false;

        updates.push('updated_at = ?');
        values.push(Date.now());
        values.push(id, userId);

        const stmt = this.db.prepare(`
            UPDATE txt_pro_core_infos
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTxtProCoreInfo(id: string, userId: number): Promise<boolean> {
        const stmt = this.db.prepare(`
            DELETE FROM txt_pro_core_infos
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    // TXT PRO Narration Styles methods
    async createTxtProNarrationStyle(userId: number, data: { id: string; landmarkName: string; description?: string; speakerTone?: string; commonPhrases: string[]; timingRhythm?: string }): Promise<string> {
        const now = Date.now();
        const stmt = this.db.prepare(`
            INSERT INTO txt_pro_narration_styles (id, user_id, landmark_name, description, speaker_tone, common_phrases, timing_rhythm, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        stmt.run(
            data.id,
            userId,
            data.landmarkName,
            data.description || null,
            data.speakerTone || null,
            JSON.stringify(data.commonPhrases),
            data.timingRhythm || null,
            now,
            now
        );
        return data.id;
    }

    async getTxtProNarrationStyles(userId: number): Promise<any[]> {
        const stmt = this.db.prepare(`
            SELECT * FROM txt_pro_narration_styles
            WHERE user_id = ?
            ORDER BY created_at DESC
        `);
        const rows = stmt.all(userId);
        return rows.map(row => ({
            ...row,
            commonPhrases: JSON.parse(row.common_phrases),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at)
        }));
    }

    async updateTxtProNarrationStyle(id: string, userId: number, data: Partial<{ landmarkName: string; description: string; speakerTone: string; commonPhrases: string[]; timingRhythm: string }>): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.landmarkName !== undefined) {
            updates.push('landmark_name = ?');
            values.push(data.landmarkName);
        }
        if (data.description !== undefined) {
            updates.push('description = ?');
            values.push(data.description);
        }
        if (data.speakerTone !== undefined) {
            updates.push('speaker_tone = ?');
            values.push(data.speakerTone);
        }
        if (data.commonPhrases !== undefined) {
            updates.push('common_phrases = ?');
            values.push(JSON.stringify(data.commonPhrases));
        }
        if (data.timingRhythm !== undefined) {
            updates.push('timing_rhythm = ?');
            values.push(data.timingRhythm);
        }

        if (updates.length === 0) return false;

        updates.push('updated_at = ?');
        values.push(Date.now());
        values.push(id, userId);

        const stmt = this.db.prepare(`
            UPDATE txt_pro_narration_styles
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTxtProNarrationStyle(id: string, userId: number): Promise<boolean> {
        const stmt = this.db.prepare(`
            DELETE FROM txt_pro_narration_styles
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    // TS (Transcript) Tab methods
    async createTsChannel(userId: number, data: { channelId: string; channelTitle: string; channelUrl: string; thumbnail?: string; description?: string; subscriberCount?: number; videoLimit: number }): Promise<TsChannel> {
        const now = new Date().toISOString();
        const stmt = this.db.prepare(`
            INSERT INTO ts_channels (user_id, channel_id, channel_title, channel_url, thumbnail, description, subscriber_count, video_limit, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        const result = stmt.run(userId, data.channelId, data.channelTitle, data.channelUrl, data.thumbnail || null, data.description || null, data.subscriberCount || 0, data.videoLimit, now);

        const channel = this.db.prepare('SELECT * FROM ts_channels WHERE id = ?').get(result.lastInsertRowid) as any;
        return {
            id: channel.id,
            userId: channel.user_id,
            channelId: channel.channel_id,
            channelTitle: channel.channel_title,
            channelUrl: channel.channel_url,
            thumbnail: channel.thumbnail,
            description: channel.description,
            subscriberCount: channel.subscriber_count,
            videoLimit: channel.video_limit,
            lastRefreshTime: channel.last_refresh_time ? new Date(channel.last_refresh_time) : undefined,
            createdAt: new Date(channel.created_at)
        };
    }

    async getTsChannels(userId: number): Promise<TsChannel[]> {
        const stmt = this.db.prepare('SELECT * FROM ts_channels WHERE user_id = ? ORDER BY created_at DESC');
        const rows = stmt.all(userId) as any[];
        return rows.map(row => ({
            id: row.id,
            userId: row.user_id,
            channelId: row.channel_id,
            channelTitle: row.channel_title,
            channelUrl: row.channel_url,
            thumbnail: row.thumbnail,
            description: row.description,
            subscriberCount: row.subscriber_count,
            videoLimit: row.video_limit,
            lastRefreshTime: row.last_refresh_time ? new Date(row.last_refresh_time) : undefined,
            createdAt: new Date(row.created_at)
        }));
    }

    async getTsChannel(id: number, userId: number): Promise<TsChannel | null> {
        const stmt = this.db.prepare('SELECT * FROM ts_channels WHERE id = ? AND user_id = ?');
        const row = stmt.get(id, userId) as any;
        if (!row) return null;

        return {
            id: row.id,
            userId: row.user_id,
            channelId: row.channel_id,
            channelTitle: row.channel_title,
            channelUrl: row.channel_url,
            thumbnail: row.thumbnail,
            description: row.description,
            subscriberCount: row.subscriber_count,
            videoLimit: row.video_limit,
            lastRefreshTime: row.last_refresh_time ? new Date(row.last_refresh_time) : undefined,
            createdAt: new Date(row.created_at)
        };
    }

    async updateTsChannel(id: number, userId: number, data: Partial<TsChannel>): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.channelTitle !== undefined) {
            updates.push('channel_title = ?');
            values.push(data.channelTitle);
        }
        if (data.channelUrl !== undefined) {
            updates.push('channel_url = ?');
            values.push(data.channelUrl);
        }
        if (data.thumbnail !== undefined) {
            updates.push('thumbnail = ?');
            values.push(data.thumbnail);
        }
        if (data.description !== undefined) {
            updates.push('description = ?');
            values.push(data.description);
        }
        if (data.subscriberCount !== undefined) {
            updates.push('subscriber_count = ?');
            values.push(data.subscriberCount);
        }
        if (data.videoLimit !== undefined) {
            updates.push('video_limit = ?');
            values.push(data.videoLimit);
        }
        if (data.lastRefreshTime !== undefined) {
            updates.push('last_refresh_time = ?');
            values.push(data.lastRefreshTime.toISOString());
        }

        if (updates.length === 0) return false;

        values.push(id, userId);
        const stmt = this.db.prepare(`
            UPDATE ts_channels
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTsChannel(id: number, userId: number): Promise<boolean> {
        const stmt = this.db.prepare('DELETE FROM ts_channels WHERE id = ? AND user_id = ?');
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    async createTsVideo(userId: number, data: Partial<TsVideo>): Promise<TsVideo> {
        const now = new Date().toISOString();
        const stmt = this.db.prepare(`
            INSERT INTO ts_videos (id, title, thumbnail, channel_id, channel_title, view_count, published_at, user_id, ts_channel_id, description, duration, transcript_path, has_transcript, transcript_downloaded_at, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        stmt.run(
            data.id,
            data.title,
            data.thumbnail,
            data.channelId,
            data.channelTitle,
            data.viewCount || 0,
            data.publishedAt?.toISOString() || now,
            userId,
            data.tsChannelId,
            data.description || null,
            data.duration || null,
            data.transcriptPath || null,
            data.hasTranscript ? 1 : 0,
            data.transcriptDownloadedAt?.toISOString() || null,
            now
        );

        const video = this.db.prepare('SELECT * FROM ts_videos WHERE id = ?').get(data.id) as any;
        return {
            id: video.id,
            title: video.title,
            thumbnail: video.thumbnail,
            channelId: video.channel_id,
            channelTitle: video.channel_title,
            viewCount: video.view_count,
            publishedAt: new Date(video.published_at),
            userId: video.user_id,
            tsChannelId: video.ts_channel_id,
            description: video.description,
            duration: video.duration,
            transcriptPath: video.transcript_path,
            hasTranscript: Boolean(video.has_transcript),
            transcriptDownloadedAt: video.transcript_downloaded_at ? new Date(video.transcript_downloaded_at) : undefined,
            createdAt: new Date(video.created_at)
        };
    }

    async getTsVideos(userId: number, channelId?: string): Promise<TsVideo[]> {
        let query = 'SELECT * FROM ts_videos WHERE user_id = ?';
        const params: any[] = [userId];

        if (channelId) {
            query += ' AND channel_id = ?';
            params.push(channelId);
        }

        query += ' ORDER BY published_at DESC';

        const stmt = this.db.prepare(query);
        const rows = stmt.all(...params) as any[];

        return rows.map(row => ({
            id: row.id,
            title: row.title,
            thumbnail: row.thumbnail,
            channelId: row.channel_id,
            channelTitle: row.channel_title,
            viewCount: row.view_count,
            publishedAt: new Date(row.published_at),
            userId: row.user_id,
            tsChannelId: row.ts_channel_id,
            description: row.description,
            duration: row.duration,
            transcriptPath: row.transcript_path,
            hasTranscript: Boolean(row.has_transcript),
            transcriptDownloadedAt: row.transcript_downloaded_at ? new Date(row.transcript_downloaded_at) : undefined,
            createdAt: new Date(row.created_at)
        }));
    }

    async getTsVideo(id: string, userId: number): Promise<TsVideo | null> {
        const stmt = this.db.prepare('SELECT * FROM ts_videos WHERE id = ? AND user_id = ?');
        const row = stmt.get(id, userId) as any;
        if (!row) return null;

        return {
            id: row.id,
            title: row.title,
            thumbnail: row.thumbnail,
            channelId: row.channel_id,
            channelTitle: row.channel_title,
            viewCount: row.view_count,
            publishedAt: new Date(row.published_at),
            userId: row.user_id,
            tsChannelId: row.ts_channel_id,
            description: row.description,
            duration: row.duration,
            transcriptPath: row.transcript_path,
            hasTranscript: Boolean(row.has_transcript),
            transcriptDownloadedAt: row.transcript_downloaded_at ? new Date(row.transcript_downloaded_at) : undefined,
            createdAt: new Date(row.created_at)
        };
    }

    async updateTsVideo(id: string, userId: number, data: Partial<TsVideo>): Promise<boolean> {
        const updates: string[] = [];
        const values: any[] = [];

        if (data.title !== undefined) {
            updates.push('title = ?');
            values.push(data.title);
        }
        if (data.thumbnail !== undefined) {
            updates.push('thumbnail = ?');
            values.push(data.thumbnail);
        }
        if (data.viewCount !== undefined) {
            updates.push('view_count = ?');
            values.push(data.viewCount);
        }
        if (data.description !== undefined) {
            updates.push('description = ?');
            values.push(data.description);
        }
        if (data.duration !== undefined) {
            updates.push('duration = ?');
            values.push(data.duration);
        }
        if (data.transcriptPath !== undefined) {
            updates.push('transcript_path = ?');
            values.push(data.transcriptPath);
        }
        if (data.hasTranscript !== undefined) {
            updates.push('has_transcript = ?');
            values.push(data.hasTranscript ? 1 : 0);
        }
        if (data.transcriptDownloadedAt !== undefined) {
            updates.push('transcript_downloaded_at = ?');
            values.push(data.transcriptDownloadedAt?.toISOString() || null);
        }

        if (updates.length === 0) return false;

        values.push(id, userId);
        const stmt = this.db.prepare(`
            UPDATE ts_videos
            SET ${updates.join(', ')}
            WHERE id = ? AND user_id = ?
        `);
        const result = stmt.run(...values);
        return result.changes > 0;
    }

    async deleteTsVideo(id: string, userId: number): Promise<boolean> {
        const stmt = this.db.prepare('DELETE FROM ts_videos WHERE id = ? AND user_id = ?');
        const result = stmt.run(id, userId);
        return result.changes > 0;
    }

    async refreshTsChannel(channelId: number, userId: number): Promise<void> {
        try {
            console.log(`Refreshing TS channel ${channelId} for user ${userId}`);

            // Get the TS channel
            const channel = await this.getTsChannel(channelId, userId);
            if (!channel) {
                throw new Error(`TS Channel ${channelId} not found`);
            }

            console.log(`Refreshing videos for TS channel: ${channel.channel_title}`);

            // Convert TsChannel to YoutubeChannel format for compatibility with existing video fetching
            const youtubeChannelFormat = {
                id: channel.id,
                userId: channel.user_id,
                channelId: channel.channel_id,
                channelTitle: channel.channel_title,
                channelUrl: channel.channel_url,
                lastRefreshTime: channel.last_refresh_time
            };

            // Use the existing fetchChannelVideos method
            const videos = await this.fetchChannelVideos(youtubeChannelFormat);
            console.log(`Fetched ${videos.length} videos for channel ${channel.channel_title}`);

            // Apply video limit if specified
            const limitedVideos = channel.video_limit > 0 ? videos.slice(0, channel.video_limit) : videos;
            console.log(`Applying limit of ${channel.video_limit}, processing ${limitedVideos.length} videos`);

            // Save videos to TS videos table
            let newVideos = 0;
            for (const video of limitedVideos) {
                // Check if video already exists
                const existingVideo = await this.getTsVideo(video.id, userId);
                if (!existingVideo) {
                    await this.createTsVideo(userId, {
                        video_id: video.id,
                        channel_id: channel.id,
                        title: video.title || '',
                        description: video.description || '',
                        published_at: video.publishedAt || new Date(),
                        thumbnail: video.thumbnail || '',
                        duration: video.duration || '',
                        view_count: video.viewCount || 0,
                        has_transcript: false,
                        transcript_path: null
                    });
                    newVideos++;
                }
            }

            // Update channel's last refresh time
            await this.updateTsChannel(channelId, userId, {
                last_refresh_time: new Date()
            });

            console.log(`Channel refresh completed: ${newVideos} new videos added out of ${limitedVideos.length} total videos`);

        } catch (error) {
            console.error(`Error refreshing TS channel ${channelId}:`, error);
            throw error;
        }
    }

    async downloadTranscript(videoId: string, userId: number): Promise<string | null> {
        try {
            console.log(`Downloading transcript for video ${videoId} for user ${userId}`);

            // Get the video from TS videos table
            const video = await this.getTsVideo(videoId, userId);
            if (!video) {
                throw new Error(`Video ${videoId} not found in TS videos`);
            }

            // Check if transcript already exists
            if (video.has_transcript && video.transcript_path) {
                console.log(`Transcript already exists at: ${video.transcript_path}`);
                return video.transcript_path;
            }

            // Import the YouTube transcript utility
            const { fetchTranscript } = require('../utils/youtube-transcript');

            // Fetch transcript from YouTube
            const transcriptText = await fetchTranscript(videoId);
            if (!transcriptText) {
                console.log(`No transcript available for video ${videoId}`);
                return null;
            }

            // Create transcripts directory structure
            const fs = require('fs');
            const path = require('path');
            const os = require('os');

            // Create user-specific transcript directory
            const transcriptsDir = path.join(os.homedir(), 'TrendyTranscripts', `user_${userId}`);
            if (!fs.existsSync(transcriptsDir)) {
                fs.mkdirSync(transcriptsDir, { recursive: true });
            }

            // Get channel info for organizing files
            const channel = await this.getTsChannel(video.channel_id, userId);
            const channelName = channel ? channel.channel_title.replace(/[^a-zA-Z0-9]/g, '_') : 'unknown_channel';

            // Create channel-specific directory
            const channelDir = path.join(transcriptsDir, channelName);
            if (!fs.existsSync(channelDir)) {
                fs.mkdirSync(channelDir, { recursive: true });
            }

            // Create filename with video title and ID
            const videoTitle = video.title.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 50);
            const filename = `${videoTitle}_${videoId}.txt`;
            const filePath = path.join(channelDir, filename);

            // Write transcript to file
            const transcriptContent = `Video: ${video.title}\nChannel: ${channel?.channel_title || 'Unknown'}\nURL: https://www.youtube.com/watch?v=${videoId}\nDate: ${new Date().toISOString()}\n\n${transcriptText}`;
            fs.writeFileSync(filePath, transcriptContent, 'utf8');

            // Update video record with transcript info
            await this.updateTsVideo(videoId, userId, {
                has_transcript: true,
                transcript_path: filePath
            });

            console.log(`Transcript saved to: ${filePath}`);
            return filePath;

        } catch (error) {
            console.error(`Error downloading transcript for video ${videoId}:`, error);
            return null;
        }
    }
}

