import { requestQueueManager, RequestType, RequestPriority } from './request-queue-manager';
import { storage } from '../storage';
import { workerManager, TaskPriority } from './worker-manager';
import { queueChannelVideoRefresh } from './task-queue';

// Initialize request handlers
export function initializeRequestHandlers() {
  // Register handler for video loading
  requestQueueManager.registerHandler(RequestType.VIDEO_LOAD, async (request) => {
    console.log(`Handling video load request for video ID: ${request.data.videoId}`);
    
    // Prioritize this request over refresh operations
    requestQueueManager.prioritizeVideoLoading();
    requestQueueManager.deprioritizeRefreshRequests();
    
    // Get the video
    const video = await storage.getYoutubeVideo(request.data.videoId);
    return video;
  });
  
  // Register handler for video refresh
  requestQueueManager.registerHandler(RequestType.VIDEO_REFRESH, async (request) => {
    console.log(`Handling video refresh request for user ID: ${request.userId}`);
    
    // Get the user
    const user = await storage.getUser(request.userId);
    if (!user) {
      throw new Error(`User not found: ${request.userId}`);
    }
    
    // Refresh videos
    const videos = await storage.refreshVideos(user);
    return videos;
  });
  
  // Register handler for group refresh
  requestQueueManager.registerHandler(RequestType.GROUP_REFRESH, async (request) => {
    console.log(`Handling group refresh request for group ID: ${request.data.groupId}`);
    
    // Get the group
    const group = await storage.getKeywordGroup(request.data.groupId);
    if (!group) {
      throw new Error(`Keyword group not found: ${request.data.groupId}`);
    }
    
    // Check if the group belongs to the user
    if (group.userId !== request.userId) {
      throw new Error(`User ${request.userId} doesn't have permission to refresh group ${request.data.groupId}`);
    }
    
    // Refresh group videos
    const videos = await storage.refreshGroupVideos(request.data.groupId, request.userId);
    return videos;
  });
  
  // Register handler for channel refresh
  requestQueueManager.registerHandler(RequestType.CHANNEL_REFRESH, async (request) => {
    console.log(`Handling channel refresh request for channel ID: ${request.data.channelId}`);
    
    // Queue a task for channel refresh
    const taskId = await queueChannelVideoRefresh(request.data.channelId, request.userId);
    
    // Get the channel
    const channel = await storage.getYoutubeChannel(request.data.channelId);
    if (!channel) {
      throw new Error(`Channel not found: ${request.data.channelId}`);
    }
    
    // Get existing videos
    const existingVideos = await storage.getYoutubeChannelVideos(request.data.channelId);
    
    return {
      videos: existingVideos,
      message: "Channel refresh has been queued and will be processed in the background.",
      taskId
    };
  });
}

// Start the request queue manager
export function startRequestQueueManager() {
  initializeRequestHandlers();
  requestQueueManager.start();
  
  // Clean up old requests periodically
  setInterval(() => {
    requestQueueManager.cleanupRequests();
  }, 6 * 60 * 60 * 1000); // Clean up every 6 hours
  
  return requestQueueManager;
}
