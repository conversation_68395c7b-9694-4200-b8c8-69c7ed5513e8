import { Router } from 'express';
import { storage } from '../storage';
import { ensureAuthenticated } from '../middleware/auth';

// Helper function for consistent logging
const logWithTimestamp = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] 🔖 OpenRouterPrompts: ${message}`, data ? data : '');
};

const router = Router();

// Get all prompts for the current user
router.get('/', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    logWithTimestamp(`GET /api/openrouter-prompts - Fetching all prompts for user ${userId}`);

    if (!userId) {
      logWithTimestamp('GET /api/openrouter-prompts - Unauthorized: No user ID');
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const prompts = await storage.getOpenRouterPrompts(userId);
    logWithTimestamp(`GET /api/openrouter-prompts - Found ${prompts.length} prompts for user ${userId}`,
      prompts.map(p => ({ id: p.id, name: p.name })));
    res.json(prompts);
  } catch (error) {
    logWithTimestamp('GET /api/openrouter-prompts - Error getting prompts', error);
    console.error('Error getting OpenRouter prompts:', error);
    res.status(500).json({ message: 'Error getting OpenRouter prompts' });
  }
});

// Get a specific prompt
router.get('/:id', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const promptId = parseInt(req.params.id);
    const prompt = await storage.getOpenRouterPrompt(promptId);

    if (!prompt) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    // Check if the prompt belongs to the current user
    if (prompt.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    res.json(prompt);
  } catch (error) {
    console.error('Error getting OpenRouter prompt:', error);
    res.status(500).json({ message: 'Error getting OpenRouter prompt' });
  }
});

// Create a new prompt
router.post('/', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { name, promptText, isDefault } = req.body;

    if (!name || !promptText) {
      return res.status(400).json({ message: 'Name and prompt text are required' });
    }

    const prompt = await storage.createOpenRouterPrompt(userId, name, promptText, '', isDefault);
    res.status(201).json(prompt);
  } catch (error) {
    console.error('Error creating OpenRouter prompt:', error);
    res.status(500).json({ message: 'Error creating OpenRouter prompt' });
  }
});

// Update a prompt
router.put('/:id', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    const promptId = parseInt(req.params.id);
    logWithTimestamp(`PUT /api/openrouter-prompts/${promptId} - Updating prompt for user ${userId}`);

    if (!userId) {
      logWithTimestamp(`PUT /api/openrouter-prompts/${promptId} - Unauthorized: No user ID`);
      return res.status(401).json({ message: 'Unauthorized' });
    }

    logWithTimestamp(`PUT /api/openrouter-prompts/${promptId} - Fetching prompt to update`);
    const prompt = await storage.getOpenRouterPrompt(promptId);

    if (!prompt) {
      logWithTimestamp(`PUT /api/openrouter-prompts/${promptId} - Prompt not found`);
      return res.status(404).json({ message: 'Prompt not found' });
    }

    // Check if the prompt belongs to the current user
    if (prompt.userId !== userId) {
      logWithTimestamp(`PUT /api/openrouter-prompts/${promptId} - Forbidden: Prompt belongs to user ${prompt.userId}, not ${userId}`);
      return res.status(403).json({ message: 'Forbidden' });
    }

    const { name, promptText, isDefault } = req.body;
    const updates: any = {};

    if (name !== undefined) updates.name = name;
    if (promptText !== undefined) updates.promptText = promptText;
    if (isDefault !== undefined) updates.isDefault = isDefault;

    logWithTimestamp(`PUT /api/openrouter-prompts/${promptId} - Updating prompt with changes`, {
      nameChanged: name !== undefined,
      textChanged: promptText !== undefined,
      isDefaultChanged: isDefault !== undefined,
      newName: name,
      textLength: promptText ? promptText.length : 'unchanged',
      isDefault
    });

    const updatedPrompt = await storage.updateOpenRouterPrompt(promptId, updates);
    logWithTimestamp(`PUT /api/openrouter-prompts/${promptId} - Successfully updated prompt`, {
      id: updatedPrompt.id,
      name: updatedPrompt.name,
      isDefault: updatedPrompt.isDefault
    });

    res.json(updatedPrompt);
  } catch (error) {
    const promptId = req.params.id;
    logWithTimestamp(`PUT /api/openrouter-prompts/${promptId} - Error updating prompt`, error);
    console.error('Error updating OpenRouter prompt:', error);
    res.status(500).json({ message: 'Error updating OpenRouter prompt' });
  }
});

// Delete a prompt
router.delete('/:id', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const promptId = parseInt(req.params.id);
    const prompt = await storage.getOpenRouterPrompt(promptId);

    if (!prompt) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    // Check if the prompt belongs to the current user
    if (prompt.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    await storage.deleteOpenRouterPrompt(promptId);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting OpenRouter prompt:', error);
    res.status(500).json({ message: 'Error deleting OpenRouter prompt' });
  }
});

// Set a prompt as default
router.post('/:id/set-default', ensureAuthenticated, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const promptId = parseInt(req.params.id);
    const prompt = await storage.getOpenRouterPrompt(promptId);

    if (!prompt) {
      return res.status(404).json({ message: 'Prompt not found' });
    }

    // Check if the prompt belongs to the current user
    if (prompt.userId !== userId) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    await storage.setDefaultOpenRouterPrompt(userId, promptId);

    // Update user settings to use this prompt directly
    try {
      // Use a direct SQL update instead of going through getSettings/updateSettings
      const db = storage.db;
      const updateStmt = db.prepare(`
        UPDATE settings
        SET selected_prompt_id = ?
        WHERE user_id = ?
      `);
      updateStmt.run(promptId, userId);
    } catch (updateError) {
      console.error('Error updating settings with selected prompt:', updateError);
      // Continue anyway since the prompt is already set as default
    }

    res.status(200).json({ message: 'Prompt set as default' });
  } catch (error) {
    console.error('Error setting default OpenRouter prompt:', error);
    res.status(500).json({ message: 'Error setting default OpenRouter prompt' });
  }
});

export default router;
