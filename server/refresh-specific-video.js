import axios from 'axios';
import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';
import * as cheerio from 'cheerio';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Use an absolute path for the database file
const dbPath = path.resolve(__dirname, '../data.db');
console.log('Using database path:', dbPath);

// The video ID to refresh
const videoId = 'sEQjiBL94mA';

/**
 * Checks if a YouTube video is unavailable (deleted, private, etc.)
 * @param videoId The YouTube video ID
 * @returns True if the video is unavailable, false otherwise
 */
async function isVideoUnavailable(videoId) {
  try {
    console.log(`Checking if video ${videoId} is unavailable`);

    // First try the oembed endpoint which is lightweight and returns 404 for unavailable videos
    try {
      const oembedResponse = await axios.get(`https://www.youtube.com/oembed?url=http://www.youtube.com/watch?v=${videoId}&format=json`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        },
        validateStatus: (status) => status < 500, // Don't throw for 4xx errors
      });

      // If we get a 404 or other error status, the video is unavailable
      if (oembedResponse.status !== 200) {
        console.log(`Video ${videoId} is unavailable (oembed status: ${oembedResponse.status})`);
        return true;
      }

      // If we get a 200 response, the video is available
      return false;
    } catch (oembedError) {
      console.log(`Error checking oembed for video ${videoId}, falling back to page check:`, oembedError.message);
      // Fall back to checking the actual page
    }

    // If oembed check fails, try fetching the actual video page
    const response = await axios.get(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      }
    });

    const html = response.data;

    // Check for common indicators of unavailable videos
    const unavailableIndicators = [
      'This video isn\'t available anymore',
      'Video unavailable',
      'This video has been removed',
      'This video is private',
      'This video is no longer available',
      'This video has been removed by the uploader',
      '<title>YouTube</title>', // Empty title often indicates unavailable video
      'PLAYABILITY_STATUS_REASON_NOT_FOUND',
      'PLAYABILITY_STATUS_REASON_PRIVATE',
      'PLAYABILITY_STATUS_REASON_REMOVED',
      'PLAYABILITY_STATUS_REASON_COPYRIGHT',
      '"status":"ERROR"',
      '"status":"UNPLAYABLE"',
      'class="ytp-error"'
    ];

    for (const indicator of unavailableIndicators) {
      if (html.includes(indicator)) {
        console.log(`Video ${videoId} is unavailable (found indicator: "${indicator}")`);
        return true;
      }
    }

    // If we didn't find any unavailable indicators, check if we can find view count
    // A video without a view count is likely unavailable
    const viewCountPatterns = [
      /videoViewCountRenderer.*?(\d+[\d,]*)\s+views/i,
      /"viewCount":\s*"(\d+)"/,
      /"viewCount":\s*{\s*"simpleText":\s*"([\d,]+)\s+views"/i,
      /"text":\s*"([\d,]+)\s+views"/i,
      /(\d+[\d,]*)\s+views/i
    ];

    let hasViewCount = false;
    for (const pattern of viewCountPatterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        hasViewCount = true;
        break;
      }
    }

    if (!hasViewCount) {
      console.log(`Video ${videoId} is likely unavailable (no view count found)`);
      return true;
    }

    // If we get here, the video is probably available
    console.log(`Video ${videoId} is available`);
    return false;
  } catch (error) {
    console.error(`Error checking if video ${videoId} is unavailable:`, error);
    // If we can't determine, assume it's unavailable to be safe
    return true;
  }
}

/**
 * Fetches the view count for a YouTube video by scraping the watch page
 * @param videoId The YouTube video ID
 * @returns The view count as a number, or 0 if it couldn't be retrieved
 */
async function fetchVideoViewCount(videoId) {
  try {
    console.log(`Fetching view count for video ${videoId}`);

    // Use a modern user agent
    const response = await axios.get(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      }
    });

    const html = response.data;
    const $ = cheerio.load(html);

    // Method 1: Try to extract view count from the JSON-LD script
    try {
      const scriptTags = $('script[type="application/ld+json"]');
      for (let i = 0; i < scriptTags.length; i++) {
        const scriptContent = $(scriptTags[i]).html();
        if (scriptContent) {
          const jsonData = JSON.parse(scriptContent);
          if (jsonData && jsonData.interactionStatistic && Array.isArray(jsonData.interactionStatistic)) {
            for (const stat of jsonData.interactionStatistic) {
              if (stat.interactionType === 'http://schema.org/WatchAction' && stat.userInteractionCount) {
                const viewCount = parseInt(stat.userInteractionCount, 10);
                if (!isNaN(viewCount)) {
                  console.log(`Found view count in JSON-LD: ${viewCount} for video ${videoId}`);
                  return viewCount;
                }
              }
            }
          }
        }
      }
    } catch (jsonError) {
      console.log(`Error parsing JSON-LD for video ${videoId}:`, jsonError);
    }

    // Method 2: Try to extract view count from the page using regex patterns
    const viewCountPatterns = [
      // Most reliable patterns based on testing - prioritize these
      /videoViewCountRenderer.*?(\d+[\d,]*)\s+views/i, // Format in some parts of the page (most reliable)
      /"viewCount":\s*"(\d+)"/,                  // Standard format in YouTube's JSON
      /"viewCount":\s*{\s*"simpleText":\s*"([\d,]+)\s+views"/i, // Another common format
      /"text":\s*"([\d,]+)\s+views"/i,           // Yet another format
      /aria-label="([\d,]+) views"/i,            // Aria label format

      // Try to extract from YouTube API data embedded in the page
      /"viewCount":\s*(\d+)/,                    // Direct API format

      // More generic pattern - use with caution as it might match other numbers
      /(\d+[\d,]*)\s+views/i,                    // Simple text format
    ];

    // Function to validate view count is reasonable
    const isReasonableViewCount = (count) => {
      // YouTube videos typically don't have more than a few billion views
      // This helps filter out parsing errors that might result in unreasonably large numbers

      // Basic range check
      if (count < 0 || count > 10000000000) { // 10 billion max
        console.log(`Rejecting unreasonable view count: ${count} for video ${videoId}`);
        return false;
      }

      return true;
    };

    let bestViewCount = 0;
    let bestPattern = '';
    let allFoundViewCounts = [];

    // Try all patterns and collect all reasonable results
    for (const pattern of viewCountPatterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        // Remove commas from the number string
        const cleanNumber = match[1].replace(/,/g, '');
        const viewCount = parseInt(cleanNumber, 10);

        if (!isNaN(viewCount) && isReasonableViewCount(viewCount)) {
          console.log(`Found view count with pattern ${pattern}: ${viewCount} for video ${videoId}`);
          allFoundViewCounts.push(viewCount);

          // If this is our first valid match or it's more reasonable than previous matches, use it
          if (bestViewCount === 0 ||
              (viewCount > 0 && viewCount < bestViewCount)) {
            bestViewCount = viewCount;
            bestPattern = pattern.toString();
          }
        }
      }
    }

    // If we have multiple view counts, do additional validation
    if (allFoundViewCounts.length > 1) {
      console.log(`Found ${allFoundViewCounts.length} different view counts for video ${videoId}: ${allFoundViewCounts.join(', ')}`);

      // Sort the view counts
      allFoundViewCounts.sort((a, b) => a - b);

      // Calculate the frequency of each view count
      const countFrequency = {};
      allFoundViewCounts.forEach(count => {
        countFrequency[count] = (countFrequency[count] || 0) + 1;
      });

      // Find the most common view count
      let mostCommonCount = 0;
      let highestFrequency = 0;

      for (const count in countFrequency) {
        if (countFrequency[count] > highestFrequency) {
          mostCommonCount = parseInt(count, 10);
          highestFrequency = countFrequency[count];
        }
      }

      // If there's a clear winner (appears more than once), use it
      if (highestFrequency > 1) {
        console.log(`Using most common value: ${mostCommonCount} (appeared ${highestFrequency} times)`);
        bestViewCount = mostCommonCount;
      }
      // If there's no clear winner, check for outliers
      else if (allFoundViewCounts.length >= 3) {
        const smallestValue = allFoundViewCounts[0];
        const largestValue = allFoundViewCounts[allFoundViewCounts.length - 1];

        if (largestValue > smallestValue * 10) {
          console.log(`Detected outlier: ${largestValue} is more than 10x larger than ${smallestValue}`);
          // Use the median value instead
          const medianIndex = Math.floor(allFoundViewCounts.length / 2);
          const medianValue = allFoundViewCounts[medianIndex];
          console.log(`Using median value: ${medianValue} instead of outlier ${largestValue}`);
          bestViewCount = medianValue;
        }
        // If no outliers, use the smallest value as it's likely the most accurate
        else {
          console.log(`No clear winner or outliers, using smallest value: ${smallestValue}`);
          bestViewCount = smallestValue;
        }
      }
      // If we only have 2 values and no clear winner, use the smaller one
      else if (allFoundViewCounts.length === 2) {
        const smallerValue = allFoundViewCounts[0];
        const largerValue = allFoundViewCounts[1];

        // If the larger value is significantly higher, it might be an outlier
        if (largerValue > smallerValue * 5) {
          console.log(`Using smaller value ${smallerValue} instead of potentially inflated ${largerValue}`);
          bestViewCount = smallerValue;
        }
        // Otherwise use the average of the two
        else {
          const averageValue = Math.round((smallerValue + largerValue) / 2);
          console.log(`Using average of two values: ${averageValue}`);
          bestViewCount = averageValue;
        }
      }
    }

    if (bestViewCount > 0) {
      console.log(`Using best view count match: ${bestViewCount} (pattern: ${bestPattern}) for video ${videoId}`);
      return bestViewCount;
    }

    console.log(`Could not find view count for video ${videoId} using any method`);
    return 0;
  } catch (error) {
    console.error(`Error fetching view count for video ${videoId}:`, error);
    return 0;
  }
}

async function refreshVideo() {
  try {
    console.log(`Refreshing video ${videoId}`);

    // Open the database
    const db = new Database(dbPath);

    // First check if the video exists in the database
    const videoExists = db.prepare('SELECT 1 FROM youtube_videos WHERE id = ?').get(videoId);

    if (!videoExists) {
      console.log(`Video ${videoId} not found in database`);
      db.close();
      return;
    }

    // Check if the video is unavailable
    const unavailable = await isVideoUnavailable(videoId);

    if (unavailable) {
      console.log(`Video ${videoId} is unavailable, deleting from database`);

      // Delete the video from the database
      const deleteResult = db.prepare('DELETE FROM youtube_videos WHERE id = ?').run(videoId);
      console.log(`Deleted video ${videoId} from database (${deleteResult.changes} rows affected)`);

      db.close();
      return 'deleted';
    }

    // Get the current view count from the database
    const currentViewCount = db.prepare('SELECT view_count FROM youtube_videos WHERE id = ?').get(videoId);
    console.log(`Current view count in database: ${currentViewCount?.view_count || 'not found'}`);

    // Fetch the correct view count using our improved logic
    const newViewCount = await fetchVideoViewCount(videoId);

    if (newViewCount === 0) {
      console.log(`Could not get view count for video ${videoId}`);
      db.close();
      return;
    }

    console.log(`Fetched new view count: ${newViewCount} for video ${videoId}`);

    // Update the view count in the database
    const result = db.prepare('UPDATE youtube_videos SET view_count = ? WHERE id = ?').run(newViewCount, videoId);

    console.log(`Updated view count for video ${videoId} to ${newViewCount} (${result.changes} rows affected)`);

    // Verify the update
    const updatedViewCount = db.prepare('SELECT view_count FROM youtube_videos WHERE id = ?').get(videoId);
    console.log(`New view count in database: ${updatedViewCount?.view_count || 'not found'}`);

    // Close the database connection
    db.close();

    return newViewCount;
  } catch (error) {
    console.error(`Error refreshing video ${videoId}:`, error);
  }
}

refreshVideo()
  .then(result => {
    if (result === 'deleted') {
      console.log(`Successfully deleted unavailable video ${videoId} from database`);
    } else if (result) {
      console.log(`Successfully refreshed view count for video ${videoId} to ${result}`);
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
