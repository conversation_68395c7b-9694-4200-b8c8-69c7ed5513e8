// A singleton wrapper for the Cast API to prevent multiple initializations

// Track initialization state
let isInitialized = false;
let isInitializing = false;
let initPromise: Promise<void> | null = null;
let castApiReady = false;

// Store callbacks to be called when the API is ready
const readyCallbacks: Array<() => void> = [];

// Initialize the Cast API only once
export function initializeCastApiSingleton(): Promise<void> {
  // If already initialized, return immediately
  if (castApiReady) {
    console.log('Cast API already initialized and ready');
    return Promise.resolve();
  }

  // If initialization is in progress, return the existing promise
  if (isInitializing && initPromise) {
    console.log('Cast API initialization already in progress');
    return initPromise;
  }

  // Start initialization
  isInitializing = true;
  
  // Create a new promise for initialization
  initPromise = new Promise<void>((resolve, reject) => {
    // Function to initialize the Cast API
    const initializeApi = () => {
      try {
        // Check if the Cast framework is already initialized
        if (window.chrome && window.chrome.cast && window.chrome.cast.isAvailable) {
          console.log('Cast API already available');
          castApiReady = true;
          
          // Call all registered callbacks
          readyCallbacks.forEach(callback => {
            try {
              callback();
            } catch (e) {
              console.error('Error in Cast API ready callback:', e);
            }
          });
          
          resolve();
          return;
        }
        
        // Set up the receiver ID and initialize options
        const applicationID = chrome.cast.media.DEFAULT_MEDIA_RECEIVER_APP_ID;
        const sessionRequest = new chrome.cast.SessionRequest(applicationID);
        
        const apiConfig = new chrome.cast.ApiConfig(
          sessionRequest,
          (session) => {
            console.log('Cast session initialized:', session);
          },
          (availability) => {
            console.log('Cast availability changed:', availability);
          },
          chrome.cast.AutoJoinPolicy.ORIGIN_SCOPED
        );
        
        // Initialize the Cast API
        chrome.cast.initialize(
          apiConfig,
          () => {
            console.log('Cast API initialized successfully');
            castApiReady = true;
            isInitialized = true;
            isInitializing = false;
            
            // Call all registered callbacks
            readyCallbacks.forEach(callback => {
              try {
                callback();
              } catch (e) {
                console.error('Error in Cast API ready callback:', e);
              }
            });
            
            resolve();
          },
          (error) => {
            console.error('Cast API initialization error:', error);
            isInitializing = false;
            reject(error);
          }
        );
      } catch (error) {
        console.error('Error during Cast API initialization:', error);
        isInitializing = false;
        reject(error);
      }
    };
    
    // Function to handle when the Cast API becomes available
    const onCastApiAvailable = (available: boolean) => {
      if (available) {
        console.log('Cast API is now available');
        initializeApi();
      } else {
        console.log('Cast API is not available');
        isInitializing = false;
        reject(new Error('Cast API not available'));
      }
    };
    
    // Check if the Cast API script is already loaded
    if (window.chrome && window.chrome.cast && window.chrome.cast.isAvailable) {
      console.log('Cast API already available, initializing directly');
      initializeApi();
      return;
    }
    
    // Set up the global callback for when the Cast API is ready
    window.__onGCastApiAvailable = onCastApiAvailable;
    
    // Check if the Cast API script is already being loaded
    if (document.querySelector('script[src*="cast_sender.js"]')) {
      console.log('Cast API script already loading, waiting for availability');
      return;
    }
    
    // Load the Cast API script
    console.log('Loading Cast API script');
    const script = document.createElement('script');
    script.src = 'https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1';
    document.head.appendChild(script);
  });
  
  return initPromise;
}

// Register a callback to be called when the Cast API is ready
export function onCastApiReady(callback: () => void): void {
  if (castApiReady) {
    // If already ready, call immediately
    callback();
  } else {
    // Otherwise, store for later
    readyCallbacks.push(callback);
  }
}

// Check if the Cast API is ready
export function isCastApiReady(): boolean {
  return castApiReady;
}

// Declare the global callback type
declare global {
  interface Window {
    __onGCastApiAvailable?: (available: boolean) => void;
    chrome?: any;
  }
}
