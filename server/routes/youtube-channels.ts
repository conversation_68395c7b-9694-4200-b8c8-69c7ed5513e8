import { Router } from 'express';
import { storage } from '../storage';
import { z } from 'zod';
import axios from 'axios';
import * as cheerio from 'cheerio';
import { YoutubeChannel, YoutubeVideo } from '@shared/schema';
import { analyzeTranscriptForFinancialBenefits, FinancialCategory } from '../services/financial-analysis';
import { enhancedFinancialAnalysis } from '../services/financial-analysis-integration';
import { analyzeTranscriptWithOllama, convertOllamaAnalysisToAppFormat } from '../services/ollama-financial-analysis';
import Database from 'better-sqlite3';
import { queueTranscriptionsForVideos, queueChannelVideoRefresh, queueFinancialAnalysis } from '../services/task-queue';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

const router = Router();

// Schema for creating/updating a YouTube channel
const youtubeChannelSchema = z.object({
  channelId: z.string().min(1, "Channel ID is required"),
  channelTitle: z.string().min(1, "Channel title is required"),
  channelUrl: z.string().url("Valid channel URL is required"),
  thumbnail: z.string().optional(),
  description: z.string().optional(),
  videoLimit: z.number().min(1).max(15).optional(),
});

// Schema for batch importing YouTube channels
const batchImportSchema = z.array(
  z.object({
    channelId: z.string().min(1, "Channel ID is required"),
    channelTitle: z.string().min(1, "Channel title is required"),
    channelUrl: z.string().url("Valid channel URL is required"),
    videoLimit: z.number().min(1).max(15).optional(),
  })
);

// Schema for importing all data (channels and videos)
const allDataImportSchema = z.object({
  version: z.string(),
  exportDate: z.string(),
  channels: z.array(
    z.object({
      channelId: z.string().min(1, "Channel ID is required"),
      channelTitle: z.string().min(1, "Channel title is required"),
      channelUrl: z.string().url("Valid channel URL is required"),
      videoLimit: z.number().min(1).max(15).optional(),
      thumbnail: z.string().optional(),
      description: z.string().optional(),
      subscriberCount: z.number().optional(),
      videos: z.array(
        z.object({
          id: z.string().min(1, "Video ID is required"),
          title: z.string(),
          description: z.string().optional(),
          publishedAt: z.string(),
          thumbnail: z.string().optional(),
          duration: z.string().optional(),
          transcription: z.string().optional(),
          hasTranscription: z.boolean().optional(),
          hasFinancialAnalysis: z.boolean().optional(),
          financialScore: z.number().optional(),
          financialCategory: z.string().optional(),
          financialAmount: z.string().optional(),
          financialTimeline: z.string().optional(),
          financialRecipients: z.string().optional(),
          financialSteps: z.string().optional(),
          financialViralPotential: z.string().optional(),
          financialSkepticism: z.string().optional(),
          financialAnalysis: z.string().optional(),
          financialTimestamps: z.string().optional(),
          openRouterBenefitAmounts: z.array(z.string()).optional(),
          openRouterRawData: z.string().optional(),
          openRouterModelUsed: z.string().optional(),
        })
      ).optional()
    })
  )
});

// Get all videos
router.get('/videos', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Fetching all videos');

    // Get all videos
    const db = (storage as any).db;
    if (!db) {
      return res.status(500).json({ message: 'Database not available' });
    }

    const stmt = db.prepare(`
      SELECT id, title, channelTitle, description, publishedAt, thumbnail, duration,
             has_transcription as hasTranscription, has_financial_analysis as hasFinancialAnalysis
      FROM youtube_videos
      ORDER BY publishedAt DESC
      LIMIT 100
    `);

    const rows = stmt.all();
    if (!rows || rows.length === 0) {
      return res.status(404).json({ message: 'No videos found' });
    }

    console.log(`Server: Found ${rows.length} videos`);
    res.json(rows);
  } catch (error) {
    console.error('Error fetching videos:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to fetch videos: ${errorMessage}` });
  }
});

// Get videos with transcriptions
router.get('/videos-with-transcriptions', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Fetching videos with transcriptions');

    // Get videos with transcriptions
    const db = (storage as any).db;
    if (!db) {
      return res.status(500).json({ message: 'Database not available' });
    }

    const stmt = db.prepare(`
      SELECT id, title, channelTitle, description, publishedAt, thumbnail, duration, has_transcription, has_financial_analysis
      FROM youtube_videos
      WHERE has_transcription = 1
      ORDER BY publishedAt DESC
      LIMIT 100
    `);

    const rows = stmt.all();
    if (!rows || rows.length === 0) {
      return res.status(404).json({ message: 'No videos with transcriptions found' });
    }

    console.log(`Server: Found ${rows.length} videos with transcriptions`);
    res.json(rows);
  } catch (error) {
    console.error('Error fetching videos with transcriptions:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to fetch videos with transcriptions: ${errorMessage}` });
  }
});

// Analyze a specific video
router.post('/analyze-video', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { videoId } = req.body;
    if (!videoId) {
      return res.status(400).json({ message: 'Video ID is required' });
    }

    console.log(`Server: Analyzing video ${videoId}`);

    // Get the video details
    const db = (storage as any).db;
    if (!db) {
      return res.status(500).json({ message: 'Database not available' });
    }

    const stmt = db.prepare(`
      SELECT id, title, description, transcription FROM youtube_videos
      WHERE id = ? AND has_transcription = 1
    `);

    const row = stmt.get(videoId);
    if (!row) {
      return res.status(404).json({ message: 'Video not found or has no transcription' });
    }

    const title = row.title;
    const description = row.description;
    const transcription = row.transcription;

    console.log(`Server: Found video: ${videoId}`);
    console.log(`Server: Title: ${title}`);
    console.log(`Server: Transcription length: ${transcription?.length || 0}`);

    // Check if the video already has a financial analysis (for logging purposes only)
    const existingAnalysisStmt = db.prepare(`
      SELECT has_financial_analysis
      FROM youtube_videos
      WHERE id = ?
    `);

    const existingAnalysis = existingAnalysisStmt.get(videoId);

    // Log whether we're overwriting an existing analysis
    if (existingAnalysis && existingAnalysis.has_financial_analysis) {
      console.log(`Server: Overwriting existing financial analysis for video ${videoId}`);
    } else {
      console.log(`Server: Creating new financial analysis for video ${videoId}`);
    }

    // Analyze the transcription
    console.log(`Server: Performing new analysis for video ${videoId}`);
    const analysis = await analyzeTranscriptForFinancialBenefits(
      transcription,
      title,
      description
    );

    // Save the analysis to the database
    await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
      financialScore: analysis.score,
      financialCategory: analysis.category,
      financialAmount: analysis.amount,
      financialTimeline: analysis.timeline,
      financialRecipients: analysis.recipients,
      financialSteps: analysis.steps,
      financialViralPotential: analysis.viralPotential,
      financialSkepticism: analysis.skepticism,
      financialAnalysis: analysis.analysis,
      financialTimestamps: analysis.timestamps,
      hasFinancialAnalysis: true
    });

    console.log(`Server: Financial analysis completed for video ${videoId}`);
    res.json({ success: true, videoId, analysis, cached: false });
  } catch (error) {
    console.error('Error analyzing video:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to analyze video: ${errorMessage}` });
  }
});

// Test endpoint for financial analysis
router.get('/test-financial-analysis', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Testing financial analysis functionality (legacy endpoint)');

    // Get a video with a transcription
    const db = (storage as any).db;
    if (!db) {
      return res.status(500).json({ message: 'Database not available' });
    }

    const stmt = db.prepare(`
      SELECT id, title, description, transcription FROM youtube_videos
      WHERE has_transcription = 1
      LIMIT 1
    `);

    const row = stmt.get();
    if (!row) {
      return res.status(404).json({ message: 'No videos with transcriptions found' });
    }

    const videoId = row.id;
    const title = row.title;
    const description = row.description;
    const transcription = row.transcription;

    console.log(`Server: Found video with transcription: ${videoId}`);
    console.log(`Server: Title: ${title}`);
    console.log(`Server: Transcription length: ${transcription?.length || 0}`);

    // Analyze the transcription
    console.log(`Server: Analyzing transcription for video ${videoId}`);
    const analysis = await analyzeTranscriptForFinancialBenefits(
      transcription,
      title,
      description
    );

    // Save the analysis to the database
    await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
      financialScore: analysis.score,
      financialCategory: analysis.category,
      financialAmount: analysis.amount,
      financialTimeline: analysis.timeline,
      financialRecipients: analysis.recipients,
      financialSteps: analysis.steps,
      financialViralPotential: analysis.viralPotential,
      financialSkepticism: analysis.skepticism,
      financialAnalysis: analysis.analysis,
      financialTimestamps: analysis.timestamps,
      hasFinancialAnalysis: true
    });

    console.log(`Server: Financial analysis completed for video ${videoId}`);
    res.json({ success: true, videoId, analysis });
  } catch (error) {
    console.error('Error testing financial analysis:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to test financial analysis: ${errorMessage}` });
  }
});



// Test Ollama connection
router.get('/test-ollama-connection', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Testing Ollama connection...');
    const response = await fetch('http://localhost:11434/api/tags');

    if (!response.ok) {
      console.error(`Ollama server returned error: ${response.status}`);
      return res.status(500).json({
        message: "Failed to connect to Ollama server",
        status: response.status,
        statusText: response.statusText
      });
    }

    const data = await response.json();
    console.log(`Ollama server is running with ${data.models.length} models available`);

    return res.json({
      status: "success",
      message: "Ollama server is running",
      models: data.models.map((model: any) => model.name)
    });
  } catch (error) {
    console.error('Error testing Ollama connection:', error);
    return res.status(500).json({
      message: "Failed to connect to Ollama server",
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get available Ollama models
router.get('/ollama-models', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { getAvailableOllamaModels } = require('../services/ollama-service');
    const models = await getAvailableOllamaModels();
    res.json({ models });
  } catch (error) {
    console.error('Error fetching Ollama models:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to fetch Ollama models: ${errorMessage}` });
  }
});

// Export all channel and video data
router.get('/export-all-data', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Exporting all channel and video data');

    // Get all channels for the user
    const channels = await storage.getYoutubeChannels(req.user.id);

    if (channels.length === 0) {
      return res.status(404).json({ message: 'No channels found to export' });
    }

    // Create a temporary directory for the export
    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'youtube-export-'));
    const exportFilePath = path.join(tempDir, `youtube-export-${new Date().toISOString().split('T')[0]}.json`);

    // Prepare the export data structure
    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      channels: []
    };

    // Process each channel and its videos
    for (const channel of channels) {
      console.log(`Processing channel: ${channel.channelTitle}`);

      // Get videos for this channel
      const videos = await storage.getYoutubeChannelVideos(channel.id);
      console.log(`Found ${videos.length} videos for channel ${channel.channelTitle}`);

      // Format channel data for export
      const channelData = {
        channelId: channel.channelId,
        channelTitle: channel.channelTitle,
        channelUrl: channel.channelUrl,
        videoLimit: channel.videoLimit || 15,
        thumbnail: channel.thumbnail,
        description: channel.description,
        subscriberCount: channel.subscriberCount,
        videos: videos.map(video => ({
          id: video.id,
          title: video.title,
          description: video.description,
          publishedAt: video.publishedAt.toISOString(),
          thumbnail: video.thumbnail,
          duration: video.duration,
          transcription: video.transcription,
          hasTranscription: video.hasTranscription,
          hasFinancialAnalysis: video.hasFinancialAnalysis,
          financialScore: video.financialScore,
          financialCategory: video.financialCategory,
          financialAmount: video.financialAmount,
          financialTimeline: video.financialTimeline,
          financialRecipients: video.financialRecipients,
          financialSteps: video.financialSteps,
          financialViralPotential: video.financialViralPotential,
          financialSkepticism: video.financialSkepticism,
          financialAnalysis: video.financialAnalysis,
          financialTimestamps: video.financialTimestamps,
          openRouterBenefitAmounts: video.openRouterBenefitAmounts,
          openRouterRawData: video.openRouterRawData,
          openRouterModelUsed: video.openRouterModelUsed
        }))
      };

      exportData.channels.push(channelData);
    }

    // Write the export data to a file
    fs.writeFileSync(exportFilePath, JSON.stringify(exportData, null, 2));

    // Set headers for file download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename=youtube-export-${new Date().toISOString().split('T')[0]}.json`);

    // Send the file
    res.sendFile(exportFilePath, {}, (err) => {
      if (err) {
        console.error('Error sending export file:', err);
      }

      // Clean up the temporary file
      try {
        fs.unlinkSync(exportFilePath);
        fs.rmdirSync(tempDir);
      } catch (cleanupError) {
        console.error('Error cleaning up temporary files:', cleanupError);
      }
    });
  } catch (error) {
    console.error('Error exporting channel and video data:', error);
    res.status(500).json({ message: "Failed to export channel and video data" });
  }
});

// Import all channel and video data
router.post('/import-all-data', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Importing all channel and video data');

    // Validate the import data
    const validatedData = allDataImportSchema.parse(req.body);

    // Get existing channels for this user to check for duplicates
    const existingChannels = await storage.getYoutubeChannels(req.user.id);

    // Track import statistics
    const stats = {
      channels: {
        total: validatedData.channels.length,
        added: 0,
        updated: 0,
        skipped: 0,
        failed: 0
      },
      videos: {
        total: 0,
        added: 0,
        updated: 0,
        skipped: 0,
        failed: 0
      },
      details: []
    };

    // Process each channel
    for (const channelData of validatedData.channels) {
      try {
        // Check if channel already exists
        const existingChannel = existingChannels.find(c => c.channelId === channelData.channelId);

        let channel;
        if (existingChannel) {
          // Update existing channel
          channel = await storage.updateYoutubeChannel(existingChannel.id, {
            channelTitle: channelData.channelTitle,
            channelUrl: channelData.channelUrl,
            videoLimit: channelData.videoLimit,
            thumbnail: channelData.thumbnail,
            description: channelData.description,
            subscriberCount: channelData.subscriberCount
          });

          stats.channels.updated++;
          stats.details.push({
            channelTitle: channelData.channelTitle,
            status: 'updated',
            message: 'Channel updated successfully'
          });
        } else {
          // Create new channel
          channel = await storage.createYoutubeChannel(req.user.id, {
            channelId: channelData.channelId,
            channelTitle: channelData.channelTitle,
            channelUrl: channelData.channelUrl,
            videoLimit: channelData.videoLimit,
            thumbnail: channelData.thumbnail,
            description: channelData.description,
            subscriberCount: channelData.subscriberCount
          });

          stats.channels.added++;
          stats.details.push({
            channelTitle: channelData.channelTitle,
            status: 'added',
            message: 'Channel added successfully'
          });
        }

        // Process videos for this channel if they exist
        if (channelData.videos && channelData.videos.length > 0) {
          stats.videos.total += channelData.videos.length;

          // Get existing videos for this channel
          const existingVideos = await storage.getYoutubeChannelVideos(channel.id);
          const existingVideoMap = new Map(existingVideos.map(v => [v.id, v]));

          // Process each video
          for (const videoData of channelData.videos) {
            try {
              const existingVideo = existingVideoMap.get(videoData.id);

              if (existingVideo) {
                // Update existing video
                // First update transcription if it exists
                if (videoData.transcription && videoData.hasTranscription) {
                  await storage.updateYoutubeVideoTranscription(videoData.id, videoData.transcription);
                }

                // Then update financial analysis if it exists
                if (videoData.hasFinancialAnalysis) {
                  await storage.updateYoutubeVideoFinancialAnalysis(videoData.id, {
                    financialScore: videoData.financialScore || 0,
                    financialCategory: videoData.financialCategory || '',
                    financialAmount: videoData.financialAmount || '',
                    financialTimeline: videoData.financialTimeline || '',
                    financialRecipients: videoData.financialRecipients || '',
                    financialSteps: videoData.financialSteps || '',
                    financialViralPotential: videoData.financialViralPotential || '',
                    financialSkepticism: videoData.financialSkepticism || '',
                    financialAnalysis: videoData.financialAnalysis || '',
                    financialTimestamps: videoData.financialTimestamps || '',
                    hasFinancialAnalysis: true,
                    openRouterBenefitAmounts: videoData.openRouterBenefitAmounts || [],
                    openRouterRawData: videoData.openRouterRawData || '',
                    openRouterModelUsed: videoData.openRouterModelUsed || ''
                  });
                }

                stats.videos.updated++;
              } else {
                // We can't directly create videos as they're normally fetched from YouTube
                // Instead, we'll log that we skipped this video
                stats.videos.skipped++;
              }
            } catch (videoError) {
              console.error(`Error processing video ${videoData.id}:`, videoError);
              stats.videos.failed++;
            }
          }
        }
      } catch (channelError) {
        console.error(`Error processing channel ${channelData.channelTitle}:`, channelError);
        stats.channels.failed++;
        stats.details.push({
          channelTitle: channelData.channelTitle,
          status: 'failed',
          message: channelError instanceof Error ? channelError.message : 'Unknown error'
        });
      }
    }

    // Return import statistics
    res.status(200).json({
      message: 'Import completed',
      stats
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Validation error", errors: error.errors });
    }

    console.error('Error importing channel and video data:', error);
    res.status(500).json({ message: "Failed to import channel and video data" });
  }
});

// Get all YouTube channels for the current user
router.get('/', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const channels = await storage.getYoutubeChannels(req.user.id);
    res.json(channels);
  } catch (error) {
    console.error('Error fetching YouTube channels:', error);
    res.status(500).json({ message: "Failed to fetch YouTube channels" });
  }
});

// Get video counts for all channels in a single efficient request
router.get('/counts', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Getting video counts for all channels');
    const channels = await storage.getYoutubeChannels(req.user.id);

    if (!channels || channels.length === 0) {
      return res.json({});
    }

    // Get counts for all channels in a single database query
    const counts = await storage.getYoutubeChannelsCounts(req.user.id);
    console.log(`Retrieved counts for ${Object.keys(counts).length} channels`);

    res.json(counts);
  } catch (error) {
    console.error('Error fetching channel video counts:', error);
    res.status(500).json({ message: "Failed to fetch channel video counts" });
  }
});

// Get all videos from all YouTube channels for the current user with pagination
router.get('/all-videos', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    // Parse pagination parameters
    const page = parseInt(req.query.page as string) || 1;
    let limit = parseInt(req.query.limit as string) || 30;

    // If the limit is very large (e.g., 1000), we're trying to fetch all videos at once
    const isFetchingAll = limit > 100;
    if (isFetchingAll) {
      console.log(`Large limit detected (${limit}), fetching all videos at once`);
    }

    const offset = (page - 1) * limit;

    // Parse sorting and filtering parameters
    const sortBy = req.query.sortBy as string || 'date_desc';
    const searchQuery = req.query.searchQuery as string || '';
    const durationFilter = req.query.durationFilter as string || 'all';
    const channelId = req.query.channelId as string || null;

    // Always log the sort parameter to help debug sorting issues
    console.log(`SERVER: Received request for videos with sort: ${sortBy}, channelId: ${channelId}`);

    // Get all channels for the user
    const channels = await storage.getYoutubeChannels(req.user.id);

    if (process.env.NODE_ENV !== 'production') {
      console.log(`Fetching paginated videos (page ${page}, limit ${limit}) for ${channels.length} channels`);
      console.log(`Sorting by: ${sortBy}, Search: "${searchQuery}"`);
    }

    // Create an array to store all videos
    let allVideos = [];

    // Check if we have a cached total count
    let totalCount = 0;

    // Option 1: Use the paginated method if available
    if (typeof storage.getAllChannelVideosPaginated === 'function') {
      // Pass all sorting and filtering parameters to the storage method
      const result = await storage.getAllChannelVideosPaginated(req.user.id, offset, limit, {
        sortBy,
        searchQuery,
        durationFilter,
        channelId
      });
      allVideos = result.videos;
      totalCount = result.totalCount;

      // No longer falling back to default sort if no videos are found
      if (allVideos.length === 0) {
        console.log(`No videos found with sort ${sortBy}, keeping the requested sort option`);
      }

      // If we still have no videos but we know there are channels, try fetching directly from each channel
      if (allVideos.length === 0 && channels.length > 0) {
        console.log(`No videos found with paginated method, trying direct channel fetch`);

        // Fetch videos for each channel sequentially
        for (const channel of channels) {
          try {
            if (allVideos.length >= limit) break; // Stop once we have enough videos

            const videos = await storage.getYoutubeChannelVideos(channel.id);

            // Add these videos to our collection (up to the limit)
            allVideos = [...allVideos, ...videos];

            console.log(`Added ${videos.length} videos from channel ${channel.channelTitle}`);
          } catch (channelError) {
            console.error(`Error fetching videos for channel ${channel.channelTitle}:`, channelError);
            // Continue with the next channel instead of failing the whole request
          }
        }

        // Update the total count
        totalCount = allVideos.length;
      }

      if (process.env.NODE_ENV !== 'production') {
        console.log(`Fetched ${allVideos.length} videos (page ${page}/${Math.ceil(totalCount/limit)})`);
      }
    }
    // Option 2: Fallback to fetching from each channel with limits
    else {
      // Fetch videos for each channel sequentially with limits to avoid overwhelming the database
      for (const channel of channels) {
        try {
          if (allVideos.length >= limit) break; // Stop once we have enough videos

          const videos = await storage.getYoutubeChannelVideos(channel.id);

          // Add these videos to our collection (up to the limit)
          allVideos = [...allVideos, ...videos];

          if (process.env.NODE_ENV !== 'production') {
            console.log(`Added ${videos.length} videos from channel ${channel.channelTitle}`);
          }
        } catch (channelError) {
          console.error(`Error fetching videos for channel ${channel.channelTitle}:`, channelError);
          // Continue with the next channel instead of failing the whole request
        }
      }
    }

    // Sort videos by publishedAt date (newest first)
    allVideos.sort((a, b) => {
      return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
    });

    // If we don't have a total count yet, estimate it based on what we know
    if (totalCount === 0) {
      // If we got fewer videos than requested, that's probably all of them
      if (allVideos.length < limit) {
        totalCount = offset + allVideos.length;
      }
      // Otherwise, we need to make an educated guess
      else {
        // Assume we have at least one more page
        totalCount = offset + allVideos.length + limit;
      }
    }

    // Log which videos belong to which channels
    if (process.env.NODE_ENV !== 'production' || true) {
      // Count videos by channel title
      const videosByChannel = {};
      allVideos.forEach(video => {
        if (!videosByChannel[video.channelTitle]) {
          videosByChannel[video.channelTitle] = 0;
        }
        videosByChannel[video.channelTitle]++;
      });

      console.log('Videos by channel:', videosByChannel);

      // Find channels that have no videos in the current result set
      const channelsWithVideos = new Set(Object.keys(videosByChannel));
      const channelsWithNoVideos = channels
        .filter(channel => !channelsWithVideos.has(channel.channelTitle))
        .map(channel => channel.channelTitle);

      console.log('These channels have no videos in the current result set:', channelsWithNoVideos);

      // Log a sample video for debugging
      if (allVideos.length > 0) {
        console.log('Sample video:', {
          title: allVideos[0].title,
          channelTitle: allVideos[0].channelTitle,
          publishedAt: allVideos[0].publishedAt
        });
      }
    }

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasMore = page < totalPages;
    const nextPage = hasMore ? page + 1 : null;

    if (process.env.NODE_ENV !== 'production') {
      // Log the number of videos found for each channel
      const videosByChannel = {};
      allVideos.forEach(video => {
        const channelTitle = video.channelTitle || 'Unknown';
        videosByChannel[channelTitle] = (videosByChannel[channelTitle] || 0) + 1;
      });

      console.log(`Returning ${allVideos.length} videos (page ${page}/${totalPages}, total: ${totalCount})`);
      console.log('Videos by channel:', videosByChannel);

      // Check if any channels have no videos
      const channelsWithVideos = Object.keys(videosByChannel);
      const allChannelTitles = channels.map(c => c.channelTitle);
      const missingChannels = allChannelTitles.filter(title => !channelsWithVideos.includes(title));

      if (missingChannels.length > 0) {
        console.warn(`These channels have no videos: ${missingChannels.join(', ')}`);
      }

      // Log a sample video if available
      if (allVideos.length > 0) {
        console.log('Sample video:', {
          title: allVideos[0].title,
          channelTitle: allVideos[0].channelTitle,
          publishedAt: allVideos[0].publishedAt
        });
      }
    }

    // Return videos with pagination metadata
    res.json({
      videos: allVideos,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasMore,
        nextPage
      }
    });
  } catch (error) {
    console.error('Error fetching all YouTube videos:', error);
    res.status(500).json({ message: "Failed to fetch all YouTube videos" });
  }
});

// Delete old videos data (older than 7 days) and vacuum the database
router.delete('/old-videos-data', async (req, res) => {
  console.log('Received request to delete old videos data (older than 7 days)');

  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    // Calculate the timestamp for 7 days ago
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const sevenDaysAgoTimestamp = sevenDaysAgo.toISOString();

    console.log(`Deleting videos older than ${sevenDaysAgoTimestamp}`);

    // Delete old videos
    const deleteStmt = storage.db.prepare(`
      DELETE FROM youtube_videos
      WHERE user_id = ? AND published_at < ?
    `);

    const result = deleteStmt.run(req.user.id, sevenDaysAgoTimestamp);
    console.log(`Deleted ${result.changes} old videos`);

    // Vacuum the database to reclaim space
    const vacuumResult = await storage.vacuumDatabase();

    res.json({
      success: true,
      deletedCount: result.changes,
      message: `Deleted ${result.changes} videos older than 7 days`,
      vacuumResult
    });
  } catch (error) {
    console.error('Error deleting old videos data:', error);
    res.status(500).json({
      success: false,
      message: "Failed to delete old videos data",
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete all channel data (videos, transcripts, financial analysis) for all channels but keep the channels
// Important: This route must be defined BEFORE the '/:id' routes to avoid conflicts
router.delete('/all-data', async (req, res) => {
  console.log('Received request to delete all channel data');

  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log(`Server: Deleting data for all YouTube channels for user ${req.user.id}`);

    // Get all channels for this user
    const channels = await storage.getYoutubeChannels(req.user.id);
    if (channels.length === 0) {
      return res.json({
        success: true,
        message: 'No channels found to delete data from'
      });
    }

    console.log(`Found ${channels.length} channels for user ${req.user.id}`);

    // Track statistics for response
    let totalVideosDeleted = 0;
    const channelsProcessed = [];

    // Process each channel
    for (const channel of channels) {
      // Get all videos for this channel
      const videos = await storage.getYoutubeChannelVideos(channel.id);
      console.log(`Found ${videos.length} videos to delete for channel ${channel.channelTitle} (ID: ${channel.id})`);

      if (videos.length === 0) {
        channelsProcessed.push({
          id: channel.id,
          title: channel.channelTitle,
          videosDeleted: 0
        });
        continue;
      }

      // Get video IDs
      const videoIds = videos.map(v => v.id);

      // Use a transaction to ensure all operations complete or none do
      storage.db.transaction(() => {
        // Delete videos by their IDs directly
        if (videoIds.length > 0) {
          // Prepare a placeholder string with the correct number of placeholders
          const placeholders = videoIds.map(() => '?').join(',');

          // Delete videos by their IDs
          const deleteByIdsStmt = storage.db.prepare(`
            DELETE FROM youtube_videos
            WHERE id IN (${placeholders})
          `);

          const deleteResult = deleteByIdsStmt.run(...videoIds);
          console.log(`Deleted ${deleteResult.changes} videos by ID for channel ${channel.channelTitle}`);
        } else {
          console.log(`No videos to delete for channel ${channel.channelTitle}`);
        }
      })();

      totalVideosDeleted += videos.length;
      channelsProcessed.push({
        id: channel.id,
        title: channel.channelTitle,
        videosDeleted: videos.length
      });
    }

    // Return statistics in the response
    res.json({
      success: true,
      totalChannels: channels.length,
      totalVideosDeleted,
      channelsProcessed,
      message: `Deleted ${totalVideosDeleted} videos from ${channels.length} channels`
    });
  } catch (error) {
    console.error('Error deleting all channel data:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to delete all channel data: ${errorMessage}` });
  }
});

// Get statistics about videos (transcription and financial analysis counts)
router.get('/stats', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Retrieving video statistics for user', req.user.id);

    // Get the database connection
    const db = (storage as any).db;
    if (!db) {
      return res.status(500).json({ message: 'Database not available' });
    }

    // Get counts of videos with transcription and financial analysis
    const stats = db.prepare(`
      SELECT
        COUNT(*) as totalVideos,
        SUM(CASE WHEN has_transcription = 1 THEN 1 ELSE 0 END) as videosWithTranscription,
        SUM(CASE WHEN has_financial_analysis = 1 THEN 1 ELSE 0 END) as videosWithFinancialAnalysis
      FROM youtube_videos
      WHERE user_id = ?
    `).get(req.user.id);

    res.json({
      totalVideos: stats.totalVideos,
      videosWithTranscription: stats.videosWithTranscription,
      videosWithFinancialAnalysis: stats.videosWithFinancialAnalysis,
      failedAnalyses: 0, // We don't track this yet
      errors: []
    });
  } catch (error) {
    console.error('Error retrieving video statistics:', error);
    res.status(500).json({
      message: "Failed to retrieve video statistics",
      totalVideos: 0,
      videosWithTranscription: 0,
      videosWithFinancialAnalysis: 0,
      failedAnalyses: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    });
  }
});

// Get a specific YouTube channel
router.get('/:id', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const channelId = parseInt(req.params.id);
    const channel = await storage.getYoutubeChannel(channelId);

    if (!channel) {
      return res.status(404).json({ message: "YouTube channel not found" });
    }

    // Check if the channel belongs to the current user
    if (channel.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to access this channel" });
    }

    res.json(channel);
  } catch (error) {
    console.error('Error fetching YouTube channel:', error);
    res.status(500).json({ message: "Failed to fetch YouTube channel" });
  }
});

// Batch import YouTube channels
router.post('/batch-import', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    // Validate request body
    const validatedData = batchImportSchema.parse(req.body);

    // Get existing channels for this user to check for duplicates
    const existingChannels = await storage.getYoutubeChannels(req.user.id);

    // Process each channel
    const results = [];

    for (const channelData of validatedData) {
      try {
        // Check if channel already exists
        const existingChannel = existingChannels.find(c => c.channelId === channelData.channelId);

        if (existingChannel) {
          // If channel exists and videoLimit is different, update it
          if (channelData.videoLimit && channelData.videoLimit !== existingChannel.videoLimit) {
            await storage.updateYoutubeChannel(existingChannel.id, {
              ...existingChannel,
              videoLimit: channelData.videoLimit
            });

            results.push({
              channelId: channelData.channelId,
              channelTitle: channelData.channelTitle,
              status: 'updated',
              message: `Updated video limit to ${channelData.videoLimit}`
            });
          } else {
            // Skip if channel already exists and no updates needed
            results.push({
              channelId: channelData.channelId,
              channelTitle: channelData.channelTitle,
              status: 'skipped',
              message: 'Channel already exists'
            });
          }
        } else {
          // Create new channel
          const newChannel = await storage.createYoutubeChannel(req.user.id, {
            channelId: channelData.channelId,
            channelTitle: channelData.channelTitle,
            channelUrl: channelData.channelUrl,
            videoLimit: channelData.videoLimit || 15,
            subscriberCount: 0
          });

          results.push({
            channelId: channelData.channelId,
            channelTitle: channelData.channelTitle,
            status: 'added',
            newChannelId: newChannel.id
          });
        }
      } catch (error) {
        console.error(`Error processing channel ${channelData.channelTitle}:`, error);

        results.push({
          channelId: channelData.channelId,
          channelTitle: channelData.channelTitle,
          status: 'failed',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Collect all newly added channels
    const addedChannels = results
      .filter(result => result.status === 'added')
      .map(result => ({ id: result.newChannelId, title: result.channelTitle }));

    console.log(`Batch import: ${addedChannels.length} channels added, fetching videos and transcriptions...`);

    // Queue background tasks for fetching videos and transcriptions for newly added channels
    try {
      // For each newly added channel, queue a task to fetch videos and transcriptions
      for (const channel of addedChannels) {
        console.log(`Queueing background task to fetch videos for channel ${channel.title} (ID: ${channel.id})`);
        await queueChannelVideoRefresh(channel.id, req.user.id);
      }

      console.log('Batch import: Queued background tasks for all newly added channels');
    } catch (error) {
      console.error('Error queueing background tasks for newly added channels:', error);
    }

    // Return results immediately without waiting for video/transcription fetching
    res.status(200).json({
      total: validatedData.length,
      results
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Validation error", errors: error.errors });
    }

    console.error('Error batch importing YouTube channels:', error);
    res.status(500).json({ message: "Failed to import YouTube channels" });
  }
});

// Create a new YouTube channel subscription
router.post('/', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    // Validate request body
    const validatedData = youtubeChannelSchema.parse(req.body);

    // Check if the channel already exists for this user
    const existingChannels = await storage.getYoutubeChannels(req.user.id);
    const isDuplicate = existingChannels.some(channel => channel.channelId === validatedData.channelId);

    if (isDuplicate) {
      return res.status(400).json({ message: "Channel already exists in your subscriptions" });
    }

    // Create the YouTube channel
    const newChannel = await storage.createYoutubeChannel(req.user.id, {
      ...validatedData,
      subscriberCount: 0,
    });

    // Queue a background task to fetch videos and transcriptions for the new channel
    await queueChannelVideoRefresh(newChannel.id, req.user.id);

    res.status(201).json(newChannel);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Validation error", errors: error.errors });
    }

    console.error('Error creating YouTube channel:', error);
    res.status(500).json({ message: "Failed to create YouTube channel" });
  }
});

// Update a YouTube channel
router.put('/:id', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const channelId = parseInt(req.params.id);
    const channel = await storage.getYoutubeChannel(channelId);

    if (!channel) {
      return res.status(404).json({ message: "YouTube channel not found" });
    }

    // Check if the channel belongs to the current user
    if (channel.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to update this channel" });
    }

    // Validate request body
    const validatedData = youtubeChannelSchema.parse(req.body);

    // Update the channel
    const updatedChannel = await storage.updateYoutubeChannel(channelId, validatedData);

    res.json(updatedChannel);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Validation error", errors: error.errors });
    }

    console.error('Error updating YouTube channel:', error);
    res.status(500).json({ message: "Failed to update YouTube channel" });
  }
});

// Delete a YouTube channel
router.delete('/:id', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const channelId = parseInt(req.params.id);
    const channel = await storage.getYoutubeChannel(channelId);

    if (!channel) {
      return res.status(404).json({ message: "YouTube channel not found" });
    }

    // Check if the channel belongs to the current user
    if (channel.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to delete this channel" });
    }

    // Delete the channel
    await storage.deleteYoutubeChannel(channelId);

    res.sendStatus(204);
  } catch (error) {
    console.error('Error deleting YouTube channel:', error);
    res.status(500).json({ message: "Failed to delete YouTube channel" });
  }
});

// Delete channel data (videos, transcripts, financial analysis) but keep the channel
router.delete('/:id/data', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const channelId = parseInt(req.params.id);
    console.log(`Server: Deleting data for YouTube channel ${channelId}`);

    // Check if the channel exists and belongs to the user
    const channel = await storage.getYoutubeChannel(channelId);
    if (!channel) {
      return res.status(404).json({ message: 'Channel not found' });
    }

    if (channel.userId !== req.user.id) {
      return res.status(403).json({ message: 'You do not have permission to delete data for this channel' });
    }

    // Get all videos for this channel
    const videos = await storage.getYoutubeChannelVideos(channelId);
    console.log(`Found ${videos.length} videos to delete for channel ${channelId}`);

    // Delete all videos, transcripts, and financial analysis for this channel
    // First, log the videos that will be deleted for debugging
    console.log('Videos to be deleted:', videos.map(v => ({ id: v.id, title: v.title })));

    // EMERGENCY FIX: Use a more direct approach to delete videos
    console.log(`EMERGENCY FIX: Deleting videos for channel ${channel.channelTitle} (ID: ${channelId}, YouTube ID: ${channel.channelId})`);

    // First, get all video IDs for this channel to ensure we can delete them directly
    const videoIds = videos.map(v => v.id);
    console.log(`Found ${videoIds.length} video IDs to delete: ${videoIds.join(', ')}`);

    // Use a transaction to ensure all operations complete or none do
    storage.db.transaction(() => {
      // First approach: Delete by channel_id
      const deleteByChannelStmt = storage.db.prepare(`
        DELETE FROM youtube_videos
        WHERE channel_id = ?
      `);

      const channelResult = deleteByChannelStmt.run(channel.channelId);
      console.log(`Approach 1: Deleted ${channelResult.changes} videos by channel_id for channel ${channelId} (YouTube ID: ${channel.channelId})`);

      // Second approach: Delete videos one by one by ID
      let individualDeletes = 0;
      if (videoIds.length > 0) {
        const deleteByIdStmt = storage.db.prepare(`
          DELETE FROM youtube_videos
          WHERE id = ?
        `);

        for (const videoId of videoIds) {
          const result = deleteByIdStmt.run(videoId);
          individualDeletes += result.changes;
        }

        console.log(`Approach 2: Deleted ${individualDeletes} videos individually by ID`);
      }

      // Third approach: Delete using IN clause with all video IDs
      if (videoIds.length > 0) {
        const placeholders = videoIds.map(() => '?').join(',');
        const deleteByIdsStmt = storage.db.prepare(`
          DELETE FROM youtube_videos
          WHERE id IN (${placeholders})
        `);

        const batchResult = deleteByIdsStmt.run(...videoIds);
        console.log(`Approach 3: Deleted ${batchResult.changes} videos using IN clause`);
      }

      // Verify deletion by checking if any videos remain
      const remainingVideos = storage.db.prepare(`
        SELECT COUNT(*) as count FROM youtube_videos WHERE channel_id = ?
      `).get(channel.channelId);

      console.log(`Remaining videos for channel ${channelId} (YouTube ID: ${channel.channelId}): ${remainingVideos.count}`);

      if (remainingVideos.count > 0) {
        console.warn(`Warning: ${remainingVideos.count} videos still remain for channel ${channelId} after deletion`);

        // Log the remaining videos for debugging
        const remainingVideosList = storage.db.prepare(`
          SELECT id, title FROM youtube_videos WHERE channel_id = ?
        `).all(channel.channelId);

        console.log('Remaining videos:', remainingVideosList);

        // Final approach: Brute force delete by channel title
        const deleteByTitleStmt = storage.db.prepare(`
          DELETE FROM youtube_videos
          WHERE channel_title = ?
        `);

        const titleResult = deleteByTitleStmt.run(channel.channelTitle);
        console.log(`Approach 4: Deleted ${titleResult.changes} videos by channel_title`);
      }
    })();

    // Return the channel ID in the response for client-side cache invalidation
    res.json({
      success: true,
      channelId: channelId,
      message: `Deleted ${videos.length} videos including transcripts and financial analysis`
    });
  } catch (error) {
    console.error('Error deleting channel data:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to delete channel data: ${errorMessage}` });
  }
});



// Test Ollama connection
router.get('/test-ollama-connection', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Testing Ollama connection...');
    const response = await fetch('http://localhost:11434/api/tags');

    if (!response.ok) {
      console.error(`Ollama server returned error: ${response.status}`);
      return res.status(500).json({
        message: "Failed to connect to Ollama server",
        status: response.status,
        statusText: response.statusText
      });
    }

    const data = await response.json();
    console.log(`Ollama server is running with ${data.models.length} models available`);

    return res.json({
      status: "success",
      message: "Ollama server is running",
      models: data.models.map((model: any) => model.name)
    });
  } catch (error) {
    console.error('Error testing Ollama connection:', error);
    return res.status(500).json({
      message: "Failed to connect to Ollama server",
      error: error.message
    });
  }
});

// Get a specific YouTube video by ID
router.get('/videos/:videoId', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { videoId } = req.params;
    console.log(`Server: Getting video with ID ${videoId}`);

    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      console.log(`Server: Video with ID ${videoId} not found`);
      return res.status(404).json({ message: "YouTube video not found" });
    }

    // Check if the video belongs to the current user
    if (video.userId !== req.user.id) {
      console.log(`Video ${videoId} does not belong to user ${req.user.id}`);
      return res.status(403).json({ message: "You don't have permission to access this video" });
    }

    console.log(`Server: Returning video with ID ${videoId}`);
    console.log(`Server: Video has Ollama model: ${video.ollamaModelUsed || 'None'}`);
    res.json(video);
  } catch (error) {
    console.error('Error fetching video:', error);
    res.status(500).json({ message: "Failed to fetch video" });
  }
});

// Get videos for a specific YouTube channel
router.get('/:id/videos', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const channelId = parseInt(req.params.id);
    console.log(`Getting videos for channel ID: ${channelId}`);

    const channel = await storage.getYoutubeChannel(channelId);

    if (!channel) {
      console.log(`Channel with ID ${channelId} not found`);
      return res.status(404).json({ message: "YouTube channel not found" });
    }

    console.log(`Found channel: ${channel.channelTitle} (channelId: ${channel.channelId})`);

    // Check if the channel belongs to the current user
    if (channel.userId !== req.user.id) {
      console.log(`Channel ${channelId} does not belong to user ${req.user.id}`);
      return res.status(403).json({ message: "You don't have permission to access this channel's videos" });
    }

    // Get videos for the channel
    console.log(`Fetching videos for channel: ${channel.channelTitle}`);
    const videos = await storage.getYoutubeChannelVideos(channelId);
    console.log(`Found ${videos.length} videos for channel ${channel.channelTitle}`);

    // Just log the count, no need to show sample video details
    if (videos.length > 0) {
      console.log(`Found ${videos.length} videos for channel ${channel.channelTitle}`);
    } else {
      console.log(`No videos found for channel ${channel.channelTitle}`);
    }

    res.json(videos);
  } catch (error) {
    console.error('Error fetching channel videos:', error);
    res.status(500).json({ message: "Failed to fetch videos" });
  }
});

// Refresh videos for a specific YouTube channel
router.post('/:id/refresh', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const channelId = parseInt(req.params.id);
    console.log(`Refreshing channel with ID: ${channelId}`);

    const channel = await storage.getYoutubeChannel(channelId);

    if (!channel) {
      console.log(`Channel with ID ${channelId} not found`);
      return res.status(404).json({ message: "YouTube channel not found" });
    }

    console.log(`Found channel: ${channel.channelTitle} (channelId: ${channel.channelId})`);

    // Check if the channel belongs to the current user
    if (channel.userId !== req.user.id) {
      console.log(`Channel ${channelId} does not belong to user ${req.user.id}`);
      return res.status(403).json({ message: "You don't have permission to refresh this channel" });
    }

    console.log(`Starting refresh for channel: ${channel.channelTitle}`);

    // Queue a background task to refresh videos and fetch transcriptions
    const taskId = await queueChannelVideoRefresh(channelId, req.user.id);

    console.log(`Queued background task (${taskId}) to refresh videos for channel ${channel.channelTitle}`);

    // Return the existing videos immediately
    const existingVideos = await storage.getYoutubeChannelVideos(channelId);
    res.json({
      videos: existingVideos,
      message: "Channel refresh has been queued and will be processed in the background. Videos without AI analysis will be automatically analyzed.",
      taskId
    });
  } catch (error) {
    console.error('Error refreshing channel videos:', error);
    res.status(500).json({ message: "Failed to refresh videos" });
  }
});

// Direct endpoint for refreshing channel videos (no authentication required)
router.post('/:id/refresh-direct', async (req, res) => {
  try {
    const channelId = parseInt(req.params.id);
    console.log(`Direct refresh for channel with ID: ${channelId}`);

    const channel = await storage.getYoutubeChannel(channelId);

    if (!channel) {
      console.log(`Channel with ID ${channelId} not found`);
      return res.status(404).json({ message: "YouTube channel not found" });
    }

    console.log(`Found channel: ${channel.channelTitle} (channelId: ${channel.channelId})`);

    // Refresh videos for the channel
    const result = await storage.refreshYoutubeChannelVideos(channelId);
    const videos = result.videos;
    const videosNeedingAnalysis = result.videosNeedingAnalysis;

    console.log(`Refreshed ${videos.length} videos for channel ${channel.channelTitle}`);
    console.log(`Found ${videosNeedingAnalysis.length} videos needing AI analysis`);

    // Update the last refresh time
    await storage.updateYoutubeChannelLastRefreshTime(channelId);

    // Return the videos and analysis info
    res.json({
      videos,
      videosNeedingAnalysis: videosNeedingAnalysis.length,
      message: "Channel videos refreshed successfully"
    });
  } catch (error) {
    console.error('Error refreshing channel videos:', error);
    res.status(500).json({ message: "Failed to refresh videos" });
  }
});

// Delete transcription for a specific YouTube video
router.delete('/videos/:videoId/transcription', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { videoId } = req.params;
    console.log(`Server: Deleting transcription for video ID: ${videoId}`);
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      console.log(`Server: Video ID ${videoId} not found in database`);
      return res.status(404).json({ message: "Video not found" });
    }

    // Check if the video belongs to the current user
    if (video.userId !== req.user.id) {
      console.log(`Server: User ${req.user.id} does not have permission to access video ${videoId}`);
      return res.status(403).json({ message: "You don't have permission to access this video" });
    }

    // Update the video to remove the transcription
    await storage.db.prepare(`
      UPDATE youtube_videos
      SET transcription = NULL, has_transcription = 0
      WHERE id = ?
    `).run(videoId);

    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error deleting video transcription:', error);
    res.status(500).json({ message: "Failed to delete transcription" });
  }
});

// Get transcription for a specific YouTube video
router.get('/videos/:videoId/transcription', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { videoId } = req.params;
    console.log(`Server: Fetching transcription for video ID: ${videoId}`);
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      console.log(`Server: Video ID ${videoId} not found in database`);
      // Even if video is not found, return a fallback transcription
      const fallbackTranscription = `Video ID ${videoId} not found in your subscriptions. You can view it on YouTube: https://www.youtube.com/watch?v=${videoId}`;
      return res.json({ transcription: fallbackTranscription });
    }

    // Check if the video belongs to the current user
    if (video.userId !== req.user.id) {
      console.log(`Server: User ${req.user.id} does not have permission to access video ${videoId}`);
      // Even if permission is denied, return a fallback transcription
      const fallbackTranscription = `You don't have permission to access this video. You can view it on YouTube: https://www.youtube.com/watch?v=${videoId}`;
      return res.json({ transcription: fallbackTranscription });
    }

    // Check if transcription already exists
    if (video.transcription) {
      // Check if it's a simulated transcript and clear it if needed
      if (video.transcription.includes('This is a simulated transcript')) {
        console.log(`Server: Found simulated transcript for video ${videoId}, clearing it`);
        // Clear the simulated transcript from the database
        await storage.db.prepare(`
          UPDATE youtube_videos
          SET transcription = NULL, has_transcription = 0
          WHERE id = ?
        `).run(videoId);

        // Queue a background task to fetch the real transcription
        try {
          console.log(`Server: Queueing transcription fetch for video ${videoId} after clearing simulated transcript`);
          const taskId = await queueTranscriptionsForVideos([{ id: videoId }], req.user.id);
          console.log(`Server: Queued transcription fetch task (${taskId}) for video ${videoId}`);

          // Return a message indicating the task has been queued
          return res.json({
            message: "Simulated transcript detected and cleared. Fetching actual transcription in the background.",
            taskId,
            videoId
          });
        } catch (error) {
          console.error('Error queueing transcription fetch after clearing simulated transcript:', error);
          const fallbackTranscription = `Transcription not available for this video. You can view it on YouTube: https://www.youtube.com/watch?v=${videoId}`;
          return res.json({ transcription: fallbackTranscription });
        }
      } else {
        // Return the existing transcription if it's not a simulated one
        return res.json({ transcription: video.transcription });
      }
    }

    // Queue a background task to fetch the transcription
    try {
      console.log(`Server: Queueing transcription fetch for video ${videoId}`);
      const taskId = await queueTranscriptionsForVideos([{ id: videoId }], req.user.id);
      console.log(`Server: Queued transcription fetch task (${taskId}) for video ${videoId}`);

      // Return a message indicating the task has been queued
      res.json({
        message: "Transcription fetch has been queued and will be processed in the background.",
        taskId,
        videoId
      });
    } catch (error) {
      console.error('Error fetching transcription:', error);
      // Return a fallback transcription instead of an error
      const fallbackTranscription = `Transcription not available for this video. You can view it on YouTube: https://www.youtube.com/watch?v=${videoId}`;
      res.json({ transcription: fallbackTranscription });
    }
  } catch (error) {
    console.error('Error getting video transcription:', error);
    // Return a fallback transcription instead of an error
    const fallbackTranscription = `Error retrieving transcription. You can view this video on YouTube: https://www.youtube.com/watch?v=${videoId}`;
    res.json({ transcription: fallbackTranscription });
  }
});

// Helper function to fetch transcription from YouTube
export async function fetchTranscription(videoId: string): Promise<string> {
  try {
    console.log(`fetchTranscription: Starting transcription fetch for video ${videoId}`);

    // First check if the video exists and is accessible
    try {
      const checkResponse = await axios.get(`https://www.youtube.com/oembed?url=http://www.youtube.com/watch?v=${videoId}&format=json`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
        validateStatus: (status) => status < 500, // Don't throw for 4xx errors
      });

      if (checkResponse.status !== 200) {
        console.log(`fetchTranscription: Video ${videoId} not found or not accessible (status: ${checkResponse.status})`);
        return `Transcription not available. The video may be private, deleted, or not exist: https://www.youtube.com/watch?v=${videoId}`;
      }
    } catch (error) {
      console.error(`fetchTranscription: Error checking video existence for ${videoId}:`, error.message);
      // Continue anyway, as the video might still be accessible
    }

    // Method 1: Try to get the transcript using a direct approach with YouTube's transcript list API
    try {
      console.log(`fetchTranscription: Trying Method 1 for video ${videoId}`);
      // First, get the list of available transcripts
      const listResponse = await axios.get(`https://www.youtube.com/watch?v=${videoId}`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept-Language': 'en-US,en;q=0.9',
        }
      });
      const html = listResponse.data;

      // Extract the captions data from the YouTube page
      const captionsMatch = html.match(/"captions":\s*({.*?}),"videoDetails"/);
      if (captionsMatch && captionsMatch[1]) {
        try {
          console.log(`fetchTranscription: Found captions data for video ${videoId}`);
          const captionsData = JSON.parse(captionsMatch[1]);
          console.log(`fetchTranscription: Parsed captions data:`, JSON.stringify(captionsData).substring(0, 200) + '...');

          const captionTracks = captionsData?.playerCaptionsTracklistRenderer?.captionTracks || [];

          if (captionTracks.length > 0) {
            console.log(`fetchTranscription: Found ${captionTracks.length} caption tracks for video ${videoId}`);
            // Get the first available transcript (usually English)
            const transcriptUrl = captionTracks[0].baseUrl;
            console.log(`fetchTranscription: Transcript URL: ${transcriptUrl}`);

            // Fetch the transcript XML
            const transcriptResponse = await axios.get(transcriptUrl);
            const transcriptXml = transcriptResponse.data;
            console.log(`fetchTranscription: Got XML response of length ${transcriptXml.length}`);

            // Parse the XML
            const $ = cheerio.load(transcriptXml, { xmlMode: true });
            let transcript = '';

            // Extract text and timestamps from each transcript segment
            $('text').each((i, el) => {
              // Get the start time in seconds
              const startSeconds = parseFloat($(el).attr('start') || '0');

              // Format the timestamp as MM:SS
              const minutes = Math.floor(startSeconds / 60);
              const seconds = Math.floor(startSeconds % 60);
              const timestamp = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

              // Decode HTML entities in the text
              const text = decodeHtmlEntities($(el).text());

              // Add the timestamp and text to the transcript
              transcript += `[${timestamp}] ${text}\n`;
            });

            console.log(`fetchTranscription: Successfully extracted transcript for video ${videoId} (${transcript.length} chars)`);
            return transcript.trim();
          } else {
            console.log(`fetchTranscription: No caption tracks found for video ${videoId}`);
          }
        } catch (parseError) {
          console.error(`fetchTranscription: Error parsing captions data for video ${videoId}:`, parseError);
        }
      } else {
        console.log(`fetchTranscription: No captions match found in HTML for video ${videoId}`);
      }
    } catch (err) {
      console.log(`fetchTranscription: Method 1 failed for video ${videoId}, trying method 2...`, err.message);
    }

    // Method 2: Try to extract the transcript from the video description
    try {
      console.log(`fetchTranscription: Trying Method 2 for video ${videoId}`);
      const response = await axios.get(`https://www.youtube.com/watch?v=${videoId}`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept-Language': 'en-US,en;q=0.9',
        }
      });
      const html = response.data;
      console.log(`fetchTranscription: Got HTML response of length ${html.length} for video ${videoId}`);

      // Extract video details
      const videoDetailsMatch = html.match(/"videoDetails":\s*({.*?}),"playerConfig"/);
      if (videoDetailsMatch && videoDetailsMatch[1]) {
        try {
          console.log(`fetchTranscription: Found video details for video ${videoId}`);
          const videoDetails = JSON.parse(videoDetailsMatch[1]);
          console.log(`fetchTranscription: Parsed video details:`, JSON.stringify(videoDetails).substring(0, 200) + '...');

          const title = decodeHtmlEntities(videoDetails.title || '');
          const author = decodeHtmlEntities(videoDetails.author || '');
          const description = decodeHtmlEntities(videoDetails.shortDescription || '');

          // Create a formatted transcript with video details
          let transcript = `Title: ${title}\n\nAuthor: ${author}\n\n`;

          if (description) {
            transcript += `Description:\n${description}\n\n`;
            console.log(`fetchTranscription: Added description for video ${videoId} (${description.length} chars)`);
          }

          // Add timestamps if found in description
          const timestampMatches = description.match(/([0-9]+:[0-9]+:[0-9]+|[0-9]+:[0-9]+)\s+(.+?)(?=\n|$)/g);
          if (timestampMatches && timestampMatches.length > 0) {
            console.log(`fetchTranscription: Found ${timestampMatches.length} timestamps for video ${videoId}`);
            transcript += "Timestamps:\n";
            timestampMatches.forEach(match => {
              transcript += match + "\n";
            });
          }

          console.log(`fetchTranscription: Successfully created transcript from video details for ${videoId} (${transcript.length} chars)`);
          return transcript;
        } catch (parseError) {
          console.error(`fetchTranscription: Error parsing video details for video ${videoId}:`, parseError);
        }
      } else {
        console.log(`fetchTranscription: No video details found in HTML for video ${videoId}`);
      }
    } catch (err) {
      console.log(`fetchTranscription: Method 2 failed for video ${videoId}, trying method 3...`, err.message);
    }

    // Method 3: Use YouTube's oEmbed API to get basic video information
    try {
      console.log(`fetchTranscription: Trying Method 3 for video ${videoId}`);
      const response = await axios.get(`https://www.youtube.com/oembed?url=http://www.youtube.com/watch?v=${videoId}&format=json`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        }
      });
      const data = response.data;
      console.log(`fetchTranscription: oEmbed response for video ${videoId}:`, data);

      if (data && data.title) {
        console.log(`fetchTranscription: Found oEmbed data for video ${videoId}, but could not get actual transcript`);
        // Instead of creating a simulated transcript, return a clear message that the transcript is not available
        return `Transcription not available for this video.\n\nTitle: ${decodeHtmlEntities(data.title)}\nAuthor: ${decodeHtmlEntities(data.author_name)}\n\nThe actual transcript could not be retrieved. This usually means:\n- The video doesn't have captions\n- The captions are auto-generated and not accessible via API\n- The video is private or restricted\n\nYou can view the video directly on YouTube: https://www.youtube.com/watch?v=${videoId}`;
      } else {
        console.log(`fetchTranscription: No title found in oEmbed data for video ${videoId}`);
      }
    } catch (err) {
      console.log(`fetchTranscription: Method 3 failed for video ${videoId}, using fallback...`, err.message);
    }

    // Method 4: Use a fallback transcript with a disclaimer
    console.log(`fetchTranscription: Using fallback method for video ${videoId}`);
    return `Transcription not available for this video.\n\nPossible reasons:\n- The video doesn't have captions\n- The captions are auto-generated and not accessible via API\n- The video is private or restricted\n\nYou can view the video directly on YouTube: https://www.youtube.com/watch?v=${videoId}`;
  } catch (error) {
    console.error(`fetchTranscription: Error fetching transcription for video ${videoId}:`, error);
    return `Transcription not available for this video.\n\nThere was an error retrieving the transcription. This usually means the video doesn't have an accessible transcript.\n\nYou can view the video directly on YouTube: https://www.youtube.com/watch?v=${videoId}`;
  }
}

// Helper function to decode HTML entities
function decodeHtmlEntities(text: string): string {
  if (!text) return '';

  // Create a temporary element to use the browser's built-in HTML entity decoding
  const entities = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'",
    '&apos;': "'",
    '&#x2F;': '/',
    '&#x27;': "'",
    '&#x60;': '`',
    '&nbsp;': ' '
  };

  // Replace all entities with their decoded values
  return text.replace(/&[#\w]+;/g, (entity) => {
    const decoded = entities[entity];
    if (decoded) {
      return decoded;
    }

    // Handle numeric entities (like &#39;)
    if (entity.match(/&#\d+;/)) {
      const code = entity.match(/&#(\d+);/)[1];
      return String.fromCharCode(parseInt(code, 10));
    }

    // Handle hex entities (like &#x27;)
    if (entity.match(/&#x[\da-f]+;/i)) {
      const code = entity.match(/&#x([\da-f]+);/i)[1];
      return String.fromCharCode(parseInt(code, 16));
    }

    return entity;
  });
}

// Test endpoint for financial analysis
router.get('/test-financial-analysis', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Testing financial analysis functionality');

    // Get a video with a transcription
    const stmt = storage.db.prepare(`
      SELECT id FROM youtube_videos
      WHERE has_transcription = 1
      LIMIT 1
    `);

    const row = stmt.get();
    if (!row) {
      return res.status(404).json({ message: 'No videos with transcriptions found' });
    }

    const videoId = row.id;
    console.log(`Server: Found video with transcription: ${videoId}`);

    // Get the video
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    // Analyze the transcription
    console.log(`Server: Analyzing transcription for video ${videoId}`);
    // Use enhanced NLP-based financial analysis
    const analysis = await enhancedFinancialAnalysis(
      video.transcription!,
      video.title,
      video.description
    );

    // Save the analysis to the database
    await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
      financialScore: analysis.score,
      financialCategory: analysis.category,
      financialAmount: analysis.amount,
      financialTimeline: analysis.timeline,
      financialRecipients: analysis.recipients,
      financialSteps: analysis.steps,
      financialViralPotential: analysis.viralPotential,
      financialSkepticism: analysis.skepticism,
      financialAnalysis: analysis.analysis,
      financialTimestamps: analysis.timestamps,
      hasFinancialAnalysis: true
    });

    console.log(`Server: Financial analysis completed for video ${videoId}`);
    res.json({ success: true, videoId, analysis });
  } catch (error) {
    console.error('Error testing financial analysis:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to test financial analysis: ${errorMessage}` });
  }
});

// Test endpoint for financial analysis
router.get('/test-financial-analysis', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Testing financial analysis functionality');

    // Get a video with a transcription
    const stmt = (storage as any).db.prepare(`
      SELECT id FROM youtube_videos
      WHERE has_transcription = 1
      LIMIT 1
    `);

    const row = stmt.get();
    if (!row) {
      return res.status(404).json({ message: 'No videos with transcriptions found' });
    }

    const videoId = row.id;
    console.log(`Server: Found video with transcription: ${videoId}`);

    // Get the video
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    // Analyze the transcription
    console.log(`Server: Analyzing transcription for video ${videoId}`);
    const analysis = await analyzeTranscriptForFinancialBenefits(
      video.transcription!,
      video.title,
      video.description
    );

    // Save the analysis to the database
    await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
      financialScore: analysis.score,
      financialCategory: analysis.category,
      financialAmount: analysis.amount,
      financialTimeline: analysis.timeline,
      financialRecipients: analysis.recipients,
      financialSteps: analysis.steps,
      financialViralPotential: analysis.viralPotential,
      financialSkepticism: analysis.skepticism,
      financialAnalysis: analysis.analysis,
      financialTimestamps: analysis.timestamps,
      hasFinancialAnalysis: true
    });

    console.log(`Server: Financial analysis completed for video ${videoId}`);
    res.json({ success: true, videoId, analysis });
  } catch (error) {
    console.error('Error testing financial analysis:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to test financial analysis: ${errorMessage}` });
  }
});

// Test endpoint for financial analysis
router.get('/test-financial-analysis', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Testing financial analysis functionality');

    // Get a video with a transcription
    const stmt = (storage as any).db.prepare(`
      SELECT id FROM youtube_videos
      WHERE has_transcription = 1
      LIMIT 1
    `);

    const row = stmt.get();
    if (!row) {
      return res.status(404).json({ message: 'No videos with transcriptions found' });
    }

    const videoId = row.id;
    console.log(`Server: Found video with transcription: ${videoId}`);

    // Get the video
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    // Analyze the transcription
    console.log(`Server: Analyzing transcription for video ${videoId}`);
    const analysis = await analyzeTranscriptForFinancialBenefits(
      video.transcription!,
      video.title,
      video.description
    );

    // Save the analysis to the database
    await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
      financialScore: analysis.score,
      financialCategory: analysis.category,
      financialAmount: analysis.amount,
      financialTimeline: analysis.timeline,
      financialRecipients: analysis.recipients,
      financialSteps: analysis.steps,
      financialViralPotential: analysis.viralPotential,
      financialSkepticism: analysis.skepticism,
      financialAnalysis: analysis.analysis,
      financialTimestamps: analysis.timestamps,
      hasFinancialAnalysis: true
    });

    console.log(`Server: Financial analysis completed for video ${videoId}`);
    res.json({ success: true, videoId, analysis });
  } catch (error) {
    console.error('Error testing financial analysis:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to test financial analysis: ${errorMessage}` });
  }
});

// Test endpoint for financial analysis
router.get('/test-financial-analysis', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Testing financial analysis functionality');

    // Get a video with a transcription
    const db = (storage as any).db;
    if (!db) {
      return res.status(500).json({ message: 'Database not available' });
    }

    const stmt = db.prepare(`
      SELECT id, title, description, transcription FROM youtube_videos
      WHERE has_transcription = 1
      LIMIT 1
    `);

    const row = stmt.get();
    if (!row) {
      return res.status(404).json({ message: 'No videos with transcriptions found' });
    }

    const videoId = row.id;
    const title = row.title;
    const description = row.description;
    const transcription = row.transcription;

    console.log(`Server: Found video with transcription: ${videoId}`);
    console.log(`Server: Title: ${title}`);
    console.log(`Server: Transcription length: ${transcription?.length || 0}`);

    // Analyze the transcription
    console.log(`Server: Analyzing transcription for video ${videoId}`);
    // Use enhanced NLP-based financial analysis
    const analysis = await enhancedFinancialAnalysis(
      transcription,
      title,
      description
    );

    // Save the analysis to the database
    await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
      financialScore: analysis.score,
      financialCategory: analysis.category,
      financialAmount: analysis.amount,
      financialTimeline: analysis.timeline,
      financialRecipients: analysis.recipients,
      financialSteps: analysis.steps,
      financialViralPotential: analysis.viralPotential,
      financialSkepticism: analysis.skepticism,
      financialAnalysis: analysis.analysis,
      financialTimestamps: analysis.timestamps,
      hasFinancialAnalysis: true
    });

    console.log(`Server: Financial analysis completed for video ${videoId}`);
    res.json({ success: true, videoId, analysis });
  } catch (error) {
    console.error('Error testing financial analysis:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to test financial analysis: ${errorMessage}` });
  }
});

// Analyze a video transcript for financial benefits
router.get('/videos/:videoId/analyze-financial', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { videoId } = req.params;
    console.log(`Server: Analyzing financial benefits for video ID: ${videoId}`);

    // Get the video
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      console.log(`Server: Video ID ${videoId} not found in database`);
      return res.status(404).json({ message: `Video ID ${videoId} not found in your subscriptions` });
    }

    // Check if the video already has a financial analysis
    if (video.hasFinancialAnalysis) {
      console.log(`Server: Video ${videoId} already has financial analysis`);
      return res.json({
        financialScore: video.financialScore,
        financialCategory: video.financialCategory,
        financialAmount: video.financialAmount,
        financialTimeline: video.financialTimeline,
        financialRecipients: video.financialRecipients,
        financialSteps: video.financialSteps,
        financialViralPotential: video.financialViralPotential,
        financialSkepticism: video.financialSkepticism,
        financialAnalysis: video.financialAnalysis,
        financialTimestamps: video.financialTimestamps
      });
    }

    // Get the transcription
    if (!video.transcription) {
      console.log(`Server: No transcription available for video ${videoId}`);
      return res.status(404).json({ message: 'No transcription available for this video' });
    }

    // Analyze the transcription
    console.log(`Server: Analyzing transcription for video ${videoId}`);
    // Use enhanced NLP-based financial analysis
    const analysis = await enhancedFinancialAnalysis(
      video.transcription,
      video.title,
      video.description
    );

    // Save the analysis to the database
    await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
      financialScore: analysis.score,
      financialCategory: analysis.category,
      financialAmount: analysis.amount,
      financialTimeline: analysis.timeline,
      financialRecipients: analysis.recipients,
      financialSteps: analysis.steps,
      financialViralPotential: analysis.viralPotential,
      financialSkepticism: analysis.skepticism,
      financialAnalysis: analysis.analysis,
      financialTimestamps: analysis.timestamps,
      hasFinancialAnalysis: true
    });

    console.log(`Server: Financial analysis completed for video ${videoId}`);
    res.json(analysis);
  } catch (error) {
    console.error('Error analyzing video transcript:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error details:', errorMessage);
    res.status(500).json({ message: `Failed to analyze video transcript: ${errorMessage}` });
  }
});

// Helper function to fetch transcriptions for multiple videos in the background
async function fetchTranscriptionsForVideos(videos: YoutubeVideo[], userId: number) {
  try {
    // Filter out videos that already have transcriptions to avoid unnecessary processing
    const videosNeedingTranscription = videos.filter(video => !video.hasTranscription);

    if (videosNeedingTranscription.length === 0) {
      console.log('No videos need transcription, skipping transcription fetching');
      return; // No videos need transcription, exit early
    }

    console.log(`Starting transcription fetching for ${videosNeedingTranscription.length}/${videos.length} videos`);

    // Process videos in batches to avoid overwhelming the system
    const batchSize = 5; // Increased batch size for better performance
    const batches = [];

    // Split videos into batches
    for (let i = 0; i < videosNeedingTranscription.length; i += batchSize) {
      batches.push(videosNeedingTranscription.slice(i, i + batchSize));
    }

    console.log(`Split videos into ${batches.length} batches of size ${batchSize}`);

    // Process each batch sequentially
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`Processing batch ${i + 1}/${batches.length} with ${batch.length} videos`);

      await Promise.all(batch.map(async (video) => {
        try {
          // Fetch and save transcription
          console.log(`Fetching transcription for video ${video.id} in batch ${i + 1}`);
          const transcription = await fetchTranscription(video.id);
          await storage.updateYoutubeVideoTranscription(video.id, transcription);
          console.log(`Transcription fetched and saved for video ${video.id}`);

          // Automatically analyze the transcription for financial benefits
          try {
            console.log(`Automatically analyzing financial benefits for video ${video.id}`);
            // Use enhanced NLP-based financial analysis
            const analysis = await enhancedFinancialAnalysis(
              transcription,
              video.title,
              video.description
            );

            // Save the analysis to the database
            await storage.updateYoutubeVideoFinancialAnalysis(video.id, {
              financialScore: analysis.score,
              financialCategory: analysis.category,
              financialAmount: analysis.amount,
              financialTimeline: analysis.timeline,
              financialRecipients: analysis.recipients,
              financialSteps: analysis.steps,
              financialViralPotential: analysis.viralPotential,
              financialSkepticism: analysis.skepticism,
              financialAnalysis: analysis.analysis,
              financialTimestamps: analysis.timestamps,
              hasFinancialAnalysis: true
            });
            console.log(`Financial analysis completed for video ${video.id}`);
          } catch (analysisError) {
            console.error(`Error analyzing financial benefits for video ${video.id}:`, analysisError);
          }
        } catch (error) {
          console.error(`Error fetching transcription for video ${video.id}:`, error);
        }
      }));

      // Add a small delay between batches to avoid rate limiting
      if (i < batches.length - 1) {
        console.log(`Adding delay between batches ${i + 1} and ${i + 2}`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`Completed processing ${videosNeedingTranscription.length} videos`);
  } catch (error) {
    console.error('Error in batch transcription fetching:', error);
  }
}

// Get financial analysis for a specific video
router.get('/videos/:videoId/financial-analysis', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { videoId } = req.params;
    console.log(`Server: Fetching financial analysis for video ${videoId}`);

    // Get the video details
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    if (!video.hasFinancialAnalysis) {
      return res.status(404).json({ message: 'No financial analysis available for this video' });
    }

    // Return the financial analysis
    const analysis = {
      score: video.financialScore,
      category: video.financialCategory,
      amount: video.financialAmount,
      timeline: video.financialTimeline,
      recipients: video.financialRecipients,
      steps: video.financialSteps,
      viralPotential: video.financialViralPotential,
      skepticism: video.financialSkepticism,
      analysis: video.financialAnalysis,
      timestamps: video.financialTimestamps,
      // Include Ollama analysis fields if available
      ollamaBenefitAmounts: video.ollamaBenefitAmounts,
      ollamaExpectedArrivalDate: video.ollamaExpectedArrivalDate,
      ollamaEligiblePeople: video.ollamaEligiblePeople,
      ollamaProofOrSource: video.ollamaProofOrSource,
      ollamaActionsToClaim: video.ollamaActionsToClaim,
      ollamaPriorityTag: video.ollamaPriorityTag,
      ollamaModelUsed: video.ollamaModelUsed,
      ollamaReasonForPriority: video.ollamaReasonForPriority,
      ollamaViralPotential: video.ollamaViralPotential
    };

    res.json(analysis);
  } catch (error) {
    console.error('Error fetching financial analysis:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to fetch financial analysis: ${errorMessage}` });
  }
});

// Analyze a video transcript using Ollama LLM
router.get('/videos/:videoId/analyze-ollama', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { videoId } = req.params;
    console.log(`Server: Analyzing video ${videoId} with Ollama LLM`);

    // Get the video details
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    if (!video.hasTranscription) {
      return res.status(400).json({ message: 'Video does not have a transcription' });
    }

    // The transcription is already in the video object
    const transcription = video.transcription;

    if (!transcription) {
      return res.status(404).json({ message: 'Transcription not found' });
    }

    // Analyze the transcription using Ollama LLM
    console.log(`Server: Analyzing transcription for video ${videoId} with Ollama LLM`);
    try {
      const ollamaAnalysis = await analyzeTranscriptWithOllama(
        req.user.id,
        transcription,
        video.title,
        video.description
      );

      // Convert Ollama analysis to app format
      const analysis = convertOllamaAnalysisToAppFormat(ollamaAnalysis);

      // Save the analysis to the database
      await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
        financialScore: analysis.score,
        financialCategory: analysis.category,
        financialAmount: analysis.amount,
        financialTimeline: analysis.timeline,
        financialRecipients: analysis.recipients,
        financialSteps: analysis.steps,
        financialViralPotential: analysis.viralPotential,
        financialSkepticism: analysis.skepticism,
        financialAnalysis: analysis.analysis,
        financialTimestamps: '',
        hasFinancialAnalysis: true,
        ollamaBenefitAmounts: analysis.ollamaBenefitAmounts,
        ollamaExpectedArrivalDate: analysis.ollamaExpectedArrivalDate,
        ollamaEligiblePeople: analysis.ollamaEligiblePeople,
        ollamaProofOrSource: analysis.ollamaProofOrSource,
        ollamaActionsToClaim: analysis.ollamaActionsToClaim,
        ollamaPriorityTag: analysis.ollamaPriorityTag,
        ollamaReasonForPriority: analysis.ollamaReasonForPriority,
        ollamaViralPotential: analysis.ollamaViralPotential,
        ollamaModelUsed: analysis.ollamaModelUsed
      });

      console.log(`Server: Ollama analysis completed for video ${videoId}`);
      res.json({ success: true, videoId, analysis, cached: false });
    } catch (ollamaError) {
      console.error('Error in Ollama analysis:', ollamaError);
      // If there's an error with Ollama, pass it through to the client
      const errorMessage = ollamaError instanceof Error ? ollamaError.message : 'Unknown Ollama error';
      return res.status(500).json({ message: errorMessage });
    }
  } catch (error) {
    console.error('Error analyzing video with Ollama:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to analyze video with Ollama: ${errorMessage}` });
  }
});

// Refresh videos metadata only (no transcripts or AI analysis)
router.post('/refresh-metadata', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('Server: Refreshing video metadata only (no transcripts or AI analysis)');
    const startTime = Date.now();

    // Get all channels for the user
    const channels = await storage.getYoutubeChannels(req.user.id);

    if (channels.length === 0) {
      return res.status(404).json({ message: 'No channels found' });
    }

    console.log(`Found ${channels.length} channels for user ${req.user.id}`);

    // Track statistics for response
    let totalVideosRefreshed = 0;
    let totalVideosDeleted = 0;
    const channelsProcessed = [];
    const suspiciousViewCountUpdates = [];

    // Process all channels in parallel for better performance
    // This is a view-count-only operation, so it's safe to process all channels at once
    console.log(`Processing all ${channels.length} channels in parallel for view count refresh`);

    // Process all channels in parallel
    const channelResults = await Promise.all(channels.map(async (channel) => {
        try {
          const channelStartTime = Date.now();

          // Fetch videos from YouTube without transcripts or AI analysis
          const videos = await storage.fetchChannelVideos(channel);

          // Save videos to database
          let newVideos = 0;
          let updatedVideos = 0;
          let deletedVideos = 0;
          const channelSuspiciousUpdates = [];

          // Get all existing videos for this channel to check for unavailable videos
          const existingChannelVideos = await storage.getYoutubeChannelVideos(channel.id);
          const existingVideoMap = new Map(existingChannelVideos.map(v => [v.id, v]));
          const fetchedVideoIds = new Set(videos.map(v => v.id));

          // Get all video IDs (both newly fetched and existing)
          const allVideoIds = [...new Set([...fetchedVideoIds, ...existingVideoMap.keys()])];
          console.log(`Channel ${channel.channelTitle}: Processing ${allVideoIds.length} total videos (${fetchedVideoIds.size} new, ${existingVideoMap.size} existing)`);

          // Fetch view counts for all videos (both new and existing)
          const { batchFetchViewCounts, isVideoUnavailable } = await import('../utils/youtube-utils');

          // Fetch view counts for all videos in this channel
          console.log(`Fetching view counts for all ${allVideoIds.length} videos in channel ${channel.channelTitle}`);
          const viewCounts = await batchFetchViewCounts(allVideoIds, 10, false);

          // Update view counts in the newly fetched videos
          for (const video of videos) {
            if (viewCounts.has(video.id)) {
              video.viewCount = viewCounts.get(video.id);
            }
          }

          // Update view counts for existing videos that weren't in the newly fetched list
          const existingOnlyVideoIds = [...existingVideoMap.keys()].filter(id => !fetchedVideoIds.has(id));
          console.log(`Updating view counts for ${existingOnlyVideoIds.length} existing-only videos in channel ${channel.channelTitle}`);

          // Prepare batch updates for existing videos
          const existingVideoUpdates = [];

          for (const videoId of existingOnlyVideoIds) {
            if (viewCounts.has(videoId)) {
              const viewCount = viewCounts.get(videoId);
              const existingVideo = existingVideoMap.get(videoId);

              if (existingVideo && viewCount > 0) {
                existingVideoUpdates.push({
                  id: videoId,
                  updates: { viewCount }
                });
              }
            }
          }

          // Process all existing video updates in parallel
          if (existingVideoUpdates.length > 0) {
            await Promise.all(existingVideoUpdates.map(update =>
              storage.updateYoutubeVideoMetadata(update.id, update.updates, true)
            ));
            console.log(`Updated view counts for ${existingVideoUpdates.length} existing-only videos in channel ${channel.channelTitle}`);
            // Count these as updated videos too
            updatedVideos += existingVideoUpdates.length;
          }

          // Check for videos that are no longer available - do this in parallel
          const unavailableChecks = existingChannelVideos
            .filter(existingVideo => !fetchedVideoIds.has(existingVideo.id) && viewCounts.get(existingVideo.id) === -1)
            .map(async (existingVideo) => {
              // Video is no longer available, check if it's really unavailable
              const unavailable = await isVideoUnavailable(existingVideo.id);
              if (unavailable) {
                await storage.deleteYoutubeVideo(existingVideo.id);
                return true; // Count as deleted
              }
              return false;
            });

          // Wait for all unavailable checks to complete
          const unavailableResults = await Promise.all(unavailableChecks);
          deletedVideos = unavailableResults.filter(result => result).length;

          // Process each video - prepare updates in batches
          const videoUpdates = [];
          const newVideoInserts = [];

          for (const video of videos) {
            const existingVideo = existingVideoMap.get(video.id);

            if (!existingVideo) {
              // New video, add to database
              newVideoInserts.push({
                ...video,
                userId: channel.userId,
                channelId: channel.channelId
              });
            } else {
              // Check for suspicious view count changes
              if (existingVideo.viewCount > 0 && video.viewCount > 0) {
                const ratio = video.viewCount / existingVideo.viewCount;

                // If the new view count is significantly different (more than 10x higher or less than 1/10th)
                if (ratio > 10 || ratio < 0.1) {
                  console.log(`⚠️ Suspicious view count change for video ${video.id}: ${existingVideo.viewCount} -> ${video.viewCount}`);

                  // Add to suspicious updates list for reporting
                  channelSuspiciousUpdates.push({
                    videoId: video.id,
                    title: video.title,
                    oldViewCount: existingVideo.viewCount,
                    newViewCount: video.viewCount,
                    ratio: ratio
                  });
                }
              }

              // Add to update batch
              videoUpdates.push({
                id: video.id,
                updates: {
                  viewCount: video.viewCount,
                  title: video.title,
                  description: video.description,
                  thumbnail: video.thumbnail
                }
              });
            }
          }

          // Process all new video inserts in parallel
          if (newVideoInserts.length > 0) {
            await Promise.all(newVideoInserts.map(videoData =>
              storage.saveYoutubeVideo(videoData)
            ));
            newVideos = newVideoInserts.length;
          }

          // Process all video updates in parallel
          if (videoUpdates.length > 0) {
            await Promise.all(videoUpdates.map(update =>
              storage.updateYoutubeVideoMetadata(update.id, update.updates, true)
            ));
            updatedVideos = videoUpdates.length;
          }

          // Update channel's last refresh time
          await storage.updateYoutubeChannelLastRefreshTime(channel.id);

          const channelDuration = Date.now() - channelStartTime;

          return {
            id: channel.id,
            title: channel.channelTitle,
            newVideos,
            updatedVideos,
            deletedVideos,
            suspiciousUpdates: channelSuspiciousUpdates,
            duration: channelDuration
          };
        } catch (error) {
          console.error(`Error refreshing metadata for channel ${channel.channelTitle}:`, error);
          return {
            id: channel.id,
            title: channel.channelTitle,
            error: error instanceof Error ? error.message : 'Unknown error',
            newVideos: 0,
            updatedVideos: 0,
            deletedVideos: 0,
            suspiciousUpdates: []
          };
        }
      }));

    // Aggregate results from all channels
    for (const result of channelResults) {
      channelsProcessed.push(result);
      totalVideosRefreshed += (result.newVideos + result.updatedVideos);
      totalVideosDeleted += result.deletedVideos;

      if (result.suspiciousUpdates && result.suspiciousUpdates.length > 0) {
        suspiciousViewCountUpdates.push(...result.suspiciousUpdates);
      }
    }

    const totalDuration = Date.now() - startTime;

    // Return statistics in the response
    res.json({
      success: true,
      totalChannels: channels.length,
      totalVideosRefreshed,
      totalVideosDeleted,
      channelsProcessed,
      suspiciousViewCountUpdates: suspiciousViewCountUpdates.length > 0 ? suspiciousViewCountUpdates : undefined,
      duration: totalDuration,
      message: `Refreshed metadata for ${totalVideosRefreshed} videos from ${channels.length} channels in ${(totalDuration/1000).toFixed(1)}s, deleted ${totalVideosDeleted} unavailable videos${suspiciousViewCountUpdates.length > 0 ? ` (${suspiciousViewCountUpdates.length} suspicious view count changes detected)` : ''}`
    });
  } catch (error) {
    console.error('Error refreshing video metadata:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to refresh video metadata: ${errorMessage}` });
  }
});

// Refresh metadata for a specific video
router.post('/videos/:videoId/refresh-metadata', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  const { videoId } = req.params;

  try {
    const startTime = Date.now();

    // Get the video details first to check if it's a live video
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: "Video not found" });
    }

    // Check if the video belongs to the current user
    if (video.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to refresh this video" });
    }

    // Quick check if this is a live video by looking at the title
    const isLive = video?.title?.match(/(\🔴\s*LIVE|LIVE\s*NOW|\[LIVE\]|\(LIVE\)|LIVE\:|LIVE\s*STREAM|LIVESTREAM|STREAMING\s*NOW|STREAMING\s*LIVE)/i) !== null;

    // Import the fetchVideoViewCount function directly to avoid unnecessary imports
    const { fetchVideoViewCount } = await import('../utils/youtube-utils');

    // Fetch the current view count - for live videos, this will be the current viewer count
    const viewCount = await fetchVideoViewCount(videoId, false);

    if (viewCount > 0) {
      // Check for suspicious view count changes
      if (video.viewCount > 0) {
        const ratio = viewCount / video.viewCount;

        // If the new view count is significantly different (more than 10x higher or less than 1/10th)
        if (ratio > 10 || ratio < 0.1) {
          // Include warning in response
          const warning = `Warning: New view count (${viewCount}) is significantly ${ratio > 1 ? 'higher' : 'lower'} than current (${video.viewCount})`;

          // Update the view count in the database
          await storage.updateYoutubeVideoMetadata(videoId, { viewCount });

          const duration = Date.now() - startTime;

          res.json({
            success: true,
            viewCount,
            isLive,
            previousViewCount: video.viewCount,
            warning,
            duration
          });
          return;
        }
      }

      // Update the view count in the database
      await storage.updateYoutubeVideoMetadata(videoId, { viewCount });

      const duration = Date.now() - startTime;

      res.json({
        success: true,
        viewCount,
        isLive,
        previousViewCount: video.viewCount,
        duration
      });
    } else {
      const duration = Date.now() - startTime;
      res.status(400).json({
        success: false,
        message: `Could not fetch ${isLive ? 'live viewer' : 'view'} count`,
        duration
      });
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`Error refreshing metadata for video ${videoId}:`, error);
    res.status(500).json({
      success: false,
      message: "Failed to refresh metadata",
      error: error instanceof Error ? error.message : 'Unknown error',
      duration
    });
  }
});

export default router;
