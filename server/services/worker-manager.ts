import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { isClientIdle } from '../routes/client-state';

// Task status enum
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Task priority enum
export enum TaskPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2
}

// Task type
export interface Task {
  id: string;
  name: string;
  description: string;
  status: TaskStatus;
  progress: number;
  error?: string;
  result?: any;
  userId: number;
  createdAt: Date;
  updatedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  priority: TaskPriority;
  type: string;
  data: any;
}

// Task handler type
export type TaskHandler = (task: Task, updateProgress: (progress: number) => void) => Promise<any>;

// Worker manager class
export class WorkerManager extends EventEmitter {
  private static instance: WorkerManager;
  private tasks: Map<string, Task> = new Map();
  private queue: string[] = [];
  private handlers: Map<string, TaskHandler> = new Map();
  private running: boolean = false;
  private maxConcurrent: number = 3;
  private currentRunning: number = 0;

  private constructor() {
    super();
  }

  // Get singleton instance
  public static getInstance(): WorkerManager {
    if (!WorkerManager.instance) {
      WorkerManager.instance = new WorkerManager();
    }
    return WorkerManager.instance;
  }

  // Register a task handler
  public registerHandler(type: string, handler: TaskHandler): void {
    this.handlers.set(type, handler);
    console.log(`Registered handler for task type: ${type}`);
  }

  // Add a task to the queue
  public addTask(
    type: string,
    name: string,
    description: string,
    userId: number,
    data: any,
    priority: TaskPriority = TaskPriority.NORMAL
  ): Task {
    const id = uuidv4();
    const task: Task = {
      id,
      name,
      description,
      status: TaskStatus.PENDING,
      progress: 0,
      userId,
      createdAt: new Date(),
      updatedAt: new Date(),
      priority,
      type,
      data
    };

    this.tasks.set(id, task);
    this.queue.push(id);

    // Sort queue by priority
    this.sortQueue();

    console.log(`Added task ${id} (${name}) to queue`);

    // Log the current state of tasks and queue
    const taskCount = this.tasks.size;
    const queueCount = this.queue.length;
    console.log(`Current state: ${taskCount} tasks in map, ${queueCount} tasks in queue`);

    this.emit('task:added', task);

    // Start processing if not already running
    if (!this.running) {
      this.start();
    }

    return task;
  }

  // Get a task by ID
  public getTask(id: string): Task | undefined {
    return this.tasks.get(id);
  }

  // Get all tasks for a user
  public getUserTasks(userId: number): Task[] {
    return Array.from(this.tasks.values())
      .filter(task => task.userId === userId);
  }

  // Get recent tasks for a user
  public getRecentUserTasks(userId: number, limit: number = 10): Task[] {
    return Array.from(this.tasks.values())
      .filter(task => task.userId === userId)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      .slice(0, limit);
  }

  // Keep track of previous tasks count to avoid excessive logging
  private prevTasksCount = 0;
  private prevStatusCounts = {};

  // Get all tasks
  public getAllTasks(): Task[] {
    const tasks = Array.from(this.tasks.values());
    const statusCounts = tasks.reduce((acc, task) => {
      acc[task.status] = (acc[task.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Only log when there's a change in the tasks count or status distribution
    const statusCountsStr = JSON.stringify(statusCounts);
    if (tasks.length !== this.prevTasksCount || statusCountsStr !== JSON.stringify(this.prevStatusCounts)) {
      console.log(`Tasks status changed: ${tasks.length} tasks. Status counts:`, statusCounts);
      this.prevTasksCount = tasks.length;
      this.prevStatusCounts = JSON.parse(statusCountsStr);
    }

    return tasks.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  // Cancel a task
  public cancelTask(id: string): boolean {
    const task = this.tasks.get(id);
    if (!task) return false;

    if (task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING) {
      // Store original status to check if it was running
      const wasRunning = task.status === TaskStatus.RUNNING;

      // Remove from queue if pending
      const index = this.queue.indexOf(id);
      if (index !== -1) {
        this.queue.splice(index, 1);
      }

      // Mark as cancelled
      task.status = TaskStatus.CANCELLED;
      task.updatedAt = new Date();
      task.error = 'Task was cancelled by user';
      this.tasks.set(id, task);
      this.emit('task:cancelled', task);

      // If it was running, decrement the running count
      if (wasRunning) {
        console.log(`Cancelling running task ${id}, decrementing running count`);
        this.currentRunning = Math.max(0, this.currentRunning - 1);
      }

      return true;
    }

    return false;
  }

  // Remove a task completely (for clearing history)
  public removeTask(id: string): boolean {
    const task = this.tasks.get(id);
    if (!task) return false;

    // Only allow removing completed, failed, or cancelled tasks
    if (task.status === TaskStatus.COMPLETED ||
        task.status === TaskStatus.FAILED ||
        task.status === TaskStatus.CANCELLED) {

      // Remove from tasks map
      this.tasks.delete(id);

      // Also remove from queue if somehow still there
      const index = this.queue.indexOf(id);
      if (index !== -1) {
        this.queue.splice(index, 1);
      }

      this.emit('task:removed', task);
      return true;
    }

    // Can't remove pending or running tasks
    return false;
  }

  // Start processing the queue
  public start(): void {
    if (this.running) return;

    this.running = true;
    this.processQueue();
    console.log('Worker manager started');
  }

  // Stop processing the queue
  public stop(): void {
    this.running = false;
    console.log('Worker manager stopped');
  }

  // Keep track of previous queue state to avoid excessive logging
  private prevQueueLength = 0;
  private prevRunningTasks = 0;

  // Process the next task in the queue
  private async processQueue(): Promise<void> {
    // Only log when there's a change in the queue status
    if (this.queue.length !== this.prevQueueLength || this.currentRunning !== this.prevRunningTasks) {
      console.log(`Queue status changed: length=${this.queue.length}, running=${this.currentRunning}, maxConcurrent=${this.maxConcurrent}`);
      this.prevQueueLength = this.queue.length;
      this.prevRunningTasks = this.currentRunning;
    }

    // If we're not running, have no tasks, or at max capacity, schedule a check with longer delay
    if (!this.running || this.queue.length === 0 || this.currentRunning >= this.maxConcurrent) {
      // Use a longer delay when idle to reduce CPU usage
      const delay = 1000; // Increase to 1 second (from 500ms) to reduce CPU usage
      setTimeout(() => this.processQueue(), delay);
      return;
    }

    // Get the next task
    const taskId = this.queue[0]; // Peek at the next task without removing it yet
    if (!taskId) {
      setTimeout(() => this.processQueue(), 1000);
      return;
    }

    const task = this.tasks.get(taskId);
    if (!task) {
      this.queue.shift(); // Remove invalid task
      setTimeout(() => this.processQueue(), 100);
      return;
    }

    // Check if the user's client is idle
    const isUserIdle = isClientIdle(task.userId);

    // If the user is idle and this is a low priority task, delay processing
    if (isUserIdle && task.priority === TaskPriority.LOW) {
      // Use a much longer delay for low priority tasks when the client is idle
      setTimeout(() => this.processQueue(), 5000); // 5 seconds
      return;
    }

    // Now remove the task from the queue
    this.queue.shift();

    // Update task status
    task.status = TaskStatus.RUNNING;
    task.startedAt = new Date();
    task.updatedAt = new Date();
    this.tasks.set(taskId, task);
    this.currentRunning++;

    this.emit('task:started', task);
    console.log(`Started task ${taskId} (${task.name})`);

    // Process next task after a short delay to prevent CPU spikes
    // Only check for more tasks if we have capacity and tasks in queue
    if (this.queue.length > 0 && this.currentRunning < this.maxConcurrent) {
      setTimeout(() => this.processQueue(), 50);
    }

    try {
      // Get handler for this task type
      const handler = this.handlers.get(task.type);
      if (!handler) {
        throw new Error(`No handler registered for task type: ${task.type}`);
      }

      // Create progress update function
      const updateProgress = (progress: number) => {
        const updatedTask = this.tasks.get(taskId);
        if (updatedTask && updatedTask.status === TaskStatus.RUNNING) {
          updatedTask.progress = Math.min(Math.max(0, progress), 100);
          updatedTask.updatedAt = new Date();
          this.tasks.set(taskId, updatedTask);
          this.emit('task:progress', updatedTask);
        }
      };

      // Execute the task
      const result = await handler(task, updateProgress);

      // Update task with result
      const completedTask = this.tasks.get(taskId);
      if (completedTask) {
        completedTask.status = TaskStatus.COMPLETED;
        completedTask.progress = 100;
        completedTask.result = result;
        completedTask.completedAt = new Date();
        completedTask.updatedAt = new Date();
        this.tasks.set(taskId, completedTask);
        this.emit('task:completed', completedTask);
        console.log(`Completed task ${taskId} (${task.name})`);
      }
    } catch (error) {
      // Update task with error
      const failedTask = this.tasks.get(taskId);
      if (failedTask) {
        failedTask.status = TaskStatus.FAILED;
        failedTask.error = error.message || 'Unknown error';
        failedTask.updatedAt = new Date();
        this.tasks.set(taskId, failedTask);
        this.emit('task:failed', failedTask);
        console.error(`Failed task ${taskId} (${task.name}):`, error);
      }
    } finally {
      this.currentRunning--;

      // Check if we need to process more tasks
      if (this.queue.length > 0 && this.running) {
        // Check if the next task is for an idle user
        const nextTaskId = this.queue[0];
        if (nextTaskId) {
          const nextTask = this.tasks.get(nextTaskId);
          if (nextTask && isClientIdle(nextTask.userId)) {
            // If the user is idle, use a longer delay
            setTimeout(() => this.processQueue(), 1000);
            return;
          }
        }

        // Use a short delay to prevent CPU spikes
        setTimeout(() => this.processQueue(), 100); // Increased from 50ms
      } else {
        // Use a longer delay when queue is empty
        setTimeout(() => this.processQueue(), 2000); // Increased from 500ms
      }
    }
  }

  // Sort queue by priority
  private sortQueue(): void {
    this.queue.sort((a, b) => {
      const taskA = this.tasks.get(a);
      const taskB = this.tasks.get(b);
      if (!taskA || !taskB) return 0;
      return taskB.priority - taskA.priority;
    });
  }

  // Clean up old tasks
  public cleanupTasks(maxAge: number = 30 * 24 * 60 * 60 * 1000): void {
    // Increase retention period to 30 days
    const now = new Date().getTime();
    for (const [id, task] of this.tasks.entries()) {
      if (
        (task.status === TaskStatus.COMPLETED ||
         task.status === TaskStatus.FAILED ||
         task.status === TaskStatus.CANCELLED) &&
        task.updatedAt.getTime() + maxAge < now
      ) {
        this.tasks.delete(id);
      }
    }
  }
}

// Export singleton instance
export const workerManager = WorkerManager.getInstance();
