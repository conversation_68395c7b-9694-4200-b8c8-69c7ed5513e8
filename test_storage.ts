// Simple test for storage functionality
import Database from 'better-sqlite3';

console.log('Starting storage test...');

try {
  console.log('Opening database...');
  const db = new Database('data.db');
  
  console.log('Checking users table...');
  const users = db.prepare('SELECT * FROM users LIMIT 5').all();
  console.log('Users:', users);
  
  console.log('Checking settings table...');
  const settings = db.prepare('SELECT * FROM settings LIMIT 5').all();
  console.log('Settings:', settings);
  
  console.log('Checking admin_settings table...');
  const adminSettings = db.prepare('SELECT * FROM admin_settings LIMIT 5').all();
  console.log('Admin Settings:', adminSettings);
  
  console.log('Database test completed successfully');
  db.close();
} catch (error) {
  console.error('Database test failed:', error);
}
