import React, { useState, use<PERSON>emo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, TrendingUp, TrendingDown, Clock, Target, Zap, Eye, Users, Calendar, BarChart3, Activity, AlertTriangle, CheckCircle, ArrowUp, ArrowDown, Minus } from 'lucide-react';
import { ChannelRankingGraph } from './channel-ranking-graph';

interface RankingDataPoint {
  timestamp: number;
  ranking: number;
  channelName: string;
  urlName: string;
}

interface ChannelMetrics {
  name: string;
  videoCount: number;
  totalViews: number;
  avgViews: number;
  avgVph: number;
  newestVideoDate: Date;
  oldestVideoDate: Date;
  viewsPerVideo: number;
  rankingPositions: number[];
}

interface UrlMetrics {
  [url: string]: {
    urlName: string;
    videoCount: number;
    totalViews: number;
    bestRanking: number;
    rankingPositions: number[];
    videoRankings?: { [videoId: string]: number };
  };
}

interface Video {
  id: string;
  title: string;
  channelTitle: string;
  publishedAt: string;
  viewCount?: number;
  statistics?: {
    viewCount: string;
  };
  vph?: number;
  thumbnail?: string;
  contentDetails?: {
    duration: string;
  };
}

interface AdvancedMetrics {
  audienceRetention?: number;
  clickThroughRate?: number;
  engagementVelocity?: number;
  shareRate?: number;
  subscriberConversion?: number;
  avgViewDuration?: number;
  peakViewingTime?: string;
  geographicPerformance?: { [country: string]: number };
  devicePerformance?: { mobile: number; desktop: number; tablet: number };
  trafficSources?: { [source: string]: number };
}

interface TrendingTopic {
  keyword: string;
  momentum: number;
  competitorCoverage: number;
  opportunityScore: number;
  predictedPeak?: string;
  currentVolume: number;
}

interface ContentGap {
  topic: string;
  competitorVideos: number;
  yourVideos: number;
  avgViews: number;
  opportunityScore: number;
  suggestedAction: string;
}

interface PerformanceHeatmap {
  hour: number;
  day: number;
  performance: number;
  videoCount: number;
}

interface CompetitorIntelligence {
  name: string;
  responseTime: number; // hours to respond to trends
  contentFrequency: number; // videos per week
  avgPerformance: number;
  strengths: string[];
  weaknesses: string[];
  nextPredictedUpload?: string;
}

interface ChannelPerformanceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  channelMetrics: ChannelMetrics[];
  urlMetrics: UrlMetrics;
  yourChannelName: string;
  trackedChannelName: string;
  rankingHistory?: RankingDataPoint[];
  videos?: Video[];
}

export function ChannelPerformanceDialog({
  isOpen,
  onClose,
  channelMetrics,
  urlMetrics,
  yourChannelName,
  trackedChannelName,
  rankingHistory = [],
  videos = []
}: ChannelPerformanceDialogProps) {
  const [activeTab, setActiveTab] = useState('overview');

  // Sort channels by total views
  const sortedChannels = [...channelMetrics].sort((a, b) => b.totalViews - a.totalViews);

  // Get the tracked channel metrics
  const trackedChannel = channelMetrics.find(c => c.name === trackedChannelName);

  // Calculate enhanced metrics with REAL data
  const enhancedMetrics = useMemo(() => {
    const trackedVideos = videos.filter(v => v.channelTitle === trackedChannelName);

    if (trackedVideos.length === 0) {
      return {
        totalVideos: 0,
        totalViews: 0,
        avgViews: 0,
        avgVph: 0,
        recentPerformance: 0,
        contentConsistency: 0,
        competitivePosition: 0
      };
    }

    const totalViews = trackedVideos.reduce((sum, v) => {
      const views = v.statistics?.viewCount ? Number(v.statistics.viewCount) : (v.viewCount || 0);
      return sum + views;
    }, 0);

    const avgViews = totalViews / trackedVideos.length;

    // Calculate average VPH (Views Per Hour)
    const totalVph = trackedVideos.reduce((sum, v) => sum + (v.vph || 0), 0);
    const avgVph = totalVph / trackedVideos.length;

    // Calculate recent performance (last 7 days vs older videos)
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const recentVideos = trackedVideos.filter(v => new Date(v.publishedAt) > sevenDaysAgo);
    const olderVideos = trackedVideos.filter(v => new Date(v.publishedAt) <= sevenDaysAgo);

    const recentAvgViews = recentVideos.length > 0 ?
      recentVideos.reduce((sum, v) => {
        const views = v.statistics?.viewCount ? Number(v.statistics.viewCount) : (v.viewCount || 0);
        return sum + views;
      }, 0) / recentVideos.length : 0;

    const olderAvgViews = olderVideos.length > 0 ?
      olderVideos.reduce((sum, v) => {
        const views = v.statistics?.viewCount ? Number(v.statistics.viewCount) : (v.viewCount || 0);
        return sum + views;
      }, 0) / olderVideos.length : 0;

    const recentPerformance = olderAvgViews > 0 ?
      ((recentAvgViews - olderAvgViews) / olderAvgViews) * 100 : 0;

    // Calculate content consistency (how consistent view counts are)
    const viewCounts = trackedVideos.map(v =>
      v.statistics?.viewCount ? Number(v.statistics.viewCount) : (v.viewCount || 0)
    );
    const variance = viewCounts.reduce((sum, views) =>
      sum + Math.pow(views - avgViews, 2), 0) / viewCounts.length;
    const stdDev = Math.sqrt(variance);
    const coefficientOfVariation = avgViews > 0 ? (stdDev / avgViews) : 0;
    const contentConsistency = Math.max(0, 100 - (coefficientOfVariation * 100));

    // Calculate competitive position (ranking in current results)
    const allChannelViews = {};
    videos.forEach(v => {
      const views = v.statistics?.viewCount ? Number(v.statistics.viewCount) : (v.viewCount || 0);
      if (!allChannelViews[v.channelTitle]) {
        allChannelViews[v.channelTitle] = { totalViews: 0, videoCount: 0 };
      }
      allChannelViews[v.channelTitle].totalViews += views;
      allChannelViews[v.channelTitle].videoCount += 1;
    });

    const channelAvgViews = Object.entries(allChannelViews).map(([channel, data]) => ({
      channel,
      avgViews: data.totalViews / data.videoCount
    })).sort((a, b) => b.avgViews - a.avgViews);

    const yourRank = channelAvgViews.findIndex(c => c.channel === trackedChannelName) + 1;
    const totalChannels = channelAvgViews.length;
    const competitivePosition = totalChannels > 0 ?
      ((totalChannels - yourRank + 1) / totalChannels) * 100 : 0;

    return {
      totalVideos: trackedVideos.length,
      totalViews,
      avgViews,
      avgVph,
      recentPerformance,
      contentConsistency,
      competitivePosition
    };
  }, [videos, trackedChannelName]);



  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="pb-2 flex-shrink-0">
          <DialogTitle className="flex items-center justify-between">
            <span>Enhanced Channel Performance Analysis</span>
            <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 pr-2">{/* Scrollable content */}
          {/* Top section - HowToGuys stats and Top Channels */}
          <div className="flex gap-4">
            {/* HowToGuys Channel Stats */}
            {trackedChannel && (
              <div className="bg-green-500/20 p-3 rounded-md border border-green-500/30 flex-1 min-w-[200px]">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-green-400">@{trackedChannelName}</h3>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-green-400">{trackedChannel.videoCount} videos</span>
                    <span className="text-xs text-green-400">#{sortedChannels.findIndex(c => c.name === trackedChannelName) + 1} rank</span>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-3">
                  <div className="bg-muted/30 rounded p-2">
                    <div className="text-sm text-muted-foreground">Best Rank</div>
                    <div className="text-lg font-medium">#{Math.min(...trackedChannel.rankingPositions)}</div>
                  </div>
                  <div className="bg-muted/30 rounded p-2">
                    <div className="text-sm text-muted-foreground">Total Views</div>
                    <div className="text-lg font-medium">{trackedChannel.totalViews.toLocaleString()}</div>
                  </div>
                  <div className="bg-muted/30 rounded p-2">
                    <div className="text-sm text-muted-foreground">Avg Views</div>
                    <div className="text-lg font-medium">{Math.round(trackedChannel.avgViews).toLocaleString()}</div>
                  </div>
                </div>

                {/* Enhanced Real Metrics */}
                <div className="mt-3 grid grid-cols-3 gap-2">
                  <div className="bg-blue-500/20 rounded p-2 text-center">
                    <div className="text-xs text-blue-300">Avg VPH</div>
                    <div className="text-sm font-bold text-white">{enhancedMetrics.avgVph.toFixed(1)}</div>
                  </div>
                  <div className="bg-yellow-500/20 rounded p-2 text-center">
                    <div className="text-xs text-yellow-300">Recent Trend</div>
                    <div className={`text-sm font-bold ${enhancedMetrics.recentPerformance >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {enhancedMetrics.recentPerformance >= 0 ? '+' : ''}{enhancedMetrics.recentPerformance.toFixed(1)}%
                    </div>
                  </div>
                  <div className="bg-purple-500/20 rounded p-2 text-center">
                    <div className="text-xs text-purple-300">Consistency</div>
                    <div className="text-sm font-bold text-white">{enhancedMetrics.contentConsistency.toFixed(1)}%</div>
                  </div>
                </div>

                {/* Competitive Position */}
                <div className="mt-2">
                  <div className="bg-green-500/20 rounded p-2 text-center">
                    <div className="text-xs text-green-300">Competitive Position</div>
                    <div className="text-sm font-bold text-white">
                      {enhancedMetrics.competitivePosition.toFixed(1)}%
                      <span className="text-xs ml-1">
                        ({enhancedMetrics.competitivePosition >= 80 ? 'Top Tier' :
                          enhancedMetrics.competitivePosition >= 60 ? 'Strong' :
                          enhancedMetrics.competitivePosition >= 40 ? 'Average' : 'Needs Work'})
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Top Channels */}
            <div className="bg-muted/20 p-3 rounded-md flex-1 min-w-[250px]">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-blue-400">Top Channels</h3>
                <span className="text-xs text-muted-foreground">{channelMetrics.length} total</span>
              </div>

              <ScrollArea className="h-[180px] pr-2">
                <div className="space-y-1">
                  {sortedChannels.map((channel, idx) => (
                    <div
                      key={idx}
                      className={`flex justify-between items-center py-1.5 px-2 rounded ${
                        channel.name === trackedChannelName
                          ? 'bg-green-500/20 border-l-2 border-l-green-500 font-medium'
                          : 'hover:bg-muted/30'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{idx + 1}.</span>
                        <span className="text-sm truncate max-w-[150px]" title={channel.name}>{channel.name}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="text-xs text-muted-foreground">{channel.videoCount}v</span>
                        <span className="text-sm font-medium">{channel.totalViews.toLocaleString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>

          {/* URL Performance */}
          <div className="bg-muted/10 p-3 rounded-md">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-green-400">URL Performance</h3>
              <span className="text-xs text-muted-foreground">
                {Object.keys(urlMetrics).length} URLs
              </span>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {Object.entries(urlMetrics).length > 0 ? (
                Object.entries(urlMetrics).map(([url, metrics]) => (
                  <div
                    key={url}
                    className="p-2 bg-green-500/10 rounded border border-green-500/20"
                  >
                    <div className="font-medium text-green-300 mb-1 truncate" title={metrics.urlName}>
                      {metrics.urlName}
                    </div>
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      <div>
                        <div className="text-xs text-muted-foreground">Videos</div>
                        <div className="font-medium">{metrics.videoCount || 0}</div>
                      </div>
                      <div>
                        <div className="text-xs text-muted-foreground">Rank</div>
                        <div className="font-medium">#{metrics.bestRanking || '-'}</div>
                      </div>
                      <div>
                        <div className="text-xs text-muted-foreground">Views</div>
                        <div className="font-medium">{metrics.totalViews ? metrics.totalViews.toLocaleString() : '0'}</div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-sm text-yellow-400 p-2 col-span-full text-center">
                  No URL performance data available
                </div>
              )}
            </div>
          </div>

          {/* Market Share Analysis with Pie Chart */}
          {channelMetrics.length > 0 && (
            <div className="bg-muted/10 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-green-400">Market Share Analysis</h3>
                <span className="text-xs text-muted-foreground">
                  {channelMetrics.length} channels analyzed
                </span>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Pie Chart */}
                <div className="flex flex-col items-center">
                  <h4 className="text-xs font-medium text-green-300 mb-2">Views Distribution</h4>
                  {(() => {
                    const totalViews = channelMetrics.reduce((sum, channel) => sum + channel.totalViews, 0);
                    const trackedChannel = channelMetrics.find(c => c.name === trackedChannelName);
                    const trackedViews = trackedChannel?.totalViews || 0;
                    const trackedShare = totalViews > 0 ? (trackedViews / totalViews) * 100 : 0;

                    // Get top 5 channels by views
                    const topChannels = [...channelMetrics]
                      .sort((a, b) => b.totalViews - a.totalViews)
                      .slice(0, 5);

                    const otherViews = channelMetrics
                      .filter(c => !topChannels.includes(c))
                      .reduce((sum, c) => sum + c.totalViews, 0);

                    const chartData = [
                      ...topChannels.map((channel, idx) => ({
                        name: channel.name,
                        views: channel.totalViews,
                        percentage: (channel.totalViews / totalViews) * 100,
                        color: channel.name === trackedChannelName ? '#10b981' :
                               idx === 0 ? '#3b82f6' :
                               idx === 1 ? '#8b5cf6' :
                               idx === 2 ? '#f59e0b' :
                               idx === 3 ? '#ef4444' : '#6b7280'
                      })),
                      ...(otherViews > 0 ? [{
                        name: 'Others',
                        views: otherViews,
                        percentage: (otherViews / totalViews) * 100,
                        color: '#374151'
                      }] : [])
                    ];

                    const radius = 60;
                    const centerX = 80;
                    const centerY = 80;
                    let currentAngle = 0;

                    return (
                      <div className="relative">
                        <svg width="160" height="160" className="transform -rotate-90">
                          {chartData.map((segment, idx) => {
                            const angle = (segment.percentage / 100) * 360;
                            const startAngle = currentAngle;
                            const endAngle = currentAngle + angle;

                            const x1 = centerX + radius * Math.cos((startAngle * Math.PI) / 180);
                            const y1 = centerY + radius * Math.sin((startAngle * Math.PI) / 180);
                            const x2 = centerX + radius * Math.cos((endAngle * Math.PI) / 180);
                            const y2 = centerY + radius * Math.sin((endAngle * Math.PI) / 180);

                            const largeArcFlag = angle > 180 ? 1 : 0;

                            const pathData = [
                              `M ${centerX} ${centerY}`,
                              `L ${x1} ${y1}`,
                              `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                              'Z'
                            ].join(' ');

                            currentAngle += angle;

                            return (
                              <path
                                key={idx}
                                d={pathData}
                                fill={segment.color}
                                stroke="#1f2937"
                                strokeWidth="1"
                                opacity={segment.name === trackedChannelName ? 1 : 0.8}
                              >
                                <title>{segment.name}: {segment.views.toLocaleString()} views ({segment.percentage.toFixed(1)}%)</title>
                              </path>
                            );
                          })}
                        </svg>

                        {/* Center text */}
                        <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
                          <div className="text-lg font-bold text-green-400">{trackedShare.toFixed(1)}%</div>
                          <div className="text-xs text-green-300">Market Share</div>
                        </div>
                      </div>
                    );
                  })()}
                </div>

                {/* Market Share Details */}
                <div>
                  <h4 className="text-xs font-medium text-green-300 mb-2">Competitive Analysis</h4>
                  {(() => {
                    const totalViews = channelMetrics.reduce((sum, channel) => sum + channel.totalViews, 0);
                    const trackedChannel = channelMetrics.find(c => c.name === trackedChannelName);
                    const trackedViews = trackedChannel?.totalViews || 0;
                    const trackedShare = totalViews > 0 ? (trackedViews / totalViews) * 100 : 0;

                    const sortedChannels = [...channelMetrics].sort((a, b) => b.totalViews - a.totalViews);
                    const trackedRank = sortedChannels.findIndex(c => c.name === trackedChannelName) + 1;

                    return (
                      <div className="space-y-2">
                        {/* Key Metrics */}
                        <div className="grid grid-cols-2 gap-2">
                          <div className="bg-green-500/20 p-2 rounded text-center">
                            <div className="text-xs text-green-300">Market Position</div>
                            <div className="text-sm font-bold text-green-400">#{trackedRank} of {channelMetrics.length}</div>
                          </div>
                          <div className="bg-blue-500/20 p-2 rounded text-center">
                            <div className="text-xs text-blue-300">Share vs Leader</div>
                            <div className="text-sm font-bold text-blue-400">
                              {sortedChannels[0] && trackedChannel ?
                                `${((trackedViews / sortedChannels[0].totalViews) * 100).toFixed(1)}%` :
                                'N/A'
                              }
                            </div>
                          </div>
                        </div>

                        {/* Top Competitors */}
                        <div>
                          <div className="text-xs text-muted-foreground mb-1">Top Competitors:</div>
                          <div className="space-y-1 max-h-[120px] overflow-y-auto">
                            {sortedChannels.slice(0, 6).map((channel, idx) => {
                              const share = (channel.totalViews / totalViews) * 100;
                              const isTracked = channel.name === trackedChannelName;

                              return (
                                <div
                                  key={idx}
                                  className={`flex items-center justify-between p-1 rounded text-xs ${
                                    isTracked ? 'bg-green-500/20 border border-green-500/30' : 'bg-muted/20'
                                  }`}
                                >
                                  <div className="flex items-center gap-1">
                                    <span className="font-medium">#{idx + 1}</span>
                                    <span className={`truncate max-w-[80px] ${isTracked ? 'text-green-400 font-medium' : ''}`}>
                                      {channel.name}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <span className="font-medium">{share.toFixed(1)}%</span>
                                    <div className="w-8 bg-gray-700 rounded-full h-1">
                                      <div
                                        className={`h-1 rounded-full ${isTracked ? 'bg-green-400' : 'bg-blue-400'}`}
                                        style={{ width: `${Math.min(share * 2, 100)}%` }}
                                      ></div>
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>

                        {/* Market Insights */}
                        <div className="bg-yellow-500/10 p-2 rounded border border-yellow-500/20">
                          <div className="text-xs font-medium text-yellow-400 mb-1">Market Insights:</div>
                          <div className="text-xs text-yellow-300">
                            {trackedShare >= 25 ? "🏆 Dominant position - maintain leadership" :
                             trackedShare >= 15 ? "🚀 Strong position - opportunity to lead" :
                             trackedShare >= 10 ? "📈 Competitive position - growth potential" :
                             trackedShare >= 5 ? "⚡ Emerging player - scale content" :
                             "🌱 Early stage - focus on quality & consistency"}
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>
            </div>
          )}

          {/* Real Trending Topics Analysis */}
          {videos.length > 0 && (
            <div className="bg-muted/10 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-orange-400">Trending Topics Analysis</h3>
                <span className="text-xs text-muted-foreground">
                  Based on {videos.length} videos
                </span>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {(() => {
                  // Extract keywords from video titles
                  const keywordCounts = {};
                  const socialSecurityTerms = ['social security', 'ssi', 'ssdi', 'disability', 'retirement'];
                  const stimulusTerms = ['stimulus', 'check', 'payment', 'money', 'cash'];
                  const benefitTerms = ['benefits', 'medicare', 'medicaid', 'snap', 'food stamps'];
                  const financialTerms = ['tax', 'refund', 'irs', 'credit', 'income'];

                  const allTerms = [...socialSecurityTerms, ...stimulusTerms, ...benefitTerms, ...financialTerms];

                  allTerms.forEach(term => {
                    const count = videos.filter(v =>
                      v.title.toLowerCase().includes(term.toLowerCase())
                    ).length;
                    if (count > 0) {
                      keywordCounts[term] = count;
                    }
                  });

                  // Sort by frequency and take top 8
                  const topKeywords = Object.entries(keywordCounts)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 8);

                  return topKeywords.map(([keyword, count]) => {
                    const percentage = ((count / videos.length) * 100);
                    const isHot = percentage > 15;
                    const isWarm = percentage > 8;

                    return (
                      <div
                        key={keyword}
                        className={`p-2 rounded border text-center ${
                          isHot ? 'bg-red-500/20 border-red-500/30' :
                          isWarm ? 'bg-orange-500/20 border-orange-500/30' :
                          'bg-blue-500/20 border-blue-500/30'
                        }`}
                      >
                        <div className="text-xs font-medium capitalize">{keyword}</div>
                        <div className="text-sm font-bold">
                          {count} videos ({percentage.toFixed(1)}%)
                        </div>
                        <div className="text-xs">
                          {isHot ? '🔥 Hot' : isWarm ? '📈 Trending' : '📊 Active'}
                        </div>
                      </div>
                    );
                  });
                })()}
              </div>
            </div>
          )}

          {/* Real Content Gap Analysis */}
          {videos.length > 0 && (
            <div className="bg-muted/10 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-red-400">Content Gap Analysis</h3>
                <span className="text-xs text-muted-foreground">
                  Opportunities vs Competitors
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {(() => {
                  // Find topics that competitors cover but @HowToGuys doesn't
                  const trackedVideos = videos.filter(v => v.channelTitle === trackedChannelName);
                  const competitorVideos = videos.filter(v => v.channelTitle !== trackedChannelName);

                  const trackedTopics = new Set();
                  const competitorTopics = {};

                  // Extract topics from @HowToGuys videos
                  trackedVideos.forEach(v => {
                    const title = v.title.toLowerCase();
                    if (title.includes('medicare')) trackedTopics.add('medicare');
                    if (title.includes('disability')) trackedTopics.add('disability');
                    if (title.includes('unemployment')) trackedTopics.add('unemployment');
                    if (title.includes('housing')) trackedTopics.add('housing');
                    if (title.includes('food stamp')) trackedTopics.add('food stamps');
                    if (title.includes('veteran')) trackedTopics.add('veterans');
                    if (title.includes('child tax')) trackedTopics.add('child tax credit');
                    if (title.includes('earned income')) trackedTopics.add('earned income credit');
                  });

                  // Count competitor coverage of topics
                  competitorVideos.forEach(v => {
                    const title = v.title.toLowerCase();
                    if (title.includes('medicare')) competitorTopics['medicare'] = (competitorTopics['medicare'] || 0) + 1;
                    if (title.includes('disability')) competitorTopics['disability'] = (competitorTopics['disability'] || 0) + 1;
                    if (title.includes('unemployment')) competitorTopics['unemployment'] = (competitorTopics['unemployment'] || 0) + 1;
                    if (title.includes('housing')) competitorTopics['housing'] = (competitorTopics['housing'] || 0) + 1;
                    if (title.includes('food stamp')) competitorTopics['food stamps'] = (competitorTopics['food stamps'] || 0) + 1;
                    if (title.includes('veteran')) competitorTopics['veterans'] = (competitorTopics['veterans'] || 0) + 1;
                    if (title.includes('child tax')) competitorTopics['child tax credit'] = (competitorTopics['child tax credit'] || 0) + 1;
                    if (title.includes('earned income')) competitorTopics['earned income credit'] = (competitorTopics['earned income credit'] || 0) + 1;
                  });

                  // Find gaps (topics competitors cover but you don't)
                  const gaps = Object.entries(competitorTopics)
                    .filter(([topic, count]) => !trackedTopics.has(topic) && count >= 2)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 6);

                  if (gaps.length === 0) {
                    return (
                      <div className="col-span-2 text-center text-green-400 p-4">
                        ✅ Great coverage! No major content gaps found.
                      </div>
                    );
                  }

                  return gaps.map(([topic, count]) => {
                    const avgViews = competitorVideos
                      .filter(v => v.title.toLowerCase().includes(topic.toLowerCase()))
                      .reduce((sum, v) => {
                        const views = v.statistics?.viewCount ? Number(v.statistics.viewCount) : (v.viewCount || 0);
                        return sum + views;
                      }, 0) / count;

                    const opportunityScore = Math.min(100, (count * 10) + (avgViews / 1000));

                    return (
                      <div
                        key={topic}
                        className="p-3 bg-red-500/10 rounded border border-red-500/20"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="font-medium text-red-300 capitalize">{topic}</div>
                          <div className="text-xs bg-red-500/20 px-2 py-1 rounded">
                            Gap Found
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <div className="text-xs text-muted-foreground">Competitor Videos</div>
                            <div className="font-medium">{count}</div>
                          </div>
                          <div>
                            <div className="text-xs text-muted-foreground">Avg Views</div>
                            <div className="font-medium">{avgViews.toLocaleString()}</div>
                          </div>
                        </div>
                        <div className="mt-2">
                          <div className="text-xs text-muted-foreground">Opportunity Score</div>
                          <div className="flex items-center gap-2">
                            <div className="flex-1 bg-muted/30 rounded-full h-2">
                              <div
                                className="bg-red-400 h-2 rounded-full"
                                style={{ width: `${Math.min(100, opportunityScore)}%` }}
                              />
                            </div>
                            <div className="text-xs font-medium">{opportunityScore.toFixed(0)}/100</div>
                          </div>
                        </div>
                      </div>
                    );
                  });
                })()}
              </div>
            </div>
          )}

          {/* Peak Timing Analysis */}
          {videos.length > 0 && (
            <div className="bg-muted/10 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-cyan-400">Peak Timing Analysis</h3>
                <span className="text-xs text-muted-foreground">
                  Based on top performing videos
                </span>
              </div>

              {(() => {
                // Get videos with view history and calculate peak times
                const videosWithHistory = videos.filter(video => {
                  const viewCount = video.statistics?.viewCount ? Number(video.statistics.viewCount) : (video.viewCount || 0);
                  return viewCount > 0 && video.publishedAt;
                }).sort((a, b) => {
                  const aViews = a.statistics?.viewCount ? Number(a.statistics.viewCount) : (a.viewCount || 0);
                  const bViews = b.statistics?.viewCount ? Number(b.statistics.viewCount) : (b.viewCount || 0);
                  return bViews - aViews;
                }).slice(0, 10); // Top 10 videos for analysis

                if (videosWithHistory.length < 3) {
                  return (
                    <div className="text-center text-yellow-400 p-4">
                      Need at least 3 videos with view data for peak timing analysis
                    </div>
                  );
                }

                // Analyze publish times and performance patterns
                const publishTimeAnalysis = videosWithHistory.map(video => {
                  const publishDate = new Date(video.publishedAt);
                  const publishHour = publishDate.getHours();
                  const publishDay = publishDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
                  const viewCount = video.statistics?.viewCount ? Number(video.statistics.viewCount) : (video.viewCount || 0);

                  // Calculate hours since publication
                  const hoursElapsed = Math.max(1, (Date.now() - publishDate.getTime()) / (1000 * 60 * 60));
                  const viewsPerHour = viewCount / hoursElapsed;

                  return {
                    title: video.title,
                    publishHour,
                    publishDay,
                    viewCount,
                    viewsPerHour,
                    publishDate,
                    hoursElapsed
                  };
                });

                // Group by hour and calculate average performance
                const hourlyPerformance: Record<number, { totalVph: number; count: number; videos: any[] }> = {};
                publishTimeAnalysis.forEach(video => {
                  if (!hourlyPerformance[video.publishHour]) {
                    hourlyPerformance[video.publishHour] = { totalVph: 0, count: 0, videos: [] };
                  }
                  hourlyPerformance[video.publishHour].totalVph += video.viewsPerHour;
                  hourlyPerformance[video.publishHour].count += 1;
                  hourlyPerformance[video.publishHour].videos.push(video);
                });

                // Calculate average VPH for each hour
                const hourlyAvgVph = Object.entries(hourlyPerformance).map(([hour, data]) => ({
                  hour: parseInt(hour),
                  avgVph: data.totalVph / data.count,
                  videoCount: data.count,
                  videos: data.videos
                })).sort((a, b) => b.avgVph - a.avgVph);

                // Find peak positive and negative times
                const peakPositiveHours = hourlyAvgVph.slice(0, 3); // Top 3 hours
                const peakNegativeHours = hourlyAvgVph.slice(-3).reverse(); // Bottom 3 hours

                const formatHour = (hour: number) => {
                  const period = hour >= 12 ? 'PM' : 'AM';
                  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
                  return `${displayHour}:00 ${period}`;
                };

                const getDayName = (day: number) => {
                  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                  return days[day];
                };

                return (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {/* Peak Positive Times */}
                    <div className="bg-green-500/10 p-3 rounded border border-green-500/20">
                      <h4 className="text-sm font-medium text-green-400 mb-2 flex items-center">
                        📈 Peak Positive Times
                        <span className="ml-2 text-xs text-green-300">(Best Performance)</span>
                      </h4>

                      <div className="space-y-2">
                        {peakPositiveHours.map((timeData, idx) => (
                          <div key={idx} className="bg-green-500/20 p-2 rounded">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm font-medium text-green-300">
                                #{idx + 1} {formatHour(timeData.hour)}
                              </span>
                              <span className="text-xs text-green-400 font-bold">
                                {timeData.avgVph.toFixed(1)} VPH
                              </span>
                            </div>

                            <div className="text-xs text-green-200">
                              {timeData.videoCount} video{timeData.videoCount > 1 ? 's' : ''} analyzed
                            </div>

                            {/* Show top video example */}
                            {timeData.videos.length > 0 && (
                              <div className="mt-1 text-xs text-green-300 truncate">
                                Best: "{timeData.videos.sort((a, b) => b.viewsPerHour - a.viewsPerHour)[0].title.substring(0, 40)}..."
                              </div>
                            )}
                          </div>
                        ))}
                      </div>

                      <div className="mt-3 p-2 bg-green-500/10 rounded border border-green-500/30">
                        <div className="text-xs font-medium text-green-400 mb-1">💡 Recommendation:</div>
                        <div className="text-xs text-green-300">
                          {peakPositiveHours.length >= 3 ? (
                            <>Schedule uploads between {formatHour(peakPositiveHours[0].hour)} - {formatHour(peakPositiveHours[2].hour)} for maximum engagement</>
                          ) : peakPositiveHours.length >= 2 ? (
                            <>Schedule uploads between {formatHour(peakPositiveHours[0].hour)} - {formatHour(peakPositiveHours[1].hour)} for better engagement</>
                          ) : peakPositiveHours.length >= 1 ? (
                            <>Schedule uploads around {formatHour(peakPositiveHours[0].hour)} for better engagement</>
                          ) : (
                            <>Need more data to provide timing recommendations</>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Peak Negative Times */}
                    <div className="bg-red-500/10 p-3 rounded border border-red-500/20">
                      <h4 className="text-sm font-medium text-red-400 mb-2 flex items-center">
                        📉 Peak Negative Times
                        <span className="ml-2 text-xs text-red-300">(Avoid These)</span>
                      </h4>

                      <div className="space-y-2">
                        {peakNegativeHours.map((timeData, idx) => (
                          <div key={idx} className="bg-red-500/20 p-2 rounded">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm font-medium text-red-300">
                                #{idx + 1} {formatHour(timeData.hour)}
                              </span>
                              <span className="text-xs text-red-400 font-bold">
                                {timeData.avgVph.toFixed(1)} VPH
                              </span>
                            </div>

                            <div className="text-xs text-red-200">
                              {timeData.videoCount} video{timeData.videoCount > 1 ? 's' : ''} analyzed
                            </div>

                            {/* Show example */}
                            {timeData.videos.length > 0 && (
                              <div className="mt-1 text-xs text-red-300 truncate">
                                Example: "{timeData.videos[0].title.substring(0, 40)}..."
                              </div>
                            )}
                          </div>
                        ))}
                      </div>

                      <div className="mt-3 p-2 bg-red-500/10 rounded border border-red-500/30">
                        <div className="text-xs font-medium text-red-400 mb-1">⚠️ Warning:</div>
                        <div className="text-xs text-red-300">
                          {peakNegativeHours.length >= 3 ? (
                            <>Avoid uploading during {formatHour(peakNegativeHours[0].hour)} - {formatHour(peakNegativeHours[2].hour)} for better performance</>
                          ) : peakNegativeHours.length >= 2 ? (
                            <>Avoid uploading during {formatHour(peakNegativeHours[0].hour)} - {formatHour(peakNegativeHours[1].hour)} for better performance</>
                          ) : peakNegativeHours.length >= 1 ? (
                            <>Consider avoiding uploads around {formatHour(peakNegativeHours[0].hour)}</>
                          ) : (
                            <>Need more data to identify timing patterns</>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          )}

          {/* Real Performance Heatmap */}
          {videos.length > 0 && (
            <div className="bg-muted/10 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-cyan-400">Performance Heatmap</h3>
                <span className="text-xs text-muted-foreground">
                  Optimal Posting Times
                </span>
              </div>

              <div className="space-y-3">
                {(() => {
                  // Analyze posting times and performance
                  const trackedVideos = videos.filter(v => v.channelTitle === trackedChannelName);

                  if (trackedVideos.length === 0) {
                    return (
                      <div className="text-center text-muted-foreground p-4">
                        No @HowToGuys videos found for analysis
                      </div>
                    );
                  }

                  // Group by hour of day
                  const hourlyPerformance = {};
                  const dayOfWeekPerformance = {};

                  trackedVideos.forEach(video => {
                    const publishDate = new Date(video.publishedAt);
                    const hour = publishDate.getHours();
                    const dayOfWeek = publishDate.getDay(); // 0 = Sunday
                    const views = video.statistics?.viewCount ? Number(video.statistics.viewCount) : (video.viewCount || 0);
                    const vph = video.vph || 0;

                    // Hour analysis
                    if (!hourlyPerformance[hour]) {
                      hourlyPerformance[hour] = { totalViews: 0, totalVph: 0, count: 0 };
                    }
                    hourlyPerformance[hour].totalViews += views;
                    hourlyPerformance[hour].totalVph += vph;
                    hourlyPerformance[hour].count += 1;

                    // Day of week analysis
                    if (!dayOfWeekPerformance[dayOfWeek]) {
                      dayOfWeekPerformance[dayOfWeek] = { totalViews: 0, totalVph: 0, count: 0 };
                    }
                    dayOfWeekPerformance[dayOfWeek].totalViews += views;
                    dayOfWeekPerformance[dayOfWeek].totalVph += vph;
                    dayOfWeekPerformance[dayOfWeek].count += 1;
                  });

                  // Calculate averages and find best times
                  const hourlyAvgs = Object.entries(hourlyPerformance).map(([hour, data]) => ({
                    hour: parseInt(hour),
                    avgViews: data.totalViews / data.count,
                    avgVph: data.totalVph / data.count,
                    count: data.count
                  })).sort((a, b) => b.avgVph - a.avgVph);

                  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                  const dayAvgs = Object.entries(dayOfWeekPerformance).map(([day, data]) => ({
                    day: parseInt(day),
                    dayName: dayNames[parseInt(day)],
                    avgViews: data.totalViews / data.count,
                    avgVph: data.totalVph / data.count,
                    count: data.count
                  })).sort((a, b) => b.avgVph - a.avgVph);

                  const maxVph = Math.max(...hourlyAvgs.map(h => h.avgVph));
                  const maxDayVph = Math.max(...dayAvgs.map(d => d.avgVph));

                  return (
                    <>
                      {/* Best Hours */}
                      <div>
                        <h4 className="text-sm font-medium text-cyan-300 mb-2">Best Hours to Post</h4>
                        <div className="grid grid-cols-6 gap-1">
                          {hourlyAvgs.slice(0, 12).map(({ hour, avgVph, count }) => {
                            const intensity = maxVph > 0 ? (avgVph / maxVph) * 100 : 0;
                            const timeStr = hour === 0 ? '12 AM' :
                                          hour < 12 ? `${hour} AM` :
                                          hour === 12 ? '12 PM' : `${hour - 12} PM`;

                            return (
                              <div
                                key={hour}
                                className="p-2 rounded text-center border"
                                style={{
                                  backgroundColor: `rgba(34, 197, 94, ${intensity / 100 * 0.3})`,
                                  borderColor: `rgba(34, 197, 94, ${intensity / 100 * 0.5})`
                                }}
                              >
                                <div className="text-xs font-medium">{timeStr}</div>
                                <div className="text-xs">{avgVph.toFixed(1)} VPH</div>
                                <div className="text-xs text-muted-foreground">{count}v</div>
                              </div>
                            );
                          })}
                        </div>
                      </div>

                      {/* Best Days */}
                      <div>
                        <h4 className="text-sm font-medium text-cyan-300 mb-2">Best Days to Post</h4>
                        <div className="grid grid-cols-7 gap-1">
                          {dayAvgs.map(({ day, dayName, avgVph, count }) => {
                            const intensity = maxDayVph > 0 ? (avgVph / maxDayVph) * 100 : 0;

                            return (
                              <div
                                key={day}
                                className="p-2 rounded text-center border"
                                style={{
                                  backgroundColor: `rgba(59, 130, 246, ${intensity / 100 * 0.3})`,
                                  borderColor: `rgba(59, 130, 246, ${intensity / 100 * 0.5})`
                                }}
                              >
                                <div className="text-xs font-medium">{dayName.slice(0, 3)}</div>
                                <div className="text-xs">{avgVph.toFixed(1)} VPH</div>
                                <div className="text-xs text-muted-foreground">{count}v</div>
                              </div>
                            );
                          })}
                        </div>
                      </div>

                      {/* Top Recommendations */}
                      <div className="bg-cyan-500/10 rounded p-3 border border-cyan-500/20">
                        <h4 className="text-sm font-medium text-cyan-300 mb-2">📈 Recommendations</h4>
                        <div className="space-y-1 text-sm">
                          <div>
                            🕐 <strong>Best Hour:</strong> {hourlyAvgs[0]?.hour === 0 ? '12 AM' :
                              hourlyAvgs[0]?.hour < 12 ? `${hourlyAvgs[0]?.hour} AM` :
                              hourlyAvgs[0]?.hour === 12 ? '12 PM' : `${hourlyAvgs[0]?.hour - 12} PM`}
                            ({hourlyAvgs[0]?.avgVph.toFixed(1)} VPH avg)
                          </div>
                          <div>
                            📅 <strong>Best Day:</strong> {dayAvgs[0]?.dayName} ({dayAvgs[0]?.avgVph.toFixed(1)} VPH avg)
                          </div>
                          <div>
                            📊 <strong>Sample Size:</strong> {trackedVideos.length} videos analyzed
                          </div>
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Real Competitor Intelligence */}
          {videos.length > 0 && (
            <div className="bg-muted/10 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-pink-400">Competitor Intelligence</h3>
                <span className="text-xs text-muted-foreground">
                  Strategic Analysis
                </span>
              </div>

              <div className="space-y-3">
                {(() => {
                  // Analyze top competitors
                  const competitorAnalysis = {};

                  videos.forEach(video => {
                    const channel = video.channelTitle;
                    if (channel === trackedChannelName) return; // Skip tracked channel

                    if (!competitorAnalysis[channel]) {
                      competitorAnalysis[channel] = {
                        name: channel,
                        videoCount: 0,
                        totalViews: 0,
                        totalVph: 0,
                        publishTimes: [],
                        topics: new Set(),
                        avgVideoAge: 0,
                        recentVideos: 0
                      };
                    }

                    const views = video.statistics?.viewCount ? Number(video.statistics.viewCount) : (video.viewCount || 0);
                    const vph = video.vph || 0;
                    const publishDate = new Date(video.publishedAt);
                    const now = new Date();
                    const ageInDays = (now.getTime() - publishDate.getTime()) / (1000 * 60 * 60 * 24);

                    competitorAnalysis[channel].videoCount += 1;
                    competitorAnalysis[channel].totalViews += views;
                    competitorAnalysis[channel].totalVph += vph;
                    competitorAnalysis[channel].publishTimes.push(publishDate.getHours());
                    competitorAnalysis[channel].avgVideoAge += ageInDays;

                    // Count recent videos (last 7 days)
                    if (ageInDays <= 7) {
                      competitorAnalysis[channel].recentVideos += 1;
                    }

                    // Extract topics
                    const title = video.title.toLowerCase();
                    if (title.includes('social security')) competitorAnalysis[channel].topics.add('Social Security');
                    if (title.includes('stimulus')) competitorAnalysis[channel].topics.add('Stimulus');
                    if (title.includes('medicare')) competitorAnalysis[channel].topics.add('Medicare');
                    if (title.includes('disability')) competitorAnalysis[channel].topics.add('Disability');
                    if (title.includes('tax')) competitorAnalysis[channel].topics.add('Tax');
                  });

                  // Calculate final metrics and sort by performance
                  const competitors = Object.values(competitorAnalysis)
                    .filter(comp => comp.videoCount >= 2) // Only channels with 2+ videos
                    .map(comp => ({
                      ...comp,
                      avgViews: comp.totalViews / comp.videoCount,
                      avgVph: comp.totalVph / comp.videoCount,
                      avgVideoAge: comp.avgVideoAge / comp.videoCount,
                      contentFrequency: comp.recentVideos, // Videos per week approximation
                      topics: Array.from(comp.topics),
                      preferredHour: comp.publishTimes.length > 0 ?
                        comp.publishTimes.reduce((a, b, i, arr) => a + b / arr.length, 0) : 0
                    }))
                    .sort((a, b) => b.avgVph - a.avgVph) // Sort by VPH performance
                    .slice(0, 5); // Top 5 competitors

                  if (competitors.length === 0) {
                    return (
                      <div className="text-center text-muted-foreground p-4">
                        No competitor data available for analysis
                      </div>
                    );
                  }

                  return (
                    <>
                      {/* Top Competitors */}
                      <div>
                        <h4 className="text-sm font-medium text-pink-300 mb-2">Top Performing Competitors</h4>
                        <div className="space-y-2">
                          {competitors.map((competitor, index) => {
                            const threatLevel = competitor.avgVph > (enhancedMetrics.avgVph || 0) ? 'High' :
                                              competitor.avgVph > (enhancedMetrics.avgVph || 0) * 0.7 ? 'Medium' : 'Low';
                            const threatColor = threatLevel === 'High' ? 'text-red-400' :
                                              threatLevel === 'Medium' ? 'text-yellow-400' : 'text-green-400';

                            return (
                              <div
                                key={competitor.name}
                                className="p-3 bg-pink-500/10 rounded border border-pink-500/20"
                              >
                                <div className="flex justify-between items-start mb-2">
                                  <div className="font-medium text-pink-300 truncate max-w-[200px]" title={competitor.name}>
                                    #{index + 1} {competitor.name}
                                  </div>
                                  <div className={`text-xs px-2 py-1 rounded ${threatColor} bg-current/20`}>
                                    {threatLevel} Threat
                                  </div>
                                </div>

                                <div className="grid grid-cols-3 gap-2 text-sm mb-2">
                                  <div>
                                    <div className="text-xs text-muted-foreground">Avg VPH</div>
                                    <div className="font-medium">{competitor.avgVph.toFixed(1)}</div>
                                  </div>
                                  <div>
                                    <div className="text-xs text-muted-foreground">Videos</div>
                                    <div className="font-medium">{competitor.videoCount}</div>
                                  </div>
                                  <div>
                                    <div className="text-xs text-muted-foreground">Recent</div>
                                    <div className="font-medium">{competitor.recentVideos}/week</div>
                                  </div>
                                </div>

                                <div className="space-y-1 text-xs">
                                  <div>
                                    <span className="text-muted-foreground">Avg Views:</span> {competitor.avgViews.toLocaleString()}
                                  </div>
                                  <div>
                                    <span className="text-muted-foreground">Posting Time:</span> {
                                      Math.round(competitor.preferredHour) === 0 ? '12 AM' :
                                      Math.round(competitor.preferredHour) < 12 ? `${Math.round(competitor.preferredHour)} AM` :
                                      Math.round(competitor.preferredHour) === 12 ? '12 PM' :
                                      `${Math.round(competitor.preferredHour) - 12} PM`
                                    }
                                  </div>
                                  <div>
                                    <span className="text-muted-foreground">Topics:</span> {competitor.topics.slice(0, 3).join(', ')}
                                    {competitor.topics.length > 3 && ` +${competitor.topics.length - 3} more`}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>

                      {/* Strategic Insights */}
                      <div className="bg-pink-500/10 rounded p-3 border border-pink-500/20">
                        <h4 className="text-sm font-medium text-pink-300 mb-2">🎯 Strategic Insights</h4>
                        <div className="space-y-1 text-sm">
                          {(() => {
                            const topCompetitor = competitors[0];
                            const yourAvgVph = enhancedMetrics.avgVph || 0;
                            const competitorAdvantage = topCompetitor ?
                              ((topCompetitor.avgVph - yourAvgVph) / yourAvgVph * 100) : 0;

                            const insights = [];

                            if (competitorAdvantage > 20) {
                              insights.push(`⚠️ Top competitor has ${competitorAdvantage.toFixed(1)}% higher VPH`);
                            } else if (competitorAdvantage < -10) {
                              insights.push(`✅ You're outperforming top competitor by ${Math.abs(competitorAdvantage).toFixed(1)}%`);
                            }

                            // Analyze posting frequency
                            const avgCompetitorFreq = competitors.reduce((sum, c) => sum + c.contentFrequency, 0) / competitors.length;
                            const yourRecentVideos = videos.filter(v =>
                              v.channelTitle === trackedChannelName &&
                              (new Date().getTime() - new Date(v.publishedAt).getTime()) / (1000 * 60 * 60 * 24) <= 7
                            ).length;

                            if (yourRecentVideos < avgCompetitorFreq) {
                              insights.push(`📈 Consider increasing posting frequency (competitors avg: ${avgCompetitorFreq.toFixed(1)}/week)`);
                            }

                            // Topic analysis
                            const competitorTopics = new Set();
                            competitors.forEach(c => c.topics.forEach(topic => competitorTopics.add(topic)));
                            insights.push(`🔍 Competitors focus on: ${Array.from(competitorTopics).slice(0, 3).join(', ')}`);

                            return insights.map((insight, i) => (
                              <div key={i}>{insight}</div>
                            ));
                          })()}
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Viral Prediction Engine */}
          {videos.length > 0 && (
            <div className="bg-muted/10 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-emerald-400">Viral Prediction Engine</h3>
                <span className="text-xs text-muted-foreground">
                  Success Indicators
                </span>
              </div>

              <div className="space-y-3">
                {(() => {
                  // Analyze recent videos for viral potential
                  const recentVideos = videos
                    .filter(v => {
                      const ageInHours = (new Date().getTime() - new Date(v.publishedAt).getTime()) / (1000 * 60 * 60);
                      return ageInHours <= 24; // Last 24 hours
                    })
                    .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
                    .slice(0, 10);

                  if (recentVideos.length === 0) {
                    return (
                      <div className="text-center text-muted-foreground p-4">
                        No recent videos (last 24h) found for viral analysis
                      </div>
                    );
                  }

                  // Calculate viral potential for each video
                  const videosWithPotential = recentVideos.map(video => {
                    const views = video.statistics?.viewCount ? Number(video.statistics.viewCount) : (video.viewCount || 0);
                    const vph = video.vph || 0;
                    const ageInHours = (new Date().getTime() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60);

                    // Viral indicators
                    let viralScore = 0;
                    const indicators = [];

                    // High VPH (above average)
                    const avgVph = videos.reduce((sum, v) => sum + (v.vph || 0), 0) / videos.length;
                    if (vph > avgVph * 1.5) {
                      viralScore += 25;
                      indicators.push('🚀 High VPH');
                    }

                    // Trending keywords in title
                    const title = video.title.toLowerCase();
                    const trendingKeywords = ['breaking', 'urgent', 'new', 'update', 'confirmed', 'announced', 'alert'];
                    const hastrending = trendingKeywords.some(keyword => title.includes(keyword));
                    if (hastrending) {
                      viralScore += 20;
                      indicators.push('📈 Trending Keywords');
                    }

                    // Emotional triggers
                    const emotionalWords = ['shocking', 'amazing', 'incredible', 'unbelievable', 'must', 'secret'];
                    const hasEmotional = emotionalWords.some(word => title.includes(word));
                    if (hasEmotional) {
                      viralScore += 15;
                      indicators.push('💥 Emotional Trigger');
                    }

                    // Numbers in title (specific amounts, dates)
                    const hasNumbers = /\$[\d,]+|\d{4}|\d+%/.test(video.title);
                    if (hasNumbers) {
                      viralScore += 15;
                      indicators.push('🔢 Specific Numbers');
                    }

                    // Recent upload (recency boost)
                    if (ageInHours <= 6) {
                      viralScore += 10;
                      indicators.push('⏰ Fresh Content');
                    }

                    // High early engagement (views per hour vs age)
                    const expectedViews = avgVph * ageInHours;
                    if (views > expectedViews * 1.3) {
                      viralScore += 15;
                      indicators.push('⚡ High Early Engagement');
                    }

                    return {
                      ...video,
                      viralScore: Math.min(100, viralScore),
                      indicators,
                      ageInHours,
                      views
                    };
                  }).sort((a, b) => b.viralScore - a.viralScore);

                  const topPotential = videosWithPotential.slice(0, 5);

                  return (
                    <>
                      {/* Top Viral Potential Videos */}
                      <div>
                        <h4 className="text-sm font-medium text-emerald-300 mb-2">Videos with Viral Potential</h4>
                        <div className="space-y-2">
                          {topPotential.map((video, index) => {
                            const potentialLevel = video.viralScore >= 70 ? 'High' :
                                                 video.viralScore >= 40 ? 'Medium' : 'Low';
                            const potentialColor = potentialLevel === 'High' ? 'text-emerald-400' :
                                                  potentialLevel === 'Medium' ? 'text-yellow-400' : 'text-gray-400';

                            return (
                              <div
                                key={video.id}
                                className="p-3 bg-emerald-500/10 rounded border border-emerald-500/20"
                              >
                                <div className="flex justify-between items-start mb-2">
                                  <div className="font-medium text-emerald-300 text-sm truncate max-w-[250px]" title={video.title}>
                                    {video.title}
                                  </div>
                                  <div className={`text-xs px-2 py-1 rounded ${potentialColor} bg-current/20`}>
                                    {video.viralScore}/100
                                  </div>
                                </div>

                                <div className="grid grid-cols-3 gap-2 text-sm mb-2">
                                  <div>
                                    <div className="text-xs text-muted-foreground">Views</div>
                                    <div className="font-medium">{video.views.toLocaleString()}</div>
                                  </div>
                                  <div>
                                    <div className="text-xs text-muted-foreground">VPH</div>
                                    <div className="font-medium">{(video.vph || 0).toFixed(1)}</div>
                                  </div>
                                  <div>
                                    <div className="text-xs text-muted-foreground">Age</div>
                                    <div className="font-medium">{video.ageInHours.toFixed(1)}h</div>
                                  </div>
                                </div>

                                {/* Viral Indicators */}
                                <div className="flex flex-wrap gap-1">
                                  {video.indicators.map((indicator, i) => (
                                    <span
                                      key={i}
                                      className="text-xs bg-emerald-500/20 text-emerald-300 px-2 py-1 rounded"
                                    >
                                      {indicator}
                                    </span>
                                  ))}
                                </div>

                                {/* Progress bar */}
                                <div className="mt-2">
                                  <div className="flex items-center gap-2">
                                    <div className="flex-1 bg-muted/30 rounded-full h-2">
                                      <div
                                        className="bg-emerald-400 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${video.viralScore}%` }}
                                      />
                                    </div>
                                    <div className="text-xs font-medium">{potentialLevel}</div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>

                      {/* Viral Success Factors */}
                      <div className="bg-emerald-500/10 rounded p-3 border border-emerald-500/20">
                        <h4 className="text-sm font-medium text-emerald-300 mb-2">🎯 Viral Success Factors</h4>
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div>
                            <div className="font-medium text-emerald-300 mb-1">Title Optimization</div>
                            <div className="space-y-1 text-xs">
                              <div>• Use trending keywords</div>
                              <div>• Include specific numbers</div>
                              <div>• Add emotional triggers</div>
                            </div>
                          </div>
                          <div>
                            <div className="font-medium text-emerald-300 mb-1">Timing Strategy</div>
                            <div className="space-y-1 text-xs">
                              <div>• Post during peak hours</div>
                              <div>• Monitor early engagement</div>
                              <div>• React to trending topics</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Advanced Engagement Patterns & Heatmaps */}
          {videos.length > 0 && (
            <div className="bg-muted/10 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-indigo-400">Advanced Engagement Patterns</h3>
                <span className="text-xs text-muted-foreground">
                  Engagement Heatmaps & Ratios
                </span>
              </div>

              <div className="space-y-4">
                {(() => {
                  // Filter videos for analysis
                  const trackedVideos = videos.filter(v => v.channelTitle === trackedChannelName);
                  const allVideos = videos;

                  if (trackedVideos.length === 0) {
                    return (
                      <div className="text-center text-muted-foreground p-4">
                        No @HowToGuys videos found for engagement analysis
                      </div>
                    );
                  }

                  // 1. Engagement Heatmap by Time of Day
                  const hourlyEngagement = {};
                  trackedVideos.forEach(video => {
                    const publishDate = new Date(video.publishedAt);
                    const hour = publishDate.getHours();
                    const views = video.statistics?.viewCount ? Number(video.statistics.viewCount) : (video.viewCount || 0);
                    const likes = video.statistics?.likeCount ? Number(video.statistics.likeCount) : 0;
                    const comments = video.statistics?.commentCount ? Number(video.statistics.commentCount) : 0;

                    if (!hourlyEngagement[hour]) {
                      hourlyEngagement[hour] = {
                        totalViews: 0,
                        totalLikes: 0,
                        totalComments: 0,
                        videoCount: 0,
                        engagementScore: 0
                      };
                    }

                    hourlyEngagement[hour].totalViews += views;
                    hourlyEngagement[hour].totalLikes += likes;
                    hourlyEngagement[hour].totalComments += comments;
                    hourlyEngagement[hour].videoCount += 1;

                    // Calculate engagement score (likes + comments) / views * 100
                    const engagementRate = views > 0 ? ((likes + comments) / views) * 100 : 0;
                    hourlyEngagement[hour].engagementScore += engagementRate;
                  });

                  // Calculate averages for each hour
                  const hourlyEngagementData = Object.entries(hourlyEngagement).map(([hour, data]) => ({
                    hour: parseInt(hour),
                    avgEngagementRate: data.engagementScore / data.videoCount,
                    avgLikeToViewRatio: data.totalViews > 0 ? (data.totalLikes / data.totalViews) * 100 : 0,
                    avgCommentToViewRatio: data.totalViews > 0 ? (data.totalComments / data.totalViews) * 100 : 0,
                    videoCount: data.videoCount
                  })).sort((a, b) => a.hour - b.hour);

                  const maxEngagement = Math.max(...hourlyEngagementData.map(h => h.avgEngagementRate));

                  // 2. Overall Engagement Ratios Analysis
                  const overallStats = trackedVideos.reduce((acc, video) => {
                    const views = video.statistics?.viewCount ? Number(video.statistics.viewCount) : (video.viewCount || 0);
                    const likes = video.statistics?.likeCount ? Number(video.statistics.likeCount) : 0;
                    const comments = video.statistics?.commentCount ? Number(video.statistics.commentCount) : 0;

                    acc.totalViews += views;
                    acc.totalLikes += likes;
                    acc.totalComments += comments;
                    acc.videoCount += 1;

                    return acc;
                  }, { totalViews: 0, totalLikes: 0, totalComments: 0, videoCount: 0 });

                  const avgLikeToViewRatio = overallStats.totalViews > 0 ? (overallStats.totalLikes / overallStats.totalViews) * 100 : 0;
                  const avgCommentToViewRatio = overallStats.totalViews > 0 ? (overallStats.totalComments / overallStats.totalViews) * 100 : 0;
                  const avgEngagementRate = overallStats.totalViews > 0 ? ((overallStats.totalLikes + overallStats.totalComments) / overallStats.totalViews) * 100 : 0;

                  // 3. Top Performing Videos by Engagement
                  const topEngagementVideos = trackedVideos
                    .map(video => {
                      const views = video.statistics?.viewCount ? Number(video.statistics.viewCount) : (video.viewCount || 0);
                      const likes = video.statistics?.likeCount ? Number(video.statistics.likeCount) : 0;
                      const comments = video.statistics?.commentCount ? Number(video.statistics.commentCount) : 0;
                      const engagementRate = views > 0 ? ((likes + comments) / views) * 100 : 0;

                      return {
                        ...video,
                        engagementRate,
                        likeToViewRatio: views > 0 ? (likes / views) * 100 : 0,
                        commentToViewRatio: views > 0 ? (comments / views) * 100 : 0,
                        views,
                        likes,
                        comments
                      };
                    })
                    .sort((a, b) => b.engagementRate - a.engagementRate)
                    .slice(0, 5);

                  return (
                    <>
                      {/* Engagement Heatmap by Hour */}
                      <div>
                        <h4 className="text-sm font-medium text-indigo-300 mb-3">🕐 Engagement Heatmap by Hour</h4>
                        <div className="grid grid-cols-12 gap-1 mb-3">
                          {Array.from({ length: 24 }, (_, i) => {
                            const hourData = hourlyEngagementData.find(h => h.hour === i);
                            const intensity = hourData && maxEngagement > 0 ? (hourData.avgEngagementRate / maxEngagement) * 100 : 0;
                            const timeStr = i === 0 ? '12 AM' :
                                          i < 12 ? `${i} AM` :
                                          i === 12 ? '12 PM' : `${i - 12} PM`;

                            return (
                              <div
                                key={i}
                                className="p-2 rounded text-center border text-xs"
                                style={{
                                  backgroundColor: `rgba(99, 102, 241, ${intensity / 100 * 0.4})`,
                                  borderColor: `rgba(99, 102, 241, ${intensity / 100 * 0.6})`
                                }}
                                title={`${timeStr}: ${hourData ? hourData.avgEngagementRate.toFixed(2) : 0}% engagement rate`}
                              >
                                <div className="font-medium">{i}</div>
                                <div className="text-xs">{hourData ? hourData.avgEngagementRate.toFixed(1) : '0'}%</div>
                                <div className="text-xs text-muted-foreground">{hourData ? hourData.videoCount : 0}v</div>
                              </div>
                            );
                          })}
                        </div>
                        <div className="text-xs text-muted-foreground text-center">
                          Hover over hours to see detailed engagement rates
                        </div>
                      </div>

                      {/* Overall Engagement Metrics */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div className="bg-indigo-500/20 rounded p-3 text-center border border-indigo-500/30">
                          <div className="text-xs text-indigo-300">Overall Engagement</div>
                          <div className="text-lg font-bold text-white">{avgEngagementRate.toFixed(2)}%</div>
                          <div className="text-xs text-muted-foreground">Likes + Comments / Views</div>
                        </div>
                        <div className="bg-green-500/20 rounded p-3 text-center border border-green-500/30">
                          <div className="text-xs text-green-300">Like-to-View Ratio</div>
                          <div className="text-lg font-bold text-white">{avgLikeToViewRatio.toFixed(2)}%</div>
                          <div className="text-xs text-muted-foreground">{overallStats.totalLikes.toLocaleString()} total likes</div>
                        </div>
                        <div className="bg-blue-500/20 rounded p-3 text-center border border-blue-500/30">
                          <div className="text-xs text-blue-300">Comment-to-View Ratio</div>
                          <div className="text-lg font-bold text-white">{avgCommentToViewRatio.toFixed(2)}%</div>
                          <div className="text-xs text-muted-foreground">{overallStats.totalComments.toLocaleString()} total comments</div>
                        </div>
                        <div className="bg-purple-500/20 rounded p-3 text-center border border-purple-500/30">
                          <div className="text-xs text-purple-300">Videos Analyzed</div>
                          <div className="text-lg font-bold text-white">{overallStats.videoCount}</div>
                          <div className="text-xs text-muted-foreground">{overallStats.totalViews.toLocaleString()} total views</div>
                        </div>
                      </div>

                      {/* Top Performing Videos by Engagement */}
                      <div>
                        <h4 className="text-sm font-medium text-indigo-300 mb-2">🏆 Top Engagement Videos</h4>
                        <div className="space-y-2">
                          {topEngagementVideos.map((video, idx) => (
                            <div
                              key={video.id}
                              className="p-3 bg-indigo-500/10 rounded border border-indigo-500/20"
                            >
                              <div className="flex justify-between items-start mb-2">
                                <div className="font-medium text-indigo-300 text-sm truncate max-w-[300px]" title={video.title}>
                                  #{idx + 1}. {video.title}
                                </div>
                                <div className="text-xs bg-indigo-500/20 px-2 py-1 rounded text-indigo-300">
                                  {video.engagementRate.toFixed(2)}% engagement
                                </div>
                              </div>

                              <div className="grid grid-cols-5 gap-2 text-sm">
                                <div>
                                  <div className="text-xs text-muted-foreground">Views</div>
                                  <div className="font-medium">{video.views.toLocaleString()}</div>
                                </div>
                                <div>
                                  <div className="text-xs text-muted-foreground">Likes</div>
                                  <div className="font-medium">{video.likes.toLocaleString()}</div>
                                </div>
                                <div>
                                  <div className="text-xs text-muted-foreground">Comments</div>
                                  <div className="font-medium">{video.comments.toLocaleString()}</div>
                                </div>
                                <div>
                                  <div className="text-xs text-muted-foreground">Like Ratio</div>
                                  <div className="font-medium">{video.likeToViewRatio.toFixed(2)}%</div>
                                </div>
                                <div>
                                  <div className="text-xs text-muted-foreground">Comment Ratio</div>
                                  <div className="font-medium">{video.commentToViewRatio.toFixed(2)}%</div>
                                </div>
                              </div>

                              {/* Engagement bar */}
                              <div className="mt-2">
                                <div className="flex items-center gap-2">
                                  <div className="flex-1 bg-muted/30 rounded-full h-2">
                                    <div
                                      className="bg-indigo-400 h-2 rounded-full transition-all duration-300"
                                      style={{ width: `${Math.min(100, video.engagementRate * 10)}%` }}
                                    />
                                  </div>
                                  <div className="text-xs font-medium">
                                    {video.engagementRate > 5 ? '🔥 High' :
                                     video.engagementRate > 2 ? '📈 Good' : '📊 Average'}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Real Published Dates Verification */}
                      <div className="bg-blue-500/10 rounded p-3 border border-blue-500/20">
                        <h4 className="text-sm font-medium text-blue-300 mb-2">📅 Real Published Dates (Last 5 Videos)</h4>
                        <div className="space-y-1 text-xs">
                          {trackedVideos
                            .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
                            .slice(0, 5)
                            .map((video, idx) => {
                              const publishDate = new Date(video.publishedAt);
                              const now = new Date();
                              const diffMs = now.getTime() - publishDate.getTime();
                              const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                              const diffDays = Math.floor(diffHours / 24);

                              const relativeTime = diffDays > 0 ? `${diffDays} days ago` :
                                                 diffHours > 0 ? `${diffHours} hours ago` :
                                                 'Less than 1 hour ago';

                              return (
                                <div key={video.id} className="flex justify-between items-center">
                                  <div className="truncate max-w-[200px]" title={video.title}>
                                    {video.title}
                                  </div>
                                  <div className="text-blue-300 font-medium">
                                    {publishDate.toLocaleDateString()} {publishDate.toLocaleTimeString()} ({relativeTime})
                                  </div>
                                </div>
                              );
                            })}
                        </div>
                        <div className="mt-2 text-xs text-muted-foreground">
                          ✅ These are REAL published dates calculated from YouTube's relative time data
                        </div>
                      </div>

                      {/* Engagement Insights & Recommendations */}
                      <div className="bg-indigo-500/10 rounded p-3 border border-indigo-500/20">
                        <h4 className="text-sm font-medium text-indigo-300 mb-2">💡 Engagement Insights</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                          <div>
                            <div className="font-medium text-indigo-300 mb-1">Best Engagement Hours</div>
                            <div className="space-y-1 text-xs">
                              {hourlyEngagementData
                                .sort((a, b) => b.avgEngagementRate - a.avgEngagementRate)
                                .slice(0, 3)
                                .map(hour => {
                                  const timeStr = hour.hour === 0 ? '12 AM' :
                                                hour.hour < 12 ? `${hour.hour} AM` :
                                                hour.hour === 12 ? '12 PM' : `${hour.hour - 12} PM`;
                                  return (
                                    <div key={hour.hour}>
                                      • {timeStr}: {hour.avgEngagementRate.toFixed(1)}% engagement
                                    </div>
                                  );
                                })}
                            </div>
                          </div>
                          <div>
                            <div className="font-medium text-indigo-300 mb-1">Optimization Tips</div>
                            <div className="space-y-1 text-xs">
                              <div>• {avgLikeToViewRatio > 3 ? 'Great like ratio! Keep it up' : 'Focus on more engaging content'}</div>
                              <div>• {avgCommentToViewRatio > 1 ? 'Good comment engagement' : 'Ask more questions to boost comments'}</div>
                              <div>• {avgEngagementRate > 4 ? 'Excellent overall engagement!' : 'Try posting during peak hours'}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Channel Ranking Graphs */}
          {rankingHistory.length > 0 && (
            <div className="bg-muted/10 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-purple-400">Channel Ranking Trends (Last 12 Hours)</h3>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">
                    {new Set(rankingHistory.map(r => r.channelName)).size} channels tracked
                  </span>

                  {/* Test buttons for debugging ranking data */}
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-6 px-2 py-0 text-xs bg-purple-500/20"
                    onClick={() => {
                      // Force create mock ranking data for testing
                      const now = Date.now();
                      const mockData = [];

                      // Create mock data for the last 12 hours with multiple URLs
                      const urls = ['Test URL 1', 'Test URL 2', 'Test URL 3'];

                      for (let i = 0; i < 12; i++) {
                        const timestamp = now - (i * 60 * 60 * 1000); // Each hour back

                        urls.forEach(urlName => {
                          // Mock some channels with varying rankings
                          const channels = [
                            { name: '@HowToGuys', ranking: Math.floor(Math.random() * 5) + 1 },
                            { name: '@benefitsreport', ranking: Math.floor(Math.random() * 3) + 1 },
                            { name: '@ssswealth', ranking: Math.floor(Math.random() * 4) + 2 },
                            { name: '@BlindtoBillionaire', ranking: Math.floor(Math.random() * 6) + 1 },
                            { name: '@ErinTalksMoney', ranking: Math.floor(Math.random() * 8) + 3 }
                          ];

                          channels.forEach(channel => {
                            mockData.push({
                              channelName: channel.name,
                              ranking: channel.ranking,
                              timestamp: timestamp,
                              urlName: urlName
                            });
                          });
                        });
                      }

                      try {
                        localStorage.setItem('ytr_ranking_history', JSON.stringify(mockData));
                        console.log(`Added ${mockData.length} mock ranking entries for testing`);
                        // Force page refresh to see the data
                        window.location.reload();
                      } catch (error) {
                        console.error('Failed to save mock ranking data:', error);
                      }
                    }}
                  >
                    Add Test Data
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    className="h-6 px-2 py-0 text-xs bg-yellow-500/20"
                    onClick={() => {
                      console.log('Current ranking history:', rankingHistory);
                      console.log('LocalStorage ranking data:', JSON.parse(localStorage.getItem('ytr_ranking_history') || '[]'));
                    }}
                  >
                    Debug ({rankingHistory.length})
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {/* Get unique combinations of channel and URL, prioritize @HowToGuys and sort by channel importance */}
                {Array.from(new Set(rankingHistory.map(r => `${r.channelName}|${r.urlName}`)))
                  .sort((a, b) => {
                    // Prioritize @HowToGuys first
                    const aChannel = a.split('|')[0];
                    const bChannel = b.split('|')[0];
                    if (aChannel === '@HowToGuys' && bChannel !== '@HowToGuys') return -1;
                    if (bChannel === '@HowToGuys' && aChannel !== '@HowToGuys') return 1;

                    // Then sort by channel name and URL name
                    return a.localeCompare(b);
                  })
                  .map(combination => {
                    const [channelName, urlName] = combination.split('|');
                    const channelRankingData = rankingHistory.filter(r =>
                      r.channelName === channelName && r.urlName === urlName
                    );

                    // Highlight @HowToGuys with special styling
                    const isTrackedChannel = channelName === '@HowToGuys';

                    return (
                      <div
                        key={combination}
                        className={`rounded border p-2 ${
                          isTrackedChannel
                            ? 'bg-green-500/10 border-green-500/30'
                            : 'bg-purple-500/10 border-purple-500/20'
                        }`}
                      >
                        <ChannelRankingGraph
                          rankingHistory={channelRankingData}
                          channelName={channelName}
                          urlName={urlName}
                          height={80}
                          width={220}
                        />
                      </div>
                    );
                  })}
              </div>

              {rankingHistory.length === 0 && (
                <div className="text-sm text-yellow-400 p-4 text-center">
                  <div className="mb-3">No ranking history data available</div>
                  <div className="flex justify-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-7 px-3 py-0 text-xs bg-purple-500/20"
                      onClick={() => {
                        // Force create mock ranking data for testing
                        const now = Date.now();
                        const mockData = [];

                        // Create mock data for the last 12 hours with multiple URLs
                        const urls = ['Test URL 1', 'Test URL 2', 'Test URL 3'];

                        for (let i = 0; i < 12; i++) {
                          const timestamp = now - (i * 60 * 60 * 1000); // Each hour back

                          urls.forEach(urlName => {
                            // Mock some channels with varying rankings
                            const channels = [
                              { name: '@HowToGuys', ranking: Math.floor(Math.random() * 5) + 1 },
                              { name: '@benefitsreport', ranking: Math.floor(Math.random() * 3) + 1 },
                              { name: '@ssswealth', ranking: Math.floor(Math.random() * 4) + 2 },
                              { name: '@BlindtoBillionaire', ranking: Math.floor(Math.random() * 6) + 1 },
                              { name: '@ErinTalksMoney', ranking: Math.floor(Math.random() * 8) + 3 }
                            ];

                            channels.forEach(channel => {
                              mockData.push({
                                channelName: channel.name,
                                ranking: channel.ranking,
                                timestamp: timestamp,
                                urlName: urlName
                              });
                            });
                          });
                        }

                        try {
                          localStorage.setItem('ytr_ranking_history', JSON.stringify(mockData));
                          console.log(`Added ${mockData.length} mock ranking entries for testing`);
                          // Force page refresh to see the data
                          window.location.reload();
                        } catch (error) {
                          console.error('Failed to save mock ranking data:', error);
                        }
                      }}
                    >
                      Add Test Data
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      className="h-7 px-3 py-0 text-xs bg-yellow-500/20"
                      onClick={() => {
                        console.log('Current ranking history:', rankingHistory);
                        console.log('LocalStorage ranking data:', JSON.parse(localStorage.getItem('ytr_ranking_history') || '[]'));
                      }}
                    >
                      Debug Data
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="pt-2 flex-shrink-0">
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
