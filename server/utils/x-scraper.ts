/**
 * X.com (Twitter) Scraper Utility
 *
 * This utility provides functions to scrape content from X.com (formerly Twitter)
 * using the twscrape library, which implements Twitter's GraphQL API.
 */

import axios from 'axios';
import * as cheerio from 'cheerio';
import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import * as path from 'path';
import * as os from 'os';

// Interface for tweet data
interface TweetData {
  id: string;
  text: string;
  authorName: string;
  authorUsername: string;
  createdAt: string;
  images?: string[];
  videos?: string[];
  quotedTweet?: TweetData;
  replyToTweet?: string;
  replyToUser?: string;
}

/**
 * Scrape a tweet using the twscrape Python library
 *
 * @param tweetId The ID of the tweet to scrape
 * @returns Promise with the tweet data or null if not found
 */
export async function scrapeTweet(tweetId: string): Promise<TweetData | null> {
  try {
    console.log(`Scraping tweet with ID: ${tweetId} using twscrape`);

    // Check if Python 3.10+ is available
    try {
      const pythonVersionCheck = await new Promise<boolean>((resolve) => {
        const pythonProcess = spawn('python3', ['--version']);
        let versionOutput = '';

        pythonProcess.stdout.on('data', (data) => {
          versionOutput += data.toString();
        });

        pythonProcess.on('close', (code) => {
          if (code === 0 && versionOutput) {
            // Extract version number
            const versionMatch = versionOutput.match(/Python (\d+)\.(\d+)\.(\d+)/);
            if (versionMatch) {
              const major = parseInt(versionMatch[1], 10);
              const minor = parseInt(versionMatch[2], 10);

              // Check if Python 3.10+ is available
              if (major > 3 || (major === 3 && minor >= 10)) {
                resolve(true);
                return;
              }
            }
          }
          resolve(false);
        });
      });

      if (!pythonVersionCheck) {
        console.log('Python 3.10+ is required for twscrape but not available. Skipping this method.');
        return null;
      }
    } catch (pythonCheckError) {
      console.error('Error checking Python version:', pythonCheckError);
      return null;
    }

    // Create a temporary file to store the tweet data
    const tempDir = os.tmpdir();
    const outputFile = path.join(tempDir, `tweet_${tweetId}_${Date.now()}.json`);

    // Run the Python script to fetch the tweet
    const result = await runPythonTwscrape(tweetId, outputFile);

    if (!result.success) {
      console.error('Failed to run twscrape:', result.error);
      return null;
    }

    // Read the output file
    try {
      const fileContent = await fs.readFile(outputFile, 'utf-8');
      const tweetData = JSON.parse(fileContent);

      // Clean up the temporary file
      await fs.unlink(outputFile).catch(err => console.error('Error deleting temp file:', err));

      // Transform the data into our format
      return transformTweetData(tweetData);
    } catch (fileError) {
      console.error('Error reading tweet data file:', fileError);
      return null;
    }
  } catch (error) {
    console.error('Error in scrapeTweet:', error);
    return null;
  }
}

/**
 * Run the Python twscrape library to fetch tweet data
 *
 * @param tweetId The ID of the tweet to fetch
 * @param outputFile Path to save the JSON output
 * @returns Promise with the result of the operation
 */
async function runPythonTwscrape(tweetId: string, outputFile: string): Promise<{ success: boolean; error?: string }> {
  return new Promise((resolve) => {
    // Create a Python script that uses twscrape
    const pythonScript = `
import asyncio
import json
import sys
import os

# Check Python version
if sys.version_info < (3, 10):
    print("Error: Python 3.10 or higher is required for twscrape")
    with open("${outputFile.replace(/\\/g, '\\\\')}", 'w', encoding='utf-8') as f:
        json.dump({"error": "Python 3.10 or higher is required"}, f)
    sys.exit(1)

try:
    from twscrape import API
    from twscrape.logger import set_log_level
except ImportError:
    # Install twscrape if not available
    try:
        import subprocess
        print("Installing twscrape...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "git+https://github.com/vladkens/twscrape.git"])
        from twscrape import API
        from twscrape.logger import set_log_level
    except Exception as install_error:
        print(f"Error installing twscrape: {install_error}")
        with open("${outputFile.replace(/\\/g, '\\\\')}", 'w', encoding='utf-8') as f:
            json.dump({"error": f"Failed to install twscrape: {str(install_error)}"}, f)
        sys.exit(1)

async def main():
    tweet_id = "${tweetId}"
    output_file = "${outputFile.replace(/\\/g, '\\\\')}"

    # Set log level to ERROR to reduce output
    set_log_level("ERROR")

    # Initialize API
    api = API()

    try:
        # Try to get tweet details
        tweet = await api.tweet_details(tweet_id)

        # Convert to dict and save to file
        tweet_dict = tweet.dict()

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(tweet_dict, f, ensure_ascii=False, indent=2)

        print(f"Tweet data saved to {output_file}")
        return True
    except Exception as e:
        print(f"Error fetching tweet: {str(e)}")
        # Create an empty file to indicate failure
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({"error": str(e)}, f)
        return False

if __name__ == "__main__":
    asyncio.run(main())
`;

    // Create a temporary Python script file
    const scriptPath = path.join(os.tmpdir(), `tweet_scraper_${Date.now()}.py`);

    // Write the script to a file
    fs.writeFile(scriptPath, pythonScript)
      .then(() => {
        // Run the Python script
        const pythonProcess = spawn('python3', [scriptPath]);

        let stdoutData = '';
        let stderrData = '';

        pythonProcess.stdout.on('data', (data) => {
          stdoutData += data.toString();
        });

        pythonProcess.stderr.on('data', (data) => {
          stderrData += data.toString();
        });

        pythonProcess.on('close', (code) => {
          // Clean up the script file
          fs.unlink(scriptPath).catch(err => console.error('Error deleting script file:', err));

          if (code === 0) {
            resolve({ success: true });
          } else {
            console.error(`Python process exited with code ${code}`);
            console.error('STDOUT:', stdoutData);
            console.error('STDERR:', stderrData);
            resolve({ success: false, error: stderrData || stdoutData });
          }
        });
      })
      .catch(error => {
        console.error('Error writing Python script:', error);
        resolve({ success: false, error: error.message });
      });
  });
}

/**
 * Transform the raw tweet data from twscrape into our format
 *
 * @param rawTweetData The raw tweet data from twscrape
 * @returns Transformed tweet data
 */
function transformTweetData(rawTweetData: any): TweetData {
  // Handle error case
  if (rawTweetData.error) {
    throw new Error(rawTweetData.error);
  }

  // Extract media
  const images: string[] = [];
  const videos: string[] = [];

  if (rawTweetData.media) {
    for (const media of rawTweetData.media) {
      if (media.type === 'photo') {
        images.push(media.url);
      } else if (media.type === 'video' || media.type === 'animated_gif') {
        // Get the highest quality video
        if (media.variants && media.variants.length > 0) {
          // Sort by bitrate (highest first)
          const sortedVariants = [...media.variants].sort((a, b) =>
            (b.bitrate || 0) - (a.bitrate || 0)
          );
          videos.push(sortedVariants[0].url);
        }
      }
    }
  }

  // Extract quoted tweet if present
  let quotedTweet: TweetData | undefined;
  if (rawTweetData.quotedTweet) {
    quotedTweet = transformTweetData(rawTweetData.quotedTweet);
  }

  // Create the transformed data
  return {
    id: rawTweetData.id,
    text: rawTweetData.rawContent || rawTweetData.renderedContent || '',
    authorName: rawTweetData.user?.displayname || '',
    authorUsername: rawTweetData.user?.username || '',
    createdAt: rawTweetData.date?.toISOString() || new Date().toISOString(),
    images: images.length > 0 ? images : undefined,
    videos: videos.length > 0 ? videos : undefined,
    quotedTweet: quotedTweet,
    replyToTweet: rawTweetData.inReplyToTweetId,
    replyToUser: rawTweetData.inReplyToUser?.username
  };
}

/**
 * Generate HTML content from tweet data
 *
 * @param tweetData The tweet data
 * @param url The original tweet URL
 * @returns HTML content for the tweet
 */
export function generateTweetHtml(tweetData: TweetData, url: string): string {
  let html = '';

  // Add author information
  if (tweetData.authorName) {
    html += `<h2>${tweetData.authorName}</h2>`;
  }

  if (tweetData.authorUsername) {
    html += `<p class="tweet-username">@${tweetData.authorUsername}</p>`;
  }

  // Add date
  if (tweetData.createdAt) {
    const date = new Date(tweetData.createdAt);
    html += `<p class="tweet-date">${date.toLocaleString()}</p>`;
  }

  // Add tweet text
  html += `<div class="tweet-content">${tweetData.text}</div>`;

  // Add images if present
  if (tweetData.images && tweetData.images.length > 0) {
    html += '<div class="tweet-images">';
    for (const imageUrl of tweetData.images) {
      html += `<img src="${imageUrl}" alt="Tweet image" class="tweet-image" />`;
    }
    html += '</div>';
  }

  // Add videos if present
  if (tweetData.videos && tweetData.videos.length > 0) {
    html += '<div class="tweet-videos">';
    for (const videoUrl of tweetData.videos) {
      html += `
        <video controls class="tweet-video">
          <source src="${videoUrl}" type="video/mp4">
          Your browser does not support the video tag.
        </video>
      `;
    }
    html += '</div>';
  }

  // Add quoted tweet if present
  if (tweetData.quotedTweet) {
    html += '<div class="quoted-tweet">';
    html += '<h3>Quoted Tweet</h3>';
    html += generateTweetHtml(tweetData.quotedTweet, url);
    html += '</div>';
  }

  // Add source note
  html += `<p class="source-note"><small>Content retrieved from X.com (Twitter) using twscrape</small></p>`;

  return html;
}

/**
 * Fallback method to scrape X.com using 12ft.io
 *
 * @param url The tweet URL
 * @returns HTML content or null if failed
 */
export async function scrapeTweetWith12ft(url: string): Promise<string | null> {
  try {
    const twelveFtUrl = `https://12ft.io/proxy?q=${encodeURIComponent(url)}`;
    console.log(`Trying 12ft.io for X.com: ${twelveFtUrl}`);

    const response = await axios.get(twelveFtUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
      },
      timeout: 20000
    });

    const $ = cheerio.load(response.data);

    // Get the tweet content
    const tweetContent = $('.tweet-content').html() ||
                        $('.tweet-text').html() ||
                        $('[data-testid="tweetText"]').html();

    if (tweetContent) {
      console.log('Found tweet content from 12ft.io');

      // Get the author info
      const authorName = $('.tweet-author-name').text().trim() ||
                        $('.fullname').text().trim() ||
                        $('[data-testid="User-Name"]').text().trim();

      const authorUsername = $('.tweet-author-username').text().trim() ||
                            $('.username').text().trim() ||
                            $('[data-testid="User-Username"]').text().trim();

      // Get the tweet date
      const tweetDate = $('.tweet-date').text().trim() ||
                       $('.timestamp').text().trim() ||
                       $('[data-testid="tweet-time"]').text().trim();

      // Construct the content
      let fullContent = '';
      if (authorName) fullContent += `<h2>${authorName}</h2>`;
      if (authorUsername) fullContent += `<p class="tweet-username">${authorUsername}</p>`;
      if (tweetDate) fullContent += `<p class="tweet-date">${tweetDate}</p>`;

      fullContent += `<div class="tweet-content">${tweetContent}</div>`;
      fullContent += `<p class="source-note"><small>Content retrieved from X.com (Twitter) via 12ft.io</small></p>`;

      return fullContent;
    }
  } catch (error) {
    console.error('Error using 12ft.io for X.com:', error);
  }

  return null;
}
