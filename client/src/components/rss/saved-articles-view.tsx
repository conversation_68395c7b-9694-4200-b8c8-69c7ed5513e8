import React, { useState, useRef, useCallback } from 'react';
import { SavedArticle, SavedArticleList } from '@shared/schema';
import { useSavedArticles } from '@/hooks/use-saved-articles';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Trash2, FileDown, Copy, ExternalLink, Eye, EyeOff,
  Import, Export, Upload, Download
} from 'lucide-react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
// Import the processHtmlContent function from rss-tab.tsx
import { processHtmlContent } from './rss-utils';

interface SavedArticlesViewProps {
  listId: number | null;
  listName: string;
}

export function SavedArticlesView({ listId, listName }: SavedArticlesViewProps) {
  const {
    articles, isLoading, removeArticle, generatePdf, generateText,
    exportArticles, importArticles
  } = useSavedArticles(listId);
  const { toast } = useToast();
  const [selectedArticle, setSelectedArticle] = useState<SavedArticle | null>(null);
  const [articleContent, setArticleContent] = useState<string>('');
  const [isLoadingArticle, setIsLoadingArticle] = useState(false);
  const [isCopyingText, setIsCopyingText] = useState(false);
  const [isPdfGenerating, setIsPdfGenerating] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const selectedArticleRef = useRef<HTMLDivElement>(null);

  // Handle viewing an article
  const handleViewArticle = (article: SavedArticle) => {
    setSelectedArticle(article);
    // Process the content with our HTML content processor to highlight dollar amounts
    const content = article.scrapedContent || article.content || 'No content available';
    setArticleContent(processHtmlContent(content));
  };

  // Handle removing an article from the list
  const handleRemoveArticle = async (article: SavedArticle) => {
    if (!listId) return;

    await removeArticle.mutateAsync({ listId, articleId: article.id });

    // If this was the selected article, clear the selection
    if (selectedArticle?.id === article.id) {
      setSelectedArticle(null);
      setArticleContent('');
    }
  };

  // Handle generating PDF from all articles in the list
  const handleGeneratePdf = async () => {
    if (!listId) return;

    setIsPdfGenerating(true);
    try {
      // The PDF is generated on the server side, so we can't directly modify the content
      // However, we can ensure the content is properly displayed in the UI
      await generatePdf(listId);
    } finally {
      setIsPdfGenerating(false);
    }
  };

  // Handle copying all articles as text
  const handleCopyText = async () => {
    if (!listId) return;

    setIsCopyingText(true);
    try {
      const text = await generateText(listId);
      if (text) {
        await navigator.clipboard.writeText(text);
        toast({
          title: 'Copied to clipboard',
          description: 'All articles have been copied to your clipboard.',
        });
      }
    } catch (error) {
      console.error('Error copying text:', error);
      toast({
        title: 'Error',
        description: 'Failed to copy text to clipboard',
        variant: 'destructive',
      });
    } finally {
      setIsCopyingText(false);
    }
  };

  // Handle exporting articles
  const handleExportArticles = async () => {
    if (!listId) return;

    setIsExporting(true);
    try {
      await exportArticles(listId);
    } finally {
      setIsExporting(false);
    }
  };

  // Handle importing articles
  const handleImportArticles = useCallback(async (file: File) => {
    if (!listId) return;

    try {
      // Read the file content
      const fileContent = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = (e) => reject(e);
        reader.readAsText(file);
      });

      // Parse the JSON data
      const importData = JSON.parse(fileContent);

      // Send the data to the server
      await importArticles.mutateAsync({ listId, importData });
      setIsImportDialogOpen(false);
    } catch (error) {
      console.error('Error importing articles:', error);
      toast({
        title: 'Error',
        description: 'Failed to import articles. Make sure the file is a valid JSON export.',
        variant: 'destructive',
      });
    }
  }, [listId, importArticles, toast]);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleImportArticles(file);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">{listName || 'Select a List'}</h3>
        {listId && articles.length > 0 && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsImportDialogOpen(true)}
              disabled={importArticles.isPending}
              className="flex items-center gap-1"
              title="Import articles from JSON file"
            >
              {importArticles.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-1"></div>
                  Importing...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-1" />
                  Import
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportArticles}
              disabled={isExporting}
              className="flex items-center gap-1"
              title="Export articles as JSON file"
            >
              {isExporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-1"></div>
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-1" />
                  Export
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyText}
              disabled={isCopyingText || isPdfGenerating}
              className="flex items-center gap-1"
              title="Copy all articles as text"
            >
              {isCopyingText ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-1"></div>
                  Copying...
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-1" />
                  Copy All
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleGeneratePdf}
              disabled={isCopyingText || isPdfGenerating}
              className="flex items-center gap-1"
              title="Download all articles as PDF"
            >
              {isPdfGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-1"></div>
                  Generating...
                </>
              ) : (
                <>
                  <FileDown className="h-4 w-4 mr-1" />
                  PDF
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      {!listId ? (
        <div className="text-center text-muted-foreground p-4">
          Select a list to view saved articles.
        </div>
      ) : isLoading ? (
        <div className="flex justify-center items-center h-20">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </div>
      ) : articles.length === 0 ? (
        <div className="text-center text-muted-foreground p-4">
          No articles saved in this list yet.
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Articles List */}
          <div>
            <ScrollArea className="h-[calc(100vh-220px)]">
              <div className="space-y-3 pr-4">
                {articles.map((article) => (
                  <div
                    key={article.id}
                    ref={selectedArticle?.id === article.id ? selectedArticleRef : null}
                    className={`p-3 rounded-md cursor-pointer border ${
                      selectedArticle?.id === article.id
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:bg-muted'
                    }`}
                    onClick={() => handleViewArticle(article)}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium text-sm text-white leading-snug">
                        <div
                          dangerouslySetInnerHTML={{
                            __html: processHtmlContent(article.title)
                          }}
                          className="saved-article-title"
                        />
                      </h3>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 ml-2 flex-shrink-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveArticle(article);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex justify-between items-center text-xs text-muted-foreground">
                      <span>
                        {format(new Date(article.publishedAt), 'MMM d, yyyy')}
                      </span>
                      <span>
                        Added: {format(new Date(article.addedAt), 'MMM d, yyyy')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Article Content */}
          <div>
            {selectedArticle ? (
              <Card className="h-[calc(100vh-220px)] flex flex-col">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">
                      <div
                        dangerouslySetInnerHTML={{
                          __html: processHtmlContent(selectedArticle.title)
                        }}
                        className="saved-article-title"
                      />
                    </CardTitle>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7"
                      onClick={() => window.open(selectedArticle.link, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                  <CardDescription>
                    {format(new Date(selectedArticle.publishedAt), 'MMMM d, yyyy')}
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-grow overflow-auto">
                  <div
                    className="prose prose-invert max-w-none"
                    dangerouslySetInnerHTML={{ __html: articleContent }}
                  />
                </CardContent>
              </Card>
            ) : (
              <div className="flex justify-center items-center h-[calc(100vh-220px)] text-muted-foreground">
                Select an article to view its content
              </div>
            )}
          </div>
        </div>
      )}

      {/* Hidden file input for importing */}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        accept=".json"
        onChange={handleFileChange}
      />

      {/* Import Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Articles</DialogTitle>
            <DialogDescription>
              Import articles from a JSON file previously exported from this application.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div
              className="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-300 rounded-lg"
              onDragOver={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              onDrop={(e) => {
                e.preventDefault();
                e.stopPropagation();

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                  handleImportArticles(files[0]);
                }
              }}
            >
              <Upload className="h-10 w-10 text-gray-400 mb-2" />
              <p className="text-sm text-center text-muted-foreground mb-4">
                Drag and drop your JSON file here, or click to select a file
              </p>
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={importArticles.isPending}
              >
                {importArticles.isPending ? 'Importing...' : 'Select File'}
              </Button>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
