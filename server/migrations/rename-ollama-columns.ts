import Database from 'better-sqlite3';

/**
 * Migration script to rename Ollama columns to OpenRouter columns
 */
function runMigration() {
  console.log('Running migration: Renaming Ollama columns to OpenRouter columns');
  
  try {
    // Connect to the database
    const db = new Database('data.db');
    
    // Check if the youtube_videos table exists
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='youtube_videos'").all();
    if (tables.length === 0) {
      console.log('youtube_videos table does not exist. No migration needed.');
      db.close();
      return;
    }
    
    // Get the list of columns in the youtube_videos table
    const tableInfo = db.prepare('PRAGMA table_info(youtube_videos)').all();
    
    // Check if the Ollama columns exist
    const hasOllamaColumns = tableInfo.some(column => column.name === 'ollama_benefit_amounts');
    
    if (!hasOllamaColumns) {
      console.log('Ollama columns do not exist in youtube_videos table. No migration needed.');
      db.close();
      return;
    }
    
    // Check if the OpenRouter columns already exist
    const hasOpenRouterColumns = tableInfo.some(column => column.name === 'openrouter_benefit_amounts');
    
    if (hasOpenRouterColumns) {
      console.log('OpenRouter columns already exist in youtube_videos table. No migration needed.');
      db.close();
      return;
    }
    
    // Create temporary columns with OpenRouter prefix
    console.log('Creating temporary OpenRouter columns...');
    db.exec(`
      ALTER TABLE youtube_videos ADD COLUMN openrouter_benefit_amounts TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_expected_arrival_date TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_eligible_people TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_proof_or_source TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_actions_to_claim TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_priority_tag TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_reason_for_priority TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_viral_potential TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_model_used TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_prompt TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_system_prompt TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN openrouter_prompt_name TEXT DEFAULT NULL;
    `);
    
    // Copy data from Ollama columns to OpenRouter columns
    console.log('Copying data from Ollama columns to OpenRouter columns...');
    db.exec(`
      UPDATE youtube_videos SET
        openrouter_benefit_amounts = ollama_benefit_amounts,
        openrouter_expected_arrival_date = ollama_expected_arrival_date,
        openrouter_eligible_people = ollama_eligible_people,
        openrouter_proof_or_source = ollama_proof_or_source,
        openrouter_actions_to_claim = ollama_actions_to_claim,
        openrouter_priority_tag = ollama_priority_tag,
        openrouter_reason_for_priority = ollama_reason_for_priority,
        openrouter_viral_potential = ollama_viral_potential,
        openrouter_model_used = ollama_model_used,
        openrouter_prompt = ollama_prompt,
        openrouter_system_prompt = ollama_system_prompt,
        openrouter_prompt_name = ollama_prompt_name
    `);
    
    // Update the settings table
    const settingsInfo = db.prepare('PRAGMA table_info(settings)').all();
    const hasOllamaModel = settingsInfo.some(column => column.name === 'ollama_model');
    const hasOllamaAnalysisPrompt = settingsInfo.some(column => column.name === 'ollama_analysis_prompt');
    
    if (hasOllamaModel && !settingsInfo.some(column => column.name === 'openrouter_model')) {
      console.log('Copying Ollama model to OpenRouter model in settings table...');
      db.exec(`
        ALTER TABLE settings ADD COLUMN openrouter_model TEXT DEFAULT 'google/gemini-2.0-flash-exp:free';
        UPDATE settings SET openrouter_model = ollama_model WHERE openrouter_model IS NULL;
      `);
    }
    
    if (hasOllamaAnalysisPrompt && !settingsInfo.some(column => column.name === 'openrouter_analysis_prompt')) {
      console.log('Copying Ollama analysis prompt to OpenRouter analysis prompt in settings table...');
      db.exec(`
        ALTER TABLE settings ADD COLUMN openrouter_analysis_prompt TEXT DEFAULT NULL;
        UPDATE settings SET openrouter_analysis_prompt = ollama_analysis_prompt WHERE openrouter_analysis_prompt IS NULL;
      `);
    }
    
    console.log('Migration completed successfully');
    
    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
}

// Run the migration
runMigration();
