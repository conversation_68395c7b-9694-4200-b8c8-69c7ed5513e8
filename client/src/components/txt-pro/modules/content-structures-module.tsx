import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { Building2, Plus, Save, Edit, Trash2, Copy, FileText } from 'lucide-react';

interface ContentStructure {
  id: string;
  title: string;
  type: 'ssa-update' | 'stimulus-news' | 'benefit-change' | 'deadline-alert' | 'custom';
  description: string;
  sections: ContentSection[];
  dateCreated: Date;
}

interface ContentSection {
  id: string;
  title: string;
  content: string;
  placeholder: string;
  order: number;
}

const ContentStructuresModule: React.FC = () => {
  const { toast } = useToast();
  const [structures, setStructures] = useState<ContentStructure[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [editingStructure, setEditingStructure] = useState<ContentStructure | null>(null);
  const [newStructure, setNewStructure] = useState({
    title: '',
    type: 'custom' as const,
    description: '',
    sections: [] as ContentSection[]
  });

  // Load structures from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('txtpro_content_structures');
    if (saved) {
      setStructures(JSON.parse(saved));
    } else {
      // Initialize with default templates
      const defaultStructures = getDefaultStructures();
      setStructures(defaultStructures);
      localStorage.setItem('txtpro_content_structures', JSON.stringify(defaultStructures));
    }
  }, []);

  // Save structures to localStorage
  useEffect(() => {
    localStorage.setItem('txtpro_content_structures', JSON.stringify(structures));
  }, [structures]);

  const getDefaultStructures = (): ContentStructure[] => [
    {
      id: 'ssa-update-template',
      title: 'SSA Payment Update',
      type: 'ssa-update',
      description: 'Template for Social Security Administration payment updates',
      dateCreated: new Date(),
      sections: [
        {
          id: 'hook',
          title: 'Opening Hook',
          content: 'Attention Social Security recipients! Important news about your [MONTH] payments...',
          placeholder: 'Enter compelling opening that grabs attention',
          order: 1
        },
        {
          id: 'main-update',
          title: 'Main Update',
          content: 'The Social Security Administration has announced [UPDATE DETAILS]...',
          placeholder: 'Describe the main update or change',
          order: 2
        },
        {
          id: 'impact',
          title: 'Impact on Recipients',
          content: 'This means that if you receive [BENEFIT TYPE], you can expect...',
          placeholder: 'Explain how this affects the audience',
          order: 3
        },
        {
          id: 'action-steps',
          title: 'What You Need to Do',
          content: 'Here\'s what you need to know and do: [ACTION ITEMS]...',
          placeholder: 'List specific actions viewers should take',
          order: 4
        },
        {
          id: 'closing',
          title: 'Closing & CTA',
          content: 'Stay informed about your benefits by subscribing and hitting the bell...',
          placeholder: 'Strong closing with call-to-action',
          order: 5
        }
      ]
    },
    {
      id: 'stimulus-news-template',
      title: 'Stimulus News Update',
      type: 'stimulus-news',
      description: 'Template for stimulus payment and economic relief news',
      dateCreated: new Date(),
      sections: [
        {
          id: 'breaking-news',
          title: 'Breaking News Hook',
          content: 'BREAKING: New stimulus update that could put money in your pocket...',
          placeholder: 'Urgent breaking news opening',
          order: 1
        },
        {
          id: 'details',
          title: 'Stimulus Details',
          content: 'Congress has [ACTION TAKEN] regarding [STIMULUS TYPE]...',
          placeholder: 'Specific details about the stimulus',
          order: 2
        },
        {
          id: 'eligibility',
          title: 'Who Qualifies',
          content: 'You may be eligible if you [ELIGIBILITY CRITERIA]...',
          placeholder: 'Clear eligibility requirements',
          order: 3
        },
        {
          id: 'timeline',
          title: 'Timeline & Next Steps',
          content: 'Here\'s when you can expect [TIMELINE] and what happens next...',
          placeholder: 'Timeline and process information',
          order: 4
        }
      ]
    }
  ];

  const handleCreateStructure = () => {
    if (!newStructure.title.trim()) {
      toast({
        title: "Validation Error",
        description: "Structure title is required",
        variant: "destructive"
      });
      return;
    }

    const structure: ContentStructure = {
      id: `structure_${Date.now()}`,
      title: newStructure.title.trim(),
      type: newStructure.type,
      description: newStructure.description.trim(),
      sections: newStructure.sections,
      dateCreated: new Date()
    };

    setStructures(prev => [...prev, structure]);
    setNewStructure({ title: '', type: 'custom', description: '', sections: [] });
    setIsCreating(false);

    toast({
      title: "Structure Created",
      description: `"${structure.title}" has been added to your templates`
    });
  };

  const handleDeleteStructure = (structureId: string) => {
    setStructures(prev => prev.filter(s => s.id !== structureId));
    toast({
      title: "Structure Deleted",
      description: "Content structure has been removed"
    });
  };

  const handleCopyStructure = (structure: ContentStructure) => {
    const structureText = `${structure.title}\n\n${structure.sections.map(section => 
      `${section.title}:\n${section.content}\n`
    ).join('\n')}`;
    
    navigator.clipboard.writeText(structureText);
    toast({
      title: "Structure Copied",
      description: "Content structure has been copied to clipboard"
    });
  };

  const addSection = () => {
    const newSection: ContentSection = {
      id: `section_${Date.now()}`,
      title: '',
      content: '',
      placeholder: '',
      order: newStructure.sections.length + 1
    };

    setNewStructure(prev => ({
      ...prev,
      sections: [...prev.sections, newSection]
    }));
  };

  const updateSection = (sectionId: string, field: keyof ContentSection, value: string) => {
    setNewStructure(prev => ({
      ...prev,
      sections: prev.sections.map(section =>
        section.id === sectionId ? { ...section, [field]: value } : section
      )
    }));
  };

  const removeSection = (sectionId: string) => {
    setNewStructure(prev => ({
      ...prev,
      sections: prev.sections.filter(section => section.id !== sectionId)
    }));
  };

  const typeOptions = [
    { value: 'ssa-update', label: 'SSA Update' },
    { value: 'stimulus-news', label: 'Stimulus News' },
    { value: 'benefit-change', label: 'Benefit Change' },
    { value: 'deadline-alert', label: 'Deadline Alert' },
    { value: 'custom', label: 'Custom Template' }
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Content Structure Templates
              </CardTitle>
              <CardDescription>
                Predefine content skeletons for different types of financial updates
              </CardDescription>
            </div>
            <Button onClick={() => setIsCreating(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Template
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isCreating && (
            <Card className="mb-6 border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-lg">Create New Structure Template</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="structureTitle">Template Title *</Label>
                    <Input
                      id="structureTitle"
                      placeholder="e.g., COLA Increase Update"
                      value={newStructure.title}
                      onChange={(e) => setNewStructure(prev => ({ ...prev, title: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="structureType">Template Type</Label>
                    <Select 
                      value={newStructure.type} 
                      onValueChange={(value: any) => setNewStructure(prev => ({ ...prev, type: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {typeOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="structureDescription">Description</Label>
                  <Textarea
                    id="structureDescription"
                    placeholder="Describe when and how to use this template..."
                    value={newStructure.description}
                    onChange={(e) => setNewStructure(prev => ({ ...prev, description: e.target.value }))}
                    rows={2}
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">Content Sections</Label>
                    <Button onClick={addSection} variant="outline" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Section
                    </Button>
                  </div>

                  {newStructure.sections.map((section, index) => (
                    <Card key={section.id} className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">Section {index + 1}</Label>
                          <Button 
                            onClick={() => removeSection(section.id)} 
                            variant="ghost" 
                            size="sm"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        <Input
                          placeholder="Section title (e.g., Opening Hook)"
                          value={section.title}
                          onChange={(e) => updateSection(section.id, 'title', e.target.value)}
                        />
                        <Textarea
                          placeholder="Default content template..."
                          value={section.content}
                          onChange={(e) => updateSection(section.id, 'content', e.target.value)}
                          rows={3}
                        />
                        <Input
                          placeholder="Placeholder text for guidance"
                          value={section.placeholder}
                          onChange={(e) => updateSection(section.id, 'placeholder', e.target.value)}
                        />
                      </div>
                    </Card>
                  ))}
                </div>

                <div className="flex gap-2">
                  <Button onClick={handleCreateStructure}>
                    <Save className="h-4 w-4 mr-2" />
                    Save Template
                  </Button>
                  <Button onClick={() => setIsCreating(false)} variant="outline">
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {structures.map(structure => (
              <Card key={structure.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{structure.title}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline">
                          {typeOptions.find(opt => opt.value === structure.type)?.label}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {structure.sections.length} sections
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button onClick={() => handleCopyStructure(structure)} variant="ghost" size="sm">
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button onClick={() => handleDeleteStructure(structure.id)} variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">{structure.description}</p>
                  <div className="space-y-2">
                    {structure.sections.map((section, index) => (
                      <div key={section.id} className="text-sm">
                        <span className="font-medium">{index + 1}. {section.title}</span>
                        <p className="text-muted-foreground text-xs mt-1 line-clamp-2">
                          {section.content.substring(0, 100)}...
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {structures.length === 0 && !isCreating && (
            <div className="text-center py-12">
              <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Templates Yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first content structure template to speed up script creation
              </p>
              <Button onClick={() => setIsCreating(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create First Template
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ContentStructuresModule;
