/**
 * Implementation of OpenRouter Prompts methods for SQLiteStorage
 */

import { OpenRouterPrompt } from '@shared/schema';
import { SQLiteStorage } from './sqliteStorage';

export function implementOpenRouterPromptMethods(storage: SQLiteStorage) {
  /**
   * Get all OpenRouter prompts for a user
   */
  storage.getOpenRouterPrompts = async function(userId: number): Promise<OpenRouterPrompt[]> {
    try {
      const stmt = this.db.prepare(`
        SELECT id, user_id, name, prompt_text, is_default, created_at
        FROM openrouter_prompts
        WHERE user_id = ?
        ORDER BY is_default DESC, name ASC
      `);

      const prompts = stmt.all(userId);

      return prompts.map(prompt => ({
        id: prompt.id,
        userId: prompt.user_id,
        name: prompt.name,
        promptText: prompt.prompt_text,
        isDefault: Boolean(prompt.is_default),
        createdAt: prompt.created_at ? new Date(prompt.created_at) : undefined
      }));
    } catch (error) {
      console.error('Error getting OpenRouter prompts:', error);
      throw error;
    }
  };

  /**
   * Get a specific OpenRouter prompt by ID
   */
  storage.getOpenRouterPrompt = async function(promptId: number): Promise<OpenRouterPrompt | undefined> {
    try {
      const stmt = this.db.prepare(`
        SELECT id, user_id, name, prompt_text, is_default, created_at
        FROM openrouter_prompts
        WHERE id = ?
      `);

      const prompt = stmt.get(promptId);

      if (!prompt) return undefined;

      return {
        id: prompt.id,
        userId: prompt.user_id,
        name: prompt.name,
        promptText: prompt.prompt_text,
        isDefault: Boolean(prompt.is_default),
        createdAt: prompt.created_at ? new Date(prompt.created_at) : undefined
      };
    } catch (error) {
      console.error('Error getting OpenRouter prompt:', error);
      throw error;
    }
  };

  /**
   * Create a new OpenRouter prompt
   */
  storage.createOpenRouterPrompt = async function(
    userId: number,
    name: string,
    promptText: string,
    systemPrompt: string = '', // Kept for backward compatibility but not used
    isDefault: boolean = false
  ): Promise<OpenRouterPrompt> {
    try {
      // If this prompt is set as default, unset any existing default prompts for this user
      if (isDefault) {
        const unsetDefaultStmt = this.db.prepare(`
          UPDATE openrouter_prompts
          SET is_default = 0
          WHERE user_id = ? AND is_default = 1
        `);
        unsetDefaultStmt.run(userId);
      }

      // Insert the new prompt
      const insertStmt = this.db.prepare(`
        INSERT INTO openrouter_prompts (user_id, name, prompt_text, is_default)
        VALUES (?, ?, ?, ?)
      `);

      const result = insertStmt.run(userId, name, promptText, isDefault ? 1 : 0);
      const promptId = result.lastInsertRowid as number;

      // If this is the default prompt, update the user's settings
      if (isDefault) {
        const updateSettingsStmt = this.db.prepare(`
          UPDATE settings
          SET selected_prompt_id = ?
          WHERE user_id = ?
        `);
        updateSettingsStmt.run(promptId, userId);
      }

      return {
        id: promptId,
        userId,
        name,
        promptText,
        isDefault,
        createdAt: new Date()
      };
    } catch (error) {
      console.error('Error creating OpenRouter prompt:', error);
      throw error;
    }
  };

  /**
   * Update an existing OpenRouter prompt
   */
  storage.updateOpenRouterPrompt = async function(
    promptId: number,
    updates: Partial<OpenRouterPrompt>
  ): Promise<OpenRouterPrompt> {
    try {
      // Get the current prompt
      const currentPrompt = await this.getOpenRouterPrompt(promptId);
      if (!currentPrompt) {
        throw new Error(`Prompt with ID ${promptId} not found`);
      }

      // If setting this prompt as default, unset any existing default prompts for this user
      if (updates.isDefault && !currentPrompt.isDefault) {
        const unsetDefaultStmt = this.db.prepare(`
          UPDATE openrouter_prompts
          SET is_default = 0
          WHERE user_id = ? AND is_default = 1
        `);
        unsetDefaultStmt.run(currentPrompt.userId);
      }

      // Update the prompt
      const updateStmt = this.db.prepare(`
        UPDATE openrouter_prompts
        SET
          name = COALESCE(?, name),
          prompt_text = COALESCE(?, prompt_text),
          is_default = COALESCE(?, is_default)
        WHERE id = ?
      `);

      updateStmt.run(
        updates.name || null,
        updates.promptText || null,
        updates.isDefault !== undefined ? (updates.isDefault ? 1 : 0) : null,
        promptId
      );

      // If this is now the default prompt, update the user's settings
      if (updates.isDefault) {
        const updateSettingsStmt = this.db.prepare(`
          UPDATE settings
          SET selected_prompt_id = ?
          WHERE user_id = ?
        `);
        updateSettingsStmt.run(promptId, currentPrompt.userId);
      }

      // Get the updated prompt
      const updatedPrompt = await this.getOpenRouterPrompt(promptId);
      if (!updatedPrompt) {
        throw new Error(`Failed to retrieve updated prompt with ID ${promptId}`);
      }

      return updatedPrompt;
    } catch (error) {
      console.error('Error updating OpenRouter prompt:', error);
      throw error;
    }
  };

  /**
   * Delete an OpenRouter prompt
   */
  storage.deleteOpenRouterPrompt = async function(promptId: number): Promise<void> {
    try {
      // Get the prompt to check if it's the default
      const prompt = await this.getOpenRouterPrompt(promptId);
      if (!prompt) {
        return; // Prompt doesn't exist, nothing to delete
      }

      // Delete the prompt
      const deleteStmt = this.db.prepare('DELETE FROM openrouter_prompts WHERE id = ?');
      deleteStmt.run(promptId);

      // If this was the default prompt, update the user's settings
      if (prompt.isDefault) {
        // Find another prompt to set as default
        const findAnotherPromptStmt = this.db.prepare(`
          SELECT id FROM openrouter_prompts
          WHERE user_id = ? AND id != ?
          ORDER BY created_at DESC
          LIMIT 1
        `);

        const anotherPrompt = findAnotherPromptStmt.get(prompt.userId, promptId);

        if (anotherPrompt) {
          // Set this prompt as the new default
          await this.setDefaultOpenRouterPrompt(prompt.userId, anotherPrompt.id);
        } else {
          // No other prompts, set selected_prompt_id to NULL
          const updateSettingsStmt = this.db.prepare(`
            UPDATE settings
            SET selected_prompt_id = NULL
            WHERE user_id = ?
          `);
          updateSettingsStmt.run(prompt.userId);
        }
      }
    } catch (error) {
      console.error('Error deleting OpenRouter prompt:', error);
      throw error;
    }
  };

  /**
   * Set a prompt as the default for a user
   */
  storage.setDefaultOpenRouterPrompt = async function(userId: number, promptId: number): Promise<void> {
    try {
      // Unset any existing default prompts for this user
      const unsetDefaultStmt = this.db.prepare(`
        UPDATE openrouter_prompts
        SET is_default = 0
        WHERE user_id = ? AND is_default = 1
      `);
      unsetDefaultStmt.run(userId);

      // Set the new default prompt
      const setDefaultStmt = this.db.prepare(`
        UPDATE openrouter_prompts
        SET is_default = 1
        WHERE id = ? AND user_id = ?
      `);
      setDefaultStmt.run(promptId, userId);

      // Update the user's settings
      const updateSettingsStmt = this.db.prepare(`
        UPDATE settings
        SET selected_prompt_id = ?
        WHERE user_id = ?
      `);
      updateSettingsStmt.run(promptId, userId);
    } catch (error) {
      console.error('Error setting default OpenRouter prompt:', error);
      throw error;
    }
  };
}
