import { useCallback } from 'react';
import { apiRequest } from '@/lib/queryClient';

/**
 * Hook to resolve YouTube handles to channel IDs
 */
export function useYoutubeHandleResolver() {
  /**
   * Resolves a YouTube handle to a channel ID
   * @param handle YouTube handle (with or without @)
   * @returns Object containing handle, channelId, and channelUrl if successful
   */
  const resolveHandle = useCallback(async (handle: string) => {
    try {
      // Normalize handle (remove @ if present)
      const normalizedHandle = handle.startsWith('@') ? handle.substring(1) : handle;

      // Call the API to resolve the handle
      const response = await apiRequest('GET', `/api/youtube-handle-resolver/${normalizedHandle}`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error resolving handle:', errorData);
        throw new Error(errorData.message || 'Failed to resolve YouTube handle');
      }

      return await response.json();
    } catch (error) {
      console.error('Error in resolveHandle:', error);
      throw error;
    }
  }, []);

  return { resolveHandle };
}
