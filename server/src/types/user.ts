export interface User {
    id: string;
    username: string;
    password: string;
    role: 'admin' | 'user';
    isAdmin: boolean;
    createdAt: Date;
    updatedAt: Date;
}

/**
 * User data returned by API endpoints
 * @example
 * ```json
 * {
 *   "id": "1",
 *   "username": "admin",
 *   "role": "admin",
 *   "isAdmin": true
 * }
 * ```
 */
export interface UserResponse {
    /** Unique user identifier */
    id: string;
    /** User's login name */
    username: string;
    /** User's role (admin/user) */
    role: 'admin' | 'user';
    /** Whether user has admin privileges */
    isAdmin: boolean;
    /** Date when user was created */
    createdAt?: Date;
    /** Date when user was last updated */
    updatedAt?: Date;
}

/**
 * Input for creating a new user
 * @example
 * ```json
 * {
 *   "username": "newuser",
 *   "password": "securepassword123",
 *   "role": "user"
 * }
 * ```
 */
export interface CreateUserInput {
    username: string;
    password: string;
    role: 'admin' | 'user';
}

