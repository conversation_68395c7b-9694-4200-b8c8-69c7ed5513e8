import { Video } from "@shared/schema";

/**
 * Calculate momentum score based on views per hour
 * @param video The video to calculate the score for
 * @returns A score between 0 and 1, where higher is more momentum
 */
export const calculateMomentumScore = (video: Video): number => {
  // Calculate hours since publication
  const hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
  
  // Calculate views per hour (velocity)
  const viewsPerHour = video.viewCount / hoursElapsed;
  
  // Normalize to 0-1 (assuming 1000+ views/hour is very high)
  return Math.min(viewsPerHour / 1000, 1);
};

/**
 * Analyze title quality based on patterns that indicate valuable content
 * @param video The video to analyze
 * @returns A score between 0 and 1, where higher indicates better title quality
 */
export const analyzeTitleQuality = (video: Video): number => {
  const title = video.title.toLowerCase();
  
  // Check for clickbait patterns
  const clickbaitPatterns = [
    /you won't believe/i, 
    /shocking/i, 
    /mind-blowing/i, 
    /\?\?\?+/,
    /!{3,}/
  ];
  
  const clickbaitScore = clickbaitPatterns.reduce((score, pattern) => 
    pattern.test(title) ? score + 0.2 : score, 0);
  
  // Check for educational/valuable content indicators
  const valuePatterns = [
    /how to/i, 
    /learn/i, 
    /guide/i, 
    /tutorial/i, 
    /explained/i,
    /review/i,
    /analysis/i
  ];
  
  const valueScore = valuePatterns.reduce((score, pattern) => 
    pattern.test(title) ? score + 0.15 : score, 0);
  
  // Check for specificity (specific titles often indicate better content)
  const specificityScore = title.length > 40 ? 0.2 : 0;
  
  // Calculate final score (higher is better)
  return Math.min(Math.max(0.5 + valueScore + specificityScore - clickbaitScore, 0), 1);
};

/**
 * Detect evergreen content based on sustained views over time
 * @param video The video to analyze
 * @returns A score between 0 and 1, where higher indicates more evergreen content
 */
export const detectEvergreen = (video: Video): number => {
  // Calculate days since publication
  const daysElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60 * 24));
  
  // Calculate sustained views per day
  const viewsPerDay = video.viewCount / daysElapsed;
  
  // Older videos with high views per day are likely evergreen
  if (daysElapsed > 90) { // 3+ months old
    // Normalize to 0-1 (assuming 1000+ views/day for an old video is excellent)
    const sustainedPopularity = Math.min(viewsPerDay / 1000, 1);
    return sustainedPopularity;
  }
  
  // Newer videos can't be classified as evergreen yet
  return Math.min(viewsPerDay / 5000, 0.5); // Cap newer videos at 0.5
};

/**
 * Calculate trending velocity based on recent performance
 * @param video The video to analyze
 * @returns A score between 0 and 1, where higher indicates stronger trending
 */
export const calculateTrendingVelocity = (video: Video): number => {
  // Calculate hours since publication
  const hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
  
  // Recent videos (less than 48 hours old)
  if (hoursElapsed < 48) {
    // Calculate views per hour
    const viewsPerHour = video.viewCount / hoursElapsed;
    
    // Normalize to 0-1 (assuming 500+ views/hour is very high)
    return Math.min(viewsPerHour / 500, 1);
  }
  
  // Older videos get lower trending scores
  return Math.min((video.viewCount / hoursElapsed) / 200, 1);
};

/**
 * Calculate a fresh and valuable score combining recency and quality
 * @param video The video to analyze
 * @returns A score between 0 and 1, where higher indicates fresher and more valuable content
 */
export const calculateFreshValueScore = (video: Video): number => {
  // Calculate recency (1.0 for new, declining with age)
  const daysElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60 * 24));
  const recencyScore = Math.max(0, 1 - (daysElapsed / 14)); // 2-week window
  
  // Calculate engagement velocity
  const viewsPerDay = video.viewCount / daysElapsed;
  const velocityScore = Math.min(viewsPerDay / 10000, 1);
  
  // Calculate title quality
  const qualityScore = analyzeTitleQuality(video);
  
  // Combine with weights
  return (recencyScore * 0.3) + (velocityScore * 0.4) + (qualityScore * 0.3);
};

/**
 * Calculate a comprehensive quality score
 * @param video The video to analyze
 * @returns A score between 0 and 1, where higher indicates better overall quality
 */
export const calculateComprehensiveQuality = (video: Video): number => {
  // Calculate individual metrics
  const momentumScore = calculateMomentumScore(video);
  const titleQualityScore = analyzeTitleQuality(video);
  const evergreenScore = detectEvergreen(video);
  const trendingScore = calculateTrendingVelocity(video);
  
  // Weights for different factors
  const weights = {
    momentum: 0.25,
    titleQuality: 0.30,
    evergreen: 0.20,
    trending: 0.25
  };
  
  // Calculate weighted score
  return (
    momentumScore * weights.momentum +
    titleQualityScore * weights.titleQuality +
    evergreenScore * weights.evergreen +
    trendingScore * weights.trending
  );
};

/**
 * Calculate views per hour for a video
 * @param video The video to calculate views per hour for
 * @returns The number of views per hour
 */
export const calculateViewsPerHour = (video: Video): number => {
  const hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
  return video.viewCount / hoursElapsed;
};

/**
 * Enhance a video with additional engagement metrics
 * @param video The video to enhance
 * @returns The video with additional engagement metrics
 */
export const enhanceVideo = (video: Video) => {
  const momentumScore = calculateMomentumScore(video);
  const titleQualityScore = analyzeTitleQuality(video);
  const evergreenScore = detectEvergreen(video);
  const trendingScore = calculateTrendingVelocity(video);
  const qualityScore = calculateComprehensiveQuality(video);
  const freshValueScore = calculateFreshValueScore(video);
  const viewsPerHour = calculateViewsPerHour(video);
  
  return {
    ...video,
    momentumScore,
    titleQualityScore,
    evergreenScore,
    trendingScore,
    qualityScore,
    freshValueScore,
    viewsPerHour
  };
};

/**
 * Sort videos based on the specified sort method
 * @param videos The videos to sort
 * @param sortMethod The method to sort by
 * @returns The sorted videos
 */
export const sortVideos = (videos: Video[], sortMethod: string): Video[] => {
  // Enhance videos with additional metrics
  const enhancedVideos = videos.map(enhanceVideo);
  
  // Sort based on selected method
  switch (sortMethod) {
    case "trending":
      return enhancedVideos.sort((a, b) => (b as any).trendingScore - (a as any).trendingScore);
    case "momentum":
      return enhancedVideos.sort((a, b) => (b as any).momentumScore - (a as any).momentumScore);
    case "evergreen":
      return enhancedVideos.sort((a, b) => (b as any).evergreenScore - (a as any).evergreenScore);
    case "quality":
      return enhancedVideos.sort((a, b) => (b as any).qualityScore - (a as any).qualityScore);
    case "fresh_valuable":
      return enhancedVideos.sort((a, b) => (b as any).freshValueScore - (a as any).freshValueScore);
    case "views_per_hour":
      return enhancedVideos.sort((a, b) => (b as any).viewsPerHour - (a as any).viewsPerHour);
    case "date_asc":
      return enhancedVideos.sort((a, b) => new Date(a.publishedAt).getTime() - new Date(b.publishedAt).getTime());
    case "date_desc":
      return enhancedVideos.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());
    case "views_asc":
      return enhancedVideos.sort((a, b) => a.viewCount - b.viewCount);
    case "views_desc":
      return enhancedVideos.sort((a, b) => b.viewCount - a.viewCount);
    default:
      return enhancedVideos;
  }
};
