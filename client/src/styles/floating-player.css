/* Floating player styles */

/* Optimize performance during dragging */
div[data-floating-player].dragging {
  will-change: left, top;
  transition: none !important;
}

/* Drag handle styles */
.drag-handle {
  opacity: 0.9;
  transition: opacity 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.drag-handle:hover {
  opacity: 1;
}

div[data-floating-player].dragging .drag-handle {
  cursor: grabbing;
  opacity: 1;
  background-color: rgba(59, 130, 246, 0.2);
}

/* Visual feedback during resizing */
div[data-floating-player].resizing {
  will-change: width, height;
  transition: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4) !important;
}



/* Hide YouTube title bar */
.youtube-container {
  position: relative;
}

.youtube-container iframe {
  position: relative;
  z-index: 1;
  margin-top: -30px; /* Push the iframe up to hide the title bar */
  height: calc(100% + 30px); /* Compensate for the margin-top */
}

/* Styles for the resize handles */
.resize-handle {
  opacity: 0;
  transition: opacity 0.2s ease;
  background-color: rgba(255, 255, 255, 0.2);
  z-index: 20;
}

div[data-floating-player]:hover .resize-handle {
  opacity: 0.3;
}

.resize-handle:hover {
  opacity: 0.8 !important;
  background-color: rgba(59, 130, 246, 0.4);
}

.resize-e, .resize-w {
  top: 50%;
  transform: translateY(-50%);
}

.resize-n, .resize-s {
  left: 50%;
  transform: translateX(-50%);
}

.resize-e {
  right: 0;
  cursor: e-resize;
}

.resize-w {
  left: 0;
  cursor: w-resize;
}

.resize-n {
  top: 0;
  cursor: n-resize;
}

.resize-s {
  bottom: 0;
  cursor: s-resize;
}

.resize-ne {
  top: 0;
  right: 0;
  cursor: ne-resize;
}

.resize-nw {
  top: 0;
  left: 0;
  cursor: nw-resize;
}

.resize-se {
  bottom: 0;
  right: 0;
  cursor: se-resize;
}

.resize-sw {
  bottom: 0;
  left: 0;
  cursor: sw-resize;
}
