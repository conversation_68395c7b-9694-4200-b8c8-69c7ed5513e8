import Database from 'better-sqlite3';

// This script adds the role column to the users table and sets the admin role for the admin user

async function migrateDatabase() {
  try {
    console.log('Starting database migration...');
    const db = new Database('data.db');

    // Check if role column exists in users table
    const tableInfo = db.prepare("PRAGMA table_info(users)").all();
    const roleColumnExists = tableInfo.some(col => col.name === 'role');

    if (!roleColumnExists) {
      console.log('Adding role column to users table...');
      // Add role column with default value 'user'
      db.exec(`ALTER TABLE users ADD COLUMN role TEXT DEFAULT 'user'`);
      console.log('Role column added successfully.');
    } else {
      console.log('Role column already exists in users table.');
    }

    // Set admin role for user with username 'admin'
    const updateAdminRole = db.prepare("UPDATE users SET role = 'admin' WHERE username = 'admin'");
    const result = updateAdminRole.run();
    console.log(`Updated ${result.changes} admin user(s).`);

    console.log('Migration completed successfully.');
    db.close();
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

migrateDatabase();
