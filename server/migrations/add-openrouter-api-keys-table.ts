import Database from 'better-sqlite3';
import { fileURLToPath } from 'url';
import path from 'path';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function main() {
  console.log('Running migration to add OpenRouter API keys table...');

  // Connect to the database
  const db = new Database(path.resolve(__dirname, '../../data.db'));

  try {
    // Create the openrouter_api_keys table if it doesn't exist
    db.exec(`
      CREATE TABLE IF NOT EXISTS openrouter_api_keys (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        token TEXT NOT NULL,
        name TEXT,
        is_active BOOLEAN DEFAULT 1,
        last_used DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        user_id INTEGER NOT NULL,
        is_default BOOLEAN DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users(id)
      );
    `);

    console.log('Successfully created OpenRouter API keys table');
  } catch (error) {
    console.error('Error creating OpenRouter API keys table:', error);
    throw error;
  } finally {
    // Close the database connection
    db.close();
  }
}

main().catch(console.error);
