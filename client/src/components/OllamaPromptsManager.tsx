import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  CircularProgress,
  Divider
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { OllamaPrompt } from '@shared/schema';
import {
  getOllamaPrompts,
  createOllamaPrompt,
  updateOllamaPrompt,
  deleteOllamaPrompt
} from '../services/ollamaPromptsService';

interface OllamaPromptsManagerProps {
  selectedPromptId: number | null;
  onPromptSelected: (promptId: number | null) => void;
  onPromptTextChanged?: (promptText: string | null) => void;
}

const OllamaPromptsManager: React.FC<OllamaPromptsManagerProps> = ({
  selectedPromptId,
  onPromptSelected,
  onPromptTextChanged
}) => {
  const [prompts, setPrompts] = useState<OllamaPrompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [promptName, setPromptName] = useState('');
  const [promptText, setPromptText] = useState('');
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [promptToDelete, setPromptToDelete] = useState<OllamaPrompt | null>(null);

  // Load prompts when component mounts or when selectedPromptId changes
  useEffect(() => {
    loadPrompts();
  }, [selectedPromptId]);

  // Load prompts from the server
  const loadPrompts = useCallback(async () => {
    try {
      setLoading(true);
      const data = await getOllamaPrompts();
      setPrompts(data);

      // Only handle selection logic if we have prompts
      if (data.length > 0) {
        // If a prompt ID is already selected, verify it exists
        if (selectedPromptId) {
          const selectedPromptExists = data.some(p => p.id === selectedPromptId);

          if (selectedPromptExists) {
            // The selected prompt exists, find it and use its text
            const selectedPrompt = data.find(p => p.id === selectedPromptId);
            if (selectedPrompt && onPromptTextChanged) {
              onPromptTextChanged(selectedPrompt.promptText);
            }
          }
        }
        // If no prompt is selected and we have a default, select it
        else if (!selectedPromptId) {
          const defaultPrompt = data.find(p => p.isDefault);
          if (defaultPrompt) {
            onPromptSelected(defaultPrompt.id);
            if (onPromptTextChanged) {
              onPromptTextChanged(defaultPrompt.promptText);
            }
          }
        }
      }

      setError(null);
    } catch (err) {
      console.error('Error loading prompts:', err);
      setError('Failed to load prompts');
    } finally {
      setLoading(false);
    }
  }, [selectedPromptId, onPromptSelected, onPromptTextChanged]);

  // Handle selecting a prompt
  const handleSelectPrompt = (promptId: number) => {
    if (promptId !== selectedPromptId) {
      const selectedPrompt = prompts.find(p => p.id === promptId);
      if (selectedPrompt) {
        // Update the selected ID
        onPromptSelected(promptId);

        // Update the prompt text
        if (onPromptTextChanged) {
          onPromptTextChanged(selectedPrompt.promptText);
        }
      }
    }
  };

  // Open the dialog to add a new prompt
  const handleAddPrompt = () => {
    setPromptName('');
    setPromptText('');
    setOpenDialog(true);
  };

  // Handle prompt deletion
  const handleDeletePrompt = (prompt: OllamaPrompt) => {
    setPromptToDelete(prompt);
    setDeleteConfirmOpen(true);
  };

  // Confirm and execute prompt deletion
  const confirmDeletePrompt = async () => {
    if (!promptToDelete) return;

    try {
      await deleteOllamaPrompt(promptToDelete.id);

      // If the deleted prompt was selected, clear the selection
      if (selectedPromptId === promptToDelete.id) {
        onPromptSelected(null);
        if (onPromptTextChanged) {
          onPromptTextChanged(null);
        }
      }

      // Reload prompts
      await loadPrompts();
    } catch (err) {
      console.error('Error deleting prompt:', err);
    } finally {
      setDeleteConfirmOpen(false);
      setPromptToDelete(null);
    }
  };

  // Save a new prompt
  const handleSavePrompt = async () => {
    if (!promptName.trim() || !promptText.trim()) return;

    try {
      // Create new prompt
      const newPrompt = await createOllamaPrompt(promptName, promptText, false);

      // Select the new prompt if no prompt is currently selected
      if (!selectedPromptId) {
        onPromptSelected(newPrompt.id);
        if (onPromptTextChanged) {
          onPromptTextChanged(newPrompt.promptText);
        }
      }

      // Reload prompts
      await loadPrompts();
    } catch (err) {
      console.error('Error saving prompt:', err);
    } finally {
      setOpenDialog(false);
    }
  };

  return (
    <Box sx={{ mb: 2 }}>
      <Typography variant="h6" gutterBottom>
        OpenRouter Analysis Prompts
      </Typography>

      {loading ? (
        <CircularProgress size={24} />
      ) : error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1">
              Select a Prompt Template
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddPrompt}
              size="small"
            >
              Add New Prompt
            </Button>
          </Box>

          {prompts.length > 0 && (
            <List sx={{ bgcolor: '#1e1e1e', color: 'white', borderRadius: '4px', mb: 2 }}>
              {prompts.map((prompt) => (
                <React.Fragment key={prompt.id}>
                  <ListItem
                    button
                    selected={selectedPromptId === prompt.id}
                    onClick={() => handleSelectPrompt(prompt.id)}
                    sx={{
                      borderRadius: '4px',
                      mb: 1,
                      backgroundColor: selectedPromptId === prompt.id ? '#2a2a2a' : 'transparent',
                      '&:hover': {
                        backgroundColor: selectedPromptId === prompt.id ? '#2a2a2a' : 'rgba(255, 255, 255, 0.05)'
                      },
                      '&.Mui-selected': {
                        backgroundColor: '#2a2a2a'
                      }
                    }}
                  >
                    <ListItemText
                      primary={
                        <Typography sx={{ color: 'white', fontWeight: selectedPromptId === prompt.id ? 'bold' : 'normal' }}>
                          {String(prompt.name)}
                        </Typography>
                      }
                      secondary={
                        selectedPromptId === prompt.id ?
                          <Typography variant="caption" sx={{ color: '#3b82f6' }}>
                            Active prompt
                          </Typography> : null
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        size="small"
                        onClick={() => handleDeletePrompt(prompt)}
                        disabled={prompts.length === 1} // Prevent deleting the last prompt
                        sx={{ color: 'white' }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {prompts.indexOf(prompt) < prompts.length - 1 && <Divider sx={{ backgroundColor: '#333' }} />}
                </React.Fragment>
              ))}
            </List>
          )}
        </>
      )}

      {/* Add New Prompt Dialog */}
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          style: {
            backgroundColor: '#1e1e1e',
            color: 'white',
          }
        }}
      >
        <DialogTitle>Add New Prompt</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Prompt Name"
            fullWidth
            value={promptName}
            onChange={(e) => setPromptName(e.target.value)}
            sx={{
              mb: 2,
              '& .MuiInputBase-root': {
                color: 'white',
                backgroundColor: '#2a2a2a',
              },
              '& .MuiInputLabel-root': {
                color: 'white',
              },
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#555',
              },
            }}
          />
          <TextField
            label="Prompt Text"
            multiline
            rows={10}
            fullWidth
            value={promptText}
            onChange={(e) => setPromptText(e.target.value)}
            variant="outlined"
            sx={{
              mb: 2,
              '& .MuiInputBase-root': {
                color: 'white',
                backgroundColor: '#2a2a2a',
              },
              '& .MuiInputLabel-root': {
                color: 'white',
              },
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#555',
              },
              '& textarea': {
                color: 'white',
              },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ backgroundColor: '#1e1e1e' }}>
          <Button
            onClick={() => setOpenDialog(false)}
            sx={{ color: 'white' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSavePrompt}
            variant="contained"
            color="primary"
            disabled={!promptName.trim() || !promptText.trim()}
            sx={{
              backgroundColor: '#3b82f6',
              color: 'white',
            }}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
        PaperProps={{
          style: {
            backgroundColor: '#1e1e1e',
            color: 'white',
          }
        }}
      >
        <DialogTitle>Delete Prompt</DialogTitle>
        <DialogContent>
          <Typography sx={{ color: 'white' }}>
            Are you sure you want to delete the prompt "{promptToDelete?.name}"?
          </Typography>
        </DialogContent>
        <DialogActions sx={{ backgroundColor: '#1e1e1e' }}>
          <Button
            onClick={() => setDeleteConfirmOpen(false)}
            sx={{ color: 'white' }}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmDeletePrompt}
            color="error"
            sx={{ color: '#f87171' }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default OllamaPromptsManager;
