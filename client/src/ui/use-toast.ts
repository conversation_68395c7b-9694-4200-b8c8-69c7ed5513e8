import { ToastProps } from './toast';
import { ToasterAPI } from './Toaster';

type ToastOptions = Omit<ToastProps, 'id' | 'onDismiss'>;

export function useToast(): ToasterAPI {
  if (!window.__toasterRef) {
    throw new Error('Toaster context not found. Wrap your app with <ToasterProvider>');
  }
  
  return {
    toast: (props: ToastOptions) => window.__toasterRef.current?.toast(props),
    dismiss: (id: string) => window.__toasterRef.current?.dismiss(id),
    dismissAll: () => window.__toasterRef.current?.dismissAll(),
  };
}

