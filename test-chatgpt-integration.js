#!/usr/bin/env node

/**
 * Test script for ChatGPT integration
 * This script tests the ChatGPT client service directly
 */

import axios from 'axios';

// Test the ChatGPT server connection
async function testChatGPTConnection() {
  console.log('🧪 Testing ChatGPT Server Connection...');
  console.log('=====================================');
  
  const baseURL = 'http://localhost:7777';
  
  try {
    // Test 1: Check if server is running
    console.log('1. Testing server availability...');
    try {
      const response = await axios.get(`${baseURL}/`, { timeout: 5000 });
      console.log('   ✅ Server is running and responding');
    } catch (error) {
      console.log('   ❌ Server is not responding');
      console.log('   Error:', error.message);
      return false;
    }
    
    // Test 2: Check API status endpoint
    console.log('2. Testing API status endpoint...');
    try {
      const response = await axios.get(`${baseURL}/api/status`, { timeout: 10000 });
      console.log('   ✅ API status endpoint is working');
      console.log('   Response:', response.data);
    } catch (error) {
      console.log('   ⚠️  API status endpoint failed, but server is running');
      console.log('   Error:', error.message);
    }
    
    // Test 3: Test main application integration
    console.log('3. Testing main application integration...');
    try {
      // Test the integration endpoint instead of importing TypeScript
      const response = await axios.get('http://localhost:5001/api/txt-tab/chatgpt/status', {
        timeout: 5000,
        validateStatus: () => true // Accept any status code
      });

      if (response.status === 401) {
        console.log('   ⚠️  Integration endpoint exists but requires authentication');
      } else if (response.status === 200) {
        console.log('   ✅ ChatGPT client integration working');
        console.log('   Status:', response.data);
      } else {
        console.log('   ⚠️  Integration endpoint responded with status:', response.status);
      }
    } catch (error) {
      console.log('   ❌ ChatGPT client integration failed');
      console.log('   Error:', error.message);
    }
    
    return true;
    
  } catch (error) {
    console.log('❌ Overall test failed:', error.message);
    return false;
  }
}

// Test the main application server
async function testMainApplication() {
  console.log('\n🧪 Testing Main Application...');
  console.log('==============================');
  
  const baseURL = 'http://localhost:5001';
  
  try {
    // Test if main app is running
    console.log('1. Testing main application availability...');
    const response = await axios.get(`${baseURL}/`, { timeout: 5000 });
    console.log('   ✅ Main application is running');
    
    return true;
  } catch (error) {
    console.log('   ❌ Main application is not running');
    console.log('   Error:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 ChatGPT Integration Test Suite');
  console.log('==================================\n');
  
  const mainAppRunning = await testMainApplication();
  const chatGPTRunning = await testChatGPTConnection();
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  console.log(`Main Application: ${mainAppRunning ? '✅ Running' : '❌ Not Running'}`);
  console.log(`ChatGPT Server: ${chatGPTRunning ? '✅ Running' : '❌ Not Running'}`);
  
  if (mainAppRunning && chatGPTRunning) {
    console.log('\n🎉 Integration is ready for testing!');
    console.log('Next steps:');
    console.log('1. Open http://localhost:5001 in your browser');
    console.log('2. Navigate to the TXT tab');
    console.log('3. Go to the ChatGPT Integration sub-tab');
    console.log('4. Check the server status section');
  } else {
    console.log('\n⚠️  Integration setup incomplete:');
    if (!mainAppRunning) {
      console.log('- Start the main application with: npm run dev');
    }
    if (!chatGPTRunning) {
      console.log('- Start the ChatGPT server with: ./start_chatgpt_server.sh');
    }
  }
}

// Run the tests
runTests().catch(console.error);
