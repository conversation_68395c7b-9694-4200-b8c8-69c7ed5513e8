// refresh-worker-simple.js
// A simplified version of the refresh worker that doesn't use ES modules

// Set up event listener for messages from the main thread
self.addEventListener('message', function(event) {
  const { type, payload } = event.data;
  
  try {
    switch (type) {
      case 'refresh-videos':
        // Simulate the refresh operation
        refreshVideos(payload);
        break;
        
      case 'refresh-group':
        // Refresh a specific group
        refreshGroupVideos(payload);
        break;
        
      default:
        console.error('Unknown message type:', type);
    }
  } catch (error) {
    // Send error back to main thread
    self.postMessage({
      type: 'error',
      error: error.message || 'Unknown error'
    });
  }
});

// Function to refresh videos
function refreshVideos(options) {
  try {
    // Notify main thread that refresh has started
    self.postMessage({ type: 'refresh-started' });
    
    // Use fetch API to make the request
    fetch('/api/videos/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(options || {})
    })
    .then(function(response) {
      if (!response.ok) {
        throw new Error('Refresh failed: ' + response.status + ' ' + response.statusText);
      }
      return response.json();
    })
    .then(function(result) {
      // Send success message back to main thread
      self.postMessage({
        type: 'refresh-completed',
        result: result
      });
    })
    .catch(function(error) {
      // Send error back to main thread
      self.postMessage({
        type: 'error',
        error: error.message || 'Unknown error in fetch'
      });
    });
  } catch (error) {
    // Send error back to main thread
    self.postMessage({
      type: 'error',
      error: error.message || 'Unknown error in refreshVideos'
    });
  }
}

// Function to refresh group videos
function refreshGroupVideos(groupId) {
  try {
    // Notify main thread that refresh has started
    self.postMessage({ 
      type: 'group-refresh-started',
      groupId: groupId
    });
    
    // Use fetch API to make the request
    fetch('/api/keyword-groups/' + groupId + '/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(function(response) {
      if (!response.ok) {
        throw new Error('Group refresh failed: ' + response.status + ' ' + response.statusText);
      }
      return response.json();
    })
    .then(function(result) {
      // Send success message back to main thread
      self.postMessage({
        type: 'group-refresh-completed',
        groupId: groupId,
        result: result
      });
    })
    .catch(function(error) {
      // Send error back to main thread
      self.postMessage({
        type: 'error',
        error: error.message || 'Unknown error in fetch',
        groupId: groupId
      });
    });
  } catch (error) {
    // Send error back to main thread
    self.postMessage({
      type: 'error',
      error: error.message || 'Unknown error in refreshGroupVideos',
      groupId: groupId
    });
  }
}
