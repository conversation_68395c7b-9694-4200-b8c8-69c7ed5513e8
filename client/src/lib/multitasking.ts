// multitasking.ts
// Main export file for all multitasking utilities

// Export request manager
export {
  requestManager,
  useRequestManager,
  RequestPriority,
  RequestType
} from './request-manager';

// Export worker manager
export {
  refreshWorker,
  useRefreshWorker,
  RefreshWorkerManager
} from './worker-manager';

// Helper function to detect browser support for multitasking features
export function detectMultitaskingSupport() {
  const features = {
    webWorkers: typeof Worker !== 'undefined',
    requestIdleCallback: typeof window.requestIdleCallback !== 'undefined',
    serviceWorker: 'serviceWorker' in navigator,
    sharedWorkers: typeof SharedWorker !== 'undefined',
    concurrency: navigator.hardwareConcurrency || 4
  };

  return features;
}

// Initialize multitasking features
export function initializeMultitasking() {
  const support = detectMultitaskingSupport();

  console.log('Multitasking support:', support);

  // Import the refreshWorker here to avoid reference errors
  let refreshWorker;
  try {
    // Try dynamic import first (preferred for ES modules)
    import('./worker-manager').then(module => {
      refreshWorker = module.refreshWorker;
      console.log('Refresh worker imported successfully via dynamic import');
    }).catch(err => {
      console.error('Error importing worker-manager via dynamic import:', err);
    });
  } catch (error) {
    console.error('Error setting up worker import:', error);
  }

  // Set up idle callback for low-priority tasks
  if (support.requestIdleCallback) {
    window.addEventListener('load', () => {
      window.requestIdleCallback(() => {
        console.log('Initializing background tasks during idle time');
        // Pre-initialize the worker via dynamic import to ensure it's loaded
        import('./worker-manager').then(module => {
          console.log('Pre-initializing refresh worker via idle callback');
        }).catch(err => {
          console.error('Error importing worker-manager in idle callback:', err);
        });
      });
    });
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      console.log('Initializing background tasks with setTimeout fallback');
      // Pre-initialize the worker via dynamic import to ensure it's loaded
      import('./worker-manager').then(module => {
        console.log('Pre-initializing refresh worker via setTimeout');
      }).catch(err => {
        console.error('Error importing worker-manager in setTimeout:', err);
      });
    }, 1000);
  }

  return support;
}

// Export video loading and refresh hooks
export { useVideoLoader } from '../hooks/use-video-loader';
export { useVideoRefresh } from '../hooks/use-video-refresh';
