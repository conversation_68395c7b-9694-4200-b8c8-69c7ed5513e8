import { Router } from 'express';
import { resolveYoutubeHandle } from '../utils/youtube-handle-resolver';

const router = Router();

// Resolve a YouTube handle to a channel ID
router.get('/:handle', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { handle } = req.params;
    
    if (!handle) {
      return res.status(400).json({ message: "Handle parameter is required" });
    }
    
    // Remove @ if it exists at the beginning
    const normalizedHandle = handle.startsWith('@') ? handle.substring(1) : handle;
    
    console.log(`Attempting to resolve YouTube handle: ${normalizedHandle}`);
    const channelId = await resolveYoutubeHandle(normalizedHandle);
    
    if (channelId) {
      return res.json({ 
        handle: `@${normalizedHandle}`, 
        channelId,
        channelUrl: `https://www.youtube.com/channel/${channelId}`
      });
    } else {
      return res.status(404).json({ 
        message: "Could not resolve handle to channel ID",
        handle: `@${normalizedHandle}`
      });
    }
  } catch (error) {
    console.error('Error resolving YouTube handle:', error);
    res.status(500).json({ message: "Failed to resolve YouTube handle" });
  }
});

export default router;
