import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { useWorkflow } from '../workflow-state';
import { 
  Save, 
  Plus, 
  X, 
  Edit, 
  Trash2, 
  Copy, 
  Scissors, 
  Target,
  FileText,
  Lightbulb,
  Zap,
  BookOpen
} from 'lucide-react';

interface Script {
  id: string;
  title: string;
  sourceVideoUrl?: string;
  transcript: string;
  tags: string[];
  dateAdded: Date;
}

interface Chunk {
  id: string;
  name: string;
  description: string;
  content: string;
  purpose: 'opening-hook' | 'call-to-action' | 'information-drop' | 'summary' | 'other';
  tags: string[];
  sourceScriptId?: string;
  dateCreated: Date;
}

interface ChunkingModuleProps {
  scripts: Script[];
  chunks: Chunk[];
  setChunks: React.Dispatch<React.SetStateAction<Chunk[]>>;
  selectedScript: Script | null;
  toast: any;
}

const ChunkingModuleRedesigned: React.FC<ChunkingModuleProps> = ({
  scripts,
  chunks,
  setChunks,
  selectedScript,
  toast
}) => {
  const { updateStepData, markStepComplete } = useWorkflow();
  const [isCreatingChunk, setIsCreatingChunk] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [chunkForm, setChunkForm] = useState({
    name: '',
    description: '',
    content: '',
    purpose: 'other' as Chunk['purpose'],
    tags: [] as string[]
  });
  const [newTag, setNewTag] = useState('');
  const [filterPurpose, setFilterPurpose] = useState<string>('all');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const purposeOptions = [
    { value: 'opening-hook', label: 'Opening Hook', color: 'bg-red-100 text-red-800', icon: Target },
    { value: 'call-to-action', label: 'Call-To-Action', color: 'bg-green-100 text-green-800', icon: Zap },
    { value: 'information-drop', label: 'Information Drop', color: 'bg-blue-100 text-blue-800', icon: BookOpen },
    { value: 'summary', label: 'Summary', color: 'bg-purple-100 text-purple-800', icon: FileText },
    { value: 'other', label: 'Other', color: 'bg-gray-100 text-gray-800', icon: Lightbulb }
  ];

  const getSlickV1DefaultChunks = (): Chunk[] => [
    {
      id: 'slick-v1-urgent-hook',
      name: 'SlickV1 Urgent Hook',
      description: 'High-impact opening for Social Security updates',
      content: 'Hello everyone, this is urgent, this is real. Are you one of the millions of Americans receiving Social Security benefits? You need to hear this right now.',
      purpose: 'opening-hook',
      tags: ['SlickV1', 'urgent', 'social-security'],
      dateCreated: new Date()
    },
    {
      id: 'slick-v1-scam-warning',
      name: 'SlickV1 Scam Warning CTA',
      description: 'Essential scam protection for beneficiaries',
      content: 'Before we continue, I need to give you a critical warning. The Social Security Administration will NEVER ask for personal information over unsolicited calls, emails, or texts. They will never ask for fees, gift cards, wire transfers, or cryptocurrency. If someone contacts you claiming to be from SSA asking for these things, hang up immediately and report it.',
      purpose: 'call-to-action',
      tags: ['SlickV1', 'scam-warning', 'protection'],
      dateCreated: new Date()
    },
    {
      id: 'slick-v1-financial-steps',
      name: 'SlickV1 Financial Action Steps',
      description: 'Practical financial guidance for beneficiaries',
      content: 'Here\'s what you need to do right now: First, verify your contact information with the Social Security Administration. Second, update your banking information if needed. Third, if you receive extra money, prioritize paying down high-interest debt, then build your emergency savings. This is about restoring fairness and dignity to your financial life.',
      purpose: 'information-drop',
      tags: ['SlickV1', 'action-steps', 'financial-advice'],
      dateCreated: new Date()
    },
    {
      id: 'slick-v1-empathetic-close',
      name: 'SlickV1 Empathetic Close',
      description: 'Compassionate closing with engagement',
      content: 'I know many of you have been struggling to stay afloat, hit hardest by inflation and rising costs. Your concerns are completely valid. But better days are here and brighter ones may be just around the corner. Make sure you hit that subscribe button and turn on notifications so you never miss important updates like this one. Drop a comment below - let me know how this information would help you.',
      purpose: 'call-to-action',
      tags: ['SlickV1', 'empathetic', 'subscribe', 'engagement'],
      dateCreated: new Date()
    }
  ];

  // Initialize with default chunks if none exist
  useEffect(() => {
    if (chunks.length === 0) {
      const defaultChunks = getSlickV1DefaultChunks();
      setChunks(defaultChunks);
    }
  }, [chunks.length, setChunks]);

  const handleTextSelection = () => {
    if (textareaRef.current) {
      const start = textareaRef.current.selectionStart;
      const end = textareaRef.current.selectionEnd;
      const selected = textareaRef.current.value.substring(start, end);
      
      if (selected.trim()) {
        setSelectedText(selected.trim());
        setChunkForm(prev => ({ ...prev, content: selected.trim() }));
        setIsCreatingChunk(true);
      }
    }
  };

  const handleCreateChunk = () => {
    if (!chunkForm.name.trim() || !chunkForm.content.trim()) {
      toast({
        title: "Validation Error",
        description: "Chunk name and content are required",
        variant: "destructive"
      });
      return;
    }

    const newChunk: Chunk = {
      id: `chunk_${Date.now()}`,
      name: chunkForm.name.trim(),
      description: chunkForm.description.trim(),
      content: chunkForm.content.trim(),
      purpose: chunkForm.purpose,
      tags: chunkForm.tags,
      sourceScriptId: selectedScript?.id,
      dateCreated: new Date()
    };

    setChunks(prev => [...prev, newChunk]);
    
    // Update workflow state
    const updatedChunks = [...chunks, newChunk];
    updateStepData('chunk-creation', { selectedChunks: updatedChunks });
    markStepComplete('chunk-creation');

    setChunkForm({
      name: '',
      description: '',
      content: '',
      purpose: 'other',
      tags: []
    });
    setSelectedText('');
    setIsCreatingChunk(false);

    toast({
      title: "Chunk Created",
      description: `"${newChunk.name}" has been added to your library`
    });
  };

  const handleQuickCreateChunk = (content: string, purpose: Chunk['purpose'], name: string) => {
    const newChunk: Chunk = {
      id: `chunk_${Date.now()}`,
      name,
      description: `Quick chunk from ${selectedScript?.title || 'transcript'}`,
      content,
      purpose,
      tags: [purpose],
      sourceScriptId: selectedScript?.id,
      dateCreated: new Date()
    };

    setChunks(prev => [...prev, newChunk]);
    
    // Update workflow state
    const updatedChunks = [...chunks, newChunk];
    updateStepData('chunk-creation', { selectedChunks: updatedChunks });
    markStepComplete('chunk-creation');

    toast({
      title: "Quick Chunk Created",
      description: `"${newChunk.name}" added to your library`
    });
  };

  const handleDeleteChunk = (chunkId: string) => {
    setChunks(prev => prev.filter(chunk => chunk.id !== chunkId));
    toast({
      title: "Chunk Deleted",
      description: "Chunk has been removed from your library"
    });
  };

  const handleCopyChunk = (chunk: Chunk) => {
    navigator.clipboard.writeText(chunk.content);
    toast({
      title: "Chunk Copied",
      description: `"${chunk.name}" has been copied to clipboard`
    });
  };

  const addTag = () => {
    if (newTag.trim() && !chunkForm.tags.includes(newTag.trim())) {
      setChunkForm(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setChunkForm(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const filteredChunks = chunks.filter(chunk => {
    if (filterPurpose === 'all') return true;
    return chunk.purpose === filterPurpose;
  });

  const getPurposeStyle = (purpose: string) => {
    return purposeOptions.find(opt => opt.value === purpose)?.color || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Value Proposition Header */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Scissors className="h-5 w-5 text-blue-600" />
            Content Chunking & Reusability
          </CardTitle>
          <CardDescription className="text-blue-700">
            💡 <strong>Why use this:</strong> Save time by creating reusable content pieces. Turn your best-performing transcript sections into templates you can use in future scripts.
          </CardDescription>
          <div className="flex gap-2 mt-3">
            <Badge variant="secondary" className="text-xs">
              ⚡ Speed up script writing
            </Badge>
            <Badge variant="secondary" className="text-xs">
              🎯 Reuse proven content
            </Badge>
            <Badge variant="secondary" className="text-xs">
              📚 Build content library
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Your Content Chunks ({chunks.length})</CardTitle>
              <CardDescription>
                Reusable content pieces ready for your next script
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button onClick={() => setIsCreatingChunk(true)} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                Create New Chunk
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Method 1: Text Selection from Script */}
          {selectedScript && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-blue-600" />
                <Label className="font-medium">Method 1: Select Text from Script</Label>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <Label className="text-sm font-medium mb-2 block">
                  Select text below to create a chunk:
                </Label>
                <Textarea
                  ref={textareaRef}
                  value={selectedScript.transcript}
                  readOnly
                  onMouseUp={handleTextSelection}
                  className="min-h-[200px] cursor-text"
                  placeholder="Script transcript will appear here..."
                />
                <p className="text-xs text-muted-foreground mt-2">
                  💡 Tip: Highlight any text above and it will automatically start creating a new chunk
                </p>
              </div>
            </div>
          )}

          {/* Method 2: Manual Entry */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Edit className="h-4 w-4 text-green-600" />
              <Label className="font-medium">Method 2: Manual Entry</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" 
                    onClick={() => {
                      setChunkForm(prev => ({ ...prev, purpose: 'opening-hook' }));
                      setIsCreatingChunk(true);
                    }}>
                <div className="text-center">
                  <Target className="h-8 w-8 text-red-600 mx-auto mb-2" />
                  <h4 className="font-medium">Opening Hook</h4>
                  <p className="text-xs text-muted-foreground">Attention-grabbing openers</p>
                </div>
              </Card>
              <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => {
                      setChunkForm(prev => ({ ...prev, purpose: 'call-to-action' }));
                      setIsCreatingChunk(true);
                    }}>
                <div className="text-center">
                  <Zap className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h4 className="font-medium">Call-to-Action</h4>
                  <p className="text-xs text-muted-foreground">Engagement prompts</p>
                </div>
              </Card>
              <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => {
                      setChunkForm(prev => ({ ...prev, purpose: 'information-drop' }));
                      setIsCreatingChunk(true);
                    }}>
                <div className="text-center">
                  <BookOpen className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <h4 className="font-medium">Info Drop</h4>
                  <p className="text-xs text-muted-foreground">Key information blocks</p>
                </div>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chunk Creation Form */}
      {isCreatingChunk && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-lg text-green-800">Create New Chunk</CardTitle>
            <CardDescription className="text-green-700">
              {selectedText ? 'Selected text loaded - just add details!' : 'Fill in the details for your new content chunk'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="chunkName">Chunk Name *</Label>
                <Input
                  id="chunkName"
                  placeholder="e.g., Urgent SSA Update Hook"
                  value={chunkForm.name}
                  onChange={(e) => setChunkForm(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="chunkPurpose">Purpose</Label>
                <Select
                  value={chunkForm.purpose}
                  onValueChange={(value: Chunk['purpose']) => setChunkForm(prev => ({ ...prev, purpose: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {purposeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="chunkContent">Content *</Label>
              <Textarea
                id="chunkContent"
                placeholder="Enter the actual content text..."
                value={chunkForm.content}
                onChange={(e) => setChunkForm(prev => ({ ...prev, content: e.target.value }))}
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="chunkDescription">Description (Optional)</Label>
              <Input
                id="chunkDescription"
                placeholder="Brief description of when to use this chunk..."
                value={chunkForm.description}
                onChange={(e) => setChunkForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Tags</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Add tag..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addTag()}
                />
                <Button onClick={addTag} variant="outline" size="sm">
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                {chunkForm.tags.map(tag => (
                  <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                    {tag} ×
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              <Button onClick={handleCreateChunk} className="bg-green-600 hover:bg-green-700">
                <Save className="h-4 w-4 mr-2" />
                Save Chunk
              </Button>
              <Button onClick={() => setIsCreatingChunk(false)} variant="outline">
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chunk Library */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Chunk Library</CardTitle>
            <Select value={filterPurpose} onValueChange={setFilterPurpose}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Purposes</SelectItem>
                {purposeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredChunks.map(chunk => {
              const purposeOption = purposeOptions.find(opt => opt.value === chunk.purpose);
              const IconComponent = purposeOption?.icon || Lightbulb;

              return (
                <Card key={chunk.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4 text-muted-foreground" />
                        <CardTitle className="text-base">{chunk.name}</CardTitle>
                      </div>
                      <div className="flex gap-1">
                        <Button onClick={() => handleCopyChunk(chunk)} variant="ghost" size="sm">
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button onClick={() => handleDeleteChunk(chunk.id)} variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <Badge className={`w-fit ${getPurposeStyle(chunk.purpose)}`}>
                      {purposeOption?.label}
                    </Badge>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm mb-3 line-clamp-3">{chunk.content}</p>
                    {chunk.description && (
                      <p className="text-xs text-muted-foreground mb-2">{chunk.description}</p>
                    )}
                    <div className="flex flex-wrap gap-1">
                      {chunk.tags.map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {filteredChunks.length === 0 && (
            <div className="text-center py-12">
              <Scissors className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Chunks Yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first content chunk to start building your reusable library
              </p>
              <Button onClick={() => setIsCreatingChunk(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create First Chunk
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ChunkingModuleRedesigned;
