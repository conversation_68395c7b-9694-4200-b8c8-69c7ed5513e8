/**
 * Financial Analysis Integration Service
 * 
 * This service integrates the NLP-based financial analysis with the existing system,
 * providing a seamless transition to the more accurate analysis method.
 */

import { analyzeTranscriptForFinancialBenefits, FinancialAnalysisResult, FinancialAmount } from './financial-analysis';
import { analyzeTranscriptWithNLP, determineFinancialCategory } from './nlp-financial-analysis';

/**
 * Enhanced financial analysis that combines NLP-based detection with existing analysis
 * @param transcript The video transcript to analyze
 * @param title The video title
 * @param description The video description (optional)
 * @returns Enhanced financial analysis result
 */
export async function enhancedFinancialAnalysis(
  transcript: string,
  title: string,
  description?: string
): Promise<FinancialAnalysisResult> {
  try {
    // First, run the NLP-based analysis to get accurate financial amounts
    const nlpAmounts = analyzeTranscriptWithNLP(transcript, title, description);
    
    // Determine financial category and status using NLP context analysis
    const { category, status } = determineFinancialCategory(nlpAmounts, transcript);
    
    // If NLP analysis found valid amounts, use them
    if (nlpAmounts && nlpAmounts.length > 0) {
      // Run the existing analysis to get other metadata (timestamps, etc.)
      const legacyAnalysis = await analyzeTranscriptForFinancialBenefits(transcript, title, description);
      
      // Combine the results, prioritizing NLP-detected amounts
      return {
        ...legacyAnalysis,
        allAmounts: nlpAmounts,
        amount: nlpAmounts[0]?.formatted || '',
        category,
        status,
        // Recalculate score based on NLP amounts
        score: calculateScoreFromNLPAmounts(nlpAmounts, category, status)
      };
    }
    
    // Fallback to legacy analysis if NLP didn't find any amounts
    return await analyzeTranscriptForFinancialBenefits(transcript, title, description);
  } catch (error) {
    console.error('Enhanced Financial Analysis Error:', error);
    // Fallback to legacy analysis in case of errors
    return await analyzeTranscriptForFinancialBenefits(transcript, title, description);
  }
}

/**
 * Calculate financial score based on NLP-detected amounts
 */
function calculateScoreFromNLPAmounts(
  amounts: FinancialAmount[],
  category: string,
  status: string
): number {
  if (!amounts || amounts.length === 0) {
    return 0;
  }
  
  // Base score from category
  let score = 0;
  switch (category) {
    case 'urgent':
      score = 80;
      break;
    case 'anticipated':
      score = 50;
      break;
    case 'proposed':
      score = 30;
      break;
    case 'doubtful':
      score = 15;
      break;
    default:
      return 0;
  }
  
  // Adjust for status
  switch (status) {
    case 'confirmed':
      score += 15;
      break;
    case 'approved':
      score += 10;
      break;
    case 'proposed':
      score += 5;
      break;
    case 'rumored':
      score -= 10;
      break;
  }
  
  // Primary amount bonus
  const primaryAmount = amounts[0];
  if (primaryAmount) {
    // Weight by confidence
    const confidenceWeight = primaryAmount.confidence;
    
    // Amount-based bonus
    let amountBonus = 0;
    const value = primaryAmount.value;
    
    if (value >= 1000000000) { // Billions - extremely rare for individual benefits
      amountBonus = 15;
    } else if (value >= 1000000) { // Millions - rare for individual benefits
      amountBonus = 16;
    } else if (value >= 10000) { // Tens of thousands
      amountBonus = 18;
    } else if (value >= 2000) { // Few thousand (typical stimulus range)
      amountBonus = 20; // Maximum bonus - very relevant for most people
    } else if (value >= 1000) { // One thousand
      amountBonus = 18;
    } else if (value >= 500) { // Hundreds
      amountBonus = 16;
    } else if (value >= 100) { // Small hundreds
      amountBonus = 12;
    } else if (value >= 50) { // Tens
      amountBonus = 8;
    } else if (value > 0) { // Any positive amount
      amountBonus = 5;
    }
    
    // Apply confidence weighting to amount bonus
    score += amountBonus * confidenceWeight;
  }
  
  // Multiple amounts bonus
  if (amounts.length > 1) {
    // Bonus for having multiple high-confidence amounts
    const highConfidenceAmounts = amounts.filter(a => a.confidence >= 0.7);
    if (highConfidenceAmounts.length > 1) {
      score += Math.min(10, highConfidenceAmounts.length * 2);
    }
    
    // Bonus for typical benefit ranges
    const typicalBenefitAmounts = amounts.filter(a => 
      a.value >= 500 && a.value <= 5000 && a.confidence >= 0.6
    );
    
    if (typicalBenefitAmounts.length > 0) {
      score += Math.min(5, typicalBenefitAmounts.length * 1.5);
    }
  }
  
  // Ensure score is between 0 and 100
  return Math.max(0, Math.min(100, Math.round(score)));
}
