#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to install and test the twscrape library for X.com (Twitter) scraping.
This script is used by the server to scrape tweets.
"""

import asyncio
import json
import sys
import os
import subprocess

def check_python_version():
    """Check if Python version is 3.10 or higher."""
    if sys.version_info < (3, 10):
        print("Error: Python 3.10 or higher is required for twscrape.")
        print(f"Current Python version: {sys.version}")
        return False
    print(f"Python version {sys.version} is compatible with twscrape.")
    return True

def install_twscrape():
    """Install the twscrape library if not already installed."""
    # First check Python version
    if not check_python_version():
        return False

    try:
        import twscrape
        print("twscrape is already installed.")
        return True
    except ImportError:
        print("Installing twscrape...")
        try:
            # Use the GitHub repository directly for the latest version
            subprocess.check_call([sys.executable, "-m", "pip", "install", "git+https://github.com/vladkens/twscrape.git"])
            print("twscrape installed successfully.")
            return True
        except Exception as e:
            print(f"Error installing twscrape: {e}")
            return False

async def test_twscrape(tweet_id="1664267318053179398"):
    """Test the twscrape library by fetching a tweet."""
    try:
        from twscrape import API
        from twscrape.logger import set_log_level

        # Set log level to ERROR to reduce output
        set_log_level("ERROR")

        # Initialize API
        api = API()

        # Try to get tweet details
        print(f"Fetching tweet {tweet_id}...")
        tweet = await api.tweet_details(tweet_id)

        # Convert to dict and print
        tweet_dict = tweet.dict()
        print(json.dumps(tweet_dict, indent=2))

        print("twscrape test successful!")
        return True
    except Exception as e:
        print(f"Error testing twscrape: {e}")
        return False

async def main():
    """Main function to install and test twscrape."""
    # Check Python version first
    if not check_python_version():
        print("Python version check failed. twscrape requires Python 3.10+.")
        print("The X.com scraper will fall back to alternative methods.")
        sys.exit(1)

    if not install_twscrape():
        print("Failed to install twscrape. Exiting.")
        print("The X.com scraper will fall back to alternative methods.")
        sys.exit(1)

    if not await test_twscrape():
        print("Failed to test twscrape. Exiting.")
        print("The X.com scraper will fall back to alternative methods.")
        sys.exit(1)

    print("twscrape is ready to use!")
    print("The X.com scraper will use twscrape as the primary method.")

if __name__ == "__main__":
    asyncio.run(main())
