import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useSettings } from "@/hooks/use-settings";
import { Sidebar } from "@/components/navigation/sidebar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Settings, Server, Network, RefreshCw, Power, Database, HardDrive } from "lucide-react";

interface AdminSettings {
  serverPort: number;
  localhostOnly: boolean;
  lastUpdated: Date;
}

export default function AdminSettingsPage() {
  const { settings } = useSettings();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [adminSettings, setAdminSettings] = useState<AdminSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isRestarting, setIsRestarting] = useState(false);
  const [isVacuuming, setIsVacuuming] = useState(false);
  const [vacuumResult, setVacuumResult] = useState<{ sizeBefore: number, sizeAfter: number, spaceReclaimed: number } | null>(null);
  const [serverPort, setServerPort] = useState<number>(5001);
  const [localhostOnly, setLocalhostOnly] = useState<boolean>(false);

  // Redirect if not admin
  useEffect(() => {
    if (settings && !settings.isAdmin) {
      setLocation("/");
    }
  }, [settings, setLocation]);

  // Fetch admin settings
  useEffect(() => {
    const fetchAdminSettings = async () => {
      try {
        const response = await fetch("/api/admin/settings");
        if (!response.ok) {
          throw new Error("Failed to fetch admin settings");
        }
        const data = await response.json();
        setAdminSettings(data);
        setServerPort(data.serverPort);
        setLocalhostOnly(data.localhostOnly);
      } catch (error) {
        console.error("Error fetching admin settings:", error);
        toast({
          title: "Error",
          description: "Failed to load admin settings",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    const fetchDatabaseStats = async () => {
      try {
        const response = await fetch("/api/admin/stats");
        if (!response.ok) {
          throw new Error("Failed to fetch database stats");
        }
        const data = await response.json();

        // If we have database size information, set it in the vacuum result
        if (data.summary && typeof data.summary.databaseSize === 'number') {
          setVacuumResult({
            sizeBefore: data.summary.databaseSize,
            sizeAfter: data.summary.databaseSize,
            spaceReclaimed: 0
          });
        }
      } catch (error) {
        console.error("Error fetching database stats:", error);
        // Don't show a toast for this error as it's not critical
      }
    };

    fetchAdminSettings();
    fetchDatabaseStats();
  }, [toast]);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const response = await fetch("/api/admin/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          serverPort,
          localhostOnly,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update settings");
      }

      const data = await response.json();
      setAdminSettings(data);

      toast({
        title: "Settings Saved",
        description: data.message || "Server settings updated successfully",
      });
    } catch (error) {
      console.error("Error saving admin settings:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Database vacuum handler
  const handleVacuumDatabase = async () => {
    // Confirm vacuum operation
    if (!window.confirm("Are you sure you want to vacuum the database? This operation may take some time for large databases.")) {
      return;
    }

    // Set loading state
    setIsVacuuming(true);

    try {
      // Make the API call
      const response = await fetch("/api/admin/vacuum-database", {
        method: "POST",
        headers: { "Content-Type": "application/json" }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to vacuum database");
      }

      const data = await response.json();

      // Update vacuum result state
      setVacuumResult({
        sizeBefore: data.sizeBefore,
        sizeAfter: data.sizeAfter,
        spaceReclaimed: data.spaceReclaimed
      });

      // Show success message
      toast({
        title: "Database Vacuum Complete",
        description: data.message || `Database vacuum completed successfully. Space reclaimed: ${data.spaceReclaimed} MB`
      });
    } catch (error) {
      // Show error message
      console.error("Vacuum error:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to vacuum database",
        variant: "destructive"
      });
    } finally {
      // Reset loading state
      setIsVacuuming(false);
    }
  };

  // Simple restart handler
  const handleRestart = () => {
    // Confirm restart
    if (!window.confirm("Are you sure you want to restart the application?")) {
      return;
    }

    // Set loading state
    setIsRestarting(true);

    // Make the API call
    fetch("/api/admin/restart", {
      method: "POST",
      headers: { "Content-Type": "application/json" }
    })
    .then(response => response.json())
    .then(data => {
      // Show success message
      toast({
        title: "Restarting Application",
        description: "The server is restarting. The page will refresh in 5 seconds."
      });

      // Refresh the page after 5 seconds
      setTimeout(() => window.location.reload(), 5000);
    })
    .catch(error => {
      // Show error message
      console.error("Restart error:", error);
      toast({
        title: "Error",
        description: "Failed to restart the application",
        variant: "destructive"
      });

      // Reset loading state
      setIsRestarting(false);
    });
  };

  if (!settings || !settings.isAdmin) {
    return null;
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Toaster />
      <Sidebar />

      <main className="flex-1 overflow-auto">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <Settings className="h-6 w-6" />
              <h1 className="text-2xl font-bold">Admin Settings</h1>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="grid gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Server className="h-5 w-5" />
                    Server Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure server network settings. Changes require a server restart to take effect.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="serverPort">Server Port</Label>
                    <Input
                      id="serverPort"
                      type="number"
                      min="1024"
                      max="65535"
                      value={serverPort}
                      onChange={(e) => setServerPort(parseInt(e.target.value))}
                    />
                    <p className="text-sm text-muted-foreground">
                      Port number (1024-65535) the server will listen on
                    </p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="localhostOnly">Localhost Only</Label>
                      <p className="text-sm text-muted-foreground">
                        When enabled, the server will only be accessible from this computer
                      </p>
                    </div>
                    <Switch
                      id="localhostOnly"
                      checked={localhostOnly}
                      onCheckedChange={setLocalhostOnly}
                    />
                  </div>

                  {adminSettings && (
                    <div className="pt-2 border-t">
                      <p className="text-sm text-muted-foreground">
                        Last updated: {new Date(adminSettings.lastUpdated).toLocaleString()}
                      </p>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => setLocation("/settings")}>
                    Cancel
                  </Button>
                  <Button onClick={handleSave} disabled={isSaving}>
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save Changes"
                    )}
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Network className="h-5 w-5" />
                    Network Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Current Port:</span>
                      <span className="font-medium">{adminSettings?.serverPort || 5001}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Network Access:</span>
                      <span className="font-medium">
                        {adminSettings?.localhostOnly
                          ? "Disabled (localhost only)"
                          : "Enabled (accessible on local network)"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Local URL:</span>
                      <span className="font-medium">
                        http://localhost:{adminSettings?.serverPort || 5001}
                      </span>
                    </div>
                    {!adminSettings?.localhostOnly && (
                      <div className="flex justify-between">
                        <span>Network URL:</span>
                        <span className="font-medium">
                          http://[your-ip-address]:{adminSettings?.serverPort || 5001}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col gap-2">
                  <Button
                    onClick={handleRestart}
                    disabled={isRestarting}
                    className="w-full"
                    variant="outline"
                  >
                    {isRestarting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Restarting...
                      </>
                    ) : (
                      <>
                        <Power className="mr-2 h-4 w-4" />
                        Restart Application
                      </>
                    )}
                  </Button>

                  <form
                    method="post"
                    action="/api/admin/restart"
                    onSubmit={(e) => {
                      e.preventDefault();
                      if (window.confirm("Are you sure you want to restart the application?")) {
                        e.currentTarget.submit();
                      }
                    }}
                  >
                    <Button type="submit" className="w-full" variant="secondary">
                      Restart (Form Submit)
                    </Button>
                  </form>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Database Maintenance
                  </CardTitle>
                  <CardDescription>
                    Perform database maintenance operations to optimize performance and reclaim disk space.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <HardDrive className="h-4 w-4" />
                        <span>Database Size:</span>
                        <span className="font-medium">
                          {vacuumResult ? `${vacuumResult.sizeAfter} MB` : "Unknown"}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        When you delete data from the database, the space is not automatically reclaimed.
                        Use the Vacuum Database button to compact the database and reclaim unused space.
                      </p>
                    </div>

                    {vacuumResult && (
                      <div className="p-4 border rounded-md bg-muted">
                        <h4 className="font-medium mb-2">Last Vacuum Result:</h4>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <span>Size Before:</span>
                          <span>{vacuumResult.sizeBefore} MB</span>
                          <span>Size After:</span>
                          <span>{vacuumResult.sizeAfter} MB</span>
                          <span>Space Reclaimed:</span>
                          <span className="font-medium">{vacuumResult.spaceReclaimed} MB</span>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    onClick={handleVacuumDatabase}
                    disabled={isVacuuming}
                    className="w-full"
                    variant="outline"
                  >
                    {isVacuuming ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Vacuuming Database...
                      </>
                    ) : (
                      <>
                        <Database className="mr-2 h-4 w-4" />
                        Vacuum Database
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
