import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

/**
 * Migration to add financial benefit fields to the rss_feed_items table
 */
export async function runMigration() {
  console.log('Starting migration to add financial benefit fields to rss_feed_items table...');

  // Get the directory name in ESM
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);

  // Use an absolute path for the database file
  const dbPath = path.resolve(__dirname, '../../data.db');
  console.log('Using database at:', dbPath);

  const db = new Database(dbPath);

  try {
    // Check if the columns already exist
    const columnInfo = db.prepare(`PRAGMA table_info(rss_feed_items)`).all();
    const columnNames = columnInfo.map((col: any) => col.name);

    console.log('Current columns in rss_feed_items table:', columnNames);

    // Check for financial_benefit_amount column
    if (!columnNames.includes('financial_benefit_amount')) {
      console.log('Adding financial_benefit_amount column to rss_feed_items table');
      db.exec(`ALTER TABLE rss_feed_items ADD COLUMN financial_benefit_amount REAL DEFAULT 0`);
    } else {
      console.log('financial_benefit_amount column already exists');
    }

    // Check for financial_benefit_description column
    if (!columnNames.includes('financial_benefit_description')) {
      console.log('Adding financial_benefit_description column to rss_feed_items table');
      db.exec(`ALTER TABLE rss_feed_items ADD COLUMN financial_benefit_description TEXT`);
    } else {
      console.log('financial_benefit_description column already exists');
    }

    // Check for financial_benefit_type column
    if (!columnNames.includes('financial_benefit_type')) {
      console.log('Adding financial_benefit_type column to rss_feed_items table');
      db.exec(`ALTER TABLE rss_feed_items ADD COLUMN financial_benefit_type TEXT`);
    } else {
      console.log('financial_benefit_type column already exists');
    }

    // Verify the columns were added
    const updatedColumnInfo = db.prepare(`PRAGMA table_info(rss_feed_items)`).all();
    const updatedColumnNames = updatedColumnInfo.map((col: any) => col.name);

    console.log('Updated columns in rss_feed_items table:', updatedColumnNames);

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    db.close();
  }
}

// Run the migration
runMigration()
  .then(() => {
    console.log('Migration completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
