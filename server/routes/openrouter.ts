/**
 * OpenRouter Routes
 *
 * This file contains routes for interacting with the OpenRouter API service.
 */

import express from 'express';
import { getAvailableOpenRouterModels } from '../services/openrouter-service';
import { storage } from '../storage';
import { analyzeTranscriptWithOpenRouter, convertOpenRouterAnalysisToAppFormat } from '../services/openrouter-financial-analysis';
import { createFinancialAnalysisPrompt } from '../services/openrouter-financial-analysis';

const router = express.Router();

// Test OpenRouter connection
router.get('/test-connection', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    // Check if a specific model was requested for testing
    const modelToTest = req.query.model as string;

    console.log('🤖 OpenRouter API: Testing OpenRouter connection...');
    if (modelToTest) {
      console.log(`🤖 OpenRouter API: Testing specific model: ${modelToTest}`);
    }

    try {
      // First test basic connection by getting available models
      const models = await getAvailableOpenRouterModels();
      console.log(`🤖 OpenRouter API: Connection successful, found ${models.length} models`);

      // If a specific model was requested, test it with a simple prompt
      if (modelToTest) {
        try {
          // Create a simple test prompt
          const testPrompt = "Please respond with 'OK' if you can process this message.";
          console.log(`🤖 OpenRouter API: Testing model ${modelToTest} with a simple prompt`);

          // Set a timeout for the model test
          const testStartTime = Date.now();
          const testTimeout = 15000; // 15 seconds timeout for model test

          // Use a Promise with timeout to test the model
          const modelTestPromise = new Promise(async (resolve, reject) => {
            try {
              // Try to generate a response with the model
              const response = await generateOpenRouterResponse(modelToTest, testPrompt);
              resolve(response);
            } catch (modelError) {
              reject(modelError);
            }
          });

          // Create a timeout promise
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
              reject(new Error(`Test timed out after ${testTimeout/1000} seconds`));
            }, testTimeout);
          });

          // Race the model test against the timeout
          const response = await Promise.race([modelTestPromise, timeoutPromise]);
          const testEndTime = Date.now();

          console.log(`🤖 OpenRouter API: Model ${modelToTest} test completed in ${testEndTime - testStartTime}ms`);
          console.log(`🤖 OpenRouter API: Model response: ${response}`);

          res.json({
            success: true,
            message: `Connected to OpenRouter API. Found ${models.length} models. Model ${modelToTest} test successful.`,
            modelTested: modelToTest,
            modelTestSuccessful: true,
            testDuration: testEndTime - testStartTime
          });
        } catch (modelTestError) {
          console.error(`🤖 OpenRouter API: Error testing model ${modelToTest}:`, modelTestError);

          // Check if this is a rate limit error
          const isRateLimitError = modelTestError.message &&
            modelTestError.message.includes('rate limit exceeded');

          // Check if this is a "No response choices" error which indicates model availability issues
          const isModelAvailabilityError = modelTestError.message &&
            modelTestError.message.includes('No response choices returned from OpenRouter API');

          res.json({
            success: true, // Still return success for the connection
            message: isRateLimitError
              ? `Connected to OpenRouter API. Found ${models.length} models. However, your OpenRouter account has reached its rate limit. Please try again later or upgrade your OpenRouter account.`
              : isModelAvailabilityError
                ? `Connected to OpenRouter API. Found ${models.length} models. The model "${modelToTest}" appears to be unavailable or has issues. Please try a different model.`
                : `Connected to OpenRouter API. Found ${models.length} models. But model ${modelToTest} test failed: ${modelTestError.message}`,
            modelTested: modelToTest,
            modelTestSuccessful: false,
            modelTestError: modelTestError.message || "Unknown error",
            isModelAvailabilityError: isModelAvailabilityError,
            isRateLimitError: isRateLimitError
          });
        }
      } else {
        // No specific model to test, just return success for the connection
        res.json({ success: true, message: `Connected to OpenRouter API. Found ${models.length} models.` });
      }
    } catch (connectionError) {
      console.error('🤖 OpenRouter API: Error connecting to OpenRouter API:', connectionError);
      res.status(500).json({
        success: false,
        message: "Failed to connect to OpenRouter API",
        error: connectionError.message || "Unknown error"
      });
    }
  } catch (error) {
    console.error('🤖 OpenRouter API: Unexpected error in test connection endpoint:', error);
    res.status(500).json({
      success: false,
      message: "Failed to test OpenRouter connection",
      error: error.message || "Unknown error"
    });
  }
});

// Get available OpenRouter models
router.get('/models', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    console.log('🤖 OpenRouter API: Fetching available models...');
    try {
      const models = await getAvailableOpenRouterModels();
      console.log(`🤖 OpenRouter API: Returning ${models.length} available models to client`);
      res.json(models);
    } catch (modelError) {
      console.error('🤖 OpenRouter API: Error fetching OpenRouter models:', modelError);
      res.status(500).json({
        message: "Failed to fetch OpenRouter models",
        error: modelError.message || "Unknown error"
      });
    }
  } catch (error) {
    console.error('🤖 OpenRouter API: Unexpected error in OpenRouter models endpoint:', error);
    res.status(500).json({
      message: "Failed to fetch OpenRouter models",
      error: error.message || "Unknown error"
    });
  }
});

// Analyze a video transcript using OpenRouter API
router.get('/analyze-youtube-video/:videoId', async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.sendStatus(401);
  }

  try {
    const { videoId } = req.params;
    console.log(`🤖 OpenRouter API: Analyzing YouTube video ${videoId} with OpenRouter API`);

    // Get the video details
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      console.error(`🤖 OpenRouter API: Video ${videoId} not found`);
      return res.status(404).json({ message: 'Video not found' });
    }

    if (!video.hasTranscription) {
      console.error(`🤖 OpenRouter API: Video ${videoId} has no transcription`);
      return res.status(400).json({ message: 'Video has no transcription. Please fetch the transcription first.' });
    }

    // Get the transcription from the video object
    const transcription = video.transcription;
    if (!transcription) {
      console.error(`🤖 OpenRouter API: Transcription for video ${videoId} not found`);
      return res.status(404).json({ message: 'Transcription not found' });
    }

    // Get the model from query parameters or settings
    let currentOpenRouterModel = 'google/gemini-2.0-flash-exp:free'; // Default model
    if (req.query.model) {
      currentOpenRouterModel = req.query.model as string;
      console.log(`🤖 OpenRouter API: Using model from query parameter: ${currentOpenRouterModel}`);
    } else {
      // Get the user's selected model from settings
      const settings = await storage.getSettings(req.user.id);
      if (settings && settings.openRouterModel) {
        currentOpenRouterModel = settings.openRouterModel;
        console.log(`🤖 OpenRouter API: Using model from user settings: ${currentOpenRouterModel}`);
      } else {
        console.log(`🤖 OpenRouter API: No model specified, using default: ${currentOpenRouterModel}`);
      }
    }

    // Get the custom prompt
    let customPrompt = null;
    const settings = await storage.getSettings(req.user.id);

    // If user has a selected prompt ID, use that prompt
    if (settings?.selectedPromptId) {
      try {
        const selectedPrompt = await storage.getOpenRouterPrompt(settings.selectedPromptId);
        if (selectedPrompt) {
          console.log(`🤖 OpenRouter API: Using selected prompt for analysis: ${selectedPrompt.name}`);
          customPrompt = selectedPrompt.promptText;
        }
      } catch (error) {
        console.error('🤖 OpenRouter API: Error getting selected prompt for analysis:', error);
      }
    } else if (settings?.openRouterAnalysisPrompt) {
      console.log(`🤖 OpenRouter API: Using custom prompt from settings for analysis`);
      customPrompt = settings.openRouterAnalysisPrompt;
    }

    // If no prompt is available, return an error
    if (!customPrompt) {
      return res.status(400).json({
        message: 'No prompt available. Please select a prompt in the settings.'
      });
    }

    // Analyze the transcription using OpenRouter API
    console.log(`🤖 OpenRouter API: Analyzing transcription for video ${videoId} with OpenRouter model: ${currentOpenRouterModel}`);
    try {
      // Get the published date and view count
      const publishedAt = video.publishedAt ? new Date(video.publishedAt) : undefined;
      const viewCount = video.viewCount || undefined;

      // Pass the model and custom prompt for analysis
      const openRouterAnalysis = await analyzeTranscriptWithOpenRouter(
        req.user.id,
        transcription,
        video.title,
        video.description,
        publishedAt,
        viewCount,
        customPrompt, // Pass the custom prompt
        currentOpenRouterModel // Pass the model to use
      );

      // Double-check that the model was set correctly
      if (openRouterAnalysis.modelUsed !== currentOpenRouterModel) {
        console.log(`🤖 OpenRouter API: Model mismatch! Expected ${currentOpenRouterModel}, got ${openRouterAnalysis.modelUsed}. Fixing...`);
        openRouterAnalysis.modelUsed = currentOpenRouterModel;
      }

      // Convert the analysis to the app format
      const appAnalysis = convertOpenRouterAnalysisToAppFormat(openRouterAnalysis);

      // Use the raw data directly from the analysis
      const completeRawData = openRouterAnalysis.rawData;

      // Extract benefit amounts from the raw data if needed
      if (!openRouterAnalysis.extractedInfo?.benefitAmounts || openRouterAnalysis.extractedInfo.benefitAmounts.length === 0) {
        console.log('🤖 OpenRouter API: No benefit amounts found in extractedInfo, trying to extract from raw data');
        try {
          // Parse the raw data
          const parsedRawData = JSON.parse(completeRawData);

          // Try to extract from benefitDescription
          if (parsedRawData.benefitDescription) {
            const matches = parsedRawData.benefitDescription.match(/\$\d+(?:,\d+)*(?:\.\d+)?/g);
            if (matches && matches.length > 0) {
              console.log('🤖 OpenRouter API: Extracted benefit amounts from raw data:', matches);
              if (!openRouterAnalysis.extractedInfo) {
                openRouterAnalysis.extractedInfo = {
                  benefitAmounts: matches,
                  expectedArrivalDate: '',
                  eligiblePeople: '',
                  proofOrSource: '',
                  actionsToClaim: ''
                };
              } else {
                openRouterAnalysis.extractedInfo.benefitAmounts = matches;
              }
            }
          }
        } catch (error) {
          console.error('🤖 OpenRouter API: Error extracting benefit amounts from raw data:', error);
        }
      }

      // Log the benefit amounts before storing
      console.log('🤖 OpenRouter API: Benefit amounts to store:', openRouterAnalysis.extractedInfo?.benefitAmounts);

      // Prepare benefit amounts for storage
      const benefitAmounts = openRouterAnalysis.extractedInfo?.benefitAmounts || [];
      const benefitAmountsJson = JSON.stringify(benefitAmounts);

      // Update the video with the analysis results - store only in OpenRouter fields
      await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
        // Basic financial fields
        financialScore: openRouterAnalysis.score,
        financialCategory: openRouterAnalysis.score >= 70 ? 'urgent' : openRouterAnalysis.score >= 50 ? 'anticipated' : 'doubtful',
        financialAmount: openRouterAnalysis.benefitDescription || '',
        financialTimeline: openRouterAnalysis.extractedInfo?.expectedArrivalDate || '',
        financialRecipients: openRouterAnalysis.extractedInfo?.eligiblePeople || '',
        financialSteps: openRouterAnalysis.extractedInfo?.actionsToClaim || '',
        financialViralPotential: '',
        financialSkepticism: '',
        financialAnalysis: openRouterAnalysis.benefitDescription || '',
        financialTimestamps: '',
        hasFinancialAnalysis: true,

        // Store raw data only in OpenRouter field
        openRouterRawData: completeRawData,

        // Store model info in both fields for consistency
        ollamaModelUsed: openRouterAnalysis.modelUsed,
        openRouterModelUsed: openRouterAnalysis.modelUsed,

        // Store prompt info in both fields for consistency
        ollamaPromptName: openRouterAnalysis.promptName || 'Default Prompt',
        openRouterPromptName: openRouterAnalysis.promptName || 'Default Prompt',
        ollamaPrompt: openRouterAnalysis.prompt || '',
        openRouterPrompt: openRouterAnalysis.prompt || '',
        ollamaSystemPrompt: openRouterAnalysis.systemPrompt || '',
        openRouterSystemPrompt: openRouterAnalysis.systemPrompt || '',

        // Store benefit amounts in both fields for consistency
        ollamaBenefitAmounts: benefitAmounts.length > 0 ? benefitAmounts : null,
        openRouterBenefitAmounts: benefitAmounts.length > 0 ? benefitAmounts : null,

        // Store other extracted info in both fields for consistency
        ollamaExpectedArrivalDate: openRouterAnalysis.extractedInfo?.expectedArrivalDate || '',
        openRouterExpectedArrivalDate: openRouterAnalysis.extractedInfo?.expectedArrivalDate || '',
        ollamaEligiblePeople: openRouterAnalysis.extractedInfo?.eligiblePeople || '',
        openRouterEligiblePeople: openRouterAnalysis.extractedInfo?.eligiblePeople || '',
        ollamaProofOrSource: openRouterAnalysis.extractedInfo?.proofOrSource || '',
        openRouterProofOrSource: openRouterAnalysis.extractedInfo?.proofOrSource || '',
        ollamaActionsToClaim: openRouterAnalysis.extractedInfo?.actionsToClaim || '',
        openRouterActionsToClaim: openRouterAnalysis.extractedInfo?.actionsToClaim || '',
        ollamaPriorityTag: openRouterAnalysis.priorityTag || '',
        openRouterPriorityTag: openRouterAnalysis.priorityTag || '',
        ollamaReasonForPriority: openRouterAnalysis.reasoning || '',
        openRouterReasonForPriority: openRouterAnalysis.reasoning || '',
        ollamaViralPotential: openRouterAnalysis.viralPotential || '',
        openRouterViralPotential: openRouterAnalysis.viralPotential || ''
      });

      // Get the updated video
      const updatedVideo = await storage.getYoutubeVideo(videoId);

      // Return the analysis results
      res.json({
        success: true,
        analysis: appAnalysis,
        video: updatedVideo,
        rawData: openRouterAnalysis.rawData
      });
    } catch (analysisError) {
      console.error('🤖 OpenRouter API: Error analyzing video with OpenRouter:', analysisError);
      res.status(500).json({
        message: `Failed to analyze video with OpenRouter: ${analysisError.message || 'Unknown error'}`,
        error: analysisError
      });
    }
  } catch (error) {
    console.error('🤖 OpenRouter API: Error analyzing video with OpenRouter:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ message: `Failed to analyze video with OpenRouter: ${errorMessage}` });
  }
});

// Analyze all videos in a channel using OpenRouter API
router.post('/analyze-channel/:channelId', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { channelId } = req.params;
    console.log(`🤖 OpenRouter API: Starting batch analysis for channel ${channelId}`);

    // Get all videos for the channel
    const videos = await storage.getYoutubeChannelVideos(parseInt(channelId));
    if (!videos || videos.length === 0) {
      return res.status(404).json({ message: 'No videos found for this channel' });
    }

    // Filter videos that have transcriptions but haven't been analyzed yet or have failed analysis
    const videosToAnalyze = videos.filter(video =>
      video.hasTranscription && (
        // Videos that have never been analyzed
        (!video.openRouterModelUsed && !video.lastAnalyzedAt) ||
        // Videos that have been marked as failed analysis (hasFinancialAnalysis=false)
        (video.hasFinancialAnalysis === false) ||
        // Videos that have been analyzed but don't have a model (likely failed)
        (video.lastAnalyzedAt && !video.openRouterModelUsed)
      )
    );

    if (videosToAnalyze.length === 0) {
      return res.json({
        message: 'No videos need analysis',
        totalVideos: videos.length,
        videosWithTranscription: videos.filter(v => v.hasTranscription).length,
        videosAlreadyAnalyzed: videos.filter(v => v.openRouterModelUsed && v.lastAnalyzedAt).length
      });
    }

    // Send immediate response to client
    res.json({
      message: `Starting batch analysis of ${videosToAnalyze.length} videos`,
      totalVideos: videos.length,
      videosToAnalyze: videosToAnalyze.length
    });

    // Continue processing in the background using the task queue
    console.log(`🤖 OpenRouter API: Queueing ${videosToAnalyze.length} videos for analysis using task queue`);

    // Import the task queue function
    const { queueOpenRouterFinancialAnalysis } = await import('../services/task-queue-openrouter');

    // Queue each video for analysis
    let queuedCount = 0;
    let skippedCount = 0;

    for (const video of videosToAnalyze) {
      try {
        // Get the transcription from the video object
        const transcription = video.transcription;
        if (!transcription) {
          console.error(`🤖 OpenRouter API: Transcription for video ${video.id} not found, skipping`);
          skippedCount++;
          continue;
        }

        // Queue the video for analysis with the task queue
        // This will handle retries automatically if the analysis fails
        const taskId = await queueOpenRouterFinancialAnalysis(
          video.id,
          transcription,
          video.title,
          video.description || '',
          req.user.id,
          'batch_analysis',  // Source of the analysis
          true  // Force reanalysis for videos that have failed before
        );

        if (taskId) {
          console.log(`🤖 OpenRouter API: Queued video ${video.id} for analysis with task ID ${taskId}`);
          queuedCount++;
        } else {
          console.log(`🤖 OpenRouter API: Skipped video ${video.id} (already has successful analysis)`);
          skippedCount++;
        }
      } catch (error) {
        console.error(`🤖 OpenRouter API: Error queueing video ${video.id} for analysis:`, error);
        skippedCount++;
      }
    }

    console.log(`🤖 OpenRouter API: Batch analysis queued. Queued ${queuedCount} videos, skipped ${skippedCount} videos.`);
  } catch (error) {
    console.error('🤖 OpenRouter API: Error in batch analysis:', error);
    // No response here since we already sent a response
  }
});

// Debug endpoint to see what would be sent to OpenRouter
router.get('/debug-prompt/:videoId', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const { videoId } = req.params;
    console.log(`🤖 OpenRouter API: Generating debug prompt for video ${videoId}`);

    // Get the video details
    const video = await storage.getYoutubeVideo(videoId);
    if (!video) {
      return res.status(404).json({ message: 'Video not found' });
    }

    // Get the transcription from the video object
    const transcription = video.transcription;
    if (!transcription) {
      return res.status(404).json({ message: 'Transcription not found' });
    }

    // Get user settings to check for custom prompt
    const settings = await storage.getSettings(req.user.id);
    let userCustomPrompt = null;
    let promptName = 'Custom Prompt';

    // If user has a selected prompt ID, use that prompt instead (preferred method)
    if (settings?.selectedPromptId) {
      try {
        const selectedPrompt = await storage.getOpenRouterPrompt(settings.selectedPromptId);
        if (selectedPrompt) {
          console.log(`🤖 OpenRouter API: Using selected prompt for debug: ${selectedPrompt.name}`);
          console.log(`🤖 OpenRouter API: Selected prompt text for debug (first 100 chars): ${selectedPrompt.promptText.substring(0, 100)}...`);
          userCustomPrompt = selectedPrompt.promptText;
          promptName = selectedPrompt.name;
        }
      } catch (error) {
        console.error('🤖 OpenRouter API: Error getting selected prompt for debug:', error);
      }
    }

    // If no selected prompt, try using the custom prompt from settings
    if (!userCustomPrompt && settings?.openRouterAnalysisPrompt) {
      console.log(`🤖 OpenRouter API: Using custom prompt from settings for debug`);
      userCustomPrompt = settings.openRouterAnalysisPrompt;
    }

    // If still no prompt, return an error
    if (!userCustomPrompt) {
      return res.status(400).json({
        message: 'No prompt available. Please select a prompt in the settings.'
      });
    }

    // Create the prompt
    const prompt = createFinancialAnalysisPrompt(
      transcription,
      video.title,
      video.description,
      userCustomPrompt
    );

    // Return the prompt and other debug info
    res.json({
      videoId,
      title: video.title,
      promptName,
      transcriptLength: transcription.length,
      promptLength: prompt.length,
      prompt: prompt,
      transcriptPlaceholderReplaced: !prompt.includes('[Paste transcript here]'),
      systemPrompt: 'Not using system prompt as requested by user'
    });
  } catch (error) {
    console.error('🤖 OpenRouter API: Error generating debug prompt:', error);
    res.status(500).json({ message: 'Failed to generate debug prompt', error: error.message });
  }
});

export default router;
