import { exec } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

async function applyAdminFix() {
  try {
    console.log('Applying admin functionality fix...');
    
    // 1. Run the migration script to add role column
    console.log('Running migration script...');
    await runCommand('npx ts-node migrate_admin.ts');
    
    // 2. Backup the original sqliteStorage.ts file
    const originalFile = path.resolve(__dirname, 'sqliteStorage.ts');
    const backupFile = path.resolve(__dirname, 'sqliteStorage.backup.ts');
    
    console.log('Backing up original sqliteStorage.ts file...');
    fs.copyFileSync(originalFile, backupFile);
    console.log(`Backup created at ${backupFile}`);
    
    // 3. Replace with the new implementation
    const newFile = path.resolve(__dirname, 'sqliteStorage.new.ts');
    
    console.log('Replacing sqliteStorage.ts with new implementation...');
    fs.copyFileSync(newFile, originalFile);
    console.log('Replacement complete');
    
    console.log('Admin functionality fix applied successfully!');
    console.log('Please restart the server for changes to take effect.');
  } catch (error) {
    console.error('Error applying admin fix:', error);
  }
}

function runCommand(command: string): Promise<void> {
  return new Promise((resolve, reject) => {
    exec(command, { cwd: __dirname }, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing command: ${error.message}`);
        console.error(`stderr: ${stderr}`);
        reject(error);
        return;
      }
      
      console.log(stdout);
      resolve();
    });
  });
}

applyAdminFix();
