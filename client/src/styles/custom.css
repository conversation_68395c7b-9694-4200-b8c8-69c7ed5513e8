/* Global styles for Switch component in dark mode */
:root {
  --switch-checked-bg: #2563eb; /* Default blue color */
  --switch-unchecked-bg: #e5e7eb; /* Default gray color */
  --switch-checked-thumb: white; /* Default thumb color */
  --switch-unchecked-thumb: white; /* Default thumb color */
}

.dark:root {
  --switch-checked-bg: white !important;
  --switch-unchecked-bg: #374151 !important;
  --switch-checked-thumb: black !important;
  --switch-unchecked-thumb: white !important;
}

/* Apply the custom colors to all switches */
button[role="switch"][data-state="checked"] {
  background-color: var(--switch-checked-bg) !important;
  border-color: var(--switch-checked-bg) !important;
}

button[role="switch"][data-state="unchecked"] {
  background-color: var(--switch-unchecked-bg) !important;
  border-color: var(--switch-unchecked-bg) !important;
}

button[role="switch"][data-state="checked"] > span {
  background-color: var(--switch-checked-thumb) !important;
}

button[role="switch"][data-state="unchecked"] > span {
  background-color: var(--switch-unchecked-thumb) !important;
}

/* Force the styles to be applied with !important */
button[role="switch"] {
  transition: background-color 0.2s ease-in-out !important;
}

button[role="switch"] > span {
  transition: background-color 0.2s ease-in-out, transform 0.2s ease-in-out !important;
}

/* Cast queue hover styles */
.dark .hover\:bg-accent\/50:hover {
  background-color: rgba(255, 255, 255, 0.15) !important;
  color: white !important;
}

.light .hover\:bg-accent\/50:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
  color: black !important;
}

/* Ensure text is visible in cast queue items */
.dark [class*="flex items-start gap-3 p-3 rounded-md"] {
  color: white !important;
}

.light [class*="flex items-start gap-3 p-3 rounded-md"] {
  color: black !important;
}

/* Force text color in cast queue */
.dark .text-foreground {
  color: white !important;
}

.light .text-foreground {
  color: black !important;
}

/* Ensure hover doesn't change text color to black */
.dark .hover\:text-foreground:hover {
  color: white !important;
}

.light .hover\:text-foreground:hover {
  color: black !important;
}

/* Fix for cast queue text color on hover */
.dark .group-hover\:text-primary {
  color: hsl(var(--primary)) !important;
}

.dark .dark\:group-hover\:text-primary-foreground {
  color: hsl(var(--primary-foreground)) !important;
}

.light .group-hover\:text-primary {
  color: hsl(var(--primary)) !important;
}

/* Ensure play overlay doesn't make text unreadable */
.dark .bg-black\/50 {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.light .bg-black\/50 {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* Custom scrollbar styles for channel performance table */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Highlight styles for HowToGuys channel */
.channel-highlight-howtoguys {
  background-color: rgba(34, 197, 94, 0.2) !important;
  border-left: 2px solid rgb(34, 197, 94) !important;
  font-weight: 500;
}

/* Sticky header for channel comparison table */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: rgba(30, 41, 59, 0.9);
  backdrop-filter: blur(4px);
}
