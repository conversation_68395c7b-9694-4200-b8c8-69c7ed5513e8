// worker-manager.ts
// This utility manages Web Workers and provides a clean interface for using them

// Define types for worker messages
type WorkerMessage = {
  type: string;
  payload?: any;
  error?: string;
  result?: any;
  groupId?: string | number;
};

// Class to manage the refresh worker
export class RefreshWorkerManager {
  private worker: Worker | null = null;
  private callbacks: Map<string, Function[]> = new Map();
  private isWorkerSupported: boolean = typeof Worker !== 'undefined';

  constructor() {
    this.initWorker();
  }

  // Initialize the worker
  private initWorker() {
    if (!this.isWorkerSupported) {
      console.warn('Web Workers are not supported in this browser. Falling back to main thread processing.');
      return;
    }

    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined' || typeof document === 'undefined') {
        console.warn('Not in browser environment. Skipping worker initialization.');
        this.isWorkerSupported = false;
        return;
      }

      // Create the worker with a try-catch for each step
      try {
        // First try to create the URL
        const workerUrl = new URL('../workers/refresh-worker.js', import.meta.url);

        // Then try to create the worker
        this.worker = new Worker(workerUrl, { type: 'module' });

        // Set up message handler
        this.worker.onmessage = this.handleWorkerMessage.bind(this);

        // Set up error handler
        this.worker.onerror = (error) => {
          console.error('Worker error:', error);
          this.triggerCallbacks('error', { error: error.message || 'Unknown worker error' });
        };

        console.log('Refresh worker initialized successfully');
      } catch (innerError) {
        console.error('Error during worker creation:', innerError);
        this.isWorkerSupported = false;

        // Try the simplified worker version as fallback
        try {
          const simplePath = new URL('../workers/refresh-worker-simple.js', import.meta.url);
          console.log('Trying simplified worker:', simplePath.toString());
          this.worker = new Worker(simplePath);

          // Set up message handler
          this.worker.onmessage = this.handleWorkerMessage.bind(this);

          // Set up error handler
          this.worker.onerror = (error) => {
            console.error('Worker error (simplified):', error);
            this.triggerCallbacks('error', { error: error.message || 'Unknown worker error' });
          };

          console.log('Refresh worker initialized with simplified version');
        } catch (fallbackError) {
          console.error('Simplified worker initialization failed:', fallbackError);

          // Try an absolute path as a last resort
          try {
            // Create a blob URL as a last resort
            const workerCode = `
              self.addEventListener('message', function(e) {
                self.postMessage({
                  type: 'error',
                  error: 'Using minimal worker implementation. Full functionality not available.'
                });
              });
            `;

            const blob = new Blob([workerCode], { type: 'application/javascript' });
            const blobUrl = URL.createObjectURL(blob);

            console.log('Trying blob URL worker as last resort');
            this.worker = new Worker(blobUrl);

            // Set up message handler
            this.worker.onmessage = this.handleWorkerMessage.bind(this);

            // Set up error handler
            this.worker.onerror = (error) => {
              console.error('Worker error (blob):', error);
              this.triggerCallbacks('error', { error: error.message || 'Unknown worker error' });
            };

            console.log('Minimal worker initialized with blob URL');
          } catch (lastError) {
            console.error('All worker initialization attempts failed:', lastError);
            this.isWorkerSupported = false;
          }
        }
      }
    } catch (error) {
      console.error('Failed to initialize worker:', error);
      this.isWorkerSupported = false;
    }
  }

  // Handle messages from the worker
  private handleWorkerMessage(event: MessageEvent) {
    const { type, ...data } = event.data as WorkerMessage;

    // Trigger callbacks for this message type
    this.triggerCallbacks(type, data);
  }

  // Register a callback for a specific message type
  public on(type: string, callback: Function) {
    if (!this.callbacks.has(type)) {
      this.callbacks.set(type, []);
    }

    this.callbacks.get(type)?.push(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.callbacks.get(type) || [];
      const index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    };
  }

  // Trigger all callbacks for a specific message type
  private triggerCallbacks(type: string, data: any) {
    const callbacks = this.callbacks.get(type) || [];
    callbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in callback for ${type}:`, error);
      }
    });
  }

  // Send a message to the worker
  public sendMessage(type: string, payload?: any) {
    if (!this.isWorkerSupported || !this.worker) {
      // If workers aren't supported, handle the operation on the main thread
      this.handleFallback(type, payload);
      return;
    }

    this.worker.postMessage({ type, payload });
  }

  // Get the request status from the server
  public async getRequestStatus(requestId: string) {
    try {
      const response = await fetch(`/api/requests/${requestId}`);
      if (!response.ok) {
        throw new Error(`Failed to get request status: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting request status:', error);
      throw error;
    }
  }

  // Fallback for browsers that don't support Web Workers
  private async handleFallback(type: string, payload?: any) {
    console.log(`Fallback: handling ${type} on main thread`);

    try {
      // Notify that we're starting the operation
      if (type === 'refresh-videos') {
        this.triggerCallbacks('refresh-started', {});
      } else if (type === 'refresh-group') {
        this.triggerCallbacks('group-refresh-started', { groupId: payload });
      }

      // Use setTimeout to make the operation non-blocking
      setTimeout(async () => {
        try {
          // Simulate the worker's behavior on the main thread
          let response;
          let result;

          switch (type) {
            case 'refresh-videos':
              try {
                response = await fetch('/api/videos/refresh', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify(payload || {})
                });

                if (!response.ok) {
                  throw new Error(`Refresh failed: ${response.status} ${response.statusText}`);
                }

                result = await response.json();
                this.triggerCallbacks('refresh-completed', { result });
              } catch (fetchError: any) {
                this.triggerCallbacks('error', {
                  error: fetchError.message || 'Error refreshing videos'
                });
              }
              break;

            case 'refresh-group':
              try {
                const groupId = payload;
                response = await fetch(`/api/keyword-groups/${groupId}/refresh`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) {
                  throw new Error(`Group refresh failed: ${response.status} ${response.statusText}`);
                }

                result = await response.json();
                this.triggerCallbacks('group-refresh-completed', { groupId, result });
              } catch (fetchError: any) {
                this.triggerCallbacks('error', {
                  error: fetchError.message || 'Error refreshing group',
                  groupId: payload
                });
              }
              break;

            default:
              console.warn(`Unknown fallback operation: ${type}`);
              this.triggerCallbacks('error', {
                error: `Unknown operation type: ${type}`
              });
          }
        } catch (innerError: any) {
          this.triggerCallbacks('error', {
            error: innerError.message || 'Unknown error in fallback handler'
          });
        }
      }, 10); // Small delay to ensure non-blocking behavior
    } catch (error: any) {
      this.triggerCallbacks('error', {
        error: error.message || 'Unknown error in fallback handler setup'
      });
    }

    // Return immediately to ensure non-blocking behavior
    return;
  }

  // Refresh all videos
  public refreshVideos(options = {}) {
    this.sendMessage('refresh-videos', options);
  }

  // Refresh a specific group's videos
  public refreshGroupVideos(groupId: string | number) {
    this.sendMessage('refresh-group', groupId);
  }

  // Terminate the worker
  public terminate() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }
}

// Create and export a singleton instance
export const refreshWorker = new RefreshWorkerManager();

// Export a hook for React components
export function useRefreshWorker() {
  return refreshWorker;
}
