import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, Plus, Trash2, RefreshCw, Edit, Check, X, ExternalLink, Eye, Radio, Clock, Activity, Zap, ClipboardCopy, ChevronDown, ChevronUp, ChevronRight, ArrowUp, ArrowDown } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Video } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { requestBatcher } from '@/services/request-batcher';
import { RealtimeAutoRefresh } from '@/components/realtime-auto-refresh';
import { useMutation } from '@tanstack/react-query';
import { Badge } from "@/components/ui/badge";
import { VphIndicator } from "@/components/video/vph-indicator";
import { ViewHistoryGraph } from "@/components/video/view-history-graph";
import { isLiveStream, isEndedLiveStream } from "@/lib/youtube-utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ChannelPerformanceDialog } from '@/components/youtube/channel-performance-dialog';
import { ChannelRankingGraph } from '@/components/youtube/channel-ranking-graph';
import { MarketShareChart } from '@/components/youtube/market-share-chart';
import { PeakTimingAnalysis } from '@/components/youtube/peak-timing-analysis';
import { YtrSearchUrlsService, type YtrSearchUrl } from '@/services/ytr-search-urls';

interface YtrTabProps {
  isActive: boolean;
  onRefresh?: (refreshTime: Date) => void;
  eliminatedVideos?: Set<string>;
  onVideoElimination?: (videoId: string) => void;
}

interface SearchUrl {
  id: string;
  name: string;
  url: string;
  resultsLimit: number;
}

export function YtrTab({ isActive, onRefresh, eliminatedVideos = new Set(), onVideoElimination }: YtrTabProps) {
  const { toast } = useToast();

  // State for search URLs (now loaded from database)
  const [searchUrls, setSearchUrls] = useState<SearchUrl[]>([]);
  const [isLoadingUrls, setIsLoadingUrls] = useState(true);
  const [hasMigrated, setHasMigrated] = useState(false);
  const [connectionHealth, setConnectionHealth] = useState<'healthy' | 'degraded' | 'offline'>('healthy');
  const [retryCount, setRetryCount] = useState(0);
  const [cacheStatus, setCacheStatus] = useState<'fresh' | 'stale' | 'empty'>('empty');
  const [performanceMetrics, setPerformanceMetrics] = useState<{
    lastLoadTime: number;
    averageLoadTime: number;
    successRate: number;
  }>({ lastLoadTime: 0, averageLoadTime: 0, successRate: 100 });

  // State for new URL input
  const [newUrlName, setNewUrlName] = useState('');
  const [newUrlValue, setNewUrlValue] = useState('');
  const [newUrlResultsLimit, setNewUrlResultsLimit] = useState(25);
  const [isAddingUrl, setIsAddingUrl] = useState(false);

  // State for editing URL
  const [editingUrlId, setEditingUrlId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [editingUrl, setEditingUrl] = useState('');
  const [editingResultsLimit, setEditingResultsLimit] = useState(25);

  // State for videos
  const [videos, setVideos] = useState<Video[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUrlId, setSelectedUrlId] = useState<string | null>(null);
  const [selectedVideos, setSelectedVideos] = useState<Video[]>([]);
  const [refreshingVideoId, setRefreshingVideoId] = useState<string | null>(null);
  const [cacheUpdateTimestamp, setCacheUpdateTimestamp] = useState<number>(0);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const [rankingUpdateTimestamp, setRankingUpdateTimestamp] = useState<number>(0);
  const [isUrlSwitching, setIsUrlSwitching] = useState(false);

  // Cache for videos by URL to prevent flickering
  const [videoCache, setVideoCache] = useState<Record<string, Video[]>>({});

  // State for Channel Performance Dialog
  const [showChannelPerformanceDialog, setShowChannelPerformanceDialog] = useState(false);

  // State for ranking history (last 12 hours)
  const [rankingHistory, setRankingHistory] = useState<{
    timestamp: number;
    ranking: number;
    channelName: string;
    urlName: string;
  }[]>([]);
  const [previousVphValues, setPreviousVphValues] = useState<Record<string, {
    previousValue: number;
    timestamp: number;
    previousTimestamp?: number;
    highestValue?: number;
    hasMomentum?: boolean;
    viewHistory?: { views: number; timestamp: number }[]; // Updated to store timestamps with view counts
  }>>({});

  // Sorting state
  const [sortField, setSortField] = useState<'youtube' | 'publishedAt' | 'viewCount'>('youtube'); // Default to YouTube order
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Define the channel name we want to track
  const TRACKED_CHANNEL_NAME = '@HowToGuys';
  const TRACKED_CHANNEL_NAME_ALT = 'How To Guys'; // Alternative format without @ and with spaces

  // Define all possible variations of the channel name
  const CHANNEL_NAME_VARIATIONS = [
    '@HowToGuys',
    'How To Guys',
    'HowToGuys',
    '@How To Guys',
    'How To Guys Channel',
    'HowToGuys Channel'
  ];

  // This is critical - we need to match the exact format used in the YouTube API response
  const EXACT_CHANNEL_MATCH = 'How To Guys';

  // Channel analysis state
  const [channelAnalysis, setChannelAnalysis] = useState<{
    yourChannelName: string;
    yourChannelVideos: Video[];
    yourChannelRankings: {videoId: string, position: number}[];
    competitorChannels: {
      name: string;
      count: number;
      totalViews?: number;
      avgViews?: number;
      avgVph?: number;
      viewsPerVideo?: number;
      bestRanking?: number;
    }[];
    allChannelMetrics?: {
      name: string;
      videoCount: number;
      totalViews: number;
      avgViews: number;
      avgVph: number;
      newestVideoDate: Date;
      oldestVideoDate: Date;
      viewsPerVideo: number;
      rankingPositions: number[];
    }[];
    // Per-URL metrics for @HowToGuys
    urlMetrics?: {
      [url: string]: {
        urlName: string;
        videoCount: number;
        totalViews: number;
        bestRanking: number;
        rankingPositions: number[];
        // Store video-specific rankings for each URL
        videoRankings?: {
          [videoId: string]: number;
        };
      }
    };
    yourChannelMetrics?: {
      name: string;
      videoCount: number;
      totalViews: number;
      avgViews: number;
      avgVph: number;
      newestVideoDate: Date;
      oldestVideoDate: Date;
      viewsPerVideo: number;
      rankingPositions: number[];
    };
    showAnalysis: boolean;
    showDetailedAnalysis?: boolean;
  }>({
    yourChannelName: TRACKED_CHANNEL_NAME,
    yourChannelVideos: [],
    yourChannelRankings: [],
    competitorChannels: [],
    urlMetrics: {},
    showAnalysis: true,
    showDetailedAnalysis: false
  });

  // Memoized stable ranking data to prevent flashing
  const stableRankingData = useMemo(() => {
    const rankingMap: Record<string, Array<{ url: string, rank: number, urlCode: string, urlName: string }>> = {};

    if (channelAnalysis.urlMetrics) {
      Object.entries(channelAnalysis.urlMetrics).forEach(([url, metrics]) => {
        if (metrics.videoRankings) {
          Object.entries(metrics.videoRankings).forEach(([videoId, rank]) => {
            if (rank <= 10) { // Only show top 10 rankings
              if (!rankingMap[videoId]) {
                rankingMap[videoId] = [];
              }

              // Generate stable URL code
              let urlCode = '';
              if (metrics.urlName.toLowerCase().includes('social security')) {
                urlCode = 'SS';
              } else if (metrics.urlName.toLowerCase().includes('stimulus')) {
                urlCode = 'ST';
              } else if (metrics.urlName.toLowerCase().includes('payment')) {
                urlCode = 'PAY';
              } else if (metrics.urlName.toLowerCase().includes('check')) {
                urlCode = 'CHK';
              } else if (metrics.urlName.toLowerCase().includes('increase')) {
                urlCode = 'INC';
              } else if (metrics.urlName.toLowerCase().includes('benefit')) {
                urlCode = 'BEN';
              } else {
                urlCode = metrics.urlName.split(' ')[0].substring(0, 2).toUpperCase();
              }

              rankingMap[videoId].push({
                url,
                rank,
                urlCode,
                urlName: metrics.urlName
              });
            }
          });
        }
      });

      // Sort rankings for each video by rank (best first)
      Object.keys(rankingMap).forEach(videoId => {
        rankingMap[videoId].sort((a, b) => a.rank - b.rank);
        // Limit to 5 badges to avoid cluttering
        rankingMap[videoId] = rankingMap[videoId].slice(0, 5);
      });
    }

    return rankingMap;
  }, [channelAnalysis.urlMetrics, rankingUpdateTimestamp]);

  // Connection health check function
  const checkConnectionHealth = useCallback(async () => {
    try {
      console.log('YTR Tab: Checking connection health...');
      const response = await apiRequest('GET', '/api/ping');
      if (response.ok) {
        setConnectionHealth('healthy');
        setRetryCount(0);
        console.log('YTR Tab: Connection healthy');
        return true;
      } else {
        setConnectionHealth('degraded');
        console.log('YTR Tab: Connection degraded');
        return false;
      }
    } catch (error) {
      console.error('YTR Tab: Connection check failed:', error);
      setConnectionHealth('offline');
      return false;
    }
  }, []);

  // Load search URLs from database and handle migration
  useEffect(() => {
    const loadSearchUrls = async () => {
      try {
        setIsLoadingUrls(true);

        // Check if there's localStorage data that needs migration
        if (!hasMigrated && YtrSearchUrlsService.hasLocalStorageData()) {
          console.log('YTR Tab: Found localStorage data, attempting migration...');

          toast({
            title: "Migrating Search URLs",
            description: "Moving your search URLs to permanent storage...",
            duration: 3000
          });

          try {
            const migrationResult = await YtrSearchUrlsService.migrateFromLocalStorage();

            if (migrationResult.success) {
              console.log('YTR Tab: Migration successful:', migrationResult.message);
              toast({
                title: "Migration Complete",
                description: migrationResult.message,
                duration: 3000
              });
            } else {
              console.error('YTR Tab: Migration failed:', migrationResult.message);
              toast({
                title: "Migration Failed",
                description: migrationResult.message,
                variant: "destructive",
                duration: 5000
              });
            }
          } catch (migrationError) {
            console.error('YTR Tab: Migration error:', migrationError);
            toast({
              title: "Migration Error",
              description: "Failed to migrate search URLs. Please try refreshing the page.",
              variant: "destructive",
              duration: 5000
            });
          }

          setHasMigrated(true);
        }

        // Load search URLs from database
        const urls = await YtrSearchUrlsService.getSearchUrls();
        console.log('YTR Tab: Loaded search URLs from database:', urls);

        // Convert to the expected format
        const formattedUrls: SearchUrl[] = urls.map(url => ({
          id: url.id,
          name: url.name,
          url: url.url,
          resultsLimit: url.resultsLimit || 25
        }));

        setSearchUrls(formattedUrls);
      } catch (error) {
        console.error('YTR Tab: Error loading search URLs:', error);

        // Check connection health first
        const isHealthy = await checkConnectionHealth();

        if (!isHealthy && retryCount < 3) {
          console.log(`YTR Tab: Connection unhealthy, retrying... (attempt ${retryCount + 1}/3)`);
          setRetryCount(prev => prev + 1);

          // Retry after exponential backoff
          const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 5000);
          setTimeout(() => {
            console.log('YTR Tab: Retrying URL load after delay...');
            setIsLoadingUrls(true); // This will trigger the useEffect again
          }, retryDelay);

          return; // Don't show error toast yet, we're retrying
        }

        // Check if it's an authentication error
        if (error instanceof Error && error.message.includes('401')) {
          console.log('YTR Tab: Authentication error detected, user may need to log in again');
          toast({
            title: "Authentication Required",
            description: "Please refresh the page and log in again",
            variant: "destructive",
            duration: 5000
          });
        } else if (connectionHealth === 'offline') {
          toast({
            title: "Connection Error",
            description: "Unable to connect to server. Please check your internet connection.",
            variant: "destructive",
            duration: 5000
          });
        } else {
          toast({
            title: "Error Loading URLs",
            description: `Failed to load search URLs from database${retryCount > 0 ? ` after ${retryCount} retries` : ''}`,
            variant: "destructive",
            duration: 5000
          });
        }

        // Fallback to localStorage if database fails
        try {
          const saved = localStorage.getItem('ytrSearchUrls');
          if (saved) {
            const fallbackUrls = JSON.parse(saved);
            setSearchUrls(fallbackUrls);
            console.log('YTR Tab: Using localStorage fallback');
          } else {
            // If no localStorage data either, create a default URL
            console.log('YTR Tab: No localStorage data, creating default URL');
            const defaultUrl: SearchUrl = {
              id: 'default-1',
              name: 'Default Search',
              url: 'https://www.youtube.com/results?search_query=trending',
              resultsLimit: 25
            };
            setSearchUrls([defaultUrl]);
          }
        } catch (fallbackError) {
          console.error('YTR Tab: Fallback to localStorage also failed:', fallbackError);
          // Last resort: create a basic default URL
          const defaultUrl: SearchUrl = {
            id: 'default-1',
            name: 'Default Search',
            url: 'https://www.youtube.com/results?search_query=trending',
            resultsLimit: 25
          };
          setSearchUrls([defaultUrl]);
        }
      } finally {
        setIsLoadingUrls(false);
      }
    };

    loadSearchUrls();
  }, [hasMigrated, toast, checkConnectionHealth, retryCount]);

  // Periodic health checks when tab is active
  useEffect(() => {
    if (!isActive) return;

    // Initial health check
    checkConnectionHealth();

    // Set up periodic health checks every 30 seconds
    const healthCheckInterval = setInterval(() => {
      if (isActive) {
        checkConnectionHealth();
      }
    }, 30000);

    return () => clearInterval(healthCheckInterval);
  }, [isActive, checkConnectionHealth]);

  // Monitor cache status
  useEffect(() => {
    if (selectedUrlId && searchUrls.length > 0) {
      const selectedUrl = searchUrls.find(url => url.id === selectedUrlId);
      if (selectedUrl) {
        const cachedVideos = localStorage.getItem(`ytr_videos_${selectedUrl.url}`);
        const cachedTimestamp = localStorage.getItem(`ytr_videos_timestamp_${selectedUrl.url}`);

        if (cachedVideos && cachedTimestamp) {
          const timestamp = parseInt(cachedTimestamp, 10);
          const now = Date.now();
          const cacheAge = now - timestamp;

          if (cacheAge < 300000) { // 5 minutes
            setCacheStatus('fresh');
          } else if (cacheAge < 1800000) { // 30 minutes
            setCacheStatus('stale');
          } else {
            setCacheStatus('empty');
          }
        } else {
          setCacheStatus('empty');
        }
      }
    }
  }, [selectedUrlId, searchUrls, videos]);

  // Select the first URL by default if none is selected
  useEffect(() => {
    if (searchUrls.length > 0 && !selectedUrlId) {
      setSelectedUrlId(searchUrls[0].id);
    }
  }, [searchUrls, selectedUrlId]);

  // Function to fetch videos from a search URL or multiple URLs (ALL option)
  const fetchVideosFromSearchUrl = useCallback(async (url: string | string[], forceRefresh: boolean = false) => {
    console.log('YTR Tab: Starting fetch for:', url, 'forceRefresh:', forceRefresh);

    // NEVER show loading state - keep current data visible while fetching in background
    // Only show loading if we have no data at all
    if (videos.length === 0) {
      setIsLoading(true);
    }

    try {
      // Handle the ALL option (multiple URLs)
      if (Array.isArray(url)) {
        console.log('YTR Tab: Fetching videos from ALL search URLs:', url);

        // Always fetch fresh data when explicitly requested (refresh button)
        // Only use cache as fallback if fetch fails
        console.log(`YTR Tab: ${forceRefresh ? 'Force refresh' : 'Background fetch'} for ALL URLs`);

        const combinedUrlKey = `combined_${url.sort().join('_')}`;



        // Make sure we have valid URLs to fetch
        const validUrls = url.filter(u => u && u.includes('youtube.com/results'));
        if (validUrls.length === 0) {
          console.error('YTR Tab: No valid YouTube search URLs provided');
          toast({
            title: "Error",
            description: "No valid YouTube search URLs provided",
            variant: "destructive"
          });
          setIsLoading(false);
          return [];
        }

        console.log(`YTR Tab: Sending ${validUrls.length} URLs to server for combined search`);

        // Get the maximum results limit from all URLs
        const maxResultsLimit = Math.max(...searchUrls.map(url => url.resultsLimit || 25));

        // Send all URLs to the server at once (ALWAYS force refresh for real-time data)
        const response = await apiRequest('POST', '/api/youtube-search', {
          body: JSON.stringify({
            searchUrl: validUrls,
            batched: true, // Flag to indicate this is a batched request
            forceRefresh: true, // ALWAYS force refresh for real-time data
            resultsLimit: maxResultsLimit // Use the highest limit for combined results
          })
        });

        console.log('YTR Tab: Response status for ALL URLs:', response.status, response.statusText);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('YTR Tab: Error response body:', errorText);
          throw new Error(`Failed to fetch videos: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('YTR Tab: Combined videos count:', data.videos ? data.videos.length : 0);

        if (data.videos && Array.isArray(data.videos)) {
          // Client-side deduplication as extra safety measure
          const uniqueVideos = data.videos.filter((video: Video, index: number, array: Video[]) =>
            array.findIndex(v => v.id === video.id) === index
          );

          console.log(`YTR Tab: Server returned ${data.videos.length} videos, after client deduplication: ${uniqueVideos.length}`);

          // Log channel distribution for debugging
          const channelCounts: Record<string, number> = {};
          uniqueVideos.forEach((video: Video) => {
            const channel = video.channelTitle || 'Unknown';
            channelCounts[channel] = (channelCounts[channel] || 0) + 1;
          });
          console.log('YTR Tab: Videos by channel:', channelCounts);

          // DON'T update videos automatically - let the caller decide when to update
          // setVideos(uniqueVideos);

          // Update video cache for instant switching
          setVideoCache(prev => ({ ...prev, all: uniqueVideos }));

          // Update last refresh time
          setLastRefreshTime(new Date());

          // Cache the deduplicated videos for fallback only
          localStorage.setItem(`ytr_videos_${combinedUrlKey}`, JSON.stringify(uniqueVideos));
          localStorage.setItem(`ytr_videos_timestamp_${combinedUrlKey}`, Date.now().toString());

          // Analyze channel performance with the combined URL key using deduplicated videos
          analyzeChannelPerformance(uniqueVideos, 'all');

          // Also analyze each URL individually to collect metrics for all URLs
          console.log('YTR Tab: Analyzing individual URLs for comprehensive metrics');
          setTimeout(async () => {
            for (const singleUrl of validUrls) {
              try {
                // Check if we have cached videos for this URL
                const urlCachedVideos = localStorage.getItem(`ytr_videos_${singleUrl}`);
                const urlCachedTimestamp = localStorage.getItem(`ytr_videos_timestamp_${singleUrl}`);

                if (urlCachedVideos && urlCachedTimestamp) {
                  const timestamp = parseInt(urlCachedTimestamp, 10);
                  const now = Date.now();
                  const cacheAge = now - timestamp;

                  // If cache is less than 30 minutes old, use it
                  if (cacheAge < 1800000) {
                    console.log(`YTR Tab: Using cached videos for individual URL analysis: ${singleUrl}`);
                    const parsedVideos = JSON.parse(urlCachedVideos);
                    analyzeChannelPerformance(parsedVideos, singleUrl);
                    continue;
                  }
                }

                // If no valid cache, fetch videos for this URL FOR ANALYSIS ONLY (don't update main video list)
                console.log(`YTR Tab: Fetching videos for individual URL analysis: ${singleUrl}`);
                const response = await apiRequest('POST', '/api/youtube-search', {
                  body: JSON.stringify({ searchUrl: singleUrl, forceRefresh })
                });

                if (response.ok) {
                  const data = await response.json();
                  if (data.videos && Array.isArray(data.videos)) {
                    // ONLY analyze for metrics - DO NOT update the main video list
                    analyzeChannelPerformance(data.videos, singleUrl);
                    console.log(`YTR Tab: Analyzed ${data.videos.length} videos for URL: ${singleUrl}`);
                  }
                }
              } catch (error) {
                console.error(`YTR Tab: Error analyzing URL ${singleUrl}:`, error);
              }
            }
          }, 100);

          // If onRefresh callback is provided, call it with the current time
          if (onRefresh) {
            onRefresh(new Date());
          }

          toast({
            title: "ALL Videos Refreshed",
            description: `Fetched ${uniqueVideos.length} unique videos from ${validUrls.length} YouTube searches`,
            duration: 3000
          });

          return uniqueVideos;
        } else {
          console.error('YTR Tab: Invalid videos data for ALL URLs:', data);
          toast({
            title: "Error",
            description: "Received invalid data from server",
            variant: "destructive"
          });
          return [];
        }
      }
      // Handle single URL case
      else {
        console.log('YTR Tab: Fetching videos from search URL:', url);

        // Always fetch fresh data - cache only used as fallback if fetch fails
        console.log(`YTR Tab: Fetching fresh data for ${url}`);

        // Keep reference to cached data as fallback
        const cachedVideos = localStorage.getItem(`ytr_videos_${url}`);
        const cachedTimestamp = localStorage.getItem(`ytr_videos_timestamp_${url}`);

        // Find the search URL object to get its results limit
        const searchUrlObj = searchUrls.find(searchUrl => searchUrl.url === url);
        const resultsLimit = searchUrlObj?.resultsLimit || 25;

        const response = await apiRequest('POST', '/api/youtube-search', {
          body: JSON.stringify({
            searchUrl: url,
            forceRefresh: true, // Always force refresh
            resultsLimit: resultsLimit // Include the results limit for this URL
          })
        });

        console.log('YTR Tab: Response status:', response.status, response.statusText);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('YTR Tab: Error response body:', errorText);
          throw new Error(`Failed to fetch videos: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('YTR Tab: Videos count:', data.videos ? data.videos.length : 0);

        if (data.videos && Array.isArray(data.videos)) {
          // Client-side deduplication as extra safety measure
          const uniqueVideos = data.videos.filter((video: Video, index: number, array: Video[]) =>
            array.findIndex(v => v.id === video.id) === index
          );

          console.log(`YTR Tab: Server returned ${data.videos.length} videos, after client deduplication: ${uniqueVideos.length}`);

          // DON'T update videos automatically - let the caller decide when to update
          // setVideos(uniqueVideos);

          // Update video cache for instant switching - find the URL ID
          const urlObj = searchUrls.find(searchUrl => searchUrl.url === url);
          if (urlObj) {
            setVideoCache(prev => ({ ...prev, [urlObj.id]: uniqueVideos }));
          }

          // Update last refresh time
          setLastRefreshTime(new Date());

          // Cache the deduplicated videos for fallback only
          localStorage.setItem(`ytr_videos_${url}`, JSON.stringify(uniqueVideos));
          localStorage.setItem(`ytr_videos_timestamp_${url}`, Date.now().toString());

          // Analyze channel performance with the current URL using deduplicated videos
          analyzeChannelPerformance(uniqueVideos, url);

          // If onRefresh callback is provided, call it with the current time
          if (onRefresh) {
            onRefresh(new Date());
          }

          toast({
            title: "Videos Refreshed",
            description: `Fetched ${uniqueVideos.length} unique videos from YouTube search`,
            duration: 3000
          });

          return uniqueVideos;
        } else {
          console.error('YTR Tab: Invalid videos data:', data);
          toast({
            title: "Error",
            description: "Received invalid data from server",
            variant: "destructive"
          });
          return [];
        }
      }
    } catch (error) {
      console.error('YTR Tab: Error fetching videos from search URL:', error);

      // Try to use cached data as fallback
      if (Array.isArray(url)) {
        const combinedUrlKey = `combined_${url.sort().join('_')}`;
        const cachedVideos = localStorage.getItem(`ytr_videos_${combinedUrlKey}`);
        if (cachedVideos) {
          console.log('YTR Tab: Using cached data as fallback for ALL URLs');
          const parsedVideos = JSON.parse(cachedVideos);
          const uniqueVideos = parsedVideos.filter((video: Video, index: number, array: Video[]) =>
            array.findIndex(v => v.id === video.id) === index
          );
          // Don't automatically update videos - let caller decide
          // setVideos(uniqueVideos);
          toast({
            title: "Using Cached Data",
            description: "Showing previous data due to network error",
            variant: "default"
          });
          return uniqueVideos;
        }
      } else {
        const cachedVideos = localStorage.getItem(`ytr_videos_${url}`);
        if (cachedVideos) {
          console.log('YTR Tab: Using cached data as fallback for single URL');
          const parsedVideos = JSON.parse(cachedVideos);
          const uniqueVideos = parsedVideos.filter((video: Video, index: number, array: Video[]) =>
            array.findIndex(v => v.id === video.id) === index
          );
          // Don't automatically update videos - let caller decide
          // setVideos(uniqueVideos);
          toast({
            title: "Using Cached Data",
            description: "Showing previous data due to network error",
            variant: "default"
          });
          return uniqueVideos;
        }
      }

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch videos",
        variant: "destructive"
      });
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [onRefresh, toast, searchUrls]);

  // Function to refresh a single video's view count
  const refreshSingleVideoViewCount = useCallback(async (videoId: string) => {
    setRefreshingVideoId(videoId);
    try {
      console.log(`YTR Tab: Starting refresh for video: ${videoId}`);

      // Find the current video to see its current view count
      const currentVideo = videos.find(v => v.id === videoId);
      const currentViewCount = currentVideo?.viewCount || 0;
      console.log(`YTR Tab: Current view count for ${videoId}: ${currentViewCount}`);

      // Use the YouTube utils function to get real-time view count
      console.log(`YTR Tab: Making API request to /api/youtube-utils/view-count`);
      const response = await apiRequest('POST', '/api/youtube-utils/view-count', { videoId });

      console.log(`YTR Tab: API response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`YTR Tab: API request failed: ${response.status} ${response.statusText}`);
        console.error(`YTR Tab: Error response: ${errorText}`);
        throw new Error(`Failed to fetch view count: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`YTR Tab: API response data:`, data);
      const newViewCount = data.viewCount;

      if (newViewCount && newViewCount > 0) {
        console.log(`YTR Tab: Got new view count for ${videoId}: ${currentViewCount} -> ${newViewCount}`);

        // Update the video in the current videos list
        console.log(`YTR Tab: Updating videos state for ${videoId}`);
        setVideos(prevVideos => {
          const updatedVideos = prevVideos.map(video =>
            video.id === videoId
              ? { ...video, viewCount: newViewCount }
              : video
          );
          console.log(`YTR Tab: Videos state updated, video ${videoId} now has view count:`,
            updatedVideos.find(v => v.id === videoId)?.viewCount);
          return updatedVideos;
        });

        // Update ALL cached data for this video across all URLs
        console.log(`YTR Tab: Updating all caches for video ${videoId} with new view count: ${newViewCount}`);

        // Update ALL individual URL caches
        searchUrls.forEach(searchUrl => {
          const cachedVideos = localStorage.getItem(`ytr_videos_${searchUrl.url}`);
          if (cachedVideos) {
            try {
              const parsedVideos = JSON.parse(cachedVideos);
              const videoExists = parsedVideos.some((video: Video) => video.id === videoId);

              if (videoExists) {
                const updatedVideos = parsedVideos.map((video: Video) =>
                  video.id === videoId
                    ? { ...video, viewCount: newViewCount }
                    : video
                );
                localStorage.setItem(`ytr_videos_${searchUrl.url}`, JSON.stringify(updatedVideos));
                console.log(`YTR Tab: Updated cache for URL: ${searchUrl.name}`);
              }
            } catch (error) {
              console.error(`YTR Tab: Error updating cache for URL ${searchUrl.name}:`, error);
            }
          }
        });

        // Update ALL URLs combined cache if it exists
        const allUrls = searchUrls.map(url => url.url);
        const combinedUrlKey = `combined_${allUrls.sort().join('_')}`;
        const cachedCombinedVideos = localStorage.getItem(`ytr_videos_${combinedUrlKey}`);
        if (cachedCombinedVideos) {
          try {
            const parsedVideos = JSON.parse(cachedCombinedVideos);
            const updatedVideos = parsedVideos.map((video: Video) =>
              video.id === videoId
                ? { ...video, viewCount: newViewCount }
                : video
            );
            localStorage.setItem(`ytr_videos_${combinedUrlKey}`, JSON.stringify(updatedVideos));
            console.log(`YTR Tab: Updated combined cache for ALL URLs`);
          } catch (error) {
            console.error(`YTR Tab: Error updating combined cache:`, error);
          }
        }

        // Force refresh the current view if user switches URLs
        // This ensures they see the updated data immediately
        console.log(`YTR Tab: View count update complete for video ${videoId}`);

        // Update the cache timestamp to force refresh when switching URLs
        setCacheUpdateTimestamp(Date.now());

        // Update the database with the new view count
        try {
          await apiRequest('POST', '/api/youtube-search/update-view-count', {
            body: JSON.stringify({ videoId, viewCount: newViewCount })
          });
          console.log(`YTR Tab: Updated database with new view count for ${videoId}`);
        } catch (error) {
          console.error(`YTR Tab: Error updating database view count for ${videoId}:`, error);
        }

        // Update channel analysis with the new view count
        analyzeChannelPerformance(videos.map(video =>
          video.id === videoId ? { ...video, viewCount: newViewCount } : video
        ), selectedUrlId === 'all' ? 'all' : searchUrls.find(url => url.id === selectedUrlId)?.url);

        // Force chart refresh by updating the cache timestamp
        setCacheUpdateTimestamp(Date.now());

        toast({
          title: "View Count Updated",
          description: `Updated to ${newViewCount.toLocaleString()} views`,
          duration: 2000
        });
      } else {
        throw new Error('Invalid view count received');
      }
    } catch (error) {
      console.error(`YTR Tab: Error refreshing view count for ${videoId}:`, error);
      toast({
        title: "Error",
        description: "Failed to refresh view count",
        variant: "destructive",
        duration: 2000
      });
    } finally {
      setRefreshingVideoId(null);
    }
  }, [selectedUrlId, searchUrls, toast]);

  // Function to refresh view counts for all videos using the same method as chart clicks
  const refreshAllViewCountsLikeChartClick = useCallback(async (videoList: Video[]) => {
    console.log(`🔄 YTR Tab: Starting view count refresh for ${videoList.length} videos using chart click method`);

    // Show toast notification
    toast({
      title: "Refreshing View Counts",
      description: `Updating view counts for ${videoList.length} videos...`,
      duration: 3000
    });

    let successfulUpdates = 0;

    // Process videos with staggered delays to avoid rate limiting
    for (let i = 0; i < videoList.length; i++) {
      const video = videoList[i];
      try {
        // Add delay between requests (200ms each)
        if (i > 0) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        console.log(`🔄 YTR Tab: Refreshing view count for video ${i + 1}/${videoList.length}: ${video.id}`);

        // Use the exact same method as chart click
        await refreshSingleVideoViewCount(video.id);
        successfulUpdates++;

        console.log(`✅ YTR Tab: Successfully updated view count for ${video.id} (${i + 1}/${videoList.length})`);
      } catch (error) {
        console.error(`❌ YTR Tab: Error refreshing view count for ${video.id}:`, error);
      }
    }

    console.log(`✅ YTR Tab: Completed view count refresh. Updated ${successfulUpdates} out of ${videoList.length} videos`);

    // Show completion toast
    toast({
      title: "View Counts Updated",
      description: `Successfully updated ${successfulUpdates} out of ${videoList.length} videos`,
      duration: 2000
    });

    return successfulUpdates;
  }, [refreshSingleVideoViewCount, toast]);

  // Function to format time difference
  const formatTimeAgo = useCallback((date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSeconds < 60) {
      return `${diffSeconds} second${diffSeconds !== 1 ? 's' : ''} ago`;
    } else if (diffMinutes < 60) {
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    }
  }, []);

  // Update the time display every minute
  useEffect(() => {
    if (!lastRefreshTime) return;

    const interval = setInterval(() => {
      // Force re-render to update the time display
      setLastRefreshTime(prev => prev);
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [lastRefreshTime]);

  // Function to handle URL button clicks with priority refresh
  const handleUrlButtonClick = useCallback(async (urlId: string) => {
    console.log('YTR Tab: URL button clicked:', urlId);

    // Prevent multiple simultaneous URL switches
    if (isUrlSwitching) {
      console.log('YTR Tab: URL switch already in progress, ignoring click');
      return;
    }

    setIsUrlSwitching(true);

    try {
      // 1. Set the selected URL immediately (this will update the UI tabs)
      setSelectedUrlId(urlId);

      // 2. KEEP CURRENT VIDEOS VISIBLE - don't change them until fresh data is ready
      console.log(`YTR Tab: Switching to ${urlId}, keeping current videos visible during load`);

      // 3. Start loading fresh data in background
      setIsLoading(true);

      // 4. Show toast notification for URL switch with auto-refresh
      const urlName = urlId === 'all' ? 'All URLs' : searchUrls.find(url => url.id === urlId)?.name || 'Unknown URL';
      toast({
        title: `Switching to ${urlName}`,
        description: "Auto-refreshing data for the selected tab",
        duration: 2000
      });

      // 4. Fetch fresh data based on URL type
      if (urlId === 'all') {
        console.log('YTR Tab: Priority refresh for ALL URLs');
        const allUrls = searchUrls.map(url => url.url);

        if (allUrls.length > 0) {
          // Fetch fresh data for ALL URLs
          console.log('YTR Tab: Fetching fresh data for ALL URLs (priority)');
          try {
            const freshVideos = await fetchVideosFromSearchUrl(allUrls, true);
            // Always update with fresh data if we got it
            if (freshVideos && freshVideos.length > 0) {
              console.log('YTR Tab: Updating videos with fresh data for ALL URLs');
              // Extra deduplication safety check before setting videos
              const finalUniqueVideos = freshVideos.filter((video: Video, index: number, array: Video[]) =>
                array.findIndex(v => v.id === video.id) === index
              );
              console.log(`YTR Tab: Final deduplication: ${freshVideos.length} -> ${finalUniqueVideos.length} videos`);
              setVideos(finalUniqueVideos);

              // Auto-refresh view counts for all videos using the same method as chart clicks
              console.log('🔄 YTR Tab: Auto-refreshing view counts for ALL URLs after URL switch');
              // Use setTimeout to ensure the videos state has been updated before refreshing view counts
              setTimeout(() => {
                console.log('🔄 YTR Tab: Starting chart-click-style refresh for ALL URLs');
                refreshAllViewCountsLikeChartClick(finalUniqueVideos);
              }, 500); // Small delay to let UI update

              // Clear URL metrics only after we have fresh data
              setChannelAnalysis(prev => ({
                ...prev,
                urlMetrics: {}
              }));
              // Force chart refresh
              setCacheUpdateTimestamp(Date.now());
              // Initialize view history for new videos
              updatePreviousVphValues(finalUniqueVideos, true);
            } else {
              console.log('YTR Tab: No fresh videos received for ALL URLs');
            }
            console.log('YTR Tab: Priority refresh completed for ALL URLs');
          } catch (error) {
            console.error('YTR Tab: Error in priority refresh for ALL URLs:', error);
            // Only show cached data if we have no current videos and fresh data failed
            const cacheKey = 'all';
            if (videos.length === 0 && videoCache[cacheKey] && videoCache[cacheKey].length > 0) {
              console.log('YTR Tab: Fresh data failed, showing cached data as fallback');
              setVideos(videoCache[cacheKey]);
            }
          }
        }
      } else {
        // Single URL case
        const selectedUrl = searchUrls.find(url => url.id === urlId);
        if (selectedUrl) {
          console.log('YTR Tab: Priority refresh for URL:', selectedUrl.name);

          // Fetch fresh data for this specific URL
          console.log('YTR Tab: Fetching fresh data for URL (priority):', selectedUrl.url);
          try {
            const freshVideos = await fetchVideosFromSearchUrl(selectedUrl.url, true);
            // Always update with fresh data if we got it
            if (freshVideos && freshVideos.length > 0) {
              console.log('YTR Tab: Updating videos with fresh data for URL:', selectedUrl.name);
              // Extra deduplication safety check before setting videos
              const finalUniqueVideos = freshVideos.filter((video: Video, index: number, array: Video[]) =>
                array.findIndex(v => v.id === video.id) === index
              );
              console.log(`YTR Tab: Final deduplication: ${freshVideos.length} -> ${finalUniqueVideos.length} videos`);
              setVideos(finalUniqueVideos);

              // Auto-refresh view counts for videos using the same method as chart clicks
              console.log('🔄 YTR Tab: Auto-refreshing view counts for SINGLE URL after URL switch');
              // Use setTimeout to ensure the videos state has been updated before refreshing view counts
              setTimeout(() => {
                console.log('🔄 YTR Tab: Starting chart-click-style refresh for SINGLE URL');
                refreshAllViewCountsLikeChartClick(finalUniqueVideos);
              }, 500); // Small delay to let UI update

              // Clear URL metrics only after we have fresh data
              setChannelAnalysis(prev => ({
                ...prev,
                urlMetrics: {}
              }));
              // Force chart refresh
              setCacheUpdateTimestamp(Date.now());
              // Initialize view history for new videos
              updatePreviousVphValues(finalUniqueVideos, true);
            } else {
              console.log('YTR Tab: No fresh videos received for URL:', selectedUrl.name);
            }
            console.log('YTR Tab: Priority refresh completed for URL:', selectedUrl.name);

            // Priority 2: Refresh all other feeds in background
            console.log('YTR Tab: Starting background refresh for all other URLs');
            const otherUrls = searchUrls.filter(url => url.id !== urlId);

            // Refresh other URLs in background with a small delay to not overwhelm the server
            setTimeout(async () => {
              for (const otherUrl of otherUrls) {
                try {
                  console.log('YTR Tab: Background refresh for URL:', otherUrl.name);
                  await fetchVideosFromSearchUrl(otherUrl.url, true);
                  // Small delay between background refreshes
                  await new Promise(resolve => setTimeout(resolve, 1000));
                } catch (error) {
                  console.error('YTR Tab: Error in background refresh for URL:', otherUrl.name, error);
                }
              }
              console.log('YTR Tab: Background refresh completed for all other URLs');
            }, 2000); // 2 second delay before starting background refresh

          } catch (error) {
            console.error('YTR Tab: Error in priority refresh for URL:', selectedUrl.name, error);
            // Only show cached data if we have no current videos and fresh data failed
            if (videos.length === 0 && videoCache[urlId] && videoCache[urlId].length > 0) {
              console.log('YTR Tab: Fresh data failed, showing cached data as fallback');
              setVideos(videoCache[urlId]);
            }
          }
        }
      }

    } catch (error) {
      console.error('YTR Tab: Error in URL switch:', error);
    } finally {
      // Reset the URL switching flag and loading state
      setIsUrlSwitching(false);
      setIsLoading(false);
    }
  }, [searchUrls, fetchVideosFromSearchUrl, refreshAllViewCountsLikeChartClick]);

  // Enhanced tab activation logic with retry mechanism
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`YTR Tab: useEffect triggered at ${timestamp} - isActive:`, isActive, 'searchUrls.length:', searchUrls.length, 'selectedUrlId:', selectedUrlId, 'videos.length:', videos.length, 'isUrlSwitching:', isUrlSwitching, 'isLoadingUrls:', isLoadingUrls);

    if (!isActive) {
      console.log('YTR Tab: Tab not active, skipping data load');
      return;
    }

    // If URLs are still loading, wait for them
    if (isLoadingUrls) {
      console.log('YTR Tab: URLs still loading, waiting...');
      return;
    }

    // If no URLs loaded, something went wrong - try to reload them
    if (searchUrls.length === 0) {
      console.log('YTR Tab: No URLs found, attempting to reload...');
      // Trigger a reload of URLs after a short delay
      const reloadTimer = setTimeout(() => {
        console.log('YTR Tab: Reloading URLs after delay...');
        // Try to reload URLs instead of full page reload
        setIsLoadingUrls(true);
        // The loadSearchUrls function will be called again due to the useEffect dependency
      }, 2000);

      return () => clearTimeout(reloadTimer);
    }

    // Auto-select first URL if none selected
    if (!selectedUrlId && searchUrls.length > 0) {
      console.log('YTR Tab: Auto-selecting first URL:', searchUrls[0].id);
      setSelectedUrlId(searchUrls[0].id);
      return;
    }

    // Load videos if we have everything we need
    if (selectedUrlId && !isUrlSwitching) {
      // Check if we already have videos for this URL in cache
      const cachedVideos = videoCache[selectedUrlId];
      if (cachedVideos && cachedVideos.length > 0 && videos.length === 0) {
        console.log('YTR Tab: Using cached videos for URL:', selectedUrlId, 'count:', cachedVideos.length);
        setVideos(cachedVideos);
        return;
      }

      // Load fresh data if no videos or cache is empty - but only on initial load
      if (videos.length === 0) {
        console.log('YTR Tab: Loading fresh data for URL:', selectedUrlId);
        // Call handleUrlButtonClick directly without including it in dependencies
        // This prevents the circular dependency issue
        const loadInitialData = async () => {
          if (isUrlSwitching) {
            console.log('YTR Tab: URL switch already in progress, skipping initial load');
            return;
          }

          setIsUrlSwitching(true);
          try {
            setSelectedUrlId(selectedUrlId);
            setIsLoading(true);

            if (selectedUrlId === 'all') {
              const allUrls = searchUrls.map(url => url.url);
              if (allUrls.length > 0) {
                const freshVideos = await fetchVideosFromSearchUrl(allUrls, false); // Don't force refresh on initial load
                if (freshVideos && freshVideos.length > 0) {
                  const finalUniqueVideos = freshVideos.filter((video: Video, index: number, array: Video[]) =>
                    array.findIndex(v => v.id === video.id) === index
                  );
                  setVideos(finalUniqueVideos);

                  // Auto-refresh view counts for initial load
                  console.log('🔄 YTR Tab: Auto-refreshing view counts for INITIAL LOAD (ALL URLs)');
                  // Use setTimeout to ensure the videos state has been updated before refreshing view counts
                  setTimeout(() => {
                    console.log('🔄 YTR Tab: Starting chart-click-style refresh for INITIAL LOAD (ALL URLs)');
                    refreshAllViewCountsLikeChartClick(finalUniqueVideos);
                  }, 1000); // Longer delay for initial load
                }
              }
            } else {
              const selectedUrl = searchUrls.find(url => url.id === selectedUrlId);
              if (selectedUrl) {
                const freshVideos = await fetchVideosFromSearchUrl(selectedUrl.url, false); // Don't force refresh on initial load
                if (freshVideos && freshVideos.length > 0) {
                  const finalUniqueVideos = freshVideos.filter((video: Video, index: number, array: Video[]) =>
                    array.findIndex(v => v.id === video.id) === index
                  );
                  setVideos(finalUniqueVideos);

                  // Auto-refresh view counts for initial load
                  console.log('🔄 YTR Tab: Auto-refreshing view counts for INITIAL LOAD (single URL)');
                  // Use setTimeout to ensure the videos state has been updated before refreshing view counts
                  setTimeout(() => {
                    console.log('🔄 YTR Tab: Starting chart-click-style refresh for INITIAL LOAD (single URL)');
                    refreshAllViewCountsLikeChartClick(finalUniqueVideos);
                  }, 1000); // Longer delay for initial load
                }
              }
            }
          } catch (error) {
            console.error('YTR Tab: Error in initial data load:', error);
          } finally {
            setIsUrlSwitching(false);
            setIsLoading(false);
          }
        };

        loadInitialData();
      }
    }
  }, [isActive, searchUrls, selectedUrlId, videos.length, isUrlSwitching, isLoadingUrls, videoCache, fetchVideosFromSearchUrl, refreshAllViewCountsLikeChartClick]);

  // Function to add a new search URL
  const handleAddUrl = async () => {
    if (!newUrlName.trim() || !newUrlValue.trim()) {
      toast({
        title: "Error",
        description: "Please enter both a name and URL",
        variant: "destructive"
      });
      return;
    }

    // Validate URL
    try {
      new URL(newUrlValue);
    } catch (error) {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid YouTube search URL",
        variant: "destructive"
      });
      return;
    }

    // Check if it's a YouTube search URL
    if (!newUrlValue.includes('youtube.com/results')) {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid YouTube search URL (should contain 'youtube.com/results')",
        variant: "destructive"
      });
      return;
    }

    try {
      // Save to database
      const newUrl = await YtrSearchUrlsService.createSearchUrl({
        name: newUrlName,
        url: newUrlValue,
        resultsLimit: newUrlResultsLimit
      });

      // Update local state
      const formattedUrl: SearchUrl = {
        id: newUrl.id,
        name: newUrl.name,
        url: newUrl.url,
        resultsLimit: newUrl.resultsLimit || newUrlResultsLimit
      };

      setSearchUrls([...searchUrls, formattedUrl]);
      setNewUrlName('');
      setNewUrlValue('');
      setNewUrlResultsLimit(25);
      setIsAddingUrl(false);
      setSelectedUrlId(formattedUrl.id);

      toast({
        title: "Search URL Added",
        description: `Added "${newUrlName}" to your search URLs and saved to database`,
        duration: 3000
      });
    } catch (error) {
      console.error('Error adding search URL:', error);
      toast({
        title: "Error Adding URL",
        description: "Failed to save search URL to database. Please try again.",
        variant: "destructive",
        duration: 5000
      });
    }
  };

  // Function to update a search URL
  const handleUpdateUrl = async () => {
    if (!editingUrlId || !editingName.trim() || !editingUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter both a name and URL",
        variant: "destructive"
      });
      return;
    }

    // Validate URL
    try {
      new URL(editingUrl);
    } catch (error) {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid YouTube search URL",
        variant: "destructive"
      });
      return;
    }

    // Check if it's a YouTube search URL
    if (!editingUrl.includes('youtube.com/results')) {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid YouTube search URL (should contain 'youtube.com/results')",
        variant: "destructive"
      });
      return;
    }

    try {
      // Update in database
      await YtrSearchUrlsService.updateSearchUrl(editingUrlId, {
        name: editingName,
        url: editingUrl,
        resultsLimit: editingResultsLimit
      });

      // Update local state
      const updatedUrls = searchUrls.map(url =>
        url.id === editingUrlId
          ? { ...url, name: editingName, url: editingUrl, resultsLimit: editingResultsLimit }
          : url
      );

      setSearchUrls(updatedUrls);
      setEditingUrlId(null);

      toast({
        title: "Search URL Updated",
        description: `Updated "${editingName}" in your search URLs and database`,
        duration: 3000
      });
    } catch (error) {
      console.error('Error updating search URL:', error);
      toast({
        title: "Error Updating URL",
        description: "Failed to update search URL in database. Please try again.",
        variant: "destructive",
        duration: 5000
      });
    }
  };

  // Function to delete a search URL
  const handleDeleteUrl = async (id: string) => {
    const urlToDelete = searchUrls.find(url => url.id === id);
    if (!urlToDelete) return;

    try {
      // Delete from database
      await YtrSearchUrlsService.deleteSearchUrl(id);

      // Update local state
      const updatedUrls = searchUrls.filter(url => url.id !== id);
      setSearchUrls(updatedUrls);

      // If the deleted URL was selected, select the first URL in the list
      if (selectedUrlId === id) {
        setSelectedUrlId(updatedUrls.length > 0 ? updatedUrls[0].id : null);
      }

      toast({
        title: "Search URL Deleted",
        description: `Deleted "${urlToDelete.name}" from your search URLs and database`,
        duration: 3000
      });
    } catch (error) {
      console.error('Error deleting search URL:', error);
      toast({
        title: "Error Deleting URL",
        description: "Failed to delete search URL from database. Please try again.",
        variant: "destructive",
        duration: 5000
      });
    }
  };

  // Mutation for refreshing videos
  const refreshVideos = useMutation({
    mutationFn: async () => {
      if (!selectedUrlId) {
        console.log('YTR Tab: No URL selected, skipping refresh');
        return [];
      }

      // Clear existing URL metrics to ensure fresh data
      setChannelAnalysis(prev => ({
        ...prev,
        urlMetrics: {}
      }));

      console.log('YTR Tab: Cleared existing URL metrics for fresh analysis');

      // Special case for "ALL" option
      if (selectedUrlId === 'all') {
        console.log('YTR Tab: Refreshing videos for ALL URLs');
        const allUrls = searchUrls.map(url => url.url);

        if (allUrls.length === 0) {
          console.log('YTR Tab: No URLs available for ALL option, skipping refresh');
          return [];
        }

        // Clear both client and server cache for the combined URLs to force a fresh fetch
        const combinedUrlKey = `combined_${allUrls.sort().join('_')}`;
        localStorage.removeItem(`ytr_videos_${combinedUrlKey}`);
        localStorage.removeItem(`ytr_videos_timestamp_${combinedUrlKey}`);

        // Also clear individual URL caches to ensure fresh data for each URL
        allUrls.forEach(url => {
          localStorage.removeItem(`ytr_videos_${url}`);
          localStorage.removeItem(`ytr_videos_timestamp_${url}`);
        });

        // Clear server cache for all URLs
        try {
          await apiRequest('POST', '/api/youtube-search/clear-cache', {});
          console.log('YTR Tab: Server cache cleared for all URLs');
        } catch (error) {
          console.warn('YTR Tab: Failed to clear server cache:', error);
        }

        console.log(`YTR Tab: Refreshing videos for ${allUrls.length} URLs`);

        // First, fetch videos for all URLs combined with force refresh
        const combinedVideos = await fetchVideosFromSearchUrl(allUrls, true);

        // Dispatch a custom event to clear URL metrics
        window.dispatchEvent(new Event('clearUrlMetrics'));

        // We'll analyze the combined videos to collect metrics for all URLs
        console.log('YTR Tab: Analyzing combined videos to collect comprehensive metrics');

        // Instead of making additional API requests, we'll analyze the videos we already have
        // This prevents unnecessary API calls and reduces server load
        if (combinedVideos.length > 0) {
          // Process each URL individually using the combined videos
          for (const searchUrl of searchUrls) {
            console.log(`YTR Tab: Analyzing metrics for URL "${searchUrl.name}" from combined videos`);
            try {
              // Extract the search query parameters from the URL
              const urlObj = new URL(searchUrl.url);
              const searchQuery = urlObj.searchParams.get('search_query')?.toLowerCase() || '';

              if (searchQuery) {
                // Filter the combined videos to find ones that match this search query
                // This is an approximation since we don't have the exact YouTube algorithm
                const matchingVideos = combinedVideos.filter(video =>
                  video.title.toLowerCase().includes(searchQuery) ||
                  video.description?.toLowerCase().includes(searchQuery) ||
                  video.channelTitle.toLowerCase().includes(searchQuery)
                );

                console.log(`YTR Tab: Found ${matchingVideos.length} potential matches for "${searchUrl.name}" query: "${searchQuery}"`);

                // Update the channel analysis with these videos
                analyzeChannelPerformance(matchingVideos, searchUrl.url);
              }
            } catch (error) {
              console.error(`YTR Tab: Error analyzing videos for URL "${searchUrl.name}":`, error);
            }
          }
          console.log('YTR Tab: Finished analyzing metrics for all URLs');
        }

        return combinedVideos;
      }
      // Normal single URL case
      else {
        const selectedUrl = searchUrls.find(url => url.id === selectedUrlId);
        if (!selectedUrl) {
          console.log('YTR Tab: Selected URL not found in searchUrls, skipping refresh');
          return [];
        }

        // Clear both client and server cache for this URL to force a fresh fetch
        localStorage.removeItem(`ytr_videos_${selectedUrl.url}`);
        localStorage.removeItem(`ytr_videos_timestamp_${selectedUrl.url}`);

        // Clear server cache for this URL
        try {
          await apiRequest('POST', '/api/youtube-search/force-refresh', {
            body: JSON.stringify({ searchUrl: selectedUrl.url })
          });
          console.log('YTR Tab: Server cache cleared for URL:', selectedUrl.url);
        } catch (error) {
          console.warn('YTR Tab: Failed to clear server cache:', error);
        }

        console.log('YTR Tab: Refreshing videos for URL:', selectedUrl.url);
        return fetchVideosFromSearchUrl(selectedUrl.url);
      }
    }
  });

  // Function to update previous VPH values for the RealtimeAutoRefresh component
  const updatePreviousVphValues = useCallback((videos: Video[], isBeforeRefresh: boolean) => {
    if (isBeforeRefresh) {
      // Check if we've updated recently (within the last 10 minutes)
      const lastUpdateTime = localStorage.getItem('ytr_last_view_history_update');
      const now = Date.now();

      if (lastUpdateTime) {
        const lastUpdate = parseInt(lastUpdateTime, 10);
        const timeSinceLastUpdate = now - lastUpdate;

        // If it's been less than 10 minutes since the last update, skip this update
        // unless it's the first load of the page
        if (timeSinceLastUpdate < 10 * 60 * 1000 && previousVphValues && Object.keys(previousVphValues).length > 0) {
          console.log('Skipping view history update - last update was', Math.round(timeSinceLastUpdate / 1000), 'seconds ago');
          return;
        }
      }

      // Update the last update time
      localStorage.setItem('ytr_last_view_history_update', now.toString());
      // Store current values before refresh
      const newValues = { ...previousVphValues };

      // First, try to load any saved view history from localStorage
      try {
        const savedHistory = localStorage.getItem('ytr_view_history');
        if (savedHistory) {
          const parsedHistory = JSON.parse(savedHistory) as Record<string, { views: number; timestamp: number }[]>;

          // Merge saved history with current state
          Object.keys(parsedHistory).forEach(videoId => {
            if (newValues[videoId] && parsedHistory[videoId]) {
              // If we already have this video in state, merge the histories
              const existingHistory = newValues[videoId].viewHistory || [];
              const combinedHistory = [...parsedHistory[videoId], ...existingHistory];

              // Remove duplicates by timestamp
              const uniqueHistory = combinedHistory.filter((entry, index, self) =>
                index === self.findIndex(e => e.timestamp === entry.timestamp)
              );

              // Sort by timestamp
              uniqueHistory.sort((a, b) => a.timestamp - b.timestamp);

              // Update the history
              newValues[videoId] = {
                ...newValues[videoId],
                viewHistory: uniqueHistory
              };
            } else if (parsedHistory[videoId]) {
              // If this is a new video to our state, initialize it
              newValues[videoId] = {
                previousValue: 0,
                timestamp: now,
                previousTimestamp: now,
                highestValue: 0,
                viewHistory: parsedHistory[videoId]
              };
            }
          });
        }
      } catch (error) {
        console.error('Error loading view history from localStorage:', error);
      }

      videos.forEach(video => {
        if (video.id) {
          // Get view count from the most reliable source
          let viewCount = 0;
          if (video.statistics?.viewCount) {
            viewCount = Number(video.statistics.viewCount);
          } else if (video.viewCount) {
            viewCount = typeof video.viewCount === 'string' ?
              Number(video.viewCount) : video.viewCount;
          }

          // Calculate VPH properly
          let calculatedVph = 0;
          if (video.publishedAt && viewCount > 0) {
            try {
              const publishDate = new Date(video.publishedAt);
              const now = new Date();
              const hoursElapsed = Math.max(1, (now.getTime() - publishDate.getTime()) / (1000 * 60 * 60));
              calculatedVph = Math.round(viewCount / hoursElapsed);
            } catch (e) {
              console.error('Error calculating VPH for video', video.id, ':', e);
              calculatedVph = video.vph || 0;
            }
          } else {
            calculatedVph = video.vph || 0;
          }

          // If we already have a previous value, update it
          if (newValues[video.id]) {
            // Get existing view history or initialize empty array
            const viewHistory = newValues[video.id].viewHistory || [];

            // Keep all data from the last 24 hours, but also keep at least 24 hours worth of data points
            // even if they're older than 24 hours
            const twentyFourHoursAgo = now - (24 * 60 * 60 * 1000);

            // First, separate recent and older entries
            const recentEntries = viewHistory.filter(entry => entry.timestamp >= twentyFourHoursAgo);
            const olderEntries = viewHistory.filter(entry => entry.timestamp < twentyFourHoursAgo);

            // If we have fewer than 5 data points in the last 12 hours, keep some older entries
            // to ensure we always have a good history graph
            let filteredHistory = recentEntries;
            if (recentEntries.length < 5 && olderEntries.length > 0) {
              // Sort older entries by timestamp (newest first)
              const sortedOlderEntries = [...olderEntries].sort((a, b) => b.timestamp - a.timestamp);

              // Take enough older entries to have at least 5 total data points (or all if fewer)
              const neededOlderEntries = sortedOlderEntries.slice(0, Math.max(0, 5 - recentEntries.length));

              // Combine recent and needed older entries, then sort by timestamp
              filteredHistory = [...recentEntries, ...neededOlderEntries].sort((a, b) => a.timestamp - b.timestamp);
            }

            // Check if we already have a recent entry (within the last minute)
            const oneMinuteAgo = now - (60 * 1000);
            const hasRecentEntry = filteredHistory.some(entry => entry.timestamp > oneMinuteAgo);

            // Only add a new entry if we don't have a recent one or if the view count has changed significantly
            let updatedHistory = filteredHistory;

            if (!hasRecentEntry || (filteredHistory.length > 0 &&
                Math.abs(filteredHistory[filteredHistory.length - 1].views - viewCount) > 10)) {
              updatedHistory = [
                ...filteredHistory,
                { views: viewCount, timestamp: now }
              ];
            }

            newValues[video.id] = {
              ...newValues[video.id],
              previousValue: calculatedVph,
              previousTimestamp: newValues[video.id].timestamp,
              timestamp: now,
              // Update highest value if current VPH is higher
              highestValue: Math.max(calculatedVph, newValues[video.id].highestValue || 0),
              // Update view history
              viewHistory: updatedHistory
            };
          } else {
            // First time seeing this video
            newValues[video.id] = {
              previousValue: calculatedVph,
              timestamp: now,
              previousTimestamp: now,
              highestValue: calculatedVph,
              viewHistory: [{ views: viewCount, timestamp: now }]
            };
          }
        }
      });

      // Save view history to localStorage
      try {
        // Create a map of videoId -> viewHistory
        const historyToSave: Record<string, { views: number; timestamp: number }[]> = {};

        Object.keys(newValues).forEach(videoId => {
          if (newValues[videoId].viewHistory && newValues[videoId].viewHistory!.length > 0) {
            // Keep at least 24 hours of data (double the display window)
            // This ensures we have enough historical data even when crossing day boundaries
            const twentyFourHoursAgo = now - (24 * 60 * 60 * 1000);

            // Get recent entries (last 24 hours)
            const recentEntries = newValues[videoId].viewHistory!.filter(
              entry => entry.timestamp >= twentyFourHoursAgo
            );

            // Always keep at least 10 data points for a good history, even if they're older
            let entriesToSave = recentEntries;

            if (recentEntries.length < 10 && newValues[videoId].viewHistory!.length > recentEntries.length) {
              // Get older entries
              const olderEntries = newValues[videoId].viewHistory!.filter(
                entry => entry.timestamp < twentyFourHoursAgo
              );

              // Sort older entries by timestamp (newest first)
              const sortedOlderEntries = [...olderEntries].sort((a, b) => b.timestamp - a.timestamp);

              // Take enough older entries to have at least 10 total data points (or all if fewer)
              const neededOlderEntries = sortedOlderEntries.slice(0, Math.max(0, 10 - recentEntries.length));

              // Combine recent and needed older entries
              entriesToSave = [...recentEntries, ...neededOlderEntries];
            }

            // Sort by timestamp before saving
            entriesToSave.sort((a, b) => a.timestamp - b.timestamp);

            // Save to history
            historyToSave[videoId] = entriesToSave;
          }
        });

        localStorage.setItem('ytr_view_history', JSON.stringify(historyToSave));
      } catch (error) {
        console.error('Error saving view history to localStorage:', error);
      }

      setPreviousVphValues(newValues);
    }
  }, [previousVphValues]);

  // Function to update cache for the RealtimeAutoRefresh component
  const updateCache = useCallback(() => {
    // This is a placeholder function since we're not using a cache in this component
    // but the RealtimeAutoRefresh component expects it
  }, []);

  // Add event listener for clearUrlMetrics event
  useEffect(() => {
    if (isActive) {
      const handleClearUrlMetrics = (event: Event) => {
        console.log('YTR Tab: Received clearUrlMetrics event, clearing URL metrics');
        setChannelAnalysis(prev => ({
          ...prev,
          urlMetrics: {}
        }));
      };

      window.addEventListener('clearUrlMetrics', handleClearUrlMetrics);

      // Clean up event listener when component is unmounted or becomes inactive
      return () => {
        window.removeEventListener('clearUrlMetrics', handleClearUrlMetrics);
      };
    }
  }, [isActive]);

  // Function to sort videos based on current sort field and direction
  const sortVideos = useCallback((videoList: Video[]) => {
    if (!videoList || videoList.length === 0) return videoList;

    // If YouTube order is selected, preserve the original order from the search results
    if (sortField === 'youtube') {
      return [...videoList]; // Return videos in their original YouTube search order
    }

    return [...videoList].sort((a, b) => {
      if (sortField === 'publishedAt') {
        const dateA = new Date(a.publishedAt).getTime();
        const dateB = new Date(b.publishedAt).getTime();
        return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
      } else if (sortField === 'viewCount') {
        // Get view count from the most reliable source
        const getViewCount = (video: Video): number => {
          let viewCount = 0;
          if (video.statistics?.viewCount) {
            viewCount = Number(video.statistics.viewCount);
          } else if (video.viewCount) {
            viewCount = typeof video.viewCount === 'string' ?
              Number(video.viewCount) : video.viewCount;
          }

          // Validate the view count is reasonable
          if (isNaN(viewCount) || viewCount < 0 || viewCount > 10000000000) {
            return 0;
          }

          return viewCount;
        };

        const viewCountA = getViewCount(a);
        const viewCountB = getViewCount(b);
        return sortDirection === 'asc' ? viewCountA - viewCountB : viewCountB - viewCountA;
      }

      // Default sort by published date if sortField is not recognized
      return sortDirection === 'asc'
        ? new Date(a.publishedAt).getTime() - new Date(b.publishedAt).getTime()
        : new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
    });
  }, [sortField, sortDirection]);

  // Function to toggle sorting
  const toggleSort = useCallback((field: 'youtube' | 'publishedAt' | 'viewCount') => {
    if (field === 'youtube') {
      // YouTube order doesn't have direction, just set it
      setSortField('youtube');
    } else if (sortField === field) {
      // If already sorting by this field, toggle direction
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // If sorting by a new field, set it and use default direction
      setSortField(field);
      // Default to desc (newest first) for dates, desc (highest first) for view counts
      setSortDirection('desc');
    }
  }, [sortField, sortDirection]);

  // Function to store ranking history
  const storeRankingHistory = useCallback((videos: Video[], urlName: string) => {
    const now = Date.now();

    // Group videos by channel and count them
    const channelCounts: Record<string, number> = {};
    videos.forEach(video => {
      const channel = video.channelTitle || 'Unknown';
      channelCounts[channel] = (channelCounts[channel] || 0) + 1;
    });

    // Sort channels by video count to determine rankings
    const sortedChannels = Object.entries(channelCounts)
      .sort(([, a], [, b]) => b - a)
      .map(([channelName], index) => ({
        channelName,
        ranking: index + 1,
        timestamp: now,
        urlName
      }));

    // Store in localStorage for persistence with error handling
    const storageKey = 'ytr_ranking_history';
    let finalHistoryLength = 0;

    try {
      const existingHistory = JSON.parse(localStorage.getItem(storageKey) || '[]');

      // Add new rankings
      const updatedHistory = [...existingHistory, ...sortedChannels];

      // Keep only last 12 hours of data
      const twelveHoursAgo = now - (12 * 60 * 60 * 1000);
      const filteredHistory = updatedHistory.filter(entry => entry.timestamp > twelveHoursAgo);

      // Limit the size to prevent localStorage quota issues (keep max 500 entries)
      const limitedHistory = filteredHistory.slice(-500);
      finalHistoryLength = limitedHistory.length;

      localStorage.setItem(storageKey, JSON.stringify(limitedHistory));
      setRankingHistory(limitedHistory);
    } catch (error) {
      console.error('Failed to save ranking history to localStorage:', error);
      // If localStorage is full, try to clean up and retry
      try {
        // Remove old entries and try again
        const existingHistory = JSON.parse(localStorage.getItem(storageKey) || '[]');
        const twelveHoursAgo = now - (12 * 60 * 60 * 1000);
        const recentHistory = existingHistory.filter((entry: any) => entry.timestamp > twelveHoursAgo);
        const limitedHistory = [...recentHistory, ...sortedChannels].slice(-200); // More aggressive limit
        finalHistoryLength = limitedHistory.length;

        localStorage.setItem(storageKey, JSON.stringify(limitedHistory));
        setRankingHistory(limitedHistory);
        console.log('Successfully saved ranking history after cleanup');
      } catch (retryError) {
        console.error('Failed to save ranking history even after cleanup:', retryError);
        // Just update the state without localStorage
        const fallbackHistory = [...sortedChannels].slice(-200);
        finalHistoryLength = fallbackHistory.length;
        setRankingHistory(prev => [...prev, ...sortedChannels].slice(-200));
      }
    }

    console.log(`YTR Tab: Stored ranking history for ${sortedChannels.length} channels in ${urlName}`);
    console.log('YTR Tab: Ranking history stored:', sortedChannels);
    console.log('YTR Tab: Total ranking history entries:', finalHistoryLength);

    // Debug view history storage
    console.log('YTR Tab: View history debug:', {
      totalVideos: videos.length,
      videosWithHistory: Object.keys(previousVphValues).length,
      sampleVideoHistory: Object.keys(previousVphValues).length > 0 ?
        previousVphValues[Object.keys(previousVphValues)[0]]?.viewHistory?.length : 0
    });
  }, []);

  // Function to load ranking history from localStorage
  const loadRankingHistory = useCallback(() => {
    const storageKey = 'ytr_ranking_history';
    const existingHistory = JSON.parse(localStorage.getItem(storageKey) || '[]');

    // Filter to last 12 hours
    const now = Date.now();
    const twelveHoursAgo = now - (12 * 60 * 60 * 1000);
    const filteredHistory = existingHistory.filter((entry: any) => entry.timestamp > twelveHoursAgo);

    setRankingHistory(filteredHistory);
    console.log(`YTR Tab: Loaded ${filteredHistory.length} ranking history entries`);
  }, []);

  // Load ranking history on component mount
  useEffect(() => {
    loadRankingHistory();

    // Add some mock data for testing if no data exists
    const addMockRankingData = () => {
      const storageKey = 'ytr_ranking_history';
      const existingHistory = JSON.parse(localStorage.getItem(storageKey) || '[]');

      if (existingHistory.length === 0) {
        console.log('YTR Tab: No ranking history found, adding mock data for testing');
        const now = Date.now();
        const mockData = [];

        // Create mock data for the last 12 hours
        for (let i = 0; i < 12; i++) {
          const timestamp = now - (i * 60 * 60 * 1000); // Each hour back

          // Mock some channels with varying rankings
          const channels = [
            { name: '@HowToGuys', ranking: Math.floor(Math.random() * 5) + 1 },
            { name: '@benefitsreport', ranking: Math.floor(Math.random() * 3) + 1 },
            { name: '@ssswealth', ranking: Math.floor(Math.random() * 4) + 2 },
            { name: '@BlindtoBillionaire', ranking: Math.floor(Math.random() * 6) + 1 },
            { name: '@ErinTalksMoney', ranking: Math.floor(Math.random() * 8) + 3 }
          ];

          channels.forEach(channel => {
            mockData.push({
              channelName: channel.name,
              ranking: channel.ranking,
              timestamp: timestamp,
              urlName: 'Test URL'
            });
          });
        }

        try {
          localStorage.setItem(storageKey, JSON.stringify(mockData));
          setRankingHistory(mockData);
          console.log(`YTR Tab: Added ${mockData.length} mock ranking entries for testing`);
        } catch (error) {
          console.error('Failed to save mock ranking data:', error);
        }
      }
    };

    // Add mock data after a short delay
    setTimeout(addMockRankingData, 1000);

    // Set up periodic cleanup of localStorage
    const cleanupInterval = setInterval(() => {
      try {
        const storageKey = 'ytr_ranking_history';
        const existingHistory = JSON.parse(localStorage.getItem(storageKey) || '[]');

        if (existingHistory.length > 500) {
          const now = Date.now();
          const twelveHoursAgo = now - (12 * 60 * 60 * 1000);
          const filteredHistory = existingHistory.filter((entry: any) => entry.timestamp > twelveHoursAgo);
          const limitedHistory = filteredHistory.slice(-300); // Keep only 300 most recent

          localStorage.setItem(storageKey, JSON.stringify(limitedHistory));
          setRankingHistory(limitedHistory);
          console.log(`YTR Tab: Cleaned up ranking history from ${existingHistory.length} to ${limitedHistory.length} entries`);
        }
      } catch (error) {
        console.error('Error during periodic ranking history cleanup:', error);
      }
    }, 30 * 60 * 1000); // Run every 30 minutes

    return () => clearInterval(cleanupInterval);
  }, [loadRankingHistory]);

  // Function to analyze channel performance in search results
  const analyzeChannelPerformance = useCallback((videoList: Video[], currentUrl?: string) => {
    if (!videoList || videoList.length === 0) return;

    // Filter out eliminated videos from calculations
    const filteredVideoList = videoList.filter(video => !eliminatedVideos.has(video.id));
    console.log(`YTR Tab: Filtered out ${videoList.length - filteredVideoList.length} eliminated videos, analyzing ${filteredVideoList.length} videos`);

    // Log the first few videos to debug
    const sampleVideos = filteredVideoList.slice(0, 5);
    console.log('YTR Tab: Sample videos:', sampleVideos.map(v => ({
      title: v.title,
      channel: v.channelTitle,
      id: v.id
    })));

    // Find your channel's videos
    const yourChannelName = channelAnalysis.yourChannelName;
    const yourVideos = filteredVideoList.filter(video =>
      video.channelTitle && video.channelTitle.trim() === yourChannelName.trim()
    );

    // Calculate rankings for your videos (use original videoList for ranking positions)
    const yourRankings = yourVideos.map(video => ({
      videoId: video.id,
      position: videoList.findIndex(v => v.id === video.id) + 1
    }));

    // Enhanced channel performance analysis
    // Group videos by channel and calculate metrics
    const channelMetrics: Record<string, {
      name: string;
      videoCount: number;
      totalViews: number;
      avgViews: number;
      avgVph: number;
      newestVideoDate: Date;
      oldestVideoDate: Date;
      viewsPerVideo: number;
      rankingPositions: number[];
    }> = {};

    // Process all videos to gather channel metrics
    filteredVideoList.forEach((video, index) => {
      if (!video.channelTitle) return;

      const channelName = video.channelTitle.trim();

      // Get view count from the most reliable source
      let viewCount = 0;
      if (video.statistics?.viewCount) {
        viewCount = Number(video.statistics.viewCount);
      } else if (video.viewCount) {
        viewCount = typeof video.viewCount === 'string' ?
          Number(video.viewCount) : video.viewCount;
      }

      // Validate the view count is reasonable
      if (isNaN(viewCount) || viewCount < 0 || viewCount > 10000000000) {
        viewCount = 0;
      }

      const publishDate = new Date(video.publishedAt);
      const vph = video.vph || 0;

      // Initialize channel metrics if this is the first video from this channel
      if (!channelMetrics[channelName]) {
        channelMetrics[channelName] = {
          name: channelName,
          videoCount: 0,
          totalViews: 0,
          avgViews: 0,
          avgVph: 0,
          newestVideoDate: publishDate,
          oldestVideoDate: publishDate,
          viewsPerVideo: 0,
          rankingPositions: []
        };
      }

      // Update channel metrics
      const metrics = channelMetrics[channelName];
      metrics.videoCount++;
      metrics.totalViews += viewCount;
      // Use original videoList position for ranking
      const originalPosition = videoList.findIndex(v => v.id === video.id) + 1;
      metrics.rankingPositions.push(originalPosition);

      // Update newest/oldest video dates
      if (publishDate > metrics.newestVideoDate) {
        metrics.newestVideoDate = publishDate;
      }
      if (publishDate < metrics.oldestVideoDate) {
        metrics.oldestVideoDate = publishDate;
      }

      // Add VPH to running total (we'll calculate average later)
      metrics.avgVph = ((metrics.avgVph * (metrics.videoCount - 1)) + vph) / metrics.videoCount;
    });

    // Check if there are any @HowToGuys videos in the results
    // Check for all variations of the channel name
    const howToGuysVideos = filteredVideoList.filter(video =>
      video.channelTitle && CHANNEL_NAME_VARIATIONS.some(variation =>
        video.channelTitle.trim() === variation
      )
    );
    console.log(`YTR Tab: Found ${howToGuysVideos.length} ${TRACKED_CHANNEL_NAME} videos`);

    // Log all channel titles to debug
    const allChannelTitles = [...new Set(filteredVideoList.map(v => v.channelTitle))];
    console.log('YTR Tab: All channel titles:', allChannelTitles);

    // Calculate final metrics for each channel
    Object.values(channelMetrics).forEach(metrics => {
      metrics.avgViews = metrics.videoCount > 0 ? metrics.totalViews / metrics.videoCount : 0;
      metrics.viewsPerVideo = metrics.videoCount > 0 ? metrics.totalViews / metrics.videoCount : 0;
    });

    // Convert to array and sort by total views (descending)
    const allChannelMetrics = Object.values(channelMetrics)
      .sort((a, b) => b.totalViews - a.totalViews);

    // Extract competitor channels (excluding your channel)
    const competitorChannels = allChannelMetrics
      .filter(metrics => metrics.name !== yourChannelName.trim())
      .map(metrics => ({
        name: metrics.name,
        count: metrics.videoCount,
        totalViews: metrics.totalViews,
        avgViews: Math.round(metrics.avgViews),
        avgVph: Math.round(metrics.avgVph),
        viewsPerVideo: Math.round(metrics.viewsPerVideo),
        bestRanking: Math.min(...metrics.rankingPositions)
      }))
      .slice(0, 10); // Top 10 competitors

    // Get your channel metrics
    const yourChannelMetrics = channelMetrics[yourChannelName.trim()];

    // Track metrics for @HowToGuys by URL
    // Get the metrics from the channelMetrics object - check all variations
    let howToGuysMetrics = null;

    // Try all variations of the channel name
    for (const variation of CHANNEL_NAME_VARIATIONS) {
      if (channelMetrics[variation]) {
        howToGuysMetrics = channelMetrics[variation];
        console.log(`YTR Tab: Found metrics using channel name variation: "${variation}"`);
        break;
      }
    }

    // Always create metrics for @HowToGuys, even if none found in the results
    // This ensures we always have a valid entry for the UI
    console.log('YTR Tab: Creating or updating metrics for @HowToGuys');

    if (howToGuysVideos.length > 0) {
      // We have videos, calculate metrics
      console.log('YTR Tab: Found @HowToGuys videos - creating metrics from videos');

      // Calculate total views
      const totalViews = howToGuysVideos.reduce((sum, video) => {
        let viewCount = 0;
        if (video.statistics?.viewCount) {
          viewCount = Number(video.statistics.viewCount);
        } else if (video.viewCount) {
          viewCount = typeof video.viewCount === 'string' ?
            Number(video.viewCount) : video.viewCount;
        }
        return sum + (isNaN(viewCount) ? 0 : viewCount);
      }, 0);

      // Calculate rankings
      const rankingPositions = howToGuysVideos.map((video, idx) =>
        videoList.findIndex(v => v.id === video.id) + 1
      );

      // Create metrics manually
      channelMetrics[TRACKED_CHANNEL_NAME] = {
        name: TRACKED_CHANNEL_NAME,
        videoCount: howToGuysVideos.length,
        totalViews: totalViews,
        avgViews: howToGuysVideos.length > 0 ? totalViews / howToGuysVideos.length : 0,
        avgVph: howToGuysVideos.length > 0 ?
          howToGuysVideos.reduce((sum, v) => sum + (v.vph || 0), 0) / howToGuysVideos.length : 0,
        newestVideoDate: howToGuysVideos.length > 0 ?
          new Date(Math.max(...howToGuysVideos.map(v => new Date(v.publishedAt).getTime()))) : new Date(),
        oldestVideoDate: howToGuysVideos.length > 0 ?
          new Date(Math.min(...howToGuysVideos.map(v => new Date(v.publishedAt).getTime()))) : new Date(),
        viewsPerVideo: howToGuysVideos.length > 0 ? totalViews / howToGuysVideos.length : 0,
        rankingPositions: rankingPositions
      };
    } else if (!howToGuysMetrics) {
      // No videos found and no existing metrics, create empty metrics
      console.log('YTR Tab: No @HowToGuys videos found - creating empty metrics');

      channelMetrics[TRACKED_CHANNEL_NAME] = {
        name: TRACKED_CHANNEL_NAME,
        videoCount: 0,
        totalViews: 0,
        avgViews: 0,
        avgVph: 0,
        newestVideoDate: new Date(),
        oldestVideoDate: new Date(),
        viewsPerVideo: 0,
        rankingPositions: []
      };
    }

    // Get the updated metrics
    const updatedHowToGuysMetrics = channelMetrics[TRACKED_CHANNEL_NAME];
    console.log('YTR Tab: @HowToGuys metrics:', updatedHowToGuysMetrics);

    // Get the current URL information for the current analysis
    let urlInfo: { url: string, name: string } | undefined;

    if (currentUrl === 'all') {
      // For "ALL" combined view
      urlInfo = { url: 'all', name: 'All URLs Combined' };
    } else if (selectedUrlId === 'all') {
      // For "ALL" combined view when no currentUrl is passed
      urlInfo = { url: 'all', name: 'All URLs Combined' };
    } else if (currentUrl) {
      // For a specific URL
      urlInfo = { url: currentUrl, name: searchUrls.find(u => u.url === currentUrl)?.name || 'Unknown URL' };
    } else if (selectedUrlId) {
      // If we have a selected URL but no current URL passed
      const selectedUrl = searchUrls.find(u => u.id === selectedUrlId);
      if (selectedUrl) {
        urlInfo = { url: selectedUrl.url, name: selectedUrl.name };
      }
    }

    console.log('YTR Tab: URL info for metrics:', urlInfo);

    // Create a copy of the existing URL metrics
    const existingUrlMetrics = {...(channelAnalysis.urlMetrics || {})};

    // If we have URL info, add metrics for @HowToGuys (even if none found)
    if (urlInfo) {
      // Find the HowToGuys metrics - CRITICAL: Use the exact channel name format from the API
      const howToGuysMetrics = channelMetrics[EXACT_CHANNEL_MATCH];

      // Always add metrics for the URL, even if no videos were found
      // Create a map of video IDs to their rankings for this URL
      const videoRankings: Record<string, number> = {};

      // Process all videos in the current search results to get their rankings
      videoList.forEach((video, index) => {
        videoRankings[video.id] = index + 1;
      });

      existingUrlMetrics[urlInfo.url] = {
        urlName: urlInfo.name,
        videoCount: howToGuysMetrics ? howToGuysMetrics.videoCount : 0,
        totalViews: howToGuysMetrics ? howToGuysMetrics.totalViews : 0,
        bestRanking: howToGuysMetrics && howToGuysMetrics.rankingPositions.length > 0 ?
          Math.min(...howToGuysMetrics.rankingPositions) : 0,
        rankingPositions: howToGuysMetrics ? [...howToGuysMetrics.rankingPositions] : [],
        videoRankings: videoRankings
      };

      if (howToGuysMetrics) {
        console.log(`YTR Tab: Saved @HowToGuys metrics for URL "${urlInfo.name}": ${howToGuysMetrics.videoCount} videos, best rank #${howToGuysMetrics.rankingPositions.length > 0 ? Math.min(...howToGuysMetrics.rankingPositions) : 0}`);
      } else {
        // No @HowToGuys videos found - log it
        console.log(`YTR Tab: No @HowToGuys videos found for URL "${urlInfo.name}"`);
      }
    }

    // If we're analyzing the "all" URL, also analyze each individual URL
    // This ensures we have metrics for all URLs even when viewing the combined results
    if (currentUrl === 'all' || selectedUrlId === 'all') {
      console.log('YTR Tab: Analyzing all URLs individually to collect comprehensive metrics');

      // We'll keep the existing metrics for the current URL, but we need to make sure
      // we have metrics for all other URLs as well

      // For each URL in searchUrls, check if we already have metrics
      searchUrls.forEach(searchUrl => {
        // Skip if we already have metrics for this URL
        if (existingUrlMetrics[searchUrl.url]) {
          console.log(`YTR Tab: Already have metrics for URL "${searchUrl.name}"`);
          return;
        }

        console.log(`YTR Tab: Need to collect metrics for URL "${searchUrl.name}"`);

        // We don't have metrics for this URL yet, so we need to add placeholder metrics
        // These will be updated when the user selects this URL or refreshes
        existingUrlMetrics[searchUrl.url] = {
          urlName: searchUrl.name,
          videoCount: 0,
          totalViews: 0,
          bestRanking: 0,
          rankingPositions: [],
          videoRankings: {}
        };
      });
    }

    // Update state with analysis results and trigger ranking update timestamp
    setChannelAnalysis({
      ...channelAnalysis,
      yourChannelVideos: yourVideos,
      yourChannelRankings: yourRankings,
      competitorChannels,
      allChannelMetrics: allChannelMetrics,
      yourChannelMetrics: yourChannelMetrics,
      urlMetrics: existingUrlMetrics
    });

    // Debounce ranking updates to prevent flashing
    setTimeout(() => {
      setRankingUpdateTimestamp(Date.now());
    }, 500);

    console.log(`YTR Tab: Enhanced channel analysis complete. Found ${yourVideos.length} videos from your channel.`);
    console.log(`YTR Tab: Analyzed ${Object.keys(channelMetrics).length} channels in total.`);
    if (howToGuysMetrics) {
      console.log(`YTR Tab: @HowToGuys has ${howToGuysMetrics.videoCount} videos in these results.`);
    }

    // Store ranking history for this analysis
    if (urlInfo) {
      storeRankingHistory(videoList, urlInfo.name);
    }
  }, [channelAnalysis, searchUrls, selectedUrlId, storeRankingHistory, eliminatedVideos]);

  // Function to copy selected videos info to clipboard
  const copySelectedVideosInfo = useCallback(async () => {
    if (selectedVideos.length === 0) {
      toast({
        title: "No Videos Selected",
        description: "Please select at least one video to copy information.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Create markdown summary
      let markdown = `# Selected YouTube Videos\n\n`;
      markdown += `**Selected Videos:** ${selectedVideos.length}\n`;
      markdown += `**Date:** ${new Date().toLocaleDateString()}\n`;
      markdown += `**Top VPH:** ${Math.max(...selectedVideos.map(v => v.vph || 0)).toLocaleString()} views per hour\n\n`;
      markdown += `## Video Details\n\n`;

      // Create detailed information for each video
      const videosInfo = selectedVideos.map((video, index) => {
        const viewCount = video.statistics?.viewCount ? parseInt(video.statistics.viewCount).toLocaleString() :
                         (video.viewCount ? (typeof video.viewCount === 'string' ? parseInt(video.viewCount).toLocaleString() : video.viewCount.toLocaleString()) : 'N/A');

        return `### ${index + 1}. ${video.title}\n` +
          `- **URL:** https://www.youtube.com/watch?v=${video.id}\n` +
          `- **Channel:** ${video.channelTitle}\n` +
          `- **Published:** ${new Date(video.publishedAt).toLocaleString()}\n` +
          `- **Views:** ${viewCount}\n` +
          `- **VPH:** ${Math.round(video.vph || 0).toLocaleString()}\n\n`;
      }).join('');

      markdown += videosInfo;

      // Copy to clipboard
      await navigator.clipboard.writeText(markdown);

      toast({
        title: "Copied to Clipboard",
        description: `Information for ${selectedVideos.length} videos has been copied to your clipboard in Markdown format.`,
        duration: 3000
      });
    } catch (error) {
      console.error('Error copying selected videos info:', error);
      toast({
        title: "Copy Failed",
        description: "Failed to copy video information to clipboard.",
        variant: "destructive"
      });
    }
  }, [selectedVideos, toast]);

  return (
    <div className="space-y-4">
      {/* Connection Status Indicator */}
      {connectionHealth !== 'healthy' && (
        <div className={`p-2 rounded-md text-sm ${
          connectionHealth === 'degraded'
            ? 'bg-yellow-100 text-yellow-800 border border-yellow-300'
            : 'bg-red-100 text-red-800 border border-red-300'
        }`}>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              connectionHealth === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
            }`}></div>
            <span>
              {connectionHealth === 'degraded'
                ? 'Connection degraded - some features may be slower'
                : 'Connection offline - using cached data where available'}
            </span>
            {retryCount > 0 && (
              <span className="text-xs opacity-75">
                (Retry {retryCount}/3)
              </span>
            )}
            {cacheStatus !== 'empty' && (
              <span className="text-xs opacity-75">
                • Cache: {cacheStatus}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Performance Status (only show if there are issues) */}
      {(performanceMetrics.successRate < 90 || performanceMetrics.lastLoadTime > 10000) && (
        <div className="p-2 rounded-md text-sm bg-blue-100 text-blue-800 border border-blue-300">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
            <span>
              Performance: {performanceMetrics.successRate.toFixed(0)}% success rate
              {performanceMetrics.lastLoadTime > 0 && (
                <span> • Last load: {(performanceMetrics.lastLoadTime / 1000).toFixed(1)}s</span>
              )}
            </span>
          </div>
        </div>
      )}

      <div className="flex flex-col space-y-4">
        {/* Collapsible Search URL Management */}
        <div className="mb-2">
          <div className="flex justify-between items-center mb-1">
            <Button
              variant="ghost"
              className="p-0 h-auto flex items-center text-sm font-medium hover:bg-transparent"
              onClick={() => {
                // Toggle URL section visibility using localStorage
                const isVisible = localStorage.getItem('ytrUrlSectionVisible') !== 'false';
                localStorage.setItem('ytrUrlSectionVisible', (!isVisible).toString());
                // Force re-render by updating state
                setIsAddingUrl(isAddingUrl);
              }}
            >
              <span className="mr-1">YouTube Search URLs</span>
              {localStorage.getItem('ytrUrlSectionVisible') !== 'false' ?
                <ChevronDown className="h-4 w-4" /> :
                <ChevronRight className="h-4 w-4" />
              }
            </Button>

            {searchUrls.length > 0 && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <span>Selected: </span>
                <span className="font-medium text-white">
                  {selectedUrlId ?
                    selectedUrlId === 'all' ?
                      <span className="flex items-center">
                        <Activity className="h-3 w-3 mr-1 text-green-500" />
                        ALL COMBINED ({searchUrls.length} URLs)
                      </span> :
                      searchUrls.find(url => url.id === selectedUrlId)?.name || 'None'
                    : 'None'}
                </span>
              </div>
            )}
          </div>

          {/* URL Section - Collapsible */}
          {localStorage.getItem('ytrUrlSectionVisible') !== 'false' && (
            <Card className="mb-2">
              <CardContent className="p-2">
                {/* URL List */}
                {searchUrls.length > 0 ? (
                  <div className="space-y-1 mb-2">
                    <Tabs
                      value={selectedUrlId || ''}
                      onValueChange={(value) => {
                        // Only set the selected URL ID - data fetching is handled by onClick events
                        console.log('YTR Tab: URL tab selection changed to:', value);
                        // Don't trigger data fetching here to prevent duplicate requests
                      }}
                    >
                      <TabsList className="mb-1 h-10">
                        {/* ALL option */}
                        {searchUrls.length > 1 && (
                          <TabsTrigger
                            key="all"
                            value="all"
                            className="h-8 px-2 py-1 text-xs bg-green-700/20 data-[state=active]:bg-green-700 data-[state=active]:text-white cursor-pointer hover:bg-green-700/30 transition-colors"
                            onClick={(e) => {
                              e.preventDefault();
                              console.log('YTR Tab: ALL button clicked');
                              handleUrlButtonClick('all');
                            }}
                            title={(() => {
                              // Get the most recent refresh time from all URLs
                              const allRefreshTimes = searchUrls.map(url => {
                                const lastRefreshKey = `ytr_videos_timestamp_${url.url}`;
                                const lastRefreshTime = localStorage.getItem(lastRefreshKey);
                                return lastRefreshTime ? parseInt(lastRefreshTime) : 0;
                              }).filter(time => time > 0);

                              if (allRefreshTimes.length === 0) {
                                return 'Click to refresh if already selected';
                              }

                              const mostRecentTime = Math.max(...allRefreshTimes);
                              const mostRecentDate = new Date(mostRecentTime);
                              return `Most recent refresh: ${mostRecentDate.toLocaleString()}\nClick to refresh if already selected`;
                            })()}
                          >
                            <div className="flex flex-col items-center">
                              <div className="flex items-center">
                                <Activity className="h-3 w-3 mr-1" />
                                <span>ALL</span>
                              </div>
                              {(() => {
                                // Show the most recent refresh time
                                const allRefreshTimes = searchUrls.map(url => {
                                  const lastRefreshKey = `ytr_videos_timestamp_${url.url}`;
                                  const lastRefreshTime = localStorage.getItem(lastRefreshKey);
                                  return lastRefreshTime ? parseInt(lastRefreshTime) : 0;
                                }).filter(time => time > 0);

                                if (allRefreshTimes.length === 0) return null;

                                const mostRecentTime = Math.max(...allRefreshTimes);
                                const mostRecentDate = new Date(mostRecentTime);
                                const now = new Date();
                                const diffMs = now.getTime() - mostRecentDate.getTime();
                                const diffMins = Math.floor(diffMs / (1000 * 60));
                                const diffHours = Math.floor(diffMins / 60);

                                let timeText = '';
                                if (diffMins < 1) timeText = 'Just now';
                                else if (diffMins < 60) timeText = `${diffMins}m ago`;
                                else if (diffHours < 24) timeText = `${diffHours}h ago`;
                                else timeText = mostRecentDate.toLocaleDateString();

                                return (
                                  <span className="text-[9px] text-muted-foreground opacity-70">
                                    {timeText}
                                  </span>
                                );
                              })()}
                            </div>
                          </TabsTrigger>
                        )}

                        {/* Individual URLs */}
                        {searchUrls.map(url => {
                          // Get last refresh time for this URL
                          const lastRefreshKey = `ytr_videos_timestamp_${url.url}`;
                          const lastRefreshTime = localStorage.getItem(lastRefreshKey);
                          const lastRefreshDate = lastRefreshTime ? new Date(parseInt(lastRefreshTime)) : null;

                          return (
                            <TabsTrigger
                              key={url.id}
                              value={url.id}
                              className="h-8 px-2 py-1 text-xs relative group cursor-pointer hover:bg-muted/50 transition-colors"
                              onClick={(e) => {
                                e.preventDefault();
                                console.log(`YTR Tab: URL button clicked: ${url.name}`);
                                handleUrlButtonClick(url.id);
                              }}
                              title={lastRefreshDate ?
                                `Last refreshed: ${lastRefreshDate.toLocaleString()}\nClick to refresh if already selected` :
                                'Click to refresh if already selected'
                              }
                            >
                              <div className="flex flex-col items-center">
                                <span>{url.name}</span>
                                {lastRefreshDate && (
                                  <span className="text-[9px] text-muted-foreground opacity-70">
                                    {(() => {
                                      const now = new Date();
                                      const diffMs = now.getTime() - lastRefreshDate.getTime();
                                      const diffMins = Math.floor(diffMs / (1000 * 60));
                                      const diffHours = Math.floor(diffMins / 60);

                                      if (diffMins < 1) return 'Just now';
                                      if (diffMins < 60) return `${diffMins}m ago`;
                                      if (diffHours < 24) return `${diffHours}h ago`;
                                      return lastRefreshDate.toLocaleDateString();
                                    })()}
                                  </span>
                                )}
                              </div>
                            </TabsTrigger>
                          );
                        })}
                      </TabsList>

                      {/* ALL option content */}
                      {searchUrls.length > 1 && (
                        <TabsContent key="all" value="all" className="mt-1">
                          <div className="flex flex-col gap-1">
                            <div className="text-xs text-muted-foreground mb-1">
                              Combining results from all {searchUrls.length} search URLs
                            </div>
                          </div>
                        </TabsContent>
                      )}

                      {/* Individual URL contents */}
                      {searchUrls.map(url => (
                        <TabsContent key={url.id} value={url.id} className="mt-1">
                          <div className="flex items-center gap-1">
                            {editingUrlId === url.id ? (
                              <div className="flex-1 flex flex-col gap-1">
                                <div className="flex gap-1">
                                  <Input
                                    value={editingName}
                                    onChange={(e) => setEditingName(e.target.value)}
                                    placeholder="URL Name"
                                    className="flex-1 h-7 text-xs"
                                  />
                                  <Input
                                    value={editingUrl}
                                    onChange={(e) => setEditingUrl(e.target.value)}
                                    placeholder="https://www.youtube.com/results?search_query=..."
                                    className="flex-2 h-7 text-xs"
                                  />
                                  <Input
                                    type="number"
                                    value={editingResultsLimit}
                                    onChange={(e) => setEditingResultsLimit(Math.max(1, Math.min(500, parseInt(e.target.value) || 25)))}
                                    placeholder="Results"
                                    min="1"
                                    max="500"
                                    className="w-16 h-7 text-xs"
                                    title="Number of results to fetch (1-500)"
                                  />
                                </div>
                                <div className="flex justify-end gap-1">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7"
                                    onClick={handleUpdateUrl}
                                    title="Save changes"
                                  >
                                    <Check className="h-3 w-3 text-green-500" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7"
                                    onClick={() => setEditingUrlId(null)}
                                    title="Cancel"
                                  >
                                    <X className="h-3 w-3 text-red-500" />
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <>
                                <div className="flex-1 flex flex-col gap-1">
                                  <Input
                                    value={url.url}
                                    readOnly
                                    className="w-full h-7 text-xs"
                                  />
                                  <div className="text-xs text-muted-foreground">
                                    Results limit: {url.resultsLimit || 25} videos
                                  </div>
                                </div>
                                <div className="flex gap-0">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7"
                                    onClick={() => {
                                      setEditingUrlId(url.id);
                                      setEditingName(url.name);
                                      setEditingUrl(url.url);
                                      setEditingResultsLimit(url.resultsLimit || 25);
                                    }}
                                    title="Edit URL"
                                  >
                                    <Edit className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7"
                                    onClick={() => handleDeleteUrl(url.id)}
                                    title="Delete URL"
                                  >
                                    <Trash2 className="h-3 w-3 text-red-500" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7"
                                    onClick={() => window.open(url.url, '_blank')}
                                    title="Open URL"
                                  >
                                    <ExternalLink className="h-3 w-3" />
                                  </Button>
                                </div>
                              </>
                            )}
                          </div>
                        </TabsContent>
                      ))}
                    </Tabs>
                  </div>
                ) : (
                  <div className="text-center py-2 text-xs text-muted-foreground">
                    No search URLs added yet. Add a YouTube search URL to get started.
                  </div>
                )}

                {/* Add New URL - Compact */}
                {isAddingUrl ? (
                  <div className="flex flex-col space-y-1 bg-muted/30 p-2 rounded-md">
                    <h4 className="text-xs font-medium">Add New YouTube Search URL</h4>
                    <div className="flex gap-1">
                      <Input
                        value={newUrlName}
                        onChange={(e) => setNewUrlName(e.target.value)}
                        placeholder="URL Name"
                        className="flex-1 h-7 text-xs"
                      />
                      <Input
                        value={newUrlValue}
                        onChange={(e) => setNewUrlValue(e.target.value)}
                        placeholder="https://www.youtube.com/results?search_query=..."
                        className="flex-2 h-7 text-xs"
                      />
                      <Input
                        type="number"
                        value={newUrlResultsLimit}
                        onChange={(e) => setNewUrlResultsLimit(Math.max(1, Math.min(500, parseInt(e.target.value) || 25)))}
                        placeholder="Results"
                        min="1"
                        max="500"
                        className="w-16 h-7 text-xs"
                        title="Number of results to fetch (1-500, default: 25)"
                      />
                    </div>
                    <div className="text-xs text-muted-foreground mb-1">
                      Results limit: {newUrlResultsLimit} videos (default: 25, max: 500)
                    </div>
                    <div className="flex justify-end gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-6 text-xs"
                        onClick={() => {
                          setIsAddingUrl(false);
                          setNewUrlName('');
                          setNewUrlValue('');
                          setNewUrlResultsLimit(25);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        className="h-6 text-xs"
                        onClick={handleAddUrl}
                      >
                        Add URL
                      </Button>
                    </div>
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsAddingUrl(true)}
                    className="w-full h-6 text-xs"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add New Search URL
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Auto-refresh controls */}
        {selectedUrlId && searchUrls.length > 0 && (
          <div className="flex flex-col gap-1">
            {/* Last Refresh Time Display */}
            {lastRefreshTime && (
              <div className="text-xs text-white/60 text-center bg-muted/10 p-1 rounded">
                Last refresh: {lastRefreshTime.toLocaleString()} ({formatTimeAgo(lastRefreshTime)})
              </div>
            )}

            <div className="flex items-center justify-between bg-muted/20 p-1 rounded-md mb-2">
              {/* @HowToGuys Performance Summary - Ultra Compact */}
              {(() => {
              // Filter out eliminated videos from calculations
              const filteredVideoList = videos.filter(video => !eliminatedVideos.has(video.id));

              const howToGuysMetrics = channelAnalysis.allChannelMetrics?.find(c => c.name === EXACT_CHANNEL_MATCH);
              const totalVideos = howToGuysMetrics?.videoCount || 0;
              const totalViews = howToGuysMetrics?.totalViews || 0;
              const bestRank = howToGuysMetrics?.rankingPositions ? Math.min(...howToGuysMetrics.rankingPositions) : 0;
              const urlCount = Object.values(channelAnalysis.urlMetrics || {}).filter(m => m.videoCount > 0).length;

              // Calculate total views from ONLY @HowToGuys videos in current results
              const howToGuysVideosInResults = filteredVideoList.filter(video => video.channelTitle === EXACT_CHANNEL_MATCH);
              const howToGuysTotalViews = howToGuysVideosInResults.reduce((sum, video) => {
                let viewCount = 0;
                if (video.statistics?.viewCount) {
                  viewCount = Number(video.statistics.viewCount);
                } else if (video.viewCount) {
                  viewCount = typeof video.viewCount === 'string' ? Number(video.viewCount) : video.viewCount;
                }
                return sum + (isNaN(viewCount) ? 0 : viewCount);
              }, 0);

              if (howToGuysVideosInResults.length > 0) {
                // Format views without K conversion for low numbers
                const formatViews = (views) => {
                  if (views >= 10000) {
                    return `${(views/1000).toFixed(1)}K`;
                  }
                  return views.toString();
                };

                // Calculate competitor views from ONLY videos in current search results (not all their videos)
                const competitorVideosInResults = filteredVideoList.filter(video => video.channelTitle !== EXACT_CHANNEL_MATCH);
                const competitorViews = competitorVideosInResults.reduce((sum, video) => {
                  let viewCount = 0;
                  if (video.statistics?.viewCount) {
                    viewCount = Number(video.statistics.viewCount);
                  } else if (video.viewCount) {
                    viewCount = typeof video.viewCount === 'string' ? Number(video.viewCount) : video.viewCount;
                  }
                  return sum + (isNaN(viewCount) ? 0 : viewCount);
                }, 0);

                // Calculate market share based on current search results only
                const totalMarketViews = howToGuysTotalViews + competitorViews;
                const marketShare = totalMarketViews > 0 ? ((howToGuysTotalViews / totalMarketViews) * 100) : 0;

                // Calculate TMCV (Total Max Competition Views) - the highest view count among competitors in current search results
                // Group competitor videos by channel and sum their views
                const competitorChannelViews = {};
                competitorVideosInResults.forEach(video => {
                  const channelName = video.channelTitle;
                  if (!competitorChannelViews[channelName]) {
                    competitorChannelViews[channelName] = 0;
                  }
                  let viewCount = 0;
                  if (video.statistics?.viewCount) {
                    viewCount = Number(video.statistics.viewCount);
                  } else if (video.viewCount) {
                    viewCount = typeof video.viewCount === 'string' ? Number(video.viewCount) : video.viewCount;
                  }
                  competitorChannelViews[channelName] += isNaN(viewCount) ? 0 : viewCount;
                });

                const maxCompetitorViews = Object.keys(competitorChannelViews).length > 0 ?
                  Math.max(...Object.values(competitorChannelViews)) : 0;
                const maxCompetitorName = Object.keys(competitorChannelViews).find(
                  channel => competitorChannelViews[channel] === maxCompetitorViews
                ) || 'N/A';

                // Calculate gap to #1 competitor
                const gapToTopCompetitor = howToGuysTotalViews - maxCompetitorViews;

                return (
                  <div className="flex items-center gap-3 text-xs text-green-400 font-medium">
                    <div className="flex items-center gap-2">
                      <span>
                        @HowToGuys: <span className="bg-cyan-500/20 px-1 rounded font-bold text-white">{marketShare.toFixed(1)}%</span>, <span className="bg-green-500/20 px-1 rounded font-bold text-white">{howToGuysVideosInResults.length}</span> videos, <span className="bg-blue-500/20 px-1 rounded font-bold text-white">{formatViews(howToGuysTotalViews)}</span> ÷ <span className="bg-red-500/20 px-1 rounded font-bold text-white">{formatViews(competitorViews)}</span>, rank <span className="bg-yellow-500/20 px-1 rounded font-bold text-white">#{bestRank || 'N/A'}</span>
                      </span>

                      {/* TMCV and Gap Analysis */}
                      <span className="text-[10px] text-gray-300 border-l border-gray-600 pl-2">
                        TMCV: <span className="bg-red-500/20 px-1 rounded font-bold text-white" title={`Max competitor: ${maxCompetitorName}`}>{maxCompetitorViews.toLocaleString()}</span>
                        {gapToTopCompetitor !== 0 && (
                          <span className={`ml-1 px-1 rounded font-bold ${gapToTopCompetitor >= 0 ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'}`}>
                            {gapToTopCompetitor >= 0 ? '+' : ''}{gapToTopCompetitor.toLocaleString()}
                          </span>
                        )}
                      </span>
                    </div>

                    {/* Enhanced Market Share Chart - Larger */}
                    <MarketShareChart
                      channelName="@HowToGuys"
                      currentHowToGuysViews={howToGuysTotalViews}
                      currentCompetitorViews={competitorViews}
                      currentMarketShare={marketShare}
                      height={24}
                      width={120}
                      maxDays={7}
                      showDaySelector={false}
                    />
                  </div>
                );
              }
              return <div className="text-xs text-muted-foreground">@HowToGuys: No data available</div>;
            })()}

            {/* DEBUG: Temporary test button */}
            <Button
              variant="outline"
              size="sm"
              className="h-7 px-2 py-0 text-xs mr-2"
              onClick={() => {
                console.log('🔄 DEBUG: Manual test button clicked - using chart click method');
                refreshAllViewCountsLikeChartClick(videos);
              }}
            >
              🔄 Test Refresh
            </Button>

            <RealtimeAutoRefresh
              isActive={isActive && Boolean(selectedUrlId)}
              onRefresh={async () => {
                console.log(`YTR Tab: Refresh button clicked - staying on ${selectedUrlId}`);

                if (searchUrls.length === 0) {
                  console.log('YTR Tab: No URLs available, skipping refresh');
                  return videos;
                }

                // Use the same mechanism as bar chart clicks for consistent view count updates
                console.log('YTR Tab: Using bar chart click mechanism for refresh button');

                // First, refresh the URL data to get fresh videos
                let refreshedVideos = videos;

                // If we're on a specific URL, refresh that URL first (urgently)
                if (selectedUrlId && selectedUrlId !== 'all') {
                  const selectedUrl = searchUrls.find(url => url.id === selectedUrlId);
                  if (selectedUrl) {
                    console.log(`YTR Tab: Refreshing selected URL first: ${selectedUrl.name}`);

                    // Clear cache for the selected URL to force fresh fetch
                    localStorage.removeItem(`ytr_videos_${selectedUrl.url}`);
                    localStorage.removeItem(`ytr_videos_timestamp_${selectedUrl.url}`);

                    // Show refresh toast for the selected URL
                    toast({
                      title: `Refreshing ${selectedUrl.name}`,
                      description: "Fetching fresh data and updating view counts",
                      duration: 3000
                    });

                    // Fetch the selected URL first with force refresh
                    const freshVideos = await fetchVideosFromSearchUrl([selectedUrl.url], true);
                    if (freshVideos && freshVideos.length > 0) {
                      refreshedVideos = freshVideos;
                      setVideos(refreshedVideos);
                    }

                    // Optionally refresh other URLs in the background (without switching views)
                    const otherUrls = searchUrls.filter(url => url.id !== selectedUrlId).map(url => url.url);
                    if (otherUrls.length > 0) {
                      console.log(`YTR Tab: Refreshing ${otherUrls.length} other URLs in background`);

                      // Clear cache for other URLs
                      otherUrls.forEach(url => {
                        localStorage.removeItem(`ytr_videos_${url}`);
                        localStorage.removeItem(`ytr_videos_timestamp_${url}`);
                      });

                      // Fetch other URLs in background with force refresh (don't await)
                      fetchVideosFromSearchUrl(otherUrls, true);
                    }
                  }
                } else {
                  // If we're on 'all' view, refresh all URLs
                  const allUrls = searchUrls.map(url => url.url);
                  console.log(`YTR Tab: Refreshing all ${allUrls.length} URLs for 'all' view`);

                  // Clear cache for all URLs to force fresh fetch
                  const combinedUrlKey = `combined_${allUrls.sort().join('_')}`;
                  localStorage.removeItem(`ytr_videos_${combinedUrlKey}`);
                  localStorage.removeItem(`ytr_videos_timestamp_${combinedUrlKey}`);

                  // Also clear individual URL caches
                  allUrls.forEach(url => {
                    localStorage.removeItem(`ytr_videos_${url}`);
                    localStorage.removeItem(`ytr_videos_timestamp_${url}`);
                  });

                  // Clear existing URL metrics to ensure fresh data
                  setChannelAnalysis(prev => ({
                    ...prev,
                    urlMetrics: {}
                  }));

                  // Show refresh toast
                  toast({
                    title: "Refreshing All URLs",
                    description: `Fetching fresh data and updating view counts for all ${searchUrls.length} URLs`,
                    duration: 3000
                  });

                  // Fetch all URLs with force refresh
                  const freshVideos = await fetchVideosFromSearchUrl(allUrls, true);
                  if (freshVideos && freshVideos.length > 0) {
                    refreshedVideos = freshVideos;
                    setVideos(refreshedVideos);
                  }
                }

                // Now refresh view counts for all videos using the same method as chart clicks
                console.log('YTR Tab: Refreshing view counts for all videos using chart click method');
                const successfulUpdates = await refreshAllViewCountsLikeChartClick(refreshedVideos);

                console.log(`YTR Tab: Successfully updated view counts for ${successfulUpdates} out of ${refreshedVideos.length} videos`);

                // Update channel analysis with the new view counts
                analyzeChannelPerformance(refreshedVideos, selectedUrlId === 'all' ? 'all' : searchUrls.find(url => url.id === selectedUrlId)?.url);

                // Force chart refresh by updating the cache timestamp
                setCacheUpdateTimestamp(Date.now());

                // Show completion toast
                toast({
                  title: "Refresh Complete",
                  description: `Updated view counts for ${successfulUpdates} videos`,
                  duration: 2000
                });

                return refreshedVideos;
              }}
              videos={videos}
              updatePreviousVphValues={updatePreviousVphValues}
              updateCache={updateCache}
              // Use a much longer interval for YTR tab to avoid rate limiting
              defaultInterval={3600} // 60 minutes
              compact={true} // Add a compact prop to make it smaller
            />
            </div>
          </div>
        )}

        {/* Videos Display - Compact Layout */}
        <div className="mt-2">
          {/* Action Bar - Only show when needed */}
          {(channelAnalysis.yourChannelVideos.length > 0 || selectedVideos.length > 0) && (
            <div className="flex justify-between items-center mb-2">
              {channelAnalysis.yourChannelVideos.length > 0 && (
                <div className="text-sm text-green-400">
                  {channelAnalysis.yourChannelVideos.length} from your channel
                </div>
              )}

              <div className="flex items-center gap-2 ml-auto">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-7 px-2 py-0 text-xs"
                  onClick={() => {
                    if (!channelAnalysis.showAnalysis) {
                      setChannelAnalysis({
                        ...channelAnalysis,
                        showAnalysis: true
                      });
                    } else {
                      setShowChannelPerformanceDialog(true);
                    }
                  }}
                >
                  <Activity className="h-3 w-3 mr-1" />
                  {!channelAnalysis.showAnalysis ? 'Show Analysis' : 'Channel Analysis'}
                </Button>

                {selectedVideos.length > 0 && (
                  <>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={copySelectedVideosInfo}
                    >
                      <ClipboardCopy className="h-4 w-4 mr-2" />
                      Copy {selectedVideos.length} {selectedVideos.length === 1 ? 'Video' : 'Videos'}
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedVideos([])}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Clear Selection
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Sorting Controls */}
          <div className="flex items-center gap-2 mb-2 p-2 bg-muted/20 rounded-md">
            <span className="text-xs text-white/70 font-medium">Sort by:</span>
            <Button
              variant={sortField === 'youtube' ? 'default' : 'outline'}
              size="sm"
              className="h-6 text-xs"
              onClick={() => toggleSort('youtube')}
              title="Show videos in the same order as YouTube search results"
            >
              YouTube Order
            </Button>
            <Button
              variant={sortField === 'publishedAt' ? 'default' : 'outline'}
              size="sm"
              className="h-6 text-xs"
              onClick={() => toggleSort('publishedAt')}
              title={`Sort by publish date (${sortField === 'publishedAt' && sortDirection === 'asc' ? 'oldest first' : 'newest first'})`}
            >
              Published
              {sortField === 'publishedAt' && (
                <span className="ml-1">
                  {sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />}
                </span>
              )}
            </Button>
            <Button
              variant={sortField === 'viewCount' ? 'default' : 'outline'}
              size="sm"
              className="h-6 text-xs"
              onClick={() => toggleSort('viewCount')}
              title={`Sort by view count (${sortField === 'viewCount' && sortDirection === 'asc' ? 'lowest first' : 'highest first'})`}
            >
              Views
              {sortField === 'viewCount' && (
                <span className="ml-1">
                  {sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />}
                </span>
              )}
            </Button>
          </div>

          {/* Column Headers */}
          <div className="grid grid-cols-[40px_1fr_110px_250px_50px_50px_100px] gap-2 items-center p-2 bg-muted/30 rounded-md text-xs font-medium text-white/70">
            <div className="text-center">Select</div>
            <div>Content</div>
            <div>Published</div>
            <div>Total Views (24h Chart)</div>
            <div className="text-center">T</div>
            <div className="text-center">X</div>
            <div className="text-center">Performance</div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading videos...</span>
            </div>
          ) : videos.length > 0 ? (
            <ScrollArea className="h-[calc(100vh-350px)]">
              <div className="space-y-2">
                {sortVideos(videos).map((video) => (
                  <div
                    key={video.id}
                    className={`grid grid-cols-[40px_1fr_110px_250px_50px_50px_100px] gap-2 items-center p-2 hover:bg-muted/30 rounded-md transition-colors
                      ${selectedVideos.some(v => v.id === video.id) ? 'bg-muted/50 border border-primary/30' : ''}
                      ${video.channelTitle === EXACT_CHANNEL_MATCH ? 'bg-green-500/20 border-l-2 border-l-green-500' : ''}
                      ${video.channelTitle === channelAnalysis.yourChannelName && video.channelTitle !== EXACT_CHANNEL_MATCH ? 'bg-blue-500/10 border border-blue-500/50' : ''}
                      ${eliminatedVideos.has(video.id) ? 'opacity-50 bg-red-500/10 border border-red-500/30' : ''}
                      ${
                      (() => {
                        // Check if video has positive momentum
                        if (previousVphValues[video.id]) {
                          const prevVphData = previousVphValues[video.id];
                          const previousVph = prevVphData.previousValue || 0;
                          const currentVph = video.vph || 0;
                          const momentumDelta = currentVph - previousVph;

                          // Use the exact same threshold as the Realtime tab (1% or 2 views, whichever is greater)
                          const momentumThreshold = Math.max(2, previousVph * 0.01);
                          const timeSincePrevious = prevVphData.timestamp - (prevVphData.previousTimestamp || 0);
                          const minutesSincePrevious = timeSincePrevious / (1000 * 60);

                          // Check if this video has momentum - only positive momentum counts
                          const hasMomentum = momentumDelta > 0 && Math.abs(momentumDelta) >= momentumThreshold && minutesSincePrevious >= 5;

                          // Store the momentum state in the prevVphData object to persist it
                          if (hasMomentum && !prevVphData.hasMomentum) {
                            // If it has momentum now but didn't before, update the state
                            setPreviousVphValues(prev => ({
                              ...prev,
                              [video.id]: {
                                ...prev[video.id],
                                hasMomentum: true
                              }
                            }));
                          } else if (!hasMomentum && prevVphData.hasMomentum) {
                            // If it had momentum before but doesn't anymore, reset the flag
                            setPreviousVphValues(prev => ({
                              ...prev,
                              [video.id]: {
                                ...prev[video.id],
                                hasMomentum: false
                              }
                            }));
                          }

                          // Return the green border if the video has positive momentum
                          if (hasMomentum) {
                            return 'border-2 border-green-500 bg-green-500/10';
                          }
                        }
                        return '';
                      })()
                    }`}
                    style={{ cursor: 'pointer' }}
                  >
                    {/* Checkbox column */}
                    <div
                      className="flex items-center justify-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (selectedVideos.some(v => v.id === video.id)) {
                          setSelectedVideos(selectedVideos.filter(v => v.id !== video.id));
                        } else {
                          setSelectedVideos([...selectedVideos, video]);
                        }
                      }}
                    >
                      <input
                        type="checkbox"
                        checked={selectedVideos.some(v => v.id === video.id)}
                        readOnly
                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                    </div>

                    {/* Content column (thumbnail + title) */}
                    <div
                      className="flex items-center gap-3 overflow-hidden"
                      onClick={() => window.open(`https://www.youtube.com/watch?v=${video.id}`, '_blank')}
                    >
                      <img
                        src={video.thumbnail || video.thumbnailUrl}
                        alt={video.title}
                        className="w-16 h-9 object-cover rounded-md flex-shrink-0"
                      />
                      <div className="min-w-0 overflow-hidden max-w-full">
                        <p className="font-medium text-sm truncate max-w-[calc(100%-40px)] text-white" title={video.title}>{video.title}</p>
                        <div className="flex items-center gap-1">
                          {(() => {
                            // Calculate analytics from this channel's perspective
                            const channelName = video.channelTitle;
                            const channelMetrics = channelAnalysis.allChannelMetrics?.find(c => c.name === channelName);

                            if (channelMetrics && channelAnalysis.allChannelMetrics) {
                              const totalMarketViews = channelAnalysis.allChannelMetrics.reduce((sum, ch) => sum + ch.totalViews, 0);
                              const thisChannelViews = channelMetrics.totalViews;
                              const competitorViews = totalMarketViews - thisChannelViews;
                              const marketShare = totalMarketViews > 0 ? ((thisChannelViews / totalMarketViews) * 100) : 0;
                              const rank = channelAnalysis.allChannelMetrics
                                .sort((a, b) => b.totalViews - a.totalViews)
                                .findIndex(c => c.name === channelName) + 1;

                              // Format views for display
                              const formatViews = (views) => {
                                if (views >= 10000) return `${(views/1000).toFixed(1)}K`;
                                return views.toString();
                              };

                              return (
                                <p
                                  className={`text-xs truncate max-w-[calc(100%-40px)] cursor-pointer transition-all hover:text-cyan-400 hover:font-medium ${
                                    channelName === EXACT_CHANNEL_MATCH ? 'text-green-400 font-medium' : 'text-white/70'
                                  }`}
                                  title={`${channelName}: ${marketShare.toFixed(1)}% market share, ${channelMetrics.videoCount} videos, ${formatViews(thisChannelViews)} ÷ ${formatViews(competitorViews)}, rank #${rank}`}
                                >
                                  {channelName}
                                </p>
                              );
                            }

                            return (
                              <p className={`text-xs truncate max-w-[calc(100%-40px)] ${channelName === EXACT_CHANNEL_MATCH ? 'text-green-400 font-medium' : 'text-white/70'}`} title={channelName}>
                                {channelName}
                              </p>
                            );
                          })()}

                          {/* URL Ranking Badges - Using stable memoized data to prevent flashing */}
                          <div className="flex flex-wrap items-center gap-1 ml-1">
                          {stableRankingData[video.id]?.map((ranking) => (
                            <Badge
                              key={`${video.id}-${ranking.url}`}
                              variant="outline"
                              className="flex-shrink-0 bg-gray-800 text-gray-200 border-gray-700 h-5 px-1 text-[10px] font-medium"
                              title={`Ranked #${ranking.rank} in ${ranking.urlName}`}
                            >
                              <span className="font-bold">#{ranking.rank}</span>{' '}
                              <span>{ranking.urlCode}</span>
                            </Badge>
                          ))}
                          </div>

                          {/* HowToGuys Badge */}
                          {video.channelTitle === EXACT_CHANNEL_MATCH && (
                            <Badge
                              variant="outline"
                              className="ml-1 flex-shrink-0 bg-green-100 text-green-800 border-green-200 h-5 px-1 text-[10px]"
                            >
                              <span className="font-bold">HTG</span>
                            </Badge>
                          )}

                          {/* Your Channel Badge */}
                          {video.channelTitle === channelAnalysis.yourChannelName && video.channelTitle !== EXACT_CHANNEL_MATCH && (
                            <Badge
                              variant="outline"
                              className="ml-1 flex-shrink-0 bg-blue-100 text-blue-800 border-blue-200 h-5 px-1 text-[10px]"
                            >
                              <span className="font-bold">YOU</span>
                            </Badge>
                          )}

                          {/* New Video Tag */}
                          {(() => {
                            const publishedDate = new Date(video.publishedAt);
                            const now = new Date();
                            const sixHoursAgo = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                            return publishedDate > sixHoursAgo ? (
                              <Badge
                                variant="outline"
                                className="ml-1 flex-shrink-0 bg-green-100 text-green-800 border-green-200 h-5 px-1 text-[10px]"
                              >
                                <span className="font-bold">N</span>
                              </Badge>
                            ) : null;
                          })()}
                        </div>
                      </div>
                    </div>

                    {/* Published date column */}
                    <div className="text-sm text-white whitespace-nowrap flex flex-col justify-center items-start h-9">
                      <div>
                        {video.publishedTimeText || "Recently"}
                      </div>
                      <div className="text-xs text-white/70">
                        1:01 AM
                      </div>
                    </div>

                    {/* Views column with graph - CLICKABLE */}
                    <div className="text-sm text-white whitespace-nowrap flex justify-start items-center h-9">
                      {isLiveStream(video.title || '', video.contentDetails?.duration) && !isEndedLiveStream(video.title || '', video.isUnplayable) ? (
                        <span className="text-red-500 font-medium flex items-center">
                          <Radio className="w-3 h-3 mr-1 text-red-500 animate-pulse" />
                          <div
                            className="flex items-center cursor-pointer hover:bg-red-500/20 rounded px-1 py-0.5 transition-colors relative"
                            onClick={(e) => {
                              e.stopPropagation();
                              refreshSingleVideoViewCount(video.id);
                            }}
                            title="Click to refresh view count"
                          >
                            {refreshingVideoId === video.id && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded">
                                <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                              </div>
                            )}
                            {(() => {
                              // Get view count from the most reliable source
                              let viewCount = 0;
                              if (video.statistics?.viewCount) {
                                viewCount = Number(video.statistics.viewCount);
                              } else if (video.viewCount) {
                                viewCount = typeof video.viewCount === 'string' ?
                                  Number(video.viewCount) : video.viewCount;
                              }

                              // Validate the view count is reasonable
                              if (isNaN(viewCount) || viewCount < 0 || viewCount > 10000000000) {
                                viewCount = 0;
                              }

                              return (
                                <>
                                  <span className="text-lg font-bold text-white mr-2">{viewCount.toLocaleString()}</span>
                                  <ViewHistoryGraph
                                    key={`${video.id}-${viewCount}-${cacheUpdateTimestamp}`}
                                    videoId={video.id}
                                    currentViews={viewCount}
                                    viewHistory={previousVphValues[video.id]?.viewHistory}
                                    height={18}
                                    width={120}
                                  />
                                </>
                              );
                            })()}
                          </div>
                        </span>
                      ) : (
                        <div
                          className="flex items-center cursor-pointer hover:bg-muted/30 rounded px-1 py-0.5 transition-colors relative"
                          onClick={(e) => {
                            e.stopPropagation();
                            refreshSingleVideoViewCount(video.id);
                          }}
                          title="Click to refresh view count"
                        >
                          {refreshingVideoId === video.id && (
                            <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded">
                              <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                            </div>
                          )}
                          {(() => {
                            // Get view count from the most reliable source
                            let viewCount = 0;
                            if (video.statistics?.viewCount) {
                              viewCount = Number(video.statistics.viewCount);
                            } else if (video.viewCount) {
                              viewCount = typeof video.viewCount === 'string' ?
                                Number(video.viewCount) : video.viewCount;
                            }

                            // Validate the view count is reasonable
                            if (isNaN(viewCount) || viewCount < 0 || viewCount > 10000000000) {
                              viewCount = 0;
                            }

                            return (
                              <>
                                <span className="text-lg font-bold text-white mr-2">{viewCount.toLocaleString()}</span>
                                <ViewHistoryGraph
                                  key={`${video.id}-${viewCount}-${cacheUpdateTimestamp}`}
                                  videoId={video.id}
                                  currentViews={viewCount}
                                  viewHistory={previousVphValues[video.id]?.viewHistory}
                                  height={18}
                                  width={120}
                                />
                              </>
                            );
                          })()}
                          {isEndedLiveStream(video.title || '', video.isUnplayable) && (
                            <span className="ml-1 text-xs text-gray-400">(ended)</span>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Transcript button column */}
                    <div className="flex items-center justify-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 flex items-center justify-center"
                        title="Copy Transcription (Pure Text)"
                        onClick={async (e) => {
                          e.stopPropagation();

                          try {
                            // Show loading toast
                            toast({
                              title: "Fetching Transcription",
                              description: "Retrieving the transcript from the server...",
                              duration: 3000
                            });

                            // Fetch transcription
                            console.log(`Fetching transcription for video ID: ${video.id}`);
                            const res = await fetch(`/api/youtube-channels/videos/${video.id}/transcription`);

                            if (!res.ok) {
                              console.error(`Failed to fetch transcription: ${res.status} ${res.statusText}`);
                              throw new Error(`Failed to fetch transcription: ${res.status} ${res.statusText}`);
                            }

                            const data = await res.json();
                            console.log('Transcription API response:', data);
                            let transcription = data.transcription;

                            // Check if we need to queue a transcription fetch
                            if (data.message && data.message.includes('queued')) {
                              toast({
                                title: "Fetching Transcription",
                                description: "Transcription is being fetched in the background. Please try again in a few moments.",
                                duration: 5000
                              });
                              return;
                            }

                            // Check if transcription is a fallback message or a simulated transcript
                            if (transcription.includes('Transcription not available') ||
                                transcription.includes('Error retrieving transcription') ||
                                transcription.includes('not found in your subscriptions') ||
                                transcription.includes('The actual transcript could not be retrieved') ||
                                transcription.includes('This is a simulated transcript')) {
                              // Try to queue a transcription fetch
                              await fetch('/api/tasks/fetch-transcription', {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ videoId: video.id })
                              });

                              toast({
                                title: "Fetching Transcription",
                                description: "Transcription is being fetched in the background. Please try again in a few moments.",
                                duration: 5000
                              });
                              return;
                            }

                            // Format the transcription for clipboard - pure transcript only
                            let formattedText = "";

                            // Process the transcription content
                            if (transcription && transcription.trim()) {
                              // Check if the transcription contains simulated content
                              if (transcription.includes('This is a simulated transcript')) {
                                // If it's a simulated transcript, just add a note that the actual transcript is not available
                                formattedText = "The actual transcript is not available for this video.";
                              } else {
                                // Clean up the transcription to get pure text only
                                let cleanTranscription = transcription
                                  .split('\n')
                                  .map(line => {
                                    // Remove timestamps like [00:00] or [00:00:00]
                                    return line.replace(/\[\d+:\d+(?::\d+)?\]\s*/g, '');
                                  })
                                  .filter(line => {
                                    // Filter out lines that are just URLs or empty
                                    const urlPattern = /^https?:\/\/[\w\-]+(\.[\w\-]+)+([\w\-.,@?^=%&:/~+#]*[\w\-@?^=%&/~+#])?$/;
                                    return line.trim() && !urlPattern.test(line.trim());
                                  });

                                // Remove any metadata lines that might be in the transcription
                                const metadataPatterns = [
                                  /^Title:/i,
                                  /^Author:/i,
                                  /^Description:/i,
                                  /^Timestamps:/i,
                                  /^To view this video/i,
                                  /^The video may cover/i,
                                  /^Note:/i,
                                  /^Transcription not available/i,
                                  /^Possible reasons:/i,
                                  /^The actual transcript could not be retrieved/i
                                ];

                                cleanTranscription = cleanTranscription.filter(line => {
                                  // Keep the line only if it doesn't match any of the metadata patterns
                                  return !metadataPatterns.some(pattern => pattern.test(line));
                                });

                                // Join the lines back together
                                formattedText = cleanTranscription.join('\n');
                              }
                            } else {
                              formattedText = "No transcript content available for this video.";
                            }

                            // Copy to clipboard
                            try {
                              await navigator.clipboard.writeText(formattedText);
                            } catch (clipboardError) {
                              console.error('Clipboard error:', clipboardError);
                              // Fallback method for clipboard
                              const textArea = document.createElement('textarea');
                              textArea.value = formattedText;
                              document.body.appendChild(textArea);
                              textArea.select();
                              const success = document.execCommand('copy');
                              document.body.removeChild(textArea);
                              if (!success) throw new Error('Failed to copy using fallback method');
                            }

                            // Show different toast messages based on whether it was a simulated transcript
                            if (transcription.includes('This is a simulated transcript')) {
                              toast({
                                title: "Transcription Unavailable",
                                description: "The actual transcript is not available for this video.",
                                duration: 3000
                              });
                            } else {
                              toast({
                                title: "Transcription Copied",
                                description: "Pure transcript text copied to clipboard (no timestamps or metadata).",
                                duration: 3000
                              });
                            }
                          } catch (error) {
                            console.error('Error copying transcription:', error);
                            toast({
                              title: "Copy Failed",
                              description: "Failed to copy transcription. Please try again.",
                              variant: "destructive"
                            });
                          }
                        }}
                      >
                        <span className="font-bold text-white text-base bg-gray-900 px-2 py-0.5 rounded-md border border-gray-600 shadow-sm hover:bg-gray-800 transition-colors">T</span>
                      </Button>
                    </div>

                    {/* Elimination button column */}
                    <div className="flex items-center justify-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`h-8 w-8 p-0 flex items-center justify-center transition-colors ${
                          eliminatedVideos.has(video.id)
                            ? 'text-green-400 hover:text-green-300'
                            : 'text-red-400 hover:text-red-300'
                        }`}
                        title={eliminatedVideos.has(video.id) ? "Restore video to calculations" : "Eliminate video from calculations"}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (onVideoElimination) {
                            onVideoElimination(video.id);
                          }
                        }}
                      >
                        {eliminatedVideos.has(video.id) ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                      </Button>
                    </div>

                    {/* Performance Metrics column */}
                    <div className="text-xs font-medium whitespace-nowrap flex flex-col justify-center h-9 gap-0.5">
                      {(() => {
                        // Get current view count
                        let viewCount = 0;
                        if (video.statistics?.viewCount) {
                          viewCount = Number(video.statistics.viewCount);
                        } else if (video.viewCount) {
                          viewCount = typeof video.viewCount === 'string' ?
                            Number(video.viewCount) : video.viewCount;
                        }

                        // Validate the view count
                        if (isNaN(viewCount) || viewCount < 0 || viewCount > 10000000000) {
                          viewCount = 0;
                        }

                        // 1. Search Ranking Score - Average position across URLs
                        const videoRankings: number[] = [];
                        if (channelAnalysis.urlMetrics) {
                          Object.values(channelAnalysis.urlMetrics).forEach(metrics => {
                            if (metrics.videoRankings && metrics.videoRankings[video.id]) {
                              videoRankings.push(metrics.videoRankings[video.id]);
                            }
                          });
                        }
                        const avgRanking = videoRankings.length > 0 ?
                          Math.round(videoRankings.reduce((sum, rank) => sum + rank, 0) / videoRankings.length) : null;

                        // 2. Momentum Indicator - View growth rate
                        let momentumIndicator = '→';
                        let momentumColor = 'text-gray-400';
                        if (previousVphValues[video.id]?.viewHistory && previousVphValues[video.id].viewHistory.length >= 2) {
                          const history = previousVphValues[video.id].viewHistory;
                          const recent = history.slice(-6); // Last 6 data points
                          const older = history.slice(-12, -6); // Previous 6 data points

                          if (recent.length > 0 && older.length > 0) {
                            const recentAvg = recent.reduce((sum, entry) => sum + entry.views, 0) / recent.length;
                            const olderAvg = older.reduce((sum, entry) => sum + entry.views, 0) / older.length;
                            const growthRate = ((recentAvg - olderAvg) / olderAvg) * 100;

                            if (growthRate > 5) {
                              momentumIndicator = '↗️';
                              momentumColor = 'text-green-400';
                            } else if (growthRate < -5) {
                              momentumIndicator = '↘️';
                              momentumColor = 'text-red-400';
                            }
                          }
                        }

                        // 3. Competition Score - Performance vs other videos
                        let competitionScore = 'Avg';
                        let competitionColor = 'text-gray-400';
                        if (videos.length > 1) {
                          const allViewCounts = videos.map(v => {
                            let vc = 0;
                            if (v.statistics?.viewCount) vc = Number(v.statistics.viewCount);
                            else if (v.viewCount) vc = typeof v.viewCount === 'string' ? Number(v.viewCount) : v.viewCount;
                            return isNaN(vc) ? 0 : vc;
                          }).filter(vc => vc > 0);

                          if (allViewCounts.length > 0) {
                            allViewCounts.sort((a, b) => b - a);
                            const position = allViewCounts.findIndex(vc => vc <= viewCount) + 1;
                            const percentile = (1 - position / allViewCounts.length) * 100;

                            if (percentile >= 80) {
                              competitionScore = 'Top';
                              competitionColor = 'text-green-400';
                            } else if (percentile >= 60) {
                              competitionScore = 'High';
                              competitionColor = 'text-blue-400';
                            } else if (percentile <= 20) {
                              competitionScore = 'Low';
                              competitionColor = 'text-red-400';
                            }
                          }
                        }

                        // 4. Search Dominance - Coverage across search URLs
                        const totalUrls = Object.keys(channelAnalysis.urlMetrics || {}).length;
                        const urlsWithVideo = videoRankings.length;
                        const dominancePercentage = totalUrls > 0 ? Math.round((urlsWithVideo / totalUrls) * 100) : 0;

                        // 5. Engagement Velocity - Views per day since publication
                        let engagementVelocity = 0;
                        let velocityDisplay = '0/day';
                        let velocityColor = 'text-gray-400';

                        if (video.publishedAt && viewCount > 0) {
                          try {
                            const publishDate = new Date(video.publishedAt);
                            const now = new Date();
                            const daysElapsed = Math.max(1, (now.getTime() - publishDate.getTime()) / (1000 * 60 * 60 * 24));
                            engagementVelocity = Math.round(viewCount / daysElapsed);

                            // Format the display
                            if (engagementVelocity >= 10000) {
                              velocityDisplay = `${(engagementVelocity / 1000).toFixed(1)}K/day`;
                            } else if (engagementVelocity >= 1000) {
                              velocityDisplay = `${(engagementVelocity / 1000).toFixed(1)}K/day`;
                            } else {
                              velocityDisplay = `${engagementVelocity}/day`;
                            }

                            // Color based on velocity (relative to video age)
                            if (daysElapsed <= 1) {
                              // New videos (less than 1 day old)
                              if (engagementVelocity >= 5000) velocityColor = 'text-green-400';
                              else if (engagementVelocity >= 1000) velocityColor = 'text-yellow-400';
                            } else if (daysElapsed <= 7) {
                              // Recent videos (1-7 days old)
                              if (engagementVelocity >= 2000) velocityColor = 'text-green-400';
                              else if (engagementVelocity >= 500) velocityColor = 'text-yellow-400';
                            } else {
                              // Older videos (7+ days old)
                              if (engagementVelocity >= 1000) velocityColor = 'text-green-400';
                              else if (engagementVelocity >= 200) velocityColor = 'text-yellow-400';
                            }
                          } catch (e) {
                            console.error('Error calculating engagement velocity:', e);
                          }
                        }

                        return (
                          <div className="flex flex-col items-center gap-0.5" title={`Ranking: ${avgRanking ? `#${avgRanking} avg` : 'Not ranked'} | Momentum: ${momentumIndicator} | Competition: ${competitionScore} | Coverage: ${dominancePercentage}% | Velocity: ${velocityDisplay}`}>
                            {/* Top row: Ranking + Momentum */}
                            <div className="flex items-center gap-1">
                              {avgRanking ? (
                                <span className={`font-bold ${avgRanking <= 3 ? 'text-green-400' : avgRanking <= 10 ? 'text-yellow-400' : 'text-gray-400'}`}>
                                  #{avgRanking}
                                </span>
                              ) : (
                                <span className="text-gray-500 text-[10px]">NR</span>
                              )}
                              <span className={`${momentumColor}`}>{momentumIndicator}</span>
                            </div>

                            {/* Middle row: Engagement Velocity */}
                            <div className={`text-[9px] ${velocityColor} font-medium`}>
                              {velocityDisplay}
                            </div>

                            {/* Bottom row: Competition + Dominance */}
                            <div className="flex items-center gap-1 text-[10px]">
                              <span className={competitionColor}>{competitionScore}</span>
                              <span className="text-gray-400">•</span>
                              <span className={`${dominancePercentage >= 80 ? 'text-green-400' : dominancePercentage >= 50 ? 'text-yellow-400' : 'text-gray-400'}`}>
                                {dominancePercentage}%
                              </span>
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              {selectedUrlId ?
                "No videos found. Try refreshing or selecting a different search URL." :
                "Add and select a YouTube search URL to see videos."}
            </div>
          )}
        </div>
      </div>

      {/* Channel Performance Analysis - Moved to End */}
      {channelAnalysis.showAnalysis && (
        <Card className="mt-4 bg-muted/20 border-muted/30">
          <CardContent className="p-3">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium flex items-center">
                <Activity className="h-4 w-4 mr-1 text-blue-400" />
                Channel Performance Analysis
              </h3>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 px-2 py-0 text-xs"
                  onClick={() => setShowChannelPerformanceDialog(true)}
                >
                  Detailed View
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 py-0 text-xs"
                  onClick={() => setChannelAnalysis({...channelAnalysis, showAnalysis: !channelAnalysis.showAnalysis})}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>

            {/* Compact Channel Performance Analysis */}
            <div className="flex flex-col space-y-2">
              {/* Top Row - Key Metrics in a compact horizontal layout */}
              <div className="flex flex-wrap gap-2">
                {/* HowToGuys Channel Quick Stats - Enhanced with Market Share */}
                <div className="bg-green-500/20 p-2 rounded-md border border-green-500/30 flex-1 min-w-[200px]">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-green-400">@HowToGuys</h4>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-green-400">{channelAnalysis.allChannelMetrics?.find(c => c.name === EXACT_CHANNEL_MATCH)?.videoCount || 0} videos</span>
                      <span className="text-xs text-green-400">#{channelAnalysis.allChannelMetrics?.sort((a, b) => b.totalViews - a.totalViews).findIndex(c => c.name === EXACT_CHANNEL_MATCH) + 1} rank</span>
                    </div>
                  </div>

                  {/* Market Share Analysis */}
                  {(() => {
                    const howToGuysMetrics = channelAnalysis.allChannelMetrics?.find(c => c.name === EXACT_CHANNEL_MATCH);
                    const totalMarketViews = channelAnalysis.allChannelMetrics?.reduce((sum, channel) => sum + channel.totalViews, 0) || 0;
                    const howToGuysViews = howToGuysMetrics?.totalViews || 0;
                    const marketShare = totalMarketViews > 0 ? ((howToGuysViews / totalMarketViews) * 100) : 0;

                    // Calculate competitive position
                    const totalChannels = channelAnalysis.allChannelMetrics?.length || 0;
                    const howToGuysRank = channelAnalysis.allChannelMetrics?.sort((a, b) => b.totalViews - a.totalViews).findIndex(c => c.name === EXACT_CHANNEL_MATCH) + 1;
                    const topTierThreshold = Math.ceil(totalChannels * 0.2); // Top 20%

                    let competitiveStatus = 'Emerging';
                    let statusColor = 'text-yellow-400';

                    if (howToGuysRank <= topTierThreshold && marketShare >= 15) {
                      competitiveStatus = 'Dominant';
                      statusColor = 'text-green-400';
                    } else if (howToGuysRank <= topTierThreshold || marketShare >= 10) {
                      competitiveStatus = 'Leading';
                      statusColor = 'text-blue-400';
                    } else if (marketShare >= 5) {
                      competitiveStatus = 'Competitive';
                      statusColor = 'text-yellow-400';
                    } else {
                      competitiveStatus = 'Growing';
                      statusColor = 'text-orange-400';
                    }

                    return (
                      <div className="mt-2">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs text-green-300">Market Share:</span>
                          <span className={`text-xs font-bold ${statusColor}`}>
                            {marketShare.toFixed(1)}% ({competitiveStatus})
                          </span>
                        </div>

                        {/* Market Share Bar */}
                        <div className="w-full bg-gray-700 rounded-full h-2 mb-1">
                          <div
                            className="bg-green-400 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min(marketShare, 100)}%` }}
                          ></div>
                        </div>

                        <div className="text-[10px] text-green-300 space-y-0.5">
                          <div className="flex justify-between">
                            <span>@HowToGuys:</span>
                            <span className="font-bold">{howToGuysViews.toLocaleString()} views</span>
                          </div>
                          <div className="flex justify-between">
                            <span>All Competitors:</span>
                            <span className="font-bold">{(totalMarketViews - howToGuysViews).toLocaleString()} views</span>
                          </div>
                          <div className="flex justify-between">
                            <span>TMCV (Max Competitor):</span>
                            <span className="font-bold text-red-400">
                              {(() => {
                                // Find the highest view count among all competitors (excluding HowToGuys)
                                const competitorChannels = channelAnalysis.allChannelMetrics?.filter(c => c.name !== EXACT_CHANNEL_MATCH) || [];
                                const maxCompetitorViews = competitorChannels.length > 0 ?
                                  Math.max(...competitorChannels.map(c => c.totalViews)) : 0;
                                const maxCompetitorName = competitorChannels.find(c => c.totalViews === maxCompetitorViews)?.name || 'N/A';
                                return `${maxCompetitorViews.toLocaleString()}`;
                              })()}
                            </span>
                          </div>
                          <div className="flex justify-between border-t border-green-500/30 pt-0.5">
                            <span>Total Market:</span>
                            <span className="font-bold text-green-400">{totalMarketViews.toLocaleString()} views</span>
                          </div>
                          <div className="flex justify-between text-[9px] text-gray-400">
                            <span>Gap to #1 Competitor:</span>
                            <span className={`font-bold ${(() => {
                              const competitorChannels = channelAnalysis.allChannelMetrics?.filter(c => c.name !== EXACT_CHANNEL_MATCH) || [];
                              const maxCompetitorViews = competitorChannels.length > 0 ?
                                Math.max(...competitorChannels.map(c => c.totalViews)) : 0;
                              const gap = howToGuysViews - maxCompetitorViews;
                              return gap >= 0 ? 'text-green-400' : 'text-red-400';
                            })()}`}>
                              {(() => {
                                const competitorChannels = channelAnalysis.allChannelMetrics?.filter(c => c.name !== EXACT_CHANNEL_MATCH) || [];
                                const maxCompetitorViews = competitorChannels.length > 0 ?
                                  Math.max(...competitorChannels.map(c => c.totalViews)) : 0;
                                const gap = howToGuysViews - maxCompetitorViews;
                                return gap >= 0 ? `+${gap.toLocaleString()}` : gap.toLocaleString();
                              })()}
                            </span>
                          </div>
                        </div>

                        {/* Enhanced Statistics Grid */}
                        <div className="mt-2 pt-2 border-t border-green-500/30">
                          <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 mb-3">
                            {/* Total Views */}
                            <div className="text-center bg-green-500/30 rounded-lg p-2 border border-green-400/30">
                              <div className="text-[10px] text-green-300 font-medium">Total Views</div>
                              <div className="text-sm font-bold text-green-100">{howToGuysViews.toLocaleString()}</div>
                            </div>

                            {/* TMCV - Total Max Competition Views */}
                            <div className="text-center bg-red-500/30 rounded-lg p-2 border border-red-400/30">
                              <div className="text-[10px] text-red-300 font-medium">TMCV</div>
                              <div className="text-sm font-bold text-red-100">
                                {(() => {
                                  const competitorChannels = channelAnalysis.allChannelMetrics?.filter(c => c.name !== EXACT_CHANNEL_MATCH) || [];
                                  const maxCompetitorViews = competitorChannels.length > 0 ?
                                    Math.max(...competitorChannels.map(c => c.totalViews)) : 0;
                                  return maxCompetitorViews.toLocaleString();
                                })()}
                              </div>
                              <div className="text-[8px] text-red-400">Max Competitor</div>
                            </div>

                            {/* Gap to #1 */}
                            <div className="text-center bg-purple-500/30 rounded-lg p-2 border border-purple-400/30">
                              <div className="text-[10px] text-purple-300 font-medium">Gap to #1</div>
                              <div className={`text-sm font-bold ${(() => {
                                const competitorChannels = channelAnalysis.allChannelMetrics?.filter(c => c.name !== EXACT_CHANNEL_MATCH) || [];
                                const maxCompetitorViews = competitorChannels.length > 0 ?
                                  Math.max(...competitorChannels.map(c => c.totalViews)) : 0;
                                const gap = howToGuysViews - maxCompetitorViews;
                                return gap >= 0 ? 'text-green-300' : 'text-red-300';
                              })()}`}>
                                {(() => {
                                  const competitorChannels = channelAnalysis.allChannelMetrics?.filter(c => c.name !== EXACT_CHANNEL_MATCH) || [];
                                  const maxCompetitorViews = competitorChannels.length > 0 ?
                                    Math.max(...competitorChannels.map(c => c.totalViews)) : 0;
                                  const gap = howToGuysViews - maxCompetitorViews;
                                  return gap >= 0 ? `+${gap.toLocaleString()}` : gap.toLocaleString();
                                })()}
                              </div>
                            </div>

                            {/* Market Potential */}
                            <div className="text-center bg-cyan-500/30 rounded-lg p-2 border border-cyan-400/30">
                              <div className="text-[10px] text-cyan-300 font-medium">Market Potential</div>
                              <div className="text-sm font-bold text-cyan-100">
                                {(() => {
                                  const competitorChannels = channelAnalysis.allChannelMetrics?.filter(c => c.name !== EXACT_CHANNEL_MATCH) || [];
                                  const maxCompetitorViews = competitorChannels.length > 0 ?
                                    Math.max(...competitorChannels.map(c => c.totalViews)) : 0;
                                  const potential = maxCompetitorViews > 0 ? ((maxCompetitorViews / howToGuysViews) * 100) : 100;
                                  return `${potential.toFixed(0)}%`;
                                })()}
                              </div>
                              <div className="text-[8px] text-cyan-400">vs Top Competitor</div>
                            </div>
                          </div>
                        </div>

                        {/* Interactive Market Share Chart - Enhanced and Larger */}
                        <div className="mt-2 pt-2 border-t border-green-500/30">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-green-300 font-medium">Market Share Trend Analysis (7 Days)</span>
                            <span className="text-[10px] text-green-400">Hover for daily details</span>
                          </div>
                          <div className="w-full bg-gray-900 rounded-lg p-4">
                            <MarketShareChart
                              channelName="@HowToGuys"
                              currentHowToGuysViews={howToGuysViews}
                              currentCompetitorViews={(() => {
                                // Calculate competitor views from ALL competitors (excluding @HowToGuys)
                                const competitorChannels = channelAnalysis.allChannelMetrics?.filter(c => c.name !== EXACT_CHANNEL_MATCH) || [];
                                return competitorChannels.reduce((sum, channel) => sum + channel.totalViews, 0);
                              })()}
                              currentMarketShare={marketShare}
                              height={220}
                              width={600}
                              maxDays={7}
                              showTooltip={true}
                              showDaySelector={true}
                            />
                          </div>
                        </div>

                        {/* Peak Timing Analysis */}
                        <div className="mt-2 pt-2 border-t border-green-500/30">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs text-green-300">Peak Timing Analysis (14 Days):</span>
                            <span className="text-[10px] text-green-400">Select day to view peaks</span>
                          </div>
                          <PeakTimingAnalysis
                            channelName="@HowToGuys"
                            currentViews={howToGuysViews}
                            currentMarketShare={marketShare}
                          />
                        </div>
                      </div>
                    );
                  })()}

                  {/* URL Performance for @HowToGuys - Compact horizontal scroll */}
                  {Object.keys(channelAnalysis.urlMetrics || {}).length > 0 ? (
                    <div className="mt-2">
                      <div className="text-xs text-green-300 mb-1">URL Performance:</div>
                      <div className="flex gap-1 overflow-x-auto pb-1 custom-scrollbar">
                        {Object.entries(channelAnalysis.urlMetrics || {})
                          .filter(([_, metrics]) => metrics.videoCount > 0) // Only show URLs where @HowToGuys has videos
                          .map(([url, metrics]) => {
                            // Generate short URL code
                            let urlCode = '';
                            if (metrics.urlName.toLowerCase().includes('social security')) {
                              urlCode = 'SS';
                            } else if (metrics.urlName.toLowerCase().includes('stimulus')) {
                              urlCode = 'ST';
                            } else if (metrics.urlName.toLowerCase().includes('payment')) {
                              urlCode = 'PAY';
                            } else if (metrics.urlName.toLowerCase().includes('check')) {
                              urlCode = 'CHK';
                            } else if (metrics.urlName.toLowerCase().includes('increase')) {
                              urlCode = 'INC';
                            } else if (metrics.urlName.toLowerCase().includes('benefit')) {
                              urlCode = 'BEN';
                            } else {
                              // Fallback: use first 2-3 letters of the first word
                              urlCode = metrics.urlName.split(' ')[0].substring(0, 3).toUpperCase();
                            }

                            // Calculate market share for this URL
                            const urlTotalViews = channelAnalysis.allChannelMetrics?.reduce((sum, channel) => {
                              // Only count views from videos that appear in this URL's results
                              return sum + channel.totalViews;
                            }, 0) || 0;

                            const marketShareForUrl = urlTotalViews > 0 ? ((metrics.totalViews / urlTotalViews) * 100) : 0;

                            return (
                              <div
                                key={url}
                                className="bg-green-500/30 rounded px-1 py-0.5 min-w-[50px] flex-shrink-0 text-center"
                                title={`${metrics.urlName}: ${metrics.videoCount} videos, ${metrics.totalViews} views, rank #${metrics.bestRanking || '-'}, ${marketShareForUrl.toFixed(1)}% market share`}
                              >
                                <div className="text-[9px] font-medium text-green-200">{urlCode}</div>
                                <div className="text-[8px] text-green-300">{metrics.videoCount}v</div>
                                <div className="text-[8px] text-green-300">{metrics.totalViews > 1000 ? `${(metrics.totalViews/1000).toFixed(1)}k` : metrics.totalViews}</div>
                                <div className="text-[7px] text-green-400 font-bold">{marketShareForUrl.toFixed(1)}%</div>
                              </div>
                            );
                          })}
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-3 gap-2 mt-2">
                      <div className="text-center bg-muted/30 rounded p-1">
                        <div className="text-xs text-muted-foreground">Rank</div>
                        <div className="text-sm font-medium">#{Math.min(...(channelAnalysis.allChannelMetrics?.find(c => c.name === EXACT_CHANNEL_MATCH)?.rankingPositions || [0]))}</div>
                      </div>
                      <div className="text-center bg-muted/30 rounded p-1">
                        <div className="text-xs text-muted-foreground">Views</div>
                        <div className="text-sm font-medium">{channelAnalysis.allChannelMetrics?.find(c => c.name === EXACT_CHANNEL_MATCH)?.totalViews ? channelAnalysis.allChannelMetrics.find(c => c.name === EXACT_CHANNEL_MATCH).totalViews.toLocaleString() : '0'}</div>
                      </div>
                      <div className="text-center bg-muted/30 rounded p-1">
                        <div className="text-xs text-muted-foreground">Avg</div>
                        <div className="text-sm font-medium">{channelAnalysis.allChannelMetrics?.find(c => c.name === EXACT_CHANNEL_MATCH)?.avgViews ? Math.round(channelAnalysis.allChannelMetrics.find(c => c.name === EXACT_CHANNEL_MATCH).avgViews).toLocaleString() : '0'}</div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Top Channels Summary */}
                <div className="bg-muted/20 p-2 rounded-md flex-1 min-w-[250px]">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-blue-400">Top Channels</h4>
                    <span className="text-xs text-muted-foreground">
                      {channelAnalysis.allChannelMetrics?.length || 0} total
                    </span>
                  </div>

                  {channelAnalysis.allChannelMetrics && channelAnalysis.allChannelMetrics.length > 0 ? (
                    <div className="grid grid-cols-1 gap-y-1 max-h-[120px] overflow-y-auto custom-scrollbar pr-1">
                      {channelAnalysis.allChannelMetrics
                        .sort((a, b) => b.totalViews - a.totalViews)
                        .slice(0, 10) // Show only top 10 channels
                        .map((channel, idx) => {
                          // Calculate analytics from this channel's perspective
                          const totalMarketViews = channelAnalysis.allChannelMetrics?.reduce((sum, ch) => sum + ch.totalViews, 0) || 0;
                          const thisChannelViews = channel.totalViews;
                          const competitorViews = totalMarketViews - thisChannelViews;
                          const marketShare = totalMarketViews > 0 ? ((thisChannelViews / totalMarketViews) * 100) : 0;
                          const rank = idx + 1;

                          // Format views for display
                          const formatViews = (views) => {
                            if (views >= 10000) return `${(views/1000).toFixed(1)}K`;
                            return views.toString();
                          };

                          return (
                            <div
                              key={idx}
                              className={`text-sm flex justify-between items-center py-1 px-2 rounded cursor-pointer transition-all ${
                                channel.name === EXACT_CHANNEL_MATCH
                                  ? 'bg-green-500/20 border-l-2 border-l-green-500 font-medium'
                                  : 'hover:bg-muted/30 hover:border-l-2 hover:border-l-blue-500'
                              }`}
                              title={`${channel.name}: ${marketShare.toFixed(1)}% market share, ${channel.videoCount} videos, ${formatViews(thisChannelViews)} ÷ ${formatViews(competitorViews)}, rank #${rank}`}
                            >
                              <div className="flex items-center gap-1">
                                <span className="font-medium">{rank}.</span>
                                <span className="truncate max-w-[120px]">
                                  {channel.name}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-muted-foreground">{channel.videoCount}v</span>
                                <span className="font-medium">{channel.totalViews.toLocaleString()}</span>
                                <span className="text-xs text-cyan-400 font-bold">{marketShare.toFixed(1)}%</span>
                              </div>
                            </div>
                          );
                        })
                      }
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground p-2 text-center">
                      No channel data available.
                    </div>
                  )}
                </div>
              </div>

              {/* Channel Ranking Trends - Front Side Display */}
              {rankingHistory.length > 0 && (
                <div className="bg-purple-500/10 p-2 rounded-md border border-purple-500/20">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-purple-400">Channel Ranking Trends (Last 12h)</h4>
                    <span className="text-xs text-muted-foreground">
                      {new Set(rankingHistory.map(r => r.channelName)).size} channels tracked
                    </span>
                  </div>

                  <div className="flex gap-2 overflow-x-auto pb-2 custom-scrollbar">
                    {/* Get unique combinations of channel and URL, prioritize @HowToGuys */}
                    {Array.from(new Set(rankingHistory.map(r => `${r.channelName}|${r.urlName}`)))
                      .sort((a, b) => {
                        // Prioritize @HowToGuys first
                        const aChannel = a.split('|')[0];
                        const bChannel = b.split('|')[0];
                        if (aChannel === '@HowToGuys' && bChannel !== '@HowToGuys') return -1;
                        if (bChannel === '@HowToGuys' && aChannel !== '@HowToGuys') return 1;
                        return a.localeCompare(b);
                      })
                      .slice(0, 6) // Show only top 6 combinations to avoid clutter
                      .map(combination => {
                        const [channelName, urlName] = combination.split('|');
                        const channelRankingData = rankingHistory.filter(r =>
                          r.channelName === channelName && r.urlName === urlName
                        );

                        return (
                          <div key={combination} className="bg-purple-500/20 rounded border border-purple-500/30 p-1 min-w-[180px] flex-shrink-0">
                            <ChannelRankingGraph
                              rankingHistory={channelRankingData}
                              channelName={channelName}
                              urlName={urlName}
                              height={60}
                              width={170}
                            />
                          </div>
                        );
                      })}
                  </div>

                  {rankingHistory.length === 0 && (
                    <div className="text-sm text-yellow-400 p-2 text-center">
                      No ranking history data available. Refresh URLs to start tracking.
                      <br />
                      <span className="text-xs text-gray-400">
                        Debug: {JSON.stringify({
                          rankingHistoryLength: rankingHistory.length,
                          localStorageKey: 'ytr_ranking_history',
                          hasLocalStorage: !!localStorage.getItem('ytr_ranking_history')
                        })}
                      </span>
                    </div>
                  )}
                </div>
              )}

              {/* Detailed Analysis Toggle Button */}
              <div className="flex justify-end">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 py-0 text-xs"
                  onClick={() => setChannelAnalysis({
                    ...channelAnalysis,
                    showDetailedAnalysis: !channelAnalysis.showDetailedAnalysis
                  })}
                >
                  {channelAnalysis.showDetailedAnalysis ? (
                    <>
                      <ChevronUp className="h-3 w-3 mr-1" />
                      Hide Details
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-3 w-3 mr-1" />
                      Show Details
                    </>
                  )}
                </Button>
              </div>

              {/* Collapsible Detailed Analysis */}
              {channelAnalysis.showDetailedAnalysis && (
                <div className="space-y-2">
                  {/* Channel Comparison Table - Compact Version */}
                  <div className="bg-muted/10 p-1.5 rounded-md">
                    <h4 className="text-xs font-medium mb-1 text-blue-400">Channel Comparison</h4>

                    <div className="overflow-x-auto">
                      <div className="max-h-[150px] overflow-y-auto pr-1 custom-scrollbar">
                        <table className="w-full text-xs">
                          <thead className="bg-muted/30 sticky top-0 z-10">
                            <tr>
                              <th className="text-left p-0.5 sticky top-0 bg-muted/30">Channel</th>
                              <th className="text-right p-0.5 sticky top-0 bg-muted/30">Videos</th>
                              <th className="text-right p-0.5 sticky top-0 bg-muted/30">Views</th>
                              <th className="text-right p-0.5 sticky top-0 bg-muted/30">Avg</th>
                              <th className="text-right p-0.5 sticky top-0 bg-muted/30">Rank</th>
                            </tr>
                          </thead>
                          <tbody>
                            {channelAnalysis.allChannelMetrics?.map((channel, idx) => (
                              <tr
                                key={idx}
                                className={`border-b border-muted/10
                                  ${channel.name === EXACT_CHANNEL_MATCH
                                    ? 'bg-green-500/20 border-l-2 border-l-green-500 font-medium'
                                    : (channel.name === channelAnalysis.yourChannelName && channel.name !== EXACT_CHANNEL_MATCH
                                      ? 'bg-blue-500/10'
                                      : '')
                                  }`}
                              >
                                <td className="p-0.5 truncate max-w-[120px]" title={channel.name}>{channel.name}</td>
                                <td className="p-0.5 text-right">{channel.videoCount}</td>
                                <td className="p-0.5 text-right">{(channel.totalViews/1000).toFixed(1)}K</td>
                                <td className="p-0.5 text-right">{Math.round(channel.avgViews).toLocaleString()}</td>
                                <td className="p-0.5 text-right">#{Math.min(...channel.rankingPositions)}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  {/* URL Details - Compact Grid */}
                  <div className="bg-muted/10 p-1.5 rounded-md">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="text-xs font-medium text-green-400">URL Details</h4>
                      <span className="text-xs text-muted-foreground">
                        {Object.keys(channelAnalysis.urlMetrics || {}).filter(url =>
                          channelAnalysis.urlMetrics[url].videoCount > 0
                        ).length} active URLs
                      </span>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 max-h-[150px] overflow-y-auto custom-scrollbar pr-1">
                      {Object.keys(channelAnalysis.urlMetrics || {}).length > 0 ? (
                        Object.entries(channelAnalysis.urlMetrics || {})
                          .filter(([_, metrics]) => metrics.videoCount > 0)
                          .map(([url, metrics]) => (
                          <div
                            key={url}
                            className="text-xs p-1 bg-green-500/10 rounded border border-green-500/20"
                          >
                            <div className="flex justify-between items-center">
                              <div className="font-medium text-green-300 truncate max-w-[70%]" title={metrics.urlName}>{metrics.urlName}</div>
                              <div className="text-[10px] text-muted-foreground">{metrics.videoCount} videos</div>
                            </div>
                            <div className="flex justify-between mt-0.5 text-[10px]">
                              <div>Rank: <span className="font-medium">#{metrics.bestRanking}</span></div>
                              <div>Views: <span className="font-medium">{(metrics.totalViews/1000).toFixed(1)}K</span></div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-xs text-yellow-400 p-1 col-span-2 text-center">
                          Click refresh to analyze all URLs
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Your Channel Stats - If Available */}
                  {channelAnalysis.yourChannelMetrics && (
                    <div className="bg-blue-500/10 p-1.5 rounded-md border border-blue-500/20">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="text-xs font-medium text-blue-400">Your Channel: {channelAnalysis.yourChannelName}</h4>
                        <span className="text-xs text-blue-400">{channelAnalysis.yourChannelMetrics.videoCount} videos</span>
                      </div>

                      <div className="flex flex-wrap gap-1 text-xs">
                        <div className="bg-muted/30 p-1 rounded flex-1 min-w-[80px]">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Views:</span>
                            <span className="font-medium">{(channelAnalysis.yourChannelMetrics.totalViews/1000).toFixed(1)}K</span>
                          </div>
                        </div>
                        <div className="bg-muted/30 p-1 rounded flex-1 min-w-[80px]">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Avg:</span>
                            <span className="font-medium">{Math.round(channelAnalysis.yourChannelMetrics.avgViews).toLocaleString()}</span>
                          </div>
                        </div>
                        <div className="bg-muted/30 p-1 rounded flex-1 min-w-[80px]">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">VPH:</span>
                            <span className="font-medium">{Math.round(channelAnalysis.yourChannelMetrics.avgVph).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="mt-2 text-xs text-muted-foreground">
                <p>
                  <span className="font-medium">Performance Tips:</span> Higher rankings and view counts lead to more visibility.
                  Channels with higher views per video are outperforming others in engagement.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Channel Performance Dialog */}
      {channelAnalysis.allChannelMetrics && (
        <ChannelPerformanceDialog
          isOpen={showChannelPerformanceDialog}
          onClose={() => setShowChannelPerformanceDialog(false)}
          channelMetrics={channelAnalysis.allChannelMetrics}
          urlMetrics={channelAnalysis.urlMetrics || {}}
          yourChannelName={channelAnalysis.yourChannelName}
          trackedChannelName={EXACT_CHANNEL_MATCH}
          rankingHistory={rankingHistory}
          videos={videos}
        />
      )}
    </div>
  );
}
