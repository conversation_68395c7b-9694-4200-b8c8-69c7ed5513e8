/**
 * This is a compatibility layer for code that still uses openRouterPromptsService.
 * All calls are redirected to the new promptsService.
 *
 * New code should use promptsService directly.
 */

import { OpenRouterPrompt } from '@shared/schema';
import {
  getPrompts,
  getPrompt,
  createPrompt,
  updatePrompt,
  deletePrompt,
  setDefaultPrompt
} from './promptsService';

/**
 * Get all OpenRouter prompts for the current user
 */
export async function getOpenRouterPrompts(): Promise<OpenRouterPrompt[]> {
  return await getPrompts();
}

/**
 * Get a specific OpenRouter prompt by ID
 */
export async function getOpenRouterPrompt(promptId: number): Promise<OpenRouterPrompt> {
  return await getPrompt(promptId);
}

/**
 * Create a new OpenRouter prompt
 */
export async function createOpenRouterPrompt(
  name: string,
  promptText: string,
  systemPrompt: string = '', // Kept for backward compatibility but not used
  isDefault: boolean = false
): Promise<OpenRouterPrompt> {
  return await createPrompt(name, promptText, isDefault);
}

/**
 * Update an existing OpenRouter prompt
 */
export async function updateOpenRouterPrompt(
  promptId: number,
  updates: Partial<OpenRouterPrompt>
): Promise<OpenRouterPrompt> {
  return await updatePrompt(promptId, updates);
}

/**
 * Delete an OpenRouter prompt
 */
export async function deleteOpenRouterPrompt(promptId: number): Promise<void> {
  await deletePrompt(promptId);
}

/**
 * Set a prompt as the default for the current user
 */
export async function setDefaultOpenRouterPrompt(promptId: number): Promise<void> {
  await setDefaultPrompt(promptId);
}
