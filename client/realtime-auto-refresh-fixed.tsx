import { useState, useEffect, useCallback, useRef } from "react";
import { toast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { RefreshCw, Pause, Clock } from "lucide-react";
import { logger, LogLevel } from '@/lib/logger';

interface AutoRefreshProps {
  isActive: boolean; // Is the realtime tab active
  onRefresh: () => Promise<void>; // Function to call for refreshing data
  videos: any[]; // Current videos being displayed
  updatePreviousVphValues: (videos: any[], isPreRefresh: boolean) => void;
  updateCache: (videos: any[]) => void;
}

export function RealtimeAutoRefresh({
  isActive,
  onRefresh,
  videos,
  updatePreviousVphValues,
  updateCache
}: AutoRefreshProps) {
  // State for auto-refresh settings
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState<boolean>(() => {
    // Try to load auto-refresh state from localStorage
    const savedState = localStorage.getItem('realtimeAutoRefreshEnabled');
    // If there's a saved state, use it; otherwise, default to true
    return savedState !== null ? savedState === 'true' : true;
  });

  const [autoRefreshInterval, setAutoRefreshInterval] = useState<number>(() => {
    // Try to load auto-refresh interval from localStorage
    const savedInterval = localStorage.getItem('realtimeAutoRefreshInterval');
    // If there's a saved interval, use it; otherwise, default to 120 seconds (2 minutes)
    return savedInterval ? parseInt(savedInterval) : 120;
  });

  // State for tracking refresh status
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const [lastRefreshTimestamp, setLastRefreshTimestamp] = useState(0);
  const [autoRefreshIntervalId, setAutoRefreshIntervalId] = useState<NodeJS.Timeout | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [pauseEndTime, setPauseEndTime] = useState<Date | null>(null);
  const [debugMode, setDebugMode] = useState(false);
  const [highContrastMode, setHighContrastMode] = useState(true); // Enable high contrast by default

  // New countdown implementation
  const [nextRefreshTime, setNextRefreshTime] = useState<Date | null>(null);
  const [countdownDisplay, setCountdownDisplay] = useState("--:--");

  // Reference for the countdown timer
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Track if a refresh is in progress to prevent multiple refreshes
  const refreshInProgressRef = useRef(false);

  // Function to perform the refresh with throttling
  const handleRefresh = useCallback(async () => {
    const now = Date.now();
    const minTimeBetweenRefreshes = 20000; // 20 seconds minimum between manual refreshes to prevent refresh loops and UI glitches

    // Check if a refresh is already in progress using the ref
    if (refreshInProgressRef.current) {
      logger.verbose("Refresh already in progress (tracked by ref), skipping");
      return;
    }

    // Prevent rapid successive refreshes
    if (now - lastRefreshTimestamp < minTimeBetweenRefreshes) {
      const waitTime = Math.ceil((minTimeBetweenRefreshes - (now - lastRefreshTimestamp)) / 1000);
      toast({
        title: "Please wait",
        description: `You can refresh again in ${waitTime} seconds`,
        duration: 3000
      });
      return;
    }

    // Prevent refresh if already refreshing
    if (isRefreshing) {
      logger.verbose("Refresh already in progress (tracked by state), skipping");
      return;
    }

    // Set both state and ref to indicate refresh is in progress
    setIsRefreshing(true);
    refreshInProgressRef.current = true;
    setLastRefreshTimestamp(now);

    try {
      logger.verbose("Starting refresh operation...");

      // Store current VPH values before refreshing
      if (videos.length > 0) {
        // Make a copy of the videos array to avoid mutation issues
        const videosCopy = [...videos];
        updatePreviousVphValues(videosCopy, true);
        updateCache(videosCopy);
      }

      // Perform the refresh with a single operation
      // This will be handled by the parent component
      logger.verbose("Calling parent refresh function");
      await onRefresh();
      logger.verbose("Parent refresh function completed");

      // Update last refresh time
      const refreshTime = new Date();
      setLastRefreshTime(refreshTime);

      // Set the next refresh time after successful refresh
      if (autoRefreshEnabled && isActive && !isPaused) {
        // Wait a short delay to ensure the refresh is complete before starting the countdown
        setTimeout(() => {
          // Calculate the next refresh time
          const now = new Date();
          const nextTime = new Date(now.getTime() + autoRefreshInterval * 1000);

          // Set the next refresh time
          setNextRefreshTime(nextTime);

          // Calculate and format the countdown display
          const totalSeconds = autoRefreshInterval;
          const minutes = Math.floor(totalSeconds / 60);
          const seconds = totalSeconds % 60;
          const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;

          // Update the countdown display
          setCountdownDisplay(display);

          logger.verbose(`Set next refresh time to: ${nextTime.toLocaleTimeString()} (${display} from now)`);

          // Clear any existing countdown timer
          if (countdownTimerRef.current) {
            clearInterval(countdownTimerRef.current);
            countdownTimerRef.current = null;
          }

          // Set up a new countdown timer
          const updateCountdown = () => {
            const currentTime = new Date();
            const diffMs = nextTime.getTime() - currentTime.getTime();

            if (diffMs <= 0) {
              // Time's up, clear the timer
              if (countdownTimerRef.current) {
                clearInterval(countdownTimerRef.current);
                countdownTimerRef.current = null;
              }
              return;
            }

            // Calculate minutes and seconds
            const remainingSeconds = Math.floor(diffMs / 1000);
            const mins = Math.floor(remainingSeconds / 60);
            const secs = remainingSeconds % 60;

            // Format the countdown display
            const newDisplay = `${mins}:${secs.toString().padStart(2, '0')}`;
            setCountdownDisplay(newDisplay);
          };

          // Update immediately
          updateCountdown();

          // Set up interval for updates
          const timer = setInterval(updateCountdown, 1000);
          countdownTimerRef.current = timer;
        }, 500);
      }

      // Delay the toast to prevent UI overload
      // This ensures the toast appears after the UI has stabilized
      setTimeout(() => {
        toast({
          title: "Refresh Complete",
          description: `Updated video data at ${refreshTime.toLocaleTimeString()}`,
          duration: 3000
        });
      }, 1000);
    } catch (error) {
      logger.error("Refresh failed:", error);
      toast({
        title: "Refresh Failed",
        description: "Could not update video data. Please try again.",
        variant: "destructive",
        duration: 5000
      });
    } finally {
      // Clear the refresh in progress indicators with a shorter delay
      // This ensures the UI has time to stabilize but doesn't block the next refresh for too long
      setTimeout(() => {
        logger.verbose("Clearing refresh in progress state");
        setIsRefreshing(false);
        refreshInProgressRef.current = false;
      }, 1000);
    }
  }, [lastRefreshTimestamp, videos, onRefresh, updatePreviousVphValues, updateCache, isRefreshing, autoRefreshEnabled, isActive, isPaused, autoRefreshInterval]);

  // Function to handle auto-refresh toggle
  const handleAutoRefreshToggle = useCallback((enabled: boolean) => {
    if (isPaused && enabled) {
      toast({
        title: "Auto-refresh is paused",
        description: "Resume auto-refresh to enable it again",
        duration: 3000
      });
      return;
    }

    logger.verbose(`${enabled ? 'Enabling' : 'Disabling'} auto-refresh`);
    console.log(`Auto-refresh toggle: ${enabled ? 'ON' : 'OFF'}`);
    setAutoRefreshEnabled(enabled);

    // Save to localStorage
    localStorage.setItem('realtimeAutoRefreshEnabled', enabled.toString());

    // Clear any existing interval
    if (autoRefreshIntervalId) {
      logger.verbose("Clearing existing auto-refresh interval");
      clearInterval(autoRefreshIntervalId);
      setAutoRefreshIntervalId(null);
    }

    // Clear any existing countdown timer
    if (countdownTimerRef.current) {
      logger.verbose("Clearing existing countdown timer");
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }

    // Reset countdown and set next refresh time
    if (enabled) {
      // Calculate the next refresh time
      const now = new Date();
      const nextTime = new Date(now.getTime() + autoRefreshInterval * 1000);

      // Set the next refresh time
      setNextRefreshTime(nextTime);

      // Calculate and format the countdown display
      const totalSeconds = autoRefreshInterval;
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;

      // Update the countdown display
      setCountdownDisplay(display);

      logger.verbose(`Set next refresh time to: ${nextTime.toLocaleTimeString()} (${display} from now)`);

      // Immediately set up a new refresh interval
      if (isActive && !isPaused) {
        logger.verbose(`Setting up new auto-refresh interval: ${autoRefreshInterval} seconds`);

        // Create a new interval for auto-refresh
        console.log(`Setting up new auto-refresh interval with ${autoRefreshInterval} seconds`);
        const newIntervalId = setInterval(() => {
          // Only proceed if not already refreshing
          if (!refreshInProgressRef.current) {
            logger.verbose(`Auto-refresh interval triggered at ${new Date().toLocaleTimeString()}`);
            handleRefresh().catch(err => {
              logger.error("Error during auto-refresh:", err);
            });
          } else {
            logger.verbose("Auto-refresh interval triggered but refresh already in progress, skipping");
          }
        }, autoRefreshInterval * 1000);

        setAutoRefreshIntervalId(newIntervalId);

        // Perform an initial refresh immediately
        console.log("Scheduling initial refresh after enabling auto-refresh");
        setTimeout(() => {
          if (!refreshInProgressRef.current) {
            logger.verbose("Performing initial refresh after enabling auto-refresh");
            console.log("Executing initial refresh");
            handleRefresh().catch(err => {
              logger.error("Error during initial auto-refresh:", err);
              console.error("Initial refresh error:", err);
            });
          } else {
            console.log("Skipping initial refresh - refresh already in progress");
          }
        }, 100);
      }

      // Show a toast to confirm auto-refresh is enabled
      toast({
        title: "Auto-refresh Enabled",
        description: `Will refresh every ${autoRefreshInterval} seconds`,
        duration: 3000
      });
    } else {
      // Show a toast to confirm auto-refresh is disabled
      toast({
        title: "Auto-refresh Disabled",
        description: "Manual refresh only",
        duration: 3000
      });
    }

    // Log the change
    logger.verbose(`Auto-refresh ${enabled ? 'enabled' : 'disabled'} with interval: ${autoRefreshInterval} seconds`);
  }, [autoRefreshInterval, autoRefreshIntervalId, isPaused, isActive, handleRefresh]);

  // Function to handle interval change
  const handleIntervalChange = useCallback((value: string) => {
    if (value === "manual") {
      // If manual is selected, disable auto-refresh
      if (autoRefreshEnabled) {
        handleAutoRefreshToggle(false);
      }
      return;
    }

    const seconds = parseInt(value);
    logger.verbose(`Changing auto-refresh interval from ${autoRefreshInterval} to ${seconds} seconds`);

    setAutoRefreshInterval(seconds);

    // Save to localStorage
    localStorage.setItem('realtimeAutoRefreshInterval', seconds.toString());

    // Clear any existing countdown timer
    if (countdownTimerRef.current) {
      logger.verbose("Clearing existing countdown timer during interval change");
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }

    // Reset countdown and set next refresh time with new interval
    if (autoRefreshEnabled) {
      // Calculate the next refresh time
      const now = new Date();
      const nextTime = new Date(now.getTime() + seconds * 1000);

      // Set the next refresh time
      setNextRefreshTime(nextTime);

      // Calculate and format the countdown display
      const totalSeconds = seconds;
      const minutes = Math.floor(totalSeconds / 60);
      const secondsDisplay = totalSeconds % 60;
      const display = `${minutes}:${secondsDisplay.toString().padStart(2, '0')}`;

      // Update the countdown display
      setCountdownDisplay(display);

      logger.verbose(`Set next refresh time to: ${nextTime.toLocaleTimeString()} (${display} from now)`);
    }

    // If auto-refresh is enabled, restart the interval with the new value
    if (autoRefreshEnabled) {
      logger.verbose("Restarting auto-refresh with new interval");
      handleAutoRefreshToggle(false); // Stop current interval

      // Use a short timeout to ensure state updates have propagated
      setTimeout(() => {
        handleAutoRefreshToggle(true); // Start new interval on next tick

        // Force update the countdown display after a short delay
        setTimeout(() => {
          // Calculate the next refresh time
          const now = new Date();
          const nextTime = new Date(now.getTime() + seconds * 1000);

          // Set the next refresh time
          setNextRefreshTime(nextTime);

          // Calculate and format the countdown display
          const totalSeconds = seconds;
          const minutes = Math.floor(totalSeconds / 60);
          const secondsDisplay = totalSeconds % 60;
          const display = `${minutes}:${secondsDisplay.toString().padStart(2, '0')}`;

          // Update the countdown display
          setCountdownDisplay(display);

          logger.verbose(`Forced countdown update after interval change: ${display}`);
        }, 100);
      }, 100);
    }

    // Show a toast to confirm the change
    toast({
      title: "Auto-refresh Interval Changed",
      description: `Set to ${seconds} seconds`,
      duration: 3000
    });

    logger.verbose(`Auto-refresh interval changed to ${seconds} seconds`);
  }, [autoRefreshEnabled, handleAutoRefreshToggle, autoRefreshInterval]);

  // Function to calculate suggested refresh interval based on video activity
  // This only returns a suggestion, it doesn't override the user's setting
  const calculateSuggestedRefreshInterval = useCallback((videos) => {
    if (!videos || videos.length === 0) return autoRefreshInterval;

    // Check if any videos have high VPH (views per hour)
    const hasHighActivityVideos = videos.some(video => video.vph > 1000);
    const hasModerateActivityVideos = videos.some(video => video.vph > 500);

    if (hasHighActivityVideos) {
      return 60; // Suggest 1 minute for high activity
    } else if (hasModerateActivityVideos) {
      return 120; // Suggest 2 minutes for moderate activity
    }

    return autoRefreshInterval; // Use user setting for low activity
  }, [autoRefreshInterval]);

  // Function to temporarily pause auto-refresh
  const handlePauseAutoRefresh = useCallback((minutes: number) => {
    const wasEnabled = autoRefreshEnabled;

    // Disable auto-refresh
    handleAutoRefreshToggle(false);
    setIsPaused(true);

    // Calculate and set pause end time
    const endTime = new Date();
    endTime.setMinutes(endTime.getMinutes() + minutes);
    setPauseEndTime(endTime);

    toast({
      title: `Auto-refresh paused for ${minutes} minutes`,
      description: `It will resume at ${endTime.toLocaleTimeString()}`,
      duration: 5000
    });

    // Resume after the pause period
    const pauseTimer = setTimeout(() => {
      setIsPaused(false);
      setPauseEndTime(null);

      if (wasEnabled) {
        handleAutoRefreshToggle(true);
        toast({
          title: "Auto-refresh resumed",
          description: `Continuing with ${autoRefreshInterval} second interval`,
          duration: 3000
        });
      }
    }, minutes * 60 * 1000);

    // Store the timer ID for cleanup
    return pauseTimer;
  }, [autoRefreshEnabled, autoRefreshInterval, handleAutoRefreshToggle]);

  // Main effect for auto-refresh functionality - completely rewritten for reliability
  useEffect(() => {
    // Always clean up any existing interval first to prevent duplicates
    if (autoRefreshIntervalId) {
      logger.verbose(`Cleaning up existing auto-refresh interval ID: ${autoRefreshIntervalId}`);
      clearInterval(autoRefreshIntervalId);
      setAutoRefreshIntervalId(null);
    }

    // Only proceed if all conditions are met
    if (!isActive || isPaused || !autoRefreshEnabled || autoRefreshInterval <= 0) {
      logger.verbose("Not setting up auto-refresh interval - conditions not met");
      return;
    }

    logger.verbose(`Setting up NEW auto-refresh with interval: ${autoRefreshInterval} seconds`);

    // Perform initial refresh if needed
    const shouldPerformInitialRefresh = videos.length > 0;
    const lastRefresh = lastRefreshTime ? lastRefreshTime.getTime() : 0;
    const timeSinceLastRefresh = Date.now() - lastRefresh;

    // Only trigger initial refresh if it's been significantly longer than the interval
    if (shouldPerformInitialRefresh && (!lastRefreshTime || timeSinceLastRefresh > autoRefreshInterval * 1000 * 0.5)) {
      logger.verbose("Scheduling initial refresh for auto-refresh");

      // Use setTimeout to break the potential render cycle
      setTimeout(() => {
        if (!refreshInProgressRef.current) {
          logger.verbose("Executing initial auto-refresh");
          handleRefresh().catch(err => {
            logger.error("Error during initial auto-refresh:", err);
          });
        } else {
          logger.verbose("Skipping initial refresh - refresh already in progress");
        }
      }, 1000); // Slightly longer delay for initial refresh
    }

    // Create a new interval for periodic refreshes
    const newIntervalId = setInterval(() => {
      // Skip if refresh is already in progress
      if (refreshInProgressRef.current) {
        logger.verbose("Auto-refresh interval triggered but refresh already in progress, skipping");
        return;
      }

      // Skip if conditions have changed
      if (!isActive || !autoRefreshEnabled || isPaused) {
        logger.verbose("Auto-refresh interval triggered but conditions no longer valid, skipping");
        return;
      }

      logger.verbose(`Auto-refresh interval triggered at ${new Date().toLocaleTimeString()}`);

      // Add a small random delay to prevent synchronization issues
      const randomDelay = Math.floor(Math.random() * 300); // 0-300ms random delay

      setTimeout(() => {
        // Double-check conditions again after delay
        if (!refreshInProgressRef.current && isActive && autoRefreshEnabled && !isPaused) {
          logger.verbose("Executing scheduled auto-refresh");
          handleRefresh().catch(err => {
            logger.error("Error during scheduled auto-refresh:", err);
          });
        } else {
          logger.verbose("Skipping scheduled auto-refresh - conditions changed");
        }
      }, randomDelay);
    }, autoRefreshInterval * 1000);

    // Store the new interval ID
    logger.verbose(`New auto-refresh interval set with ID: ${newIntervalId}`);
    setAutoRefreshIntervalId(newIntervalId);

    // Clean up on unmount or when dependencies change
    return () => {
      logger.verbose(`Cleaning up auto-refresh interval ID: ${newIntervalId}`);
      clearInterval(newIntervalId);
    };
  }, [isActive, isPaused, autoRefreshEnabled, autoRefreshInterval, videos.length, handleRefresh, lastRefreshTime]);

  // New countdown timer implementation
  useEffect(() => {
    // Always clear any existing timer first to prevent duplicates
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
      logger.verbose("Cleared existing countdown timer");
    }

    // Only set up timer if auto-refresh is enabled and we're on the active tab
    if (!autoRefreshEnabled || !isActive || isPaused) {
      logger.verbose("Not setting up countdown timer - conditions not met");
      setCountdownDisplay("--:--");
      return;
    }

    logger.verbose("Setting up new countdown timer");

    // Calculate the next refresh time based on the current time and interval
    const calculateNextRefreshTime = () => {
      const now = new Date();
      const nextTime = new Date(now.getTime() + autoRefreshInterval * 1000);
      return nextTime;
    };

    // Set the next refresh time if it's not already set or if a refresh is in progress
    if (!nextRefreshTime || refreshInProgressRef.current) {
      const newNextRefreshTime = calculateNextRefreshTime();
      setNextRefreshTime(newNextRefreshTime);
      logger.verbose(`Setting next refresh time to: ${newNextRefreshTime.toLocaleTimeString()}`);

      // Also update the countdown display immediately
      const totalSeconds = autoRefreshInterval;
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      setCountdownDisplay(display);
    }

    // Function to update the countdown display
    const updateCountdown = () => {
      try {
        // Safety check - if nextRefreshTime is not set, set it now
        if (!nextRefreshTime) {
          const newNextRefreshTime = calculateNextRefreshTime();
          setNextRefreshTime(newNextRefreshTime);
          logger.verbose(`Setting missing next refresh time to: ${newNextRefreshTime.toLocaleTimeString()}`);
          return; // Return early and let the next tick handle the countdown
        }

        const now = new Date();
        const diffMs = nextRefreshTime.getTime() - now.getTime();

        if (diffMs <= 0) {
          // Time's up, trigger a refresh if not already in progress
          if (!refreshInProgressRef.current) {
            logger.verbose("Countdown reached 0, triggering refresh");
            handleRefresh().catch(err => {
              logger.error("Error during countdown-triggered refresh:", err);
            });
          }

          // Calculate the next refresh time
          const newNextRefreshTime = calculateNextRefreshTime();
          setNextRefreshTime(newNextRefreshTime);
          logger.verbose(`Setting next refresh time to: ${newNextRefreshTime.toLocaleTimeString()}`);

          // Update the countdown display immediately
          const totalSeconds = autoRefreshInterval;
          const minutes = Math.floor(totalSeconds / 60);
          const seconds = totalSeconds % 60;
          const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
          setCountdownDisplay(display);

          return;
        }

        // Calculate minutes and seconds
        const totalSeconds = Math.floor(diffMs / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;

        // Format the countdown display
        const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        setCountdownDisplay(display);

        // Log the countdown tick for debugging (but not too frequently to avoid log spam)
        if (seconds % 10 === 0 || seconds <= 5) {
          logger.verbose(`Countdown tick: ${display} (${totalSeconds}s remaining)`);
        }
      } catch (error) {
        logger.error("Error in countdown timer:", error);
      }
    };

    // Update the countdown immediately
    updateCountdown();

    // Set up a timer to update the countdown every second
    const timer = setInterval(updateCountdown, 1000);
    countdownTimerRef.current = timer;
    logger.verbose(`Set up new countdown timer with ID: ${timer}`);

    // Clean up on unmount or when dependencies change
    return () => {
      logger.verbose("Cleaning up countdown timer");
      clearInterval(timer);
      countdownTimerRef.current = null;
    };
  }, [autoRefreshEnabled, autoRefreshInterval, isActive, isPaused, handleRefresh, nextRefreshTime]);

  // Add a separate effect to monitor and log countdown changes
  useEffect(() => {
    if (autoRefreshEnabled && isActive && !isPaused) {
      logger.verbose(`Countdown display changed to: ${countdownDisplay}`);
    }
  }, [countdownDisplay, autoRefreshEnabled, isActive, isPaused]);

  // Effect to update the countdown display when the next refresh time changes
  useEffect(() => {
    if (nextRefreshTime && autoRefreshEnabled && isActive && !isPaused) {
      const now = new Date();
      const diffMs = nextRefreshTime.getTime() - now.getTime();

      if (diffMs > 0) {
        const totalSeconds = Math.floor(diffMs / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        setCountdownDisplay(display);
        logger.verbose(`Updated countdown display to ${display} based on next refresh time`);
      }
    }
  }, [nextRefreshTime, autoRefreshEnabled, isActive, isPaused]);

  // Special effect to initialize countdown immediately when component mounts
  useEffect(() => {
    // Only run this effect once when the component mounts
    logger.verbose(`Component mounted - initializing countdown`);

    if (autoRefreshEnabled && isActive && !isPaused) {
      logger.verbose(`Auto-refresh is enabled - setting up countdown`);

      // Calculate the next refresh time
      const now = new Date();
      const nextTime = new Date(now.getTime() + autoRefreshInterval * 1000);

      // Set the next refresh time
      setNextRefreshTime(nextTime);

      // Calculate and format the countdown display
      const totalSeconds = autoRefreshInterval;
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;

      // Update the countdown display
      setCountdownDisplay(display);

      logger.verbose(`Set initial next refresh time to: ${nextTime.toLocaleTimeString()} (${display} from now)`);
    } else {
      logger.verbose(`Auto-refresh not enabled or active - not setting up countdown`);
      setCountdownDisplay("--:--");
    }

    // Make sure to clean up any existing timer when the component unmounts
    return () => {
      if (countdownTimerRef.current) {
        clearInterval(countdownTimerRef.current);
        countdownTimerRef.current = null;
        logger.verbose("Cleaned up countdown timer on unmount");
      }
    };
  }, []); // Empty dependency array means this runs once on mount

  // Effect for adaptive refresh rate suggestions
  useEffect(() => {
    if (!isActive || !autoRefreshEnabled || videos.length === 0) return;

    const suggestedInterval = calculateSuggestedRefreshInterval(videos);

    // Only suggest if significantly different from current setting
    if (suggestedInterval < autoRefreshInterval * 0.5) {
      toast({
        title: "High Activity Detected",
        description: `Consider setting a shorter refresh interval (${suggestedInterval}s) for more timely updates`,
        duration: 5000
      });
    }
  }, [videos, isActive, autoRefreshEnabled, autoRefreshInterval, calculateSuggestedRefreshInterval]);

  // Effect for background throttling
  useEffect(() => {
    // Function to handle visibility change
    const handleVisibilityChange = () => {
      if (!autoRefreshEnabled || !isActive) return;

      if (document.hidden) {
        // Page is not visible, slow down refreshes
        if (autoRefreshIntervalId) {
          clearInterval(autoRefreshIntervalId);

          // Set a slower interval when in background
          const backgroundInterval = setInterval(() => {
            logger.verbose("Background refresh (reduced frequency)");
            // Use a function reference instead of calling handleRefresh directly
            // This prevents closure issues with stale state
            handleRefresh();
          }, autoRefreshInterval * 3 * 1000); // 3x slower in background

          setAutoRefreshIntervalId(backgroundInterval);
        }
      } else {
        // Page is visible again, restore normal refresh rate
        if (autoRefreshEnabled && isActive && autoRefreshIntervalId) {
          // Clear the current interval first
          clearInterval(autoRefreshIntervalId);
          setAutoRefreshIntervalId(null);

          // Use a timeout to break the potential render cycle
          setTimeout(() => {
            // Only set a new interval if auto-refresh is still enabled
            if (autoRefreshEnabled && isActive) {
              const newInterval = setInterval(() => {
                handleRefresh();
              }, autoRefreshInterval * 1000);

              setAutoRefreshIntervalId(newInterval);
            }
          }, 100);
        }
      }
    };

    // Add event listener
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Clean up
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [autoRefreshEnabled, autoRefreshInterval, isActive, autoRefreshIntervalId, handleRefresh]);

  // Effect for debug mode keyboard shortcut
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+Shift+D to toggle debug mode
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        setDebugMode(prev => !prev);
        toast({
          title: `Debug mode ${!debugMode ? 'enabled' : 'disabled'}`,
          duration: 2000
        });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [debugMode]);

  // Format time remaining for pause
  const formatPauseTimeRemaining = () => {
    if (!pauseEndTime) return "";

    const now = new Date();
    const diffMs = pauseEndTime.getTime() - now.getTime();

    if (diffMs <= 0) return "Resuming...";

    const diffMins = Math.floor(diffMs / 60000);
    const diffSecs = Math.floor((diffMs % 60000) / 1000);

    return `${diffMins}:${diffSecs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleRefresh()}
          disabled={isRefreshing}
          className="relative"
        >
          {isRefreshing ? (
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh Now
        </Button>

        <div className="flex items-center gap-2">
          <Select
            value={autoRefreshEnabled ? autoRefreshInterval.toString() : "manual"}
            onValueChange={handleIntervalChange}
            disabled={isRefreshing || isPaused}
          >
            <SelectTrigger className="w-[180px] h-9">
              <SelectValue placeholder="Select refresh interval" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="manual">Manual refresh only</SelectItem>
              <SelectItem value="30">30 seconds</SelectItem>
              <SelectItem value="60">1 minute</SelectItem>
              <SelectItem value="120">2 minutes</SelectItem>
              <SelectItem value="300">5 minutes</SelectItem>
              <SelectItem value="600">10 minutes</SelectItem>
              <SelectItem value="1200">20 minutes</SelectItem>
              <SelectItem value="1800">30 minutes</SelectItem>
              <SelectItem value="2400">40 minutes</SelectItem>
              <SelectItem value="3000">50 minutes</SelectItem>
              <SelectItem value="3600">1 hour</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex items-center gap-1">
            <span className="text-sm text-muted-foreground">Auto</span>
            <Switch
              checked={autoRefreshEnabled}
              onCheckedChange={handleAutoRefreshToggle}
              disabled={isRefreshing || autoRefreshInterval <= 0 || isPaused}
            />
          </div>
        </div>

        {/* Pause button */}
        {autoRefreshEnabled && !isPaused && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePauseAutoRefresh(5)}
            disabled={isRefreshing}
          >
            <Pause className="h-4 w-4 mr-2" />
            Pause for 5m
          </Button>
        )}

        {/* Resume button when paused */}
        {isPaused && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setIsPaused(false);
              setPauseEndTime(null);
              handleAutoRefreshToggle(true);
            }}
          >
            <Clock className="h-4 w-4 mr-2" />
            Resume ({formatPauseTimeRemaining()})
          </Button>
        )}
      </div>

      {/* Status indicators - fixed height container to prevent layout shifts */}
      <div className="flex items-center gap-6 text-sm py-2 px-4 bg-transparent rounded-lg my-2 h-14">
        <div className="flex items-center gap-6 w-full">
          {/* Last refresh time - always visible with fixed width */}
          <div className="flex items-center min-w-[240px]">
            {lastRefreshTime ? (
              <>
                <span className="text-white font-medium mr-1 px-2 py-1 rounded-md bg-gray-900 border border-gray-600 shadow-sm">Last refresh:</span>
                <span className="text-white bg-gray-900 font-bold px-4 py-1 rounded-md inline-block min-w-[120px] text-center border border-gray-600 shadow-sm">{lastRefreshTime.toLocaleTimeString()}</span>
              </>
            ) : (
              <>
                <span className="text-white font-medium mr-1 px-2 py-1 rounded-md bg-gray-900 border border-gray-600 shadow-sm opacity-0">Last refresh:</span>
                <span className="text-white bg-gray-900 font-bold px-4 py-1 rounded-md inline-block min-w-[120px] text-center border border-gray-600 shadow-sm opacity-0">00:00:00</span>
              </>
            )}
          </div>

          {/* Next refresh countdown - always visible with fixed width */}
          <div className="flex items-center min-w-[240px]">
            {autoRefreshEnabled && !isPaused ? (
              <>
                <span className="text-white font-medium mr-1 px-2 py-1 rounded-md bg-gray-900 border border-gray-600 shadow-sm">Next refresh in:</span>
                <span className={`
                  text-white bg-gray-900
                  font-bold text-xl px-4 py-1 rounded-md inline-block min-w-[80px] text-center border border-gray-600 shadow-sm ${
                  nextRefreshTime &&
                  ((nextRefreshTime.getTime() - new Date().getTime()) / 1000 < 10) ?
                  'animate-pulse bg-red-800 text-white border-red-600' : ''
                }`}>{countdownDisplay}</span>
                {debugMode && <span className="text-white ml-1 bg-gray-900 px-2 py-1 rounded-md border border-gray-600 shadow-sm">(interval: {autoRefreshInterval}s, next: {nextRefreshTime?.toLocaleTimeString() || 'N/A'})</span>}
              </>
            ) : isPaused && pauseEndTime ? (
              <>
                <span className="text-white font-medium mr-1 px-2 py-1 rounded-md bg-gray-900 border border-gray-600 shadow-sm">Auto-refresh paused until</span>
                <span className="text-white bg-gray-900 font-bold px-4 py-1 rounded-md inline-block min-w-[120px] text-center border border-gray-600 shadow-sm">{pauseEndTime.toLocaleTimeString()}</span>
              </>
            ) : (
              <>
                <span className="text-white font-medium mr-1 px-2 py-1 rounded-md bg-gray-900 border border-gray-600 shadow-sm opacity-0">Next refresh in:</span>
                <span className="text-white bg-gray-900 font-bold text-xl px-4 py-1 rounded-md inline-block min-w-[80px] text-center border border-gray-600 shadow-sm opacity-0">0:00</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Debug information */}
      {debugMode && (
        <Card className="mt-4 p-4 bg-muted/50">
          <h3 className="text-sm font-semibold">Auto-refresh Debug Info</h3>
          <ul className="text-xs mt-2 space-y-1">
            <li>Auto-refresh enabled: {autoRefreshEnabled ? 'Yes' : 'No'}</li>
            <li>Interval: {autoRefreshInterval} seconds</li>
            <li>Is active tab: {isActive ? 'Yes' : 'No'}</li>
            <li>Is paused: {isPaused ? 'Yes' : 'No'}</li>
            <li>Last refresh: {lastRefreshTime ? <span className="text-white bg-gray-900 font-bold px-2 py-0.5 rounded-md inline-block text-center border border-gray-600 shadow-sm">{lastRefreshTime.toLocaleTimeString()}</span> : 'Never'}</li>
            <li>Interval ID active: {autoRefreshIntervalId ? `Yes (ID: ${autoRefreshIntervalId})` : 'No'}</li>
            <li>Refresh in progress: {refreshInProgressRef.current ? 'Yes' : 'No'}</li>
            <li>Countdown timer active: {countdownTimerRef.current ? 'Yes' : 'No'}</li>
            <li>Videos count: {videos.length}</li>
            <li>Suggested interval: {calculateSuggestedRefreshInterval(videos)} seconds</li>
            <li>Next refresh time: {nextRefreshTime ? <span className="text-white bg-gray-900 font-bold px-2 py-0.5 rounded-md inline-block text-center border border-gray-600 shadow-sm">{nextRefreshTime.toLocaleTimeString()}</span> : 'Not set'}</li>
            <li>Countdown display: {countdownDisplay}</li>
            <li>Time until next refresh: {
              nextRefreshTime ? (() => {
                const now = new Date();
                const diffMs = nextRefreshTime.getTime() - now.getTime();
                const totalSeconds = Math.max(0, Math.floor(diffMs / 1000));
                return `${Math.floor(totalSeconds / 60)}:${(totalSeconds % 60).toString().padStart(2, '0')} (${totalSeconds}s)`;
              })() : 'Not set'
            }</li>
          </ul>
          <div className="flex gap-2 mt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                logger.debug({
                  autoRefreshEnabled,
                  autoRefreshInterval,
                  isActive,
                  isPaused,
                  lastRefreshTime: lastRefreshTime ? lastRefreshTime.toLocaleTimeString() : 'Never',
                  nextRefreshTime: nextRefreshTime ? nextRefreshTime.toLocaleTimeString() : 'Not set',
                  countdownDisplay,
                  timeUntilNextRefresh: nextRefreshTime ? (() => {
                    const now = new Date();
                    const diffMs = nextRefreshTime.getTime() - now.getTime();
                    const totalSeconds = Math.max(0, Math.floor(diffMs / 1000));
                    return `${Math.floor(totalSeconds / 60)}:${(totalSeconds % 60).toString().padStart(2, '0')} (${totalSeconds}s)`;
                  })() : 'Not set',
                  autoRefreshIntervalId,
                  refreshInProgress: refreshInProgressRef.current,
                  countdownTimerActive: !!countdownTimerRef.current,
                  videosCount: videos.length,
                  suggestedInterval: calculateSuggestedRefreshInterval(videos)
                });
              }}
            >
              Log Debug Info
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Force a refresh for debugging
                handleRefresh().catch(err => {
                  logger.error("Error during manual debug refresh:", err);
                });

                // Also force update the countdown display
                if (nextRefreshTime) {
                  const now = new Date();
                  const diffMs = nextRefreshTime.getTime() - now.getTime();
                  const totalSeconds = Math.max(0, Math.floor(diffMs / 1000));
                  const minutes = Math.floor(totalSeconds / 60);
                  const seconds = totalSeconds % 60;
                  const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                  setCountdownDisplay(display);
                }
              }}
            >
              Force Refresh
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}
