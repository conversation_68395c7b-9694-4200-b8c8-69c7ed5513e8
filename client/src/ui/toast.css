.toaster {
  position: fixed;
  top: 16px;
  right: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 9999;
}

.toast {
  padding: 12px 16px;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #333;
  min-width: 200px;
  max-width: 300px;
}

.toast-success {
  border-color: #22c55e;
}

.toast-error {
  border-color: #ef4444;
}

.toast-warning {
  border-color: #fbbf24;
}

.toast-info {
  border-color: #3b82f6;
}

.toast-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.toast-description {
  font-size: 14px;
  color: #666;
}

