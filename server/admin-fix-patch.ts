import * as fs from 'fs';
import * as path from 'path';

// Path to the SQLiteStorage class file
const filePath = path.resolve(__dirname, 'sqliteStorage.ts');

// Read the current file content
let content = fs.readFileSync(filePath, 'utf8');

// Add the missing methods to the SQLiteStorage class
// We'll add them right after the getUserByUsername method
const getUserByUsernameMethod = `    async getUserByUsername(username: string): Promise<User | undefined> {
        const stmt = this.db.prepare('SELECT * FROM users WHERE username = ?');
        const user = stmt.get(String(username));
        
        if (!user) return undefined;
        
        return this.getUser(user.id);
    }`;

const newMethods = `    async getUserByUsername(username: string): Promise<User | undefined> {
        const stmt = this.db.prepare('SELECT * FROM users WHERE username = ?');
        const user = stmt.get(String(username));
        
        if (!user) return undefined;
        
        return this.getUser(user.id);
    }

    async getAllUsers(): Promise<User[]> {
        try {
            const stmt = this.db.prepare('SELECT id FROM users');
            const userIds = stmt.all();
            
            // Get full user objects with settings
            const users = await Promise.all(
                userIds.map(row => this.getUser(row.id))
            );
            
            return users.filter(user => user !== undefined) as User[];
        } catch (error) {
            console.error('Error fetching all users:', error);
            throw error;
        }
    }

    async deleteUser(id: number): Promise<void> {
        try {
            // Don't allow deleting the admin user
            const user = await this.getUser(id);
            if (user && user.username === 'admin') {
                throw new Error("Cannot delete the admin user");
            }
            
            this.db.transaction(() => {
                this.db.prepare('DELETE FROM videos WHERE user_id = ?').run(id);
                this.db.prepare('DELETE FROM settings WHERE user_id = ?').run(id);
                this.db.prepare('DELETE FROM users WHERE id = ?').run(id);
            })();
        } catch (error) {
            console.error('Failed to delete user:', error);
            throw error;
        }
    }`;

// Replace the getUserByUsername method with our new methods
content = content.replace(getUserByUsernameMethod, newMethods);

// Write the updated content back to the file
fs.writeFileSync(filePath, content, 'utf8');

console.log('Successfully added admin methods to SQLiteStorage class!');
console.log('Please restart the server for the changes to take effect.');

// Also ensure the admin user has the correct role and isAdmin flag
console.log('Ensuring admin user has correct role and isAdmin flag...');
const { exec } = require('child_process');
exec('sqlite3 data.db "UPDATE users SET role = \'admin\', isAdmin = 1 WHERE username = \'admin\';"', (error, stdout, stderr) => {
    if (error) {
        console.error(`Error updating admin user: ${error.message}`);
        return;
    }
    if (stderr) {
        console.error(`stderr: ${stderr}`);
        return;
    }
    console.log('Admin user updated successfully!');
});
