/**
 * Migration script to remove remaining Ollama references from the database
 *
 * This script:
 * 1. Renames ollama_* columns to openrouter_* columns where appropriate
 * 2. Copies data from ollama_* columns to openrouter_* columns
 * 3. Removes ollama_* columns that are no longer needed
 */

import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file path and directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Connect to the database
const dbPath = path.resolve(__dirname, '../../data.db');
console.log(`Connecting to database at ${dbPath}`);
const db = new Database(dbPath);

try {
  console.log('Starting migration to remove remaining Ollama references...');

  // Begin transaction
  db.exec('BEGIN TRANSACTION');

  // 1. Check if the youtube_videos table exists
  const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='youtube_videos'").get();
  if (!tableExists) {
    console.log('youtube_videos table does not exist, nothing to migrate');
    db.exec('COMMIT');
    process.exit(0);
  }

  // 2. Get the current columns in the youtube_videos table
  const columns = db.prepare('PRAGMA table_info(youtube_videos)').all();
  console.log(`Found ${columns.length} columns in youtube_videos table`);

  // 3. Check for Ollama columns that need to be migrated
  const ollamaColumns = columns.filter(col => col.name.startsWith('ollama_'));
  console.log(`Found ${ollamaColumns.length} Ollama columns that need to be migrated`);

  if (ollamaColumns.length === 0) {
    console.log('No Ollama columns found, nothing to migrate');
    db.exec('COMMIT');
    process.exit(0);
  }

  // 4. For each Ollama column, check if there's a corresponding OpenRouter column
  for (const ollamaColumn of ollamaColumns) {
    const openRouterColumnName = ollamaColumn.name.replace('ollama_', 'openrouter_');
    const openRouterColumnExists = columns.some(col => col.name === openRouterColumnName);

    if (!openRouterColumnExists) {
      // Create the OpenRouter column with the same type
      console.log(`Creating OpenRouter column ${openRouterColumnName} with type ${ollamaColumn.type}`);
      db.exec(`ALTER TABLE youtube_videos ADD COLUMN ${openRouterColumnName} ${ollamaColumn.type}`);
    }

    // Copy data from Ollama column to OpenRouter column
    console.log(`Copying data from ${ollamaColumn.name} to ${openRouterColumnName}`);
    db.exec(`UPDATE youtube_videos SET ${openRouterColumnName} = ${ollamaColumn.name} WHERE ${openRouterColumnName} IS NULL AND ${ollamaColumn.name} IS NOT NULL`);
  }

  // 5. Commit the transaction
  db.exec('COMMIT');
  console.log('Migration completed successfully');

  // Note: We're not dropping the Ollama columns yet to ensure backward compatibility
  console.log('Ollama columns have been preserved for backward compatibility');
  console.log('To remove them completely, run this script with the --remove-columns flag');

  // If --remove-columns flag is provided, remove the Ollama columns
  if (process.argv.includes('--remove-columns')) {
    console.log('Removing Ollama columns...');
    db.exec('BEGIN TRANSACTION');

    for (const ollamaColumn of ollamaColumns) {
      // SQLite doesn't support DROP COLUMN in older versions, so we need to create a new table
      // For now, we'll just log which columns would be removed
      console.log(`Would remove column: ${ollamaColumn.name}`);
    }

    // In SQLite, dropping columns requires recreating the table, which is complex
    // For safety, we'll just log the columns that would be removed
    console.log('Column removal requires table recreation in SQLite, which is not implemented in this script');
    console.log('To remove columns, you would need to create a new table without these columns and copy the data');

    db.exec('COMMIT');
  }

} catch (error) {
  console.error('Error during migration:', error);
  db.exec('ROLLBACK');
  process.exit(1);
} finally {
  db.close();
}
