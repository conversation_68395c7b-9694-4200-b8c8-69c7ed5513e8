import React from 'react';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ArrowUp, ArrowDown, TrendingUp } from 'lucide-react';

interface VphIndicatorProps {
  currentVph: number;
  previousVph: number;
  highestVph: number;
  timeSincePreviousMeasurement?: number; // Time in milliseconds since previous measurement
}

export function VphIndicator({
  currentVph,
  previousVph,
  highestVph,
  timeSincePreviousMeasurement = 0
}: VphIndicatorProps) {
  // Calculate deltas
  const momentumDelta = currentVph - previousVph;
  const peakDelta = currentVph - highestVph;

  // Calculate percentage changes
  const momentumPercentage = previousVph > 0 ? (momentumDelta / previousVph) * 100 : 0;
  const peakPercentage = highestVph > 0 ? (peakDelta / highestVph) * 100 : 0;

  // Determine if this is a new peak
  const isNewPeak = currentVph > highestVph;

  // Determine trend direction
  const isTrending = momentumDelta > 0;

  // Determine if the momentum is significant (1% or at least 2 VPH)
  const momentumThreshold = Math.max(2, previousVph * 0.01);
  const hasSignificantMomentum = Math.abs(momentumDelta) >= momentumThreshold;

  // Calculate minutes since previous measurement
  const minutesSincePrevious = timeSincePreviousMeasurement / (1000 * 60);

  // Only show momentum indicator if it's significant and enough time has passed
  const showMomentumIndicator = hasSignificantMomentum &&
                               (minutesSincePrevious >= 5 || timeSincePreviousMeasurement === 0);

  return (
    <div className="flex flex-col">
      {/* Current VPH value */}
      <div className="font-medium text-white">{currentVph.toLocaleString()}</div>

      {/* Trend indicators */}
      <div className="flex items-center gap-1 mt-0.5">
        {showMomentumIndicator && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center">
                  {isTrending ? (
                    <span className="flex items-center text-green-500">
                      <ArrowUp className="h-3 w-3 mr-0.5" />
                      <span className="text-xs">{Math.abs(momentumDelta).toLocaleString()}</span>
                    </span>
                  ) : (
                    <span className="flex items-center text-red-500">
                      <ArrowDown className="h-3 w-3 mr-0.5" />
                      <span className="text-xs">{Math.abs(momentumDelta).toLocaleString()}</span>
                    </span>
                  )}
                </div>
              </TooltipTrigger>
              <TooltipContent side="right">
                <div className="text-xs">
                  <p>Current: {currentVph.toLocaleString()} VPH</p>
                  <p>Previous: {previousVph.toLocaleString()} VPH</p>
                  <p>Change: {momentumDelta > 0 ? '+' : ''}{momentumDelta.toLocaleString()} ({momentumPercentage.toFixed(1)}%)</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {/* Peak indicator */}
        {isNewPeak && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="flex items-center text-amber-500 ml-1">
                  <TrendingUp className="h-3 w-3 mr-0.5" />
                  <span className="text-xs">Peak</span>
                </span>
              </TooltipTrigger>
              <TooltipContent side="right">
                <div className="text-xs">
                  <p>New peak: {currentVph.toLocaleString()} VPH</p>
                  <p>Previous peak: {highestVph.toLocaleString()} VPH</p>
                  <p>Increase: +{peakDelta.toLocaleString()} ({peakPercentage.toFixed(1)}%)</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  );
}
