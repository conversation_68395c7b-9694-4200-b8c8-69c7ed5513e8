import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { Video } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Define the shape of our cast queue state
interface CastQueueState {
  queue: Video[];
  currentIndex: number;
  isCastingEnabled: boolean;
}

// Define the context type
interface CastQueueContextType {
  state: CastQueueState;
  addToQueue: (video: Video, options?: { silent: boolean }) => void;
  removeFromQueue: (videoId: string) => void;
  clearQueue: () => void;
  playNext: () => Video | null;
  playPrevious: () => Video | null;
  getCurrentVideo: () => Video | null;
  toggleCasting: () => void;
  isInQueue: (videoId: string) => boolean;
  isQueueLoading: boolean;
  refetchQueue: () => void;
  activateQueue: () => void;
  deactivateQueue: () => void;
}

// Default state for the cast queue
const getDefaultState = (): CastQueueState => {
  return {
    queue: [],
    currentIndex: -1,
    isCastingEnabled: true // Enable casting by default
  };
};

// Create the context with a default value
const CastQueueContext = createContext<CastQueueContextType>({
  state: getDefaultState(),
  addToQueue: () => {},
  removeFromQueue: () => {},
  clearQueue: () => {},
  playNext: () => null,
  playPrevious: () => null,
  getCurrentVideo: () => null,
  toggleCasting: () => {},
  isInQueue: () => false,
  isQueueLoading: false,
  refetchQueue: () => {},
  activateQueue: () => {},
  deactivateQueue: () => {}
});

// Provider component
export function CastQueueProvider({ children }: { children: ReactNode }) {
  // Initialize with empty queue but set loading to true by default
  const [state, setState] = useState<CastQueueState>({
    ...getDefaultState(),
    // Start with an empty queue but mark as loading
    queue: [],
    currentIndex: -1,
    isCastingEnabled: true
  });
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Track if we've loaded the queue from the server
  const [hasLoadedFromServer, setHasLoadedFromServer] = useState(false);

  // Track whether the cast queue is being actively used
  const [isActivelyUsed, setIsActivelyUsed] = useState(false);

  // Fetch the cast queue from the server only when needed
  const { data: queueData, isLoading, isError, refetch } = useQuery<Video[]>({
    queryKey: ["/api/cast-queue"],
    queryFn: async () => {
      console.log('Executing cast queue fetch function');
      try {
        // Add a cache-busting parameter to prevent browser caching
        const timestamp = new Date().getTime();
        const res = await apiRequest('GET', `/api/cast-queue?_=${timestamp}`);
        if (!res.ok) {
          throw new Error(`Failed to fetch cast queue: ${res.status}`);
        }
        const data = await res.json();
        console.log('Successfully fetched cast queue:', data);
        return data;
      } catch (error) {
        console.error('Error in cast queue fetch function:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('Successfully fetched cast queue:', data);

      // Always update the state with the data from the server
      if (data && Array.isArray(data)) {
        setState(prevState => ({
          ...prevState,
          queue: data,
          currentIndex: data.length > 0 ? 0 : -1
        }));

        // Mark that we've loaded data from the server
        setHasLoadedFromServer(true);

        console.log('Updated cast queue state with', data.length, 'videos');
      } else {
        console.warn('Received invalid data from server:', data);
      }
    },
    onError: (error) => {
      console.error('Failed to fetch cast queue:', error);
    },
    // Only fetch when needed
    staleTime: 60000, // Consider data stale after 1 minute
    refetchOnMount: false, // Don't automatically refetch when component mounts
    refetchOnWindowFocus: false, // Don't refetch when window gets focus
    retry: 3, // Retry failed requests 3 times
    retryDelay: 1000, // Wait 1 second between retries
    enabled: isActivelyUsed, // Only enable the query when the cast queue is actively used
    refetchInterval: isActivelyUsed ? 10000 : false // Only refetch every 10 seconds when actively used
  });

  // Expose loading state in the context
  const isQueueLoading = isLoading;

  // Only fetch the cast queue when it's first needed
  // We'll set isActivelyUsed to true when the user interacts with the cast queue
  const activateAndFetch = useCallback(() => {
    if (!isActivelyUsed) {
      console.log('Activating cast queue and fetching data');
      setIsActivelyUsed(true);
      // Use setTimeout to ensure the state update happens before the fetch
      setTimeout(() => {
        refetch();
      }, 100);
    }
  }, [isActivelyUsed, refetch]);

  // Deactivate the cast queue when it's not being used
  const deactivate = useCallback(() => {
    if (isActivelyUsed) {
      console.log('Deactivating cast queue');
      setIsActivelyUsed(false);
    }
  }, [isActivelyUsed]);

  // Add a video to the queue
  const addToQueueMutation = useMutation({
    mutationFn: async (video: Video) => {
      console.log('Adding video to cast queue:', video);
      try {
        const res = await apiRequest('POST', '/api/cast-queue', { video });
        const data = await res.json();
        console.log('Response from adding to cast queue:', data);
        return data;
      } catch (error) {
        console.error('Error in addToQueueMutation:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('Successfully added to cast queue, updating state with:', data);
      if (data && Array.isArray(data)) {
        setState(prevState => ({
          ...prevState,
          queue: data,
          currentIndex: prevState.currentIndex === -1 && data.length > 0 ? 0 : prevState.currentIndex
        }));
      }
      // Force a refetch to ensure we have the latest data
      refetch();
      // Also invalidate the query to ensure the cache is updated
      queryClient.invalidateQueries({ queryKey: ["/api/cast-queue"] });
    },
    onError: (error) => {
      console.error('Failed to add video to cast queue:', error);
      toast({
        title: "Error Adding to Queue",
        description: "There was a problem adding the video to your cast queue.",
        variant: "destructive"
      });
    }
  });

  // Add a video to the queue
  const addToQueue = useCallback((video: Video, options = { silent: false }) => {
    // Activate the cast queue when adding a video
    activateAndFetch();

    // Check if video is already in queue
    const isAlreadyInQueue = state.queue.some(v => v.id === video.id);

    if (isAlreadyInQueue) {
      // Show toast notification only if not silent
      if (!options.silent) {
        toast({
          title: "Already in Queue",
          description: "This video is already in your cast queue.",
        });
      }
      return;
    }

    // Add to queue via API
    addToQueueMutation.mutate(video, {
      onSuccess: (data) => {
        console.log('Successfully added to queue, forcing refetch');

        // Force a refetch to ensure we have the latest data
        setTimeout(() => {
          refetch();
        }, 100);

        // Show toast notification only if not silent
        if (!options.silent) {
          toast({
            title: "Added to Cast Queue",
            description: "Video added to your cast queue.",
          });
        }
      }
    });
  }, [toast, state.queue, addToQueueMutation, refetch, activateAndFetch]);

  // Remove a video from the queue mutation
  const removeFromQueueMutation = useMutation({
    mutationFn: async (videoId: string) => {
      const res = await apiRequest('DELETE', `/api/cast-queue/${videoId}`);
      return res.json();
    },
    onSuccess: (data) => {
      setState(prevState => {
        // Find the index of the removed video
        const videoIndex = prevState.queue.findIndex(v => !data.some((d: Video) => d.id === v.id));

        let newCurrentIndex = prevState.currentIndex;

        // Adjust currentIndex if necessary
        if (data.length === 0) {
          newCurrentIndex = -1;
        } else if (videoIndex <= prevState.currentIndex) {
          // If we removed a video before or at the current one, decrement the index
          newCurrentIndex = Math.max(0, prevState.currentIndex - 1);
        }

        return {
          ...prevState,
          queue: data,
          currentIndex: newCurrentIndex
        };
      });
      queryClient.invalidateQueries({ queryKey: ["/api/cast-queue"] });
    },
    onError: (error) => {
      console.error('Failed to remove video from cast queue:', error);
    }
  });

  // Remove a video from the queue
  const removeFromQueue = useCallback((videoId: string) => {
    // Activate the cast queue when removing a video
    activateAndFetch();

    // Check if video is in queue
    const videoInQueue = state.queue.some(v => v.id === videoId);

    if (!videoInQueue) {
      return;
    }

    // Remove from queue via API
    removeFromQueueMutation.mutate(videoId, {
      onSuccess: () => {
        toast({
          title: "Removed from Cast Queue",
          description: "Video removed from your cast queue.",
        });

        // If the queue is now empty, deactivate it
        if (state.queue.length <= 1) {
          setTimeout(deactivate, 1000);
        }
      }
    });
  }, [toast, state.queue, removeFromQueueMutation, activateAndFetch, deactivate]);

  // Clear the entire queue mutation
  const clearQueueMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest('DELETE', '/api/cast-queue');
      return res.json();
    },
    onSuccess: () => {
      setState(prevState => ({
        ...prevState,
        queue: [],
        currentIndex: -1
      }));
      queryClient.invalidateQueries({ queryKey: ["/api/cast-queue"] });
    },
    onError: (error) => {
      console.error('Failed to clear cast queue:', error);
    }
  });

  // Clear the entire queue
  const clearQueue = useCallback(() => {
    // Activate the cast queue when clearing it
    activateAndFetch();

    // Clear queue via API
    clearQueueMutation.mutate(undefined, {
      onSuccess: () => {
        toast({
          title: "Queue Cleared",
          description: "Your cast queue has been cleared.",
        });

        // Deactivate the cast queue after clearing it
        setTimeout(deactivate, 1000);
      }
    });
  }, [toast, clearQueueMutation, activateAndFetch, deactivate]);

  // Play the next video in the queue
  const playNext = useCallback((): Video | null => {
    // Activate the cast queue when playing next video
    activateAndFetch();

    let nextVideo: Video | null = null;

    setState(prevState => {
      if (prevState.queue.length === 0 || prevState.currentIndex >= prevState.queue.length - 1) {
        return prevState;
      }

      const newIndex = prevState.currentIndex + 1;
      nextVideo = prevState.queue[newIndex];

      return {
        ...prevState,
        currentIndex: newIndex
      };
    });

    return nextVideo;
  }, [activateAndFetch]);

  // Play the previous video in the queue
  const playPrevious = useCallback((): Video | null => {
    // Activate the cast queue when playing previous video
    activateAndFetch();

    let prevVideo: Video | null = null;

    setState(prevState => {
      if (prevState.queue.length === 0 || prevState.currentIndex <= 0) {
        return prevState;
      }

      const newIndex = prevState.currentIndex - 1;
      prevVideo = prevState.queue[newIndex];

      return {
        ...prevState,
        currentIndex: newIndex
      };
    });

    return prevVideo;
  }, [activateAndFetch]);

  // Get the current video in the queue
  const getCurrentVideo = useCallback((): Video | null => {
    // Activate the cast queue when getting the current video
    if (state.queue.length > 0) {
      activateAndFetch();
    }

    if (state.queue.length === 0 || state.currentIndex === -1) {
      return null;
    }

    return state.queue[state.currentIndex];
  }, [state.queue, state.currentIndex, activateAndFetch]);

  // Toggle casting mode
  const toggleCasting = useCallback(() => {
    // Activate the cast queue when toggling casting
    activateAndFetch();

    setState(prevState => {
      const newState = {
        ...prevState,
        isCastingEnabled: !prevState.isCastingEnabled
      };

      // Show toast notification
      setTimeout(() => {
        toast({
          title: prevState.isCastingEnabled ? "Casting Disabled" : "Casting Enabled",
          description: prevState.isCastingEnabled
            ? "Videos will no longer be automatically cast from the queue."
            : "Videos in your queue will be automatically cast.",
        });
      }, 0);

      // If disabling casting and queue is empty, deactivate
      if (prevState.isCastingEnabled && prevState.queue.length === 0) {
        setTimeout(deactivate, 1000);
      }

      return newState;
    });
  }, [toast, activateAndFetch, deactivate]);

  // Check if a video is in the queue
  const isInQueue = useCallback((videoId: string): boolean => {
    // No need to activate for just checking if a video is in the queue
    return state.queue.some(v => v.id === videoId);
  }, [state.queue]);

  // Define refetch function
  const refetchQueue = useCallback(() => {
    console.log('Manually refetching cast queue');
    // Activate the cast queue when manually refetching
    activateAndFetch();
  }, [activateAndFetch]);

  return (
    <CastQueueContext.Provider
      value={{
        state,
        addToQueue,
        removeFromQueue,
        clearQueue,
        playNext,
        playPrevious,
        getCurrentVideo,
        toggleCasting,
        isInQueue,
        isQueueLoading,
        refetchQueue,
        activateQueue: activateAndFetch,
        deactivateQueue: deactivate
      }}
    >
      {children}
    </CastQueueContext.Provider>
  );
}

// Custom hook to use the cast queue context
export function useCastQueue() {
  const context = useContext(CastQueueContext);
  if (!context) {
    throw new Error('useCastQueue must be used within a CastQueueProvider');
  }
  return context;
}
