import { useState, useEffect, useRef, use<PERSON>emo, use<PERSON>allback } from "react";
import { useTrendy } from "@/hooks/use-trendy";
import { useVideos } from "@/hooks/use-videos";
import { useSettings } from "@/hooks/use-settings";
import { useToast } from "@/hooks/use-toast";
import { useKeywordGroups } from "@/hooks/use-keyword-groups";
import { useFloatingVideo } from "@/context/floating-video-context";
import { useCastQueue } from "@/context/cast-queue-context";
import { Sidebar } from "@/components/navigation/sidebar";
import { VideoGrid } from "@/components/video/video-grid";
import { VideoPlayer } from "@/components/video/video-player";
import { KeywordGroupManager } from "@/components/keyword-groups";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON>lide<PERSON> } from "@/components/ui/slider";
import { Refresh<PERSON><PERSON>, Share2, AlertCircle, Filter, Menu, Layers, Loader2, Flame, Star, Clock, Search, X, Cast, Clock3, VideoIcon, Eye, Trash2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Video as BaseVideo, KeywordGroup } from "@shared/schema";
import { initializeCastApi } from "@/lib/cast";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { useIsMobile } from "@/hooks/use-mobile";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useInfiniteScroll } from "@/hooks/use-infinite-scroll";
import { sortVideos, enhanceVideo } from "@/lib/engagement-score";
import ErrorBoundary from "@/components/error-boundary";

import { queryClient } from "@/lib/queryClient";
import { ScrollToTopButton } from "@/components/ui/scroll-to-top-button";
import { Toaster } from "@/components/ui/toaster";
import { formatRefreshTime } from "@/lib/date-format";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { CastQueueManager } from "@/components/video/cast-queue-manager";

// Extended Video type that includes all fields from VidIQ API
interface Video extends BaseVideo {
  contentDetails?: {
    duration?: string;
    dimension?: string;
    definition?: string;
    caption?: string;
    licensedContent?: boolean;
    regionRestriction?: {
      allowed?: string[];
    };
    contentRating?: any;
    projection?: string;
  };
  statistics?: {
    viewCount: string;
    likeCount: string;
    favoriteCount: string;
    commentCount: string;
  };
  description?: string;
  channel_id?: string;
  vph?: number;
  tags?: string[];
  matched_tags?: string[];
  unmatched_tags?: string[];
  seen_from?: string[];
  related_to?: string[];
};

const PLAYLIST_RANGES = [
  { label: "1-50", start: 0, end: 50 },
  { label: "50-100", start: 50, end: 100 },
  { label: "100-150", start: 100, end: 150 }
];

const SORT_OPTIONS = [
  // Engagement-based sorting options
  { value: "quality", label: "Highest Quality", description: "Videos with the best overall quality score" },
  { value: "trending", label: "Trending Now", description: "Videos gaining popularity right now" },
  { value: "momentum", label: "High Momentum", description: "Videos with strong engagement velocity" },
  { value: "evergreen", label: "Evergreen Content", description: "Timeless videos that remain relevant" },
  { value: "fresh_valuable", label: "Fresh & Valuable", description: "Recent videos with educational value" },

  // Original sorting options
  { value: "engagement", label: "Engagement Score", description: "Videos with high overall engagement" },
  { value: "fresh_trending", label: "Fresh & Trending", description: "Recently uploaded trending videos" },
  { value: "views_per_hour", label: "Views Per Hour", description: "Videos with highest hourly view rate" },
  { value: "date_desc", label: "Newest First", description: "Most recently uploaded videos" },
  { value: "date_asc", label: "Oldest First", description: "Oldest uploaded videos" },
  { value: "views_desc", label: "Most Views", description: "Videos with highest view count" },
  { value: "views_asc", label: "Least Views", description: "Videos with lowest view count" },

  // No duration sorting options
];

export default function WatchPage() {
  return (
    <ErrorBoundary>
      <WatchPageContent />
    </ErrorBoundary>
  );
}

function WatchPageContent() {
  const { videos: oldVideos, isLoading: isLoadingOldVideos } = useVideos();
  const { videos, isLoading: isLoadingGlobalVideos, error: globalError, refresh: refreshGlobalVideos, refetch: refetchGlobalVideos } = useTrendy();
  const { settings, updateSettings, isLoading: settingsLoading } = useSettings();
  const { toast } = useToast();
  const { state: castQueueState, refetchQueue } = useCastQueue();
  const [castQueueOpen, setCastQueueOpen] = useState(false);

  const [selectedRange, setSelectedRange] = useState<string>("");
  // Initialize sortBy from user settings or localStorage or default to "popularity"
  const [sortBy, setSortBy] = useState<string>(() => {
    // First try to get from localStorage with watch-page specific key
    const savedSort = localStorage.getItem('watchPageSortMethod');
    // Fall back to the old shared key for backward compatibility
    const oldSavedSort = !savedSort ? localStorage.getItem('preferredSortMethod') : null;
    return savedSort || oldSavedSort || "popularity";
  });
  const [currentVideo, setCurrentVideo] = useState<Video | null>(null);
  const [activeKeywords, setActiveKeywords] = useState<string[]>([]);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("feed");
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Video duration filter state ("all", "shorts", "videos")
  const [durationFilter, setDurationFilter] = useState<string>(() => {
    // Try to load from localStorage for backward compatibility
    const savedFilter = localStorage.getItem('preferredDurationFilter');
    return savedFilter || "all"; // Default to showing all videos
  });

  // Unwatched filter state
  const [showUnwatchedOnly, setShowUnwatchedOnly] = useState<boolean>(() => {
    // Try to load from localStorage for backward compatibility
    const savedFilter = localStorage.getItem('preferredUnwatchedFilter');
    return savedFilter === 'true';
  });

  // Grid size state (1-6 columns)
  const [gridSize, setGridSize] = useState<number>(() => {
    // Try to load from localStorage
    const savedGridSize = localStorage.getItem('preferredGridSize');
    const parsedSize = savedGridSize ? parseInt(savedGridSize) : 4; // Default to 4 columns
    // Ensure the grid size is between 1 and 6
    return Math.max(1, Math.min(6, parsedSize));
  });

  // Infinite scroll state
  const [displayedVideos, setDisplayedVideos] = useState<Video[]>([]);
  const [videosPerPage, setVideosPerPage] = useState<number>(24); // Initial batch size
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  // Refresh status state
  const [refreshStartTime, setRefreshStartTime] = useState<number | null>(null);
  const [refreshDuration, setRefreshDuration] = useState<number | null>(null);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);

  // Toast hook already declared above

  const isMobile = useIsMobile();
  const scrollPosition = useRef<number>(0);

  // Get keyword groups and related functions
  const {
    groups,
    setActiveGroup,
    refreshGroupVideos,
    useGroupVideos
  } = useKeywordGroups();

  // Get videos for the selected group
  const {
    data: groupVideos = [],
    isLoading: isLoadingGroupVideos,
    refetch: refetchGroupVideos
  } = useGroupVideos(selectedGroupId);

  // Initialize Cast API when the app loads
  useEffect(() => {
    // Load the Cast API script
    const script = document.createElement('script');
    script.src = 'https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1';
    document.head.appendChild(script);

    // Set up the Cast API availability callback
    window.__onGCastApiAvailable = (isAvailable) => {
      if (isAvailable) {
        initializeCastApi()
          .then(() => console.log('Cast API initialized successfully'))
          .catch(error => console.error('Failed to initialize Cast API:', error));
      }
    };
  }, []);

  // Set the selected group based on active group in settings
  useEffect(() => {
    if (settings?.activeKeywordGroupId !== undefined) {
      console.log(`Setting selected group to ${settings.activeKeywordGroupId} from settings`);
      setSelectedGroupId(settings.activeKeywordGroupId);
    }
  }, [settings?.activeKeywordGroupId]);

  // Fallback to old videos if the new endpoint fails
  useEffect(() => {
    if (globalError && oldVideos.length > 0 && !isLoadingOldVideos) {
      console.log('Falling back to old videos endpoint due to error:', globalError);
      toast({
        title: "Using fallback data source",
        description: "There was an issue with the primary data source. Using fallback data.",
        variant: "destructive",
      });
    }
  }, [globalError, oldVideos, isLoadingOldVideos, toast]);

  // Apply user's default settings when they are loaded
  useEffect(() => {
    if (settings) {
      // Apply default sort method if available
      if (settings.defaultSortMethod) {
        console.log(`Setting sort method to ${settings.defaultSortMethod} from user settings`);
        setSortBy(settings.defaultSortMethod);
        // Use watch-page specific localStorage key
        localStorage.setItem('watchPageSortMethod', settings.defaultSortMethod);
        // Remove the old shared key to prevent conflicts
        localStorage.removeItem('preferredSortMethod');
      }

      // Apply default duration filter if available
      if (settings.defaultDurationFilter) {
        console.log(`Setting duration filter to ${settings.defaultDurationFilter} from user settings`);
        setDurationFilter(settings.defaultDurationFilter);
        localStorage.setItem('preferredDurationFilter', settings.defaultDurationFilter);
      }

      // Apply default unwatched filter if available
      if (settings.defaultUnwatchedFilter !== undefined) {
        console.log(`Setting unwatched filter to ${settings.defaultUnwatchedFilter} from user settings`);
        setShowUnwatchedOnly(settings.defaultUnwatchedFilter);
        localStorage.setItem('preferredUnwatchedFilter', String(settings.defaultUnwatchedFilter));
      }
    }
  }, [settings]);

  // Handle group changes and ensure sorting is properly applied
  useEffect(() => {
    if (selectedGroupId !== null) {
      console.log(`Group changed to ${selectedGroupId}, current sort method: ${sortBy}`);

      // Reset displayed videos when group changes
      setDisplayedVideos([]);

      // Force a refetch to ensure we have fresh data with the current sort
      refetchGroupVideos();

      // Ensure the sort method is applied to the new group
      queryClient.invalidateQueries({ queryKey: [`/api/keyword-groups/${selectedGroupId}/videos`] });
    } else {
      console.log(`Switched to global feed, current sort method: ${sortBy}`);

      // Reset displayed videos when switching to global feed
      setDisplayedVideos([]);

      // Force a refetch of global videos
      refetchGlobalVideos();

      // Ensure the sort method is applied to the global feed
      queryClient.invalidateQueries({ queryKey: ["/api/videos"] });
      queryClient.invalidateQueries({ queryKey: ["/api/trendy"] });
    }
  }, [selectedGroupId, sortBy, refetchGroupVideos, refetchGlobalVideos]);

  useEffect(() => {
    if (!settings?.autoRefreshInterval) return;

    const lastRefresh = settings.lastRefreshTime ? new Date(settings.lastRefreshTime) : new Date(0);
    const minutesSinceLastRefresh = (Date.now() - lastRefresh.getTime()) / (1000 * 60);

    if (minutesSinceLastRefresh >= settings.autoRefreshInterval) {
      // Refresh the appropriate feed
      if (selectedGroupId) {
        refreshGroupVideos.mutate(selectedGroupId);
      } else {
        refreshGlobalVideos.mutate();
      }
    }

    const intervalId = setInterval(() => {
      // Refresh the appropriate feed
      if (selectedGroupId) {
        refreshGroupVideos.mutate(selectedGroupId);
      } else {
        refreshGlobalVideos.mutate();
      }
    }, settings.autoRefreshInterval * 60 * 1000);

    return () => clearInterval(intervalId);
  }, [settings?.autoRefreshInterval, settings?.lastRefreshTime, selectedGroupId, refreshGlobalVideos, refreshGroupVideos]);

  // Get floating video context
  const { openFloatingVideo, setCurrentFeedVideos } = useFloatingVideo();

  // Handle video playback - now opens directly in floating player
  const handlePlayVideo = (video: Video) => {
    // Store current scroll position
    scrollPosition.current = window.scrollY;
    console.log('=== Opening video in floating player ===');
    console.log('Video ID:', video.id);
    console.log('Video title:', video.title);

    // Open the video in the floating player
    openFloatingVideo(video);
  };

  // Handle grid size change
  const handleGridSizeChange = useCallback((value: number[]) => {
    // Ensure the grid size is between 1 and 6
    const newSize = Math.max(1, Math.min(6, value[0]));
    setGridSize(newSize);
    // Save to localStorage
    localStorage.setItem('preferredGridSize', newSize.toString());
  }, []);

  // Restore scroll position when closing video
  const handleCloseVideo = () => {
    setCurrentVideo(null);
    setTimeout(() => {
      window.scrollTo(0, scrollPosition.current);
    }, 100);
  };

  const handlePlaylistGeneration = () => {
    // Use the appropriate videos based on selected group
    const currentVideos = selectedGroupId ? groupVideos : videos;

    if (!selectedRange || !currentVideos.length) return;

    const range = PLAYLIST_RANGES.find(r => r.label === selectedRange);
    if (!range) return;

    const { start, end } = range;
    const videoIds = currentVideos
      .slice(start, Math.min(end, currentVideos.length))
      .map(v => v.id)
      .join(',');

    window.open(`https://www.youtube.com/watch_videos?video_ids=${videoIds}`, '_blank');
  };

  // Handle refreshing the current feed
  const handleRefresh = () => {
    // Record start time
    const startTime = Date.now();
    setRefreshStartTime(startTime);
    setRefreshDuration(null);

    // Show toast notification for refresh start
    toast({
      title: "Refreshing videos",
      description: "Fetching the latest trending videos...",
      variant: "default",
    });

    // Define success callback
    const onSuccess = () => {
      // Calculate duration
      const endTime = Date.now();
      const duration = endTime - startTime;
      setRefreshDuration(duration);
      setLastRefreshTime(new Date());

      // Show success toast
      toast({
        title: "Refresh complete",
        description: `Refreshed ${selectedGroupId ? getCurrentGroupName() : "global"} videos in ${(duration / 1000).toFixed(1)} seconds`,
        variant: "success",
      });
    };

    // Define error callback
    const onError = (error: Error) => {
      // Calculate duration even for errors
      const endTime = Date.now();
      const duration = endTime - startTime;
      setRefreshDuration(duration);

      // Show error toast
      toast({
        title: "Refresh failed",
        description: error.message,
        variant: "destructive",
      });
    };

    // Perform the refresh with callbacks
    if (selectedGroupId) {
      refreshGroupVideos.mutate(selectedGroupId, {
        onSuccess,
        onError,
      });
    } else {
      refreshGlobalVideos.mutate(undefined, {
        onSuccess,
        onError,
      });
    }
  };

  // Memoize filtered global videos to prevent unnecessary recalculations
  const filteredGlobalVideos = useMemo(() => {
    // Use fallback videos if the new endpoint fails
    const videosToUse = globalError && oldVideos.length > 0 ? oldVideos : videos;

    // First, deduplicate videos by ID
    const uniqueVideos = new Map();
    videosToUse.forEach(video => {
      // If we haven't seen this video ID yet, or if this instance is newer, add it to the map
      if (!uniqueVideos.has(video.id) ||
          new Date(video.publishedAt) > new Date(uniqueVideos.get(video.id).publishedAt)) {
        uniqueVideos.set(video.id, video);
      }
    });

    // Convert the map values to an array
    const dedupedVideos = Array.from(uniqueVideos.values());
    console.log(`Global feed: ${videosToUse.length} total videos, ${dedupedVideos.length} after deduplication`);

    // Apply keyword filtering
    let filteredVideos = dedupedVideos;

    // Filter by active keywords if any
    if (activeKeywords.length > 0) {
      filteredVideos = filteredVideos.filter(video => {
        return activeKeywords.some(keyword =>
          video.title.toLowerCase().includes(keyword.toLowerCase())
        );
      });
    }

    // Apply search query filtering if search query exists
    if (searchQuery.trim()) {
      const query = searchQuery.trim().toLowerCase();
      filteredVideos = filteredVideos.filter(video => {
        return (
          video.title.toLowerCase().includes(query) ||
          video.channelTitle.toLowerCase().includes(query)
        );
      });
    }

    // Apply duration filtering
    if (durationFilter !== "all") {
      filteredVideos = filteredVideos.filter(video => {
        // Use the duration from contentDetails if available
        if (video.contentDetails?.duration) {
          try {
            // Parse the ISO 8601 duration format (PT8M10S = 8 minutes 10 seconds)
            const durationStr = video.contentDetails.duration;
            const minutesMatch = durationStr.match(/([0-9]+)M/);
            const secondsMatch = durationStr.match(/([0-9]+)S/);

            const minutes = minutesMatch ? parseInt(minutesMatch[1]) : 0;
            const seconds = secondsMatch ? parseInt(secondsMatch[1]) : 0;

            const totalSeconds = (minutes * 60) + seconds;

            // Consider videos less than or equal to 60 seconds as shorts
            const isShort = totalSeconds <= 60;

            if (durationFilter === "shorts") {
              return isShort;
            } else if (durationFilter === "videos") {
              return !isShort;
            }
          } catch (e) {
            console.error('Error parsing duration:', video.contentDetails.duration, e);
            return durationFilter !== "shorts"; // If parsing fails, include in videos but not shorts
          }
        }

        // If no duration info, include in videos but not shorts
        return durationFilter !== "shorts";
      });
    }

    // Apply unwatched filter
    if (showUnwatchedOnly && settings?.watchedVideos?.length) {
      filteredVideos = filteredVideos.filter(video => {
        return !settings.watchedVideos?.includes(video.id);
      });
    }

    return filteredVideos;
  }, [videos, activeKeywords, searchQuery, durationFilter, showUnwatchedOnly, settings?.watchedVideos]);

  // Memoize filtered group videos to prevent unnecessary recalculations
  const filteredGroupVideos = useMemo(() => {
    let filteredVideos = groupVideos;

    // Filter by active keywords if any
    if (activeKeywords.length > 0) {
      filteredVideos = filteredVideos.filter(video => {
        return activeKeywords.some(keyword =>
          video.title.toLowerCase().includes(keyword.toLowerCase())
        );
      });
    }

    // Apply search query filtering if search query exists
    if (searchQuery.trim()) {
      const query = searchQuery.trim().toLowerCase();
      filteredVideos = filteredVideos.filter(video => {
        return (
          video.title.toLowerCase().includes(query) ||
          video.channelTitle.toLowerCase().includes(query)
        );
      });
    }

    // Apply duration filtering
    if (durationFilter !== "all") {
      filteredVideos = filteredVideos.filter(video => {
        // Use the duration from contentDetails if available
        if (video.contentDetails?.duration) {
          try {
            // Parse the ISO 8601 duration format (PT8M10S = 8 minutes 10 seconds)
            const durationStr = video.contentDetails.duration;
            const minutesMatch = durationStr.match(/([0-9]+)M/);
            const secondsMatch = durationStr.match(/([0-9]+)S/);

            const minutes = minutesMatch ? parseInt(minutesMatch[1]) : 0;
            const seconds = secondsMatch ? parseInt(secondsMatch[1]) : 0;

            const totalSeconds = (minutes * 60) + seconds;

            // Consider videos less than or equal to 60 seconds as shorts
            const isShort = totalSeconds <= 60;

            if (durationFilter === "shorts") {
              return isShort;
            } else if (durationFilter === "videos") {
              return !isShort;
            }
          } catch (e) {
            console.error('Error parsing duration:', video.contentDetails.duration, e);
            return durationFilter !== "shorts"; // If parsing fails, include in videos but not shorts
          }
        }

        // If no duration info, include in videos but not shorts
        return durationFilter !== "shorts";
      });
    }

    // Apply unwatched filter
    if (showUnwatchedOnly && settings?.watchedVideos?.length) {
      filteredVideos = filteredVideos.filter(video => {
        return !settings.watchedVideos?.includes(video.id);
      });
    }

    return filteredVideos;
  }, [groupVideos, activeKeywords, searchQuery, durationFilter, showUnwatchedOnly, settings?.watchedVideos]);

  // Get the current videos based on selected group
  const currentVideos = selectedGroupId ? filteredGroupVideos : filteredGlobalVideos;

  // Force re-sort when sort method or group changes
  const sortKey = `${sortBy}-${selectedGroupId}-${currentVideos.length}`;

  // Memoize sorted videos to prevent unnecessary recalculations
  const sortedVideos = useMemo(() => {
    console.log(`Sorting videos with method: ${sortBy}`);
    console.log(`Number of videos to sort: ${currentVideos.length}`);
    console.log(`Current group: ${selectedGroupId === null ? 'Global Feed' : `Group ${selectedGroupId}`}`);

    // Create a new array to ensure we're not sorting the original array
    const videosToSort = [...currentVideos];

    // Use the enhanced sorting function from engagement-score.ts
    const result = sortVideos(videosToSort, sortBy);

    console.log(`Sorted result length: ${result.length}`);

    // Log a sample of the sorted results
    if (result.length > 0) {
      console.log('First video after sorting:', {
        title: result[0].title,
        publishedAt: result[0].publishedAt,
        viewCount: result[0].viewCount
      });

      if (result.length > 1) {
        console.log('Last video after sorting:', {
          title: result[result.length - 1].title,
          publishedAt: result[result.length - 1].publishedAt,
          viewCount: result[result.length - 1].viewCount
        });
      }
    }

    return result;
  }, [currentVideos, sortBy]); // Include sortBy and currentVideos as dependencies



  // Load more videos when scrolling
  const loadMoreVideos = useCallback(() => {
    if (isLoadingMore || !hasMore || isLoadingGlobalVideos || isLoadingGroupVideos) {
      console.log('Skipping loadMoreVideos due to:', {
        isLoadingMore,
        hasMore,
        isLoadingGlobalVideos,
        isLoadingGroupVideos
      });
      return;
    }

    console.log('Loading more videos...');
    setIsLoadingMore(true);

    // Simulate a small delay to prevent too rapid loading
    setTimeout(() => {
      const currentLength = displayedVideos.length;
      console.log(`Current displayed videos: ${currentLength}`);

      // Get the next batch of videos
      const newVideos = sortedVideos.slice(currentLength, currentLength + videosPerPage);
      console.log(`New videos to add: ${newVideos.length}`);

      if (newVideos.length > 0) {
        // Create a new array with all videos to avoid mutation issues
        const updatedVideos = [...displayedVideos, ...newVideos];
        setDisplayedVideos(updatedVideos);
        console.log(`Updated displayed videos: ${updatedVideos.length}`);
      }

      // Check if we've loaded all videos
      const allLoaded = currentLength + newVideos.length >= sortedVideos.length;
      console.log(`All videos loaded: ${allLoaded}`);
      setHasMore(!allLoaded);

      setIsLoadingMore(false);
    }, 300);
  }, [displayedVideos.length, hasMore, isLoadingGlobalVideos, isLoadingGroupVideos, isLoadingMore, sortedVideos, videosPerPage]);

  // Setup infinite scroll
  const { sentinelRef, pauseInfiniteScroll } = useInfiniteScroll(loadMoreVideos, {
    threshold: 0.5,
    rootMargin: '200px',
    enabled: hasMore && !isLoadingMore
  });

  // Reset displayed videos when filters change
  useEffect(() => {
    console.log('Sorted videos changed, updating displayed videos');
    console.log(`Sorted videos length: ${sortedVideos.length}`);
    console.log(`Current sort method: ${sortBy}`);
    console.log(`Current group: ${selectedGroupId === null ? 'Global Feed' : `Group ${selectedGroupId}`}`);
    console.log(`Sort key: ${sortKey}`);

    // Reset displayed videos with the first batch from sorted videos
    const initialVideos = sortedVideos.slice(0, videosPerPage);
    setDisplayedVideos(initialVideos);

    // Update hasMore flag based on whether there are more videos to load
    const moreAvailable = sortedVideos.length > videosPerPage;
    setHasMore(moreAvailable);

    console.log(`Initial displayed videos: ${initialVideos.length}, hasMore: ${moreAvailable}`);

    // Log the first few videos for debugging
    if (initialVideos.length > 0) {
      console.log('First displayed video:', {
        title: initialVideos[0].title,
        publishedAt: initialVideos[0].publishedAt,
        viewCount: initialVideos[0].viewCount
      });
    }

    // Save the current sort method to watch-page specific localStorage key
    localStorage.setItem('watchPageSortMethod', sortBy);
    // Remove the old shared key to prevent conflicts
    localStorage.removeItem('preferredSortMethod');

    // Set current feed videos for next/previous functionality in floating player
    // Only update the feed videos, but don't autoplay anything
    // Use a debounced update to prevent multiple feed updates when multiple state changes happen
    const updateFeedVideosTimer = setTimeout(() => {
      console.log('=== Updating current feed videos ===');
      console.log('Sorted videos count:', sortedVideos.length);
      console.log('Search query:', searchQuery);
      console.log('Selected group ID:', selectedGroupId);

      // The setCurrentFeedVideos function already has logic to preserve the current video
      // if it's playing, so we can just call it directly
      setCurrentFeedVideos(sortedVideos);

      console.log('Current feed videos updated for autoplay');
    }, 100); // Small delay to batch multiple updates

    return () => clearTimeout(updateFeedVideosTimer);
  }, [sortedVideos, videosPerPage, sortBy, selectedGroupId, sortKey, searchQuery, setCurrentFeedVideos]);

  const toggleKeyword = (keyword: string) => {
    setActiveKeywords(prev =>
      prev.includes(keyword)
        ? prev.filter(k => k !== keyword)
        : [...prev, keyword]
    );
  };

  // Get the current group name
  const getCurrentGroupName = () => {
    if (!selectedGroupId) return "Global Feed";
    const group = groups.find(g => g.id === selectedGroupId);
    return group ? group.name : "Selected Group";
  };

  // Check if we're loading videos
  const isLoading = selectedGroupId ? isLoadingGroupVideos : (isLoadingGlobalVideos && isLoadingOldVideos);

  // Get the current error
  const error = selectedGroupId ? null : globalError;

  return (
    <div className="flex h-screen overflow-hidden">
      <Toaster />
      <ScrollToTopButton threshold={300} />

      {/* Cast Queue Button - Always visible */}
        <div className="fixed bottom-32 right-4 z-50"> {/* Moved higher up from bottom-20 to bottom-32 */}
          <Button
            onClick={() => {
              // Refetch the queue before opening the dialog
              refetchQueue();
              setCastQueueOpen(true);
            }}
            className="rounded-full h-12 w-12 shadow-lg bg-primary hover:bg-primary/90"
            size="icon"
            title="Open Cast Queue"
          >
            <Cast className="h-5 w-5" />
            <Badge
              className="absolute -top-2 -right-2 h-6 w-6 flex items-center justify-center p-0 text-xs font-bold bg-secondary text-secondary-foreground"
              variant="secondary"
            >
              {castQueueState.queue.length}
            </Badge>
          </Button>
        </div>

      {/* Sidebar - Always present, either full or mini */}
      <Sidebar />

      <main className="flex-1 overflow-auto">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col gap-4 mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* Menu button removed - sidebar toggle is handled by the sidebar component */}
                <h1 className="text-2xl font-bold">Trendy</h1>
              </div>
              <div className="flex flex-col items-end gap-1">
                <div className="flex items-center gap-2">
                  <Button
                    onClick={handleRefresh}
                    disabled={refreshGlobalVideos.isPending || refreshGroupVideos.isPending}
                    variant="outline"
                    size={isMobile ? "sm" : "default"}
                    className="relative"
                  >
                    <RefreshCw
                      className={`h-4 w-4 mr-2 ${(refreshGlobalVideos.isPending || refreshGroupVideos.isPending) ? 'animate-spin' : ''}`}
                    />
                    {(refreshGlobalVideos.isPending || refreshGroupVideos.isPending) ? 'Refreshing...' : 'Refresh'} {selectedGroupId ? getCurrentGroupName() : ""}

                    {/* Show refresh progress indicator */}
                    {(refreshGlobalVideos.isPending || refreshGroupVideos.isPending) && refreshStartTime && (
                      <span className="absolute -bottom-1 left-0 h-1 bg-primary rounded-full" style={{
                        width: `${Math.min(((Date.now() - refreshStartTime) / 10000) * 100, 100)}%`,
                        transition: 'width 0.5s linear'
                      }}></span>
                    )}
                  </Button>

                  {/* Compact Grid Size Slider */}
                  <div className="flex items-center gap-2 bg-card rounded-md px-3 py-2 border border-primary/30 shadow-sm">
                    <div className="text-xs font-medium whitespace-nowrap text-foreground">Grid:</div>
                    <Slider
                      value={[gridSize]}
                      min={1}
                      max={6}
                      step={1}
                      onValueChange={handleGridSizeChange}
                      className="w-[80px] mx-1"
                    />
                    <div className="text-xs font-medium min-w-[14px] text-center bg-primary/20 text-primary-foreground px-2 py-0.5 rounded-md">
                      {gridSize}
                    </div>
                  </div>
                </div>

                {/* Last refresh time indicator */}
                <div className="text-xs text-muted-foreground flex items-center font-sans">
                  <Clock className="h-3 w-3 mr-1" />
                  Last refresh: {' '}
                  <span className="font-semibold ml-1">
                    {lastRefreshTime ? (
                      <>
                        {formatRefreshTime(lastRefreshTime)}
                        {refreshDuration && (
                          <span className="ml-1 font-normal">({(refreshDuration / 1000).toFixed(1)}s)</span>
                        )}
                      </>
                    ) : selectedGroupId ? (
                      // Show the group's last refresh time if available
                      (() => {
                        const group = groups.find(g => g.id === selectedGroupId);
                        return formatRefreshTime(group?.lastRefreshTime);
                      })()
                    ) : (
                      // Show the global feed's last refresh time if available
                      formatRefreshTime(settings?.lastRefreshTime)
                    )}
                  </span>
                </div>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="feed">Video Feed</TabsTrigger>
                <TabsTrigger value="groups">Keyword Groups</TabsTrigger>
              </TabsList>

              <TabsContent value="feed" className="space-y-4">
                {/* Multi-line Keyword Groups */}
                <div className="pb-2 px-1">
                  <h3 className="text-sm font-medium mb-2">Select Keyword Group:</h3>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant={selectedGroupId === null ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        console.log('Switching to Global Feed');
                        console.log(`Current sort method: ${sortBy}`);

                        // Update group selection
                        setSelectedGroupId(null);
                        setActiveGroup.mutate(null);

                        // Reset displayed videos but keep search query
                        setDisplayedVideos([]);
                      }}
                      className="whitespace-nowrap mb-1"
                    >
                      Global Feed
                    </Button>

                    {groups.map(group => (
                      <Button
                        key={group.id}
                        variant={selectedGroupId === group.id ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          console.log(`Switching to group ${group.id} (${group.name})`);
                          console.log(`Current sort method: ${sortBy}`);

                          // Update group selection
                          setSelectedGroupId(group.id);
                          setActiveGroup.mutate(group.id);

                          // Reset displayed videos but keep search query
                          setDisplayedVideos([]);
                        }}
                        className="whitespace-nowrap mb-1"
                      >
                        {group.name}
                      </Button>
                    ))}
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  {/* Search input */}
                  <div className="relative flex-1 min-w-[200px] max-w-md">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Search className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <Input
                      type="search"
                      placeholder="Search videos..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-10"
                    />
                    {searchQuery && (
                      <button
                        onClick={() => setSearchQuery('')}
                        className="absolute inset-y-0 right-0 flex items-center pr-3"
                        aria-label="Clear search"
                      >
                        <X className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                      </button>
                    )}
                  </div>

                  {/* Filter button removed as requested */}

                  <div className="flex gap-2">
                    <Select
                      value={sortBy}
                      onValueChange={(value) => {
                        console.log(`Changing sort method from ${sortBy} to ${value}`);

                        // Save sort preference to watch-page specific localStorage key
                        localStorage.setItem('watchPageSortMethod', value);
                        // Remove the old shared key to prevent conflicts
                        localStorage.removeItem('preferredSortMethod');

                        // Update state
                        setSortBy(value);

                        // Reset displayed videos but keep search query
                        setDisplayedVideos([]);

                        // We don't need to force a refetch here as the useEffect for sortBy changes
                        // will handle updating the displayed videos with the new sort method
                        // This prevents multiple feed reloads

                        // Save to user settings without triggering a notification or page reload
                        // This prevents the notification from causing another feed reload
                        if (settings) {
                          console.log('Updating sort preference silently:', value);
                          // Create an update with the sort method and preserve current duration filter
                          // This prevents other settings from being changed
                          updateSettings.mutate({
                            defaultSortMethod: value,
                            defaultDurationFilter: durationFilter // Preserve current duration filter
                          }, {
                            // Pass context to indicate this is a silent update
                            context: { silent: true },
                            // Override the onSuccess callback to show a notification but prevent other side effects
                            onSuccess: () => {
                              console.log('Sort preference updated successfully');
                              // Show a toast notification to confirm the save
                              toast({
                                title: "Sort Preference Saved",
                                description: `Videos will now be sorted by ${SORT_OPTIONS.find(option => option.value === value)?.label || value}`,
                                duration: 3000
                              });
                            }
                          });
                        }
                      }}
                    >
                      <SelectTrigger className={isMobile ? "w-[130px]" : "w-[180px]"}>
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        {SORT_OPTIONS.map(option => (
                          <SelectItem key={option.value} value={option.value} className="flex flex-col items-start py-2">
                            <div className="font-medium">{option.label}</div>
                            {option.description && (
                              <div className="text-xs text-muted-foreground mt-0.5">{option.description}</div>
                            )}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    {/* Duration Filter Toggle */}
                    <div className="flex items-center gap-1 bg-card rounded-md border border-input shadow-sm">
                      <Button
                        variant={durationFilter === "all" ? "default" : "ghost"}
                        size="sm"
                        className="rounded-r-none h-10"
                        onClick={() => {
                          setDurationFilter("all");
                          localStorage.setItem('preferredDurationFilter', "all");
                          // Also save the current sort to watch-page specific localStorage key
                          localStorage.setItem('watchPageSortMethod', sortBy);
                          // Remove the old shared key to prevent conflicts
                          localStorage.removeItem('preferredSortMethod');
                          setDisplayedVideos([]);

                          // Save to user settings silently while preserving current sort method
                          if (settings) {
                            console.log('Updating duration filter silently: all');
                            // Create an update with the duration filter and current sort method
                            updateSettings.mutate({
                              defaultDurationFilter: "all",
                              defaultSortMethod: sortBy // Preserve current sort method
                            }, {
                              context: { silent: true },
                              // Override the onSuccess callback to prevent any side effects
                              onSuccess: () => {
                                console.log('Duration filter updated successfully with sort method preserved');
                              }
                            });
                          }
                        }}
                      >
                        <Layers className="h-4 w-4 mr-1" />
                        All
                      </Button>
                      <Button
                        variant={durationFilter === "shorts" ? "default" : "ghost"}
                        size="sm"
                        className="rounded-none h-10"
                        onClick={() => {
                          setDurationFilter("shorts");
                          localStorage.setItem('preferredDurationFilter', "shorts");
                          // Also save the current sort to watch-page specific localStorage key
                          localStorage.setItem('watchPageSortMethod', sortBy);
                          // Remove the old shared key to prevent conflicts
                          localStorage.removeItem('preferredSortMethod');
                          setDisplayedVideos([]);

                          // Save to user settings silently while preserving current sort method
                          if (settings) {
                            console.log('Updating duration filter silently: shorts');
                            // Create an update with the duration filter and current sort method
                            updateSettings.mutate({
                              defaultDurationFilter: "shorts",
                              defaultSortMethod: sortBy // Preserve current sort method
                            }, {
                              context: { silent: true },
                              // Override the onSuccess callback to prevent any side effects
                              onSuccess: () => {
                                console.log('Duration filter updated successfully with sort method preserved');
                              }
                            });
                          }
                        }}
                      >
                        <Clock3 className="h-4 w-4 mr-1" />
                        Shorts
                      </Button>
                      <Button
                        variant={durationFilter === "videos" ? "default" : "ghost"}
                        size="sm"
                        className="rounded-none h-10"
                        onClick={() => {
                          setDurationFilter("videos");
                          localStorage.setItem('preferredDurationFilter', "videos");
                          // Also save the current sort to watch-page specific localStorage key
                          localStorage.setItem('watchPageSortMethod', sortBy);
                          // Remove the old shared key to prevent conflicts
                          localStorage.removeItem('preferredSortMethod');
                          setDisplayedVideos([]);

                          // Save to user settings silently while preserving current sort method
                          if (settings) {
                            console.log('Updating duration filter silently: videos');
                            // Create an update with the duration filter and current sort method
                            updateSettings.mutate({
                              defaultDurationFilter: "videos",
                              defaultSortMethod: sortBy // Preserve current sort method
                            }, {
                              context: { silent: true },
                              // Override the onSuccess callback to prevent any side effects
                              onSuccess: () => {
                                console.log('Duration filter updated successfully with sort method preserved');
                              }
                            });
                          }
                        }}
                      >
                        <VideoIcon className="h-4 w-4 mr-1" />
                        Videos
                      </Button>
                    </div>

                    {/* Unwatched Filter Toggle */}
                    <div className="flex items-center ml-2 gap-2">
                      <Button
                        variant={showUnwatchedOnly ? "default" : "outline"}
                        size="sm"
                        className="h-10"
                        onClick={() => {
                          const newValue = !showUnwatchedOnly;
                          setShowUnwatchedOnly(newValue);
                          localStorage.setItem('preferredUnwatchedFilter', String(newValue));
                          // Also save the current sort to watch-page specific localStorage key
                          localStorage.setItem('watchPageSortMethod', sortBy);
                          // Remove the old shared key to prevent conflicts
                          localStorage.removeItem('preferredSortMethod');
                          setDisplayedVideos([]);

                          // Save to user settings silently
                          if (settings) {
                            console.log(`Updating unwatched filter silently: ${newValue}`);
                            // Create an update with the unwatched filter and preserve current sort method and duration filter
                            updateSettings.mutate({
                              defaultUnwatchedFilter: newValue,
                              defaultSortMethod: sortBy, // Preserve current sort method
                              defaultDurationFilter: durationFilter // Preserve current duration filter
                            }, {
                              context: { silent: true },
                              // Override the onSuccess callback to prevent any side effects
                              onSuccess: () => {
                                console.log('Unwatched filter updated successfully with sort method and duration filter preserved');
                              }
                            });
                          }
                        }}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Unwatched
                      </Button>
                    </div>
                  </div>

                  <Select
                    value={selectedRange}
                    onValueChange={setSelectedRange}
                  >
                    <SelectTrigger className={isMobile ? "w-[130px]" : "w-[180px]"}>
                      <SelectValue placeholder="Select range" />
                    </SelectTrigger>
                    <SelectContent>
                      {PLAYLIST_RANGES.map(range => (
                        <SelectItem key={range.label} value={range.label}>
                          Videos {range.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Button
                    onClick={handlePlaylistGeneration}
                    disabled={!selectedRange || !currentVideos.length}
                    variant="outline"
                    size={isMobile ? "sm" : "default"}
                  >
                    <Share2 className="h-4 w-4 mr-2" />
                    Create Playlist
                  </Button>
                </div>

                {globalError && (
                  <Alert variant="destructive" className="mb-8">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{globalError.message}</AlertDescription>
                  </Alert>
                )}

                <VideoGrid
                  videos={displayedVideos}
                  isLoading={isLoadingGlobalVideos || isLoadingGroupVideos}
                  onPlayVideo={handlePlayVideo}
                  gridSize={gridSize}
                  pauseInfiniteScroll={pauseInfiniteScroll}
                />

                {/* Infinite Scroll Sentinel */}
                {hasMore && (
                  <div
                    ref={sentinelRef}
                    className="flex justify-center items-center py-8"
                  >
                    {isLoadingMore ? (
                      <div className="flex flex-col items-center">
                        <Loader2 className="h-6 w-6 animate-spin text-primary mb-2" />
                        <p className="text-sm text-muted-foreground">Loading more videos...</p>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Scroll for more videos</p>
                    )}
                  </div>
                )}

                {/* Video Count Info */}
                <div className="flex justify-between items-center mt-2 mb-4">
                  <div className="text-sm text-muted-foreground">
                    {searchQuery ? (
                      <span>
                        Found {sortedVideos.length} {sortedVideos.length === 1 ? 'video' : 'videos'} for "{searchQuery}"
                      </span>
                    ) : (
                      <span>
                        Showing {displayedVideos.length} of {sortedVideos.length} videos
                      </span>
                    )}
                  </div>
                  {!hasMore && sortedVideos.length > 0 && (
                    <div className="text-sm text-muted-foreground">
                      You've reached the end!
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="groups" className="space-y-4">
                <KeywordGroupManager />
              </TabsContent>
            </Tabs>

            {/* VideoPlayer component is no longer needed as we use FloatingVideoPlayer */}
          </div>
        </div>
      </main>

      {/* Cast Queue Dialog */}
      <Dialog open={castQueueOpen} onOpenChange={setCastQueueOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Cast className="h-5 w-5" />
              Cast Queue
            </DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            <CastQueueManager />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
