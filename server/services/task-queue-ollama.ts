import { workerManager, Task, TaskStatus, TaskPriority } from './worker-manager';
import { storage } from '../storage';
import { analyzeTranscriptWithOllama, convertOllamaAnalysisToAppFormat } from './ollama-financial-analysis';

// Import fetchTranscription from youtube-channels.ts
import { fetchTranscription } from '../routes/youtube-channels';

// Register handler for Ollama-based financial analysis
export function registerOllamaFinancialAnalysisHandler() {
  workerManager.registerHandler('ollama-financial-analysis', async (task, updateProgress) => {
    const { videoId, transcription, title, description, userId } = task.data;

    try {
      updateProgress(10);
      console.log(`Worker: Analyzing financial benefits for video ${videoId} using Ollama LLM`);

      // Use Ollama LLM for financial analysis
      const ollamaAnalysis = await analyzeTranscriptWithOllama(
        userId,
        transcription,
        title,
        description
      );
      updateProgress(50);

      console.log(`Worker: Ollama analysis complete for video ${videoId}`);

      // Convert Ollama analysis to app format
      const analysis = convertOllamaAnalysisToAppFormat(ollamaAnalysis);
      updateProgress(70);

      // Check what columns exist in the database
      const tableInfo = await storage.getTableInfo('youtube_videos');
      const hasOllamaColumns = tableInfo.some(column => column.name === 'ollama_benefit_amounts');
      const hasOllamaPromptColumns = tableInfo.some(column => column.name === 'ollama_prompt');
      const hasOllamaRawData = tableInfo.some(column => column.name === 'ollama_raw_data');

      console.log(`Worker: Database has Ollama columns: ${hasOllamaColumns}, prompt columns: ${hasOllamaPromptColumns}, raw data: ${hasOllamaRawData}`);

      // Create the analysis object with the basic fields
      const analysisData: any = {
        financialScore: analysis.score,
        financialCategory: analysis.category,
        financialAmount: analysis.amount,
        financialTimeline: analysis.timeline,
        financialRecipients: analysis.recipients,
        financialSteps: analysis.steps,
        financialViralPotential: analysis.viralPotential,
        financialSkepticism: analysis.skepticism,
        financialAnalysis: analysis.analysis,
        financialTimestamps: '',
        hasFinancialAnalysis: true
      };

      // Handle Ollama-specific fields
      if (hasOllamaColumns) {
        // Normalize benefit amounts
        let benefitAmounts = analysis.ollamaBenefitAmounts;
        if (benefitAmounts) {
          if (typeof benefitAmounts === 'string') {
            benefitAmounts = [benefitAmounts];
          } else if (!Array.isArray(benefitAmounts)) {
            benefitAmounts = [];
          }
        } else {
          benefitAmounts = [];
        }

        // Add Ollama fields
        analysisData.ollamaBenefitAmounts = benefitAmounts;
        analysisData.ollamaExpectedArrivalDate = analysis.ollamaExpectedArrivalDate || null;
        analysisData.ollamaEligiblePeople = analysis.ollamaEligiblePeople || null;
        analysisData.ollamaProofOrSource = analysis.ollamaProofOrSource || null;
        analysisData.ollamaActionsToClaim = analysis.ollamaActionsToClaim || null;
        analysisData.ollamaPriorityTag = analysis.ollamaPriorityTag || null;
        analysisData.ollamaReasonForPriority = analysis.ollamaReasonForPriority || null;
        analysisData.ollamaViralPotential = analysis.ollamaViralPotential || null;
        analysisData.ollamaModelUsed = analysis.ollamaModelUsed || null;
      }

      // Add prompt fields if they exist in the database
      if (hasOllamaPromptColumns) {
        analysisData.ollamaPrompt = analysis.ollamaPrompt || '';
        analysisData.ollamaSystemPrompt = analysis.ollamaSystemPrompt || '';
        analysisData.ollamaPromptName = analysis.ollamaPromptName || 'Default Prompt';
      }

      // Add raw data if it exists in the database
      if (hasOllamaRawData) {
        analysisData.ollamaRawData = analysis.ollamaRawData || '';
      }

      // Save analysis to database
      await storage.updateYoutubeVideoFinancialAnalysis(videoId, analysisData);
      updateProgress(90);

      // Return the analysis
      return {
        analysis,
        videoId,
        hasFinancialAnalysis: true
      };
    } catch (error) {
      console.error(`Worker: Error analyzing financial benefits with Ollama for video ${videoId}:`, error);
      throw error;
    }
  });
}

// Helper function to queue Ollama financial analysis for a video
export async function queueOllamaFinancialAnalysis(
  videoId: string,
  transcription: string,
  title: string,
  description: string,
  userId: number
): Promise<string> {
  const task = workerManager.addTask(
    'ollama-financial-analysis',
    `Analyze financial benefits for ${title} using Ollama LLM`,
    `Analyzing financial benefits for video ${videoId} using Ollama LLM`,
    userId,
    { videoId, transcription, title, description, userId },
    TaskPriority.NORMAL
  );

  return task.id;
}

// Helper function to automatically analyze transcription after fetching
export function registerAutoAnalysisAfterTranscription() {
  // Register a task listener for completed transcription tasks
  workerManager.on('task:completed', async (task) => {
    // Check if this is a completed transcription task
    if (task.type === 'fetch-transcription' && task.result) {
      const result = task.result;

      // If transcription was successful, queue financial analysis
      if (result && result.hasTranscription && result.transcription) {
        try {
          // Get the video details
          const video = await storage.getYoutubeVideo(result.videoId);

          if (video) {
            console.log(`Auto-queueing Ollama financial analysis for video ${result.videoId}`);

            // Queue Ollama financial analysis
            await queueOllamaFinancialAnalysis(
              result.videoId,
              result.transcription,
              video.title,
              video.description || '',
              task.userId
            );
          }
        } catch (error) {
          console.error(`Error auto-queueing financial analysis for video ${result.videoId}:`, error);
          // Continue anyway, don't fail the transcription task
        }
      }
    }
  });

  console.log('Registered auto-analysis after transcription');
}
