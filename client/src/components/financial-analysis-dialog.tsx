import React from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { YoutubeVideo } from '@shared/schema';

interface FinancialAnalysisDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  currentVideo: YoutubeVideo | null;
  transcript: string;
  analysisResults: {
    highlightedContent: string;
    counts: { positive: number; negative: number; neutral: number };
  };
}

export function FinancialAnalysisDialog({
  isOpen,
  onOpenChange,
  currentVideo,
  transcript,
  analysisResults
}: FinancialAnalysisDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Financial Term Analysis
            {currentVideo && (
              <Badge variant="outline" className="ml-2 text-xs">
                {currentVideo.title}
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Financial terms are highlighted: <span className="text-green-400">positive</span>, <span className="text-red-400">negative</span>, <span className="text-gray-400">neutral</span>
          </DialogDescription>
        </DialogHeader>
        
        {/* Financial Analysis Summary */}
        <div className="flex items-center gap-4 mb-4 p-2 border border-border rounded">
          <div className="text-sm font-medium">Financial Term Analysis:</div>
          <div className="flex gap-4">
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-green-400"></div>
              <span className="text-white">Positive: </span>
              <span className="font-bold text-green-400">{analysisResults.counts.positive}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-red-400"></div>
              <span className="text-white">Negative: </span>
              <span className="font-bold text-red-400">{analysisResults.counts.negative}</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-gray-400"></div>
              <span className="text-white">Neutral: </span>
              <span className="font-bold text-gray-400">{analysisResults.counts.neutral}</span>
            </div>
          </div>
        </div>
        
        {/* Transcript Content */}
        <ScrollArea className="h-[55vh]">
          {analysisResults.highlightedContent ? (
            <div 
              className="p-4 whitespace-pre-wrap"
              dangerouslySetInnerHTML={{ __html: analysisResults.highlightedContent }}
            />
          ) : (
            <div className="p-4 whitespace-pre-wrap">
              {transcript}
            </div>
          )}
        </ScrollArea>
        
        <DialogFooter>
          <Button 
            onClick={() => onOpenChange(false)}
            variant="outline"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
