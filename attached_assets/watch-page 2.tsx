import { useVideos } from "@/hooks/use-videos";
import { useSettings } from "@/hooks/use-settings";
import { VideoGrid } from "@/components/video-grid";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RefreshCw, AlertCircle, Share2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useEffect, useState } from "react";

const PLAYLIST_RANGES = [
  { label: "1-50", start: 0, end: 50 },
  { label: "50-100", start: 50, end: 100 },
  { label: "100-150", start: 100, end: 150 }
];

const SORT_OPTIONS = [
  { value: "popularity", label: "Popularity" },
  { value: "date_asc", label: "Upload Date (Oldest-Newest)" },
  { value: "date_desc", label: "Upload Date (Newest-Oldest)" },
  { value: "views_asc", label: "View Count (Lowest-Highest)" },
  { value: "views_desc", label: "View Count (Highest-Lowest)" },
];

export default function WatchPage() {
  const { videos, isLoading, error, refresh } = useVideos();
  const { settings, updateSettings } = useSettings();
  const [selectedRange, setSelectedRange] = useState<string>("");
  const [sortBy, setSortBy] = useState(settings?.sortBy || "popularity");

  // Auto-refresh based on interval setting
  useEffect(() => {
    if (!settings?.autoRefreshInterval) return;

    const lastRefresh = settings.lastRefreshTime ? new Date(settings.lastRefreshTime) : new Date(0);
    const minutesSinceLastRefresh = (Date.now() - lastRefresh.getTime()) / (1000 * 60);

    // If it's been longer than the interval since the last refresh, refresh immediately
    if (minutesSinceLastRefresh >= settings.autoRefreshInterval) {
      refresh.mutate();
    }

    // Set up periodic refresh
    const intervalId = setInterval(() => {
      refresh.mutate();
    }, settings.autoRefreshInterval * 60 * 1000);

    return () => clearInterval(intervalId);
  }, [settings?.autoRefreshInterval, settings?.lastRefreshTime]);

  const handlePlaylistGeneration = () => {
    if (!selectedRange || !videos.length) return;

    const range = PLAYLIST_RANGES.find(r => r.label === selectedRange);
    if (!range) return;

    const { start, end } = range;
    const videoIds = videos
      .slice(start, Math.min(end, videos.length))
      .map(v => v.id)
      .join(',');

    window.open(`https://www.youtube.com/watch_videos?video_ids=${videoIds}`, '_blank');
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    if (settings) {
      updateSettings.mutate({
        ...settings,
        sortBy: value
      });
    }
  };

  const sortedVideos = [...videos].sort((a, b) => {
    switch (sortBy) {
      case "date_asc":
        return new Date(a.publishedAt).getTime() - new Date(b.publishedAt).getTime();
      case "date_desc":
        return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
      case "views_asc":
        return a.viewCount - b.viewCount;
      case "views_desc":
        return b.viewCount - a.viewCount;
      default: // popularity
        return 0;
    }
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-wrap justify-between items-center mb-8 gap-4">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold">Trending Videos</h1>
          <Button 
            onClick={() => refresh.mutate()}
            disabled={refresh.isPending}
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refresh.isPending ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Select
            value={sortBy}
            onValueChange={handleSortChange}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={selectedRange}
            onValueChange={setSelectedRange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select video range" />
            </SelectTrigger>
            <SelectContent>
              {PLAYLIST_RANGES.map(range => (
                <SelectItem key={range.label} value={range.label}>
                  Videos {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            onClick={handlePlaylistGeneration}
            disabled={!selectedRange || !videos.length}
            variant="outline"
          >
            <Share2 className="h-4 w-4 mr-2" />
            Create Playlist
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-8">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      )}

      <VideoGrid videos={sortedVideos} isLoading={isLoading} />
    </div>
  );
}