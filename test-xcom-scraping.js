const axios = require('axios');
const cheerio = require('cheerio');

// Special scraper for X.com (Twitter)
async function scrapeXcom(url) {
  try {
    console.log('Using specialized X.com (Twitter) scraper for:', url);

    // Extract the tweet ID from the URL
    const tweetIdMatch = url.match(/\/status\/(\d+)/);
    if (!tweetIdMatch || !tweetIdMatch[1]) {
      console.log('Could not extract tweet ID from URL');
      return null;
    }

    const tweetId = tweetIdMatch[1];
    console.log('Tweet ID:', tweetId);

    // Add a random delay to prevent rate limiting (0-500ms)
    await new Promise(resolve => setTimeout(resolve, Math.floor(Math.random() * 500)));

    // Method 4: Try direct access with a mobile user agent as a last resort
    try {
      console.log('Trying direct access with mobile user agent for X.com');

      // Add cache-busting parameter
      const cacheBustUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;
      
      const response = await axios.get(cacheBustUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
        timeout: 15000
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);
      
      // Save the response to a file for inspection
      const fs = require('fs');
      fs.writeFileSync('xcom-response.html', response.data);
      console.log('Response saved to xcom-response.html');

      const $ = cheerio.load(response.data);

      // Get the tweet content
      const tweetContent = $('.tweet-content').html() ||
                          $('.tweet-text').html() ||
                          $('[data-testid="tweetText"]').html();

      console.log('Tweet content found:', !!tweetContent);
      if (tweetContent) {
        console.log('Tweet content length:', tweetContent.length);
        console.log('Tweet content preview:', tweetContent.substring(0, 100));
      }

      if (tweetContent && tweetContent.length > 10) {
        console.log('Found tweet content from direct access');

        // Get the author info
        const authorName = $('.tweet-author-name').text().trim() ||
                          $('.fullname').text().trim() ||
                          $('[data-testid="User-Name"]').text().trim();

        const authorUsername = $('.tweet-author-username').text().trim() ||
                              $('.username').text().trim() ||
                              $('[data-testid="User-Username"]').text().trim();

        // Get the tweet date
        const tweetDate = $('.tweet-date').text().trim() ||
                         $('.timestamp').text().trim() ||
                         $('[data-testid="tweet-time"]').text().trim();

        // Construct the content
        let fullContent = '';
        if (authorName) fullContent += `<h2>${authorName}</h2>`;
        if (authorUsername) fullContent += `<p class="tweet-username">${authorUsername}</p>`;
        if (tweetDate) fullContent += `<p class="tweet-date">${tweetDate}</p>`;

        fullContent += `<div class="tweet-content">${tweetContent}</div>`;
        fullContent += `<p class="source-note"><small>Content retrieved from X.com (Twitter) via direct access</small></p>`;

        return fullContent;
      } else {
        console.log('Direct access content is too short or empty, using fallback');
      }
    } catch (directError) {
      console.error('Error with direct access for X.com:', directError);
    }

    // Method 5: Use embedded tweet as a fallback
    try {
      console.log('Creating embedded tweet fallback');
      
      // Create an embedded tweet using Twitter's official widget
      const embedContent = `
        <div class="tweet-embed">
          <h2>Tweet from X.com</h2>
          <blockquote class="twitter-tweet" data-dnt="true">
            <a href="${url}"></a>
          </blockquote>
          <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>
          <p class="source-note"><small>Tweet embedded from X.com (Twitter)</small></p>
          <p><a href="${url}" target="_blank">View original tweet</a></p>
        </div>
      `;
      
      return embedContent;
    } catch (embedError) {
      console.error('Error creating embedded tweet:', embedError);
    }

    // Final fallback: Return a simple message with the tweet URL
    const fallbackContent = `
      <div class="tweet-fallback">
        <h2>Tweet Content</h2>
        <p>Could not retrieve the tweet content. Please view the original tweet at:</p>
        <p><a href="${url}" target="_blank">${url}</a></p>
        <p class="source-note"><small>X.com content could not be retrieved automatically</small></p>
      </div>
    `;

    return fallbackContent;
  } catch (error) {
    console.error('Error scraping X.com:', error);
    
    // Even if we have an error, return a fallback message rather than null
    // This ensures the user sees something rather than an error
    return `
      <div class="tweet-error">
        <h2>Tweet Content</h2>
        <p>An error occurred while retrieving the tweet content.</p>
        <p><a href="${url}" target="_blank">View original tweet</a></p>
        <p class="source-note"><small>Error retrieving X.com content</small></p>
      </div>
    `;
  }
}

// Test the function with the provided URL
async function testScraping() {
  const url = 'https://x.com/unusual_whales/status/1919461341313827020';
  console.log('Testing X.com scraping for URL:', url);
  
  try {
    const content = await scrapeXcom(url);
    console.log('Scraping result:', content ? 'Success' : 'Failed');
    if (content) {
      console.log('Content length:', content.length);
      console.log('Content preview:', content.substring(0, 200));
    }
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
testScraping();
