/**
 * Market Share Data Collector Service
 * Collects and stores market share data over time for chart visualization
 */

export interface MarketShareEntry {
  howToGuysViews: number;
  competitorViews: number;
  timestamp: number;
  marketShare: number;
  dayOfWeek?: string;
  date?: string;
  hour?: number;
}

class MarketShareCollector {
  private static instance: MarketShareCollector;
  private isCollecting = false;
  private collectionInterval: NodeJS.Timeout | null = null;
  private currentData: {
    howToGuysViews: number;
    competitorViews: number;
    marketShare: number;
  } | null = null;

  private constructor() {}

  public static getInstance(): MarketShareCollector {
    if (!MarketShareCollector.instance) {
      MarketShareCollector.instance = new MarketShareCollector();
    }
    return MarketShareCollector.instance;
  }

  /**
   * Update current data that will be collected
   */
  public updateCurrentData(howToGuysViews: number, competitorViews: number, marketShare: number): void {
    this.currentData = {
      howToGuysViews,
      competitorViews,
      marketShare
    };

    // Also save to localStorage for immediate access
    try {
      localStorage.setItem('current_market_share_data', JSON.stringify({
        ...this.currentData,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('Error saving current market share data:', error);
    }
  }

  /**
   * Start automatic data collection
   */
  public startCollection(): void {
    if (this.isCollecting) {
      console.log('Market Share Collector: Already running');
      return;
    }

    this.isCollecting = true;
    console.log('Market Share Collector: Starting automatic data collection');

    // Collect data immediately
    this.collectCurrentData();

    // Set up hourly collection
    this.collectionInterval = setInterval(() => {
      this.collectCurrentData();
    }, 60 * 60 * 1000); // Every hour
  }

  /**
   * Stop automatic data collection
   */
  public stopCollection(): void {
    if (!this.isCollecting) {
      console.log('Market Share Collector: Not running');
      return;
    }

    this.isCollecting = false;
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = null;
    }
    console.log('Market Share Collector: Stopped automatic data collection');
  }

  /**
   * Check if collector is running
   */
  public isRunning(): boolean {
    return this.isCollecting;
  }

  /**
   * Manually collect current data point
   */
  public collectCurrentData(): void {
    if (!this.currentData) {
      console.log('Market Share Collector: No current data to collect');
      return;
    }

    const now = Date.now();
    const currentDate = new Date(now);
    
    const entry: MarketShareEntry = {
      howToGuysViews: this.currentData.howToGuysViews,
      competitorViews: this.currentData.competitorViews,
      marketShare: this.currentData.marketShare,
      timestamp: now,
      dayOfWeek: currentDate.toLocaleDateString('en-US', { weekday: 'short' }),
      date: currentDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      hour: currentDate.getHours()
    };

    // Save to daily history
    this.saveDailyData(entry);
    
    // Save to hourly history
    this.saveHourlyData(entry);

    console.log('Market Share Collector: Data collected', {
      marketShare: entry.marketShare.toFixed(1) + '%',
      howToGuysViews: entry.howToGuysViews.toLocaleString(),
      competitorViews: entry.competitorViews.toLocaleString(),
      time: currentDate.toLocaleTimeString()
    });
  }

  /**
   * Save data to daily history
   */
  private saveDailyData(entry: MarketShareEntry): void {
    try {
      const storageKey = 'market_share_history';
      const existingHistory = JSON.parse(localStorage.getItem(storageKey) || '[]');
      
      // Add new entry
      const updatedHistory = [...existingHistory, entry];
      
      // Keep only last 7 days of data
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      const filteredHistory = updatedHistory.filter((item: MarketShareEntry) =>
        item.timestamp > sevenDaysAgo
      );
      
      localStorage.setItem(storageKey, JSON.stringify(filteredHistory));
    } catch (error) {
      console.error('Error saving daily market share data:', error);
    }
  }

  /**
   * Save data to hourly history
   */
  private saveHourlyData(entry: MarketShareEntry): void {
    try {
      const storageKey = 'market_share_hourly_history';
      const existingHistory = JSON.parse(localStorage.getItem(storageKey) || '[]');
      
      // Add new entry
      const updatedHistory = [...existingHistory, entry];
      
      // Keep only last 7 days of hourly data
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      const filteredHistory = updatedHistory.filter((item: MarketShareEntry) =>
        item.timestamp > sevenDaysAgo
      );
      
      localStorage.setItem(storageKey, JSON.stringify(filteredHistory));
    } catch (error) {
      console.error('Error saving hourly market share data:', error);
    }
  }

  /**
   * Get daily data for charts
   */
  public getDailyData(): MarketShareEntry[] {
    try {
      const storageKey = 'market_share_history';
      const data = JSON.parse(localStorage.getItem(storageKey) || '[]');
      return data.sort((a: MarketShareEntry, b: MarketShareEntry) => a.timestamp - b.timestamp);
    } catch (error) {
      console.error('Error loading daily market share data:', error);
      return [];
    }
  }

  /**
   * Get hourly data for a specific date
   */
  public getHourlyData(date: string): MarketShareEntry[] {
    try {
      const storageKey = 'market_share_hourly_history';
      const allData = JSON.parse(localStorage.getItem(storageKey) || '[]');
      
      // Filter data for the specific date
      const dateData = allData.filter((entry: MarketShareEntry) => {
        const entryDate = new Date(entry.timestamp).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        return entryDate === date;
      });
      
      return dateData.sort((a: MarketShareEntry, b: MarketShareEntry) => a.timestamp - b.timestamp);
    } catch (error) {
      console.error('Error loading hourly market share data:', error);
      return [];
    }
  }

  /**
   * Clear all collected data
   */
  public clearAllData(): void {
    try {
      localStorage.removeItem('market_share_history');
      localStorage.removeItem('market_share_hourly_history');
      localStorage.removeItem('current_market_share_data');
      console.log('Market Share Collector: All data cleared');
    } catch (error) {
      console.error('Error clearing market share data:', error);
    }
  }

  /**
   * Get current status
   */
  public getStatus(): {
    isRunning: boolean;
    hasCurrentData: boolean;
    dailyDataPoints: number;
    hourlyDataPoints: number;
  } {
    const dailyData = this.getDailyData();
    const today = new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    const hourlyData = this.getHourlyData(today);

    return {
      isRunning: this.isCollecting,
      hasCurrentData: this.currentData !== null,
      dailyDataPoints: dailyData.length,
      hourlyDataPoints: hourlyData.length
    };
  }
}

// Export singleton instance
export const marketShareCollector = MarketShareCollector.getInstance();
