import { workerManager, Task, TaskStatus, TaskPriority } from './worker-manager';
import { storage } from '../storage';
import { analyzeTranscriptWithOpenRouter, convertOpenRouterAnalysisToAppFormat } from './openrouter-financial-analysis';
import { isClientIdle } from '../routes/client-state';

// Helper function for consistent logging with timestamps
const logWithTimestamp = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] 🔄 OpenRouterTaskQueue: ${message}`, data ? data : '');
};

// Import fetchTranscription from youtube-channels.ts
import { fetchTranscription } from '../routes/youtube-channels';

// Register handler for OpenRouter-based financial analysis
export function registerOpenRouterFinancialAnalysisHandler() {
  workerManager.registerHandler('openrouter-financial-analysis', async (task, updateProgress) => {
    const { videoId, transcription, title, description, userId, source, retryCount = 0 } = task.data;

    try {
      updateProgress(10);
      logWithTimestamp(`BATCH ANALYSIS: Starting analysis for video ${videoId} using OpenRouter API (attempt ${retryCount + 1})`, {
        videoId,
        title,
        source: source || 'Unknown source',
        transcriptionLength: transcription ? transcription.length : 0,
        userId,
        retryCount
      });

      // Get the video to get the published date and view count
      const video = await storage.getYoutubeVideo(videoId);
      if (!video) {
        throw new Error(`Video ${videoId} not found`);
      }

      // Get user settings to determine which model to use
      const userSettings = await storage.getUserSettings(userId);
      const modelToUse = userSettings?.openRouterModel || 'google/gemini-2.0-flash-exp:free';
      const selectedPromptId = userSettings?.selectedPromptId;

      logWithTimestamp(`BATCH ANALYSIS: Using OpenRouter model: ${modelToUse} for video ${videoId}`, {
        modelToUse,
        selectedPromptId,
        autoAnalyzeEnabled: userSettings?.autoAnalyzeOnRefresh === true,
        retryCount
      });

      // Instead of making an HTTP request, directly call the analyzeTranscriptWithOpenRouter function
      // with the same parameters as the reanalysis endpoint would use
      updateProgress(30);

      // Get the published date and view count
      const publishedAt = video.publishedAt ? new Date(video.publishedAt) : undefined;
      const viewCount = video.viewCount || undefined;

      // Use the transcription from the video if not provided
      const videoTranscription = transcription || video.transcription;
      if (!videoTranscription) {
        throw new Error(`No transcription available for video ${videoId}`);
      }

      logWithTimestamp(`BATCH ANALYSIS: Calling analyzeTranscriptWithOpenRouter for video ${videoId}`, {
        videoId,
        title: video.title,
        publishedAt: publishedAt ? publishedAt.toISOString() : 'undefined',
        viewCount,
        modelToUse,
        retryCount
      });

      // Call the same function that the reanalysis endpoint uses
      const openRouterAnalysis = await analyzeTranscriptWithOpenRouter(
        userId,
        videoTranscription,
        video.title,
        video.description,
        publishedAt,
        viewCount,
        undefined, // No custom prompt
        modelToUse // Use the model from user settings
      );

      updateProgress(50);

      logWithTimestamp(`BATCH ANALYSIS: OpenRouter analysis complete for video ${videoId}`, {
        videoId,
        score: openRouterAnalysis.score,
        priorityTag: openRouterAnalysis.priorityTag,
        modelUsed: openRouterAnalysis.modelUsed,
        promptName: openRouterAnalysis.promptName
      });

      // Prepare benefit amounts for storage
      const benefitAmounts = openRouterAnalysis.extractedInfo?.benefitAmounts || [];
      const benefitAmountsJson = JSON.stringify(benefitAmounts);
      logWithTimestamp(`BATCH ANALYSIS: Benefit amounts for video ${videoId}`, {
        videoId,
        benefitAmounts,
        benefitAmountsJson,
        hasBenefitAmounts: benefitAmounts.length > 0
      });

      // Create the complete raw data
      const completeRawData = JSON.stringify({
        ...openRouterAnalysis,
        modelUsed: openRouterAnalysis.modelUsed || modelToUse
      });

      logWithTimestamp(`BATCH ANALYSIS: Created complete raw data for video ${videoId}`, {
        videoId,
        rawDataLength: completeRawData.length,
        modelUsed: openRouterAnalysis.modelUsed || modelToUse,
        promptName: openRouterAnalysis.promptName
      });

      // Update the video with the analysis results - store only in OpenRouter fields
      // This is the same code as in openrouter.ts
      await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
        // Basic financial fields
        financialScore: openRouterAnalysis.score,
        financialCategory: openRouterAnalysis.score >= 70 ? 'urgent' : openRouterAnalysis.score >= 50 ? 'anticipated' : 'doubtful',
        financialAmount: openRouterAnalysis.benefitDescription || '',
        financialTimeline: openRouterAnalysis.extractedInfo?.expectedArrivalDate || '',
        financialRecipients: openRouterAnalysis.extractedInfo?.eligiblePeople || '',
        financialSteps: openRouterAnalysis.extractedInfo?.actionsToClaim || '',
        financialViralPotential: '',
        financialSkepticism: '',
        financialAnalysis: openRouterAnalysis.benefitDescription || '',
        financialTimestamps: '',
        hasFinancialAnalysis: true,

        // Store raw data only in OpenRouter field
        openRouterRawData: completeRawData,

        // Store OpenRouter specific fields
        openRouterBenefitAmounts: benefitAmounts.length > 0 ? benefitAmountsJson : null,
        openRouterExpectedArrivalDate: openRouterAnalysis.extractedInfo?.expectedArrivalDate || '',
        openRouterEligiblePeople: openRouterAnalysis.extractedInfo?.eligiblePeople || '',
        openRouterProofOrSource: openRouterAnalysis.extractedInfo?.proofOrSource || '',
        openRouterActionsToClaim: openRouterAnalysis.extractedInfo?.actionsToClaim || '',
        openRouterPriorityTag: openRouterAnalysis.priorityTag || '',
        openRouterReasonForPriority: openRouterAnalysis.reasoning || '',
        openRouterViralPotential: openRouterAnalysis.viralPotential || '',
        openRouterModelUsed: openRouterAnalysis.modelUsed || modelToUse,
        openRouterPrompt: openRouterAnalysis.prompt || '',
        openRouterSystemPrompt: openRouterAnalysis.systemPrompt || '',
        openRouterPromptName: openRouterAnalysis.promptName || 'Default Prompt'
      });

      updateProgress(90);

      // Log success with detailed information about what was stored
      logWithTimestamp(`BATCH ANALYSIS: Successfully saved analysis data for video ${videoId}`, {
        videoId,
        financialScore: openRouterAnalysis.score,
        financialCategory: openRouterAnalysis.score >= 70 ? 'urgent' : openRouterAnalysis.score >= 50 ? 'anticipated' : 'doubtful',
        openRouterModelUsed: openRouterAnalysis.modelUsed || modelToUse,
        openRouterPromptName: openRouterAnalysis.promptName || 'Default Prompt',
        openRouterBenefitAmountsCount: benefitAmounts.length,
        openRouterRawDataSize: completeRawData.length,
        storedFields: [
          'financialScore', 'financialCategory', 'financialAmount', 'financialTimeline',
          'financialRecipients', 'financialSteps', 'openRouterRawData', 'openRouterBenefitAmounts',
          'openRouterExpectedArrivalDate', 'openRouterEligiblePeople', 'openRouterProofOrSource',
          'openRouterActionsToClaim', 'openRouterPriorityTag', 'openRouterReasonForPriority',
          'openRouterViralPotential', 'openRouterModelUsed', 'openRouterPrompt',
          'openRouterSystemPrompt', 'openRouterPromptName'
        ]
      });

      // Return the analysis
      return {
        analysis: openRouterAnalysis,
        videoId,
        hasFinancialAnalysis: true
      };
    } catch (error) {
      console.error(`Worker: Error analyzing financial benefits with OpenRouter for video ${videoId}:`, error);

      // Check if this is a rate limit error or other API error that might be resolved with a different key
      const errorMessage = error.message || '';
      const isRateLimitError = errorMessage.includes('rate limit') ||
                              errorMessage.includes('429') ||
                              errorMessage.includes('too many requests');
      const isApiKeyError = errorMessage.includes('API key') ||
                           errorMessage.includes('authentication') ||
                           errorMessage.includes('401') ||
                           errorMessage.includes('403');

      // Determine if we should retry
      const MAX_RETRIES = 3;
      const retryCount = task.data.retryCount || 0;
      const shouldRetry = (isRateLimitError || isApiKeyError) && retryCount < MAX_RETRIES;

      if (shouldRetry) {
        // Log the retry attempt
        logWithTimestamp(`BATCH ANALYSIS: Retrying analysis for video ${videoId} (attempt ${retryCount + 1}/${MAX_RETRIES})`, {
          videoId,
          errorType: isRateLimitError ? 'rate_limit' : isApiKeyError ? 'api_key' : 'other',
          errorMessage: errorMessage,
          retryCount: retryCount
        });

        // Create a new task with incremented retry count
        const newTask = workerManager.addTask(
          'openrouter-financial-analysis',
          `Retry analysis for ${task.data.title} (attempt ${retryCount + 1}/${MAX_RETRIES})`,
          `Retrying analysis for video ${videoId} (attempt ${retryCount + 1}/${MAX_RETRIES})`,
          task.data.userId,
          { ...task.data, retryCount: retryCount + 1 },
          TaskPriority.HIGH // Higher priority for retries
        );

        logWithTimestamp(`BATCH ANALYSIS: Queued retry task ${newTask.id} for video ${videoId}`, {
          taskId: newTask.id,
          videoId,
          retryCount: retryCount + 1
        });

        // Return a partial result indicating retry
        return {
          videoId,
          retried: true,
          newTaskId: newTask.id,
          hasFinancialAnalysis: false,
          error: errorMessage
        };
      }

      // If we're not retrying, mark the video as having failed analysis
      // This ensures it will be picked up in future batch operations
      try {
        // Update the video to indicate analysis was attempted but failed
        await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
          hasFinancialAnalysis: false,  // Mark as not having analysis so it will be retried later
          openRouterRawData: JSON.stringify({
            error: errorMessage,
            timestamp: new Date().toISOString(),
            retryCount: retryCount,
            source: task.data.source || 'batch_analysis'
          }),
          openRouterModelUsed: null,  // Clear model used so it will be retried
          lastAnalyzedAt: new Date().toISOString()  // Still update last analyzed time
        });

        logWithTimestamp(`BATCH ANALYSIS: Marked video ${videoId} as failed analysis (will be retried in future batch operations)`, {
          videoId,
          errorMessage: errorMessage,
          retryCount: retryCount
        });
      } catch (updateError) {
        console.error(`Worker: Error updating video ${videoId} after failed analysis:`, updateError);
      }

      // Rethrow the original error
      throw error;
    }
  });
}

// Helper function to queue OpenRouter financial analysis for a video
export async function queueOpenRouterFinancialAnalysis(
  videoId: string,
  transcription: string,
  title: string,
  description: string,
  userId: number,
  source: string = 'batch_refresh', // Track the source of the analysis request
  forceReanalysis: boolean = false, // Flag to force reanalysis even if analysis exists
  priority: TaskPriority = TaskPriority.NORMAL // Priority for the task
): Promise<string | null> {
  // First, check if the video already has a successful OpenRouter analysis
  if (!forceReanalysis) {
    try {
      const video = await storage.getYoutubeVideo(videoId);

      // Only skip if the video has a successful analysis (hasFinancialAnalysis=true AND openRouterModelUsed is set)
      if (video && video.hasFinancialAnalysis === true && video.openRouterModelUsed) {
        logWithTimestamp(`OPTIMIZATION: Skipping OpenRouter analysis for video ${videoId} - successful analysis already exists`, {
          videoId,
          title,
          source,
          existingModel: video.openRouterModelUsed,
          financialScore: video.financialScore,
          hasFinancialAnalysis: video.hasFinancialAnalysis
        });

        // Return null to indicate no task was created
        return null;
      }

      // Check if this is a previously failed analysis
      if (video && video.hasFinancialAnalysis === false && video.openRouterRawData) {
        try {
          // Try to parse the raw data to see if it contains error information
          const rawData = JSON.parse(video.openRouterRawData);
          if (rawData && rawData.error) {
            logWithTimestamp(`RETRY: Found previously failed analysis for video ${videoId}, will retry`, {
              videoId,
              title,
              source,
              previousError: rawData.error,
              previousTimestamp: rawData.timestamp,
              previousRetryCount: rawData.retryCount || 0
            });
          }
        } catch (parseError) {
          // If we can't parse the raw data, just continue with the analysis
          console.error(`Error parsing raw data for failed analysis of video ${videoId}:`, parseError);
        }
      }
    } catch (error) {
      // If there's an error checking the video, log it but continue with analysis
      console.error(`Error checking existing analysis for video ${videoId}:`, error);
    }
  }

  logWithTimestamp(`Queueing OpenRouter financial analysis for video ${videoId}`, {
    videoId,
    title,
    source,
    transcriptionLength: transcription ? transcription.length : 0,
    userId,
    forceReanalysis
  });

  const task = workerManager.addTask(
    'openrouter-financial-analysis',
    `Analyze financial benefits for ${title} using OpenRouter API`,
    `Analyzing financial benefits for video ${videoId} using OpenRouter API`,
    userId,
    { videoId, transcription, title, description, userId, source },
    priority // Use the provided priority
  );

  logWithTimestamp(`Queued OpenRouter financial analysis task ${task.id} for video ${videoId}`, {
    taskId: task.id,
    videoId,
    source
  });

  return task.id;
}

// Helper function to automatically analyze transcription after fetching
export function registerAutoAnalysisAfterTranscription() {
  // Register a task listener for completed transcription tasks
  workerManager.on('task:completed', async (task) => {
    // Check if this is a completed transcription task
    if (task.type === 'fetch-transcription' && task.result) {
      const result = task.result;

      // If transcription was successful, check user settings before queueing financial analysis
      if (result && result.hasTranscription && result.transcription) {
        try {
          // Check user settings to see if auto-analyze is enabled
          const userSettings = await storage.getUserSettings(task.userId);
          const autoAnalyzeEnabled = userSettings?.autoAnalyzeOnRefresh === true; // Must be explicitly true

          if (autoAnalyzeEnabled) {
            // Get the video details
            const video = await storage.getYoutubeVideo(result.videoId);

            if (video) {
              logWithTimestamp(`AUTO ANALYSIS: Queueing analysis after transcription for video ${result.videoId}`, {
                videoId: result.videoId,
                title: video.title,
                userId: task.userId,
                autoAnalyzeEnabled,
                source: 'transcription_complete'
              });

              // Check if the client is idle
              const isUserIdle = isClientIdle(task.userId);
              const priority = isUserIdle ? TaskPriority.LOW : TaskPriority.NORMAL;

              // Queue OpenRouter financial analysis
              const taskId = await queueOpenRouterFinancialAnalysis(
                result.videoId,
                result.transcription,
                video.title,
                video.description || '',
                task.userId,
                'transcription_complete', // Source of the analysis
                false, // Don't force reanalysis
                priority // Use appropriate priority based on client state
              );

              if (taskId === null) {
                logWithTimestamp(`AUTO ANALYSIS: Skipped analysis for video ${result.videoId} - analysis already exists`, {
                  videoId: result.videoId,
                  title: video.title,
                  userId: task.userId,
                  source: 'transcription_complete',
                  reason: 'analysis_exists'
                });
              }
            }
          } else {
            logWithTimestamp(`AUTO ANALYSIS: Skipping analysis for video ${result.videoId} (auto-analyze disabled)`, {
              videoId: result.videoId,
              userId: task.userId,
              autoAnalyzeEnabled
            });
          }
        } catch (error) {
          console.error(`Error auto-queueing financial analysis for video ${result.videoId}:`, error);
          // Continue anyway, don't fail the transcription task
        }
      }
    }
    // Also check if this is a completed channel refresh task
    else if ((task.type === 'refresh-channel-videos' || task.type === 'refresh-channel') && task.result) {
      const result = task.result;

      // If there are videos needing analysis, check user settings before queueing them
      if (result && result.videosNeedingAnalysis && result.videosNeedingAnalysis.length > 0) {
        try {
          // Check user settings to see if auto-analyze is enabled
          const userSettings = await storage.getUserSettings(task.userId);
          const autoAnalyzeEnabled = userSettings?.autoAnalyzeOnRefresh === true; // Must be explicitly true

          if (autoAnalyzeEnabled) {
            logWithTimestamp(`AUTO ANALYSIS: Queueing analysis for ${result.videosNeedingAnalysis.length} videos after channel refresh`, {
              channelId: result.channelId,
              channelTitle: result.channelTitle,
              videosCount: result.videosNeedingAnalysis.length,
              userId: task.userId,
              autoAnalyzeEnabled,
              source: 'channel_refresh'
            });

            // Process each video that needs analysis
            for (const videoId of result.videosNeedingAnalysis) {
              // Get the video details
              const video = await storage.getYoutubeVideo(videoId);

              if (video && video.hasTranscription && video.transcription) {
                logWithTimestamp(`AUTO ANALYSIS: Queueing analysis for video ${videoId} after channel refresh`, {
                  videoId,
                  title: video.title,
                  userId: task.userId,
                  transcriptionLength: video.transcription.length,
                  source: 'channel_refresh'
                });

                // Check if the client is idle
                const isUserIdle = isClientIdle(task.userId);
                const priority = isUserIdle ? TaskPriority.LOW : TaskPriority.NORMAL;

                // Queue OpenRouter financial analysis
                const taskId = await queueOpenRouterFinancialAnalysis(
                  videoId,
                  video.transcription,
                  video.title,
                  video.description || '',
                  task.userId,
                  'channel_refresh', // Source of the analysis
                  false, // Don't force reanalysis
                  priority // Use appropriate priority based on client state
                );

                if (taskId === null) {
                  logWithTimestamp(`AUTO ANALYSIS: Skipped analysis for video ${videoId} after channel refresh - analysis already exists`, {
                    videoId,
                    title: video.title,
                    userId: task.userId,
                    source: 'channel_refresh',
                    reason: 'analysis_exists'
                  });
                }
              }
            }
          } else {
            logWithTimestamp(`AUTO ANALYSIS: Skipping analysis for ${result.videosNeedingAnalysis.length} videos after channel refresh (auto-analyze disabled)`, {
              channelId: result.channelId,
              channelTitle: result.channelTitle,
              videosCount: result.videosNeedingAnalysis.length,
              userId: task.userId,
              autoAnalyzeEnabled
            });
          }
        } catch (error) {
          console.error(`Error auto-queueing financial analysis after channel refresh:`, error);
          // Continue anyway, don't fail the channel refresh task
        }
      }
    }
  });

  logWithTimestamp('Registered auto-analysis handlers for transcription and channel refresh with OpenRouter');
}
