import Database from 'better-sqlite3';

export function runMigration(db: Database) {
    console.log('Running migration: Adding auto_analyze_on_refresh column to settings table');

    try {
        // Check if the column already exists
        const columnInfo = db.prepare(`PRAGMA table_info(settings)`).all();
        const columnExists = columnInfo.some(col => col.name === 'auto_analyze_on_refresh');

        if (columnExists) {
            console.log('Column auto_analyze_on_refresh already exists in settings table');
            return;
        }

        // Add the column
        db.prepare(`ALTER TABLE settings ADD COLUMN auto_analyze_on_refresh BOOLEAN DEFAULT 1`).run();
        console.log('Added auto_analyze_on_refresh column to settings table');

    } catch (error) {
        console.error('Error in add-auto-analyze-column migration:', error);
        throw error;
    }
}

// Actually run the migration when this script is executed directly
// In ESM, we can't use require.main === module, so we'll just run it
const db = new Database('data.db');
runMigration(db);
db.close();
