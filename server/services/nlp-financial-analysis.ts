/**
 * NLP-Enhanced Financial Analysis Service
 *
 * This service uses natural language processing techniques to more accurately
 * identify financial benefits in video transcripts, with improved detection of:
 * - Benefit amounts vs. beneficiary counts
 * - Accurate magnitude handling (thousands vs millions)
 * - Contextual understanding of financial mentions
 */

import { BenefitFrequency, BenefitStatus, FinancialAmount, FinancialCategory } from './financial-analysis';

// Interface for NLP-detected financial entity
interface FinancialEntity {
  text: string;          // The raw text of the entity
  type: EntityType;      // The type of financial entity
  value: number;         // Normalized numeric value
  confidence: number;    // Confidence score (0-1)
  startIndex: number;    // Start position in text
  endIndex: number;      // End position in text
}

// Types of financial entities we can detect
enum EntityType {
  MONEY_AMOUNT = 'money_amount',           // Dollar amount (e.g., $1,200)
  BENEFIT_AMOUNT = 'benefit_amount',       // Explicit benefit amount (e.g., "you'll receive $1,200")
  BENEFICIARY_COUNT = 'beneficiary_count', // Count of people (e.g., "1.2 million people")
  PROGRAM_BUDGET = 'program_budget',       // Program total (e.g., "$1.9 trillion package")
  PERCENTAGE = 'percentage',               // Percentage (e.g., "20% of recipients")
  DATE = 'date',                           // Date mention (e.g., "January 15th")
  ELIGIBILITY = 'eligibility',             // Eligibility criteria (e.g., "if you earn less than")
  PROGRAM_NAME = 'program_name'            // Program name (e.g., "stimulus check", "Social Security")
}

// Context classification for financial mentions
enum ContextClass {
  INDIVIDUAL_BENEFIT = 'individual_benefit',   // Individual will receive this amount
  HOUSEHOLD_BENEFIT = 'household_benefit',     // Household will receive this amount
  PROGRAM_TOTAL = 'program_total',             // Total cost of program
  BENEFICIARY_COUNT = 'beneficiary_count',     // Number of people receiving benefits
  ELIGIBILITY_THRESHOLD = 'eligibility_threshold', // Income/threshold for eligibility
  UNRELATED = 'unrelated'                      // Unrelated financial mention
}

/**
 * Analyzes a transcript for financial benefits using NLP techniques
 * @param transcript The full transcript text
 * @param title The video title
 * @param description The video description (optional)
 * @returns Array of detected financial amounts with metadata
 */
export function analyzeTranscriptWithNLP(
  transcript: string,
  title: string,
  description?: string
): FinancialAmount[] {
  // Combine title, description, and transcript for context
  const fullText = `${title} ${description || ''} ${transcript}`;

  // 1. Extract all potential financial entities
  const entities = extractFinancialEntities(fullText);

  // 2. Analyze context around each entity
  const entitiesWithContext = entities.map(entity => {
    const context = extractContext(fullText, entity);
    const contextClass = classifyContext(context, entity);
    const frequency = determineFrequency(context);

    return {
      entity,
      context,
      contextClass,
      frequency
    };
  });

  // 3. Filter to include all money amounts, not just benefit amounts
  const moneyEntities = entitiesWithContext.filter(item =>
    item.entity.type === EntityType.MONEY_AMOUNT ||
    item.entity.type === EntityType.BENEFIT_AMOUNT ||
    item.contextClass === ContextClass.INDIVIDUAL_BENEFIT ||
    item.contextClass === ContextClass.HOUSEHOLD_BENEFIT
  );

  // 4. Convert to FinancialAmount objects
  const financialAmounts = moneyEntities.map(item => {
    // Calculate confidence based on context and entity type
    const confidence = calculateConfidence(item.entity, item.contextClass, item.context);

    return {
      raw: item.entity.text,
      value: item.entity.value,
      formatted: formatCurrency(item.entity.value),
      frequency: item.frequency,
      context: item.context,
      timestamp: '', // Will be filled in later if available
      confidence
    };
  });

  // 5. Sort by confidence and remove duplicates
  const uniqueAmounts = deduplicateAmounts(financialAmounts);

  return uniqueAmounts.sort((a, b) => b.confidence - a.confidence);
}

/**
 * Extract all potential financial entities from text
 */
function extractFinancialEntities(text: string): FinancialEntity[] {
  const entities: FinancialEntity[] = [];

  // Extract money amounts with dollar signs
  const dollarRegex = /\$\s*(\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?|\d+(?:\.\d{1,2})?)\s*(thousand|k|million|m|billion|b|trillion|t)?/gi;
  let match;

  while ((match = dollarRegex.exec(text)) !== null) {
    const rawAmount = match[1].replace(/,/g, '');
    let value = parseFloat(rawAmount);

    // Apply multiplier for magnitude words
    if (match[2]) {
      const magnitude = match[2].toLowerCase();
      if (magnitude === 'thousand' || magnitude === 'k') {
        value *= 1000;
      } else if (magnitude === 'million' || magnitude === 'm') {
        value *= 1000000;
      } else if (magnitude === 'billion' || magnitude === 'b') {
        value *= 1000000000;
      } else if (magnitude === 'trillion' || magnitude === 't') {
        value *= 1000000000000;
      }
    }

    entities.push({
      text: match[0],
      type: EntityType.MONEY_AMOUNT,
      value,
      confidence: 0.8, // Base confidence for dollar amounts
      startIndex: match.index,
      endIndex: match.index + match[0].length
    });
  }

  // Extract numeric amounts followed by "dollars"
  const dollarsRegex = /(\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?|\d+(?:\.\d{1,2})?)\s*(thousand|k|million|m|billion|b|trillion|t)?\s+(dollars|USD)/gi;

  while ((match = dollarsRegex.exec(text)) !== null) {
    const rawAmount = match[1].replace(/,/g, '');
    let value = parseFloat(rawAmount);

    // Apply multiplier for magnitude words
    if (match[2]) {
      const magnitude = match[2].toLowerCase();
      if (magnitude === 'thousand' || magnitude === 'k') {
        value *= 1000;
      } else if (magnitude === 'million' || magnitude === 'm') {
        value *= 1000000;
      } else if (magnitude === 'billion' || magnitude === 'b') {
        value *= 1000000000;
      } else if (magnitude === 'trillion' || magnitude === 't') {
        value *= 1000000000000;
      }
    }

    entities.push({
      text: match[0],
      type: EntityType.MONEY_AMOUNT,
      value,
      confidence: 0.7, // Slightly lower confidence than $ format
      startIndex: match.index,
      endIndex: match.index + match[0].length
    });
  }

  // Extract beneficiary counts
  const beneficiaryRegex = /(\d{1,3}(?:,\d{3})*|\d+(?:\.\d+)?)\s*(thousand|k|million|m|billion|b|trillion|t)?\s+(people|individuals|families|households|recipients|beneficiaries|americans)/gi;

  while ((match = beneficiaryRegex.exec(text)) !== null) {
    const rawAmount = match[1].replace(/,/g, '');
    let value = parseFloat(rawAmount);

    // Apply multiplier for magnitude words
    if (match[2]) {
      const magnitude = match[2].toLowerCase();
      if (magnitude === 'thousand' || magnitude === 'k') {
        value *= 1000;
      } else if (magnitude === 'million' || magnitude === 'm') {
        value *= 1000000;
      } else if (magnitude === 'billion' || magnitude === 'b') {
        value *= 1000000000;
      } else if (magnitude === 'trillion' || magnitude === 't') {
        value *= 1000000000000;
      }
    }

    entities.push({
      text: match[0],
      type: EntityType.BENEFICIARY_COUNT,
      value,
      confidence: 0.9, // High confidence for beneficiary counts
      startIndex: match.index,
      endIndex: match.index + match[0].length
    });
  }

  // Extract percentages
  const percentageRegex = /(\d{1,3}(?:\.\d+)?)%|\b(\d{1,3}(?:\.\d+)?)\s+percent\b/gi;

  while ((match = percentageRegex.exec(text)) !== null) {
    const value = parseFloat(match[1] || match[2]);

    entities.push({
      text: match[0],
      type: EntityType.PERCENTAGE,
      value,
      confidence: 0.9, // High confidence for percentages
      startIndex: match.index,
      endIndex: match.index + match[0].length
    });
  }

  // Extract program names
  const programRegex = /\b(stimulus|relief|benefit|payment|check|direct deposit|EIP|economic impact payment|PPP|unemployment|social security|SSI|SSDI|tax credit|child tax credit|earned income|EITC)\b/gi;

  while ((match = programRegex.exec(text)) !== null) {
    entities.push({
      text: match[0],
      type: EntityType.PROGRAM_NAME,
      value: 0, // No numeric value for program names
      confidence: 0.7,
      startIndex: match.index,
      endIndex: match.index + match[0].length
    });
  }

  return entities;
}

/**
 * Extract context around a financial entity
 */
function extractContext(text: string, entity: FinancialEntity): string {
  // Get a window of text around the entity (100 chars before and after)
  const contextStart = Math.max(0, entity.startIndex - 100);
  const contextEnd = Math.min(text.length, entity.endIndex + 100);

  return text.substring(contextStart, contextEnd);
}

/**
 * Classify the context of a financial entity
 */
function classifyContext(context: string, entity: FinancialEntity): ContextClass {
  const lowerContext = context.toLowerCase();

  // If it's already classified as a beneficiary count, return that
  if (entity.type === EntityType.BENEFICIARY_COUNT) {
    return ContextClass.BENEFICIARY_COUNT;
  }

  // Check for individual benefit indicators
  const individualBenefitIndicators = [
    'you will receive', 'you\'ll receive', 'you get', 'you\'ll get',
    'eligible for', 'qualify for', 'per person', 'per individual',
    'direct payment', 'stimulus check', 'your payment', 'your check',
    'your account', 'your bank', 'deposit', 'each person'
  ];

  if (individualBenefitIndicators.some(indicator => lowerContext.includes(indicator))) {
    return ContextClass.INDIVIDUAL_BENEFIT;
  }

  // Check for household benefit indicators
  const householdBenefitIndicators = [
    'per household', 'per family', 'each household', 'each family',
    'family of', 'household of', 'married couple', 'joint filers'
  ];

  if (householdBenefitIndicators.some(indicator => lowerContext.includes(indicator))) {
    return ContextClass.HOUSEHOLD_BENEFIT;
  }

  // Check for program total indicators
  const programTotalIndicators = [
    'package', 'bill', 'legislation', 'act', 'program', 'total',
    'budget', 'funding', 'allocated', 'cost', 'spending',
    'trillion dollar', 'billion dollar', 'million dollar'
  ];

  if (programTotalIndicators.some(indicator => lowerContext.includes(indicator))) {
    return ContextClass.PROGRAM_TOTAL;
  }

  // Check for eligibility threshold indicators
  const eligibilityIndicators = [
    'income', 'earn', 'making', 'threshold', 'limit', 'cap',
    'less than', 'more than', 'under', 'over', 'below', 'above',
    'phase out', 'cutoff'
  ];

  if (eligibilityIndicators.some(indicator => lowerContext.includes(indicator))) {
    return ContextClass.ELIGIBILITY_THRESHOLD;
  }

  // If the amount is very large and not clearly a benefit, assume it's a program total
  if (entity.value >= 1000000000) {
    return ContextClass.PROGRAM_TOTAL;
  }

  // Default to individual benefit for dollar amounts in typical ranges
  if (entity.type === EntityType.MONEY_AMOUNT && entity.value >= 100 && entity.value <= 10000) {
    return ContextClass.INDIVIDUAL_BENEFIT;
  }

  // Default to unrelated for other cases
  return ContextClass.UNRELATED;
}

/**
 * Determine the payment frequency from context
 */
function determineFrequency(context: string): BenefitFrequency {
  const lowerContext = context.toLowerCase();

  // Check for one-time payment indicators
  if (lowerContext.includes('one time') ||
      lowerContext.includes('one-time') ||
      lowerContext.includes('lump sum') ||
      lowerContext.includes('stimulus') ||
      lowerContext.includes('check')) {
    return BenefitFrequency.ONE_TIME;
  }

  // Check for weekly payment indicators
  if (lowerContext.includes('weekly') ||
      lowerContext.includes('per week') ||
      lowerContext.includes('a week') ||
      lowerContext.includes('each week')) {
    return BenefitFrequency.WEEKLY;
  }

  // Check for monthly payment indicators
  if (lowerContext.includes('monthly') ||
      lowerContext.includes('per month') ||
      lowerContext.includes('a month') ||
      lowerContext.includes('each month')) {
    return BenefitFrequency.MONTHLY;
  }

  // Check for yearly payment indicators
  if (lowerContext.includes('yearly') ||
      lowerContext.includes('per year') ||
      lowerContext.includes('a year') ||
      lowerContext.includes('annual') ||
      lowerContext.includes('annually')) {
    return BenefitFrequency.YEARLY;
  }

  // Default to unknown frequency
  return BenefitFrequency.UNKNOWN;
}

/**
 * Calculate confidence score for a financial amount
 */
function calculateConfidence(
  entity: FinancialEntity,
  contextClass: ContextClass,
  context: string
): number {
  let confidence = entity.confidence; // Start with base confidence

  // Adjust based on context class
  if (contextClass === ContextClass.INDIVIDUAL_BENEFIT) {
    confidence += 0.2; // Boost for clear individual benefits
  } else if (contextClass === ContextClass.HOUSEHOLD_BENEFIT) {
    confidence += 0.15; // Boost for household benefits
  } else if (contextClass === ContextClass.PROGRAM_TOTAL) {
    confidence -= 0.3; // Reduce for program totals
  } else if (contextClass === ContextClass.UNRELATED) {
    confidence -= 0.4; // Significantly reduce for unrelated mentions
  }

  // Adjust based on amount value
  const value = entity.value;

  // Typical benefit ranges get higher confidence
  if (value >= 500 && value <= 5000) {
    confidence += 0.15; // Common stimulus/benefit range
  } else if (value > 5000 && value <= 10000) {
    confidence += 0.05; // Less common but possible
  } else if (value > 10000 && value <= 50000) {
    confidence -= 0.1; // Uncommon for individual benefits
  } else if (value > 50000 && value <= 1000000) {
    confidence -= 0.2; // Rare for individual benefits
  } else if (value > 1000000) {
    confidence -= 0.3; // Very rare for individual benefits
  }

  // Check for strong benefit indicators in context
  const benefitIndicators = [
    'receive', 'get', 'eligible', 'qualify', 'payment', 'check',
    'deposit', 'direct deposit', 'stimulus', 'relief', 'benefit'
  ];

  const lowerContext = context.toLowerCase();
  const hasStrongIndicators = benefitIndicators.some(indicator =>
    lowerContext.includes(indicator)
  );

  if (hasStrongIndicators) {
    confidence += 0.1;
  }

  // Check for skepticism indicators
  const skepticismIndicators = [
    'fake', 'scam', 'hoax', 'false', 'rumor', 'not true', 'isn\'t true',
    'no evidence', 'no proof', 'misleading', 'misinformation'
  ];

  const hasSkepticism = skepticismIndicators.some(indicator =>
    lowerContext.includes(indicator)
  );

  if (hasSkepticism) {
    confidence -= 0.3;
  }

  // Ensure confidence is between 0 and 1
  return Math.max(0, Math.min(1, confidence));
}

/**
 * Format a numeric value as currency in simple numeric format
 */
function formatCurrency(value: number): string {
  // Format all values as simple numeric with commas
  return `$${value.toLocaleString()}`;
}

/**
 * Remove duplicate amounts, keeping the one with highest confidence
 */
function deduplicateAmounts(amounts: FinancialAmount[]): FinancialAmount[] {
  const uniqueAmounts: FinancialAmount[] = [];
  const valueMap = new Map<number, FinancialAmount>();

  // Group by value and keep the one with highest confidence
  for (const amount of amounts) {
    const existing = valueMap.get(amount.value);

    if (!existing || amount.confidence > existing.confidence) {
      valueMap.set(amount.value, amount);
    }
  }

  return Array.from(valueMap.values());
}

/**
 * Determine the financial category based on detected amounts and context
 */
export function determineFinancialCategory(
  amounts: FinancialAmount[],
  transcript: string
): { category: FinancialCategory; status: BenefitStatus } {
  if (!amounts || amounts.length === 0) {
    return {
      category: FinancialCategory.NONE,
      status: BenefitStatus.UNKNOWN
    };
  }

  const lowerTranscript = transcript.toLowerCase();

  // Check for confirmed benefits
  const confirmedIndicators = [
    'available now', 'available today', 'already approved',
    'already passed', 'signed into law', 'has been approved',
    'is available', 'can apply now', 'can claim now'
  ];

  const isConfirmed = confirmedIndicators.some(indicator =>
    lowerTranscript.includes(indicator)
  );

  // Check for approved but not yet available benefits
  const approvedIndicators = [
    'has been approved', 'has passed', 'was passed',
    'will be available', 'coming soon', 'in the coming',
    'will receive', 'will get', 'has been signed'
  ];

  const isApproved = approvedIndicators.some(indicator =>
    lowerTranscript.includes(indicator)
  );

  // Check for proposed benefits
  const proposedIndicators = [
    'proposed', 'bill', 'legislation', 'considering',
    'may pass', 'might pass', 'could pass',
    'being discussed', 'being debated', 'in talks'
  ];

  const isProposed = proposedIndicators.some(indicator =>
    lowerTranscript.includes(indicator)
  );

  // Check for skepticism indicators
  const skepticismIndicators = [
    'fake', 'scam', 'hoax', 'false', 'rumor', 'not true',
    'no evidence', 'no proof', 'misleading', 'misinformation',
    'fact check', 'debunked'
  ];

  const hasSkepticism = skepticismIndicators.some(indicator =>
    lowerTranscript.includes(indicator)
  );

  // Determine status
  let status: BenefitStatus;
  if (isConfirmed) {
    status = BenefitStatus.CONFIRMED;
  } else if (isApproved) {
    status = BenefitStatus.APPROVED;
  } else if (isProposed) {
    status = BenefitStatus.PROPOSED;
  } else if (hasSkepticism) {
    status = BenefitStatus.RUMORED;
  } else {
    status = BenefitStatus.UNKNOWN;
  }

  // Determine category
  let category: FinancialCategory;
  if (hasSkepticism || amounts[0].confidence < 0.4) {
    category = FinancialCategory.DOUBTFUL;
  } else if (isConfirmed) {
    category = FinancialCategory.URGENT;
  } else if (isApproved) {
    category = FinancialCategory.ANTICIPATED;
  } else if (isProposed) {
    category = FinancialCategory.PROPOSED;
  } else if (amounts[0].confidence >= 0.7) {
    // High confidence but unclear status - treat as anticipated
    category = FinancialCategory.ANTICIPATED;
  } else {
    category = FinancialCategory.DOUBTFUL;
  }

  return { category, status };
}
