import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useWorkflow } from './workflow-state';
import { 
  CheckCircle, 
  Circle, 
  ArrowRight, 
  RotateCcw,
  FileText,
  Scissors,
  Highlighter,
  Lightbulb,
  Settings,
  Bot
} from 'lucide-react';

const WorkflowProgress: React.FC = () => {
  const { workflowState, goToStep, resetWorkflow, getWorkflowProgress, canProceedToNext } = useWorkflow();

  const stepIcons = {
    'script-selection': FileText,
    'chunk-creation': Scissors,
    'highlight-analysis': Highlighter,
    'strategy-selection': Lightbulb,
    'configuration': Settings,
    'generation': Bot
  };

  const getStepStatus = (stepIndex: number) => {
    const step = workflowState.steps[stepIndex];
    if (step.completed) return 'completed';
    if (stepIndex === workflowState.currentStep) return 'current';
    if (stepIndex < workflowState.currentStep) return 'completed';
    return 'pending';
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'current': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-400 bg-gray-100';
    }
  };

  const handleStepClick = (stepIndex: number) => {
    // Allow access to any step at any time for maximum flexibility
    goToStep(stepIndex);
  };

  const handleNextStep = () => {
    if (workflowState.currentStep < workflowState.steps.length - 1) {
      goToStep(workflowState.currentStep + 1);
    }
  };

  const handlePreviousStep = () => {
    if (workflowState.currentStep > 0) {
      goToStep(workflowState.currentStep - 1);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            Script Generation Workflow (All Steps Optional)
          </CardTitle>
          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground">
              Progress: {getWorkflowProgress()}%
            </div>
            <Button onClick={resetWorkflow} variant="outline" size="sm">
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </div>
        <Progress value={getWorkflowProgress()} className="w-full" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Step Navigation */}
          <div className="grid grid-cols-1 md:grid-cols-6 gap-2">
            {workflowState.steps.map((step, index) => {
              const status = getStepStatus(index);
              const IconComponent = stepIcons[step.id as keyof typeof stepIcons];
              const isClickable = true; // All steps are always accessible

              return (
                <div
                  key={step.id}
                  className={`relative p-3 rounded-lg border-2 transition-all cursor-pointer ${
                    status === 'current' 
                      ? 'border-blue-500 bg-blue-50' 
                      : status === 'completed'
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 bg-gray-50'
                  } hover:shadow-md`}
                  onClick={() => handleStepClick(index)}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <div className={`p-1 rounded-full ${getStepColor(status)}`}>
                      {status === 'completed' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <IconComponent className="h-4 w-4" />
                      )}
                    </div>
                    <Badge 
                      variant={status === 'current' ? 'default' : status === 'completed' ? 'secondary' : 'outline'}
                      className="text-xs"
                    >
                      {index + 1}
                    </Badge>
                  </div>
                  <div className="text-xs font-medium">{step.name}</div>
                  
                  {/* Step Data Summary */}
                  <div className="text-xs text-muted-foreground mt-1">
                    {step.id === 'script-selection' && step.data.selectedScript && (
                      <span>✓ Script selected</span>
                    )}
                    {step.id === 'chunk-creation' && step.data.selectedChunks?.length > 0 && (
                      <span>✓ {step.data.selectedChunks.length} chunks</span>
                    )}
                    {step.id === 'highlight-analysis' && step.data.highlights?.length > 0 && (
                      <span>✓ {step.data.highlights.length} highlights</span>
                    )}
                    {step.id === 'strategy-selection' && step.data.selectedStrategies?.length > 0 && (
                      <span>✓ {step.data.selectedStrategies.length} strategies</span>
                    )}
                    {step.id === 'configuration' && step.data.title && (
                      <span>✓ Configured</span>
                    )}
                    {step.id === 'generation' && step.data.finalInstructions && (
                      <span>✓ Ready to generate</span>
                    )}
                  </div>

                  {/* Arrow for non-last steps */}
                  {index < workflowState.steps.length - 1 && (
                    <ArrowRight className="absolute -right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hidden md:block" />
                  )}
                </div>
              );
            })}
          </div>

          {/* Current Step Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-blue-900">
                  Step {workflowState.currentStep + 1}: {workflowState.steps[workflowState.currentStep]?.name}
                </h3>
                <p className="text-sm text-blue-700 mt-1">
                  {getStepDescription(workflowState.steps[workflowState.currentStep]?.id)}
                </p>
              </div>
              <div className="flex gap-2">
                {workflowState.currentStep > 0 && (
                  <Button onClick={handlePreviousStep} variant="outline" size="sm">
                    Previous
                  </Button>
                )}
                {workflowState.currentStep < workflowState.steps.length - 1 && (
                  <Button
                    onClick={handleNextStep}
                    size="sm"
                  >
                    Next Step
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Workflow Summary */}
          {getWorkflowProgress() > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium mb-2">Workflow Summary</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Source Script:</span>
                  <div className="font-medium">
                    {workflowState.steps[0]?.data.selectedScript?.title || 'Not selected'}
                  </div>
                </div>
                <div>
                  <span className="text-muted-foreground">Chunks:</span>
                  <div className="font-medium">
                    {workflowState.steps[1]?.data.selectedChunks?.length || 0} selected
                  </div>
                </div>
                <div>
                  <span className="text-muted-foreground">Highlights:</span>
                  <div className="font-medium">
                    {workflowState.steps[2]?.data.highlights?.length || 0} marked
                  </div>
                </div>
                <div>
                  <span className="text-muted-foreground">Strategies:</span>
                  <div className="font-medium">
                    {workflowState.steps[3]?.data.selectedStrategies?.length || 0} chosen
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const getStepDescription = (stepId: string): string => {
  switch (stepId) {
    case 'script-selection':
      return 'Choose a source script from your library (optional - you can skip this step).';
    case 'chunk-creation':
      return 'Select reusable content chunks (optional - add as many or as few as you want).';
    case 'highlight-analysis':
      return 'Mark engagement hotspots (optional - skip if you don\'t have analytics data).';
    case 'strategy-selection':
      return 'Choose writing strategies (optional - use your own approach if preferred).';
    case 'configuration':
      return 'Set script parameters (optional - defaults will be used if skipped).';
    case 'generation':
      return 'Generate ChatGPT instructions and save scripts (works with any combination of inputs).';
    default:
      return '';
  }
};

export default WorkflowProgress;
