/**
 * Request Batching Service
 * Batches multiple similar requests into single API calls to reduce server load
 */

interface BatchedRequest {
  urls: string[];
  resolve: (response: any) => void;
  reject: (error: Error) => void;
  timestamp: number;
}

class RequestBatcher {
  private static instance: RequestBatcher;
  private pendingBatches = new Map<string, BatchedRequest>();
  private batchTimeout: NodeJS.Timeout | null = null;
  private readonly BATCH_DELAY = 50; // 50ms delay to collect similar requests
  private readonly MAX_BATCH_SIZE = 10; // Maximum URLs per batch

  private constructor() {}

  public static getInstance(): RequestBatcher {
    if (!RequestBatcher.instance) {
      RequestBatcher.instance = new RequestBatcher();
    }
    return RequestBatcher.instance;
  }

  /**
   * Generate a batch key for similar requests
   */
  private generateBatchKey(baseUrl: string): string {
    // Extract the base search query without specific URL parameters
    try {
      const url = new URL(baseUrl);
      const searchQuery = url.searchParams.get('search_query') || '';
      const filters = url.searchParams.get('sp') || '';
      return `${searchQuery}:${filters}`;
    } catch {
      return baseUrl;
    }
  }

  /**
   * Add a request to the batch
   */
  public async batchRequest(url: string): Promise<any> {
    const batchKey = this.generateBatchKey(url);
    
    return new Promise((resolve, reject) => {
      const existingBatch = this.pendingBatches.get(batchKey);
      
      if (existingBatch) {
        // Add to existing batch if not already included
        if (!existingBatch.urls.includes(url)) {
          existingBatch.urls.push(url);
        }
        
        // Replace the resolve/reject to handle multiple callers
        const originalResolve = existingBatch.resolve;
        existingBatch.resolve = (response) => {
          originalResolve(response);
          resolve(response);
        };
        
        const originalReject = existingBatch.reject;
        existingBatch.reject = (error) => {
          originalReject(error);
          reject(error);
        };
      } else {
        // Create new batch
        const batch: BatchedRequest = {
          urls: [url],
          resolve,
          reject,
          timestamp: Date.now()
        };
        
        this.pendingBatches.set(batchKey, batch);
        
        // Schedule batch processing if not already scheduled
        if (!this.batchTimeout) {
          this.batchTimeout = setTimeout(() => {
            this.processBatches();
          }, this.BATCH_DELAY);
        }
      }
    });
  }

  /**
   * Process all pending batches
   */
  private async processBatches(): Promise<void> {
    this.batchTimeout = null;
    
    const batches = Array.from(this.pendingBatches.entries());
    this.pendingBatches.clear();
    
    console.log(`Request Batcher: Processing ${batches.length} batches`);
    
    for (const [batchKey, batch] of batches) {
      try {
        await this.processBatch(batchKey, batch);
      } catch (error) {
        console.error(`Request Batcher: Error processing batch ${batchKey}:`, error);
        batch.reject(error instanceof Error ? error : new Error(String(error)));
      }
    }
  }

  /**
   * Process a single batch
   */
  private async processBatch(batchKey: string, batch: BatchedRequest): Promise<void> {
    const { urls, resolve, reject } = batch;
    
    console.log(`Request Batcher: Processing batch ${batchKey} with ${urls.length} URLs`);
    
    try {
      // Make a single batched request to the server
      const response = await fetch('/api/youtube-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          searchUrl: urls,
          batched: true // Flag to indicate this is a batched request
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      resolve(data);
    } catch (error) {
      console.error(`Request Batcher: Batch request failed for ${batchKey}:`, error);
      reject(error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Check if a request should be batched
   */
  public shouldBatch(url: string): boolean {
    // Only batch YouTube search requests
    return url.includes('youtube.com/results') || url.includes('/api/youtube-search');
  }

  /**
   * Get current batch status
   */
  public getStatus(): {
    pendingBatches: number;
    totalUrls: number;
  } {
    const totalUrls = Array.from(this.pendingBatches.values())
      .reduce((sum, batch) => sum + batch.urls.length, 0);
    
    return {
      pendingBatches: this.pendingBatches.size,
      totalUrls
    };
  }

  /**
   * Clear all pending batches
   */
  public clearBatches(): void {
    for (const batch of this.pendingBatches.values()) {
      batch.reject(new Error('Batch cancelled'));
    }
    this.pendingBatches.clear();
    
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }
    
    console.log('Request Batcher: All batches cleared');
  }
}

// Export singleton instance
export const requestBatcher = RequestBatcher.getInstance();
