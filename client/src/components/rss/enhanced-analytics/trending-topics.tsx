import React, { useMemo, useState } from 'react';
import { RssFeedItem } from '@shared/schema';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Bar<PERSON>hart, TrendingUp, DollarSign, Clock } from 'lucide-react';
import * as RechartsPrimitive from 'recharts';
import { ChartContainer } from '@/components/ui/chart';
import { format, subDays, isAfter } from 'date-fns';
import { highlightFinancialTerms, FinancialTermType, getDominantSentiment } from '../financial-term-highlighter';
import { KeywordDetailDialog } from './keyword-detail-dialog';

interface TrendingTopicsProps {
  feedItems: RssFeedItem[];
  onOpenArticle?: (articleId: number) => void;
  onSaveArticle?: (articleId: number) => void;
  onOpenInBrowser?: (articleId: number) => void;
}

interface KeywordTrend {
  keyword: string;
  recentCount: number;
  previousCount: number;
  growthRate: number;
  articles: number[];
  sentiment: FinancialTermType | null;
  financialBenefit: boolean;
}

export function TrendingTopics({
  feedItems,
  onOpenArticle,
  onSaveArticle,
  onOpenInBrowser
}: TrendingTopicsProps) {
  const [selectedKeyword, setSelectedKeyword] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Calculate trending keywords
  const { trendingKeywords, recentKeywords, financialKeywords } = useMemo(() => {
    if (!feedItems || feedItems.length === 0) {
      return { trendingKeywords: [], recentKeywords: [], financialKeywords: [] };
    }

    // Define time periods for trend analysis
    const now = new Date();
    const recentPeriodStart = subDays(now, 3); // Last 3 days
    const previousPeriodStart = subDays(recentPeriodStart, 7); // 7 days before that

    // Extract all keywords from articles
    const keywordMap = new Map<string, KeywordTrend>();

    // Common words to exclude - including HTML/CSS related terms and other non-content words
    const excludeWords = new Set([
      // Common English words
      'the', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'as', 'is', 'are', 'was', 'were',
      'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'should', 'could', 'may',
      'might', 'must', 'can', 'that', 'this', 'these', 'those', 'it', 'they', 'them', 'their', 'from', 'not', 'no',
      'yes', 'but', 'or', 'if', 'then', 'else', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few',
      'more', 'most', 'some', 'such', 'than', 'too', 'very', 'just', 'even', 'also', 'only', 'so', 'than', 'too',

      // HTML/CSS related terms
      'style', 'class', 'div', 'span', 'color', 'width', 'height', 'margin', 'padding', 'border', 'font', 'text',
      'background', 'position', 'display', 'flex', 'grid', 'container', 'wrapper', 'header', 'footer', 'section',
      'article', 'main', 'aside', 'nav', 'menu', 'button', 'input', 'form', 'label', 'select', 'option', 'table',
      'tbody', 'thead', 'tfoot', 'tr', 'td', 'th', 'ul', 'ol', 'li', 'data', 'href', 'src', 'alt', 'title', 'id',
      'name', 'value', 'type', 'content', 'meta', 'link', 'script', 'html', 'body', 'head', 'image', 'img', 'svg',
      'path', 'rect', 'circle', 'line', 'polygon', 'important', 'white', 'black', 'blue', 'red', 'green', 'yellow',
      'purple', 'orange', 'gray', 'grey', 'brown', 'pink', 'cyan', 'magenta', 'rgba', 'rgb', 'hsla', 'hsl', 'px',
      'em', 'rem', 'vh', 'vw', 'auto', 'none', 'hidden', 'visible', 'block', 'inline', 'relative', 'absolute',
      'fixed', 'sticky', 'static', 'inherit', 'initial', 'unset', 'left', 'right', 'top', 'bottom', 'center',
      'middle', 'justify', 'align', 'items', 'content', 'wrap', 'nowrap', 'space', 'between', 'around', 'evenly',
      'stretch', 'baseline', 'start', 'end', 'weight', 'size', 'family', 'sans', 'serif', 'mono', 'bold', 'italic',
      'underline', 'uppercase', 'lowercase', 'capitalize', 'normal', 'small', 'large', 'medium', 'default', 'primary',
      'secondary', 'success', 'danger', 'warning', 'info', 'light', 'dark', 'muted', 'hover', 'focus', 'active',
      'disabled', 'selected', 'checked', 'valid', 'invalid', 'error', 'loading', 'animation', 'transition', 'transform',
      'rotate', 'scale', 'translate', 'skew', 'opacity', 'shadow', 'outline', 'overflow', 'scroll', 'auto', 'hidden',
      'visible', 'clip', 'ellipsis', 'truncate', 'break', 'word', 'wrap', 'nowrap', 'pre', 'code', 'quote', 'cite',
      'mark', 'highlight', 'strikethrough', 'superscript', 'subscript', 'indent', 'outdent', 'list', 'bullet', 'number',
      'decimal', 'alpha', 'roman', 'marker', 'counter', 'increment', 'reset', 'before', 'after', 'first', 'last',
      'nth', 'child', 'sibling', 'parent', 'root', 'host', 'document', 'window', 'viewport', 'screen', 'print',
      'speech', 'all', 'only', 'not', 'and', 'or', 'min', 'max', 'clamp', 'calc', 'var', 'attr', 'url', 'linear',
      'radial', 'conic', 'gradient', 'repeat', 'no-repeat', 'round', 'space', 'cover', 'contain', 'fill', 'fit',
      'aspect', 'ratio', 'object', 'float', 'clear', 'clearfix', 'collapse', 'separate', 'empty', 'target', 'current',
      'root', 'scope', 'host', 'slotted', 'part', 'theme', 'dark', 'light', 'mode', 'scheme', 'prefers', 'reduced',
      'motion', 'print', 'screen', 'speech', 'portrait', 'landscape', 'orientation', 'resolution', 'device', 'pixel',
      'ratio', 'width', 'height', 'aspect', 'hover', 'pointer', 'touch', 'stylus', 'keyboard', 'mouse', 'coarse',
      'fine', 'grid', 'interlace', 'progressive', 'slow', 'fast', 'scan', 'update', 'overflow', 'paged', 'optional',
      'required', 'valid', 'invalid', 'in-range', 'out-of-range', 'placeholder', 'shown', 'default', 'checked',
      'indeterminate', 'valid', 'invalid', 'user', 'agent', 'stylesheet', 'author', 'custom', 'property', 'variable',
      'animation', 'keyframes', 'from', 'to', 'delay', 'direction', 'duration', 'fill', 'iteration', 'count', 'name',
      'play', 'state', 'timing', 'function', 'transition', 'property', 'delay', 'duration', 'timing', 'function',
      'will', 'change', 'transform', 'origin', 'style', 'box', 'sizing', 'border', 'box', 'content', 'box', 'padding',
      'box', 'margin', 'box', 'outline', 'offset', 'clip', 'path', 'mask', 'filter', 'backdrop', 'blur', 'brightness',
      'contrast', 'drop', 'shadow', 'grayscale', 'hue', 'rotate', 'invert', 'opacity', 'saturate', 'sepia', 'url',
      'element', 'image', 'cross', 'fade', 'paint', 'counter', 'counters', 'attr', 'calc', 'cubic', 'bezier', 'steps',
      'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out', 'step-start', 'step-end', 'start', 'end', 'jump',
      'jump-start', 'jump-end', 'jump-none', 'jump-both', 'snap', 'proximity', 'mandatory', 'always', 'avoid',
      'page', 'column', 'region', 'avoid-page', 'avoid-column', 'avoid-region', 'recto', 'verso', 'left', 'right',
      'first', 'last', 'spread', 'clone', 'slice', 'fill', 'stroke', 'none', 'currentColor', 'transparent', 'initial',
      'inherit', 'unset', 'revert', 'revert-layer', 'layer', 'cascade', 'scope', 'container', 'query', 'condition',
      'supports', 'media', 'import', 'charset', 'namespace', 'keyframes', 'font-face', 'page', 'document', 'viewport',
      'counter-style', 'font-feature-values', 'property', 'value', 'selector', 'rule', 'declaration', 'block', 'sheet',
      'stylesheet', 'computed', 'used', 'actual', 'specified', 'resolved', 'cascaded', 'initial', 'inherit', 'unset',
      'revert', 'revert-layer', 'all', 'common', 'system', 'user', 'agent', 'author', 'important', 'normal', 'auto',
      'none', 'initial', 'inherit', 'unset', 'revert', 'revert-layer', 'all', 'common', 'system', 'user', 'agent',
      'author', 'important', 'normal', 'auto', 'none', 'initial', 'inherit', 'unset', 'revert', 'revert-layer',

      // Numbers and units
      'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'hundred', 'thousand',
      'million', 'billion', 'trillion', 'first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh', 'eighth',
      'ninth', 'tenth', 'last', 'next', 'previous', 'current', 'former', 'latter', 'final', 'initial', 'terminal',
      'percent', 'percentage', 'total', 'sum', 'average', 'mean', 'median', 'mode', 'range', 'minimum', 'maximum',
      'count', 'number', 'quantity', 'amount', 'level', 'rate', 'ratio', 'proportion', 'fraction', 'decimal', 'integer',
      'float', 'double', 'byte', 'bit', 'word', 'digit', 'figure', 'numeral', 'numeric', 'numerical', 'digital',
      'binary', 'octal', 'hexadecimal', 'decimal', 'base', 'radix', 'exponent', 'power', 'root', 'square', 'cube',
      'factorial', 'prime', 'composite', 'even', 'odd', 'positive', 'negative', 'neutral', 'zero', 'infinity',
      'undefined', 'null', 'nan', 'nil', 'void', 'empty', 'blank', 'clear', 'clean', 'pure', 'simple', 'complex',
      'compound', 'mixed', 'whole', 'part', 'piece', 'segment', 'section', 'division', 'subdivision', 'unit', 'element',
      'component', 'constituent', 'ingredient', 'factor', 'term', 'member', 'item', 'entry', 'record', 'field', 'cell',
      'node', 'vertex', 'edge', 'arc', 'path', 'cycle', 'loop', 'circuit', 'route', 'track', 'trail', 'course', 'way',
      'direction', 'orientation', 'bearing', 'heading', 'course', 'vector', 'scalar', 'tensor', 'matrix', 'array',
      'list', 'queue', 'stack', 'heap', 'tree', 'graph', 'network', 'web', 'mesh', 'grid', 'lattice', 'framework',
      'structure', 'architecture', 'design', 'pattern', 'template', 'model', 'prototype', 'instance', 'object', 'class',
      'type', 'kind', 'sort', 'variety', 'species', 'genus', 'family', 'order', 'class', 'phylum', 'kingdom', 'domain',
      'realm', 'sphere', 'world', 'universe', 'cosmos', 'nature', 'reality', 'existence', 'being', 'entity', 'thing',
      'object', 'item', 'article', 'artifact', 'product', 'good', 'ware', 'merchandise', 'commodity', 'stock', 'supply',
      'inventory', 'asset', 'resource', 'material', 'substance', 'matter', 'stuff', 'medium', 'element', 'compound',
      'mixture', 'solution', 'suspension', 'colloid', 'gel', 'solid', 'liquid', 'gas', 'plasma', 'state', 'phase',
      'condition', 'status', 'situation', 'circumstance', 'context', 'environment', 'setting', 'background', 'milieu',
      'atmosphere', 'climate', 'weather', 'temperature', 'pressure', 'humidity', 'precipitation', 'rainfall', 'snowfall',
      'wind', 'breeze', 'gale', 'storm', 'hurricane', 'typhoon', 'cyclone', 'tornado', 'twister', 'whirlwind', 'dust',
      'devil', 'waterspout', 'spout', 'funnel', 'cloud', 'fog', 'mist', 'haze', 'smog', 'smoke', 'vapor', 'steam',
      'condensation', 'evaporation', 'sublimation', 'deposition', 'freezing', 'melting', 'boiling', 'vaporization',
      'condensation', 'precipitation', 'crystallization', 'dissolution', 'solution', 'mixture', 'compound', 'element',
      'atom', 'molecule', 'ion', 'radical', 'group', 'functional', 'substituent', 'ligand', 'complex', 'coordination',
      'chelate', 'cluster', 'aggregate', 'colloid', 'suspension', 'emulsion', 'foam', 'gel', 'sol', 'aerosol', 'spray',
      'mist', 'fog', 'cloud', 'vapor', 'gas', 'liquid', 'solid', 'plasma', 'state', 'phase', 'transition', 'change',
      'transformation', 'conversion', 'alteration', 'modification', 'variation', 'mutation', 'evolution', 'development',
      'growth', 'expansion', 'contraction', 'shrinkage', 'reduction', 'diminution', 'increase', 'decrease', 'rise',
      'fall', 'fluctuation', 'oscillation', 'vibration', 'wave', 'pulse', 'signal', 'noise', 'static', 'interference',
      'distortion', 'disturbance', 'perturbation', 'disruption', 'interruption', 'break', 'pause', 'stop', 'halt',
      'cease', 'end', 'finish', 'complete', 'conclude', 'terminate', 'discontinue', 'abort', 'cancel', 'nullify',
      'void', 'invalidate', 'revoke', 'rescind', 'repeal', 'annul', 'abrogate', 'quash', 'vacate', 'set', 'aside',
      'overrule', 'overturn', 'reverse', 'undo', 'negate', 'neutralize', 'counteract', 'offset', 'compensate',
      'balance', 'equilibrate', 'stabilize', 'steady', 'fix', 'secure', 'fasten', 'attach', 'connect', 'join', 'link',
      'couple', 'pair', 'mate', 'match', 'fit', 'suit', 'correspond', 'correlate', 'associate', 'relate', 'refer',
      'pertain', 'apply', 'concern', 'regard', 'respect', 'touch', 'affect', 'influence', 'impact', 'effect', 'result',
      'consequence', 'outcome', 'output', 'product', 'yield', 'return', 'profit', 'gain', 'benefit', 'advantage',
      'edge', 'lead', 'margin', 'surplus', 'excess', 'remainder', 'rest', 'residue', 'remnant', 'leftover', 'scrap',
      'fragment', 'piece', 'bit', 'morsel', 'crumb', 'speck', 'spot', 'dot', 'point', 'mark', 'stain', 'blot', 'smudge',
      'smear', 'streak', 'line', 'stroke', 'dash', 'hyphen', 'underscore', 'tilde', 'caret', 'circumflex', 'accent',
      'grave', 'acute', 'macron', 'breve', 'caron', 'cedilla', 'diaeresis', 'umlaut', 'tilde', 'circumflex', 'accent',
      'mark', 'diacritic', 'punctuation', 'period', 'full', 'stop', 'comma', 'semicolon', 'colon', 'exclamation',
      'question', 'mark', 'quotation', 'quote', 'apostrophe', 'parenthesis', 'bracket', 'brace', 'angle', 'bracket',
      'chevron', 'guillemet', 'slash', 'backslash', 'vertical', 'bar', 'pipe', 'ampersand', 'asterisk', 'star',
      'dagger', 'double', 'dagger', 'section', 'paragraph', 'pilcrow', 'bullet', 'interpunct', 'middle', 'dot',
      'ellipsis', 'leader', 'dash', 'hyphen', 'minus', 'plus', 'equals', 'percent', 'permille', 'per', 'thousand',
      'basis', 'point', 'permyriad', 'per', 'ten', 'thousand', 'degree', 'minute', 'second', 'prime', 'double',
      'prime', 'triple', 'prime', 'quadruple', 'prime', 'quintuple', 'prime', 'sextuple', 'prime', 'septuple',
      'prime', 'octuple', 'prime', 'nonuple', 'prime', 'decuple', 'prime', 'undecuple', 'prime', 'duodecuple',
      'prime', 'tredecuple', 'prime', 'quattuordecuple', 'prime', 'quindecuple', 'prime', 'sexdecuple', 'prime',
      'septendecuple', 'prime', 'octodecuple', 'prime', 'novemdecuple', 'prime', 'vigintuple', 'prime'
    ]);

    // Process each feed item
    feedItems.forEach(item => {
      // Skip items without content
      if (!item.content && !item.title) return;

      // Combine title and content for analysis
      let text = `${item.title || ''} ${item.content || ''} ${item.scrapedContent || ''}`;

      // Clean HTML content thoroughly
      text = text
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, ' ') // Remove style tags and their content
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, ' ') // Remove script tags and their content
        .replace(/<svg[^>]*>[\s\S]*?<\/svg>/gi, ' ') // Remove SVG tags and their content
        .replace(/<[^>]*>/g, ' ') // Remove all remaining HTML tags
        .replace(/&[a-z]+;/g, ' ') // Remove HTML entities
        .replace(/\s+/g, ' ') // Normalize whitespace
        .replace(/[^\w\s]/g, ' ') // Remove punctuation
        .replace(/\b\d+\b/g, ' ') // Remove standalone numbers
        .replace(/\b[a-z]\b/g, ' ') // Remove single characters
        .replace(/\b[a-z]{1,2}\b/g, ' ') // Remove very short words (1-2 chars)
        .replace(/\s+/g, ' ') // Normalize whitespace again
        .trim(); // Trim leading/trailing whitespace

      // Get financial sentiment
      const { counts } = highlightFinancialTerms(text);
      const sentiment = getDominantSentiment(counts);

      // Check if this article has financial benefits
      const hasFinancialBenefit =
        sentiment === FinancialTermType.POSITIVE &&
        counts.positive > 2 &&
        (item.financialBenefitAmount || text.includes('$'));

      // Extract keywords with better filtering
      const words = text.toLowerCase()
        .split(/\s+/)
        .filter(word => {
          // Only include words that:
          // 1. Are at least 4 characters long
          // 2. Are not in the exclude list
          // 3. Don't contain digits
          // 4. Don't look like CSS measurements (e.g., 10px, 2em)
          // 5. Don't look like HTML/CSS properties
          return word.length > 3 &&
                 !excludeWords.has(word) &&
                 !/\d/.test(word) &&
                 !/^\d+[a-z]{1,3}$/.test(word) &&
                 !/^[a-z]+\-[a-z]+$/.test(word);
        });

      // Count unique keywords in this article
      const articleKeywords = new Set(words);

      // Determine if the article is recent or from the previous period
      const publishDate = new Date(item.publishedAt);
      const isRecent = isAfter(publishDate, recentPeriodStart);
      const isPrevious = isAfter(publishDate, previousPeriodStart) && !isRecent;

      // Update keyword trends
      articleKeywords.forEach(keyword => {
        if (!keywordMap.has(keyword)) {
          keywordMap.set(keyword, {
            keyword,
            recentCount: 0,
            previousCount: 0,
            growthRate: 0,
            articles: [],
            sentiment,
            financialBenefit: hasFinancialBenefit
          });
        }

        const keywordData = keywordMap.get(keyword)!;

        // Update counts based on period
        if (isRecent) {
          keywordData.recentCount++;
        } else if (isPrevious) {
          keywordData.previousCount++;
        }

        // Add article ID if not already included
        if (!keywordData.articles.includes(item.id)) {
          keywordData.articles.push(item.id);
        }

        // Update sentiment and financial benefit status
        if (sentiment === FinancialTermType.POSITIVE) {
          keywordData.sentiment = FinancialTermType.POSITIVE;
        }

        if (hasFinancialBenefit) {
          keywordData.financialBenefit = true;
        }
      });
    });

    // Calculate growth rates and filter for trending keywords
    const keywordTrends = Array.from(keywordMap.values())
      .map(data => {
        // Calculate growth rate (avoid division by zero)
        const growthRate = data.previousCount > 0
          ? (data.recentCount - data.previousCount) / data.previousCount
          : data.recentCount > 0 ? 1 : 0;

        return {
          ...data,
          growthRate
        };
      })
      .filter(data => {
        // Apply stricter filtering criteria:
        // 1. At least 3 mentions in recent period
        // 2. Must appear in at least 2 different articles
        // 3. Must be at least 4 characters long
        return data.recentCount >= 3 &&
               data.articles.length >= 2 &&
               data.keyword.length >= 4;
      });

    // Sort by growth rate for trending keywords
    const trendingKeywords = [...keywordTrends]
      .sort((a, b) => b.growthRate - a.growthRate)
      .slice(0, 15);

    // Sort by recent count for most mentioned recent keywords
    const recentKeywords = [...keywordTrends]
      .sort((a, b) => b.recentCount - a.recentCount)
      .slice(0, 15);

    // Filter for keywords with financial benefits
    const financialKeywords = keywordTrends
      .filter(data => data.financialBenefit)
      .sort((a, b) => b.recentCount - a.recentCount)
      .slice(0, 10);

    return { trendingKeywords, recentKeywords, financialKeywords };
  }, [feedItems]);

  // Handle keyword click
  const handleKeywordClick = (keyword: string) => {
    setSelectedKeyword(keyword);
    setIsDialogOpen(true);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Trending Topics Chart */}
      <Card className="md:col-span-2">
        <CardHeader className="pb-2">
          <CardTitle className="text-white flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-primary" />
            Trending Topics
          </CardTitle>
          <CardDescription className="text-white">
            Topics with the highest growth rate in recent articles
          </CardDescription>
        </CardHeader>
        <CardContent>
          {trendingKeywords.length === 0 ? (
            <div className="flex justify-center items-center h-[200px]">
              <p className="text-white text-lg">No trending topics identified</p>
            </div>
          ) : (
            <div className="h-[350px]">
              <ChartContainer
                config={{
                  growthRate: { color: "#3b82f6" },
                  recentCount: { color: "#10b981" }
                }}
              >
                <RechartsPrimitive.BarChart
                  data={trendingKeywords.slice(0, 8).map(item => ({
                    keyword: item.keyword,
                    growthRate: parseFloat((item.growthRate * 100).toFixed(0)),
                    recentCount: item.recentCount
                  }))}
                  margin={{ top: 20, right: 50, left: 50, bottom: 80 }}
                  barGap={20}
                  barSize={30}
                >
                  <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" />
                  <RechartsPrimitive.XAxis
                    dataKey="keyword"
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    interval={0}
                    tick={{ fontSize: 12 }}
                    tickMargin={10}
                  />
                  <RechartsPrimitive.YAxis
                    yAxisId="left"
                    orientation="left"
                    label={{ value: 'Growth Rate (%)', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle' } }}
                  />
                  <RechartsPrimitive.YAxis
                    yAxisId="right"
                    orientation="right"
                    label={{ value: 'Recent Mentions', angle: 90, position: 'insideRight', style: { textAnchor: 'middle' } }}
                  />
                  <RechartsPrimitive.Tooltip />
                  <RechartsPrimitive.Legend wrapperStyle={{ paddingTop: '20px' }} />
                  <RechartsPrimitive.Bar
                    dataKey="growthRate"
                    name="Growth Rate (%)"
                    fill="var(--color-growthRate)"
                    yAxisId="left"
                    onClick={(data) => handleKeywordClick(data.keyword)}
                    cursor="pointer"
                  />
                  <RechartsPrimitive.Bar
                    dataKey="recentCount"
                    name="Recent Mentions"
                    fill="var(--color-recentCount)"
                    yAxisId="right"
                    onClick={(data) => handleKeywordClick(data.keyword)}
                    cursor="pointer"
                  />
                </RechartsPrimitive.BarChart>
              </ChartContainer>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Financial Benefits */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-white flex items-center">
            <DollarSign className="h-5 w-5 mr-2 text-primary" />
            Financial Benefits
          </CardTitle>
          <CardDescription className="text-white">
            Topics with potential financial benefits
          </CardDescription>
        </CardHeader>
        <CardContent>
          {financialKeywords.length === 0 ? (
            <div className="flex justify-center items-center h-[200px]">
              <p className="text-white text-lg">No financial benefits identified</p>
            </div>
          ) : (
            <ScrollArea className="h-[200px]">
              <div className="space-y-3 pr-4">
                {financialKeywords.slice(0, 6).map((item, index) => (
                  <div
                    key={index}
                    className="p-3 border border-border rounded-md hover:bg-primary/10 cursor-pointer transition-colors"
                    onClick={() => handleKeywordClick(item.keyword)}
                  >
                    <div className="flex justify-between items-center">
                      <span className="text-white font-medium truncate mr-2">{item.keyword}</span>
                      <Badge variant="outline" className="bg-green-500/20 text-green-400 whitespace-nowrap">
                        {item.recentCount} mentions
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* Latest Topics */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-white flex items-center">
            <Clock className="h-5 w-5 mr-2 text-primary" />
            Latest Topics
          </CardTitle>
          <CardDescription className="text-white">
            Most mentioned topics in recent articles
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentKeywords.length === 0 ? (
            <div className="flex justify-center items-center h-[200px]">
              <p className="text-white text-lg">No recent topics identified</p>
            </div>
          ) : (
            <ScrollArea className="h-[200px]">
              <div className="grid grid-cols-2 gap-2 pr-4">
                {recentKeywords.slice(0, 12).map((item, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className={`
                      cursor-pointer hover:bg-primary/20 transition-colors text-sm py-2 px-3
                      truncate overflow-hidden text-ellipsis
                      ${item.sentiment === FinancialTermType.POSITIVE ? 'bg-green-500/20 text-green-400' :
                        item.sentiment === FinancialTermType.NEGATIVE ? 'bg-red-500/20 text-red-400' :
                        'bg-primary/20'}
                    `}
                    onClick={() => handleKeywordClick(item.keyword)}
                  >
                    {item.keyword} ({item.recentCount})
                  </Badge>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* Keyword Detail Dialog */}
      {selectedKeyword && (
        <KeywordDetailDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          keyword={selectedKeyword}
          feedItems={feedItems}
          type="word"
          onOpenArticle={onOpenArticle}
          onSaveArticle={onSaveArticle}
          onOpenInBrowser={onOpenInBrowser}
        />
      )}
    </div>
  );
}
