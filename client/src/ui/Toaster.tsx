import React from 'react';
import { useRef, useState, useCallback } from 'react';
import { Toast, ToastProps, ToastDescription, ToastTitle } from './toast';
import './toast.css';

export interface ToasterAPI {
  toast: (props: Omit<ToastProps, 'id' | 'onDismiss'>) => string;
  dismiss: (id: string) => void;
  dismissAll: () => void;
}

export const ToasterContext = React.createContext<ToasterAPI | null>(null);

declare global {
  interface Window {
    __toasterRef: React.RefObject<ToasterAPI>;
  }
}

export function ToasterProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<ToastProps[]>([]);
  const apiRef = useRef<ToasterAPI>({
    toast: (props) => {
      const id = Math.random().toString(36).substring(2, 9);
      setToasts((prev) => [...prev, { ...props, id }]);
      return id;
    },
    dismiss: (id) => setToasts((prev) => prev.filter((t) => t.id !== id)),
    dismissAll: () => setToasts([]),
  });

  if (!window.__toasterRef) {
    window.__toasterRef = { current: apiRef.current };
  }

  return (
    <ToasterContext.Provider value={apiRef.current}>
      {children}
      <div className="toaster">
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            {...toast}
            onDismiss={(id) => apiRef.current.dismiss(id)}
          />
        ))}
      </div>
    </ToasterContext.Provider>
  );
}

