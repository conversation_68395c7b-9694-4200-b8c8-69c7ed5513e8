import { useState, useEffect, useRef } from 'react';
import { logger } from '@/lib/logger';

interface YoutubeThumbnailProps {
  videoId: string;
  title: string;
  className?: string;
  width?: number;
  height?: number;
  quality?: 'default' | 'hqdefault' | 'mqdefault' | 'sddefault' | 'maxresdefault';
  thumbnail?: string; // Optional direct thumbnail URL
}

/**
 * A component that handles YouTube thumbnails with fallbacks
 * to prevent 404 errors when thumbnails are not available
 */
export function YoutubeThumbnail({
  videoId,
  title,
  className = "w-full h-full object-cover",
  width,
  height,
  quality = 'hqdefault',
  thumbnail
}: YoutubeThumbnailProps) {
  // Use the provided thumbnail URL if available, otherwise generate from videoId
  const initialThumbnail = thumbnail || `https://i.ytimg.com/vi/${videoId}/${quality}.jpg`;

  const [currentThumbnail, setCurrentThumbnail] = useState<string>(initialThumbnail);
  const [fallbackIndex, setFallbackIndex] = useState<number>(0);
  const [loaded, setLoaded] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);
  const retryCount = useRef<number>(0);

  // List of fallback thumbnail options in order of preference
  const thumbnailOptions = [
    thumbnail, // First try the provided thumbnail if available
    `https://i.ytimg.com/vi/${videoId}/maxresdefault.jpg`,
    `https://i.ytimg.com/vi/${videoId}/sddefault.jpg`,
    `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`,
    `https://i.ytimg.com/vi/${videoId}/mqdefault.jpg`,
    `https://i.ytimg.com/vi/${videoId}/default.jpg`,
    `https://i.ytimg.com/vi/${videoId}/0.jpg`,
    `https://i.ytimg.com/vi/${videoId}/1.jpg`,
    `https://i.ytimg.com/vi/${videoId}/2.jpg`,
    `https://i.ytimg.com/vi/${videoId}/3.jpg`,
    // Final fallback - a generic placeholder
    `https://placehold.co/480x360/222222/DDDDDD?text=No+Thumbnail`
  ].filter(Boolean); // Remove any null/undefined entries

  // Handle image load error by trying the next fallback
  const handleError = () => {
    // Add a small delay before trying the next fallback to prevent rapid retries
    setTimeout(() => {
      if (fallbackIndex < thumbnailOptions.length - 1) {
        const nextIndex = fallbackIndex + 1;
        const nextThumbnail = thumbnailOptions[nextIndex];

        logger.debug(`Thumbnail for ${videoId} failed to load, trying fallback ${nextIndex + 1}/${thumbnailOptions.length}`);

        setFallbackIndex(nextIndex);
        setCurrentThumbnail(nextThumbnail);

        // Increment retry count
        retryCount.current += 1;
      } else {
        // All fallbacks failed
        setError(true);
        logger.debug(`All thumbnail fallbacks failed for video ${videoId} after ${retryCount.current} retries`);
      }
    }, 100);
  };

  // Update the thumbnail when videoId or thumbnail changes
  useEffect(() => {
    // Reset states
    setFallbackIndex(0);
    setLoaded(false);
    setError(false);
    retryCount.current = 0;

    // Start with the provided thumbnail or specified quality
    const initialThumbnail = thumbnail || (quality ?
      `https://i.ytimg.com/vi/${videoId}/${quality}.jpg` :
      thumbnailOptions[0]);

    setCurrentThumbnail(initialThumbnail);
  }, [videoId, quality, thumbnail]);

  // If all fallbacks have failed and we're showing the placeholder, use a div instead
  if (error && fallbackIndex >= thumbnailOptions.length - 1) {
    return (
      <div
        className={`bg-gray-200 dark:bg-gray-800 flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <span className="text-xs text-gray-500 dark:text-gray-400 text-center p-2">
          Thumbnail unavailable
        </span>
      </div>
    );
  }

  return (
    <div className="relative">
      <img
        src={currentThumbnail}
        alt={title || 'YouTube video thumbnail'}
        className={className}
        width={width}
        height={height}
        loading="lazy"
        onError={handleError}
        onLoad={() => setLoaded(true)}
        style={{
          opacity: loaded ? 1 : 0.5,
          transition: 'opacity 0.3s ease'
        }}
      />
      {!loaded && (
        <div
          className={`absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse ${className}`}
          style={{ width, height }}
        />
      )}
    </div>
  );
}
