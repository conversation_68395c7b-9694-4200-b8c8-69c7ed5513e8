import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { Mic, Plus, Save, Edit, Trash2, Copy, TrendingUp, Clock, Volume2, Target } from 'lucide-react';

interface SuccessLandmark {
  id: string;
  title: string;
  category: 'narration-style' | 'timing' | 'delivery' | 'engagement' | 'structure' | 'custom';
  description: string;
  technique: string;
  example: string;
  metrics: {
    avgViewDuration?: string;
    engagementRate?: string;
    clickThroughRate?: string;
    retentionRate?: string;
  };
  tags: string[];
  dateCreated: Date;
  lastUsed?: Date;
}

const SuccessLandmarksModule: React.FC = () => {
  const { toast } = useToast();
  const [landmarks, setLandmarks] = useState<SuccessLandmark[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [editingLandmark, setEditingLandmark] = useState<SuccessLandmark | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [newLandmark, setNewLandmark] = useState({
    title: '',
    category: 'custom' as const,
    description: '',
    technique: '',
    example: '',
    metrics: {
      avgViewDuration: '',
      engagementRate: '',
      clickThroughRate: '',
      retentionRate: ''
    },
    tags: [] as string[],
    newTag: ''
  });

  // Load landmarks from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('txtpro_success_landmarks');
    if (saved) {
      setLandmarks(JSON.parse(saved));
    } else {
      // Initialize with default landmarks
      const defaultLandmarks = getDefaultLandmarks();
      setLandmarks(defaultLandmarks);
      localStorage.setItem('txtpro_success_landmarks', JSON.stringify(defaultLandmarks));
    }
  }, []);

  // Save landmarks to localStorage
  useEffect(() => {
    localStorage.setItem('txtpro_success_landmarks', JSON.stringify(landmarks));
  }, [landmarks]);

  const getDefaultLandmarks = (): SuccessLandmark[] => [
    {
      id: 'urgent-opener',
      title: 'Urgent News Opener',
      category: 'narration-style',
      description: 'High-energy opening for breaking news about benefits',
      technique: 'Start with "BREAKING:" or "URGENT:" followed by immediate benefit impact',
      example: '"BREAKING: Social Security recipients - this changes everything for your December payment!"',
      metrics: {
        avgViewDuration: '4:32',
        engagementRate: '8.5%',
        retentionRate: '78%'
      },
      tags: ['urgent', 'breaking', 'opener'],
      dateCreated: new Date()
    },
    {
      id: 'empathetic-tone',
      title: 'Empathetic Senior Voice',
      category: 'delivery',
      description: 'Warm, understanding tone that resonates with senior audience',
      technique: 'Use phrases like "I understand your concerns" and speak slower with clear enunciation',
      example: '"I know many of you are worried about your benefits, and I want to walk you through exactly what this means for you."',
      metrics: {
        avgViewDuration: '5:15',
        engagementRate: '12.3%',
        retentionRate: '85%'
      },
      tags: ['empathetic', 'senior', 'warm'],
      dateCreated: new Date()
    },
    {
      id: 'timing-hook',
      title: '15-Second Hook Rule',
      category: 'timing',
      description: 'Capture attention within first 15 seconds with specific benefit amount or date',
      technique: 'Lead with exact dollar amount or specific date within first 15 seconds',
      example: '"Your $1,907 Social Security payment is changing on January 3rd - here\'s what you need to know."',
      metrics: {
        avgViewDuration: '3:45',
        retentionRate: '82%'
      },
      tags: ['timing', 'hook', '15-second'],
      dateCreated: new Date()
    },
    {
      id: 'call-to-action',
      title: 'Subscribe Reminder Pattern',
      category: 'engagement',
      description: 'Natural way to remind viewers to subscribe without being pushy',
      technique: 'Tie subscription to staying informed about their benefits',
      example: '"Make sure you\'re subscribed so you never miss important updates about your Social Security benefits."',
      metrics: {
        clickThroughRate: '3.2%',
        engagementRate: '9.1%'
      },
      tags: ['subscribe', 'CTA', 'natural'],
      dateCreated: new Date()
    }
  ];

  const categoryOptions = [
    { value: 'narration-style', label: 'Narration Style', icon: Volume2 },
    { value: 'timing', label: 'Timing Techniques', icon: Clock },
    { value: 'delivery', label: 'Delivery Methods', icon: Mic },
    { value: 'engagement', label: 'Engagement Tactics', icon: TrendingUp },
    { value: 'structure', label: 'Script Structure', icon: Target },
    { value: 'custom', label: 'Custom Techniques', icon: Mic }
  ];

  const handleCreateLandmark = () => {
    if (!newLandmark.title.trim() || !newLandmark.technique.trim()) {
      toast({
        title: "Validation Error",
        description: "Title and technique are required",
        variant: "destructive"
      });
      return;
    }

    const landmark: SuccessLandmark = {
      id: `landmark_${Date.now()}`,
      title: newLandmark.title.trim(),
      category: newLandmark.category,
      description: newLandmark.description.trim(),
      technique: newLandmark.technique.trim(),
      example: newLandmark.example.trim(),
      metrics: newLandmark.metrics,
      tags: newLandmark.tags,
      dateCreated: new Date()
    };

    setLandmarks(prev => [...prev, landmark]);
    setNewLandmark({
      title: '',
      category: 'custom',
      description: '',
      technique: '',
      example: '',
      metrics: { avgViewDuration: '', engagementRate: '', clickThroughRate: '', retentionRate: '' },
      tags: [],
      newTag: ''
    });
    setIsCreating(false);

    toast({
      title: "Success Landmark Created",
      description: `"${landmark.title}" has been added to your techniques`
    });
  };

  const handleDeleteLandmark = (landmarkId: string) => {
    setLandmarks(prev => prev.filter(landmark => landmark.id !== landmarkId));
    toast({
      title: "Landmark Deleted",
      description: "Success landmark has been removed"
    });
  };

  const handleCopyLandmark = (landmark: SuccessLandmark) => {
    const landmarkText = `${landmark.title}\n\nTechnique: ${landmark.technique}\n\nExample: ${landmark.example}`;
    navigator.clipboard.writeText(landmarkText);
    toast({
      title: "Landmark Copied",
      description: "Success technique has been copied to clipboard"
    });
  };

  const addTag = () => {
    if (newLandmark.newTag.trim() && !newLandmark.tags.includes(newLandmark.newTag.trim())) {
      setNewLandmark(prev => ({
        ...prev,
        tags: [...prev.tags, prev.newTag.trim()],
        newTag: ''
      }));
    }
  };

  const removeTag = (tagToRemove: string) => {
    setNewLandmark(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const filteredLandmarks = landmarks.filter(landmark => {
    const matchesSearch = landmark.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         landmark.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         landmark.technique.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         landmark.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = filterCategory === 'all' || landmark.category === filterCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Mic className="h-5 w-5" />
                Script Success Landmarks
              </CardTitle>
              <CardDescription>
                Store proven narration styles, timing techniques, and delivery methods
              </CardDescription>
            </div>
            <Button onClick={() => setIsCreating(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Technique
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Filter */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search success techniques..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categoryOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {isCreating && (
            <Card className="mb-6 border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-lg">Add New Success Technique</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="landmarkTitle">Technique Title *</Label>
                    <Input
                      id="landmarkTitle"
                      placeholder="e.g., Urgent News Opener"
                      value={newLandmark.title}
                      onChange={(e) => setNewLandmark(prev => ({ ...prev, title: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="landmarkCategory">Category</Label>
                    <Select 
                      value={newLandmark.category} 
                      onValueChange={(value: any) => setNewLandmark(prev => ({ ...prev, category: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categoryOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="landmarkDescription">Description</Label>
                  <Input
                    id="landmarkDescription"
                    placeholder="Brief description of when to use this technique..."
                    value={newLandmark.description}
                    onChange={(e) => setNewLandmark(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="landmarkTechnique">Technique Details *</Label>
                  <Textarea
                    id="landmarkTechnique"
                    placeholder="Describe the specific technique or method..."
                    value={newLandmark.technique}
                    onChange={(e) => setNewLandmark(prev => ({ ...prev, technique: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="landmarkExample">Example</Label>
                  <Textarea
                    id="landmarkExample"
                    placeholder="Provide a specific example of this technique in action..."
                    value={newLandmark.example}
                    onChange={(e) => setNewLandmark(prev => ({ ...prev, example: e.target.value }))}
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Performance Metrics (Optional)</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <Input
                      placeholder="Avg View Duration"
                      value={newLandmark.metrics.avgViewDuration}
                      onChange={(e) => setNewLandmark(prev => ({
                        ...prev,
                        metrics: { ...prev.metrics, avgViewDuration: e.target.value }
                      }))}
                    />
                    <Input
                      placeholder="Engagement Rate"
                      value={newLandmark.metrics.engagementRate}
                      onChange={(e) => setNewLandmark(prev => ({
                        ...prev,
                        metrics: { ...prev.metrics, engagementRate: e.target.value }
                      }))}
                    />
                    <Input
                      placeholder="Click Through Rate"
                      value={newLandmark.metrics.clickThroughRate}
                      onChange={(e) => setNewLandmark(prev => ({
                        ...prev,
                        metrics: { ...prev.metrics, clickThroughRate: e.target.value }
                      }))}
                    />
                    <Input
                      placeholder="Retention Rate"
                      value={newLandmark.metrics.retentionRate}
                      onChange={(e) => setNewLandmark(prev => ({
                        ...prev,
                        metrics: { ...prev.metrics, retentionRate: e.target.value }
                      }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add tag..."
                      value={newLandmark.newTag}
                      onChange={(e) => setNewLandmark(prev => ({ ...prev, newTag: e.target.value }))}
                      onKeyPress={(e) => e.key === 'Enter' && addTag()}
                    />
                    <Button onClick={addTag} variant="outline" size="sm">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {newLandmark.tags.map(tag => (
                      <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                        {tag} ×
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button onClick={handleCreateLandmark}>
                    <Save className="h-4 w-4 mr-2" />
                    Save Technique
                  </Button>
                  <Button onClick={() => setIsCreating(false)} variant="outline">
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Landmarks Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {filteredLandmarks.map(landmark => {
              const categoryOption = categoryOptions.find(opt => opt.value === landmark.category);
              const IconComponent = categoryOption?.icon || Mic;

              return (
                <Card key={landmark.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4 text-muted-foreground" />
                        <CardTitle className="text-base">{landmark.title}</CardTitle>
                      </div>
                      <div className="flex gap-1">
                        <Button onClick={() => handleCopyLandmark(landmark)} variant="ghost" size="sm">
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button onClick={() => handleDeleteLandmark(landmark.id)} variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {categoryOption?.label}
                      </Badge>
                      {Object.values(landmark.metrics).some(metric => metric) && (
                        <Badge variant="secondary" className="text-xs">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          Metrics
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    {landmark.description && (
                      <p className="text-sm text-muted-foreground mb-3">{landmark.description}</p>
                    )}
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-xs font-medium text-muted-foreground">TECHNIQUE</Label>
                        <p className="text-sm mt-1">{landmark.technique}</p>
                      </div>
                      
                      {landmark.example && (
                        <div>
                          <Label className="text-xs font-medium text-muted-foreground">EXAMPLE</Label>
                          <p className="text-sm mt-1 italic text-blue-700 bg-blue-50 p-2 rounded">
                            "{landmark.example}"
                          </p>
                        </div>
                      )}

                      {Object.values(landmark.metrics).some(metric => metric) && (
                        <div>
                          <Label className="text-xs font-medium text-muted-foreground">METRICS</Label>
                          <div className="grid grid-cols-2 gap-2 mt-1">
                            {landmark.metrics.avgViewDuration && (
                              <div className="text-xs">
                                <span className="text-muted-foreground">Avg Duration:</span> {landmark.metrics.avgViewDuration}
                              </div>
                            )}
                            {landmark.metrics.engagementRate && (
                              <div className="text-xs">
                                <span className="text-muted-foreground">Engagement:</span> {landmark.metrics.engagementRate}
                              </div>
                            )}
                            {landmark.metrics.retentionRate && (
                              <div className="text-xs">
                                <span className="text-muted-foreground">Retention:</span> {landmark.metrics.retentionRate}
                              </div>
                            )}
                            {landmark.metrics.clickThroughRate && (
                              <div className="text-xs">
                                <span className="text-muted-foreground">CTR:</span> {landmark.metrics.clickThroughRate}
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-wrap gap-1 mt-3">
                      {landmark.tags.map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {filteredLandmarks.length === 0 && !isCreating && (
            <div className="text-center py-12">
              <Mic className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Success Techniques Found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || filterCategory !== 'all' 
                  ? 'No techniques match your search criteria'
                  : 'Add your first success technique to get started'
                }
              </p>
              {!searchTerm && filterCategory === 'all' && (
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Technique
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SuccessLandmarksModule;
