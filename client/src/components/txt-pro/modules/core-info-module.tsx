import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { Info, Plus, Save, Edit, Trash2, Copy, Calendar, DollarSign, FileText, Users } from 'lucide-react';

interface CoreInfoItem {
  id: string;
  title: string;
  category: 'date' | 'amount' | 'eligibility' | 'bill-name' | 'deadline' | 'contact' | 'custom';
  content: string;
  description: string;
  tags: string[];
  dateCreated: Date;
  lastUpdated: Date;
}

const CoreInfoModule: React.FC = () => {
  const { toast } = useToast();
  const [infoItems, setInfoItems] = useState<CoreInfoItem[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [editingItem, setEditingItem] = useState<CoreInfoItem | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [newItem, setNewItem] = useState({
    title: '',
    category: 'custom' as const,
    content: '',
    description: '',
    tags: [] as string[],
    newTag: ''
  });

  // Load info items from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('txtpro_core_info');
    if (saved) {
      setInfoItems(JSON.parse(saved));
    } else {
      // Initialize with default info items
      const defaultItems = getDefaultInfoItems();
      setInfoItems(defaultItems);
      localStorage.setItem('txtpro_core_info', JSON.stringify(defaultItems));
    }
  }, []);

  // Save info items to localStorage
  useEffect(() => {
    localStorage.setItem('txtpro_core_info', JSON.stringify(infoItems));
  }, [infoItems]);

  const getDefaultInfoItems = (): CoreInfoItem[] => [
    {
      id: 'cola-2025',
      title: '2025 COLA Increase',
      category: 'amount',
      content: '2.5% Cost of Living Adjustment for 2025',
      description: 'Annual COLA increase for Social Security benefits',
      tags: ['COLA', 'increase', '2025'],
      dateCreated: new Date(),
      lastUpdated: new Date()
    },
    {
      id: 'max-benefit-2025',
      title: 'Maximum SS Benefit 2025',
      category: 'amount',
      content: '$4,873 per month at full retirement age',
      description: 'Maximum Social Security benefit for 2025',
      tags: ['maximum', 'benefit', '2025'],
      dateCreated: new Date(),
      lastUpdated: new Date()
    },
    {
      id: 'full-retirement-age',
      title: 'Full Retirement Age',
      category: 'eligibility',
      content: 'Age 67 for those born in 1960 or later',
      description: 'Full retirement age for Social Security benefits',
      tags: ['retirement', 'age', 'eligibility'],
      dateCreated: new Date(),
      lastUpdated: new Date()
    },
    {
      id: 'ssa-contact',
      title: 'SSA Customer Service',
      category: 'contact',
      content: '************** (TTY: **************)',
      description: 'Main Social Security Administration phone number',
      tags: ['contact', 'phone', 'SSA'],
      dateCreated: new Date(),
      lastUpdated: new Date()
    }
  ];

  const categoryOptions = [
    { value: 'date', label: 'Important Dates', icon: Calendar },
    { value: 'amount', label: 'Dollar Amounts', icon: DollarSign },
    { value: 'eligibility', label: 'Eligibility Criteria', icon: Users },
    { value: 'bill-name', label: 'Bill Names', icon: FileText },
    { value: 'deadline', label: 'Deadlines', icon: Calendar },
    { value: 'contact', label: 'Contact Info', icon: Info },
    { value: 'custom', label: 'Custom Facts', icon: Info }
  ];

  const handleCreateItem = () => {
    if (!newItem.title.trim() || !newItem.content.trim()) {
      toast({
        title: "Validation Error",
        description: "Title and content are required",
        variant: "destructive"
      });
      return;
    }

    const item: CoreInfoItem = {
      id: `info_${Date.now()}`,
      title: newItem.title.trim(),
      category: newItem.category,
      content: newItem.content.trim(),
      description: newItem.description.trim(),
      tags: newItem.tags,
      dateCreated: new Date(),
      lastUpdated: new Date()
    };

    setInfoItems(prev => [...prev, item]);
    setNewItem({ title: '', category: 'custom', content: '', description: '', tags: [], newTag: '' });
    setIsCreating(false);

    toast({
      title: "Info Item Created",
      description: `"${item.title}" has been added to your core info`
    });
  };

  const handleDeleteItem = (itemId: string) => {
    setInfoItems(prev => prev.filter(item => item.id !== itemId));
    toast({
      title: "Info Item Deleted",
      description: "Core info item has been removed"
    });
  };

  const handleCopyItem = (item: CoreInfoItem) => {
    const itemText = `${item.title}: ${item.content}`;
    navigator.clipboard.writeText(itemText);
    toast({
      title: "Info Copied",
      description: "Core info has been copied to clipboard"
    });
  };

  const addTag = () => {
    if (newItem.newTag.trim() && !newItem.tags.includes(newItem.newTag.trim())) {
      setNewItem(prev => ({
        ...prev,
        tags: [...prev.tags, prev.newTag.trim()],
        newTag: ''
      }));
    }
  };

  const removeTag = (tagToRemove: string) => {
    setNewItem(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const filteredItems = infoItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = filterCategory === 'all' || item.category === filterCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                Core Information Panel
              </CardTitle>
              <CardDescription>
                Store key factual information for quick reference in script generation
              </CardDescription>
            </div>
            <Button onClick={() => setIsCreating(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Info
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Filter */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <Input
                placeholder="Search core info..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categoryOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {isCreating && (
            <Card className="mb-6 border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-lg">Add New Core Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="infoTitle">Title *</Label>
                    <Input
                      id="infoTitle"
                      placeholder="e.g., 2025 COLA Increase"
                      value={newItem.title}
                      onChange={(e) => setNewItem(prev => ({ ...prev, title: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="infoCategory">Category</Label>
                    <Select 
                      value={newItem.category} 
                      onValueChange={(value: any) => setNewItem(prev => ({ ...prev, category: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categoryOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="infoContent">Content *</Label>
                  <Textarea
                    id="infoContent"
                    placeholder="The actual factual information..."
                    value={newItem.content}
                    onChange={(e) => setNewItem(prev => ({ ...prev, content: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="infoDescription">Description</Label>
                  <Input
                    id="infoDescription"
                    placeholder="Brief description of when to use this info..."
                    value={newItem.description}
                    onChange={(e) => setNewItem(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Add tag..."
                      value={newItem.newTag}
                      onChange={(e) => setNewItem(prev => ({ ...prev, newTag: e.target.value }))}
                      onKeyPress={(e) => e.key === 'Enter' && addTag()}
                    />
                    <Button onClick={addTag} variant="outline" size="sm">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {newItem.tags.map(tag => (
                      <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                        {tag} ×
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button onClick={handleCreateItem}>
                    <Save className="h-4 w-4 mr-2" />
                    Save Info
                  </Button>
                  <Button onClick={() => setIsCreating(false)} variant="outline">
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Info Items Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredItems.map(item => {
              const categoryOption = categoryOptions.find(opt => opt.value === item.category);
              const IconComponent = categoryOption?.icon || Info;

              return (
                <Card key={item.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4 text-muted-foreground" />
                        <CardTitle className="text-base">{item.title}</CardTitle>
                      </div>
                      <div className="flex gap-1">
                        <Button onClick={() => handleCopyItem(item)} variant="ghost" size="sm">
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button onClick={() => handleDeleteItem(item.id)} variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <Badge variant="outline" className="w-fit">
                      {categoryOption?.label}
                    </Badge>
                  </CardHeader>
                  <CardContent>
                    <p className="font-medium text-sm mb-2">{item.content}</p>
                    {item.description && (
                      <p className="text-xs text-muted-foreground mb-3">{item.description}</p>
                    )}
                    <div className="flex flex-wrap gap-1">
                      {item.tags.map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {filteredItems.length === 0 && !isCreating && (
            <div className="text-center py-12">
              <Info className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Core Info Found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || filterCategory !== 'all' 
                  ? 'No items match your search criteria'
                  : 'Add your first core information item to get started'
                }
              </p>
              {!searchTerm && filterCategory === 'all' && (
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Info Item
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CoreInfoModule;
