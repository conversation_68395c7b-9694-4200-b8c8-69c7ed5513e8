/**
 * ChatGPT API Client Service
 * Handles communication with the external ChatGPT server
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

export interface ChatGPTResponse {
  success: boolean;
  response?: string;
  error?: string;
  message?: string;
}

export interface ChatGPTLoginResponse {
  success: boolean;
  message?: string;
  error?: string;
  logged_in?: boolean;
}

export interface ChatGPTVerifyResponse {
  success: boolean;
  logged_in: boolean;
  message?: string;
  error?: string;
}

export interface ChatGPTNewChatResponse {
  success: boolean;
  message?: string;
  error?: string;
}

export class ChatGPTClient {
  private client: AxiosInstance;
  private baseURL: string;
  private isConnected: boolean = false;
  private lastConnectionCheck: number = 0;
  private connectionCheckInterval: number = 30000; // 30 seconds

  constructor(baseURL: string = 'http://localhost:7777') {
    this.baseURL = baseURL;
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000, // 30 second timeout
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('ChatGPT API Error:', error.message);
        if (error.code === 'ECONNREFUSED') {
          this.isConnected = false;
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Check if the ChatGPT server is running and accessible
   */
  async checkConnection(): Promise<boolean> {
    const now = Date.now();

    // Use cached result if recent
    if (now - this.lastConnectionCheck < this.connectionCheckInterval && this.isConnected) {
      return this.isConnected;
    }

    try {
      // Try the status endpoint first, fallback to root if not available
      let response;
      try {
        response = await this.client.get('/api/status', { timeout: 5000 });
      } catch (statusError) {
        // If status endpoint fails, try the root endpoint
        response = await this.client.get('/', { timeout: 5000 });
      }

      this.isConnected = response.status === 200;
      this.lastConnectionCheck = now;
      return this.isConnected;
    } catch (error) {
      this.isConnected = false;
      this.lastConnectionCheck = now;
      return false;
    }
  }

  /**
   * Initialize login process (opens browser for manual login)
   */
  async initializeLogin(): Promise<ChatGPTLoginResponse> {
    try {
      const response: AxiosResponse<ChatGPTLoginResponse> = await this.client.post('/api/login');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to initialize login'
      };
    }
  }

  /**
   * Verify if user is logged in to ChatGPT
   */
  async verifyLogin(): Promise<ChatGPTVerifyResponse> {
    try {
      const response: AxiosResponse<ChatGPTVerifyResponse> = await this.client.post('/api/verify_login');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        logged_in: false,
        error: error.response?.data?.error || error.message || 'Failed to verify login'
      };
    }
  }

  /**
   * Send a message to ChatGPT
   */
  async sendMessage(message: string): Promise<ChatGPTResponse> {
    try {
      if (!message.trim()) {
        return {
          success: false,
          error: 'Message cannot be empty'
        };
      }

      const response: AxiosResponse<ChatGPTResponse> = await this.client.post('/api/chat', {
        message: message.trim()
      });

      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to send message'
      };
    }
  }

  /**
   * Start a new chat session
   */
  async startNewChat(): Promise<ChatGPTNewChatResponse> {
    try {
      const response: AxiosResponse<ChatGPTNewChatResponse> = await this.client.post('/api/new_chat');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to start new chat'
      };
    }
  }

  /**
   * Process a transcript with a prompt using ChatGPT
   */
  async processTranscript(prompt: string, transcript?: string): Promise<ChatGPTResponse> {
    try {
      let fullMessage = prompt;
      
      if (transcript && transcript.trim()) {
        fullMessage = `${prompt}\n\nTranscript to analyze:\n${transcript}`;
      }

      return await this.sendMessage(fullMessage);
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to process transcript'
      };
    }
  }

  /**
   * Get connection status and server info
   */
  async getStatus(): Promise<{
    connected: boolean;
    serverUrl: string;
    lastCheck: Date | null;
  }> {
    const connected = await this.checkConnection();
    
    return {
      connected,
      serverUrl: this.baseURL,
      lastCheck: this.lastConnectionCheck > 0 ? new Date(this.lastConnectionCheck) : null
    };
  }

  /**
   * Update the base URL for the ChatGPT server
   */
  updateBaseURL(newBaseURL: string): void {
    this.baseURL = newBaseURL;
    this.client.defaults.baseURL = newBaseURL;
    this.isConnected = false;
    this.lastConnectionCheck = 0;
  }
}

// Create a singleton instance
export const chatGPTClient = new ChatGPTClient();

// Export default instance
export default chatGPTClient;
