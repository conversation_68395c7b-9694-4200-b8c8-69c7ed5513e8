import { useState, useRef } from 'react';

interface YoutubeThumbnailProps {
  videoId: string;
  thumbnail?: string;
  title: string;
  className?: string;
  width?: number;
  height?: number;
}

/**
 * YoutubeThumbnail component with fallback mechanism
 *
 * This component handles YouTube thumbnails with a fallback system:
 * 1. First tries to use the provided thumbnail URL
 * 2. If that fails, tries the high quality default YouTube thumbnail
 * 3. If that fails, tries the medium quality default YouTube thumbnail
 * 4. If all fail, shows a placeholder
 */
export function YoutubeThumbnail({
  videoId,
  thumbnail,
  title,
  className = "w-16 h-9 rounded-md object-cover",
  width = 120,
  height = 68
}: YoutubeThumbnailProps) {
  // Use the provided thumbnail URL if available, otherwise generate from videoId
  const initialThumbnail = thumbnail || `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`;

  const [currentThumbnail, setCurrentThumbnail] = useState<string>(initialThumbnail);
  const [fallbackIndex, setFallbackIndex] = useState<number>(0);
  const [loaded, setLoaded] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);
  const retryCount = useRef<number>(0);

  // List of fallback thumbnail options in order of preference
  const thumbnailOptions = [
    thumbnail, // First try the provided thumbnail if available
    `https://i.ytimg.com/vi/${videoId}/maxresdefault.jpg`,
    `https://i.ytimg.com/vi/${videoId}/sddefault.jpg`,
    `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`,
    `https://i.ytimg.com/vi/${videoId}/mqdefault.jpg`,
    `https://i.ytimg.com/vi/${videoId}/default.jpg`,
    `https://i.ytimg.com/vi/${videoId}/0.jpg`,
    `https://i.ytimg.com/vi/${videoId}/1.jpg`,
    // Final fallback - a generic placeholder
    `https://placehold.co/480x360/222222/DDDDDD?text=No+Thumbnail`
  ].filter(Boolean); // Remove any null/undefined entries

  // Handle image load error by trying the next fallback
  const handleError = () => {
    // Add a small delay before trying the next fallback to prevent rapid retries
    setTimeout(() => {
      if (fallbackIndex < thumbnailOptions.length - 1) {
        const nextIndex = fallbackIndex + 1;
        const nextThumbnail = thumbnailOptions[nextIndex];

        setFallbackIndex(nextIndex);
        setCurrentThumbnail(nextThumbnail);

        // Increment retry count
        retryCount.current += 1;
      } else {
        // All fallbacks failed
        setError(true);
      }
    }, 100);
  };

  // If all fallbacks have failed and we're showing the placeholder, use a div instead
  if (error && fallbackIndex >= thumbnailOptions.length - 1) {
    return (
      <div
        className={`bg-gray-200 dark:bg-gray-800 flex items-center justify-center ${className}`}
        style={{ width, height }}
      >
        <span className="text-xs text-gray-500 dark:text-gray-400 text-center p-2">
          No Image
        </span>
      </div>
    );
  }

  return (
    <div className="relative">
      <img
        src={currentThumbnail}
        alt={title || 'YouTube video thumbnail'}
        className={className}
        width={width}
        height={height}
        loading="lazy"
        onError={handleError}
        onLoad={() => setLoaded(true)}
        style={{
          opacity: loaded ? 1 : 0.5,
          transition: 'opacity 0.3s ease'
        }}
      />
      {!loaded && (
        <div
          className={`absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse ${className}`}
          style={{ width, height }}
        />
      )}
    </div>
  );
}
