import Database from 'better-sqlite3';

/**
 * Migration script to add financial analysis columns to the youtube_videos table
 */
function runMigration() {
  console.log('Running migration: Adding financial analysis columns to youtube_videos table');
  
  try {
    // Connect to the database
    const db = new Database('data.db');
    
    // Check if the columns already exist
    const tableInfo = db.prepare('PRAGMA table_info(youtube_videos)').all();
    const hasFinancialScore = tableInfo.some(column => column.name === 'financial_score');
    
    if (hasFinancialScore) {
      console.log('Financial analysis columns already exist in youtube_videos table');
      return;
    }
    
    // Add the financial analysis columns
    db.exec(`
      ALTER TABLE youtube_videos ADD COLUMN financial_score REAL DEFAULT 0;
      ALTER TABLE youtube_videos ADD COLUMN financial_category TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN financial_amount TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN financial_timeline TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN financial_recipients TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN financial_steps TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN financial_viral_potential TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN financial_skepticism TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN financial_analysis TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN financial_timestamps TEXT DEFAULT NULL;
      ALTER TABLE youtube_videos ADD COLUMN has_financial_analysis BOOLEAN DEFAULT 0;
    `);
    
    console.log('Successfully added financial analysis columns to youtube_videos table');
    
    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
}

// Run the migration
runMigration();
