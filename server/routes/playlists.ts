import { Router } from 'express';
import { IStorage, Playlist, PlaylistVideo } from '../storage';

export function setupPlaylistRoutes(app: Router, storage: IStorage) {
    // Get all playlists for the current user
    app.get('/api/playlists', async (req, res) => {
        if (!req.isAuthenticated()) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        try {
            const userId = req.user?.id;
            
            if (!userId) {
                console.error('User ID is undefined');
                return res.status(401).json({ message: 'User not authenticated properly' });
            }
            
            const playlists = await storage.getPlaylists(userId);
            res.json(playlists);
        } catch (error) {
            console.error('Error getting playlists:', error);
            res.status(500).json({ message: 'Failed to get playlists' });
        }
    });

    // Get a specific playlist
    app.get('/api/playlists/:id', async (req, res) => {
        if (!req.isAuthenticated()) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        try {
            const playlistId = Number(req.params.id);
            const playlist = await storage.getPlaylist(playlistId);
            
            if (!playlist) {
                return res.status(404).json({ message: 'Playlist not found' });
            }
            
            const userId = req.user?.id;
            
            if (!userId) {
                console.error('User ID is undefined');
                return res.status(401).json({ message: 'User not authenticated properly' });
            }
            
            if (playlist.userId !== userId) {
                return res.status(403).json({ message: 'You do not have permission to access this playlist' });
            }
            
            res.json(playlist);
        } catch (error) {
            console.error(`Error getting playlist ${req.params.id}:`, error);
            res.status(500).json({ message: 'Failed to get playlist' });
        }
    });

    // Create a new playlist
    app.post('/api/playlists', async (req, res) => {
        if (!req.isAuthenticated()) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        try {
            const { name, description } = req.body;
            
            if (!name) {
                return res.status(400).json({ message: 'Playlist name is required' });
            }
            
            const userId = req.user?.id;
            
            if (!userId) {
                console.error('User ID is undefined');
                console.log('Request user object:', req.user);
                return res.status(401).json({ message: 'User not authenticated properly' });
            }
            
            console.log(`Creating playlist for user ${userId} with name: ${name}`);
            
            const playlist = await storage.createPlaylist(userId, name, description);
            
            res.status(201).json(playlist);
        } catch (error) {
            console.error('Error creating playlist:', error);
            res.status(500).json({ message: 'Failed to create playlist' });
        }
    });

    // Update a playlist
    app.put('/api/playlists/:id', async (req, res) => {
        if (!req.isAuthenticated()) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        try {
            const playlistId = Number(req.params.id);
            const { name, description } = req.body;
            
            // Check if the playlist exists and belongs to the current user
            const playlist = await storage.getPlaylist(playlistId);
            
            if (!playlist) {
                return res.status(404).json({ message: 'Playlist not found' });
            }
            
            const userId = req.user?.id;
            
            if (!userId) {
                console.error('User ID is undefined');
                return res.status(401).json({ message: 'User not authenticated properly' });
            }
            
            if (playlist.userId !== userId) {
                return res.status(403).json({ message: 'You do not have permission to update this playlist' });
            }
            
            const updatedPlaylist = await storage.updatePlaylist(playlistId, { name, description });
            
            res.json(updatedPlaylist);
        } catch (error) {
            console.error(`Error updating playlist ${req.params.id}:`, error);
            res.status(500).json({ message: 'Failed to update playlist' });
        }
    });

    // Delete a playlist
    app.delete('/api/playlists/:id', async (req, res) => {
        if (!req.isAuthenticated()) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        try {
            const playlistId = Number(req.params.id);
            
            // Check if the playlist exists and belongs to the current user
            const playlist = await storage.getPlaylist(playlistId);
            
            if (!playlist) {
                return res.status(404).json({ message: 'Playlist not found' });
            }
            
            const userId = req.user?.id;
            
            if (!userId) {
                console.error('User ID is undefined');
                return res.status(401).json({ message: 'User not authenticated properly' });
            }
            
            if (playlist.userId !== userId) {
                return res.status(403).json({ message: 'You do not have permission to delete this playlist' });
            }
            
            await storage.deletePlaylist(playlistId);
            
            res.status(204).send();
        } catch (error) {
            console.error(`Error deleting playlist ${req.params.id}:`, error);
            res.status(500).json({ message: 'Failed to delete playlist' });
        }
    });

    // Get all videos in a playlist
    app.get('/api/playlists/:id/videos', async (req, res) => {
        if (!req.isAuthenticated()) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        try {
            const playlistId = Number(req.params.id);
            
            // Check if the playlist exists and belongs to the current user
            const playlist = await storage.getPlaylist(playlistId);
            
            if (!playlist) {
                return res.status(404).json({ message: 'Playlist not found' });
            }
            
            const userId = req.user?.id;
            
            if (!userId) {
                console.error('User ID is undefined');
                return res.status(401).json({ message: 'User not authenticated properly' });
            }
            
            if (playlist.userId !== userId) {
                return res.status(403).json({ message: 'You do not have permission to access this playlist' });
            }
            
            const videos = await storage.getPlaylistVideos(playlistId);
            
            res.json(videos);
        } catch (error) {
            console.error(`Error getting videos for playlist ${req.params.id}:`, error);
            res.status(500).json({ message: 'Failed to get playlist videos' });
        }
    });

    // Add a video to a playlist
    app.post('/api/playlists/:id/videos', async (req, res) => {
        if (!req.isAuthenticated()) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        try {
            const playlistId = Number(req.params.id);
            const { video } = req.body;
            
            if (!video) {
                return res.status(400).json({ message: 'Video data is required' });
            }
            
            // Check if the playlist exists and belongs to the current user
            const playlist = await storage.getPlaylist(playlistId);
            
            if (!playlist) {
                return res.status(404).json({ message: 'Playlist not found' });
            }
            
            const userId = req.user?.id;
            
            if (!userId) {
                console.error('User ID is undefined');
                return res.status(401).json({ message: 'User not authenticated properly' });
            }
            
            if (playlist.userId !== userId) {
                return res.status(403).json({ message: 'You do not have permission to add videos to this playlist' });
            }
            
            const playlistVideo = await storage.addVideoToPlaylist(playlistId, video);
            
            res.status(201).json(playlistVideo);
        } catch (error) {
            console.error(`Error adding video to playlist ${req.params.id}:`, error);
            res.status(500).json({ message: 'Failed to add video to playlist' });
        }
    });

    // Remove a video from a playlist
    app.delete('/api/playlists/:playlistId/videos/:videoId', async (req, res) => {
        if (!req.isAuthenticated()) {
            return res.status(401).json({ message: 'Unauthorized' });
        }

        try {
            const playlistId = Number(req.params.playlistId);
            const videoId = req.params.videoId;
            
            // Check if the playlist exists and belongs to the current user
            const playlist = await storage.getPlaylist(playlistId);
            
            if (!playlist) {
                return res.status(404).json({ message: 'Playlist not found' });
            }
            
            const userId = req.user?.id;
            
            if (!userId) {
                console.error('User ID is undefined');
                return res.status(401).json({ message: 'User not authenticated properly' });
            }
            
            if (playlist.userId !== userId) {
                return res.status(403).json({ message: 'You do not have permission to remove videos from this playlist' });
            }
            
            await storage.removeVideoFromPlaylist(playlistId, videoId);
            
            res.status(204).send();
        } catch (error) {
            console.error(`Error removing video ${req.params.videoId} from playlist ${req.params.playlistId}:`, error);
            res.status(500).json({ message: 'Failed to remove video from playlist' });
        }
    });
}
