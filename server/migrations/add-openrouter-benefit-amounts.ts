import Database from 'better-sqlite3';

/**
 * Migration script to add OpenRouter benefit amounts column to the youtube_videos table
 */
function runMigration() {
  console.log('Running migration: Adding OpenRouter benefit amounts column to youtube_videos table');
  
  try {
    // Connect to the database
    const db = new Database('data.db');
    
    // Check if the openrouter_benefit_amounts column already exists in the youtube_videos table
    const tableInfo = db.prepare('PRAGMA table_info(youtube_videos)').all();
    const hasOpenRouterBenefitAmounts = tableInfo.some(column => column.name === 'openrouter_benefit_amounts');
    
    if (!hasOpenRouterBenefitAmounts) {
      // Add the openrouter_benefit_amounts column to the youtube_videos table
      db.exec(`
        ALTER TABLE youtube_videos ADD COLUMN openrouter_benefit_amounts TEXT DEFAULT NULL;
      `);
      
      console.log('Successfully added openrouter_benefit_amounts column to youtube_videos table');
    } else {
      console.log('openrouter_benefit_amounts column already exists in youtube_videos table');
    }
    
    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
}

// Run the migration
runMigration();
