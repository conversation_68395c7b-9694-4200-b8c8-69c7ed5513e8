import React, { useState, useEffect, useMemo } from 'react';
import { RssFeedItem } from '@shared/schema';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, BarChart, PieChart, Cloud } from 'lucide-react';
import * as RechartsPrimitive from 'recharts';
import { ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { HighlightCounts, FinancialTermType, highlightFinancialTerms } from './financial-term-highlighter';
import { ScrollArea } from '@/components/ui/scroll-area';

// Common words to exclude from keyword analysis
const COMMON_WORDS = new Set([
  'a', 'an', 'the', 'this', 'that', 'these', 'those', 'it', 'its', 'is', 'are', 'was', 'were',
  'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'can', 'could', 'will',
  'would', 'should', 'may', 'might', 'must', 'shall', 'to', 'of', 'for', 'with', 'in', 'on',
  'at', 'by', 'from', 'about', 'as', 'into', 'like', 'through', 'after', 'over', 'between',
  'out', 'up', 'down', 'off', 'and', 'or', 'but', 'if', 'while', 'because', 'so', 'though',
  'since', 'when', 'where', 'how', 'what', 'who', 'whom', 'which', 'why', 'i', 'you', 'he',
  'she', 'we', 'they', 'them', 'their', 'his', 'her', 'our', 'my', 'your', 'not', 'no', 'yes',
  'said', 'says', 'say', 'one', 'two', 'three', 'first', 'second', 'third', 'new', 'time',
  'just', 'now', 'also', 'some', 'any', 'all', 'more', 'most', 'other', 'another', 'than',
  'then', 'there', 'here', 'only', 'very', 'even', 'back', 'well', 'still', 'too', 'own',
  'see', 'get', 'go', 'know', 'make', 'made', 'take', 'took', 'come', 'came', 'give', 'gave',
  'use', 'used', 'find', 'found', 'tell', 'told', 'ask', 'asked', 'seem', 'seemed', 'try',
  'tried', 'call', 'called', 'need', 'needed', 'feel', 'felt', 'become', 'became', 'leave',
  'left', 'put', 'keep', 'kept', 'begin', 'began', 'begun', 'show', 'showed', 'shown', 'hear',
  'heard', 'let', 'help', 'helped', 'talk', 'talked', 'turn', 'turned', 'start', 'started',
  'want', 'wanted', 'look', 'looked', 'run', 'ran', 'move', 'moved', 'live', 'lived', 'believe',
  'believed', 'hold', 'held', 'bring', 'brought', 'happen', 'happened', 'write', 'wrote',
  'written', 'sit', 'sat', 'stand', 'stood', 'lose', 'lost', 'pay', 'paid', 'meet', 'met',
  'include', 'included', 'continue', 'continued', 'set', 'learn', 'learned', 'change', 'changed',
  'lead', 'led', 'understand', 'understood', 'watch', 'watched', 'follow', 'followed'
]);

// Interface for keyword data
interface KeywordData {
  keyword: string;
  count: number;
  positiveCount: number;
  negativeCount: number;
  neutralCount: number;
  articles: number[];
}

// Interface for sentiment data
interface SentimentData {
  type: string;
  count: number;
}

interface RssDashboardProps {
  allFeedItems: RssFeedItem[];
}

export function RssDashboard({ allFeedItems }: RssDashboardProps) {
  const [isExpanded, setIsExpanded] = useState(true); // Start expanded by default
  const [activeTab, setActiveTab] = useState('keywords');

  // Debug log
  console.log('RssDashboard rendered with', allFeedItems?.length || 0, 'feed items');

  // Extract and analyze keywords from all feed items
  const keywordData = useMemo(() => {
    if (!allFeedItems || allFeedItems.length === 0) return [];

    const keywordMap = new Map<string, KeywordData>();

    // Process each feed item
    allFeedItems.forEach((item, index) => {
      // Combine title and content for analysis
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      // Get the text content without HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = title + ' ' + content + ' ' + scrapedContent;
      const textContent = tempDiv.textContent || '';

      // Analyze sentiment
      const { counts } = highlightFinancialTerms(textContent);

      // Extract words, convert to lowercase, and remove punctuation
      const words = textContent.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 2 && !COMMON_WORDS.has(word));

      // Count word frequencies
      const wordCounts = new Map<string, number>();
      words.forEach(word => {
        wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
      });

      // Add to keyword map
      wordCounts.forEach((count, word) => {
        if (!keywordMap.has(word)) {
          keywordMap.set(word, {
            keyword: word,
            count: 0,
            positiveCount: 0,
            negativeCount: 0,
            neutralCount: 0,
            articles: []
          });
        }

        const data = keywordMap.get(word)!;
        data.count += count;
        data.positiveCount += counts.positive;
        data.negativeCount += counts.negative;
        data.neutralCount += counts.neutral;

        // Add article index if not already included
        if (!data.articles.includes(item.id)) {
          data.articles.push(item.id);
        }
      });
    });

    // Convert map to array and sort by count
    return Array.from(keywordMap.values())
      .filter(data => data.count > 1) // Filter out words that appear only once
      .sort((a, b) => b.count - a.count)
      .slice(0, 50); // Take top 50 keywords
  }, [allFeedItems]);

  // Calculate overall sentiment data
  const sentimentData = useMemo(() => {
    if (!allFeedItems || allFeedItems.length === 0) return [];

    let totalPositive = 0;
    let totalNegative = 0;
    let totalNeutral = 0;

    allFeedItems.forEach(item => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = title + ' ' + content + ' ' + scrapedContent;
      const textContent = tempDiv.textContent || '';

      const { counts } = highlightFinancialTerms(textContent);

      totalPositive += counts.positive;
      totalNegative += counts.negative;
      totalNeutral += counts.neutral;
    });

    return [
      { type: 'Positive', count: totalPositive },
      { type: 'Negative', count: totalNegative },
      { type: 'Neutral', count: totalNeutral }
    ];
  }, [allFeedItems]);

  // Prepare data for keyword chart
  const keywordChartData = useMemo(() => {
    return keywordData.slice(0, 20).map(data => ({
      keyword: data.keyword,
      count: data.count,
      articles: data.articles.length,
      positiveRatio: data.positiveCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0,
      negativeRatio: data.negativeCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0,
      neutralRatio: data.neutralCount / (data.positiveCount + data.negativeCount + data.neutralCount) || 0
    }));
  }, [keywordData]);

  if (!allFeedItems || allFeedItems.length === 0) {
    return null;
  }

  return (
    <Card className="mt-6 w-full border-4 border-primary bg-primary/5">
      <CardHeader className="pb-2 cursor-pointer bg-primary/20" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex justify-between items-center">
          <CardTitle className="text-white flex items-center text-xl">
            <BarChart className="h-6 w-6 mr-2 text-primary" />
            RSS Feed Analytics Dashboard
          </CardTitle>
          <Button variant="outline" size="icon" className="h-8 w-8">
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
        <CardDescription className="text-white text-base">
          Analyze trending topics and sentiment across all your RSS feeds
        </CardDescription>
      </CardHeader>

      {isExpanded && (
        <CardContent>
          <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="keywords">Top Keywords</TabsTrigger>
              <TabsTrigger value="sentiment">Sentiment Analysis</TabsTrigger>
              <TabsTrigger value="trends">Trending Topics</TabsTrigger>
            </TabsList>

            <TabsContent value="keywords" className="space-y-4">
              <div className="h-[400px]">
                <ChartContainer
                  config={{
                    count: { color: "#3b82f6" },
                    articles: { color: "#10b981" },
                    positive: { color: "#4ade80" },
                    negative: { color: "#f87171" },
                    neutral: { color: "#94a3b8" }
                  }}
                >
                  <RechartsPrimitive.BarChart
                    data={keywordChartData}
                    layout="vertical"
                    margin={{ top: 20, right: 30, left: 100, bottom: 20 }}
                  >
                    <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                    <RechartsPrimitive.XAxis type="number" />
                    <RechartsPrimitive.YAxis
                      type="category"
                      dataKey="keyword"
                      width={100}
                      tick={{ fontSize: 12 }}
                    />
                    <RechartsPrimitive.Tooltip />
                    <RechartsPrimitive.Legend />
                    <RechartsPrimitive.Bar dataKey="count" name="Occurrences" fill="var(--color-count)" />
                    <RechartsPrimitive.Bar dataKey="articles" name="Articles" fill="var(--color-articles)" />
                  </RechartsPrimitive.BarChart>
                </ChartContainer>
              </div>
            </TabsContent>

            <TabsContent value="sentiment" className="space-y-4">
              <div className="h-[400px]">
                <ChartContainer
                  config={{
                    positive: { color: "#4ade80" },
                    negative: { color: "#f87171" },
                    neutral: { color: "#94a3b8" }
                  }}
                >
                  <RechartsPrimitive.PieChart>
                    <RechartsPrimitive.Pie
                      data={sentimentData}
                      dataKey="count"
                      nameKey="type"
                      cx="50%"
                      cy="50%"
                      outerRadius={150}
                      fill="#8884d8"
                      label
                    >
                      {sentimentData.map((entry, index) => {
                        let color = "#94a3b8"; // Default neutral color
                        if (entry.type === "Positive") color = "#4ade80";
                        if (entry.type === "Negative") color = "#f87171";
                        return <RechartsPrimitive.Cell key={`cell-${index}`} fill={color} />;
                      })}
                    </RechartsPrimitive.Pie>
                    <RechartsPrimitive.Tooltip />
                    <RechartsPrimitive.Legend />
                  </RechartsPrimitive.PieChart>
                </ChartContainer>
              </div>
            </TabsContent>

            <TabsContent value="trends" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-white text-lg">Top Positive Topics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-[300px]">
                      <div className="space-y-2">
                        {keywordData
                          .filter(data => data.positiveCount > data.negativeCount)
                          .sort((a, b) => b.positiveCount - a.positiveCount)
                          .slice(0, 10)
                          .map((data, index) => (
                            <div key={index} className="flex justify-between items-center p-2 border-b border-border">
                              <span className="text-white">{data.keyword}</span>
                              <div className="flex items-center">
                                <span className="text-green-400 font-medium mr-2">{data.positiveCount}</span>
                                <span className="text-xs text-muted-foreground">
                                  ({Math.round((data.positiveCount / (data.positiveCount + data.negativeCount + data.neutralCount)) * 100)}%)
                                </span>
                              </div>
                            </div>
                          ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-white text-lg">Top Negative Topics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-[300px]">
                      <div className="space-y-2">
                        {keywordData
                          .filter(data => data.negativeCount > data.positiveCount)
                          .sort((a, b) => b.negativeCount - a.negativeCount)
                          .slice(0, 10)
                          .map((data, index) => (
                            <div key={index} className="flex justify-between items-center p-2 border-b border-border">
                              <span className="text-white">{data.keyword}</span>
                              <div className="flex items-center">
                                <span className="text-red-400 font-medium mr-2">{data.negativeCount}</span>
                                <span className="text-xs text-muted-foreground">
                                  ({Math.round((data.negativeCount / (data.positiveCount + data.negativeCount + data.neutralCount)) * 100)}%)
                                </span>
                              </div>
                            </div>
                          ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      )}
    </Card>
  );
}
