import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Search, Copy, DollarSign, Settings } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { YoutubeVideo } from '@shared/schema';
import {
  highlightFinancialTerms,
  FinancialTermType,
  getPositiveTerms,
  getNegativeTerms,
  getNeutralTerms
} from '@/components/rss/financial-term-highlighter';
import './financial-transcript.css';

interface FinancialTranscriptDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  video: YoutubeVideo;
  transcription: string | null;
}

export function FinancialTranscriptDialog({
  open,
  onOpenChange,
  video,
  transcription
}: FinancialTranscriptDialogProps) {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [highlightedTranscript, setHighlightedTranscript] = useState<string>('');
  const [financialCounts, setFinancialCounts] = useState<{ positive: number; negative: number; neutral: number }>({
    positive: 0,
    negative: 0,
    neutral: 0
  });

  // Get the current financial terms from settings
  const positiveTerms = useMemo(() => getPositiveTerms(), []);
  const negativeTerms = useMemo(() => getNegativeTerms(), []);
  const neutralTerms = useMemo(() => getNeutralTerms(), []);

  // Extract text content from transcript (memoized for performance)
  const extractedTextContent = useMemo(() => {
    if (!transcription ||
        transcription.includes('not found in your subscriptions') ||
        transcription.includes('Transcription not available') ||
        transcription.includes('Unable to fetch transcription')) {
      return '';
    }

    let textContent = '';
    const lines = transcription.split('\n');
    lines.forEach(line => {
      const timestampMatch = line.match(/^\[(\d{2}:\d{2})\]\s*(.*)$/);
      if (timestampMatch) {
        const [_, timestamp, text] = timestampMatch;
        textContent += `${text}\n`; // Only add the text, not the timestamp
      } else if (line.trim()) {
        textContent += `${line}\n`;
      }
    });

    return textContent;
  }, [transcription]);

  // Process the transcript to highlight financial terms when dialog opens
  useEffect(() => {
    if (open && extractedTextContent && !highlightedTranscript) {
      setIsProcessing(true);

      // Use setTimeout to prevent UI blocking
      setTimeout(() => {
        try {
          // Apply financial term highlighting
          const { highlightedContent, counts } = highlightFinancialTerms(extractedTextContent);
          setHighlightedTranscript(highlightedContent);
          setFinancialCounts(counts);
        } catch (error) {
          console.error('Error highlighting financial terms:', error);
          // Fallback to plain text if highlighting fails
          setHighlightedTranscript(`<div>${extractedTextContent}</div>`);
        } finally {
          setIsProcessing(false);
        }
      }, 100);
    }
  }, [open, extractedTextContent, highlightedTranscript]);

  // Simplified search function to reduce CPU usage
  const handleTranscriptSearch = useCallback(() => {
    if (!searchQuery.trim() || !transcription) return;

    try {
      // Simple approach - just highlight the entire transcript with a new version
      const transcriptElement = document.getElementById('financial-transcript-content');
      if (!transcriptElement) return;

      // Get the current HTML content
      const currentContent = transcriptElement.innerHTML;

      // Simple string replacement for highlighting (much less CPU intensive)
      const escapedQuery = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const searchRegex = new RegExp(`(${escapedQuery})`, 'gi');

      // Create highlighted version (simple string replacement)
      const highlightedContent = currentContent.replace(
        searchRegex,
        '<span class="search-highlight">$1</span>'
      );

      // Update the content
      transcriptElement.innerHTML = highlightedContent;

      // Find first match and scroll to it
      setTimeout(() => {
        const firstMatch = transcriptElement.querySelector('.search-highlight');
        if (firstMatch) {
          firstMatch.scrollIntoView({ behavior: 'auto', block: 'center' });
        }
      }, 100);

    } catch (error) {
      console.error('Error searching transcript:', error);
      toast({
        title: "Search Error",
        description: "An error occurred while searching. Please try a different search term.",
        variant: "destructive"
      });
    }
  }, [searchQuery, transcription, toast]);

  // Copy the transcript to clipboard - optimized version
  const copyTranscript = useCallback(() => {
    if (!transcription) return;

    try {
      // Use the already extracted text content for better performance
      const videoTitle = video.title;
      const videoUrl = `https://www.youtube.com/watch?v=${video.id}`;

      // Start with the title and video link
      let formattedText = `${videoTitle}\n${videoUrl}\n\n`;

      // Add the transcript content
      formattedText += extractedTextContent;

      // Use the modern clipboard API
      navigator.clipboard.writeText(formattedText)
        .then(() => {
          toast({
            title: "✅ Transcript Copied!",
            description: `Financial transcript copied to clipboard`,
            variant: "default",
            duration: 2000,
          });

          // Provide visual feedback
          const button = document.activeElement as HTMLButtonElement;
          if (button) {
            const originalText = button.innerText;
            button.innerText = "✓ Copied!";
            setTimeout(() => {
              button.innerText = originalText;
            }, 1500);
          }
        })
        .catch(err => {
          console.error("Failed to copy transcript:", err);
          toast({
            title: "❌ Copy Failed",
            description: "Could not copy to clipboard. Please try again.",
            variant: "destructive",
          });
        });
    } catch (error) {
      console.error('Error copying transcript:', error);
      toast({
        title: "Error",
        description: "Failed to copy transcript",
        variant: "destructive",
      });
    }
  }, [extractedTextContent, toast, video.id, video.title, transcription]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-green-500" />
            Financial Transcript Analysis
            <Badge variant="outline" className="ml-2 text-xs">
              Financial Terms Highlighted
            </Badge>
          </DialogTitle>
          <DialogDescription>
            {video.title}
          </DialogDescription>
        </DialogHeader>

        {/* Financial term highlight counts with settings link */}
        <div className="flex items-center justify-between mb-4 p-2 border border-border rounded">
          <div className="flex items-center gap-4">
            <div className="text-sm font-medium">Financial Term Analysis:</div>
            <div className="flex gap-4">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-green-400"></div>
                <span>Benefits: </span>
                <span className="font-bold text-green-400">{financialCounts.positive}</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-red-400"></div>
                <span>Cuts: </span>
                <span className="font-bold text-red-400">{financialCounts.negative}</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-gray-400"></div>
                <span>Neutral: </span>
                <span className="font-bold text-gray-400">{financialCounts.neutral}</span>
              </div>
            </div>
          </div>

          {/* Link to RSS financial term settings */}
          <Button
            variant="ghost"
            size="sm"
            className="text-xs flex items-center gap-1"
            onClick={() => {
              // Close this dialog
              onOpenChange(false);

              // Navigate to RSS tab with a small delay to allow this dialog to close
              setTimeout(() => {
                // Find the RSS tab button and click it
                const rssTab = document.querySelector('[data-value="rss"]');
                if (rssTab) {
                  (rssTab as HTMLElement).click();

                  // After navigating to RSS tab, find and click the settings button
                  setTimeout(() => {
                    const settingsButton = document.querySelector('.rss-settings-button');
                    if (settingsButton) {
                      (settingsButton as HTMLElement).click();
                    }
                  }, 300);
                }
              }, 100);
            }}
          >
            <Settings className="h-3 w-3 mr-1" />
            Customize Terms
          </Button>
        </div>

        {/* Search bar for transcript */}
        {transcription && !transcription.includes('not found in your subscriptions') &&
         !transcription.includes('Transcription not available') &&
         !transcription.includes('Unable to fetch transcription') && (
          <div className="mb-2 flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search in transcript..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleTranscriptSearch();
                  }
                }}
                className="pl-8"
              />
            </div>
            <Button variant="outline" onClick={handleTranscriptSearch}>
              Search
            </Button>
          </div>
        )}

        {/* Transcript content with optimized scrolling */}
        <div className="relative">
          {/* Loading overlay */}
          {isProcessing && (
            <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center">
              <div className="flex flex-col items-center gap-2">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <p className="text-sm text-muted-foreground">Processing financial terms...</p>
              </div>
            </div>
          )}

          {/* Using ScrollArea for better performance */}
          <ScrollArea className="h-[400px] border rounded-md p-4">
            {transcription ? (
              transcription.includes('not found in your subscriptions') ||
              transcription.includes('Transcription not available') ||
              transcription.includes('Unable to fetch transcription') ? (
                <div className="text-center py-10">
                  <p className="text-muted-foreground">Transcription not available for this video.</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    YouTube may not provide transcripts for all videos.
                  </p>
                </div>
              ) : (
                <div
                  id="financial-transcript-content"
                  className="whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{ __html: highlightedTranscript || `<div class="text-center py-4"><p>Loading transcript content...</p></div>` }}
                />
              )
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}
          </ScrollArea>
        </div>

        {/* Copy button */}
        {transcription && !transcription.includes('not found in your subscriptions') &&
         !transcription.includes('Transcription not available') &&
         !transcription.includes('Unable to fetch transcription') && (
          <div className="mt-4 flex justify-end">
            <Button onClick={copyTranscript}>
              <Copy className="h-4 w-4 mr-2" />
              Copy Financial Transcript
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
