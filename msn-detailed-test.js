import axios from 'axios';
import * as cheerio from 'cheerio';

// Test URL
const testUrl = "https://www.msn.com/en-us/money/retirement/social-security-checks-of-up-to-5-108-going-out-this-week/ar-AA1DKbYM";

// Function to extract article ID from URL
const extractArticleId = (url) => {
  const match = url.match(/\/ar-([A-Za-z0-9]+)/);
  return match ? match[1] : null;
};

// Function to log HTML structure
const logHtmlStructure = (html) => {
  const $ = cheerio.load(html);
  
  console.log('\nHTML Structure Analysis:');
  console.log('- Title tag:', $('title').text());
  console.log('- H1 tags:', $('h1').length);
  if ($('h1').length > 0) {
    $('h1').each((i, el) => {
      console.log(`  - H1 #${i+1}: ${$(el).text().trim()}`);
    });
  }
  
  console.log('- Article tags:', $('article').length);
  console.log('- Main tags:', $('main').length);
  console.log('- Paragraph tags:', $('p').length);
  
  // Check for common content containers
  const contentContainers = [
    '[data-testid="article-body-content"]',
    '[data-testid="articleBodyContent"]',
    '.articlebody',
    '.article-body',
    '.article-content',
    '.content-article',
    '.articlecontent',
    '#content-main',
    '.main-content',
    '.article-page-content',
    '.page-content',
    '.article-text',
    '.article',
    'article'
  ];
  
  console.log('\nContent Containers:');
  for (const selector of contentContainers) {
    const elements = $(selector);
    console.log(`- ${selector}: ${elements.length} elements`);
    if (elements.length > 0) {
      console.log(`  - Content length: ${elements.text().length} characters`);
      console.log(`  - First 100 chars: ${elements.text().substring(0, 100).replace(/\n/g, ' ')}...`);
    }
  }
  
  // Check for scripts that might contain the article content
  console.log('\nScripts:');
  const scripts = $('script');
  console.log(`- Total scripts: ${scripts.length}`);
  
  // Look for JSON-LD scripts which might contain article data
  const jsonLdScripts = $('script[type="application/ld+json"]');
  console.log(`- JSON-LD scripts: ${jsonLdScripts.length}`);
  
  if (jsonLdScripts.length > 0) {
    jsonLdScripts.each((i, el) => {
      try {
        const jsonContent = JSON.parse($(el).html());
        console.log(`  - JSON-LD #${i+1} type: ${jsonContent['@type'] || 'unknown'}`);
        if (jsonContent['@type'] === 'NewsArticle' || jsonContent['@type'] === 'Article') {
          console.log('    - Article found in JSON-LD!');
          if (jsonContent.headline) console.log(`    - Headline: ${jsonContent.headline}`);
          if (jsonContent.author) console.log(`    - Author: ${JSON.stringify(jsonContent.author)}`);
          if (jsonContent.articleBody) {
            console.log(`    - Article body length: ${jsonContent.articleBody.length} characters`);
            console.log(`    - Article body preview: ${jsonContent.articleBody.substring(0, 100)}...`);
          }
        }
      } catch (e) {
        console.log(`  - JSON-LD #${i+1}: Invalid JSON`);
      }
    });
  }
  
  // Check for meta tags
  console.log('\nMeta Tags:');
  const metaTags = $('meta');
  console.log(`- Total meta tags: ${metaTags.length}`);
  
  const importantMeta = [
    'description',
    'og:title',
    'og:description',
    'twitter:title',
    'twitter:description',
    'article:published_time',
    'article:author'
  ];
  
  for (const name of importantMeta) {
    const metaContent = $(`meta[name="${name}"]`).attr('content') || $(`meta[property="${name}"]`).attr('content');
    if (metaContent) {
      console.log(`- ${name}: ${metaContent}`);
    }
  }
  
  // Check for data attributes that might contain article data
  console.log('\nData Attributes:');
  const dataElements = $('[data-article-id], [data-article], [data-content]');
  console.log(`- Elements with article data attributes: ${dataElements.length}`);
  
  if (dataElements.length > 0) {
    dataElements.each((i, el) => {
      const $el = $(el);
      console.log(`  - Element #${i+1}:`);
      console.log(`    - Tag: ${el.tagName}`);
      console.log(`    - Classes: ${$el.attr('class') || 'none'}`);
      console.log(`    - Data attributes: ${Object.keys($el.data()).join(', ')}`);
    });
  }
};

// Test different URL formats
const testUrlFormats = async () => {
  const articleId = extractArticleId(testUrl);
  console.log(`Article ID: ${articleId}`);
  
  const urlFormats = [
    {
      name: "Original URL",
      url: testUrl
    },
    {
      name: "News Format",
      url: `https://www.msn.com/en-us/news/other/ar-${articleId}?ocid=msnews`
    },
    {
      name: "Money Format",
      url: `https://www.msn.com/en-us/money/other/ar-${articleId}?ocid=finance-verthp-feeds`
    },
    {
      name: "Retirement Format",
      url: `https://www.msn.com/en-us/money/retirement/ar-${articleId}?ocid=finance-verthp-feeds`
    }
  ];
  
  for (const format of urlFormats) {
    console.log(`\n\nTesting URL Format: ${format.name}`);
    console.log(`URL: ${format.url}`);
    console.log('='.repeat(50));
    
    try {
      const response = await axios.get(format.url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
        timeout: 10000
      });
      
      console.log(`Status: ${response.status}`);
      console.log(`Content Type: ${response.headers['content-type']}`);
      console.log(`Content Length: ${response.data.length} characters`);
      
      // Analyze the HTML structure
      logHtmlStructure(response.data);
      
    } catch (error) {
      console.log(`Error: ${error.message}`);
    }
  }
};

// Run the test
console.log('=== DETAILED MSN ARTICLE ANALYSIS ===\n');
testUrlFormats();
