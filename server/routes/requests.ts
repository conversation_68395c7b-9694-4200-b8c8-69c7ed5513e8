import { Router } from 'express';
import { requestQueueManager } from '../services/request-queue-manager';

const router = Router();

// Get status of a specific request
router.get('/:id', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const requestId = req.params.id;
    const request = requestQueueManager.getRequest(requestId);

    if (!request) {
      return res.status(404).json({ message: "Request not found" });
    }

    // Check if the request belongs to the current user
    if (request.userId && request.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to access this request" });
    }

    res.json({
      id: request.id,
      type: request.type,
      status: request.status,
      createdAt: request.createdAt,
      startedAt: request.startedAt,
      completedAt: request.completedAt,
      error: request.error,
      result: request.result
    });
  } catch (error) {
    console.error('Error fetching request status:', error);
    res.status(500).json({ message: "Failed to fetch request status" });
  }
});

// Get all requests for the current user
router.get('/', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const requests = requestQueueManager.getUserRequests(req.user.id);

    // Sort by creation time (newest first)
    const sortedRequests = requests.sort((a, b) => 
      b.createdAt.getTime() - a.createdAt.getTime()
    );

    // Return only the most recent 20 requests
    const recentRequests = sortedRequests.slice(0, 20);

    res.json(recentRequests.map(request => ({
      id: request.id,
      type: request.type,
      status: request.status,
      createdAt: request.createdAt,
      startedAt: request.startedAt,
      completedAt: request.completedAt,
      error: request.error
    })));
  } catch (error) {
    console.error('Error fetching user requests:', error);
    res.status(500).json({ message: "Failed to fetch requests" });
  }
});

// Cancel a request
router.post('/:id/cancel', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const requestId = req.params.id;
    const request = requestQueueManager.getRequest(requestId);

    if (!request) {
      return res.status(404).json({ message: "Request not found" });
    }

    // Check if the request belongs to the current user
    if (request.userId && request.userId !== req.user.id) {
      return res.status(403).json({ message: "You don't have permission to cancel this request" });
    }

    const cancelled = requestQueueManager.cancelRequest(requestId);

    if (cancelled) {
      res.json({ message: "Request cancelled successfully" });
    } else {
      res.status(400).json({ message: "Request could not be cancelled (already completed or failed)" });
    }
  } catch (error) {
    console.error('Error cancelling request:', error);
    res.status(500).json({ message: "Failed to cancel request" });
  }
});

export default router;
