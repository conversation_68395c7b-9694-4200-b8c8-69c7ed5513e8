import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronUp } from "lucide-react";

export function ScrollToTopButton() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Function to handle scroll event
    const handleScroll = () => {
      // Find the main content element
      const mainContent = document.querySelector('main.flex-1.overflow-auto');

      if (mainContent) {
        // If we found the main content, check its scroll position
        setIsVisible(mainContent.scrollTop > 300);
      }
    };

    // Find the main content element
    const mainContent = document.querySelector('main.flex-1.overflow-auto');

    // Add scroll event listener to the main content
    if (mainContent) {
      mainContent.addEventListener('scroll', handleScroll);

      // Check initial scroll position
      handleScroll();
    }

    // Cleanup function
    return () => {
      if (mainContent) {
        mainContent.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  // Function to scroll to top
  const scrollToTop = () => {
    const mainContent = document.querySelector('main.flex-1.overflow-auto');

    if (mainContent) {
      mainContent.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  // Always render the button, but control visibility with CSS
  return (
    <Button
      onClick={scrollToTop}
      className={`fixed bottom-8 right-8 z-[9999] rounded-full h-14 w-14 shadow-xl bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-background transition-opacity duration-300 ${isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
      size="icon"
      aria-label="Scroll to top"
    >
      <ChevronUp className="h-8 w-8" />
    </Button>
  );
}
