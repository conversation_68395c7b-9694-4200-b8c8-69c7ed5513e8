// This script runs before React loads to prevent dark mode flicker
(function() {
  try {
    // Check localStorage first (our backup)
    const darkModeFromStorage = localStorage.getItem('darkMode');
    
    if (darkModeFromStorage === 'true') {
      document.documentElement.classList.add('dark');
      console.log('Dark mode initialized from localStorage: true');
    } else if (darkModeFromStorage === 'false') {
      document.documentElement.classList.remove('dark');
      console.log('Dark mode initialized from localStorage: false');
    } else {
      // If no setting in localStorage, check system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (prefersDark) {
        document.documentElement.classList.add('dark');
        console.log('Dark mode initialized from system preference: true');
      }
    }
  } catch (e) {
    console.error('Error initializing dark mode:', e);
  }
})();
