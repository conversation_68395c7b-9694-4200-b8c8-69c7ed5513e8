// use-video-refresh.ts
// Hook for refreshing videos with priority handling

import { useState, useCallback } from 'react';
import { useRequestManager, RequestPriority, RequestType } from '@/lib/request-manager';
import { useToast } from '@/hooks/use-toast';
import { apiRequest, queryClient } from '@/lib/queryClient';

export function useVideoRefresh() {
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const requestManager = useRequestManager();
  const { toast } = useToast();
  
  // Function to refresh all videos with MEDIUM priority (manual refresh)
  const refreshAllVideos = useCallback(async (): Promise<any> => {
    setIsRefreshing(true);
    setError(null);
    
    return new Promise((resolve, reject) => {
      // Add the request to the manager with MEDIUM priority
      requestManager.addRequest(
        RequestType.VIDEO_REFRESH,
        RequestPriority.MEDIUM,
        async () => {
          try {
            // Get all keyword groups first
            const groupsRes = await apiRequest('GET', '/api/keyword-groups');
            const groups = await groupsRes.json();
            
            // Refresh the global feed
            const globalRes = await apiRequest('POST', '/api/videos/refresh');
            const globalResult = await globalRes.json();
            
            // Refresh each keyword group
            const refreshPromises = groups.map(async (group: any) => {
              try {
                const res = await apiRequest('POST', `/api/keyword-groups/${group.id}/refresh`);
                return res.json();
              } catch (error) {
                console.error(`Error refreshing group ${group.id}:`, error);
                return null;
              }
            });
            
            // Wait for all refreshes to complete
            await Promise.all(refreshPromises);
            
            return globalResult;
          } catch (err: any) {
            throw err;
          }
        },
        (result) => {
          setIsRefreshing(false);
          
          // Invalidate all relevant queries
          queryClient.invalidateQueries({ queryKey: ['/api/videos'] });
          queryClient.invalidateQueries({ queryKey: ['/api/keyword-groups'] });
          // Invalidate all group videos queries
          queryClient.invalidateQueries({ 
            predicate: (query) => query.queryKey[0]?.toString().includes('/api/keyword-groups/') 
          });
          
          toast({
            title: 'All feeds refreshed',
            description: 'Your global feed and all keyword groups have been updated.',
          });
          
          resolve(result);
        },
        (err) => {
          setIsRefreshing(false);
          setError(err instanceof Error ? err : new Error(String(err)));
          
          toast({
            title: 'Refresh failed',
            description: err instanceof Error ? err.message : String(err),
            variant: 'destructive',
          });
          
          reject(err);
        }
      );
    });
  }, [requestManager, toast]);
  
  // Function to refresh a specific group with MEDIUM priority (manual refresh)
  const refreshGroupVideos = useCallback(async (groupId: string | number): Promise<any> => {
    setIsRefreshing(true);
    setError(null);
    
    return new Promise((resolve, reject) => {
      // Add the request to the manager with MEDIUM priority
      requestManager.addRequest(
        RequestType.GROUP_REFRESH,
        RequestPriority.MEDIUM,
        async () => {
          try {
            const res = await apiRequest('POST', `/api/keyword-groups/${groupId}/refresh`);
            return res.json();
          } catch (err: any) {
            throw err;
          }
        },
        (result) => {
          setIsRefreshing(false);
          
          // Invalidate relevant queries
          queryClient.invalidateQueries({ queryKey: [`/api/keyword-groups/${groupId}/videos`] });
          
          toast({
            title: 'Group refreshed',
            description: 'The selected keyword group has been updated.',
          });
          
          resolve(result);
        },
        (err) => {
          setIsRefreshing(false);
          setError(err instanceof Error ? err : new Error(String(err)));
          
          toast({
            title: 'Group refresh failed',
            description: err instanceof Error ? err.message : String(err),
            variant: 'destructive',
          });
          
          reject(err);
        }
      );
    });
  }, [requestManager, toast]);
  
  // Function to auto-refresh all videos with LOW priority
  const autoRefreshAllVideos = useCallback(async (): Promise<any> => {
    // Don't show loading state for auto-refresh to avoid UI flicker
    setError(null);
    
    return new Promise((resolve, reject) => {
      // Add the request to the manager with LOW priority
      requestManager.addRequest(
        RequestType.VIDEO_REFRESH,
        RequestPriority.LOW,
        async () => {
          try {
            // Get all keyword groups first
            const groupsRes = await apiRequest('GET', '/api/keyword-groups');
            const groups = await groupsRes.json();
            
            // Refresh the global feed
            const globalRes = await apiRequest('POST', '/api/videos/refresh');
            const globalResult = await globalRes.json();
            
            // Refresh each keyword group
            const refreshPromises = groups.map(async (group: any) => {
              try {
                const res = await apiRequest('POST', `/api/keyword-groups/${group.id}/refresh`);
                return res.json();
              } catch (error) {
                console.error(`Error auto-refreshing group ${group.id}:`, error);
                return null;
              }
            });
            
            // Wait for all refreshes to complete
            await Promise.all(refreshPromises);
            
            return globalResult;
          } catch (err: any) {
            throw err;
          }
        },
        (result) => {
          // Invalidate all relevant queries
          queryClient.invalidateQueries({ queryKey: ['/api/videos'] });
          queryClient.invalidateQueries({ queryKey: ['/api/keyword-groups'] });
          // Invalidate all group videos queries
          queryClient.invalidateQueries({ 
            predicate: (query) => query.queryKey[0]?.toString().includes('/api/keyword-groups/') 
          });
          
          // No toast for auto-refresh to avoid UI clutter
          
          resolve(result);
        },
        (err) => {
          setError(err instanceof Error ? err : new Error(String(err)));
          
          // Only show toast for significant errors
          if (err instanceof Error && !err.message.includes('aborted')) {
            toast({
              title: 'Auto-refresh failed',
              description: err.message,
              variant: 'destructive',
            });
          }
          
          reject(err);
        }
      );
    });
  }, [requestManager, toast]);
  
  // Function to auto-refresh a specific group with LOW priority
  const autoRefreshGroupVideos = useCallback(async (groupId: string | number): Promise<any> => {
    // Don't show loading state for auto-refresh to avoid UI flicker
    setError(null);
    
    return new Promise((resolve, reject) => {
      // Add the request to the manager with LOW priority
      requestManager.addRequest(
        RequestType.GROUP_REFRESH,
        RequestPriority.LOW,
        async () => {
          try {
            const res = await apiRequest('POST', `/api/keyword-groups/${groupId}/refresh`);
            return res.json();
          } catch (err: any) {
            throw err;
          }
        },
        (result) => {
          // Invalidate relevant queries
          queryClient.invalidateQueries({ queryKey: [`/api/keyword-groups/${groupId}/videos`] });
          
          // No toast for auto-refresh to avoid UI clutter
          
          resolve(result);
        },
        (err) => {
          setError(err instanceof Error ? err : new Error(String(err)));
          
          // Only show toast for significant errors
          if (err instanceof Error && !err.message.includes('aborted')) {
            toast({
              title: 'Auto-refresh failed',
              description: err.message,
              variant: 'destructive',
            });
          }
          
          reject(err);
        }
      );
    });
  }, [requestManager, toast]);
  
  // Function to prioritize video loading over refresh operations
  const prioritizeVideoLoading = useCallback(() => {
    // Deprioritize all refresh operations
    requestManager.prioritizeRequestType(RequestType.VIDEO_REFRESH, RequestPriority.LOW);
    requestManager.prioritizeRequestType(RequestType.GROUP_REFRESH, RequestPriority.LOW);
  }, [requestManager]);
  
  return {
    refreshAllVideos,
    refreshGroupVideos,
    autoRefreshAllVideos,
    autoRefreshGroupVideos,
    prioritizeVideoLoading,
    isRefreshing,
    error
  };
}
