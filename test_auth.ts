// Simple test for auth functionality
import express from "express";
import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import session from "express-session";

console.log('Starting auth test...');

try {
  const app = express();
  console.log('Express app created');
  
  app.use(express.json());
  app.use(express.urlencoded({ extended: false }));
  console.log('Middleware set up');
  
  // Simple session setup
  app.use(session({
    secret: 'test-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: false,
      httpOnly: true,
      maxAge: 30 * 24 * 60 * 60 * 1000,
      sameSite: 'lax'
    }
  }));
  console.log('Session middleware set up');
  
  // Set up passport with a simple strategy
  passport.use(new LocalStrategy((username, password, done) => {
    if (username === 'admin' && password === '1') {
      return done(null, { id: 1, username: 'admin' });
    }
    return done(null, false);
  }));
  
  passport.serializeUser((user: any, done) => done(null, user.id));
  passport.deserializeUser((id: number, done) => {
    if (id === 1) {
      done(null, { id: 1, username: 'admin' });
    } else {
      done(null, null);
    }
  });
  
  app.use(passport.initialize());
  app.use(passport.session());
  console.log('Passport initialized');
  
  // Simple login route
  app.post('/api/login', passport.authenticate('local'), (req, res) => {
    res.json({ success: true, user: req.user });
  });
  
  // Simple route to check authentication
  app.get('/api/user', (req, res) => {
    if (req.isAuthenticated()) {
      res.json({ authenticated: true, user: req.user });
    } else {
      res.json({ authenticated: false });
    }
  });
  
  console.log('Routes set up');
  
  // Start the server
  const port = 5006;
  app.listen(port, () => {
    console.log(`Auth test server running on port ${port}`);
  });
  
} catch (error) {
  console.error('Error in auth test:', error);
}
