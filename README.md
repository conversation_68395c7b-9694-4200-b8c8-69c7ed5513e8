# TrendyV3.58

## Getting Started

This application will automatically install all required dependencies when started, without blocking the application's responsiveness.

### Starting the Application

You can start the application using one of the following methods:

#### Method 1: Using npm

```bash
npm run dev
```

#### Method 2: Using the start script

```bash
./start-server.sh
```

Both methods will automatically check for and install any missing dependencies in a non-blocking way, allowing the application to start quickly.

## Features

- Automatic dependency installation in the background
- Non-blocking dependency checks to maintain application responsiveness
- Smart dependency checking that only runs when needed
- Database migrations run automatically on startup
- Admin settings for server configuration

## How Dependency Management Works

The application uses a smart dependency management system that:

1. Checks if dependencies have been installed before
2. Only performs a full check when package.json has changed
3. Runs dependency installation in a background process to avoid blocking the UI
4. Uses a marker file to track when dependencies were last installed

## Development

To install dependencies manually:

```bash
npm install
```

To build the application:

```bash
npm run build
```

To start in production mode:

```bash
npm run start
```

## Troubleshooting

If you experience issues with missing dependencies, you can force a manual installation:

```bash
npm install
```

This will install all dependencies defined in package.json.
