import { fetchVideoViewCount } from './utils/youtube-utils.ts';
import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Use an absolute path for the database file
const dbPath = path.resolve(__dirname, '../data.db');
console.log('Using database path:', dbPath);

async function fixViewCount(videoId) {
  try {
    console.log(`Fixing view count for video ${videoId}`);

    // Fetch the correct view count using our improved logic
    const viewCount = await fetchVideoViewCount(videoId);

    if (viewCount === 0) {
      console.log(`Could not get view count for video ${videoId}`);
      return;
    }

    console.log(`Fetched view count: ${viewCount} for video ${videoId}`);

    // Open the database
    const db = new Database(dbPath);

    // Get the current view count from the database
    const currentViewCount = db.prepare('SELECT view_count FROM youtube_videos WHERE id = ?').get(videoId);
    console.log(`Current view count in database: ${currentViewCount?.view_count || 'not found'}`);

    if (!currentViewCount) {
      console.log(`Video ${videoId} not found in database`);
      db.close();
      return;
    }

    // Update the view count in the database
    const result = db.prepare('UPDATE youtube_videos SET view_count = ? WHERE id = ?').run(viewCount, videoId);

    console.log(`Updated view count for video ${videoId} to ${viewCount} (${result.changes} rows affected)`);

    // Verify the update
    const updatedViewCount = db.prepare('SELECT view_count FROM youtube_videos WHERE id = ?').get(videoId);
    console.log(`New view count in database: ${updatedViewCount?.view_count || 'not found'}`);

    // Close the database connection
    db.close();

    return viewCount;
  } catch (error) {
    console.error(`Error fixing view count for video ${videoId}:`, error);
  }
}

// Fix the view count for the specific video
const videoId = 'DXYOkqoLTDY';
fixViewCount(videoId)
  .then(viewCount => {
    if (viewCount) {
      console.log(`Successfully fixed view count for video ${videoId} to ${viewCount}`);
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
