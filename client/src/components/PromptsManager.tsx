import React, { useState, useEffect, useCallback } from 'react';

// Helper function for consistent logging
const logWithTimestamp = (message: string, data?: any) => {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0]; // HH:MM:SS format
  console.log(`[${timestamp}] 🔍 PromptsManager: ${message}`, data ? data : '');
};
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  CircularProgress,
  Divider
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { OpenRouterPrompt as Prompt } from '@shared/schema';
import {
  getPrompts,
  createPrompt,
  updatePrompt,
  deletePrompt
} from '../services/promptsService';

interface PromptsManagerProps {
  selectedPromptId: number | null;
  onPromptSelected: (promptId: number | null) => void;
  onPromptTextChanged?: (promptText: string | null) => void;
}

const PromptsManager: React.FC<PromptsManagerProps> = ({
  selectedPromptId,
  onPromptSelected,
  onPromptTextChanged
}) => {
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [promptName, setPromptName] = useState('');
  const [promptText, setPromptText] = useState('');
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [promptToDelete, setPromptToDelete] = useState<Prompt | null>(null);
  const [promptToEdit, setPromptToEdit] = useState<Prompt | null>(null);

  // Log component initialization
  useEffect(() => {
    logWithTimestamp('Component initialized');
    return () => {
      logWithTimestamp('Component unmounting');
    };
  }, []);

  // Load prompts when component mounts or when selectedPromptId changes
  useEffect(() => {
    logWithTimestamp(`Loading prompts. Selected prompt ID: ${selectedPromptId}`);
    loadPrompts();
  }, [selectedPromptId]);

  // Load prompts from the server
  const loadPrompts = useCallback(async () => {
    try {
      logWithTimestamp('Starting to load prompts from server');
      setLoading(true);
      const data = await getPrompts();
      logWithTimestamp(`Received ${data.length} prompts from server`, data);
      setPrompts(data);

      // Only handle selection logic if we have prompts
      if (data.length > 0) {
        // If a prompt ID is already selected, verify it exists
        if (selectedPromptId) {
          logWithTimestamp(`Checking if selected prompt ID ${selectedPromptId} exists in loaded prompts`);
          const selectedPromptExists = data.some(p => p.id === selectedPromptId);

          if (selectedPromptExists) {
            logWithTimestamp(`Selected prompt ID ${selectedPromptId} exists, updating prompt text`);
            // The selected prompt exists, find it and use its text
            const selectedPrompt = data.find(p => p.id === selectedPromptId);
            if (selectedPrompt && onPromptTextChanged) {
              logWithTimestamp(`Updating prompt text for ID ${selectedPromptId}`, {
                promptName: selectedPrompt.name,
                promptTextLength: selectedPrompt.promptText.length
              });
              onPromptTextChanged(selectedPrompt.promptText);
            }
          } else {
            logWithTimestamp(`Warning: Selected prompt ID ${selectedPromptId} not found in loaded prompts`);
          }
        }
        // If no prompt is selected and we have a default, select it
        else if (!selectedPromptId) {
          logWithTimestamp('No prompt selected, looking for default prompt');
          const defaultPrompt = data.find(p => p.isDefault);
          if (defaultPrompt) {
            logWithTimestamp(`Found default prompt, selecting ID ${defaultPrompt.id}`, {
              promptName: defaultPrompt.name,
              promptTextLength: defaultPrompt.promptText.length
            });
            onPromptSelected(defaultPrompt.id);
            if (onPromptTextChanged) {
              onPromptTextChanged(defaultPrompt.promptText);
            }
          } else {
            logWithTimestamp('No default prompt found');
          }
        }
      } else {
        logWithTimestamp('No prompts available');
      }

      setError(null);
    } catch (err) {
      logWithTimestamp('Error loading prompts', err);
      console.error('Error loading prompts:', err);
      setError('Failed to load prompts');
    } finally {
      setLoading(false);
      logWithTimestamp('Finished loading prompts');
    }
  }, [selectedPromptId, onPromptSelected, onPromptTextChanged]);

  // Handle selecting a prompt
  const handleSelectPrompt = (promptId: number) => {
    logWithTimestamp(`Prompt selection clicked: ${promptId}`);

    if (promptId !== selectedPromptId) {
      logWithTimestamp(`Changing selected prompt from ${selectedPromptId} to ${promptId}`);
      const selectedPrompt = prompts.find(p => p.id === promptId);
      if (selectedPrompt) {
        logWithTimestamp(`Found prompt to select`, {
          id: selectedPrompt.id,
          name: selectedPrompt.name,
          textLength: selectedPrompt.promptText.length
        });

        // Update the selected ID
        onPromptSelected(promptId);

        // Update the prompt text
        if (onPromptTextChanged) {
          logWithTimestamp(`Updating prompt text for parent component`);
          onPromptTextChanged(selectedPrompt.promptText);
        }
      } else {
        logWithTimestamp(`Warning: Could not find prompt with ID ${promptId}`);
      }
    } else {
      logWithTimestamp(`Prompt ${promptId} is already selected, no action needed`);
    }
  };

  // Open the dialog to add a new prompt
  const handleAddPrompt = () => {
    logWithTimestamp('Add new prompt button clicked');
    setIsEditMode(false);
    setPromptName('');
    setPromptText('');
    setPromptToEdit(null);
    setOpenDialog(true);
    logWithTimestamp('Add prompt dialog opened');
  };

  // Open the dialog to edit an existing prompt
  const handleEditPrompt = (prompt: Prompt, event: React.MouseEvent) => {
    logWithTimestamp(`Edit prompt button clicked for prompt ID ${prompt.id}`, {
      promptName: prompt.name,
      promptTextLength: prompt.promptText.length
    });
    event.stopPropagation(); // Prevent selecting the prompt when clicking edit
    setIsEditMode(true);
    setPromptName(prompt.name);
    setPromptText(prompt.promptText);
    setPromptToEdit(prompt);
    setOpenDialog(true);
    logWithTimestamp(`Edit prompt dialog opened for prompt ID ${prompt.id}`);
  };

  // Handle prompt deletion
  const handleDeletePrompt = (prompt: Prompt) => {
    logWithTimestamp(`Delete prompt button clicked for prompt ID ${prompt.id}`, {
      promptName: prompt.name
    });
    setPromptToDelete(prompt);
    setDeleteConfirmOpen(true);
    logWithTimestamp(`Delete confirmation dialog opened for prompt ID ${prompt.id}`);
  };

  // Confirm and execute prompt deletion
  const confirmDeletePrompt = async () => {
    if (!promptToDelete) {
      logWithTimestamp('No prompt to delete, aborting deletion');
      return;
    }

    logWithTimestamp(`Confirming deletion of prompt ID ${promptToDelete.id}`, {
      promptName: promptToDelete.name
    });

    try {
      logWithTimestamp(`Sending delete request for prompt ID ${promptToDelete.id}`);
      await deletePrompt(promptToDelete.id);
      logWithTimestamp(`Successfully deleted prompt ID ${promptToDelete.id}`);

      // If the deleted prompt was selected, clear the selection
      if (selectedPromptId === promptToDelete.id) {
        logWithTimestamp(`Deleted prompt was selected, clearing selection`);
        onPromptSelected(null);
        if (onPromptTextChanged) {
          onPromptTextChanged(null);
        }
      }

      // Reload prompts
      logWithTimestamp('Reloading prompts after deletion');
      await loadPrompts();
    } catch (err) {
      logWithTimestamp(`Error deleting prompt ID ${promptToDelete.id}`, err);
      console.error('Error deleting prompt:', err);
    } finally {
      setDeleteConfirmOpen(false);
      setPromptToDelete(null);
      logWithTimestamp('Delete confirmation dialog closed');
    }
  };

  // Save a prompt (new or edited)
  const handleSavePrompt = async () => {
    if (!promptName.trim() || !promptText.trim()) {
      logWithTimestamp('Cannot save prompt: name or text is empty');
      return;
    }

    logWithTimestamp(`${isEditMode ? 'Updating' : 'Creating new'} prompt`, {
      promptName,
      promptTextLength: promptText.length,
      isEditMode,
      editingPromptId: promptToEdit?.id
    });

    try {
      if (isEditMode && promptToEdit) {
        // Update existing prompt
        logWithTimestamp(`Sending update request for prompt ID ${promptToEdit.id}`);
        const updatedPrompt = await updatePrompt(promptToEdit.id, {
          name: promptName,
          promptText: promptText
        });
        logWithTimestamp(`Successfully updated prompt ID ${promptToEdit.id}`, {
          updatedName: updatedPrompt.name,
          updatedTextLength: updatedPrompt.promptText.length
        });

        // If this was the selected prompt, update the prompt text
        if (selectedPromptId === promptToEdit.id && onPromptTextChanged) {
          logWithTimestamp(`Updating prompt text for parent component after edit`);
          onPromptTextChanged(updatedPrompt.promptText);
        }
      } else {
        // Create new prompt
        logWithTimestamp('Sending create request for new prompt');
        const newPrompt = await createPrompt(promptName, promptText, false);
        logWithTimestamp(`Successfully created new prompt with ID ${newPrompt.id}`, {
          newPromptName: newPrompt.name,
          newPromptTextLength: newPrompt.promptText.length
        });

        // Select the new prompt if no prompt is currently selected
        if (!selectedPromptId) {
          logWithTimestamp(`No prompt was selected, selecting new prompt ID ${newPrompt.id}`);
          onPromptSelected(newPrompt.id);
          if (onPromptTextChanged) {
            onPromptTextChanged(newPrompt.promptText);
          }
        }
      }

      // Reload prompts
      logWithTimestamp(`Reloading prompts after ${isEditMode ? 'update' : 'creation'}`);
      await loadPrompts();
    } catch (err) {
      const errorType = isEditMode ? 'updating' : 'saving';
      logWithTimestamp(`Error ${errorType} prompt:`, err);
      console.error(`Error ${errorType} prompt:`, err);
    } finally {
      setOpenDialog(false);
      setPromptToEdit(null);
      logWithTimestamp(`${isEditMode ? 'Edit' : 'Add'} prompt dialog closed`);
    }
  };

  return (
    <Box sx={{ mb: 2 }}>
      <Typography variant="h6" gutterBottom>
        Analysis Prompts
      </Typography>

      {loading ? (
        <CircularProgress size={24} />
      ) : error ? (
        <Typography color="error">{error}</Typography>
      ) : (
        <>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1">
              Select a Prompt Template
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddPrompt}
              size="small"
            >
              Add New Prompt
            </Button>
          </Box>

          {prompts.length > 0 && (
            <List sx={{ bgcolor: '#1e1e1e', color: 'white', borderRadius: '4px', mb: 2 }}>
              {prompts.map((prompt) => (
                <React.Fragment key={prompt.id}>
                  <ListItem
                    button
                    selected={selectedPromptId === prompt.id}
                    onClick={() => handleSelectPrompt(prompt.id)}
                    sx={{
                      borderRadius: '4px',
                      mb: 1,
                      backgroundColor: selectedPromptId === prompt.id ? '#2a2a2a' : 'transparent',
                      '&:hover': {
                        backgroundColor: selectedPromptId === prompt.id ? '#2a2a2a' : 'rgba(255, 255, 255, 0.05)'
                      },
                      '&.Mui-selected': {
                        backgroundColor: '#2a2a2a'
                      }
                    }}
                  >
                    <ListItemText
                      primary={
                        <Typography sx={{ color: 'white', fontWeight: selectedPromptId === prompt.id ? 'bold' : 'normal' }}>
                          {String(prompt.name)}
                        </Typography>
                      }
                      secondary={
                        selectedPromptId === prompt.id ?
                          <Typography variant="caption" sx={{ color: '#3b82f6' }}>
                            Active prompt
                          </Typography> : null
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        size="small"
                        onClick={(e) => handleEditPrompt(prompt, e)}
                        sx={{ color: 'white', mr: 1 }}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        edge="end"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent selecting the prompt when clicking delete
                          handleDeletePrompt(prompt);
                        }}
                        disabled={prompts.length === 1} // Prevent deleting the last prompt
                        sx={{ color: 'white' }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {prompts.indexOf(prompt) < prompts.length - 1 && <Divider sx={{ backgroundColor: '#333' }} />}
                </React.Fragment>
              ))}
            </List>
          )}
        </>
      )}

      {/* Add New Prompt Dialog */}
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          style: {
            backgroundColor: '#1e1e1e',
            color: 'white',
          }
        }}
      >
        <DialogTitle>{isEditMode ? 'Edit Prompt' : 'Add New Prompt'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Prompt Name"
            fullWidth
            value={promptName}
            onChange={(e) => setPromptName(e.target.value)}
            sx={{
              mb: 2,
              '& .MuiInputBase-root': {
                color: 'white',
                backgroundColor: '#2a2a2a',
              },
              '& .MuiInputLabel-root': {
                color: 'white',
              },
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#555',
              },
            }}
          />
          <TextField
            label="Prompt Text"
            multiline
            rows={10}
            fullWidth
            value={promptText}
            onChange={(e) => setPromptText(e.target.value)}
            variant="outlined"
            sx={{
              mb: 2,
              '& .MuiInputBase-root': {
                color: 'white',
                backgroundColor: '#2a2a2a',
              },
              '& .MuiInputLabel-root': {
                color: 'white',
              },
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#555',
              },
              '& textarea': {
                color: 'white',
              },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ backgroundColor: '#1e1e1e' }}>
          <Button
            onClick={() => setOpenDialog(false)}
            sx={{ color: 'white' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSavePrompt}
            variant="contained"
            color="primary"
            disabled={!promptName.trim() || !promptText.trim()}
            sx={{
              backgroundColor: '#3b82f6',
              color: 'white',
            }}
          >
            {isEditMode ? 'Update' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
        PaperProps={{
          style: {
            backgroundColor: '#1e1e1e',
            color: 'white',
          }
        }}
      >
        <DialogTitle>Delete Prompt</DialogTitle>
        <DialogContent>
          <Typography sx={{ color: 'white' }}>
            Are you sure you want to delete the prompt "{promptToDelete?.name}"?
          </Typography>
        </DialogContent>
        <DialogActions sx={{ backgroundColor: '#1e1e1e' }}>
          <Button
            onClick={() => setDeleteConfirmOpen(false)}
            sx={{ color: 'white' }}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmDeletePrompt}
            color="error"
            sx={{ color: '#f87171' }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PromptsManager;
