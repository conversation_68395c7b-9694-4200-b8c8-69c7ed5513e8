import React, { useMemo, useState } from 'react';
import { RssFeedItem } from '@shared/schema';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { HighlightCounts, highlightFinancialTerms } from '../financial-term-highlighter';
import { KeywordDetailDialog } from './keyword-detail-dialog';

interface WordData {
  text: string;
  value: number;
  sentiment: 'positive' | 'negative' | 'neutral';
}

interface WordCloudProps {
  feedItems: RssFeedItem[];
  maxWords?: number;
  excludedWords?: string[];
  onOpenArticle?: (articleId: number) => void;
  onSaveArticle?: (articleId: number) => void;
  onOpenInBrowser?: (articleId: number) => void;
}

// Common words to exclude from word cloud
const COMMON_WORDS = new Set([
  // Common English words
  'a', 'an', 'the', 'this', 'that', 'these', 'those', 'it', 'its', 'is', 'are', 'was', 'were',
  'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'can', 'could', 'will',
  'would', 'should', 'may', 'might', 'must', 'shall', 'to', 'of', 'for', 'with', 'in', 'on',
  'at', 'by', 'from', 'about', 'as', 'into', 'like', 'through', 'after', 'over', 'between',
  'out', 'up', 'down', 'off', 'and', 'or', 'but', 'if', 'while', 'because', 'so', 'though',
  'since', 'when', 'where', 'how', 'what', 'who', 'whom', 'which', 'why', 'i', 'you', 'he',
  'she', 'we', 'they', 'them', 'their', 'his', 'her', 'our', 'my', 'your', 'not', 'no', 'yes',
  'said', 'says', 'say', 'see', 'get', 'go', 'know', 'make', 'made', 'take', 'took', 'come',
  'came', 'give', 'gave', 'use', 'used', 'find', 'found', 'tell', 'told', 'ask', 'asked',
  'seem', 'seemed', 'try', 'tried', 'call', 'called', 'need', 'needed', 'feel', 'felt',

  // HTML and CSS related terms
  'style', 'class', 'div', 'span', 'color', 'white', 'black', 'red', 'green', 'blue',
  'important', 'data', 'count', 'source', 'width', 'height', 'margin', 'padding', 'border',
  'background', 'font', 'text', 'size', 'weight', 'align', 'center', 'left', 'right',
  'position', 'display', 'block', 'inline', 'flex', 'grid', 'table', 'none', 'hidden',
  'visible', 'opacity', 'transparent', 'solid', 'dashed', 'dotted', 'relative', 'absolute',
  'fixed', 'static', 'sticky', 'top', 'bottom', 'left', 'right', 'float', 'clear', 'overflow',
  'scroll', 'auto', 'hidden', 'visible', 'clip', 'ellipsis', 'nowrap', 'break', 'word',
  'normal', 'bold', 'italic', 'underline', 'uppercase', 'lowercase', 'capitalize', 'small',
  'large', 'medium', 'thin', 'thick', 'shadow', 'outline', 'transform', 'transition', 'animation',
  'rotate', 'scale', 'translate', 'skew', 'matrix', 'gradient', 'linear', 'radial', 'conic',
  'repeat', 'no-repeat', 'cover', 'contain', 'stretch', 'auto', 'inherit', 'initial', 'unset',
  'default', 'pointer', 'cursor', 'hover', 'active', 'focus', 'visited', 'link', 'target',
  'before', 'after', 'first', 'last', 'nth', 'child', 'type', 'not', 'only', 'root', 'empty',
  'checked', 'disabled', 'enabled', 'required', 'optional', 'valid', 'invalid', 'read', 'write',
  'placeholder', 'selection', 'marker', 'file', 'dir', 'lang', 'scope', 'drop', 'drag',

  // HTML attributes and values
  'href', 'src', 'alt', 'title', 'id', 'name', 'value', 'type', 'action', 'method', 'target',
  'rel', 'media', 'content', 'http', 'https', 'www', 'com', 'org', 'net', 'edu', 'gov', 'io',

  // Data attributes
  'data-positive-count', 'data-negative-count', 'data-neutral-count', 'positive-count',
  'negative-count', 'neutral-count', 'positive', 'negative', 'neutral', 'financial-term',
  'financial-term-positive', 'financial-term-negative', 'financial-term-neutral',
  'dollar-amount-highlight',

  // Common formatting words
  'nbsp', 'amp', 'lt', 'gt', 'quot', 'apos', 'copy', 'reg', 'trade', 'deg', 'bull', 'hellip',
  'laquo', 'raquo', 'ndash', 'mdash', 'lsquo', 'rsquo', 'ldquo', 'rdquo', 'sbquo', 'bdquo',
  'lsaquo', 'rsaquo', 'prime', 'Prime', 'frasl', 'euro', 'pound', 'yen', 'cent', 'curren',
]);

export function WordCloudVisualization({
  feedItems,
  maxWords = 100,
  excludedWords = [],
  onOpenArticle,
  onSaveArticle,
  onOpenInBrowser
}: WordCloudProps) {
  // State for the keyword detail dialog
  const [selectedWord, setSelectedWord] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Create a set of excluded words for faster lookup
  const excludedWordsSet = useMemo(() => {
    const set = new Set([...COMMON_WORDS]);
    excludedWords.forEach(word => set.add(word.toLowerCase()));
    return set;
  }, [excludedWords]);

  // Handle word click
  const handleWordClick = (word: string) => {
    setSelectedWord(word);
    setIsDialogOpen(true);
  };

  // Extract and analyze words from all feed items
  const wordData = useMemo(() => {
    if (!feedItems || feedItems.length === 0) return [];

    const wordMap = new Map<string, { count: number; positive: number; negative: number; neutral: number }>();

    // Process each feed item
    feedItems.forEach(item => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      // Combine all text content
      const allText = `${title} ${content} ${scrapedContent}`;

      // First, strip HTML tags and formatting elements
      const strippedText = allText
        .replace(/<[^>]*>/g, ' ') // Remove HTML tags
        .replace(/&[a-z]+;/g, ' ') // Remove HTML entities like &nbsp;
        .replace(/style="[^"]*"/g, ' ') // Remove style attributes
        .replace(/class="[^"]*"/g, ' ') // Remove class attributes
        .replace(/data-[a-z-]+="[^"]*"/g, ' '); // Remove data attributes

      // Analyze sentiment
      const { counts } = highlightFinancialTerms(strippedText);

      // Extract words, convert to lowercase, and remove punctuation
      const words = strippedText.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => {
          // Must be longer than 3 characters
          if (word.length <= 3) return false;

          // Must not be in our excluded words set
          if (excludedWordsSet.has(word)) return false;

          // Exclude words that are likely HTML/CSS related
          if (word.includes('span') ||
              word.includes('div') ||
              word.includes('style') ||
              word.includes('class') ||
              word.includes('data')) return false;

          // Exclude words that are likely not meaningful
          if (/^\d+$/.test(word)) return false; // Exclude words that are just numbers
          if (/^[a-f0-9]{6,}$/i.test(word)) return false; // Exclude what might be hex colors or hashes

          return true;
        });

      // Count word frequencies
      words.forEach(word => {
        if (!wordMap.has(word)) {
          wordMap.set(word, { count: 0, positive: 0, negative: 0, neutral: 0 });
        }

        const data = wordMap.get(word)!;
        data.count += 1;

        // Distribute sentiment counts proportionally to word occurrences
        const totalTerms = counts.positive + counts.negative + counts.neutral;
        if (totalTerms > 0) {
          data.positive += counts.positive / totalTerms;
          data.negative += counts.negative / totalTerms;
          data.neutral += counts.neutral / totalTerms;
        }
      });
    });

    // Convert map to array and sort by count
    return Array.from(wordMap.entries())
      .map(([text, data]) => {
        // Determine dominant sentiment
        let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral';
        if (data.positive > data.negative && data.positive > data.neutral) {
          sentiment = 'positive';
        } else if (data.negative > data.positive && data.negative > data.neutral) {
          sentiment = 'negative';
        }

        return {
          text,
          value: data.count,
          sentiment
        };
      })
      .sort((a, b) => b.value - a.value)
      .slice(0, maxWords);
  }, [feedItems, excludedWordsSet, maxWords]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-white">Word Cloud</CardTitle>
        <CardDescription className="text-white">
          Most common words across all RSS feeds, colored by sentiment
        </CardDescription>
      </CardHeader>
      <CardContent>
        {wordData.length === 0 ? (
          <div className="flex justify-center items-center h-[200px]">
            <p className="text-white text-lg">No data available</p>
          </div>
        ) : (
          <div className="h-[300px] flex flex-wrap justify-center items-center gap-2 overflow-y-auto">
            {wordData.map((word, index) => {
              // Calculate font size based on frequency (between 12px and 48px)
              const maxValue = wordData[0].value;
              const minSize = 12;
              const maxSize = 48;
              const fontSize = minSize + ((word.value / maxValue) * (maxSize - minSize));

              // Set color based on sentiment
              let color = '#94a3b8'; // neutral
              if (word.sentiment === 'positive') color = '#4ade80';
              if (word.sentiment === 'negative') color = '#f87171';

              return (
                <span
                  key={index}
                  className="inline-block px-1 py-0.5 rounded cursor-pointer hover:opacity-80 transition-opacity hover:bg-primary/10"
                  style={{ fontSize: `${fontSize}px`, color }}
                  title={`${word.text}: ${word.value} occurrences - Click for details`}
                  onClick={() => handleWordClick(word.text)}
                >
                  {word.text}
                </span>
              );
            })}
          </div>
        )}
      </CardContent>

      {/* Keyword Detail Dialog */}
      {selectedWord && (
        <KeywordDetailDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          keyword={selectedWord}
          feedItems={feedItems}
          type="word"
          onOpenArticle={onOpenArticle}
          onSaveArticle={onSaveArticle}
          onOpenInBrowser={onOpenInBrowser}
        />
      )}
    </Card>
  );
}
