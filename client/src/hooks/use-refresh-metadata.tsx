import { useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useRef } from "react";

export function useRefreshMetadata() {
  const { toast } = useToast();

  // Track if a refresh is in progress to prevent multiple refreshes
  const refreshInProgressRef = useRef(false);
  // Track the last refresh time to implement debouncing
  const lastRefreshTimeRef = useRef(0);

  // Mutation for refreshing video metadata only (no transcripts or AI analysis)
  const refreshMetadata = useMutation({
    // Add a unique mutation key to prevent multiple simultaneous refreshes
    mutationKey: ['refresh-metadata'],
    mutationFn: async () => {
      // Check if a refresh is already in progress
      if (refreshInProgressRef.current) {
        console.log('Refresh already in progress, skipping duplicate refresh request');
        return { message: 'Refresh already in progress' };
      }

      // Implement debounce to prevent rapid successive refreshes
      const now = Date.now();
      const minTimeBetweenRefreshes = 15000; // 15 seconds minimum between refreshes

      if (now - lastRefreshTimeRef.current < minTimeBetweenRefreshes) {
        console.log(`Refresh attempt too soon (${now - lastRefreshTimeRef.current}ms since last refresh), debouncing`);
        return { message: 'Refresh debounced' };
      }

      // Update the last refresh time
      lastRefreshTimeRef.current = now;

      // Set the flag to indicate a refresh is in progress
      refreshInProgressRef.current = true;

      console.log('Refreshing video metadata for all channels');
      try {
        const res = await apiRequest("POST", "/api/youtube-channels/refresh-metadata");
        if (!res.ok) {
          throw new Error(`Failed to refresh metadata: ${res.status} ${res.statusText}`);
        }
        return res.json();
      } finally {
        // Clear the flag after a delay to prevent rapid successive refreshes
        setTimeout(() => {
          refreshInProgressRef.current = false;
        }, 5000);
      }
    },
    onSuccess: (data) => {
      // Log detailed statistics
      console.log('Metadata refresh completed:', {
        totalChannels: data.totalChannels,
        totalVideosRefreshed: data.totalVideosRefreshed,
        channelsProcessed: data.channelsProcessed
      });

      // Mark queries as stale and refetch them to update the UI automatically
      console.log('Marking queries as stale and triggering automatic refetches');

      // First, remove all cached data to force fresh fetches
      console.log('Removing cached data to force fresh fetches');
      queryClient.removeQueries({
        queryKey: ["/api/youtube-channels/all-videos"],
      });

      // Mark all-videos query as stale and refetch with explicit refetchType: 'all'
      queryClient.invalidateQueries({
        queryKey: ["/api/youtube-channels/all-videos"],
        refetchType: 'all', // Force refetch all queries, including inactive ones
      });

      // Mark channels query as stale and refetch with explicit refetchType: 'all'
      queryClient.invalidateQueries({
        queryKey: ["/api/youtube-channels"],
        refetchType: 'all', // Force refetch all queries, including inactive ones
      });

      // Mark individual channel video queries as stale and refetch with explicit refetchType: 'all'
      queryClient.invalidateQueries({
        predicate: (query) =>
          typeof query.queryKey[0] === 'string' &&
          query.queryKey[0].includes('/api/youtube-channels/') &&
          query.queryKey[0].includes('/videos'),
        refetchType: 'all', // Force refetch all queries, including inactive ones
      });

      // Force immediate refetch without waiting for staleTime
      console.log('Forcing immediate refetch of all videos query');
      queryClient.refetchQueries({
        queryKey: ["/api/youtube-channels/all-videos"],
        exact: false, // Match any query that starts with this key
        type: 'all', // Refetch all matching queries
      });

      // Add a small delay before forcing another explicit refetch to ensure the invalidation has been processed
      setTimeout(() => {
        console.log('Forcing secondary refetch of all videos query to ensure UI update');
        // Explicitly refetch the all-videos query again to ensure the UI is updated
        queryClient.refetchQueries({
          queryKey: ["/api/youtube-channels/all-videos"],
          exact: false, // Match any query that starts with this key
          type: 'all', // Refetch all matching queries
        });
      }, 500);

      // Force chart refresh by triggering a custom event
      // This will notify any chart components that they need to refresh their data
      setTimeout(() => {
        console.log('Triggering chart refresh event');
        window.dispatchEvent(new CustomEvent('viewCountsUpdated', {
          detail: {
            timestamp: Date.now(),
            totalVideosRefreshed: data.totalVideosRefreshed,
            totalChannels: data.totalChannels
          }
        }));
      }, 1000);

      // Update the last refresh time
      lastRefreshTimeRef.current = Date.now();

      // Clear the refresh in progress flag with a delay
      // This prevents rapid successive refreshes
      setTimeout(() => {
        console.log('Clearing refresh in progress flag');
        refreshInProgressRef.current = false;
      }, 5000);

      // Delay the toast to ensure UI is not overwhelmed with updates
      setTimeout(() => {
        // Show success toast with statistics
        toast({
          title: "View Counts Updated",
          description: `Updated ${data.totalVideosRefreshed} videos from ${data.totalChannels} channels.`,
          duration: 4000,
        });
      }, 500);
    },
    onError: (error: Error) => {
      console.error('Error refreshing metadata:', error);
      toast({
        title: "Failed to update view counts",
        description: error.message,
        variant: "destructive",
        duration: 5000,
      });
    },
  });

  return {
    refreshMetadata,
  };
}
