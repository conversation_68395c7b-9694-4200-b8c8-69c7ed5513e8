import axios from 'axios';

async function testMsnApi() {
  try {
    // Test the MSN article API endpoint
    const url = 'https://www.msn.com/en-us/money/retirement/social-security-checks-of-up-to-5-108-going-out-this-week/ar-AA1DKbYM';
    const response = await axios.get('http://localhost:5001/api/rss-feeds/msn-article-v2', {
      params: {
        url: url,
        title: 'Social Security: Checks of Up to $5,108 Going Out This Week'
      }
    });

    console.log('API Response:', response.status);
    console.log('Content Length:', response.data.content ? response.data.content.length : 0);
    console.log('Content Preview:', response.data.content ? response.data.content.substring(0, 500) : 'No content');
  } catch (error) {
    console.error('Error testing MSN API:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
}

testMsnApi();
