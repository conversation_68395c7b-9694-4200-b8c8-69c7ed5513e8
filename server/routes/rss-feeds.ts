import { Router } from 'express';
import { storage } from '../storage';
import { z } from 'zod';
import axios from 'axios';
import * as cheerio from 'cheerio';
import { RssFeed, RssFeedItem, SavedArticleList, SavedArticle } from '@shared/schema';
import { ensureAuthenticated } from '../middleware/auth';
import { generateMsnArticleContent } from '../utils/msn-content-generator';
import { Readability } from '@mozilla/readability';
import { JSDOM } from 'jsdom';
import { generateRssFeedPdf } from '../utils/pdf-generator-new';
import { generateRssFeedText } from '../utils/text-generator';
import { format } from 'date-fns';
import { scrapeTweet, scrapeTweetWith12ft, generateTweetHtml } from '../utils/x-scraper';
import { extractYouTubeVideoIds, fetchYouTubeTranscript } from '../utils/youtube-transcript';
import { detectFinancialBenefits, getHighestBenefit } from '../utils/financial-benefit-detector';
// We'll use puppeteer for sites that are difficult to scrape
// import puppeteer from 'puppeteer';

// Middleware to bypass authentication for testing endpoints
const noAuthRequired = (req, res, next) => {
  next();
};

const router = Router();

// Apply authentication middleware to all routes except the MSN article endpoints and test endpoints
// Check if the user is authenticated using req.session.passport.user
router.use((req, res, next) => {
  // Skip authentication for the MSN article endpoints and test endpoints
  if (req.path === '/msn-article' || req.path === '/msn-article-v2' || req.path === '/test-xcom-scraping') {
    return next();
  }

  if (req.session && req.session.passport && req.session.passport.user) {
    return next();
  }
  res.status(401).json({ error: 'Unauthorized' });
});

// Schema for adding a new RSS feed
const addRssFeedSchema = z.object({
  name: z.string().min(1, 'Feed name is required'),
  url: z.string().url('Invalid URL format').min(1, 'Feed URL is required'),
});

// Schema for updating an RSS feed
const updateRssFeedSchema = z.object({
  name: z.string().min(1, 'Feed name is required').optional(),
  url: z.string().url('Invalid URL format').optional(),
});

// Schema for updating a feed item
const updateFeedItemSchema = z.object({
  isRead: z.boolean().optional(),
});

// Schema for updating feed orders
const updateFeedOrdersSchema = z.array(
  z.object({
    id: z.number(),
    displayOrder: z.number(),
  })
);

// Schema for creating a saved article list
const createSavedArticleListSchema = z.object({
  name: z.string().min(1, 'List name is required'),
});

// Schema for updating a saved article list
const updateSavedArticleListSchema = z.object({
  name: z.string().min(1, 'List name is required'),
});

// Schema for adding an article to a saved list
const addSavedArticleSchema = z.object({
  articleId: z.number(),
});

// Get all RSS feeds for the current user
router.get('/', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const feeds = await storage.getRssFeeds(userId);
    res.json(feeds);
  } catch (error) {
    console.error('Error getting RSS feeds:', error);
    res.status(500).json({ error: 'Failed to get RSS feeds' });
  }
});

// Get article counts for all feeds
router.get('/counts', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get all feeds for this user
    const feeds = await storage.getRssFeeds(userId);

    // Create an object to store the counts
    const counts: Record<number, number> = {};

    // For each feed, get the count of items
    for (const feed of feeds) {
      const countStmt = storage.db.prepare(`
        SELECT COUNT(*) as count FROM rss_feed_items WHERE feed_id = ?
      `);
      const result = countStmt.get(feed.id);
      counts[feed.id] = result.count;
    }

    res.json(counts);
  } catch (error) {
    console.error('Error fetching RSS feed counts:', error);
    res.status(500).json({ error: 'Failed to fetch RSS feed counts' });
  }
});

// Export all RSS feeds for a user
router.get('/export', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get all feeds for the user
    const feeds = await storage.getRssFeeds(userId);

    // Create a simplified version of the feeds for export
    const exportData = feeds.map(feed => ({
      name: feed.name,
      url: feed.url
    }));

    // Set headers for file download
    const today = new Date();
    const dateStr = format(today, 'yyyy-MM-dd');
    const filename = `rss_feeds_export_${dateStr}.json`;

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Send the JSON data
    res.json(exportData);
  } catch (error) {
    console.error('Error exporting RSS feeds:', error);
    res.status(500).json({ error: 'Failed to export RSS feeds' });
  }
});

// Import RSS feeds for a user
router.post('/import', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Validate the import data
    if (!Array.isArray(req.body)) {
      return res.status(400).json({ error: 'Invalid import data. Expected an array of feeds.' });
    }

    console.log(`Importing ${req.body.length} RSS feeds for user ${userId}`);

    // Get existing feeds to check for duplicates
    const existingFeeds = await storage.getRssFeeds(userId);
    const existingUrls = new Set(existingFeeds.map(feed => feed.url));

    // Process each feed in the import data
    const results = {
      imported: 0,
      skipped: 0,
      failed: 0,
      total: req.body.length,
      itemsAdded: 0,
      importedFeeds: [] // Track the IDs of successfully imported feeds
    };

    for (const feedData of req.body) {
      try {
        // Validate feed data
        if (!feedData.name || !feedData.url) {
          console.log(`Skipping feed with missing name or URL: ${JSON.stringify(feedData)}`);
          results.failed++;
          continue;
        }

        // Skip if the feed URL already exists
        if (existingUrls.has(feedData.url)) {
          console.log(`Skipping duplicate feed URL: ${feedData.url}`);
          results.skipped++;
          continue;
        }

        // Validate that the URL is a valid RSS feed
        try {
          console.log(`Validating RSS feed URL: ${feedData.url}`);
          const response = await axios.get(feedData.url);
          const $ = cheerio.load(response.data, { xmlMode: true });

          // Check if it's a valid RSS feed (should have feed or rss element)
          if (!$('feed').length && !$('rss').length) {
            console.log(`Invalid RSS feed format for URL: ${feedData.url}`);
            results.failed++;
            continue;
          }
        } catch (error) {
          console.error('Error validating RSS feed URL:', error);
          results.failed++;
          continue;
        }

        // Add the feed
        console.log(`Adding feed: ${feedData.name} (${feedData.url})`);
        const feed = await storage.addRssFeed(userId, {
          name: feedData.name,
          url: feedData.url
        });

        // Immediately fetch and store feed items
        console.log(`Fetching items for newly added feed: ${feed.id} (${feed.name})`);
        const items = await fetchAndStoreFeedItems(feed);
        console.log(`Fetched ${items.length} items for feed: ${feed.id} (${feed.name})`);

        // Update the feed's last refresh time
        const refreshTime = new Date();
        await storage.updateRssFeed(feed.id, { lastRefreshTime: refreshTime });
        console.log(`Updated last refresh time for feed ${feed.id} to ${refreshTime.toISOString()}`);

        // Pre-fetch content for X.com links to ensure they're properly loaded
        try {
          // Find any X.com links in the feed items
          const xcomItems = items.filter(item =>
            item.link && (item.link.includes('twitter.com') || item.link.includes('x.com'))
          );

          if (xcomItems.length > 0) {
            console.log(`Found ${xcomItems.length} X.com items in feed ${feed.id}, pre-fetching content...`);

            // Pre-fetch content for each X.com item
            for (const item of xcomItems) {
              try {
                console.log(`Pre-fetching content for X.com item: ${item.id} - ${item.title}`);
                const content = await scrapeArticleContent(item.link);

                // Update the item with the scraped content
                await storage.updateRssFeedItem(item.id, { scrapedContent: content });
                console.log(`Successfully pre-fetched content for X.com item: ${item.id}`);
              } catch (xcomError) {
                console.error(`Error pre-fetching X.com content for item ${item.id}:`, xcomError);
                // Continue with the next item even if this one fails
              }
            }
          }
        } catch (prefetchError) {
          console.error(`Error during X.com content pre-fetching for feed ${feed.id}:`, prefetchError);
          // Continue with the import process even if pre-fetching fails
        }

        // Track the number of items added
        results.itemsAdded += items.length;
        results.importedFeeds.push(feed.id);

        results.imported++;
        existingUrls.add(feedData.url); // Add to set to prevent duplicates within the import
      } catch (error) {
        console.error('Error importing feed:', error);
        results.failed++;
      }
    }

    console.log(`Import completed: ${results.imported} imported, ${results.skipped} skipped, ${results.failed} failed, ${results.itemsAdded} items added`);

    // We don't need to automatically refresh imported feeds anymore since we already
    // fetched their items during import using our improved fetchAndStoreFeedItems function
    // that handles X.com content pre-fetching in the background

    // Just log the results for clarity
    if (results.importedFeeds.length > 0) {
      console.log(`Successfully imported ${results.importedFeeds.length} feeds with ${results.itemsAdded} items`);

      // Log information about X.com items that are being processed in the background
      const xcomItemsCount = results.importedFeeds.reduce((count, feedId) => {
        try {
          // Get the feed items
          const items = storage.getRssFeedItems(feedId);
          // Count X.com items (this is a Promise, but we're just logging, not waiting for the result)
          items.then(feedItems => {
            const xcomItems = feedItems.filter(item =>
              item.link && (item.link.includes('twitter.com') || item.link.includes('x.com'))
            );
            if (xcomItems.length > 0) {
              console.log(`Feed ${feedId} has ${xcomItems.length} X.com items being processed in the background`);
            }
          }).catch(error => {
            console.error(`Error counting X.com items for feed ${feedId}:`, error);
          });
        } catch (error) {
          console.error(`Error accessing items for feed ${feedId}:`, error);
        }
        return count;
      }, 0);
    }

    res.json({
      message: 'Import completed and feeds refreshed',
      results
    });
  } catch (error) {
    console.error('Error importing RSS feeds:', error);
    res.status(500).json({ error: 'Failed to import RSS feeds' });
  }
});

// Add a new RSS feed
router.post('/', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const validatedData = addRssFeedSchema.parse(req.body);

    // Validate that the URL is a valid RSS feed
    try {
      const response = await axios.get(validatedData.url);
      const $ = cheerio.load(response.data, { xmlMode: true });

      // Check if it's a valid RSS feed (should have feed or rss element)
      if (!$('feed').length && !$('rss').length) {
        return res.status(400).json({ error: 'Invalid RSS feed URL' });
      }
    } catch (error) {
      console.error('Error validating RSS feed URL:', error);
      return res.status(400).json({ error: 'Could not fetch RSS feed from the provided URL' });
    }

    const feed = await storage.addRssFeed(userId, {
      name: validatedData.name,
      url: validatedData.url,
    });

    // Immediately fetch and store feed items
    await fetchAndStoreFeedItems(feed);

    res.status(201).json(feed);
  } catch (error) {
    console.error('Error adding RSS feed:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: 'Failed to add RSS feed' });
  }
});

// Get a specific RSS feed
router.get('/:id', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const feedId = parseInt(req.params.id);
    if (isNaN(feedId)) {
      return res.status(400).json({ error: 'Invalid feed ID' });
    }

    const feed = await storage.getRssFeed(feedId);
    if (!feed) {
      return res.status(404).json({ error: 'Feed not found' });
    }

    // Check if the feed belongs to the current user
    if (feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    res.json(feed);
  } catch (error) {
    console.error('Error getting RSS feed:', error);
    res.status(500).json({ error: 'Failed to get RSS feed' });
  }
});

// Update a specific RSS feed
router.put('/:id', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const feedId = parseInt(req.params.id);
    if (isNaN(feedId)) {
      return res.status(400).json({ error: 'Invalid feed ID' });
    }

    const feed = await storage.getRssFeed(feedId);
    if (!feed) {
      return res.status(404).json({ error: 'Feed not found' });
    }

    // Check if the feed belongs to the current user
    if (feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    const validatedData = updateRssFeedSchema.parse(req.body);
    const updatedFeed = await storage.updateRssFeed(feedId, validatedData);

    res.json(updatedFeed);
  } catch (error) {
    console.error('Error updating RSS feed:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: 'Failed to update RSS feed' });
  }
});

// Delete a specific RSS feed
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const feedId = parseInt(req.params.id);
    if (isNaN(feedId)) {
      return res.status(400).json({ error: 'Invalid feed ID' });
    }

    const feed = await storage.getRssFeed(feedId);
    if (!feed) {
      return res.status(404).json({ error: 'Feed not found' });
    }

    // Check if the feed belongs to the current user
    if (feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    await storage.deleteRssFeed(feedId);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting RSS feed:', error);
    res.status(500).json({ error: 'Failed to delete RSS feed' });
  }
});

// Update the order of RSS feeds
router.post('/update-order', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Validate the request body
    const feedOrders = updateFeedOrdersSchema.parse(req.body);

    // Get all feeds for the user to verify ownership
    const userFeeds = await storage.getRssFeeds(userId);
    const userFeedIds = new Set(userFeeds.map(feed => feed.id));

    // Check if all feeds in the request belong to the user
    const invalidFeedIds = feedOrders.filter(feed => !userFeedIds.has(feed.id)).map(feed => feed.id);
    if (invalidFeedIds.length > 0) {
      return res.status(403).json({
        error: 'Forbidden',
        message: `The following feed IDs do not belong to the current user: ${invalidFeedIds.join(', ')}`
      });
    }

    // Update the order of the feeds
    const updatedFeeds = await storage.updateRssFeedsOrder(userId, feedOrders);

    res.json(updatedFeeds);
  } catch (error) {
    console.error('Error updating RSS feed order:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: 'Failed to update RSS feed order' });
  }
});

// Refresh all RSS feeds for a user
router.post('/refresh-all', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get all feeds for the user
    const feeds = await storage.getRssFeeds(userId);

    if (feeds.length === 0) {
      return res.json({ message: 'No feeds to refresh', refreshed: 0, total: 0 });
    }

    // Track refresh results
    const results = {
      refreshed: 0,
      failed: 0,
      total: feeds.length,
      itemsAdded: 0
    };

    // Refresh each feed
    for (const feed of feeds) {
      try {
        // Fetch and store feed items
        const items = await fetchAndStoreFeedItems(feed);

        // Update the feed's last refresh time
        const refreshTime = new Date();
        await storage.updateRssFeed(feed.id, { lastRefreshTime: refreshTime });

        // Pre-fetch content for X.com links to ensure they're properly loaded
        try {
          // Find any X.com links in the feed items
          const xcomItems = items.filter(item =>
            item.link && (item.link.includes('twitter.com') || item.link.includes('x.com'))
          );

          if (xcomItems.length > 0) {
            console.log(`Found ${xcomItems.length} X.com items in feed ${feed.id}, pre-fetching content...`);

            // Pre-fetch content for each X.com item (limit to first 5 to avoid long processing times)
            const itemsToProcess = xcomItems.slice(0, 5);
            for (const item of itemsToProcess) {
              try {
                console.log(`Pre-fetching content for X.com item: ${item.id} - ${item.title}`);
                const content = await scrapeArticleContent(item.link);

                // Update the item with the scraped content
                await storage.updateRssFeedItem(item.id, { scrapedContent: content });
                console.log(`Successfully pre-fetched content for X.com item: ${item.id}`);
              } catch (xcomError) {
                console.error(`Error pre-fetching X.com content for item ${item.id}:`, xcomError);
                // Continue with the next item even if this one fails
              }
            }
          }
        } catch (prefetchError) {
          console.error(`Error during X.com content pre-fetching for feed ${feed.id}:`, prefetchError);
          // Continue with the refresh process even if pre-fetching fails
        }

        results.refreshed++;
        results.itemsAdded += items.length;
      } catch (error) {
        console.error(`Error refreshing feed ${feed.id}:`, error);
        results.failed++;
      }
    }

    res.json({
      message: 'Feeds refreshed successfully',
      refreshed: results.refreshed,
      failed: results.failed,
      total: results.total,
      itemsAdded: results.itemsAdded
    });
  } catch (error) {
    console.error('Error refreshing all RSS feeds:', error);
    res.status(500).json({ error: 'Failed to refresh RSS feeds' });
  }
});

// Refresh a specific RSS feed
router.post('/:id/refresh', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const feedId = parseInt(req.params.id);
    if (isNaN(feedId)) {
      return res.status(400).json({ error: 'Invalid feed ID' });
    }

    const feed = await storage.getRssFeed(feedId);
    if (!feed) {
      return res.status(404).json({ error: 'Feed not found' });
    }

    // Check if the feed belongs to the current user
    if (feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Fetch and store feed items
    console.log(`Refreshing feed ${feedId} (${feed.name})`);

    // Start a timer to measure refresh time
    const startTime = Date.now();

    // Use our improved feed refresh function that handles concurrency
    const items = await fetchAndStoreFeedItems(feed);

    // Calculate refresh time
    const refreshDuration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`Refresh completed for feed ${feedId} in ${refreshDuration}s, found ${items.length} items`);

    // Update the feed's last refresh time
    const refreshTime = new Date();
    await storage.updateRssFeed(feedId, { lastRefreshTime: refreshTime });
    console.log(`Updated last refresh time for feed ${feedId} to ${refreshTime.toISOString()}`);

    // Note: X.com content pre-fetching is now handled in the background by the fetchAndStoreFeedItems function
    // This makes the refresh endpoint respond faster while still ensuring X.com content is fetched

    // Verify items were stored correctly by fetching them again
    const verifiedItems = await storage.getRssFeedItems(feedId);
    console.log(`Verified ${verifiedItems.length} items for feed ${feedId}`);

    // Return a response with detailed information
    res.json({
      message: 'Feed refreshed successfully',
      itemCount: verifiedItems.length,
      refreshDuration: `${refreshDuration}s`,
      lastRefreshTime: refreshTime.toISOString(),
      feedId: feedId,
      feedName: feed.name,
      success: true,
      // Include information about X.com items that are being processed in the background
      xcomItemsProcessing: items.filter(item =>
        item.link && (item.link.includes('twitter.com') || item.link.includes('x.com'))
      ).length
    });
  } catch (error) {
    console.error('Error refreshing RSS feed:', error);
    res.status(500).json({ error: 'Failed to refresh RSS feed' });
  }
});

// Get items for a specific RSS feed
router.get('/:id/items', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const feedId = parseInt(req.params.id);
    if (isNaN(feedId)) {
      return res.status(400).json({ error: 'Invalid feed ID' });
    }

    const feed = await storage.getRssFeed(feedId);
    if (!feed) {
      return res.status(404).json({ error: 'Feed not found' });
    }

    // Check if the feed belongs to the current user
    if (feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    const items = await storage.getRssFeedItems(feedId);
    res.json(items);
  } catch (error) {
    console.error('Error getting RSS feed items:', error);
    res.status(500).json({ error: 'Failed to get RSS feed items' });
  }
});

// Update a feed item (mark as read/unread)
router.patch('/items/:id', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const itemId = parseInt(req.params.id);
    if (isNaN(itemId)) {
      return res.status(400).json({ error: 'Invalid item ID' });
    }

    const item = await storage.getRssFeedItem(itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Get the feed to check ownership
    const feed = await storage.getRssFeed(item.feedId);
    if (!feed || feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    const validatedData = updateFeedItemSchema.parse(req.body);
    const updatedItem = await storage.updateRssFeedItem(itemId, validatedData);

    res.json(updatedItem);
  } catch (error) {
    console.error('Error updating RSS feed item:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: 'Failed to update RSS feed item' });
  }
});

// Delete a specific feed item
router.delete('/items/:id', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const itemId = parseInt(req.params.id);
    if (isNaN(itemId)) {
      return res.status(400).json({ error: 'Invalid item ID' });
    }

    const item = await storage.getRssFeedItem(itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Get the feed to check ownership
    const feed = await storage.getRssFeed(item.feedId);
    if (!feed || feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }


    await storage.deleteRssFeedItem(itemId);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting RSS feed item:', error);
    res.status(500).json({ error: 'Failed to delete RSS feed item' });
  }
});

// Delete the oldest N articles from a feed
router.delete('/:id/oldest/:count', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const feedId = parseInt(req.params.id);
    if (isNaN(feedId)) {
      return res.status(400).json({ error: 'Invalid feed ID' });
    }

    const count = parseInt(req.params.count);
    if (isNaN(count) || count <= 0) {
      return res.status(400).json({ error: 'Invalid count. Must be a positive number.' });
    }

    const feed = await storage.getRssFeed(feedId);
    if (!feed) {
      return res.status(404).json({ error: 'Feed not found' });
    }

    if (feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Get all items for this feed, sorted by published date (oldest first)
    const items = await storage.getRssFeedItems(feedId);
    items.sort((a, b) => a.publishedAt.getTime() - b.publishedAt.getTime());

    // Get the oldest N items
    const itemsToDelete = items.slice(0, count);

    if (itemsToDelete.length === 0) {
      return res.status(200).json({
        message: 'No items to delete',
        deletedCount: 0
      });
    }

    // Delete the items
    for (const item of itemsToDelete) {
      await storage.deleteRssFeedItem(item.id);
    }

    res.status(200).json({
      message: `Successfully deleted ${itemsToDelete.length} oldest items from feed`,
      deletedCount: itemsToDelete.length
    });
  } catch (error) {
    console.error('Error deleting oldest feed items:', error);
    res.status(500).json({ error: 'Failed to delete oldest feed items' });
  }
});

// Get content for a specific feed item
router.get('/items/:id/content', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const itemId = parseInt(req.params.id);
    if (isNaN(itemId)) {
      return res.status(400).json({ error: 'Invalid item ID' });
    }

    const item = await storage.getRssFeedItem(itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Get the feed to check ownership
    const feed = await storage.getRssFeed(item.feedId);
    if (!feed || feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // If we already have scraped content, return it
    if (item.scrapedContent) {
      return res.json({ content: item.scrapedContent });
    }

    // Otherwise, scrape the content
    try {
      const content = await scrapeArticleContent(item.link);

      // Update the item with the scraped content
      await storage.updateRssFeedItem(itemId, { scrapedContent: content });

      res.json({ content });
    } catch (error) {
      console.error('Error scraping article content:', error);
      res.status(500).json({ error: 'Failed to scrape article content' });
    }
  } catch (error) {
    console.error('Error getting feed item content:', error);
    res.status(500).json({ error: 'Failed to get feed item content' });
  }
});

// Generate and download a document with all articles from a feed
router.get('/:id/document', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const feedId = parseInt(req.params.id);
    if (isNaN(feedId)) {
      return res.status(400).json({ error: 'Invalid feed ID' });
    }

    const feed = await storage.getRssFeed(feedId);
    if (!feed) {
      return res.status(404).json({ error: 'Feed not found' });
    }

    // Check if the feed belongs to the current user
    if (feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Get all items for this feed
    const items = await storage.getRssFeedItems(feedId);

    // For each item, ensure we have the scraped content
    for (const item of items) {
      if (!item.scrapedContent) {
        try {
          console.log(`Scraping content for item: ${item.id} - ${item.title}`);
          const content = await scrapeArticleContent(item.link);

          // Update the item with the scraped content
          await storage.updateRssFeedItem(item.id, { scrapedContent: content });

          // Update the item in our local array
          item.scrapedContent = content;
        } catch (error) {
          console.error(`Error scraping content for item ${item.id}:`, error);
          // Continue with the next item even if this one fails
        }
      }
    }

    // Generate the PDF document
    console.log(`Generating PDF document for feed: ${feed.name} with ${items.length} items`);
    console.log(`PDF generation started at: ${new Date().toISOString()}`);

    // Start timer to measure PDF generation time
    const startTime = Date.now();

    // Generate the PDF
    const pdfBuffer = await generateRssFeedPdf(feed.name, items);

    // Calculate generation time
    const endTime = Date.now();
    const generationTimeSeconds = ((endTime - startTime) / 1000).toFixed(2);
    console.log(`PDF generation completed in ${generationTimeSeconds} seconds`);

    // Format today's date for the filename
    const today = new Date();
    const dateStr = format(today, 'yyyy-MM-dd');

    // Clean up the feed name for the filename
    // Replace special characters and spaces with underscores
    const cleanFeedName = feed.name
      .replace(/[|&;$%@"<>()+,]/g, "") // Remove special characters
      .replace(/\s+/g, "_"); // Replace spaces with underscores

    // Create a clean filename
    const filename = `${cleanFeedName}_${dateStr}.pdf`;

    // Set the appropriate headers for a PDF document download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    // Send the document
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Error generating document:', error);
    res.status(500).json({ error: 'Failed to generate document' });
  }
});

// Generate and return formatted text of all articles from a feed
router.get('/:id/text', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const feedId = parseInt(req.params.id);
    if (isNaN(feedId)) {
      return res.status(400).json({ error: 'Invalid feed ID' });
    }

    const feed = await storage.getRssFeed(feedId);
    if (!feed) {
      return res.status(404).json({ error: 'Feed not found' });
    }

    // Check if the feed belongs to the current user
    if (feed.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Get all items for this feed
    const items = await storage.getRssFeedItems(feedId);

    // For each item, ensure we have the scraped content
    for (const item of items) {
      if (!item.scrapedContent) {
        try {
          console.log(`Scraping content for item: ${item.id} - ${item.title}`);
          const content = await scrapeArticleContent(item.link);

          // Update the item with the scraped content
          await storage.updateRssFeedItem(item.id, { scrapedContent: content });

          // Update the item in our local array
          item.scrapedContent = content;
        } catch (error) {
          console.error(`Error scraping content for item ${item.id}:`, error);
          // Continue with the next item even if this one fails
        }
      }
    }

    // Generate the formatted text
    console.log(`Generating formatted text for feed: ${feed.name} with ${items.length} items`);
    console.log(`Text generation started at: ${new Date().toISOString()}`);

    // Start timer to measure text generation time
    const startTime = Date.now();

    // Generate the text
    const formattedText = await generateRssFeedText(feed.name, items);

    // Calculate generation time
    const endTime = Date.now();
    const generationTimeSeconds = ((endTime - startTime) / 1000).toFixed(2);
    console.log(`Text generation completed in ${generationTimeSeconds} seconds`);

    // Send the text as JSON
    res.json({ text: formattedText });
  } catch (error) {
    console.error('Error generating text:', error);
    res.status(500).json({ error: 'Failed to generate text' });
  }
});

// Track active feed refreshes to prevent concurrent refreshes of the same feed
const activeRefreshes = new Map<number, Promise<RssFeedItem[]>>();

// Helper function to fetch and store RSS feed items
async function fetchAndStoreFeedItems(feed: RssFeed): Promise<RssFeedItem[]> {
  // Check if this feed is already being refreshed
  if (activeRefreshes.has(feed.id)) {
    console.log(`Feed ${feed.id} is already being refreshed, waiting for existing refresh to complete`);
    try {
      // Wait for the existing refresh to complete and return its result
      return await activeRefreshes.get(feed.id)!;
    } catch (error) {
      console.error(`Error waiting for existing refresh of feed ${feed.id}:`, error);
      // Continue with a new refresh if the existing one failed
    }
  }

  // Create a promise for this refresh operation
  const refreshPromise = fetchAndStoreFeedItemsInternal(feed);

  // Store the promise in the activeRefreshes map
  activeRefreshes.set(feed.id, refreshPromise);

  try {
    // Wait for the refresh to complete
    const result = await refreshPromise;
    return result;
  } finally {
    // Remove this feed from the activeRefreshes map when done
    activeRefreshes.delete(feed.id);
  }
}

// Internal implementation of feed refresh logic
async function fetchAndStoreFeedItemsInternal(feed: RssFeed): Promise<RssFeedItem[]> {
  console.log(`Fetching items for feed ${feed.id} (${feed.name}) from URL: ${feed.url}`);

  try {
    // Get existing items before making any changes
    const existingItems = await storage.getRssFeedItems(feed.id);
    console.log(`Feed ${feed.id} has ${existingItems.length} existing items before refresh`);

    // Fetch the feed content with retry logic
    let response;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        response = await axios.get(feed.url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/rss+xml, application/atom+xml, application/xml, text/xml, */*',
          },
          timeout: 30000 // 30 second timeout
        });
        break; // Success, exit the retry loop
      } catch (error) {
        retryCount++;
        console.error(`Error fetching feed ${feed.id} (attempt ${retryCount}/${maxRetries}):`, error);
        if (retryCount >= maxRetries) {
          throw error; // Re-throw after max retries
        }
        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
      }
    }

    if (!response) {
      throw new Error(`Failed to fetch feed ${feed.id} after ${maxRetries} attempts`);
    }

    console.log(`Received response from ${feed.url} with status ${response.status}`);

    // Parse the feed content
    const $ = cheerio.load(response.data, { xmlMode: true });
    const newItems: Partial<RssFeedItem>[] = [];

    // Process Atom entries (Google Alerts uses Atom)
    const atomEntries = $('entry');
    console.log(`Found ${atomEntries.length} Atom entries`);

    atomEntries.each((i, entry) => {
      const $entry = $(entry);

      const title = $entry.find('title').html() || '';
      const link = extractLinkFromGoogleAlert($entry.find('link').attr('href') || '');
      const publishedText = $entry.find('published').text() || $entry.find('updated').text();
      const publishedAt = publishedText ? new Date(publishedText) : new Date();
      const content = $entry.find('content').html() || '';

      // Only log the first few entries to avoid log spam
      if (i < 5) {
        console.log(`Processing Atom entry ${i+1}: ${title.substring(0, 50)}${title.length > 50 ? '...' : ''}`);
      } else if (i === 5) {
        console.log(`Processing ${atomEntries.length - 5} more Atom entries...`);
      }

      // Detect financial benefits in the article content
      const detectedBenefits = detectFinancialBenefits(content, title);
      const highestBenefit = getHighestBenefit(detectedBenefits);

      newItems.push({
        feedId: feed.id,
        title,
        link,
        publishedAt,
        content,
        isRead: false,
        financialBenefitAmount: highestBenefit?.amount || 0,
        financialBenefitDescription: highestBenefit?.description || '',
        financialBenefitType: detectedBenefits.length > 0 ? detectedBenefits[0].type : ''
      });
    });

    // Process RSS items
    const rssItems = $('item');
    console.log(`Found ${rssItems.length} RSS items`);

    rssItems.each((i, item) => {
      const $item = $(item);

      const title = $item.find('title').html() || '';
      const link = $item.find('link').text() || '';
      const pubDateText = $item.find('pubDate').text();
      const publishedAt = pubDateText ? new Date(pubDateText) : new Date();
      const content = $item.find('description').html() || '';

      // Only log the first few items to avoid log spam
      if (i < 5) {
        console.log(`Processing RSS item ${i+1}: ${title.substring(0, 50)}${title.length > 50 ? '...' : ''}`);
      } else if (i === 5) {
        console.log(`Processing ${rssItems.length - 5} more RSS items...`);
      }

      // Detect financial benefits in the article content
      const detectedBenefits = detectFinancialBenefits(content, title);
      const highestBenefit = getHighestBenefit(detectedBenefits);

      newItems.push({
        feedId: feed.id,
        title,
        link,
        publishedAt,
        content,
        isRead: false,
        financialBenefitAmount: highestBenefit?.amount || 0,
        financialBenefitDescription: highestBenefit?.description || '',
        financialBenefitType: detectedBenefits.length > 0 ? detectedBenefits[0].type : ''
      });
    });

    // If no new items found, return existing items
    if (newItems.length === 0) {
      console.log(`No items found for feed ${feed.id} (${feed.name}). Keeping existing items.`);
      return existingItems;
    }

    // Use a single transaction for all database operations
    console.log(`Starting transaction for feed ${feed.id} (${feed.name})`);

    // Make sure we don't have any pending transactions
    try {
      storage.db.prepare('ROLLBACK').run();
    } catch (e) {
      // Ignore errors - this is just a precaution
    }

    // Start a fresh transaction
    storage.db.prepare('BEGIN IMMEDIATE TRANSACTION').run();

    try {
      // Store new items in the database
      const storedItems: RssFeedItem[] = [];

      // First, create a map of existing links to avoid duplicates
      const existingLinks = new Map<string, RssFeedItem>();
      for (const item of existingItems) {
        existingLinks.set(item.link, item);
      }

      // Process each new item
      for (const item of newItems) {
        try {
          // Check if this item already exists (by link)
          const existingItem = existingLinks.get(item.link || '');

          if (existingItem) {
            // Item already exists, keep it and its read status
            storedItems.push(existingItem);
          } else {
            // New item, add it to the database
            const storedItem = await storage.addRssFeedItem(item);
            storedItems.push(storedItem);

            // Check if this is an X.com URL, but don't pre-fetch content here
            // We'll do that in a separate background process
            const isXcomUrl = item.link && (item.link.includes('twitter.com') || item.link.includes('x.com'));
            if (isXcomUrl) {
              console.log(`Detected X.com URL for item ${storedItem.id}, will pre-fetch content in background`);
            }
          }
        } catch (itemError) {
          console.error(`Error processing item for feed ${feed.id}:`, itemError);
          // Continue with other items even if one fails
        }
      }

      // Commit the transaction
      storage.db.prepare('COMMIT').run();
      console.log(`Transaction committed for feed ${feed.id} (${feed.name})`);

      // Combine existing and new items, avoiding duplicates
      const allItems = [...existingItems];

      // Add new items that don't already exist in the existing items
      for (const newItem of storedItems) {
        if (!existingItems.some(existingItem => existingItem.id === newItem.id)) {
          allItems.push(newItem);
        }
      }

      console.log(`Kept all existing items for feed ${feed.id}, total items: ${allItems.length}`);

      // Start background process to pre-fetch X.com content
      setTimeout(() => {
        prefetchXcomContent(feed.id, storedItems).catch(error => {
          console.error(`Error in background X.com content pre-fetching for feed ${feed.id}:`, error);
        });
      }, 0);

      // Return all items (existing + new)
      return allItems;
    } catch (error) {
      // If anything goes wrong, roll back the transaction
      console.error(`Error in transaction for feed ${feed.id}:`, error);
      try {
        storage.db.prepare('ROLLBACK').run();
        console.log(`Transaction rolled back for feed ${feed.id}`);
      } catch (rollbackError) {
        console.error(`Error rolling back transaction for feed ${feed.id}:`, rollbackError);
      }

      // Return existing items as fallback
      return existingItems;
    }
  } catch (error) {
    console.error(`Error fetching and storing RSS feed items for feed ${feed.id}:`, error);

    // Return existing items if we fail to fetch new ones
    try {
      const existingItems = await storage.getRssFeedItems(feed.id);
      console.log(`Returning ${existingItems.length} existing items after fetch error for feed ${feed.id}`);
      return existingItems;
    } catch (innerError) {
      console.error(`Error getting existing feed items for feed ${feed.id}:`, innerError);
      return []; // Return empty array as last resort
    }
  }
}

// Background process to pre-fetch X.com content
async function prefetchXcomContent(feedId: number, items: RssFeedItem[]): Promise<void> {
  // Find X.com items
  const xcomItems = items.filter(item =>
    item.link && (item.link.includes('twitter.com') || item.link.includes('x.com'))
  );

  if (xcomItems.length === 0) {
    return; // No X.com items to process
  }

  console.log(`Starting background pre-fetch of ${xcomItems.length} X.com items for feed ${feedId}`);

  // Process X.com items in batches to avoid overwhelming the system
  const batchSize = 3;
  for (let i = 0; i < xcomItems.length; i += batchSize) {
    const batch = xcomItems.slice(i, i + batchSize);

    // Process items in this batch concurrently
    await Promise.all(batch.map(async (item) => {
      try {
        console.log(`Pre-fetching content for X.com item: ${item.id} - ${item.title}`);
        const content = await scrapeArticleContent(item.link);

        // Update the item with the scraped content
        await storage.updateRssFeedItem(item.id, { scrapedContent: content });
        console.log(`Successfully pre-fetched content for X.com item: ${item.id}`);
      } catch (error) {
        console.error(`Error pre-fetching X.com content for item ${item.id}:`, error);
        // Continue with the next item even if this one fails
      }
    }));

    // Add a small delay between batches to avoid overwhelming the system
    if (i + batchSize < xcomItems.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  console.log(`Completed background pre-fetch of X.com content for feed ${feedId}`);
}

// Helper function to delete all items for a feed
async function deleteAllFeedItems(feedId: number): Promise<void> {
  try {
    // Use the storage method to delete all items for this feed
    const stmt = storage.db.prepare(`
      DELETE FROM rss_feed_items
      WHERE feed_id = ?
    `);

    stmt.run(feedId);
    console.log(`Successfully deleted all items for feed ${feedId}`);
  } catch (error) {
    console.error(`Error deleting items for feed ${feedId}:`, error);
    throw error;
  }
}

// Helper function to extract the actual link from Google Alerts link
function extractLinkFromGoogleAlert(googleLink: string): string {
  try {
    // Google Alerts links are in the format:
    // https://www.google.com/url?rct=j&sa=t&url=ACTUAL_URL&ct=ga&cd=...
    const url = new URL(googleLink);
    const actualUrl = url.searchParams.get('url');
    return actualUrl || googleLink;
  } catch (error) {
    console.error('Error extracting link from Google Alert:', error);
    return googleLink;
  }
}

// Handle special sites that need custom scraping logic
async function handleSpecialSites(url: string): Promise<string | null> {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();

    // Handle elshora24.com
    if (hostname.includes('elshora24.com')) {
      return await scrapeElshora24(url);
    }

    // Handle otsnews.com
    if (hostname.includes('otsnews.com')) {
      return await scrapeOtsNews(url);
    }

    // Handle fingerlakes1.com
    if (hostname.includes('fingerlakes1.com')) {
      return await scrapeFingerLakes1(url);
    }

    // Handle elshora24.com
    if (hostname.includes('elshora24.com')) {
      return await scrapeElshora24(url);
    }

    // Handle ebizna24.com
    if (hostname.includes('ebizna24.com') || hostname.includes('ebizna.com')) {
      return await scrapeEbizna24(url);
    }

    // Handle MSN articles
    if (hostname.includes('msn.com')) {
      return await scrapeMsn(url);
    }

    // Handle Yahoo articles
    if (hostname.includes('yahoo.com')) {
      return await scrapeYahoo(url);
    }

    // Handle TheStreet.com articles
    if (hostname.includes('thestreet.com')) {
      return await scrapeTheStreet(url);
    }

    // Handle X.com (Twitter) articles
    if (hostname.includes('x.com') || hostname.includes('twitter.com')) {
      return await scrapeXcom(url);
    }

    // Add more special site handlers as needed

    return null; // No special handling needed
  } catch (error) {
    console.error('Error in handleSpecialSites:', error);
    return null; // Fall back to default scraping
  }
}

// Special scraper for elshora24.com
async function scrapeElshora24(url: string): Promise<string | null> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
      timeout: 10000
    });

    const $ = cheerio.load(response.data);

    // Get the title
    const title = $('.entry-title').text().trim();

    // Get the content
    const content = $('.entry-content').html() || '';

    // Get the date
    const date = $('.entry-date').text().trim();

    // Get the author
    const author = $('.author-name').text().trim();

    // Combine everything
    let fullContent = '';
    if (title) fullContent += `<h1>${title}</h1>`;
    if (date) fullContent += `<p class="article-date">${date}</p>`;
    if (author) fullContent += `<p class="article-author">By ${author}</p>`;
    fullContent += content;

    return fullContent;
  } catch (error) {
    console.error('Error scraping elshora24:', error);
    return null; // Fall back to default scraping
  }
}

// Special scraper for otsnews.com
async function scrapeOtsNews(url: string): Promise<string | null> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
      timeout: 10000
    });

    const $ = cheerio.load(response.data);

    // Get the title
    const title = $('h1.entry-title').text().trim();

    // Get the content
    const content = $('.entry-content').html() || '';

    // Get the date
    const date = $('.entry-date').text().trim();

    // Combine everything
    let fullContent = '';
    if (title) fullContent += `<h1>${title}</h1>`;
    if (date) fullContent += `<p class="article-date">${date}</p>`;
    fullContent += content;

    return fullContent;
  } catch (error) {
    console.error('Error scraping otsnews:', error);
    return null; // Fall back to default scraping
  }
}

// Special scraper for fingerlakes1.com
async function scrapeFingerLakes1(url: string): Promise<string | null> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
      timeout: 10000
    });

    const $ = cheerio.load(response.data);

    // Get the title
    const title = $('h1.entry-title').text().trim() || $('h1').first().text().trim();

    // Get the content - try different selectors
    let content = $('.entry-content').html() ||
                 $('.post-content').html() ||
                 $('.article-content').html() ||
                 $('article').html() || '';

    // If no content found, try to get all paragraphs
    if (!content) {
      const paragraphs = $('p').map((i, el) => $(el).html()).get();
      content = paragraphs.map(p => `<p>${p}</p>`).join('');
    }

    // Get the date
    const date = $('.entry-date').text().trim() || $('.post-date').text().trim();

    // Get the author
    const author = $('.author').text().trim() || $('.byline').text().trim();

    // Combine everything
    let fullContent = '';
    if (title) fullContent += `<h1>${title}</h1>`;
    if (date) fullContent += `<p class="article-date">${date}</p>`;
    if (author) fullContent += `<p class="article-author">${author}</p>`;
    fullContent += content;

    return fullContent;
  } catch (error) {
    console.error('Error scraping fingerlakes1:', error);
    return null; // Fall back to default scraping
  }
}

// Special scraper for ebizna24.com
async function scrapeEbizna24(url: string): Promise<string | null> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
      timeout: 10000
    });

    const $ = cheerio.load(response.data);

    // Get the title
    const title = $('h1.entry-title').text().trim() || $('h1').first().text().trim() || $('title').text().trim();

    // Get the content - try different selectors
    let content = $('.entry-content').html() ||
                 $('.post-content').html() ||
                 $('.article-content').html() ||
                 $('.article-body').html() ||
                 $('article').html() || '';

    // If no content found, try to get all paragraphs
    if (!content) {
      // Get all paragraphs
      const paragraphs = $('p').map((i, el) => $(el).html()).get();

      // Filter out very short paragraphs (likely not part of the main content)
      const filteredParagraphs = paragraphs.filter(p => {
        const text = $(p).text();
        return text.length > 30; // Paragraphs with more than 30 characters are likely content
      });

      content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');
    }

    // Get the date
    const date = $('.entry-date').text().trim() ||
                $('.post-date').text().trim() ||
                $('time').first().text().trim();

    // Get the author
    const author = $('.author').text().trim() ||
                  $('.byline').text().trim() ||
                  $('.author-name').text().trim();

    // Combine everything
    let fullContent = '';
    if (title) fullContent += `<h1>${title}</h1>`;
    if (date) fullContent += `<p class="article-date">${date}</p>`;
    if (author) fullContent += `<p class="article-author">By ${author}</p>`;
    fullContent += content;

    // If we still don't have much content, try a more aggressive approach
    if (fullContent.length < 500) {
      // Get all text from the body
      const bodyText = $('body').text();

      // Split by newlines and filter out short lines
      const lines = bodyText.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 50)
        .map(line => `<p>${line}</p>`)
        .join('');

      if (lines.length > fullContent.length) {
        fullContent = `<h1>${title}</h1>`;
        if (date) fullContent += `<p class="article-date">${date}</p>`;
        if (author) fullContent += `<p class="article-author">By ${author}</p>`;
        fullContent += lines;
      }
    }

    return fullContent;
  } catch (error) {
    console.error('Error scraping ebizna24:', error);
    return null; // Fall back to default scraping
  }
}

// Special scraper for MSN articles
async function scrapeMsn(url: string): Promise<string | null> {
  try {
    console.log('Scraping MSN article:', url);

    // Extract the article ID from the URL
    const articleIdMatch = url.match(/\/ar-([A-Za-z0-9]+)/);
    if (!articleIdMatch || !articleIdMatch[1]) {
      console.log('Could not extract article ID from URL');
      return null;
    }

    const articleId = articleIdMatch[1];
    console.log('Article ID:', articleId);

    // Try different URL formats for MSN
    const urlVariations = [
      url, // Original URL
      `https://www.msn.com/en-us/news/other/ar-${articleId}`, // News format
      `https://www.msn.com/en-us/money/other/ar-${articleId}`, // Money format
      `https://www.msn.com/en-us/money/retirement/ar-${articleId}`, // Retirement format
      `https://www.msn.com/en-us/lifestyle/other/ar-${articleId}`, // Lifestyle format
    ];

    for (const currentUrl of urlVariations) {
      try {
        console.log(`Trying MSN URL variation: ${currentUrl}`);

        const response = await axios.get(currentUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
          timeout: 15000
        });

        const $ = cheerio.load(response.data);

        // Get the title
        const title = $('h1').first().text().trim() || $('title').text().trim();
        console.log('Found title:', title);

        if (!title || title === 'MSN') {
          console.log('No valid title found, skipping this URL variation');
          continue;
        }

        // Get the author info
        const authorInfo = $('.authorinfo').text().trim() ||
                          $('.byline').text().trim() ||
                          $('[data-author-name]').text().trim() ||
                          $('[itemprop="author"]').text().trim();

        // Look for article content
        let content = '';

        // MSN-specific content selectors
        const contentSelectors = [
          '[data-testid="article-body-content"]',
          '[data-testid="articleBodyContent"]',
          '.articlebody',
          '.article-body',
          '.article-content',
          '.content-article',
          '.articlecontent',
          '#content-main',
          '.main-content',
          '.article-page-content',
          '.page-content',
          '.article-text',
          '.article',
          'article'
        ];

        for (const selector of contentSelectors) {
          if ($(selector).length) {
            content = $(selector).html() || '';
            console.log(`Found content using selector: ${selector}`);
            break;
          }
        }

        // If no content found with specific selectors, try paragraphs
        if (!content || content.length < 100) {
          console.log('No content found with specific selectors, trying paragraphs');
          const paragraphs = $('p').map((i, el) => $(el).html()).get();
          const filteredParagraphs = paragraphs.filter(p => {
            const text = $(p).text();
            return text.length > 30 && !text.includes('ADVERTISEMENT');
          });

          content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');
          console.log(`Found ${filteredParagraphs.length} paragraphs`);
        }

        // If we found content, return it
        if (content && content.length > 100) {
          let fullContent = '';
          if (title) fullContent += `<h1>${title}</h1>`;
          if (authorInfo) fullContent += `<p class="article-author">${authorInfo}</p>`;
          fullContent += content;

          console.log(`Found content with length: ${fullContent.length}`);
          return fullContent;
        }
      } catch (error) {
        console.error(`Error with URL variation ${currentUrl}:`, error);
      }
    }

    // Try to get the article from archive.is (archive.today/archive.ph)
    // This is a widely recommended approach for bypassing content restrictions
    try {
      // First, check if the article is already archived
      const archiveUrl = `https://archive.today/newest/${url}`;
      console.log(`Checking if article is archived: ${archiveUrl}`);

      const archiveResponse = await axios.get(archiveUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 30000, // Longer timeout for archive requests
        maxRedirects: 5
      });

      if (archiveResponse.data) {
        console.log('Found archived version of the article');
        const $ = cheerio.load(archiveResponse.data);

        // Remove archive.is header and footer
        $('#HEADER').remove();
        $('#FOOTER').remove();
        $('.THUMBS-BLOCK').remove();

        // Get the title
        const title = $('h1').first().text().trim() || $('title').text().trim();
        console.log('Found title from archive:', title);

        // Get the author and date info
        let authorInfo = '';
        let dateInfo = '';

        // MSN often has author and date in a byline
        const byline = $('.byline').text().trim() || $('.authorinfo').text().trim();
        if (byline) {
          console.log('Found byline:', byline);
          // Try to extract author and date from byline
          const bylineParts = byline.split('•').map(part => part.trim());
          if (bylineParts.length >= 1) {
            authorInfo = bylineParts[0];
          }
          if (bylineParts.length >= 2) {
            dateInfo = bylineParts[1];
          }
        }

        // If we couldn't find author/date in byline, try other selectors
        if (!authorInfo) {
          authorInfo = $('.author').text().trim() ||
                      $('[data-author]').attr('data-author') ||
                      $('[itemprop="author"]').text().trim();
        }

        if (!dateInfo) {
          dateInfo = $('time').first().text().trim() ||
                    $('.date').text().trim() ||
                    $('[itemprop="datePublished"]').text().trim();
        }

        // Try to extract the article content
        let content = '';

        // Try MSN-specific selectors first
        const msnSelectors = [
          '[data-testid="article-body-content"]',
          '[data-testid="articleBodyContent"]',
          '[data-testid="article-body"]',
          '.article-content',
          '.article-body',
          '[data-article-body="true"]',
          '[data-iscontent="true"]',
          '.content-wrapper',
          '#content-main',
          '.articlecontent',
          '.articlebody'
        ];

        for (const selector of msnSelectors) {
          if ($(selector).length) {
            content = $(selector).html() || '';
            console.log(`Found content using selector: ${selector}`);
            break;
          }
        }

        // If no content found with MSN selectors, try generic article selectors
        if (!content) {
          const genericSelectors = [
            'article',
            '.article',
            '.post-content',
            '.entry-content',
            'main'
          ];

          for (const selector of genericSelectors) {
            if ($(selector).length) {
              content = $(selector).html() || '';
              console.log(`Found content using generic selector: ${selector}`);
              break;
            }
          }
        }

        // If still no content, get all paragraphs
        if (!content) {
          console.log('No content found with selectors, trying paragraphs');
          // Get all paragraphs
          const paragraphs = $('p').map((i, el) => $(el).html()).get();

          // Filter out very short paragraphs (likely not part of the main content)
          const filteredParagraphs = paragraphs.filter(p => {
            const text = $(p).text();
            return text.length > 30; // Paragraphs with more than 30 characters are likely content
          });

          content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');
          console.log(`Found ${filteredParagraphs.length} paragraphs`);
        }

        // Combine everything
        let fullContent = '';
        if (title) fullContent += `<h1>${title}</h1>`;
        if (authorInfo) fullContent += `<p class="article-author">${authorInfo}</p>`;
        if (dateInfo) fullContent += `<p class="article-date">${dateInfo}</p>`;

        // Add the main content
        fullContent += content;

        // Add a note about the source
        fullContent += `<p class="source-note"><small>Content retrieved from archive.ph</small></p>`;

        // If we have enough content, return it
        if (fullContent.length > 500) {
          console.log(`Found content with length: ${fullContent.length}`);
          return fullContent;
        }
      }
    } catch (error) {
      console.error('Error using archive.ph:', error);
      // Continue with other methods if archive.ph fails
    }

    // Try using 12ft.io as an alternative
    try {
      const twelveFtUrl = `https://12ft.io/proxy?q=${encodeURIComponent(url)}`;
      console.log(`Trying 12ft.io: ${twelveFtUrl}`);

      const twelveFtResponse = await axios.get(twelveFtUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 30000
      });

      if (twelveFtResponse.data) {
        const $ = cheerio.load(twelveFtResponse.data);

        // Get the title
        const title = $('h1').first().text().trim() || $('title').text().trim();
        console.log('Found title from 12ft.io:', title);

        if (!title) {
          console.log('No title found from 12ft.io');
          // Continue to next method
        } else {
          // Extract content
          let content = '';
          const paragraphs = $('p').map((i, el) => $(el).html()).get();
          const filteredParagraphs = paragraphs.filter(p => {
            const text = $(p).text();
            return text.length > 30;
          });

          content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');

          let fullContent = '';
          if (title) fullContent += `<h1>${title}</h1>`;
          fullContent += content;
          fullContent += `<p class="source-note"><small>Content retrieved from 12ft.io</small></p>`;

          if (fullContent.length > 500) {
            console.log(`Found content with length: ${fullContent.length}`);
            return fullContent;
          }
        }
      }
    } catch (error) {
      console.error('Error using 12ft.io:', error);
      // Continue with other methods if 12ft.io fails
    }

    // Try using a JavaScript-disabled approach
    // This simulates disabling JavaScript in the browser, which is another recommended method
    try {
      console.log('Trying JavaScript-disabled approach');

      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          // Add headers that might help bypass restrictions
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'DNT': '1',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-User': '?1',
        },
        timeout: 20000
      });

      if (response.data) {
        const $ = cheerio.load(response.data);

        // Remove scripts to simulate JavaScript being disabled
        $('script').remove();

        // Get the title
        const title = $('h1').first().text().trim() || $('title').text().trim();
        console.log('Found title from JS-disabled approach:', title);

        if (title) {
          // Extract content
          let content = '';
          const paragraphs = $('p').map((i, el) => $(el).html()).get();
          const filteredParagraphs = paragraphs.filter(p => {
            const text = $(p).text();
            return text.length > 30;
          });

          content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');

          let fullContent = '';
          if (title) fullContent += `<h1>${title}</h1>`;
          fullContent += content;

          if (fullContent.length > 500) {
            console.log(`Found content with length: ${fullContent.length}`);
            return fullContent;
          }
        }
      }
    } catch (error) {
      console.error('Error using JavaScript-disabled approach:', error);
    }

    // If all methods fail, create a sample article with instructions
    console.log('All methods failed, creating sample article with instructions');
    return createSampleMsnArticle(articleId, url);
  } catch (error) {
    console.error('Error scraping MSN:', error);
    return null; // Fall back to default scraping
  }
}

// Helper function to create a sample article based on the article ID
async function createSampleMsnArticle(articleId: string, url: string): Promise<string> {
  // Try to extract some information from the URL
  const urlParts = url.split('/');
  const category = urlParts.find(part => ['money', 'news', 'finance', 'retirement'].includes(part.toLowerCase())) || 'news';

  // Try to extract a title from the URL
  const lastPart = urlParts[urlParts.length - 1];
  const titleFromUrl = lastPart
    .replace(`ar-${articleId}`, '')
    .replace(/[^a-zA-Z0-9\s]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Create a title based on the URL or use a generic one
  const title = titleFromUrl || `Article ${articleId}`;

  // Create a sample article with instructions on how to view the content
  return `
    <h1>${title}</h1>
    <p class="article-author">MSN ${category.charAt(0).toUpperCase() + category.slice(1)} • Today</p>

    <p>We're sorry, but we couldn't retrieve the full content of this article due to MSN's content protection measures.</p>

    <p>To view this article, you can try one of these methods:</p>

    <ol>
      <li>Click the "Open Original" button above to view the article on MSN</li>
      <li>Visit <a href="https://archive.ph/${url}" target="_blank">archive.ph</a> and paste the article URL</li>
      <li>Visit <a href="https://12ft.io/" target="_blank">12ft.io</a> and paste the article URL</li>
      <li>Disable JavaScript in your browser and then visit the article URL</li>
    </ol>

    <p>Article ID: ${articleId}</p>
    <p>URL: ${url}</p>
  `;
}

// Special scraper for TheStreet.com articles
async function scrapeTheStreet(url: string): Promise<string | null> {
  try {
    console.log('Scraping TheStreet.com article:', url);

    // Try using 12ft.io first as TheStreet.com has a paywall
    try {
      const twelveFtUrl = `https://12ft.io/proxy?q=${encodeURIComponent(url)}`;
      console.log(`Trying 12ft.io for TheStreet: ${twelveFtUrl}`);

      const response = await axios.get(twelveFtUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 20000
      });

      const $ = cheerio.load(response.data);

      // Get the title
      const title = $('h1').first().text().trim() || $('title').text().trim();
      console.log('Found title from 12ft.io:', title);

      if (title) {
        // Get the content
        let content = '';

        // Try TheStreet-specific selectors
        const streetSelectors = [
          '.article-content',
          '.article__body',
          '.article-body',
          '.article__content',
          '.content-article',
          '.content__body',
          '.mainContent'
        ];

        for (const selector of streetSelectors) {
          if ($(selector).length) {
            content = $(selector).html() || '';
            console.log(`Found content using selector: ${selector}`);
            break;
          }
        }

        // If no content found with specific selectors, try paragraphs
        if (!content) {
          const paragraphs = $('p').map((i, el) => $(el).html()).get();
          const filteredParagraphs = paragraphs.filter(p => {
            const text = $(p).text();
            return text.length > 30;
          });

          content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');
          console.log(`Found ${filteredParagraphs.length} paragraphs`);
        }

        // Get author info
        const authorInfo = $('.author').text().trim() ||
                          $('.byline').text().trim() ||
                          $('[rel="author"]').text().trim();

        // Get date info
        const dateInfo = $('time').first().text().trim() ||
                        $('.date').text().trim() ||
                        $('.published-date').text().trim();

        // Combine everything
        let fullContent = '';
        if (title) fullContent += `<h1>${title}</h1>`;
        if (authorInfo) fullContent += `<p class="article-author">${authorInfo}</p>`;
        if (dateInfo) fullContent += `<p class="article-date">${dateInfo}</p>`;
        fullContent += content;
        fullContent += `<p class="source-note"><small>Content retrieved from 12ft.io</small></p>`;

        if (fullContent.length > 500) {
          console.log(`Found content with length: ${fullContent.length}`);
          return fullContent;
        }
      }
    } catch (twelveFtError) {
      console.error('Error using 12ft.io for TheStreet:', twelveFtError);
    }

    // If 12ft.io fails, try direct access
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
        timeout: 15000
      });

      const $ = cheerio.load(response.data);

      // Get the title
      const title = $('h1').first().text().trim() || $('title').text().trim();

      // Get the content
      let content = '';

      // Try TheStreet-specific selectors
      const streetSelectors = [
        '.article-content',
        '.article__body',
        '.article-body',
        '.article__content',
        '.content-article',
        '.content__body',
        '.mainContent'
      ];

      for (const selector of streetSelectors) {
        if ($(selector).length) {
          content = $(selector).html() || '';
          break;
        }
      }

      // If no content found with specific selectors, try paragraphs
      if (!content) {
        const paragraphs = $('p').map((i, el) => $(el).html()).get();
        const filteredParagraphs = paragraphs.filter(p => {
          const text = $(p).text();
          return text.length > 30;
        });

        content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');
      }

      // Get author info
      const authorInfo = $('.author').text().trim() ||
                        $('.byline').text().trim() ||
                        $('[rel="author"]').text().trim();

      // Get date info
      const dateInfo = $('time').first().text().trim() ||
                      $('.date').text().trim() ||
                      $('.published-date').text().trim();

      // Combine everything
      let fullContent = '';
      if (title) fullContent += `<h1>${title}</h1>`;
      if (authorInfo) fullContent += `<p class="article-author">${authorInfo}</p>`;
      if (dateInfo) fullContent += `<p class="article-date">${dateInfo}</p>`;
      fullContent += content;

      return fullContent;
    } catch (directError) {
      console.error('Error directly accessing TheStreet:', directError);
    }

    // If all methods fail, try archive.today
    try {
      const archiveUrl = `https://archive.today/newest/${url}`;
      console.log(`Trying archive.today for TheStreet: ${archiveUrl}`);

      const archiveResponse = await axios.get(archiveUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 20000,
        maxRedirects: 5
      });

      if (archiveResponse.data) {
        const $ = cheerio.load(archiveResponse.data);

        // Get the title
        const title = $('h1').first().text().trim() || $('title').text().trim();

        // Get the content
        let content = '';
        const paragraphs = $('p').map((i, el) => $(el).html()).get();
        content = paragraphs.map(p => `<p>${p}</p>`).join('');

        if (content && content.length > 100) {
          let fullContent = `<h1>${title}</h1>`;
          fullContent += content;
          fullContent += `<p class="source-note"><small>Content retrieved from archive.today</small></p>`;

          console.log('Successfully extracted content from archive.today');
          return fullContent;
        }
      }
    } catch (archiveError) {
      console.error('Error using archive.today for TheStreet:', archiveError);
    }

    return null; // Fall back to default scraping
  } catch (error) {
    console.error('Error scraping TheStreet:', error);
    return null; // Fall back to default scraping
  }
}

// Special scraper for Yahoo articles
async function scrapeYahoo(url: string): Promise<string | null> {
  try {
    console.log('Using specialized Yahoo scraper for:', url);

    // Try to use Readability directly for Yahoo articles
    try {
      // Add a random query parameter to bypass caching
      const bypassCacheUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;

      const response = await axios.get(bypassCacheUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          // Limit the Accept-Encoding to avoid header overflow issues
          'Accept-Encoding': 'gzip',
        },
        timeout: 15000, // Yahoo can be slow to load
        maxContentLength: 5 * 1024 * 1024, // 5MB max content length
        maxRedirects: 3, // Limit redirects
        decompress: true, // Handle gzip automatically
        insecureHTTPParser: true, // More lenient HTTP parsing
      });

      // Create a DOM from the HTML content
      const dom = new JSDOM(response.data, { url });

      // Create a new Readability object
      const reader = new Readability(dom.window.document);

      // Parse the content
      const article = reader.parse();

      if (article) {
        console.log('Successfully parsed Yahoo article with Readability');

        // Construct the HTML content
        let content = '';

        // Add the title
        if (article.title) {
          content += `<h1>${article.title}</h1>`;
        }

        // Add the byline (author) if available
        if (article.byline) {
          content += `<p class="article-author">${article.byline}</p>`;
        }

        // Add the article content
        content += article.content;

        // Add a source note
        content += `<p class="source-note"><small>Content extracted from Yahoo Finance using Readability</small></p>`;

        return content;
      }
    } catch (readabilityError) {
      console.error('Error using Readability for Yahoo article:', readabilityError);
      console.log('Falling back to Yahoo-specific scraper');
    }

    // If Readability failed, try Yahoo-specific scraping
    // Add a random query parameter to bypass caching
    const bypassCacheUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;

    const response = await axios.get(bypassCacheUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        // Limit the Accept-Encoding to avoid header overflow issues
        'Accept-Encoding': 'gzip',
      },
      timeout: 15000, // Yahoo can be slow to load
      maxContentLength: 5 * 1024 * 1024, // 5MB max content length
      maxRedirects: 3, // Limit redirects
      decompress: true, // Handle gzip automatically
      insecureHTTPParser: true, // More lenient HTTP parsing
    });

    const $ = cheerio.load(response.data);

    // Get the title - Yahoo usually has the title in a specific format
    const title = $('h1').first().text().trim() || $('title').text().trim();

    // Get the author and date info
    let authorInfo = '';
    let dateInfo = '';

    // Yahoo often has author info in specific elements
    authorInfo = $('.caas-author-byline-collapse').text().trim() ||
                $('.author-name').text().trim() ||
                $('[data-test-locator="author"]').text().trim();

    // Yahoo often has date info in specific elements
    dateInfo = $('.caas-attr-time-style').text().trim() ||
              $('time').first().text().trim() ||
              $('.date').text().trim();

    // Get the content - Yahoo articles are usually in a specific container
    let content = '';

    // Try Yahoo-specific selectors first
    const yahooSelectors = [
      '.caas-body',
      '.article-body',
      '.canvas-body',
      '.body-wrapper',
      '.content-canvas',
      '.article-content',
      '.main-content'
    ];

    for (const selector of yahooSelectors) {
      if ($(selector).length) {
        content = $(selector).html() || '';
        break;
      }
    }

    // If no content found with Yahoo selectors, try generic article selectors
    if (!content) {
      const genericSelectors = [
        'article',
        '.article',
        '.post-content',
        '.entry-content',
        'main'
      ];

      for (const selector of genericSelectors) {
        if ($(selector).length) {
          content = $(selector).html() || '';
          break;
        }
      }
    }

    // If still no content, get all paragraphs
    if (!content) {
      // Get all paragraphs
      const paragraphs = $('p').map((i, el) => $(el).html()).get();

      // Filter out very short paragraphs (likely not part of the main content)
      const filteredParagraphs = paragraphs.filter(p => {
        const text = $(p).text();
        return text.length > 30; // Paragraphs with more than 30 characters are likely content
      });

      content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');
    }

    // Yahoo often has market data for financial articles
    const marketData = $('.fin_charts').html() || $('.fin-streamer').html();

    // Combine everything
    let fullContent = '';
    if (title) fullContent += `<h1>${title}</h1>`;
    if (authorInfo) fullContent += `<p class="article-author">${authorInfo}</p>`;
    if (dateInfo) fullContent += `<p class="article-date">${dateInfo}</p>`;
    if (marketData) fullContent += `<div class="market-data">${marketData}</div>`;

    // Add the main content
    fullContent += content;

    // If we still don't have much content, try a more aggressive approach
    if (fullContent.length < 500) {
      // Look for any divs with substantial text
      const divTexts = $('div').map((i, el) => {
        const text = $(el).text().trim();
        if (text.length > 100 && !$(el).find('script').length) {
          return `<p>${text}</p>`;
        }
        return null;
      }).get().filter(Boolean);

      if (divTexts.length > 0) {
        const divContent = divTexts.join('');
        if (divContent.length > content.length) {
          fullContent = `<h1>${title}</h1>`;
          if (authorInfo) fullContent += `<p class="article-author">${authorInfo}</p>`;
          if (dateInfo) fullContent += `<p class="article-date">${dateInfo}</p>`;
          if (marketData) fullContent += `<div class="market-data">${marketData}</div>`;
          fullContent += divContent;
        }
      }
    }

    return fullContent;
  } catch (error) {
    console.error('Error scraping Yahoo:', error);
    return null; // Fall back to default scraping
  }
}

// Special scraper for X.com (Twitter)
export async function scrapeXcom(url: string): Promise<string | null> {
  try {
    console.log('Using specialized X.com (Twitter) scraper for:', url);

    // Extract the tweet ID from the URL
    const tweetIdMatch = url.match(/\/status\/(\d+)/);
    if (!tweetIdMatch || !tweetIdMatch[1]) {
      console.log('Could not extract tweet ID from URL');
      return null;
    }

    const tweetId = tweetIdMatch[1];
    console.log('Tweet ID:', tweetId);

    // Add a random delay to prevent rate limiting (0-500ms)
    await new Promise(resolve => setTimeout(resolve, Math.floor(Math.random() * 500)));

    // Method 0: Try using Puppeteer (most reliable for modern X.com)
    try {
      console.log('Attempting to scrape tweet using Puppeteer');

      // Import puppeteer dynamically to avoid issues with ESM/CJS
      const puppeteer = await import('puppeteer');

      // Launch a headless browser with improved settings
      const browser = await puppeteer.default.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-web-security',
          '--disable-features=IsolateOrigins,site-per-process'
        ]
      });

      try {
        const page = await browser.newPage();

        // Set a mobile user agent
        await page.setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1');

        // Set viewport to mobile size
        await page.setViewport({
          width: 375,
          height: 812,
          isMobile: true
        });

        // Set extra HTTP headers
        await page.setExtraHTTPHeaders({
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        });

        // Enable JavaScript
        await page.setJavaScriptEnabled(true);

        console.log('Navigating to URL:', url);

        // Navigate with improved settings
        await page.goto(url, {
          waitUntil: 'networkidle2',
          timeout: 45000
        });

        // Wait longer for content to load
        console.log('Waiting for tweet content to load...');

        // Try multiple selectors for tweet content
        const selectors = [
          '[data-testid="tweetText"]',
          '.tweet-text',
          '.tweet-content',
          'article'
        ];

        let tweetContent = null;
        let authorName = '';
        let authorUsername = '';

        // Try each selector
        for (const selector of selectors) {
          try {
            console.log(`Trying selector: ${selector}`);
            await page.waitForSelector(selector, { timeout: 5000 });

            // If we found this selector, extract the content
            tweetContent = await page.evaluate((sel) => {
              const element = document.querySelector(sel);
              return element ? element.innerHTML : null;
            }, selector);

            if (tweetContent && tweetContent.length > 10) {
              console.log(`Found content using selector: ${selector}`);
              break;
            }
          } catch (selectorError) {
            console.log(`Selector ${selector} not found`);
          }
        }

        // Try to get author info regardless of which selector worked
        try {
          authorName = await page.evaluate(() => {
            const nameElement = document.querySelector('[data-testid="User-Name"]') ||
                               document.querySelector('.fullname') ||
                               document.querySelector('.tweet-author-name');
            return nameElement ? nameElement.textContent.trim() : '';
          });
        } catch (nameError) {
          console.log('Could not extract author name');
        }

        try {
          authorUsername = await page.evaluate(() => {
            const usernameElement = document.querySelector('[data-testid="User-Username"]') ||
                                   document.querySelector('.username') ||
                                   document.querySelector('.tweet-author-username');
            return usernameElement ? usernameElement.textContent.trim() : '';
          });
        } catch (usernameError) {
          console.log('Could not extract author username');
        }

        // Take a screenshot for debugging
        await page.screenshot({ path: '/tmp/tweet-screenshot.png' });
        console.log('Screenshot saved to /tmp/tweet-screenshot.png');

        if (tweetContent && tweetContent.length > 10) {
          console.log('Successfully retrieved tweet content using Puppeteer');
          console.log('Content length:', tweetContent.length);
          console.log('Content preview:', tweetContent.substring(0, 100));

          // Construct the content
          let fullContent = '';
          if (authorName) fullContent += `<h2>${authorName}</h2>`;
          if (authorUsername) fullContent += `<p class="tweet-username">${authorUsername}</p>`;
          fullContent += `<div class="tweet-content">${tweetContent}</div>`;
          fullContent += `<p class="source-note"><small>Content retrieved from X.com (Twitter) via Puppeteer</small></p>`;
          fullContent += `<p><a href="${url}" target="_blank">View original tweet</a></p>`;

          return fullContent;
        } else {
          console.log('Puppeteer content is too short or empty, trying other methods');
        }
      } finally {
        await browser.close();
        console.log('Browser closed');
      }
    } catch (puppeteerError) {
      console.error('Error using Puppeteer for X.com:', puppeteerError);
    }

    // Method 1: Try using twscrape (reliable but may be rate-limited)
    try {
      console.log('Attempting to scrape tweet using twscrape');
      const tweetData = await scrapeTweet(tweetId);

      if (tweetData) {
        console.log('Successfully retrieved tweet data using twscrape');
        const content = generateTweetHtml(tweetData, url);

        // Verify the content is valid
        if (content && content.length > 100) {
          console.log('Generated valid HTML content from tweet data');
          return content;
        } else {
          console.log('Generated HTML content is too short or empty, trying other methods');
        }
      } else {
        console.log('Failed to retrieve tweet data using twscrape');
      }
    } catch (twscrapeError) {
      console.error('Error using twscrape for X.com:', twscrapeError);
    }

    // Add a small delay between attempts
    await new Promise(resolve => setTimeout(resolve, 300));

    // Method 2: Try using 12ft.io
    try {
      console.log('Attempting to scrape tweet using 12ft.io');
      const content = await scrapeTweetWith12ft(url);

      if (content && content.length > 100) {
        console.log('Successfully retrieved tweet content using 12ft.io');
        return content;
      } else {
        console.log('12ft.io content is too short or empty, trying other methods');
      }
    } catch (twelveFtError) {
      console.error('Error using 12ft.io for X.com:', twelveFtError);
    }

    // Add a small delay between attempts
    await new Promise(resolve => setTimeout(resolve, 300));

    // Method 3: Try using archive.today as a fallback
    try {
      const archiveUrl = `https://archive.today/newest/${url}`;
      console.log(`Trying archive.today for X.com: ${archiveUrl}`);

      const archiveResponse = await axios.get(archiveUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 30000,
        maxRedirects: 5
      });

      if (archiveResponse.data) {
        const $ = cheerio.load(archiveResponse.data);

        // Get the tweet content
        const tweetContent = $('.tweet-content').html() ||
                            $('.tweet-text').html() ||
                            $('[data-testid="tweetText"]').html();

        if (tweetContent && tweetContent.length > 10) {
          console.log('Found tweet content from archive.today');

          // Get the author info
          const authorName = $('.tweet-author-name').text().trim() ||
                            $('.fullname').text().trim() ||
                            $('[data-testid="User-Name"]').text().trim();

          const authorUsername = $('.tweet-author-username').text().trim() ||
                                $('.username').text().trim() ||
                                $('[data-testid="User-Username"]').text().trim();

          // Get the tweet date
          const tweetDate = $('.tweet-date').text().trim() ||
                           $('.timestamp').text().trim() ||
                           $('[data-testid="tweet-time"]').text().trim();

          // Construct the content
          let fullContent = '';
          if (authorName) fullContent += `<h2>${authorName}</h2>`;
          if (authorUsername) fullContent += `<p class="tweet-username">${authorUsername}</p>`;
          if (tweetDate) fullContent += `<p class="tweet-date">${tweetDate}</p>`;

          fullContent += `<div class="tweet-content">${tweetContent}</div>`;
          fullContent += `<p class="source-note"><small>Content retrieved from X.com (Twitter) via archive.today</small></p>`;

          return fullContent;
        }
      }
    } catch (archiveError) {
      console.error('Error using archive.today for X.com:', archiveError);
    }

    // Add a small delay between attempts
    await new Promise(resolve => setTimeout(resolve, 300));

    // Method 4: Try direct access with a mobile user agent as a last resort
    try {
      console.log('Trying direct access with mobile user agent for X.com');

      // Add cache-busting parameter
      const cacheBustUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;

      const response = await axios.get(cacheBustUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
        timeout: 15000
      });

      const $ = cheerio.load(response.data);

      // Get the tweet content
      const tweetContent = $('.tweet-content').html() ||
                          $('.tweet-text').html() ||
                          $('[data-testid="tweetText"]').html();

      if (tweetContent && tweetContent.length > 10) {
        console.log('Found tweet content from direct access');

        // Get the author info
        const authorName = $('.tweet-author-name').text().trim() ||
                          $('.fullname').text().trim() ||
                          $('[data-testid="User-Name"]').text().trim();

        const authorUsername = $('.tweet-author-username').text().trim() ||
                              $('.username').text().trim() ||
                              $('[data-testid="User-Username"]').text().trim();

        // Get the tweet date
        const tweetDate = $('.tweet-date').text().trim() ||
                         $('.timestamp').text().trim() ||
                         $('[data-testid="tweet-time"]').text().trim();

        // Construct the content
        let fullContent = '';
        if (authorName) fullContent += `<h2>${authorName}</h2>`;
        if (authorUsername) fullContent += `<p class="tweet-username">${authorUsername}</p>`;
        if (tweetDate) fullContent += `<p class="tweet-date">${tweetDate}</p>`;

        fullContent += `<div class="tweet-content">${tweetContent}</div>`;
        fullContent += `<p class="source-note"><small>Content retrieved from X.com (Twitter)</small></p>`;

        return fullContent;
      }
    } catch (directError) {
      console.error('Error with direct access for X.com:', directError);
    }

    // Method 5: Use embedded tweet as a fallback
    try {
      console.log('Creating embedded tweet fallback');

      // Create an embedded tweet using Twitter's official widget
      const embedContent = `
        <div class="tweet-embed">
          <h2>Tweet from X.com</h2>
          <p>The tweet content could not be directly retrieved. Here's a link to the original tweet:</p>
          <p><a href="${url}" target="_blank" class="tweet-link">View original tweet on X.com</a></p>

          <div class="tweet-preview">
            <blockquote class="twitter-tweet" data-dnt="true">
              <a href="${url}"></a>
            </blockquote>
            <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>
          </div>

          <p class="source-note"><small>Tweet embedded from X.com (Twitter)</small></p>

          <div class="tweet-refresh-note">
            <p>If you're seeing this message, you can try refreshing the content using the refresh button above.</p>
          </div>
        </div>
      `;

      return embedContent;
    } catch (embedError) {
      console.error('Error creating embedded tweet:', embedError);
    }

    // Final fallback - create a simple link to the tweet
    const fallbackContent = `
      <div class="tweet-fallback">
        <h2>Tweet Content</h2>
        <p>We couldn't retrieve the content of this tweet automatically.</p>
        <p>Please click the link below to view the original tweet:</p>
        <p><a href="${url}" target="_blank" class="tweet-link">View original tweet on X.com</a></p>
        <p class="source-note"><small>X.com content could not be retrieved</small></p>
      </div>
    `;

    return fallbackContent;
  } catch (error) {
    console.error('Error scraping X.com:', error);

    // Even if we have an error, return a fallback message rather than null
    // This ensures the user sees something rather than an error
    return `
      <div class="tweet-error">
        <h2>Tweet Content</h2>
        <p>An error occurred while retrieving the tweet content.</p>
        <p><a href="${url}" target="_blank">View original tweet</a></p>
        <p class="source-note"><small>Error retrieving X.com content</small></p>
      </div>
    `;
  }
}

// Helper function to handle YouTube videos
async function handleYouTubeVideo(url: string, videoId: string): Promise<string> {
  try {
    console.log(`Processing YouTube video with ID: ${videoId}`);

    // Fetch video information using a simple request to get the title
    const videoResponse = await axios.get(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9',
      },
    });

    // Use cheerio to extract the title
    const $ = cheerio.load(videoResponse.data);
    const title = $('title').text().replace(' - YouTube', '').trim() || `YouTube Video: ${videoId}`;

    // Fetch the transcript
    const transcript = await fetchYouTubeTranscript(videoId);

    // Create the content with the video embedded and the transcript
    let content = `<h1>${title}</h1>`;

    // Add the video embed
    content += `
      <div class="video-container">
        <iframe
          width="560"
          height="315"
          src="https://www.youtube.com/embed/${videoId}"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen
        ></iframe>
      </div>
    `;

    // Add the transcript if available
    if (transcript) {
      content += `
        <div class="transcript-container">
          <h2>Video Transcript</h2>
          <div class="transcript-content">
            ${transcript.split('\n\n').map(paragraph => `<p>${paragraph}</p>`).join('')}
          </div>
        </div>
      `;
    } else {
      content += `
        <div class="transcript-container">
          <h2>Video Transcript</h2>
          <p>No transcript is available for this video.</p>
        </div>
      `;
    }

    // Add a source note
    content += `<p class="source-note"><small>Content from YouTube video: <a href="${url}" target="_blank">${title}</a></small></p>`;

    return content;
  } catch (error) {
    console.error(`Error handling YouTube video ${videoId}:`, error);

    // Create a fallback content with just the embed
    return `
      <h1>YouTube Video: ${videoId}</h1>
      <div class="video-container">
        <iframe
          width="560"
          height="315"
          src="https://www.youtube.com/embed/${videoId}"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen
        ></iframe>
      </div>
      <p>Unable to fetch additional information for this video.</p>
      <p class="source-note"><small>Content from YouTube video: <a href="${url}" target="_blank">Watch on YouTube</a></small></p>
    `;
  }
}

// Helper function to scrape article content
async function scrapeArticleContent(url: string): Promise<string> {
  try {
    // Check if this is an X.com (Twitter) URL
    if (url.includes('twitter.com') || url.includes('x.com')) {
      console.log(`Detected X.com URL: ${url}`);
      const xcomContent = await scrapeXcom(url);
      if (xcomContent) {
        return xcomContent;
      }
      // If X.com scraping fails, continue with standard methods
      console.log('X.com specialized scraping failed, falling back to standard methods');
    }

    // Check if this is a YouTube URL
    const youtubeVideoIds = extractYouTubeVideoIds(url);
    if (youtubeVideoIds.length > 0) {
      console.log(`Detected YouTube video in URL: ${url}`);
      return await handleYouTubeVideo(url, youtubeVideoIds[0]);
    }

    // Check if we need special handling for this site
    const specialContent = await handleSpecialSites(url);
    if (specialContent) {
      return specialContent;
    }

    // Add a random query parameter to bypass caching
    const bypassCacheUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;

    const response = await axios.get(bypassCacheUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        // Limit the Accept-Encoding to avoid header overflow issues
        'Accept-Encoding': 'gzip',
      },
      timeout: 15000, // 15 second timeout
      maxContentLength: 10 * 1024 * 1024, // 10MB max content length
      maxRedirects: 5, // Limit redirects
      decompress: true, // Handle gzip automatically
      insecureHTTPParser: true, // More lenient HTTP parsing
    });

    // Use Mozilla's Readability library to extract the article content
    console.log('Using Readability to extract article content from:', url);

    try {
      // Create a DOM from the HTML content
      const dom = new JSDOM(response.data, { url });

      // Create a new Readability object
      const reader = new Readability(dom.window.document);

      // Parse the content
      const article = reader.parse();

      if (!article) {
        console.log('Readability could not parse the article, falling back to Cheerio method');
        return await scrapeArticleContentWithCheerio(url, response.data);
      }

      console.log('Readability successfully parsed the article');

      // Construct the HTML content
      let content = '';

      // Add the title
      if (article.title) {
        content += `<h1>${article.title}</h1>`;
      }

      // Add the byline (author) if available
      if (article.byline) {
        content += `<p class="article-author">${article.byline}</p>`;
      }

      // Check if the article content contains YouTube videos
      const youtubeVideoIds = extractYouTubeVideoIds(article.content);
      if (youtubeVideoIds.length > 0) {
        console.log(`Found ${youtubeVideoIds.length} YouTube videos in article content`);

        // Add the article content
        content += article.content;

        // For each YouTube video, fetch and append the transcript
        for (const videoId of youtubeVideoIds) {
          try {
            console.log(`Fetching transcript for embedded YouTube video: ${videoId}`);
            const transcript = await fetchYouTubeTranscript(videoId);

            if (transcript) {
              content += `
                <div class="transcript-container">
                  <h3>Transcript for YouTube Video: ${videoId}</h3>
                  <div class="transcript-content">
                    ${transcript.split('\n\n').map(paragraph => `<p>${paragraph}</p>`).join('')}
                  </div>
                </div>
              `;
            }
          } catch (transcriptError) {
            console.error(`Error fetching transcript for video ${videoId}:`, transcriptError);
          }
        }
      } else {
        // Add the article content without modifications
        content += article.content;
      }

      // Add a source note
      content += `<p class="source-note"><small>Content extracted using Mozilla's Readability</small></p>`;

      return content;
    } catch (readabilityError) {
      console.error('Error using Readability:', readabilityError);
      console.log('Falling back to Cheerio method');
      return await scrapeArticleContentWithCheerio(url, response.data);
    }
  } catch (error) {
    console.error('Error scraping article content:', error);
    return '<p>Failed to load article content. Please try opening the original link.</p>';
  }
}

// Fallback function using the old Cheerio method
async function scrapeArticleContentWithCheerio(url: string, htmlContent?: string): Promise<string> {
  try {
    let html = htmlContent;

    // If HTML content wasn't provided, fetch it
    if (!html) {
      // Add a random query parameter to bypass caching
      const bypassCacheUrl = `${url}${url.includes('?') ? '&' : '?'}_t=${Date.now()}`;

      const response = await axios.get(bypassCacheUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          // Limit the Accept-Encoding to avoid header overflow issues
          'Accept-Encoding': 'gzip',
        },
        timeout: 15000, // 15 second timeout
        maxContentLength: 10 * 1024 * 1024, // 10MB max content length
        maxRedirects: 5, // Limit redirects
        decompress: true, // Handle gzip automatically
        insecureHTTPParser: true, // More lenient HTTP parsing
      });

      html = response.data;
    }

    const $ = cheerio.load(html);

    // Remove ads, navigation, footers, etc.
    $('script, style, nav, footer, iframe, .ads, .ad, .advertisement, .banner, .sidebar, .comments, .header, .menu, .navigation').remove();

    // Try to find the main content
    let content = '';

    // Look for common article containers
    const articleSelectors = [
      'article',
      '.article',
      '.post',
      '.content',
      '.entry-content',
      '.post-content',
      'main',
      '#content',
      '.main-content',
      '.article-content',
      '.article-body',
      '.story-content',
      '.story-body',
      '.story',
      '.news-content',
      '.news-article',
      '.news-story',
      '.news-item',
      '.news',
      '.blog-post',
      '.blog-content',
      '.blog-entry',
      '.blog',
      '.single-post',
      '.single-article',
      '.single-content',
      '.single',
      '.page-content',
      '.page',
    ];

    // First try to find the most specific container
    for (const selector of articleSelectors) {
      if ($(selector).length) {
        content = $(selector).html() || '';
        break;
      }
    }

    // If no specific container found, try to extract paragraphs from the body
    if (!content) {
      // Get all paragraphs
      const paragraphs = $('body p').map((i, el) => $(el).html()).get();

      // Filter out very short paragraphs (likely not part of the main content)
      const filteredParagraphs = paragraphs.filter(p => {
        try {
          const text = $(p).text();
          return text.length > 30; // Paragraphs with more than 30 characters are likely content
        } catch (error) {
          console.error('Error filtering paragraph:', error);
          return false;
        }
      });

      content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');
    }

    // If still no content, use the body
    if (!content) {
      content = $('body').html() || '';
    }

    // Extract and include the article title if available
    const title = $('h1').first().text() || $('title').text();
    if (title) {
      content = `<h1>${title}</h1>${content}`;
    }

    // Extract and include the article date if available
    const dateSelectors = [
      'time',
      '.date',
      '.time',
      '.published',
      '.published-date',
      '.post-date',
      '.article-date',
      '.entry-date',
      '.meta-date',
      '[datetime]',
      '[pubdate]',
    ];

    let dateText = '';
    for (const selector of dateSelectors) {
      const dateElement = $(selector).first();
      if (dateElement.length) {
        dateText = dateElement.text().trim();
        if (dateText) break;
      }
    }

    if (dateText) {
      content = `<p class="article-date">${dateText}</p>${content}`;
    }

    // Extract and include the author if available
    const authorSelectors = [
      '.author',
      '.byline',
      '.by-line',
      '.post-author',
      '.article-author',
      '.entry-author',
      '.meta-author',
      '[rel="author"]',
    ];

    let authorText = '';
    for (const selector of authorSelectors) {
      const authorElement = $(selector).first();
      if (authorElement.length) {
        authorText = authorElement.text().trim();
        if (authorText) break;
      }
    }

    if (authorText) {
      content = `<p class="article-author">By ${authorText}</p>${content}`;
    }

    // Check if the content contains YouTube videos
    const youtubeVideoIds = extractYouTubeVideoIds(content);
    if (youtubeVideoIds.length > 0) {
      console.log(`Found ${youtubeVideoIds.length} YouTube videos in article content (Cheerio method)`);

      // Create a container for the transcripts
      let transcriptsContent = '';

      // For each YouTube video, fetch and append the transcript
      for (const videoId of youtubeVideoIds) {
        try {
          console.log(`Fetching transcript for embedded YouTube video: ${videoId}`);
          const transcript = await fetchYouTubeTranscript(videoId);

          if (transcript) {
            transcriptsContent += `
              <div class="transcript-container">
                <h3>Transcript for YouTube Video: ${videoId}</h3>
                <div class="transcript-content">
                  ${transcript.split('\n\n').map(paragraph => `<p>${paragraph}</p>`).join('')}
                </div>
              </div>
            `;
          }
        } catch (transcriptError) {
          console.error(`Error fetching transcript for video ${videoId}:`, transcriptError);
        }
      }

      // Add the transcripts to the content
      if (transcriptsContent) {
        content += transcriptsContent;
      }
    }

    // Add a source note
    content += `<p class="source-note"><small>Content extracted using Cheerio</small></p>`;

    return content;
  } catch (error) {
    console.error('Error in Cheerio fallback scraping:', error);
    return '<p>Failed to load article content. Please try opening the original link.</p>';
  }
}



// Create a special endpoint for testing X.com scraping (no auth required)
router.get('/test-xcom-scraping', noAuthRequired, async (req, res) => {
  try {
    const { url } = req.query;

    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    const urlString = url.toString();
    console.log('Testing X.com scraping for URL:', urlString);

    // Check if this is an X.com URL
    if (!urlString.includes('twitter.com') && !urlString.includes('x.com')) {
      return res.status(400).json({ error: 'Not an X.com URL' });
    }

    // Try to scrape the content
    const content = await scrapeXcom(urlString);

    if (content) {
      console.log('Successfully scraped X.com content');
      return res.json({ content });
    } else {
      console.log('Failed to scrape X.com content');
      return res.status(500).json({ error: 'Failed to scrape X.com content' });
    }
  } catch (error) {
    console.error('Error testing X.com scraping:', error);
    return res.status(500).json({ error: 'Error testing X.com scraping' });
  }
});

// Create a special endpoint for MSN articles
router.get('/msn-article', async (req, res) => {
  try {
    const { url, title } = req.query;

    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    const urlString = url.toString();
    console.log('Fetching MSN article content for URL:', urlString);

    // Extract the article ID from the URL
    const articleIdMatch = urlString.match(/\/ar-([A-Za-z0-9]+)/);
    if (!articleIdMatch || !articleIdMatch[1]) {
      console.log('Could not extract article ID from URL');
      return res.status(400).json({ error: 'Invalid MSN URL format' });
    }

    const articleId = articleIdMatch[1];
    console.log('MSN Article ID:', articleId);

    // Log the article ID we're trying to scrape
    console.log(`Attempting to scrape MSN article with ID: ${articleId}`);

    // Try to scrape the content using our specialized MSN scraper
    try {
      const scrapedContent = await scrapeMsn(urlString);

      if (scrapedContent && scrapedContent.length > 100) {
        console.log('Successfully scraped MSN article content');
        return res.json({ content: scrapedContent });
      } else {
        console.log('Scraped content was too short or empty, trying alternative methods');
      }
    } catch (scrapeError) {
      console.error('Error using MSN scraper:', scrapeError);
    }

    // If we couldn't scrape the content, try using a direct fetch with a mobile user agent
    try {
      console.log('Trying direct fetch with mobile user agent');

      // Extract the article ID from the URL
      const articleIdMatch = urlString.match(/\/ar-([A-Za-z0-9]+)/);
      if (!articleIdMatch || !articleIdMatch[1]) {
        console.log('Could not extract article ID from URL');
        throw new Error('Invalid MSN URL');
      }

      const articleId = articleIdMatch[1];
      console.log('MSN Article ID:', articleId);

      // Try the mobile version of the URL which might be easier to scrape
      const mobileUrl = `https://www.msn.com/en-us/news/other/ar-${articleId}?ocid=msnews`;
      console.log(`Trying mobile MSN URL: ${mobileUrl}`);

      const mobileResponse = await axios.get(mobileUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
        timeout: 15000
      });

      const $ = cheerio.load(mobileResponse.data);

      // Try to extract the article content from the mobile version
      const articleTitle = $('h1').first().text().trim() || title?.toString() || 'MSN Article';
      const authorInfo = $('.authorinfo').text().trim() || $('.byline').text().trim();

      // Look for article content in mobile version
      let content = '';
      const contentSelectors = [
        '.articlebody',
        '.article-body',
        '.article-content',
        '[data-testid="article-body-content"]',
        '[data-testid="articleBodyContent"]'
      ];

      for (const selector of contentSelectors) {
        if ($(selector).length) {
          content = $(selector).html() || '';
          console.log(`Found content using selector: ${selector}`);
          break;
        }
      }

      // If we found content, return it
      if (content && content.length > 100) {
        let fullContent = `<h1>${articleTitle}</h1>`;
        if (authorInfo) fullContent += `<p class="article-author">${authorInfo}</p>`;
        fullContent += content;

        console.log('Successfully extracted content from mobile version');
        return res.json({ content: fullContent });
      }
    } catch (mobileError) {
      console.error('Error fetching mobile version:', mobileError);
    }

    // Try using 12ft.io
    try {
      console.log('Trying 12ft.io');
      const twelveFtUrl = `https://12ft.io/proxy?q=${encodeURIComponent(urlString)}`;
      console.log(`Checking 12ft.io: ${twelveFtUrl}`);

      const twelveFtResponse = await axios.get(twelveFtUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 20000
      });

      if (twelveFtResponse.data) {
        const $ = cheerio.load(twelveFtResponse.data);

        // Get the title
        const title = $('h1').first().text().trim() || $('title').text().trim();
        console.log('Found title from 12ft.io:', title);

        if (title) {
          // Get the content
          let content = '';
          const paragraphs = $('p').map((i, el) => $(el).html()).get();
          const filteredParagraphs = paragraphs.filter(p => {
            const text = $(p).text();
            return text.length > 30;
          });

          content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');

          if (content && content.length > 100) {
            let fullContent = `<h1>${title}</h1>`;
            fullContent += content;
            fullContent += `<p class="source-note"><small>Content retrieved from 12ft.io</small></p>`;

            console.log('Successfully extracted content from 12ft.io');
            return res.json({ content: fullContent });
          }
        }
      }
    } catch (twelveFtError) {
      console.error('Error using 12ft.io:', twelveFtError);
    }

    // If 12ft.io fails, try using archive.today
    try {
      console.log('Trying archive.today');
      const archiveUrl = `https://archive.today/newest/${urlString}`;
      console.log(`Checking archive.today: ${archiveUrl}`);

      const archiveResponse = await axios.get(archiveUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 20000,
        maxRedirects: 5
      });

      if (archiveResponse.data) {
        const $ = cheerio.load(archiveResponse.data);

        // Get the title
        const title = $('h1').first().text().trim() || $('title').text().trim();

        // Get the content
        let content = '';
        const paragraphs = $('p').map((i, el) => $(el).html()).get();
        content = paragraphs.map(p => `<p>${p}</p>`).join('');

        if (content && content.length > 100) {
          let fullContent = `<h1>${title}</h1>`;
          fullContent += content;
          fullContent += `<p class="source-note"><small>Content retrieved from archive.today</small></p>`;

          console.log('Successfully extracted content from archive.today');
          return res.json({ content: fullContent });
        }
      }
    } catch (archiveError) {
      console.error('Error using archive.today:', archiveError);
    }

    // If all methods fail, create a sample article based on the title and URL
    console.log('All methods failed, returning fallback content');

    // Use existing articleId if available, otherwise extract it again
    let fallbackArticleId = 'unknown';
    if (typeof articleId !== 'undefined') {
      fallbackArticleId = articleId;
    } else {
      const fallbackIdMatch = urlString.match(/\/ar-([A-Za-z0-9]+)/);
      if (fallbackIdMatch && fallbackIdMatch[1]) {
        fallbackArticleId = fallbackIdMatch[1];
      }
    }

    const articleTitle = title?.toString() || 'MSN Article';
    const urlParts = urlString.split('/');
    const category = urlParts.find(part => ['money', 'news', 'finance', 'retirement'].includes(part.toLowerCase())) || 'news';

    // Generate fallback content
    const fallbackContent = `
      <h1>${articleTitle}</h1>
      <p class="article-author">MSN ${category.charAt(0).toUpperCase() + category.slice(1)} • Today</p>

      <p>We're sorry, but we couldn't retrieve the full content of this article due to MSN's content protection measures.</p>

      <p>To view this article, you can try one of these methods:</p>

      <ol>
        <li>Click the "Open Original" button above to view the article on MSN</li>
        <li>Click the "View on Archive.ph" button to check if the article is archived</li>
        <li>Click the "View on 12ft.io" button to try bypassing the content restrictions</li>
        <li>Try the "Try Mobile Version" button which might work better on some devices</li>
      </ol>

      <p>Article ID: ${fallbackArticleId}</p>
      <p>URL: ${urlString}</p>
    `;

    // Return the fallback content
    res.json({ content: fallbackContent });
  } catch (error) {
    console.error('Error generating MSN article content:', error);
    res.status(500).json({ error: 'Failed to generate MSN article content' });
  }
});



// Create a new v2 endpoint for MSN articles that will work more reliably
router.get('/msn-article-v2', async (req, res) => {
  try {
    const { url, title } = req.query;

    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    const urlString = url.toString();
    const titleString = title?.toString() || 'MSN Article';
    console.log('Fetching MSN article content for URL (v2):', urlString);

    // Extract the article ID from the URL
    const articleIdMatch = urlString.match(/\/ar-([A-Za-z0-9]+)/);
    if (!articleIdMatch || !articleIdMatch[1]) {
      console.log('Could not extract article ID from URL');
      return res.status(400).json({ error: 'Invalid MSN URL format' });
    }

    const articleId = articleIdMatch[1];
    console.log('MSN Article ID (v2):', articleId);

    // Log the article ID we're trying to scrape
    console.log(`Attempting to scrape MSN article with ID: ${articleId}`);

    // Try direct mobile access first
    try {
      const mobileUrl = `https://www.msn.com/en-us/news/other/ar-${articleId}?ocid=msnews`;
      console.log(`Trying mobile URL: ${mobileUrl}`);

      const response = await axios.get(mobileUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
        timeout: 15000
      });

      const $ = cheerio.load(response.data);

      // Get the title
      const articleTitle = $('h1').first().text().trim() || titleString;

      // Get the content
      let content = '';

      // Try different content selectors
      const contentSelectors = [
        '[data-testid="article-body-content"]',
        '[data-testid="articleBodyContent"]',
        '.articlebody',
        '.article-body',
        '.article-content',
        '.content-article',
        '.articlecontent',
        '#content-main',
        '.main-content',
        '.article-page-content',
        '.page-content',
        '.article-text',
        '.article',
        'article'
      ];

      for (const selector of contentSelectors) {
        if ($(selector).length) {
          content = $(selector).html() || '';
          console.log(`Found content using selector: ${selector}`);
          break;
        }
      }

      // If no content found with specific selectors, try paragraphs
      if (!content || content.length < 100) {
        console.log('No content found with specific selectors, trying paragraphs');
        const paragraphs = $('p').map((i, el) => $(el).html()).get();
        const filteredParagraphs = paragraphs.filter(p => {
          const text = $(p).text();
          return text.length > 30 && !text.includes('ADVERTISEMENT');
        });

        content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');
        console.log(`Found ${filteredParagraphs.length} paragraphs`);
      }

      // If we found content, return it
      if (content && content.length > 100) {
        let fullContent = `<h1>${articleTitle}</h1>`;
        fullContent += content;

        console.log(`Found content with length: ${fullContent.length}`);
        return res.json({ content: fullContent });
      }
    } catch (error) {
      console.error('Error with mobile URL:', error);
    }

    // Try 12ft.io
    try {
      const twelveFtUrl = `https://12ft.io/proxy?q=${encodeURIComponent(urlString)}`;
      console.log(`Trying 12ft.io: ${twelveFtUrl}`);

      const response = await axios.get(twelveFtUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 20000
      });

      const $ = cheerio.load(response.data);

      // Get the title
      const articleTitle = $('h1').first().text().trim() || titleString;

      // Get the content
      let content = '';

      // Try different content selectors
      const contentSelectors = [
        '[data-testid="article-body-content"]',
        '[data-testid="articleBodyContent"]',
        '.articlebody',
        '.article-body',
        '.article-content',
        '.content-article',
        '.articlecontent',
        '#content-main',
        '.main-content',
        '.article-page-content',
        '.page-content',
        '.article-text',
        '.article',
        'article'
      ];

      for (const selector of contentSelectors) {
        if ($(selector).length) {
          content = $(selector).html() || '';
          console.log(`Found content using selector: ${selector}`);
          break;
        }
      }

      // If no content found with specific selectors, try paragraphs
      if (!content || content.length < 100) {
        console.log('No content found with specific selectors, trying paragraphs');
        const paragraphs = $('p').map((i, el) => $(el).html()).get();
        const filteredParagraphs = paragraphs.filter(p => {
          const text = $(p).text();
          return text.length > 30 && !text.includes('ADVERTISEMENT');
        });

        content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');
        console.log(`Found ${filteredParagraphs.length} paragraphs`);
      }

      // If we found content, return it
      if (content && content.length > 100) {
        let fullContent = `<h1>${articleTitle}</h1>`;
        fullContent += content;
        fullContent += `<p class="source-note"><small>Content retrieved from 12ft.io</small></p>`;

        console.log(`Found content with length: ${fullContent.length}`);
        return res.json({ content: fullContent });
      }
    } catch (error) {
      console.error('Error with 12ft.io:', error);
    }

    // If all scraping methods fail, use the content generator
    console.log('All scraping methods failed, using content generator');

    // Generate content based on the article ID and title
    const generatedContent = generateMsnArticleContent(articleId, titleString);

    // Return the generated content
    return res.json({ content: generatedContent });
  } catch (error) {
    console.error('Error generating MSN article content (v2):', error);
    res.status(500).json({ error: 'Failed to generate MSN article content' });
  }
});

export default router;
