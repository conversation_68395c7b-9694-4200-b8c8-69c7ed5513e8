import axios from 'axios';
import * as cheerio from 'cheerio';
import * as storage from '../storage';

/**
 * Fetches the view count for a YouTube video by scraping the watch page
 * @param videoId The YouTube video ID
 * @returns The view count as a number, or 0 if it couldn't be retrieved
 */
/**
 * Fetches the view count for a YouTube video by scraping the watch page
 * For live videos, returns the current viewer count ("watching now") instead of total views
 * @param videoId The YouTube video ID
 * @param forceTotalViews If true, always return total views even for live videos
 * @returns The view count as a number, or 0 if it couldn't be retrieved
 */
// Cache for view counts to reduce API calls
const viewCountCache = new Map<string, { count: number, timestamp: number }>();
const CACHE_TTL = 1 * 60 * 1000; // 1 minute cache TTL for real-time accuracy

export async function fetchVideoViewCount(videoId: string, forceTotalViews: boolean = false): Promise<number> {
  try {
    console.log(`fetchVideoViewCount: Starting fetch for video ${videoId}`);

    // For debugging, let's skip cache for now to ensure we get fresh data
    const now = Date.now();
    const cachedData = viewCountCache.get(videoId);

    // Clear cache for this video to force fresh fetch
    if (cachedData) {
      console.log(`fetchVideoViewCount: Clearing existing cache for ${videoId} (was: ${cachedData.count})`);
      viewCountCache.delete(videoId);
    }

    // Skip cache check for debugging
    console.log(`fetchVideoViewCount: Forcing fresh fetch for ${videoId} (cache bypassed)`);

    // if (cachedData && (now - cachedData.timestamp < CACHE_TTL)) {
    //   console.log(`Using cached view count for ${videoId}: ${cachedData.count}`);
    //   return cachedData.count;
    // }

    // Use a more efficient approach for fetching view counts
    // We'll use a direct fetch with minimal processing
    const startTime = Date.now();

    // Use a more modern user agent with timeout and cache-busting
    const response = await axios.get(`https://www.youtube.com/watch?v=${videoId}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
      },
      timeout: 10000 // 10 second timeout to prevent hanging requests
    });

    const html = response.data;
    const $ = cheerio.load(html);

    // Check if this is an active live video
    const isActiveLive = html.includes('"isLiveNow":true') ||
                  html.includes('"isLive":true') ||
                  html.includes('"status":"LIVE"');

    // Check if this has live indicators in the title but might be an ended live stream
    const hasLiveIndicatorsInTitle = html.includes('🔴 LIVE') ||
                  html.includes('🔴LIVE') ||
                  html.includes('[LIVE]') ||
                  html.includes('(LIVE)') ||
                  html.includes('LIVE:') ||
                  html.includes('LIVE STREAM') ||
                  html.includes('LIVESTREAM');

    // Check if this is an ended live stream by looking for specific patterns
    const isEndedLiveStream = (hasLiveIndicatorsInTitle &&
                  !isActiveLive &&
                  (html.includes('was streamed') ||
                   html.includes('Streamed live on') ||
                   html.includes('Premiered'))) ||
                  // Also check for unplayable ended live streams
                  (html.includes('"status":"UNPLAYABLE"') &&
                   html.includes('"reason":"This live stream recording is not available"') &&
                   html.includes('"isLiveNow":false') &&
                   html.includes('startTimestamp') &&
                   html.includes('endTimestamp'));

    // Try to extract the view count from the schema.org metadata for unplayable live streams
    if (isEndedLiveStream && html.includes('"status":"UNPLAYABLE"')) {
      console.log(`Detected unplayable ended live stream: ${videoId} - Extracting view count from metadata`);

      // Set the isUnplayable flag for this video in the database
      try {
        await storage.updateYoutubeVideoMetadata(videoId, { isUnplayable: true });
        console.log(`Marked video ${videoId} as unplayable in the database`);
      } catch (e) {
        console.error('Error marking video as unplayable:', e);
      }

      // Look for the view count in the schema.org metadata
      const interactionCountMatch = html.match(/itemprop="interactionType" content="https:\/\/schema.org\/WatchAction"><meta itemprop="userInteractionCount" content="(\d+)"/);
      if (interactionCountMatch && interactionCountMatch[1]) {
        const viewCount = parseInt(interactionCountMatch[1], 10);
        if (!isNaN(viewCount)) {
          console.log(`Found view count in schema.org metadata: ${viewCount} for video ${videoId}`);
          viewCountCache.set(videoId, { count: viewCount, timestamp: Date.now() });
          return viewCount;
        }
      }

      // If we couldn't find the view count in the schema.org metadata, try other methods
      console.log(`Couldn't find view count in schema.org metadata for video ${videoId}, trying other methods`);
    }

    // If it's an ended live stream, we should get the total view count, not the live viewer count
    if (isEndedLiveStream) {
      console.log(`Detected ended live stream: ${videoId} - Getting total views instead of live viewers`);
      // We'll fall through to the regular view count methods below
    }
    // Method 0: If it's an active live video, try to extract the current viewer count
    else if ((isActiveLive || html.includes('watching now')) && !forceTotalViews && !isEndedLiveStream) {
      console.log(`Detected active live video: ${videoId}${forceTotalViews ? ' (forcing total views)' : ''}`);

      // Try specific live video patterns first
      const liveViewerPatterns = [
        /"viewCount":\s*{\s*"runs":\s*\[{"text":"([\d,]+)"},{"text":"\s*watching now\s*"}\]/i,
        /"viewCount":\s*{\s*"simpleText":\s*"([\d,]+)\s*watching now\s*"/i,
        /"concurrentViewers":\s*"(\d+)"/i,
        /"viewerCount":\s*"(\d+)"/i,
        /watching now"}\],"simpleText":"([\d,]+)/i,
        /watching now.*?(\d+[\d,]*)/i,
        /(\d+[\d,]*)\s*watching now/i
      ];

      for (const pattern of liveViewerPatterns) {
        const match = html.match(pattern);
        if (match && match[1]) {
          const cleanNumber = match[1].replace(/,/g, '');
          const viewerCount = parseInt(cleanNumber, 10);
          if (!isNaN(viewerCount)) {
            console.log(`Found live viewer count with pattern ${pattern}: ${viewerCount} for video ${videoId}`);

            // Store this information in a global cache or database for future reference
            try {
              // This is where you would store the live status and viewer count
              console.log(`Video ${videoId} is live with ${viewerCount} viewers`);
            } catch (e) {
              console.error('Error storing live video info:', e);
            }

            return viewerCount;
          }
        }
      }

      // If we couldn't find live viewers, try to get the total view count instead
      console.log(`Couldn't find live viewer count, falling back to total views for video ${videoId}`);
    } else if (isActiveLive && forceTotalViews) {
      console.log(`Detected active live video: ${videoId} (forcing total views)`);
    }

    // Method 1: Try to extract view count from the JSON-LD script
    try {
      const scriptTags = $('script[type="application/ld+json"]');
      for (let i = 0; i < scriptTags.length; i++) {
        const scriptContent = $(scriptTags[i]).html();
        if (scriptContent) {
          const jsonData = JSON.parse(scriptContent);
          if (jsonData && jsonData.interactionStatistic && Array.isArray(jsonData.interactionStatistic)) {
            for (const stat of jsonData.interactionStatistic) {
              if (stat.interactionType === 'http://schema.org/WatchAction' && stat.userInteractionCount) {
                const viewCount = parseInt(stat.userInteractionCount, 10);
                if (!isNaN(viewCount)) {
                  console.log(`Found view count in JSON-LD: ${viewCount} for video ${videoId}`);
                  return viewCount;
                }
              }
            }
          }
        }
      }
    } catch (jsonError) {
      console.log(`Error parsing JSON-LD for video ${videoId}:`, jsonError);
    }

    // Method 2: Try to extract view count from the page using regex patterns
    const viewCountPatterns = [
      // Most reliable patterns based on testing - prioritize these
      /videoViewCountRenderer.*?(\d+[\d,]*)\s+views/i, // Format in some parts of the page (most reliable)
      /"viewCount":\s*"(\d+)"/,                  // Standard format in YouTube's JSON
      /"viewCount":\s*{\s*"simpleText":\s*"([\d,]+)\s+views"/i, // Another common format
      /"text":\s*"([\d,]+)\s+views"/i,           // Yet another format
      /aria-label="([\d,]+) views"/i,            // Aria label format

      // New patterns for 2024 YouTube format
      /"viewCount":\s*{\s*"runs":\s*\[\s*{\s*"text":\s*"([\d,]+)"\s*}/i, // New format in 2024
      /"viewCount":\s*{\s*"runs":\s*\[\s*{\s*"text":\s*"([\d,]+)"\s*},\s*{\s*"text":\s*"\s*views\s*"/i, // New format with "views" text
      /"viewCount":\s*{\s*"runs":\s*\[\s*{\s*"text":\s*"([\d,]+)"\s*},\s*{\s*"text":\s*"\s*views\s*"\s*}\s*\]/i, // Complete pattern

      // Try to extract from YouTube API data embedded in the page
      /"viewCount":\s*(\d+)/,                    // Direct API format
      /"videoViewCountRenderer".*?"text".*?"(\d+[\d,]*)"/s, // Another format in renderer

      // Try to extract from ytInitialPlayerResponse
      /"videoDetails".*?"viewCount":\s*"(\d+)"/s, // From player response

      // More generic pattern - use with caution as it might match other numbers
      /(\d+[\d,]*)\s+views/i,                    // Simple text format

      // Live video patterns - for "X watching now"
      /videoViewCountRenderer.*?(\d+[\d,]*)\s+watching/i, // Live video format
      /"viewCount":\s*{\s*"simpleText":\s*"([\d,]+)\s+watching"/i, // Another live format
      /"text":\s*"([\d,]+)\s+watching"/i,        // Yet another live format
      /(\d+[\d,]*)\s+watching/i,                 // Simple live text format
      /(\d+[\d,]*)\s+watching now/i              // "watching now" format
    ];

    // Function to validate view count is reasonable
    const isReasonableViewCount = (count: number): boolean => {
      // YouTube videos typically don't have more than a few billion views
      // This helps filter out parsing errors that might result in unreasonably large numbers

      // Basic range check
      if (count < 0 || count > 10000000000) { // 10 billion max
        console.log(`Rejecting unreasonable view count: ${count} for video ${videoId}`);
        return false;
      }

      // Additional validation for suspiciously high view counts
      // Check if the HTML contains any view count indicators
      const viewCountIndicators = [
        /(\d+[\d,.]+K?) views/i,
        /(\d+[\d,.]+) views/i
      ];

      for (const pattern of viewCountIndicators) {
        const match = html.match(pattern);
        if (match && match[1]) {
          const textViewCount = match[1].toLowerCase();
          console.log(`Found view count indicator in HTML: "${textViewCount} views"`);

          // If the text contains "K", it's in thousands
          if (textViewCount.includes('k')) {
            const baseNumber = parseFloat(textViewCount.replace('k', ''));
            const approximateCount = Math.round(baseNumber * 1000);

            // If our parsed count is more than 5x the approximate count, reject it
            if (count > approximateCount * 5) {
              console.log(`Rejecting suspicious view count: ${count} for video ${videoId} that appears to have ~${approximateCount} views`);
              return false;
            }
          } else {
            // Try to parse the text view count
            const cleanNumber = match[1].replace(/[,\.]/g, '');
            const textCount = parseInt(cleanNumber, 10);

            if (!isNaN(textCount)) {
              // If our parsed count is more than 5x the text count, reject it
              if (count > textCount * 5) {
                console.log(`Rejecting suspicious view count: ${count} for video ${videoId} that appears to have ~${textCount} views`);
                return false;
              }
            }
          }

          // We found at least one indicator, no need to check more
          break;
        }
      }

      return true;
    };

    let bestViewCount = 0;
    let bestPattern = '';
    let allFoundViewCounts: number[] = [];

    // Try all patterns and collect all reasonable results
    for (const pattern of viewCountPatterns) {
      try {
        const match = html.match(pattern);
        if (match && match[1]) {
          // Remove commas from the number string
          const cleanNumber = match[1].replace(/,/g, '');
          const viewCount = parseInt(cleanNumber, 10);

          if (!isNaN(viewCount) && isReasonableViewCount(viewCount)) {
            // Reduced logging - only log for the most important patterns
            if (pattern.toString().includes('videoViewCountRenderer') ||
                pattern.toString().includes('viewCount') ||
                pattern.toString().includes('videoDetails')) {
              console.log(`Found view count with pattern /...${pattern.toString().slice(-20)}/: ${viewCount} for video ${videoId}`);
            }

            allFoundViewCounts.push(viewCount);

            // If this is our first valid match or it's more reasonable than previous matches, use it
            if (bestViewCount === 0 ||
                (viewCount > 0 && viewCount < bestViewCount)) {
              bestViewCount = viewCount;
              bestPattern = pattern.toString();
            }

            // If we found a view count with one of the most reliable patterns, we can return it immediately
            // This helps avoid false positives from other patterns
            if (
              pattern.toString().includes('videoViewCountRenderer') ||
              pattern.toString().includes('viewCount') ||
              pattern.toString().includes('videoDetails')
            ) {
              console.log(`Found view count with reliable pattern, returning immediately: ${viewCount}`);
              return viewCount;
            }
          }
        }
      } catch (patternError) {
        // If a specific pattern causes an error (e.g., regex timeout), just skip it
        // Reduced logging - don't log pattern errors
        continue;
      }
    }

    // If we have multiple view counts, do additional validation
    if (allFoundViewCounts.length > 1) {
      console.log(`Found ${allFoundViewCounts.length} different view counts for video ${videoId}: ${allFoundViewCounts.join(', ')}`);

      // Sort the view counts
      allFoundViewCounts.sort((a, b) => a - b);

      // Calculate the frequency of each view count
      const countFrequency: {[key: number]: number} = {};
      allFoundViewCounts.forEach(count => {
        countFrequency[count] = (countFrequency[count] || 0) + 1;
      });

      // Find the most common view count
      let mostCommonCount = 0;
      let highestFrequency = 0;

      for (const count in countFrequency) {
        if (countFrequency[count] > highestFrequency) {
          mostCommonCount = parseInt(count, 10);
          highestFrequency = countFrequency[count];
        }
      }

      // If there's a clear winner (appears more than once), use it
      if (highestFrequency > 1) {
        console.log(`Using most common value: ${mostCommonCount} (appeared ${highestFrequency} times)`);
        bestViewCount = mostCommonCount;
      }
      // If there's no clear winner, check for outliers
      else if (allFoundViewCounts.length >= 3) {
        const smallestValue = allFoundViewCounts[0];
        const largestValue = allFoundViewCounts[allFoundViewCounts.length - 1];

        if (largestValue > smallestValue * 10) {
          console.log(`Detected outlier: ${largestValue} is more than 10x larger than ${smallestValue}`);
          // Use the median value instead
          const medianIndex = Math.floor(allFoundViewCounts.length / 2);
          const medianValue = allFoundViewCounts[medianIndex];
          console.log(`Using median value: ${medianValue} instead of outlier ${largestValue}`);
          bestViewCount = medianValue;
        }
        // If no outliers, use the smallest value as it's likely the most accurate
        else {
          console.log(`No clear winner or outliers, using smallest value: ${smallestValue}`);
          bestViewCount = smallestValue;
        }
      }
      // If we only have 2 values and no clear winner, use the smaller one
      else if (allFoundViewCounts.length === 2) {
        const smallerValue = allFoundViewCounts[0];
        const largerValue = allFoundViewCounts[1];

        // If the larger value is significantly higher, it might be an outlier
        if (largerValue > smallerValue * 5) {
          console.log(`Using smaller value ${smallerValue} instead of potentially inflated ${largerValue}`);
          bestViewCount = smallerValue;
        }
        // Otherwise use the average of the two
        else {
          const averageValue = Math.round((smallerValue + largerValue) / 2);
          console.log(`Using average of two values: ${averageValue}`);
          bestViewCount = averageValue;
        }
      }
    }

    if (bestViewCount > 0) {
      console.log(`Using best view count match: ${bestViewCount} (pattern: ${bestPattern}) for video ${videoId}`);
      // Store in cache
      viewCountCache.set(videoId, { count: bestViewCount, timestamp: Date.now() });
      return bestViewCount;
    }

    // Method 3: Try to find the view count in the meta tags
    const metaViewCount = $('meta[itemprop="interactionCount"]').attr('content');
    if (metaViewCount) {
      const viewCount = parseInt(metaViewCount, 10);
      if (!isNaN(viewCount)) {
        console.log(`Found view count in meta tag: ${viewCount} for video ${videoId}`);
        return viewCount;
      }
    }

    // Method 3.5: Try to extract from ytInitialData with improved parsing
    const ytInitialDataMatch = html.match(/ytInitialData\s*=\s*({.*?});/s); // Added 's' flag for multiline
    if (ytInitialDataMatch && ytInitialDataMatch[1]) {
      try {
        console.log(`Found ytInitialData for video ${videoId}, attempting to parse`);
        const ytData = JSON.parse(ytInitialDataMatch[1]);

        // Try multiple paths to find view count in the new YouTube structure
        const paths = [
          // Path 1: Standard path for view count (older format)
          () => {
            const viewCountText = ytData?.contents?.twoColumnWatchNextResults?.results?.results?.contents?.[0]?.videoPrimaryInfoRenderer?.viewCount?.videoViewCountRenderer?.viewCount?.simpleText;
            if (viewCountText) {
              console.log(`Found view count text in path 1: "${viewCountText}"`);
              return viewCountText;
            }
            return null;
          },

          // Path 2: New format with "runs" array (2024 format)
          () => {
            const viewCountRuns = ytData?.contents?.twoColumnWatchNextResults?.results?.results?.contents?.[0]?.videoPrimaryInfoRenderer?.viewCount?.videoViewCountRenderer?.viewCount?.runs;
            if (viewCountRuns && viewCountRuns.length > 0) {
              const viewText = viewCountRuns[0]?.text;
              if (viewText) {
                console.log(`Found view count text in path 2: "${viewText}"`);
                return viewText;
              }
            }
            return null;
          },

          // Path 3: Try to find in videoDetails
          () => {
            const viewCount = ytData?.playerOverlays?.playerOverlayRenderer?.videoDetails?.playerOverlayVideoDetailsRenderer?.subtitle?.runs?.find(run => run.text.includes('views'))?.text;
            if (viewCount) {
              console.log(`Found view count text in path 3: "${viewCount}"`);
              return viewCount;
            }
            return null;
          }
        ];

        // Try each path until we find a valid view count
        for (const getViewCountText of paths) {
          const viewCountText = getViewCountText();
          if (viewCountText) {
            // Extract the number from the text
            const match = viewCountText.match(/([\d,]+)(?:\s+views)?/i);
            if (match && match[1]) {
              const cleanNumber = match[1].replace(/,/g, '');
              const viewCount = parseInt(cleanNumber, 10);
              if (!isNaN(viewCount) && isReasonableViewCount(viewCount)) {
                console.log(`Found view count in ytInitialData: ${viewCount} for video ${videoId}`);
                return viewCount;
              }
            }
          }
        }

        console.log(`Could not extract view count from ytInitialData for video ${videoId}`);
      } catch (error) {
        console.log(`Error parsing ytInitialData: ${error}`);
      }
    } else {
      console.log(`No ytInitialData found for video ${videoId}`);
    }

    // Method 3.6: Try to extract from ytInitialPlayerResponse
    const ytPlayerMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.*?});/s); // Added 's' flag for multiline
    if (ytPlayerMatch && ytPlayerMatch[1]) {
      try {
        console.log(`Found ytInitialPlayerResponse for video ${videoId}, attempting to parse`);
        const playerData = JSON.parse(ytPlayerMatch[1]);

        // Try to get view count from videoDetails
        const viewCount = playerData?.videoDetails?.viewCount;
        if (viewCount) {
          const parsedCount = parseInt(viewCount, 10);
          if (!isNaN(parsedCount) && isReasonableViewCount(parsedCount)) {
            console.log(`Found view count in ytInitialPlayerResponse: ${parsedCount} for video ${videoId}`);
            return parsedCount;
          }
        }
      } catch (error) {
        console.log(`Error parsing ytInitialPlayerResponse: ${error}`);
      }
    }

    // Method 4: Look for view count in the page content
    // Check for regular views
    const viewTextElements = $('span:contains("views")');
    for (let i = 0; i < viewTextElements.length; i++) {
      const text = $(viewTextElements[i]).text().trim();
      console.log(`Found text element with 'views': "${text}"`);
      const match = text.match(/^([\d,]+)\s+views$/i);
      if (match && match[1]) {
        const cleanNumber = match[1].replace(/,/g, '');
        const viewCount = parseInt(cleanNumber, 10);
        if (!isNaN(viewCount)) {
          console.log(`Found view count in text element: ${viewCount} for video ${videoId}`);
          return viewCount;
        }
      }
    }

    // Method 5: Try to find the view count in the video info section
    try {
      const infoText = $('.ytd-watch-metadata').text();
      if (infoText) {
        const viewMatch = infoText.match(/(\d+[\d,]*)\s+views/i);
        if (viewMatch && viewMatch[1]) {
          const cleanNumber = viewMatch[1].replace(/,/g, '');
          const viewCount = parseInt(cleanNumber, 10);
          if (!isNaN(viewCount)) {
            console.log(`Found view count in video info section: ${viewCount} for video ${videoId}`);
            return viewCount;
          }
        }
      }
    } catch (error) {
      console.log(`Error extracting view count from video info section: ${error}`);
    }

    // Check for live stream viewers ("watching now")
    const watchingTextElements = $('span:contains("watching")');
    for (let i = 0; i < watchingTextElements.length; i++) {
      const text = $(watchingTextElements[i]).text().trim();
      const match = text.match(/^([\d,]+)\s+watching(\s+now)?$/i);
      if (match && match[1]) {
        const cleanNumber = match[1].replace(/,/g, '');
        const viewCount = parseInt(cleanNumber, 10);
        if (!isNaN(viewCount)) {
          console.log(`Found live viewers count in text element: ${viewCount} for video ${videoId}`);
          return viewCount;
        }
      }
    }

    // Reduced logging
    return 0;
  } catch (error) {
    // Simplified error logging
    console.error(`Error fetching view count for video ${videoId}: ${error.code || error.message || 'Unknown error'}`);
    return 0;
  }
}

/**
 * Parses a YouTube video duration string (ISO 8601 format) to seconds
 * @param duration Duration string in ISO 8601 format (e.g., PT1H30M15S)
 * @returns Duration in seconds
 */
export function parseDuration(duration: string): number {
  try {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return 0;

    const hours = parseInt(match[1] || '0', 10);
    const minutes = parseInt(match[2] || '0', 10);
    const seconds = parseInt(match[3] || '0', 10);

    return hours * 3600 + minutes * 60 + seconds;
  } catch (error) {
    console.error(`Error parsing duration ${duration}:`, error);
    return 0;
  }
}

/**
 * Batch fetches view counts for multiple videos to avoid too many requests
 * @param videoIds Array of YouTube video IDs
 * @param batchSize Number of videos to process in parallel
 * @param forceTotalViews If true, always return total views even for live videos
 * @returns Map of video IDs to view counts
 */
/**
 * Checks if a YouTube video is unavailable (deleted, private, etc.)
 * @param videoId The YouTube video ID
 * @returns True if the video is unavailable, false otherwise
 */
export async function isVideoUnavailable(videoId: string): Promise<boolean> {
  try {
    // First try the oembed endpoint which is lightweight and returns 404 for unavailable videos
    try {
      const oembedResponse = await axios.get(`https://www.youtube.com/oembed?url=http://www.youtube.com/watch?v=${videoId}&format=json`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
        validateStatus: (status) => status < 500, // Don't throw for 4xx errors
        timeout: 5000 // 5 second timeout
      });

      // If we get a 404 or other error status, the video is unavailable
      if (oembedResponse.status !== 200) {
        return true;
      }

      // If we get a 200 response, the video is available
      return false;
    } catch (oembedError) {
      // If there's an error with the oembed endpoint, try a second method
      // Sometimes the oembed endpoint can fail even for available videos
      try {
        // Try to fetch just the video info page which is smaller than the full watch page
        const infoResponse = await axios.get(`https://www.youtube.com/get_video_info?video_id=${videoId}`, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
          validateStatus: (status) => status < 500, // Don't throw for 4xx errors
          timeout: 5000 // 5 second timeout
        });

        // Check if the response contains indicators of an unavailable video
        const responseText = infoResponse.data;
        if (typeof responseText === 'string') {
          if (
            responseText.includes('status=fail') &&
            (responseText.includes('errorcode=150') ||
             responseText.includes('This video is unavailable') ||
             responseText.includes('Video unavailable') ||
             responseText.includes('errorcode=100') ||
             responseText.includes('Private video'))
          ) {
            return true;
          } else {
            return false;
          }
        }

        // If we can't determine from the response, assume it's available
        return false;
      } catch (infoError) {
        // If both methods fail, assume the video is unavailable
        return true;
      }
    }
  } catch (error) {
    console.error(`Error checking if video ${videoId} is unavailable: ${error.code || error.message || 'Unknown error'}`);
    // If we can't determine, assume it's unavailable to be safe
    return true;
  }
}

export async function batchFetchViewCounts(videoIds: string[], batchSize = 10, forceTotalViews = false): Promise<Map<string, number>> {
  const viewCounts = new Map<string, number>();
  // Also track unavailable videos
  const unavailableVideos = new Set<string>();
  // Track videos with errors
  const errorVideos = new Set<string>();
  // Track videos with zero views (potential issues)
  const zeroViewVideos = new Set<string>();

  // Check cache first for all videos
  const now = Date.now();
  const videosToFetch: string[] = [];

  // First check which videos we need to fetch and which ones we can get from cache
  for (const videoId of videoIds) {
    const cachedData = viewCountCache.get(videoId);
    if (cachedData && (now - cachedData.timestamp < CACHE_TTL)) {
      // Use cached data
      viewCounts.set(videoId, cachedData.count);
    } else {
      // Need to fetch this video
      videosToFetch.push(videoId);
    }
  }

  // If all videos were in cache, return early
  if (videosToFetch.length === 0) {
    console.log(`All ${videoIds.length} videos found in cache, no need to fetch from YouTube`);
    return viewCounts;
  }

  // Use a larger batch size for faster processing
  // Process videos in batches to avoid overwhelming the server
  const startTime = Date.now();
  console.log(`Starting batch fetch of view counts for ${videosToFetch.length} videos (batch size: ${batchSize}, forceTotalViews: ${forceTotalViews}), ${videoIds.length - videosToFetch.length} videos from cache`);

  // Process all batches with a limited concurrency to reduce CPU usage
  const batches: string[][] = [];
  for (let i = 0; i < videosToFetch.length; i += batchSize) {
    batches.push(videosToFetch.slice(i, i + batchSize));
  }
  console.log(`Split into ${batches.length} batches`);

  // Limit concurrent batch processing to reduce CPU usage
  const MAX_CONCURRENT_BATCHES = 2; // Process only 2 batches at a time

  // Process batches with limited concurrency
  for (let i = 0; i < batches.length; i += MAX_CONCURRENT_BATCHES) {
    const batchGroup = batches.slice(i, i + MAX_CONCURRENT_BATCHES);
    await Promise.all(batchGroup.map(async (batch, groupIndex) => {
      const batchIndex = i + groupIndex;
    const batchStartTime = Date.now();
    console.log(`Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} videos`);

    // Process all videos in this batch in parallel
    const promises = batch.map(async (videoId) => {
      try {
        // Skip the unavailable check for refresh-only operations to speed things up
        // We'll just try to get the view count directly
        const viewCount = await fetchVideoViewCount(videoId, forceTotalViews);

        if (viewCount === 0) {
          // Track videos with zero views for potential debugging
          zeroViewVideos.add(videoId);

          // Only if we couldn't get a view count, check if the video is unavailable
          console.log(`Got zero view count for video ${videoId}, checking if it's unavailable...`);
          const unavailable = await isVideoUnavailable(videoId);
          if (unavailable) {
            console.log(`Video ${videoId} is unavailable, marking for deletion`);
            unavailableVideos.add(videoId);
            viewCounts.set(videoId, -1); // Use -1 to indicate unavailable
            return;
          } else {
            console.log(`Video ${videoId} is available but returned zero views, keeping it in the database`);
          }
        }

        viewCounts.set(videoId, viewCount);
      } catch (error) {
        errorVideos.add(videoId);
        // Simplified error logging to reduce verbosity
        console.error(`Error fetching view count for video ${videoId}: ${error.code || error.message || 'Unknown error'}`);

        // Check if the video is unavailable due to the error
        try {
          console.log(`Checking if video ${videoId} is unavailable...`);
          const unavailable = await isVideoUnavailable(videoId);
          if (unavailable) {
            console.log(`Video ${videoId} is unavailable, marking for deletion`);
            unavailableVideos.add(videoId);
            viewCounts.set(videoId, -1); // Use -1 to indicate unavailable
            return;
          }
        } catch (checkError) {
          console.error(`Error checking if video ${videoId} is unavailable: ${checkError.code || checkError.message || 'Unknown error'}`);
        }

        viewCounts.set(videoId, 0);
      }
    });

    await Promise.all(promises);

    const batchDuration = Date.now() - batchStartTime;
    console.log(`Completed batch ${batchIndex + 1}/${batches.length} in ${batchDuration}ms (${(batchDuration / batch.length).toFixed(2)}ms per video)`);
    }));
  }

  const totalDuration = Date.now() - startTime;
  const cachedCount = videoIds.length - videosToFetch.length;
  console.log(`Processed ${videosToFetch.length} videos in ${totalDuration}ms (${(totalDuration / videosToFetch.length).toFixed(2)}ms per video), ${cachedCount} from cache`);
  console.log(`Results: ${viewCounts.size} view counts, ${unavailableVideos.size} unavailable videos, ${errorVideos.size} errors, ${zeroViewVideos.size} zero views, ${cachedCount} from cache`);

  // Delete unavailable videos from the database
  if (unavailableVideos.size > 0) {
    try {
      const storage = await import('../storage').then(m => m.default);

      // Delete all unavailable videos in one batch operation
      await Promise.all(
        Array.from(unavailableVideos).map(videoId =>
          storage.deleteYoutubeVideo(videoId)
        )
      );

      console.log(`Deleted ${unavailableVideos.size} unavailable videos from the database`);
    } catch (error) {
      console.error('Error deleting unavailable videos:', error);
    }
  }

  // Log videos with zero views for debugging
  if (zeroViewVideos.size > 0 && zeroViewVideos.size <= 10) {
    console.log(`Videos with zero views: ${Array.from(zeroViewVideos).join(', ')}`);
  } else if (zeroViewVideos.size > 10) {
    console.log(`${zeroViewVideos.size} videos with zero views (too many to list)`);
  }

  return viewCounts;
}
