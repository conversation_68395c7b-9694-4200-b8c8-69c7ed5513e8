.draggable-item {
  transition: transform 0.2s, opacity 0.2s, box-shadow 0.2s;
  position: relative;
  cursor: grab;
  height: 100%;
}

.draggable-item.dragging {
  opacity: 0.7;
  z-index: 100;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transform: scale(1.02);
}

.draggable-item.drag-over {
  transform: translateY(10px);
}

.draggable-card {
  transition: border-color 0.2s, box-shadow 0.2s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.draggable-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.draggable-item:active {
  cursor: grabbing;
}

.drag-handle {
  cursor: grab;
  transition: background-color 0.2s, transform 0.2s;
}

.drag-handle:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
}

.drag-handle:active {
  cursor: grabbing;
  transform: scale(0.95);
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  width: 100%;
}

@media (min-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Animation for when an item is moved */
@keyframes highlight-move {
  0% { transform: scale(1); box-shadow: 0 0 0 rgba(0, 0, 0, 0); }
  50% { transform: scale(1.03); box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2); }
  100% { transform: scale(1); box-shadow: 0 0 0 rgba(0, 0, 0, 0); }
}

.just-moved {
  animation: highlight-move 0.5s ease-in-out;
  z-index: 10;
}
