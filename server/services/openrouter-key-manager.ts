import { storage } from '../storage';
import { OpenRouterApiKey } from '../storage';

/**
 * Manages OpenRouter API keys, including rotation and error handling
 */
export class OpenRouterKeyManager {
  private static instance: OpenRouterKeyManager;
  private currentKey: OpenRouterApiKey | null = null;
  private exhaustedKeys: Set<number> = new Set();
  private isInitializing: boolean = false;
  private initPromise: Promise<void> | null = null;

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): OpenRouterKeyManager {
    if (!OpenRouterKeyManager.instance) {
      OpenRouterKeyManager.instance = new OpenRouterKeyManager();
    }
    return OpenRouterKeyManager.instance;
  }

  /**
   * Initialize the key manager
   */
  private async initialize(): Promise<void> {
    if (this.isInitializing) {
      return this.initPromise;
    }

    this.isInitializing = true;
    this.initPromise = this.getNextKey();

    try {
      await this.initPromise;
    } finally {
      this.isInitializing = false;
    }
  }

  /**
   * Reset all exhausted keys in the database
   */
  private async resetAllExhaustedKeys(): Promise<void> {
    try {
      console.log('Resetting all exhausted OpenRouter API keys in the database');

      // Get all keys that are marked as exhausted
      const stmt = storage.db.prepare(`
        SELECT id, name, exhausted_until, rate_limit_type
        FROM openrouter_api_keys
        WHERE is_active = 1
        AND exhausted_until IS NOT NULL
        AND exhausted_until > CURRENT_TIMESTAMP
      `);

      const exhaustedKeys = stmt.all();
      console.log(`Found ${exhaustedKeys.length} exhausted OpenRouter API keys`);

      if (exhaustedKeys.length === 0) {
        return;
      }

      // Reset all exhausted keys
      const resetStmt = storage.db.prepare(`
        UPDATE openrouter_api_keys
        SET exhausted_until = NULL
        WHERE is_active = 1
        AND exhausted_until IS NOT NULL
        AND exhausted_until > CURRENT_TIMESTAMP
      `);

      const result = resetStmt.run();
      console.log(`Reset ${result.changes} exhausted OpenRouter API keys`);
    } catch (error) {
      console.error('Error resetting exhausted OpenRouter API keys:', error);
    }
  }

  /**
   * Get the next available API key
   */
  public async getNextKey(): Promise<OpenRouterApiKey | null> {
    try {
      // Get all active API keys
      const activeKeys = await storage.getActiveOpenRouterApiKeys();

      if (activeKeys.length === 0) {
        console.log('No active OpenRouter API keys available');

        // Check if we have any keys that are marked as exhausted
        const stmt = storage.db.prepare(`
          SELECT COUNT(*) as count
          FROM openrouter_api_keys
          WHERE is_active = 1
          AND exhausted_until IS NOT NULL
          AND exhausted_until > CURRENT_TIMESTAMP
        `);

        const { count } = stmt.get();

        if (count > 0) {
          console.log(`Found ${count} exhausted OpenRouter API keys. Attempting to reset...`);
          await this.resetAllExhaustedKeys();

          // Try again after resetting
          return this.getNextKey();
        }

        this.currentKey = null;
        return null;
      }

      // Filter out exhausted keys
      const availableKeys = activeKeys.filter(key => !this.exhaustedKeys.has(key.id));

      if (availableKeys.length === 0) {
        console.log('All OpenRouter API keys are exhausted');

        // If all keys are exhausted, clear the exhausted set and try again
        // This gives keys a chance to recover if they've been exhausted for a while
        if (this.exhaustedKeys.size > 0) {
          console.log('Resetting exhausted OpenRouter API keys in memory');
          this.exhaustedKeys.clear();
          return this.getNextKey();
        }

        this.currentKey = null;
        return null;
      }

      // Prefer the default key if it's available
      const defaultKey = availableKeys.find(key => key.is_default);
      if (defaultKey) {
        console.log(`Using default OpenRouter API key: ${defaultKey.id}`);
        this.currentKey = defaultKey;
        return defaultKey;
      }

      // Otherwise, use the first available key
      console.log(`Using OpenRouter API key: ${availableKeys[0].id}`);
      this.currentKey = availableKeys[0];
      return availableKeys[0];
    } catch (error) {
      console.error('Error getting next OpenRouter API key:', error);
      this.currentKey = null;
      return null;
    }
  }

  /**
   * Get the current API key
   */
  public async getCurrentKey(): Promise<OpenRouterApiKey | null> {
    if (!this.currentKey) {
      await this.initialize();
    }
    return this.currentKey;
  }

  /**
   * Get the authorization header for API requests
   */
  public async getAuthorizationHeader(): Promise<string | null> {
    const key = await this.getCurrentKey();
    if (!key) {
      return null;
    }
    return `Bearer ${key.token}`;
  }

  /**
   * Get all headers needed for API requests
   */
  public async getRequestHeaders(): Promise<Record<string, string> | null> {
    const authHeader = await this.getAuthorizationHeader();
    if (!authHeader) {
      return null;
    }

    return {
      'Authorization': authHeader,
      'Content-Type': 'application/json',
      'HTTP-Referer': 'https://trendy.local', // Required by OpenRouter
      'X-Title': 'Trendy App' // Optional but recommended by OpenRouter
    };
  }

  /**
   * Mark the current key as exhausted
   * @param minutes The number of minutes to mark the key as exhausted for
   * @param rateLimitType The type of rate limit that was hit (daily, per-minute, etc.)
   */
  private async markCurrentKeyExhausted(minutes: number = 60, rateLimitType: string = 'unknown'): Promise<void> {
    if (!this.currentKey) {
      return;
    }

    // Adjust exhaustion time based on rate limit type
    let adjustedMinutes = minutes;

    // If this is a daily limit for free models, set exhaustion until tomorrow
    if (rateLimitType === 'daily-free' ||
        (rateLimitType === 'unknown' && minutes > 300)) { // If wait time is > 5 hours, assume it's a daily limit
      // Calculate minutes until midnight UTC + 1 hour buffer
      const now = new Date();
      const tomorrow = new Date();
      tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
      tomorrow.setUTCHours(0, 0, 0, 0);
      const minutesUntilTomorrow = Math.ceil((tomorrow.getTime() - now.getTime()) / (60 * 1000)) + 60; // Add 1 hour buffer

      adjustedMinutes = Math.max(minutes, minutesUntilTomorrow);
      console.log(`Daily free model limit hit. Setting exhaustion until tomorrow (${adjustedMinutes} minutes)`);
    }
    // If this is a per-minute limit, set a shorter exhaustion time
    else if (rateLimitType === 'per-minute') {
      // Set to 2 minutes to be safe
      adjustedMinutes = 2;
      console.log(`Per-minute rate limit hit. Setting short exhaustion time (${adjustedMinutes} minutes)`);
    }

    console.log(`Marking OpenRouter API key ${this.currentKey.id} as exhausted for ${adjustedMinutes} minutes (${rateLimitType} limit)`);

    // Add to exhausted set
    this.exhaustedKeys.add(this.currentKey.id);

    // Mark as exhausted in database
    await storage.markOpenRouterApiKeyExhausted(this.currentKey.id, adjustedMinutes, rateLimitType);

    // Clear current key
    this.currentKey = null;
  }

  /**
   * Handle API response error
   * @param error The error object
   * @returns True if a new key was selected, false otherwise
   */
  public async handleApiError(error: any): Promise<boolean> {
    // Check if it's a rate limit error (429 Too Many Requests)
    if (error.response && error.response.status === 429) {
      console.log('Rate limit exceeded for OpenRouter API, rotating to next API key...');

      // Extract wait time and rate limit type if provided
      const waitTime = error.waitTime || 120; // Default to 2 hours if not specified
      const rateLimitType = error.rateLimitType || 'unknown';
      const errorMessage = error.message || '';
      console.log(`Using wait time of ${waitTime} minutes for exhausted key (${rateLimitType} limit)`);

      // Check if this is a daily model limit (not just a key limit)
      const isDailyModelLimit =
        rateLimitType === 'daily-free' ||
        (errorMessage && (
          errorMessage.includes('Daily limit reached') ||
          errorMessage.includes('daily limit') ||
          errorMessage.includes('free-models-per-day')
        ));

      if (isDailyModelLimit) {
        console.log('Detected daily model limit for this API key');
        // Mark the key as exhausted for a longer period for this specific model
        if (this.currentKey) {
          // Mark as exhausted for 24 hours
          await this.markCurrentKeyExhausted(1440, 'daily-model-limit');

          // Get the next key
          const nextKey = await this.getNextKey();
          if (nextKey) {
            console.log(`Rotated to next API key ${nextKey.id} due to daily model limit`);
            return true;
          } else {
            console.log('All API keys are exhausted for this model');
            return false;
          }
        }
        return false;
      }

      // For other rate limits, mark the current key as exhausted
      if (this.currentKey) {
        // Mark as exhausted for the specified period with the rate limit type
        await this.markCurrentKeyExhausted(waitTime, rateLimitType);

        // Get the next key
        const nextKey = await this.getNextKey();
        return nextKey !== null;
      }
    }

    // Check for authentication errors (401 Unauthorized, 403 Forbidden)
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      console.log('Authentication error for OpenRouter API, rotating to next API key...');

      // Mark the current key as exhausted
      if (this.currentKey) {
        await this.markCurrentKeyExhausted();

        // Get the next key
        const nextKey = await this.getNextKey();
        return nextKey !== null;
      }
    }

    // If we have an error object with a message that includes 'rate limit'
    if (error.message && typeof error.message === 'string' &&
        (error.message.toLowerCase().includes('rate limit') ||
         error.message.toLowerCase().includes('ratelimit'))) {
      console.log('Rate limit message detected in error, rotating to next API key...');

      // Extract wait time and determine rate limit type
      let waitTime = 120; // Default to 2 hours
      let rateLimitType = 'unknown';

      // Check for daily free model limit
      if (error.message.includes('free-models-per-day')) {
        rateLimitType = 'daily-free';
        console.log('Detected daily free model limit');
        // Set a long wait time to ensure we don't use this key again today
        waitTime = 1440; // 24 hours
      }
      // Check for per-minute limit
      else if (error.message.includes('per-minute')) {
        rateLimitType = 'per-minute';
        console.log('Detected per-minute rate limit');
        waitTime = 2; // 2 minutes
      }
      // Extract specific wait time if available
      else {
        const waitTimeMatch = error.message.match(/(\d+)\s*seconds/);
        if (waitTimeMatch && waitTimeMatch[1]) {
          const seconds = parseInt(waitTimeMatch[1], 10);
          // Convert seconds to minutes and round up
          waitTime = Math.ceil(seconds / 60);
          console.log(`Extracted wait time: ${waitTime} minutes from message: ${error.message}`);

          // If wait time is very long, it's likely a daily limit
          if (waitTime > 60) {
            rateLimitType = 'daily-free';
            console.log('Long wait time suggests daily free model limit');
          }
        }
      }

      // Mark the current key as exhausted
      if (this.currentKey) {
        // Mark as exhausted for the specified period with the rate limit type
        await this.markCurrentKeyExhausted(waitTime, rateLimitType);

        // Get the next key
        const nextKey = await this.getNextKey();
        return nextKey !== null;
      }
    }

    return false;
  }
}

// Export a singleton instance
export const openRouterKeyManager = OpenRouterKeyManager.getInstance();
export default openRouterKeyManager;
