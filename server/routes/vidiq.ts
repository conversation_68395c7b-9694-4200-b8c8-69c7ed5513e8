import { Router, Request, Response } from 'express';
import axios, { AxiosError } from 'axios';
import dotenv from 'dotenv';
import path from 'path';
import { vidiqService } from '../services/vidiq';
import { keyManager } from '../services/vidiq-key-manager';
import { storage } from '../storage';

// Load VidIQ environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env.vidiq') });

const router = Router();

// Type definitions for the VidIQ API responses
interface VidIQVideo {
  id: string;
  title: string;
  channel: {
    id: string;
    title: string;
    thumbnail: string;
  };
  thumbnail: string;
  viewCount: number;
  publishedAt: string;
  score: number;
  tags?: string[];
  description?: string;
}

interface VidIQTrendyResponse {
  videos: VidIQVideo[];
  total: number;
  page: number;
  perPage: number;
}

interface ErrorResponse {
  success: false;
  error: {
    message: string;
    status?: number;
  };
}

interface SuccessResponse<T> {
  success: true;
  data: T;
}

type ApiResponse<T> = SuccessResponse<T> | ErrorResponse;

// Middleware to check if API keys are available
const checkApiKeys = async (req: Request, res: Response, next: Function) => {
  try {
    // Check if we have any active API keys
    const activeKeys = await storage.getActiveApiKeys();

    if (activeKeys.length === 0) {
      // Fall back to environment variables if no active keys
      const requiredVars = ['VIDIQ_TOKEN'];
      const missingVars = requiredVars.filter(varName => !process.env[varName]);

      if (missingVars.length > 0) {
        return res.status(500).json({
          success: false,
          error: {
            message: `No active API keys found and missing required environment variables: ${missingVars.join(', ')}`,
            status: 500
          }
        });
      }
    }

    next();
  } catch (error) {
    console.error('Error checking API keys:', error);
    return res.status(500).json({
      success: false,
      error: {
        message: 'Error checking API keys',
        status: 500
      }
    });
  }
};

// GET /api/vidiq/trendy - Get trendy videos
router.get('/trendy', checkApiKeys, async (req: Request, res: Response) => {
  try {
    const {
      query = [],
      country = 'US',
      language = 'en',
      contentDetails = false,
      page = 1,
      perPage = 20
    } = req.query;

    // Convert query to array if it's a string
    const queryArray = Array.isArray(query) ? query : [query].filter(Boolean);

    if (queryArray.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'At least one search query is required',
          status: 400
        }
      });
    }

    // Use the VidIQ service to get trendy videos
    const trendyResponse = await vidiqService.getTrendyVideos({
      keywords: queryArray as string[],
      country: country as string,
      language: language as string,
      contentDetails: contentDetails === 'true' || contentDetails === true,
      limit: Number(perPage)
    });

    // Format the response to match the expected structure
    const formattedResponse: VidIQTrendyResponse = {
      videos: trendyResponse.videos.map(video => ({
        id: video.id,
        title: video.title,
        channel: {
          id: video.channelId,
          title: video.channelTitle,
          thumbnail: ''
        },
        thumbnail: video.thumbnails?.high?.url || video.thumbnails?.medium?.url || '',
        viewCount: video.viewCount,
        publishedAt: video.publishedAt,
        score: video.score
      })),
      total: trendyResponse.totalCount,
      page: Number(page),
      perPage: Number(perPage)
    };

    // Return successful response
    return res.status(200).json({
      success: true,
      data: formattedResponse
    });
  } catch (error) {
    console.error('VidIQ API Error:', error);

    // Handle axios errors
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      return res.status(axiosError.response?.status || 500).json({
        success: false,
        error: {
          message: axiosError.response?.data?.message || 'Error fetching trendy videos',
          status: axiosError.response?.status || 500,
          details: axiosError.response?.data
        }
      });
    }

    // Handle other errors
    return res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        status: 500
      }
    });
  }
});

// GET /api/vidiq/trendy/:id - Get a specific trendy video by ID
router.get('/trendy/:id', checkApiKeys, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Video ID is required',
          status: 400
        }
      });
    }

    // Get headers from key manager
    const headers = await keyManager.getRequestHeaders();

    if (!headers) {
      return res.status(500).json({
        success: false,
        error: {
          message: 'No API keys available',
          status: 500
        }
      });
    }

    // Log which API key is being used
    const currentKey = keyManager.getCurrentKey();
    if (currentKey) {
      console.log(`Using VidIQ API Key #${currentKey.id} (${currentKey.name || 'Unnamed'}) for video details request`);
    }

    // Make request to VidIQ API for a specific video
    const response = await axios({
      method: 'GET',
      url: `https://api.vidiq.com/v0/videos/${id}`,
      headers
    });

    return res.status(200).json({
      success: true,
      data: response.data
    });
  } catch (error) {
    console.error('VidIQ API Error:', error);

    // Handle axios errors
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;

      // Check if it's a rate limit error (429 Too Many Requests)
      if (axiosError.response?.status === 429) {
        console.log('Rate limit exceeded for video details request, trying next API key...');

        // Try to rotate to the next API key
        const rotated = await keyManager.handleApiError(axiosError);

        if (rotated) {
          // Retry the request with the new key
          return res.status(503).json({
            success: false,
            error: {
              message: 'API key rate limited, please try again',
              status: 503,
              retryable: true
            }
          });
        }
      }

      return res.status(axiosError.response?.status || 500).json({
        success: false,
        error: {
          message: axiosError.response?.data?.message || 'Error fetching video details',
          status: axiosError.response?.status || 500,
          details: axiosError.response?.data
        }
      });
    }

    // Handle other errors
    return res.status(500).json({
      success: false,
      error: {
        message: 'Internal server error',
        status: 500
      }
    });
  }
});

export default router;

