import React, { useState } from 'react';
import { RssFeedItem } from '@shared/schema';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, BarChart, RefreshCw, Clock, TrendingUp } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { WordCloudVisualization } from './word-cloud';
import { TopicModeling } from './topic-modeling';
import { TemporalAnalysis } from './temporal-analysis';
import { GeographicAnalysis } from './geographic-analysis';
import { EntityRecognition } from './entity-recognition';
import { TrendingTopics } from './trending-topics';
// import { useToast } from '@/hooks/use-toast';

interface EnhancedDashboardProps {
  allFeedItems: RssFeedItem[];
  onRefresh?: () => void;
  lastRefreshed?: Date | null;
  isRefreshing?: boolean;
  onOpenArticle?: (articleId: number) => void;
  onSaveArticle?: (articleId: number) => void;
  onOpenInBrowser?: (articleId: number) => void;
}

export function EnhancedDashboard({
  allFeedItems,
  onRefresh,
  lastRefreshed = null,
  isRefreshing = false,
  onOpenArticle,
  onSaveArticle,
  onOpenInBrowser
}: EnhancedDashboardProps) {
  // Always start expanded by default
  const [isExpanded, setIsExpanded] = useState(true);
  const [activeTab, setActiveTab] = useState('wordcloud');

  // Always refresh the dashboard data when component mounts
  React.useEffect(() => {
    // Force a refresh when the component mounts, regardless of data
    if (onRefresh && !isRefreshing) {
      console.log('Forcing dashboard data refresh on mount');
      // Use setTimeout to ensure this runs after initial render
      setTimeout(() => {
        onRefresh();
      }, 100);
    }
  }, []);

  // Refresh the dashboard when allFeedItems changes
  React.useEffect(() => {
    if (onRefresh && !isRefreshing && allFeedItems.length > 0) {
      console.log('Refreshing dashboard due to feed items update');
      // Use setTimeout to ensure this runs after initial render
      setTimeout(() => {
        onRefresh();
      }, 100);
    }
  }, [allFeedItems.length]);

  // Handle article actions
  const handleOpenArticle = (articleId: number) => {
    if (onOpenArticle) {
      onOpenArticle(articleId);
    } else {
      console.error("The open article action is not available in this context.");
    }
  };

  const handleSaveArticle = (articleId: number) => {
    if (onSaveArticle) {
      onSaveArticle(articleId);
    } else {
      console.error("The save article action is not available in this context.");
    }
  };

  const handleOpenInBrowser = (articleId: number) => {
    if (onOpenInBrowser) {
      onOpenInBrowser(articleId);
    } else {
      console.error("The open in browser action is not available in this context.");
    }
  };

  return (
    <Card className="mt-6 w-full border-4 border-primary bg-primary/5">
      <CardHeader className="pb-2 cursor-pointer bg-primary/20" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="flex justify-between items-center">
          <CardTitle className="text-white flex items-center text-xl">
            <BarChart className="h-6 w-6 mr-2 text-primary" />
            Enhanced RSS Analytics Dashboard
          </CardTitle>
          <div className="flex items-center gap-2">
            {lastRefreshed && (
              <div className="flex items-center text-sm text-white mr-2">
                <Clock className="h-4 w-4 mr-1" />
                Last updated: {formatDistanceToNow(lastRefreshed, { addSuffix: true })}
              </div>
            )}
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onRefresh();
                }}
                disabled={isRefreshing}
                className="h-8 enhanced-dashboard-refresh-button"
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                {isRefreshing ? 'Refreshing...' : 'Refresh'}
              </Button>
            )}
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded(!isExpanded);
              }}
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
        </div>
        <CardDescription className="text-white text-base">
          Advanced analytics and visualizations for your RSS feeds
        </CardDescription>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-4">
          <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="wordcloud">
                <BarChart className="h-4 w-4 mr-1" />
                Word Cloud
              </TabsTrigger>
              <TabsTrigger value="trending">
                <TrendingUp className="h-4 w-4 mr-1" />
                Trending Insights
              </TabsTrigger>
              <TabsTrigger value="topics">
                <BarChart className="h-4 w-4 mr-1" />
                Topic Analysis
              </TabsTrigger>
              <TabsTrigger value="temporal">
                <Clock className="h-4 w-4 mr-1" />
                Temporal Analysis
              </TabsTrigger>
              <TabsTrigger value="geographic">Geographic Analysis</TabsTrigger>
              <TabsTrigger value="entities">Entity Recognition</TabsTrigger>
            </TabsList>

            <TabsContent value="wordcloud" className="space-y-4">
              <WordCloudVisualization
                feedItems={allFeedItems}
                onOpenArticle={handleOpenArticle}
                onSaveArticle={handleSaveArticle}
                onOpenInBrowser={handleOpenInBrowser}
              />
            </TabsContent>

            <TabsContent value="trending" className="space-y-4">
              <TrendingTopics
                feedItems={allFeedItems}
                onOpenArticle={handleOpenArticle}
                onSaveArticle={handleSaveArticle}
                onOpenInBrowser={handleOpenInBrowser}
              />
            </TabsContent>

            <TabsContent value="topics" className="space-y-4">
              <TopicModeling
                feedItems={allFeedItems}
                onOpenArticle={handleOpenArticle}
                onSaveArticle={handleSaveArticle}
                onOpenInBrowser={handleOpenInBrowser}
              />
            </TabsContent>

            <TabsContent value="temporal" className="space-y-4">
              <TemporalAnalysis
                feedItems={allFeedItems}
                onOpenArticle={handleOpenArticle}
                onSaveArticle={handleSaveArticle}
                onOpenInBrowser={handleOpenInBrowser}
              />
            </TabsContent>

            <TabsContent value="geographic" className="space-y-4">
              <GeographicAnalysis
                feedItems={allFeedItems}
                onOpenArticle={handleOpenArticle}
                onSaveArticle={handleSaveArticle}
                onOpenInBrowser={handleOpenInBrowser}
              />
            </TabsContent>

            <TabsContent value="entities" className="space-y-4">
              <EntityRecognition
                feedItems={allFeedItems}
                onOpenArticle={handleOpenArticle}
                onSaveArticle={handleSaveArticle}
                onOpenInBrowser={handleOpenInBrowser}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      )}
    </Card>
  );
}
