import express from 'express';
import { storage } from '../storage';
import { z } from 'zod';
import { validateRequest } from '../middleware/validate-request';
import { OpenRouterApiKeyExport, OpenRouterApiKeyExportData, OpenRouterApiKeyImportResult } from '@shared/schema';

const router = express.Router();

// Schema for creating a new API key
const createApiKeySchema = z.object({
  token: z.string().min(1, "API key token is required"),
  name: z.string().optional(),
  is_active: z.boolean().optional().default(true),
});

// Schema for updating an API key
const updateApiKeySchema = z.object({
  token: z.string().optional(),
  name: z.string().optional(),
  is_active: z.boolean().optional(),
});

// Get all OpenRouter API keys
router.get('/', async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const keys = await storage.getOpenRouterApiKeys(req.user.id);
    res.json(keys);
  } catch (error) {
    console.error('Error fetching OpenRouter API keys:', error);
    res.status(500).json({ error: 'Failed to fetch OpenRouter API keys' });
  }
});

// Create a new OpenRouter API key
router.post('/', validateRequest(createApiKeySchema), async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const { token, name, is_active } = req.body;
    const newKey = await storage.addOpenRouterApiKey(req.user.id, token, name, is_active);
    res.status(201).json(newKey);
  } catch (error) {
    console.error('Error creating OpenRouter API key:', error);
    res.status(500).json({ error: 'Failed to create OpenRouter API key' });
  }
});

// Update an OpenRouter API key
router.put('/:id', validateRequest(updateApiKeySchema), async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid API key ID' });
    }

    // Check if the key belongs to the user
    const key = await storage.getOpenRouterApiKey(id);
    if (!key || key.user_id !== req.user.id) {
      return res.status(404).json({ error: 'API key not found' });
    }

    const updatedKey = await storage.updateOpenRouterApiKey(id, req.body);
    res.json(updatedKey);
  } catch (error) {
    console.error('Error updating OpenRouter API key:', error);
    res.status(500).json({ error: 'Failed to update OpenRouter API key' });
  }
});

// Delete an OpenRouter API key
router.delete('/:id', async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid API key ID' });
    }

    // Check if the key belongs to the user
    const key = await storage.getOpenRouterApiKey(id);
    if (!key || key.user_id !== req.user.id) {
      return res.status(404).json({ error: 'API key not found' });
    }

    await storage.deleteOpenRouterApiKey(id);
    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting OpenRouter API key:', error);
    res.status(500).json({ error: 'Failed to delete OpenRouter API key' });
  }
});

// Test an OpenRouter API key
router.post('/:id/test', async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid API key ID' });
    }

    // Check if the key belongs to the user
    const key = await storage.getOpenRouterApiKey(id);
    if (!key || key.user_id !== req.user.id) {
      return res.status(404).json({ error: 'API key not found' });
    }

    // Test the API key by making a simple request to OpenRouter
    const testResult = await storage.testOpenRouterApiKey(id);

    // If rate limit was detected, mark the key as exhausted
    if (testResult.rateLimitInfo?.detected && testResult.rateLimitInfo.resetTime) {
      const resetTime = new Date(testResult.rateLimitInfo.resetTime);
      const now = new Date();
      const minutesUntilReset = Math.ceil((resetTime.getTime() - now.getTime()) / (60 * 1000));

      if (minutesUntilReset > 0) {
        console.log(`Marking key ${id} as exhausted for ${minutesUntilReset} minutes due to rate limit test`);
        await storage.markOpenRouterApiKeyExhausted(
          id,
          minutesUntilReset,
          testResult.rateLimitInfo.type || 'test-detected'
        );
      }
    }

    res.json(testResult);
  } catch (error) {
    console.error('Error testing OpenRouter API key:', error);
    res.status(500).json({ error: 'Failed to test OpenRouter API key' });
  }
});

// Schema for batch import of API keys
const batchImportSchema = z.object({
  keys: z.array(z.object({
    token: z.string().min(1, "API key token is required"),
    name: z.string().optional(),
    is_active: z.boolean().optional().default(true),
    is_default: z.boolean().optional().default(false),
  })).min(1, "At least one API key is required"),
});

// Batch import OpenRouter API keys
router.post('/batch-import', validateRequest(batchImportSchema), async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const { keys } = req.body;
    const result: OpenRouterApiKeyImportResult = {
      success: true,
      message: 'Import completed',
      imported: 0,
      failed: 0,
      errors: [],
    };

    // Process each key
    for (const key of keys) {
      try {
        // Check if this is a default key
        const isDefault = key.is_default || false;

        // Add the key
        const newKey = await storage.addOpenRouterApiKey(
          req.user.id,
          key.token,
          key.name,
          key.is_active
        );

        // If this should be the default key, update it
        if (isDefault) {
          await storage.updateOpenRouterApiKey(newKey.id, { is_default: true });
        }

        result.imported++;
      } catch (error) {
        result.failed++;
        result.errors.push(`Failed to import key ${key.name || 'unnamed'}: ${error.message}`);
      }
    }

    // Set overall success based on whether any keys were imported
    result.success = result.imported > 0;
    result.message = result.imported > 0
      ? `Successfully imported ${result.imported} keys${result.failed > 0 ? `, ${result.failed} failed` : ''}`
      : 'Failed to import any keys';

    res.json(result);
  } catch (error) {
    console.error('Error importing OpenRouter API keys:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to import API keys',
      imported: 0,
      failed: 0,
      errors: [error.message],
    });
  }
});

// Export all OpenRouter API keys for the current user
router.get('/export', async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get all keys for the user
    const keys = await storage.getOpenRouterApiKeys(req.user.id);

    // Get the actual tokens (we need to fetch each key individually to get the token)
    const keysWithTokens: OpenRouterApiKeyExport[] = [];

    for (const key of keys) {
      try {
        const fullKey = await storage.getOpenRouterApiKey(key.id);
        if (fullKey && fullKey.token) {
          keysWithTokens.push({
            name: key.name || `API Key ${key.id}`,
            token: fullKey.token,
            is_active: key.is_active,
            is_default: key.is_default || false,
          });
        }
      } catch (error) {
        console.error(`Error fetching token for key ${key.id}:`, error);
      }
    }

    // Create the export data
    const exportData: OpenRouterApiKeyExportData = {
      version: '1.0',
      exported_at: new Date().toISOString(),
      keys: keysWithTokens,
    };

    res.json(exportData);
  } catch (error) {
    console.error('Error exporting OpenRouter API keys:', error);
    res.status(500).json({ error: 'Failed to export API keys' });
  }
});

// Reset all exhausted API keys
router.post('/reset-exhausted', async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Get all exhausted keys for the user
    const stmt = storage.db.prepare(`
      SELECT COUNT(*) as count
      FROM openrouter_api_keys
      WHERE user_id = ?
      AND is_active = 1
      AND exhausted_until IS NOT NULL
      AND exhausted_until > CURRENT_TIMESTAMP
    `);

    const { count } = stmt.get(req.user.id);

    if (count === 0) {
      return res.json({
        success: true,
        message: 'No exhausted API keys found',
        resetCount: 0
      });
    }

    // Reset all exhausted keys for the user
    const resetStmt = storage.db.prepare(`
      UPDATE openrouter_api_keys
      SET exhausted_until = NULL
      WHERE user_id = ?
      AND is_active = 1
      AND exhausted_until IS NOT NULL
      AND exhausted_until > CURRENT_TIMESTAMP
    `);

    const result = resetStmt.run(req.user.id);
    console.log(`Reset ${result.changes} exhausted OpenRouter API keys for user ${req.user.id}`);

    res.json({
      success: true,
      message: `Successfully reset ${result.changes} exhausted API keys`,
      resetCount: result.changes
    });
  } catch (error) {
    console.error('Error resetting exhausted OpenRouter API keys:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset exhausted API keys',
      error: error.message
    });
  }
});

export default router;
