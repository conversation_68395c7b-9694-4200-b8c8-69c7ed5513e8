import Database from 'better-sqlite3';

/**
 * Simple script to fix the settings table by removing ollama_model column
 */
async function fixSettingsTable() {
  console.log('Starting to fix settings table...');

  try {
    // Connect to the database
    const db = new Database('../data.db');

    // Check if the settings table exists
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'").all();
    
    if (tables.length === 0) {
      console.log('Settings table does not exist. No fix needed.');
      db.close();
      return;
    }

    // Get the current schema
    const columns = db.prepare("PRAGMA table_info(settings)").all();
    console.log('Current settings table columns:', columns.map(c => c.name).join(', '));
    
    // Check if ollama_model column exists
    const hasOllamaModel = columns.some(col => col.name === 'ollama_model');
    const hasOllamaAnalysisPrompt = columns.some(col => col.name === 'ollama_analysis_prompt');
    
    if (!hasOllamaModel && !hasOllamaAnalysisPrompt) {
      console.log('No Ollama columns found in settings table. No fix needed.');
      db.close();
      return;
    }

    console.log('Creating new settings table without Ollama columns...');
    
    // Create a new table without the Ollama columns
    const columnsToKeep = columns
      .filter(col => col.name !== 'ollama_model' && col.name !== 'ollama_analysis_prompt')
      .map(col => col.name);
    
    // Create the SQL for the new table
    const createTableSQL = `
      CREATE TABLE settings_new (
        ${columnsToKeep.map(colName => {
          const col = columns.find(c => c.name === colName);
          return `${colName} ${col.type}${col.notnull ? ' NOT NULL' : ''}${col.dflt_value ? ` DEFAULT ${col.dflt_value}` : ''}${col.pk ? ' PRIMARY KEY' : ''}`;
        }).join(',\n        ')}
      );
    `;
    
    console.log('Creating new table with SQL:', createTableSQL);
    db.exec(createTableSQL);
    
    // Copy data from the old table to the new table
    const insertSQL = `
      INSERT INTO settings_new
      SELECT ${columnsToKeep.join(', ')}
      FROM settings;
    `;
    
    console.log('Copying data with SQL:', insertSQL);
    db.exec(insertSQL);
    
    // Drop the old table and rename the new one
    console.log('Replacing old table with new table...');
    db.exec(`
      DROP TABLE settings;
      ALTER TABLE settings_new RENAME TO settings;
    `);
    
    // Add back the index
    console.log('Adding index on user_id...');
    db.exec(`
      CREATE INDEX idx_settings_user_id ON settings(user_id);
    `);
    
    console.log('Settings table fixed successfully!');
    
    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error fixing settings table:', error);
  }
}

// Run the function
fixSettingsTable();
