import { Router } from 'express';
import { storage } from '../storage';
import { z } from 'zod';
import { SavedArticleList, SavedArticle, RssFeedItem } from '@shared/schema';
import { generateRssFeedPdf } from '../utils/pdf-generator-new';
import { generateRssFeedText } from '../utils/text-generator';

const router = Router();

// Ensure user is authenticated for all routes
router.use((req, res, next) => {
  if (req.session && req.session.passport && req.session.passport.user) {
    return next();
  }
  res.status(401).json({ error: 'Unauthorized' });
});

// Schema for creating a saved article list
const createSavedArticleListSchema = z.object({
  name: z.string().min(1, 'List name is required'),
});

// Schema for updating a saved article list
const updateSavedArticleListSchema = z.object({
  name: z.string().min(1, 'List name is required'),
});

// Schema for adding an article to a saved list
const addSavedArticleSchema = z.object({
  articleId: z.number(),
});

// Get all saved article lists for the current user
router.get('/', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const lists = await storage.getSavedArticleLists(userId);
    res.json(lists);
  } catch (error) {
    console.error('Error getting saved article lists:', error);
    res.status(500).json({ error: 'Failed to get saved article lists' });
  }
});

// Create a new saved article list
router.post('/', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const validatedData = createSavedArticleListSchema.parse(req.body);
    const list = await storage.createSavedArticleList(userId, validatedData.name);

    res.status(201).json(list);
  } catch (error) {
    console.error('Error creating saved article list:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: 'Failed to create saved article list' });
  }
});

// Get a specific saved article list
router.get('/:id', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const listId = parseInt(req.params.id);
    if (isNaN(listId)) {
      return res.status(400).json({ error: 'Invalid list ID' });
    }

    const list = await storage.getSavedArticleList(listId);
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Check if the list belongs to the current user
    if (list.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    res.json(list);
  } catch (error) {
    console.error('Error getting saved article list:', error);
    res.status(500).json({ error: 'Failed to get saved article list' });
  }
});

// Update a specific saved article list
router.patch('/:id', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const listId = parseInt(req.params.id);
    if (isNaN(listId)) {
      return res.status(400).json({ error: 'Invalid list ID' });
    }

    const list = await storage.getSavedArticleList(listId);
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Check if the list belongs to the current user
    if (list.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    const validatedData = updateSavedArticleListSchema.parse(req.body);
    const updatedList = await storage.updateSavedArticleList(listId, validatedData.name);

    res.json(updatedList);
  } catch (error) {
    console.error('Error updating saved article list:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: 'Failed to update saved article list' });
  }
});

// Delete a specific saved article list
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const listId = parseInt(req.params.id);
    if (isNaN(listId)) {
      return res.status(400).json({ error: 'Invalid list ID' });
    }

    const list = await storage.getSavedArticleList(listId);
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Check if the list belongs to the current user
    if (list.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    await storage.deleteSavedArticleList(listId);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting saved article list:', error);
    res.status(500).json({ error: 'Failed to delete saved article list' });
  }
});

// Get all articles in a saved list
router.get('/:id/articles', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const listId = parseInt(req.params.id);
    if (isNaN(listId)) {
      return res.status(400).json({ error: 'Invalid list ID' });
    }

    const list = await storage.getSavedArticleList(listId);
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Check if the list belongs to the current user
    if (list.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    const articles = await storage.getSavedArticles(listId);
    res.json(articles);
  } catch (error) {
    console.error('Error getting saved articles:', error);
    res.status(500).json({ error: 'Failed to get saved articles' });
  }
});

// Add an article to a saved list
router.post('/:id/articles', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const listId = parseInt(req.params.id);
    if (isNaN(listId)) {
      return res.status(400).json({ error: 'Invalid list ID' });
    }

    const list = await storage.getSavedArticleList(listId);
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Check if the list belongs to the current user
    if (list.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    const validatedData = addSavedArticleSchema.parse(req.body);

    // Get the article from the RSS feed
    const article = await storage.getRssFeedItem(validatedData.articleId);
    if (!article) {
      return res.status(404).json({ error: 'Article not found' });
    }

    // Add the article to the saved list
    const savedArticle = await storage.addSavedArticle(listId, article);

    res.status(201).json(savedArticle);
  } catch (error) {
    console.error('Error adding article to saved list:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: 'Failed to add article to saved list' });
  }
});

// Remove an article from a saved list
router.delete('/:listId/articles/:articleId', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const listId = parseInt(req.params.listId);
    if (isNaN(listId)) {
      return res.status(400).json({ error: 'Invalid list ID' });
    }

    const articleId = parseInt(req.params.articleId);
    if (isNaN(articleId)) {
      return res.status(400).json({ error: 'Invalid article ID' });
    }

    const list = await storage.getSavedArticleList(listId);
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Check if the list belongs to the current user
    if (list.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    await storage.removeSavedArticle(articleId);
    res.status(204).send();
  } catch (error) {
    console.error('Error removing article from saved list:', error);
    res.status(500).json({ error: 'Failed to remove article from saved list' });
  }
});

// Generate PDF from saved articles
router.get('/:id/pdf', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const listId = parseInt(req.params.id);
    if (isNaN(listId)) {
      return res.status(400).json({ error: 'Invalid list ID' });
    }

    const list = await storage.getSavedArticleList(listId);
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Check if the list belongs to the current user
    if (list.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Get all articles in the list
    const articles = await storage.getSavedArticles(listId);

    if (articles.length === 0) {
      return res.status(400).json({ error: 'No articles in the list' });
    }

    // Convert SavedArticle[] to RssFeedItem[] for the PDF generator
    const rssFeedItems: RssFeedItem[] = articles.map(article => ({
      id: article.id,
      feedId: article.feedId,
      title: article.title,
      link: article.link,
      publishedAt: article.publishedAt,
      content: article.content,
      scrapedContent: article.scrapedContent,
      isRead: false
    }));

    // Generate PDF
    const pdfBuffer = await generateRssFeedPdf(list.name, rssFeedItems);

    // Set headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(list.name)}.pdf"`);

    // Send the PDF
    res.send(pdfBuffer);
  } catch (error) {
    console.error('Error generating PDF from saved articles:', error);
    res.status(500).json({ error: 'Failed to generate PDF' });
  }
});

// Generate text from saved articles
router.get('/:id/text', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const listId = parseInt(req.params.id);
    if (isNaN(listId)) {
      return res.status(400).json({ error: 'Invalid list ID' });
    }

    const list = await storage.getSavedArticleList(listId);
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Check if the list belongs to the current user
    if (list.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Get all articles in the list
    const articles = await storage.getSavedArticles(listId);

    if (articles.length === 0) {
      return res.status(400).json({ error: 'No articles in the list' });
    }

    // Convert SavedArticle[] to RssFeedItem[] for the text generator
    const rssFeedItems: RssFeedItem[] = articles.map(article => ({
      id: article.id,
      feedId: article.feedId,
      title: article.title,
      link: article.link,
      publishedAt: article.publishedAt,
      content: article.content,
      scrapedContent: article.scrapedContent,
      isRead: false
    }));

    // Generate text
    const text = await generateRssFeedText(list.name, rssFeedItems);

    // Send the text
    res.json({ text });
  } catch (error) {
    console.error('Error generating text from saved articles:', error);
    res.status(500).json({ error: 'Failed to generate text' });
  }
});

// Export articles from a saved list
router.get('/:id/export', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const listId = parseInt(req.params.id);
    if (isNaN(listId)) {
      return res.status(400).json({ error: 'Invalid list ID' });
    }

    const list = await storage.getSavedArticleList(listId);
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Check if the list belongs to the current user
    if (list.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Get all articles in the list
    const articles = await storage.getSavedArticles(listId);

    if (articles.length === 0) {
      return res.status(400).json({ error: 'No articles in the list' });
    }

    // Create export data structure
    const exportData = {
      version: '1.0',
      exportedAt: new Date().toISOString(),
      list: {
        name: list.name,
        createdAt: list.createdAt,
      },
      articles: articles.map(article => ({
        title: article.title,
        link: article.link,
        publishedAt: article.publishedAt,
        content: article.content,
        scrapedContent: article.scrapedContent,
        addedAt: article.addedAt
      }))
    };

    // Set headers for JSON download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(list.name)}_export.json"`);

    // Send the JSON data
    res.json(exportData);
  } catch (error) {
    console.error('Error exporting saved articles:', error);
    res.status(500).json({ error: 'Failed to export saved articles' });
  }
});

// Import articles to a saved list
router.post('/:id/import', async (req, res) => {
  try {
    const userId = req.session?.passport?.user;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const listId = parseInt(req.params.id);
    if (isNaN(listId)) {
      return res.status(400).json({ error: 'Invalid list ID' });
    }

    const list = await storage.getSavedArticleList(listId);
    if (!list) {
      return res.status(404).json({ error: 'List not found' });
    }

    // Check if the list belongs to the current user
    if (list.userId !== userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Parse the JSON data from the request body
    let importData;
    try {
      importData = req.body;
    } catch (e) {
      return res.status(400).json({ error: 'Invalid JSON data' });
    }

    // Validate the import data structure
    if (!importData.version || !importData.articles || !Array.isArray(importData.articles)) {
      return res.status(400).json({ error: 'Invalid import file format' });
    }

    // Import the articles
    const results = {
      success: true,
      imported: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (const article of importData.articles) {
      try {
        // Create a mock RssFeedItem to use with the existing addSavedArticle method
        const mockArticle: RssFeedItem = {
          id: 0, // This will be ignored by the storage method
          feedId: list.id, // Use the list ID as a placeholder for the feed ID
          title: article.title,
          link: article.link,
          publishedAt: new Date(article.publishedAt),
          content: article.content,
          scrapedContent: article.scrapedContent,
          isRead: false
        };

        await storage.addSavedArticle(listId, mockArticle);
        results.imported++;
      } catch (error) {
        console.error('Error importing article:', error);
        results.failed++;
        results.errors.push(`Failed to import article: ${article.title}`);
      }
    }

    res.json(results);
  } catch (error) {
    console.error('Error importing saved articles:', error);
    res.status(500).json({ error: 'Failed to import saved articles' });
  }
});

export default router;
