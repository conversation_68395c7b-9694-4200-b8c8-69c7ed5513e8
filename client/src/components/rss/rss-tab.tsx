import React, { useState, useEffect, useRef } from 'react';
import { RssFeed, RssFeedItem, SavedArticleList } from '@shared/schema';
import { SavedArticleLists } from './saved-article-lists';
import { SavedArticlesView } from './saved-articles-view';
import { EnhancedDashboard } from './enhanced-analytics/enhanced-dashboard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import './rss-styles.css';
import { useNetworkStatus } from '@/hooks/use-network-status';
import { useSavedArticleLists, useSavedArticles } from '@/hooks/use-saved-articles';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Plus, Trash2, RefreshCw, ExternalLink, Eye, EyeOff, FileDown, Upload, Download, ChevronRight, ChevronDown, Pencil, Copy, Clock, Link2, GripVertical, Settings, Search, X, BarChart } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow, format } from 'date-fns';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';

// Import the utility functions from rss-utils.ts
import { decodeHtmlEntities, processHtmlContent } from './rss-utils';

// Helper function to handle article not found (404) errors
const handleArticleNotFound = (itemId: number) => {
  console.log(`Article with ID ${itemId} not found, it may have been deleted during a feed refresh`);

  // Remove the item from the local state
  setFeedItems(feedItems.filter(feedItem => feedItem.id !== itemId));

  // If this was the selected item, clear the selection
  if (selectedItem?.id === itemId) {
    setSelectedItem(null);
    setArticleContent('');
  }

  // Refresh the feed items to get the latest state
  if (selectedFeedId) {
    fetchFeedItems(selectedFeedId);
  }

  toast({
    title: 'Info',
    description: 'This article may have been updated during feed refresh. The feed has been refreshed.',
  });
};


interface RssTabProps {
  onRefresh?: (refreshTime: Date) => void;
}

export function RssTab({ onRefresh }: RssTabProps) {
  const [feeds, setFeeds] = useState<RssFeed[]>([]);
  const [feedItems, setFeedItems] = useState<RssFeedItem[]>([]);
  const [isAddFeedDialogOpen, setIsAddFeedDialogOpen] = useState(false);
  const [isEditFeedDialogOpen, setIsEditFeedDialogOpen] = useState(false);
  const [newFeedName, setNewFeedName] = useState('');
  const [newFeedUrl, setNewFeedUrl] = useState('');
  const [editFeedId, setEditFeedId] = useState<number | null>(null);
  const [editFeedName, setEditFeedName] = useState('');
  const [editFeedUrl, setEditFeedUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshingAll, setIsRefreshingAll] = useState(false);
  const [refreshingFeedId, setRefreshingFeedId] = useState<number | null>(null);
  const [selectedFeedId, setSelectedFeedId] = useState<number | null>(null);
  const [selectedItem, setSelectedItem] = useState<RssFeedItem | null>(null);
  const [articleContent, setArticleContent] = useState<string>('');
  const [isLoadingArticle, setIsLoadingArticle] = useState(false);
  const [isFeedsCollapsed, setIsFeedsCollapsed] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const [feedArticleCounts, setFeedArticleCounts] = useState<Record<number, number>>({});
  const [isRefreshingFeed, setIsRefreshingFeed] = useState(false);
  const [isReorderMode, setIsReorderMode] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;
  const [activeTab, setActiveTab] = useState<'feeds' | 'saved' | 'settings'>('feeds');
  const [selectedSavedListId, setSelectedSavedListId] = useState<number | null>(null);
  const [selectedSavedListName, setSelectedSavedListName] = useState<string>('');
  const [isSaveToListDialogOpen, setIsSaveToListDialogOpen] = useState(false);
  const [selectedSaveListId, setSelectedSaveListId] = useState<number | null>(null);

  // Sorting options
  type SortOption = 'newest' | 'benefit' | 'positive-terms';
  const [sortOption, setSortOption] = useState<SortOption>('newest'); // Default to newest first

  // State for the delete oldest articles dialog
  const [isDeleteOldestDialogOpen, setIsDeleteOldestDialogOpen] = useState(false);
  const [deleteOldestFeedId, setDeleteOldestFeedId] = useState<number | null>(null);
  const [deleteOldestCount, setDeleteOldestCount] = useState(10);

  // Search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<RssFeedItem[]>([]);
  const [isSearchActive, setIsSearchActive] = useState(false);

  // Dashboard functionality
  const [allFeedItems, setAllFeedItems] = useState<RssFeedItem[]>([]);
  const { toast } = useToast();

  // Get saved lists and articles
  const savedArticleLists = useSavedArticleLists();
  const savedLists = savedArticleLists.lists;
  const createList = savedArticleLists.createList;

  const savedArticles = useSavedArticles(selectedSaveListId);
  const addArticle = savedArticles.addArticle;

  // Refs for scroll position management
  const selectedItemRef = useRef<HTMLDivElement | null>(null);
  const feedItemsScrollAreaRef = useRef<HTMLDivElement | null>(null);

  // Fetch article counts for all feeds - optimized version with debugging
  const fetchFeedArticleCounts = useRef(async () => {
    if (feeds.length === 0) return;

    try {
      console.log('Fetching article counts for all feeds...');
      // Use a single API call to get counts for all feeds
      const response = await apiRequest('GET', '/api/rss-feeds/counts');

      if (response.ok) {
        const counts = await response.json();
        console.log('Received article counts:', counts);

        // Convert string counts to numbers if needed
        const numericCounts: Record<number, number> = {};
        Object.keys(counts).forEach(key => {
          const feedId = parseInt(key);
          numericCounts[feedId] = parseInt(counts[key]) || 0;
        });

        setFeedArticleCounts(numericCounts);
        console.log('Updated article counts state:', numericCounts);
      } else {
        console.error('Error fetching feed article counts:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching feed article counts:', error);
    }
  }).current;

  // Handle feed reordering
  const handleFeedReorder = async (result: DropResult) => {
    if (!result.destination) return;

    const { source, destination } = result;

    // Don't do anything if the position didn't change
    if (source.index === destination.index) return;

    // Create a copy of the feeds array
    const reorderedFeeds = Array.from(feeds);

    // Remove the feed from the source position
    const [removed] = reorderedFeeds.splice(source.index, 1);

    // Insert the feed at the destination position
    reorderedFeeds.splice(destination.index, 0, removed);

    // Update the displayOrder property for each feed
    const updatedFeeds = reorderedFeeds.map((feed, index) => ({
      ...feed,
      displayOrder: index + 1
    }));

    // Update the state immediately for a responsive UI
    setFeeds(updatedFeeds);

    try {
      // Prepare the data for the API request
      const feedOrders = updatedFeeds.map(feed => ({
        id: feed.id,
        displayOrder: feed.displayOrder || 0
      }));

      // Send the updated order to the server
      const response = await apiRequest('POST', '/api/rss-feeds/update-order', feedOrders);

      if (!response.ok) {
        throw new Error('Failed to update feed order');
      }

      // Update the feeds with the response from the server
      const data = await response.json();
      const processedFeeds = data.map((feed: RssFeed) => ({
        ...feed,
        lastRefreshTime: feed.lastRefreshTime ? new Date(feed.lastRefreshTime) : undefined
      }));

      setFeeds(processedFeeds);

      toast({
        title: 'Success',
        description: 'Feed order updated successfully',
      });
    } catch (error) {
      console.error('Error updating feed order:', error);
      toast({
        title: 'Error',
        description: 'Failed to update feed order',
        variant: 'destructive',
      });

      // Fetch the feeds again to restore the original order
      fetchFeeds();
    }
  };

  // Search across all feeds
  const searchAllFeeds = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: "Search Error",
        description: "Please enter a search term",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSearching(true);
      setIsSearchActive(true);

      // Collect all feed IDs
      const feedIds = feeds.map(feed => feed.id);

      // Initialize an array to store all items
      let allItems: RssFeedItem[] = [];

      // Fetch items from all feeds
      for (const feedId of feedIds) {
        const response = await apiRequest('GET', `/api/rss-feeds/${feedId}/items`);

        if (!response.ok) {
          console.error(`Failed to fetch items for feed ${feedId}: ${response.status} ${response.statusText}`);
          continue;
        }

        const items = await response.json();
        allItems = [...allItems, ...items];
      }

      // Perform the search
      const query = searchQuery.toLowerCase();
      const results = allItems.filter(item => {
        const titleMatch = item.title?.toLowerCase().includes(query);
        const contentMatch = item.content?.toLowerCase().includes(query);
        const scrapedContentMatch = item.scrapedContent?.toLowerCase().includes(query);

        return titleMatch || contentMatch || scrapedContentMatch;
      });

      // Sort results by newest first
      results.sort((a, b) => {
        const dateA = new Date(a.publishedAt).getTime();
        const dateB = new Date(b.publishedAt).getTime();
        return dateB - dateA;
      });

      setSearchResults(results);

      // Show toast with results count
      toast({
        title: "Search Results",
        description: `Found ${results.length} matching articles`,
      });

    } catch (error) {
      console.error('Error searching feeds:', error);
      toast({
        title: "Search Error",
        description: "Failed to search feeds",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Clear search results and return to normal view
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setIsSearchActive(false);
  };

  // Fetch RSS feeds
  const fetchFeeds = async () => {
    try {
      setIsLoading(true);
      const response = await apiRequest('GET', '/api/rss-feeds');
      const data = await response.json();

      // Process the feeds to ensure lastRefreshTime is a Date object
      const processedFeeds = data.map((feed: RssFeed) => ({
        ...feed,
        lastRefreshTime: feed.lastRefreshTime ? new Date(feed.lastRefreshTime) : undefined
      }));

      setFeeds(processedFeeds);

      // If we have feeds, set the last global refresh time
      if (processedFeeds.length > 0) {
        // Find the most recent refresh time among all feeds
        const mostRecentRefresh = processedFeeds
          .filter(feed => feed.lastRefreshTime)
          .sort((a, b) =>
            (b.lastRefreshTime?.getTime() || 0) - (a.lastRefreshTime?.getTime() || 0)
          )[0]?.lastRefreshTime;

        if (mostRecentRefresh) {
          setLastRefreshTime(mostRecentRefresh);

          // Call the onRefresh callback if provided
          if (onRefresh) {
            onRefresh(mostRecentRefresh);
          }
        }

        // After fetching feeds, fetch article counts
        setTimeout(() => {
          fetchFeedArticleCounts();
        }, 100);
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching RSS feeds:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch RSS feeds',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Fetch all feed items for the dashboard
  const fetchAllFeedItems = async (isInitialLoad = false) => {
    try {
      console.log('Fetching all feed items for dashboard from', feeds.length, 'feeds');
      if (feeds.length === 0) {
        console.log('No feeds available, skipping dashboard data fetch');
        return;
      }

      setIsRefreshingAll(true);
      let allItems: RssFeedItem[] = [];

      // Fetch items from all feeds
      for (const feed of feeds) {
        console.log(`Fetching items for feed ${feed.id} (${feed.name})`);
        try {
          const response = await apiRequest('GET', `/api/rss-feeds/${feed.id}/items`);

          if (!response.ok) {
            console.error(`Failed to fetch items for feed ${feed.id}: ${response.status} ${response.statusText}`);
            continue;
          }

          const items = await response.json();
          console.log(`Retrieved ${items.length} items from feed ${feed.id}`);

          // Process items to ensure they have all required properties
          const processedItems = items.map((item: RssFeedItem) => {
            if (item.title) item.title = processHtmlContent(item.title);
            if (item.content) item.content = processHtmlContent(item.content);
            return item;
          });

          allItems = [...allItems, ...processedItems];
        } catch (feedError) {
          console.error(`Error fetching items for feed ${feed.id}:`, feedError);
          // Continue with other feeds even if one fails
        }
      }

      console.log(`Total items collected for dashboard: ${allItems.length}`);

      // Only update if we actually got items
      if (allItems.length > 0) {
        setAllFeedItems(allItems);
        setLastRefreshTime(new Date());

        if (!isInitialLoad) {
          toast({
            title: 'Success',
            description: `Dashboard refreshed with ${allItems.length} articles`,
          });
        } else {
          console.log(`Dashboard initialized with ${allItems.length} articles`);
        }
      } else {
        console.warn('No items found for dashboard from any feeds');
        if (!isInitialLoad) {
          toast({
            title: 'Warning',
            description: 'No articles found for dashboard',
            variant: 'destructive',
          });
        }
      }
    } catch (error) {
      console.error('Error fetching all feed items:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch RSS feed items for dashboard',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setIsRefreshingAll(false);
    }
  };

  // Sort feed items based on the current sort option
  const getSortedFeedItems = (items: RssFeedItem[], option: SortOption = sortOption): RssFeedItem[] => {
    console.log(`Sorting ${items.length} items by: ${option}`);

    // Create a copy of the items to avoid modifying the original array
    const itemsToSort = [...items];

    // Log a sample of items to debug
    if (itemsToSort.length > 0) {
      console.log('Sample item:', {
        id: itemsToSort[0].id,
        title: itemsToSort[0].title?.substring(0, 30) || 'No title',
        publishedAt: itemsToSort[0].publishedAt,
        financialBenefitAmount: itemsToSort[0].financialBenefitAmount || 0
      });
    }

    if (option === 'newest') {
      // Sort by published date (newest first)
      console.log('Sorting by newest first');
      return itemsToSort.sort((a, b) => {
        const dateA = new Date(a.publishedAt).getTime();
        const dateB = new Date(b.publishedAt).getTime();
        return dateB - dateA;
      });
    } else if (option === 'benefit') {
      // Sort by financial benefit amount (highest first)
      console.log('Sorting by highest benefit first');

      // Log benefit amounts to debug
      const benefitAmounts = itemsToSort.slice(0, 5).map(item => ({
        id: item.id,
        title: item.title?.substring(0, 30) || 'No title',
        benefit: item.financialBenefitAmount || 0
      }));
      console.log('Benefit amounts (first 5):', benefitAmounts);

      return itemsToSort.sort((a, b) => {
        // First sort by benefit amount (highest first)
        const benefitA = a.financialBenefitAmount || 0;
        const benefitB = b.financialBenefitAmount || 0;
        const benefitDiff = benefitB - benefitA;

        // If benefits are equal, sort by date (newest first)
        if (benefitDiff === 0) {
          const dateA = new Date(a.publishedAt).getTime();
          const dateB = new Date(b.publishedAt).getTime();
          return dateB - dateA;
        }

        return benefitDiff;
      });
    } else if (option === 'positive-terms') {
      // Sort by number of positive financial terms (highest first)
      console.log('Sorting by most positive terms first');

      // Create a map to store positive term counts for each item
      const positiveTermCounts = new Map<number, number>();

      // First, try to extract positive term counts from data attributes in the DOM
      let foundCountsInDOM = false;

      itemsToSort.forEach(item => {
        // Get the element with the item's ID
        const itemElement = document.querySelector(`[data-id="${item.id}"]`);
        if (itemElement) {
          // Get the positive count from the data attribute
          const positiveCount = parseInt(itemElement.getAttribute('data-positive-count') || '0', 10);
          positiveTermCounts.set(item.id, positiveCount);
          foundCountsInDOM = true;
        } else {
          // If element not found, default to 0
          positiveTermCounts.set(item.id, 0);
        }
      });

      // If we couldn't find any counts in the DOM, try to extract them from the title content
      if (!foundCountsInDOM) {
        console.log('No positive counts found in DOM, extracting from title content');
        itemsToSort.forEach(item => {
          if (item.title) {
            // Use a temporary element to parse the title HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = item.title;

            // Look for the data attribute in the title content
            const countDiv = tempDiv.querySelector('div[data-positive-count]');
            if (countDiv) {
              const positiveCount = parseInt(countDiv.getAttribute('data-positive-count') || '0', 10);
              positiveTermCounts.set(item.id, positiveCount);
            } else {
              positiveTermCounts.set(item.id, 0);
            }
          } else {
            positiveTermCounts.set(item.id, 0);
          }
        });
      }

      // Log positive term counts to debug
      console.log('Positive term counts (first 5):',
        Array.from(positiveTermCounts.entries()).slice(0, 5).map(([id, count]) => ({
          id,
          count,
          title: itemsToSort.find(item => item.id === id)?.title?.substring(0, 30) || 'No title'
        }))
      );

      return itemsToSort.sort((a, b) => {
        // First sort by positive term count (highest first)
        const countA = positiveTermCounts.get(a.id) || 0;
        const countB = positiveTermCounts.get(b.id) || 0;
        const countDiff = countB - countA;

        // If counts are equal, sort by date (newest first)
        if (countDiff === 0) {
          const dateA = new Date(a.publishedAt).getTime();
          const dateB = new Date(b.publishedAt).getTime();
          return dateB - dateA;
        }

        return countDiff;
      });
    }

    // Default to newest first
    console.log('Using default sort (newest first)');
    return itemsToSort.sort((a, b) => {
      const dateA = new Date(a.publishedAt).getTime();
      const dateB = new Date(b.publishedAt).getTime();
      return dateB - dateA;
    });
  };

  // Fetch RSS feed items
  const fetchFeedItems = async (feedId: number) => {
    try {
      console.log(`Fetching items for feed ID: ${feedId}`);
      setIsLoading(true);

      // Store the current feed items to restore in case of error
      const currentFeedItems = [...feedItems];

      // Make the API request with a longer timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await apiRequest('GET', `/api/rss-feeds/${feedId}/items`, undefined, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to fetch feed items: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`Received ${data.length} items for feed ID: ${feedId}`);

      // Update the article count for this feed
      setFeedArticleCounts(prev => ({
        ...prev,
        [feedId]: data.length
      }));

      // Process all items regardless of count
      const processedItems = data.map((item: RssFeedItem) => {
        // Fix common patterns in titles
        if (item.title) {
          // Process the title with our HTML content processor
          item.title = processHtmlContent(item.title);
        }
        if (item.content) {
          // Process the content preview with our HTML content processor
          item.content = processHtmlContent(item.content);
        }
        return item;
      });

      // Only update state if we have items to show
      if (processedItems.length > 0) {
        console.log(`Processing ${processedItems.length} items with sort option: ${sortOption}`);

        // Check if any items have financial benefit amounts
        const hasBenefits = processedItems.some(item => (item.financialBenefitAmount || 0) > 0);
        console.log(`Items with financial benefits: ${hasBenefits ? 'Yes' : 'No'}`);

        if (hasBenefits) {
          console.log('Sample benefit amounts:',
            processedItems.slice(0, 5).map(item => ({
              id: item.id,
              benefit: item.financialBenefitAmount || 0
            }))
          );
        }

        // First, set the unsorted items to allow the DOM to be populated
        // This is important for the positive-terms sort which relies on DOM elements
        setFeedItems(processedItems);

        // Wait a moment for the DOM to update, then sort and update again
        setTimeout(() => {
          try {
            // Sort the items according to the current sort option
            const sortedItems = getSortedFeedItems(processedItems, sortOption);

            console.log('First 3 items after sorting:',
              sortedItems.slice(0, 3).map(item => ({
                id: item.id,
                title: item.title?.substring(0, 30) || 'No title',
                benefit: item.financialBenefitAmount || 0,
                date: new Date(item.publishedAt).toISOString()
              }))
            );

            // Update feed items with the sorted items
            setFeedItems(sortedItems);

            // Use setTimeout to ensure the state update has been processed
            // This ensures the feed items list is rendered before selecting an item
            setTimeout(() => {
              // Automatically select the first item
              if (sortedItems.length > 0) {
                console.log(`Fetching content for first item: ${sortedItems[0].title?.substring(0, 50) || 'No title'}...`);
                // Mark this item as coming from feed selection to prevent unwanted scrolling
                const itemWithFlag = { ...sortedItems[0], isFromFeedSelection: true };
                fetchArticleContent(itemWithFlag);
              }
            }, 50);
          } catch (error) {
            console.error('Error sorting feed items:', error);
            // If sorting fails, still select the first item from the unsorted list
            if (processedItems.length > 0) {
              fetchArticleContent(processedItems[0]);
            }
          }
        }, 100); // Small delay to ensure DOM is ready
      } else {
        console.log(`No items received for feed ID: ${feedId}, attempting refresh`);

        // If no items were found, try refreshing the feed
        console.log(`Attempting to refresh feed ${feedId} to get items`);
        try {
          // Set the refreshing feed ID to show animation
          setRefreshingFeedId(feedId);

          await apiRequest('POST', `/api/rss-feeds/${feedId}/refresh`);

          // Wait a moment and try fetching items again
          // Wait longer to allow X.com content to be pre-fetched
          console.log(`Waiting for server to complete refresh processing (including X.com content)...`);
          await new Promise(resolve => setTimeout(resolve, 3000));

          const retryResponse = await apiRequest('GET', `/api/rss-feeds/${feedId}/items`);

          if (!retryResponse.ok) {
            throw new Error(`Failed to fetch feed items after refresh: ${retryResponse.status} ${retryResponse.statusText}`);
          }

          const retryData = await retryResponse.json();
          console.log(`Retry fetched ${retryData.length} items for feed ID: ${feedId}`);

          if (retryData.length > 0) {
            const retryProcessedItems = retryData.map((item: RssFeedItem) => {
              if (item.title) item.title = processHtmlContent(item.title);
              if (item.content) item.content = processHtmlContent(item.content);
              return item;
            });

            setFeedItems(retryProcessedItems);

            if (retryProcessedItems.length > 0) {
              // Mark this item as coming from feed selection to prevent unwanted scrolling
              const itemWithFlag = { ...retryProcessedItems[0], isFromFeedSelection: true };
              fetchArticleContent(itemWithFlag);
            }

            toast({
              title: 'Success',
              description: 'Successfully refreshed feed items',
            });
          } else {
            // If we still have no items, keep the current items if any
            if (currentFeedItems.length > 0) {
              console.log(`No items found after refresh, keeping ${currentFeedItems.length} existing items`);
              setFeedItems(currentFeedItems);

              // Keep the current selected item if it exists
              if (selectedItem) {
                console.log('Keeping current selected item');
              } else if (currentFeedItems.length > 0) {
                console.log('Selecting first item from existing items');
                // Mark this item as coming from feed selection to prevent unwanted scrolling
                const itemWithFlag = { ...currentFeedItems[0], isFromFeedSelection: true };
                fetchArticleContent(itemWithFlag);
              }
            } else {
              toast({
                title: 'Warning',
                description: 'No items found for this feed even after refreshing',
                variant: 'destructive',
              });
            }
          }
        } catch (refreshError) {
          console.error(`Error refreshing feed ${feedId}:`, refreshError);

          // If refresh fails, keep the current items if any
          if (currentFeedItems.length > 0) {
            console.log(`Refresh failed, keeping ${currentFeedItems.length} existing items`);
            setFeedItems(currentFeedItems);

            // Keep the current selected item if it exists
            if (selectedItem) {
              console.log('Keeping current selected item');
            } else if (currentFeedItems.length > 0) {
              console.log('Selecting first item from existing items');
              // Use setTimeout to ensure the state update has been processed
              setTimeout(() => {
                // Mark this item as coming from feed selection to prevent unwanted scrolling
                const itemWithFlag = { ...currentFeedItems[0], isFromFeedSelection: true };
                fetchArticleContent(itemWithFlag);
              }, 50);
            }
          } else {
            toast({
              title: 'Error',
              description: 'Failed to refresh feed items',
              variant: 'destructive',
            });
          }
        } finally {
          // Clear the refreshing states
          setRefreshingFeedId(null);
        }
      }
    } catch (error) {
      console.error('Error fetching RSS feed items:', error);

      // Store the current feed items to restore
      const currentFeedItems = [...feedItems];

      toast({
        title: 'Error',
        description: 'Failed to fetch RSS feed items. Attempting to refresh the feed...',
        variant: 'destructive',
      });

      // Try to refresh the feed if fetching items fails
      try {
        // Set the refreshing feed ID to show animation
        setRefreshingFeedId(feedId);
        setIsRefreshingFeed(true);

        await apiRequest('POST', `/api/rss-feeds/${feedId}/refresh`);

        // Wait longer to allow X.com content to be pre-fetched
        console.log(`Waiting for server to complete refresh processing (including X.com content)...`);
        await new Promise(resolve => setTimeout(resolve, 3000));

        const retryResponse = await apiRequest('GET', `/api/rss-feeds/${feedId}/items`);

        if (!retryResponse.ok) {
          throw new Error(`Failed to fetch feed items after refresh: ${retryResponse.status} ${retryResponse.statusText}`);
        }

        const retryData = await retryResponse.json();

        if (retryData.length > 0) {
          const retryProcessedItems = retryData.map((item: RssFeedItem) => {
            if (item.title) item.title = processHtmlContent(item.title);
            if (item.content) item.content = processHtmlContent(item.content);
            return item;
          });

          setFeedItems(retryProcessedItems);

          if (retryProcessedItems.length > 0) {
            // Use setTimeout to ensure the state update has been processed
            setTimeout(() => {
              // Mark this item as coming from feed selection to prevent unwanted scrolling
              const itemWithFlag = { ...retryProcessedItems[0], isFromFeedSelection: true };
              fetchArticleContent(itemWithFlag);
            }, 50);
          }

          toast({
            title: 'Success',
            description: 'Successfully refreshed feed items',
          });
        } else {
          // If we still have no items, keep the current items if any
          if (currentFeedItems.length > 0) {
            console.log(`No items found after refresh, keeping ${currentFeedItems.length} existing items`);
            setFeedItems(currentFeedItems);

            // Keep the current selected item if it exists
            if (selectedItem) {
              console.log('Keeping current selected item');
            } else if (currentFeedItems.length > 0) {
              console.log('Selecting first item from existing items');
              // Use setTimeout to ensure the state update has been processed
              setTimeout(() => {
                // Mark this item as coming from feed selection to prevent unwanted scrolling
                const itemWithFlag = { ...currentFeedItems[0], isFromFeedSelection: true };
                fetchArticleContent(itemWithFlag);
              }, 50);
            }
          } else {
            toast({
              title: 'Warning',
              description: 'No items found for this feed even after refreshing',
              variant: 'destructive',
            });
          }
        }
      } catch (refreshError) {
        console.error(`Error refreshing feed ${feedId}:`, refreshError);

        // If refresh fails, keep the current items if any
        if (currentFeedItems.length > 0) {
          console.log(`Refresh failed, keeping ${currentFeedItems.length} existing items`);
          setFeedItems(currentFeedItems);

          // Keep the current selected item if it exists
          if (selectedItem) {
            console.log('Keeping current selected item');
          } else if (currentFeedItems.length > 0) {
            console.log('Selecting first item from existing items');
            // Use setTimeout to ensure the state update has been processed
            setTimeout(() => {
              // Mark this item as coming from feed selection to prevent unwanted scrolling
              const itemWithFlag = { ...currentFeedItems[0], isFromFeedSelection: true };
              fetchArticleContent(itemWithFlag);
            }, 50);
          }
        } else {
          toast({
            title: 'Error',
            description: 'Failed to refresh feed items',
            variant: 'destructive',
          });
        }
      } finally {
        // Clear the refreshing states
        setRefreshingFeedId(null);
        setIsRefreshingFeed(false);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Add a new RSS feed
  const addFeed = async () => {
    if (!newFeedName.trim() || !newFeedUrl.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a name and URL for the feed',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);
      const response = await apiRequest('POST', '/api/rss-feeds', {
        name: newFeedName,
        url: newFeedUrl,
      });
      const data = await response.json();

      setFeeds([...feeds, data]);

      // Initialize the article count for this feed to 0 (will be updated when items are fetched)
      setFeedArticleCounts(prev => ({
        ...prev,
        [data.id]: 0
      }));

      setNewFeedName('');
      setNewFeedUrl('');
      setIsAddFeedDialogOpen(false);
      setIsLoading(false);

      toast({
        title: 'Success',
        description: 'RSS feed added successfully',
      });
    } catch (error) {
      console.error('Error adding RSS feed:', error);
      toast({
        title: 'Error',
        description: 'Failed to add RSS feed',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Edit an existing RSS feed
  const editFeed = async () => {
    if (!editFeedId || !editFeedName.trim() || !editFeedUrl.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a name and URL for the feed',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);
      const response = await apiRequest('PUT', `/api/rss-feeds/${editFeedId}`, {
        name: editFeedName,
        url: editFeedUrl,
      });
      const updatedFeed = await response.json();

      // Update the feed in the local state
      setFeeds(feeds.map(feed =>
        feed.id === editFeedId ? updatedFeed : feed
      ));

      setEditFeedId(null);
      setEditFeedName('');
      setEditFeedUrl('');
      setIsEditFeedDialogOpen(false);
      setIsLoading(false);

      toast({
        title: 'Success',
        description: 'RSS feed updated successfully',
      });
    } catch (error) {
      console.error('Error updating RSS feed:', error);
      toast({
        title: 'Error',
        description: 'Failed to update RSS feed',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Open edit feed dialog
  const openEditFeedDialog = (feed: RssFeed) => {
    setEditFeedId(feed.id);
    setEditFeedName(feed.name);
    setEditFeedUrl(feed.url);
    setIsEditFeedDialogOpen(true);
  };

  // Delete an RSS feed
  const deleteFeed = async (feedId: number) => {
    try {
      setIsLoading(true);
      await apiRequest('DELETE', `/api/rss-feeds/${feedId}`);
      setFeeds(feeds.filter(feed => feed.id !== feedId));

      // Remove the article count for this feed
      setFeedArticleCounts(prev => {
        const newCounts = { ...prev };
        delete newCounts[feedId];
        return newCounts;
      });

      if (selectedFeedId === feedId) {
        setSelectedFeedId(null);
        setFeedItems([]);
      }

      setIsLoading(false);

      toast({
        title: 'Success',
        description: 'RSS feed deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting RSS feed:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete RSS feed',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Delete the oldest N articles from a feed
  const deleteOldestArticles = async (feedId: number, count: number) => {
    try {
      setIsLoading(true);
      const response = await apiRequest('DELETE', `/api/rss-feeds/${feedId}/oldest/${count}`);

      if (!response.ok) {
        throw new Error('Failed to delete oldest articles');
      }

      const result = await response.json();

      // If this is the currently selected feed, refresh the feed items
      if (selectedFeedId === feedId) {
        await fetchFeedItems(feedId);
      }

      // Update the article count for this feed
      setFeedArticleCounts(prev => ({
        ...prev,
        [feedId]: (prev[feedId] || 0) - result.deletedCount
      }));

      setIsLoading(false);

      toast({
        title: 'Success',
        description: `Deleted ${result.deletedCount} oldest articles from feed`,
      });

      // Update all feed counts to ensure they're accurate
      fetchFeedArticleCounts();
    } catch (error) {
      console.error('Error deleting oldest articles:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete oldest articles',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  // Use the network manager hook
  const { isNetworkAvailable } = useNetworkStatus();

  // Refresh an RSS feed with improved handling
  const refreshFeed = async (feedId: number) => {
    try {
      console.log(`Starting refresh for feed ${feedId}`);

      // Check if network is available
      if (!isNetworkAvailable()) {
        console.log(`Network unavailable, skipping refresh for feed ${feedId}`);
        toast({
          title: 'Network Unavailable',
          description: 'Cannot refresh feed while offline. Please check your connection.',
          variant: 'destructive',
        });
        return;
      }

      // Prevent multiple simultaneous refreshes of the same feed
      if (refreshingFeedId === feedId || isRefreshingFeed) {
        console.log(`Feed ${feedId} is already being refreshed, skipping duplicate refresh`);
        toast({
          title: 'Refresh in Progress',
          description: 'This feed is already being refreshed. Please wait for it to complete.',
        });
        return;
      }

      // Store current items and selected item to restore if refresh fails
      const currentFeedItems = [...feedItems];
      const currentSelectedItem = selectedItem;
      const currentArticleContent = articleContent;

      // Set the refreshing feed ID to show animation for this specific feed
      setRefreshingFeedId(feedId);
      setIsRefreshingFeed(true);
      setRetryCount(0);

      // Only set isLoading if this is the selected feed to avoid blocking the UI
      if (selectedFeedId === feedId) {
        setIsLoading(true);
      }

      // Start a timer to measure client-side refresh time
      const startTime = Date.now();

      // Send the refresh request to the server
      console.log(`Sending refresh request for feed ${feedId}`);
      const response = await apiRequest('POST', `/api/rss-feeds/${feedId}/refresh`);

      if (!response.ok) {
        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
      }

      const refreshResult = await response.json();
      console.log(`Refresh result for feed ${feedId}:`, refreshResult);

      // Calculate how long the server-side refresh took
      const serverRefreshDuration = refreshResult.refreshDuration || '0s';
      console.log(`Server-side refresh took ${serverRefreshDuration}`);

      // Update the article count for this feed if available in the refresh result
      if (refreshResult.itemCount !== undefined) {
        console.log(`Updating article count for feed ${feedId} to ${refreshResult.itemCount}`);
        setFeedArticleCounts(prev => ({
          ...prev,
          [feedId]: refreshResult.itemCount
        }));
      }

      // Update the feed's last refresh time in our local state using the server's timestamp
      const serverRefreshTime = refreshResult.lastRefreshTime ? new Date(refreshResult.lastRefreshTime) : new Date();
      setFeeds(prevFeeds => prevFeeds.map(feed =>
        feed.id === feedId
          ? { ...feed, lastRefreshTime: serverRefreshTime }
          : feed
      ));

      // Update all feed counts to ensure they're accurate
      fetchFeedArticleCounts();

      // Check if there are X.com items being processed in the background
      const xcomItemsProcessing = refreshResult.xcomItemsProcessing || 0;
      if (xcomItemsProcessing > 0) {
        console.log(`Server is processing ${xcomItemsProcessing} X.com items in the background`);
        // Show a toast to inform the user
        toast({
          title: 'Background Processing',
          description: `${xcomItemsProcessing} X.com items are being processed in the background. They will be available shortly.`,
          duration: 5000,
        });
      }

      // Fetch updated feed items if this is the selected feed
      if (selectedFeedId === feedId) {
        try {
          console.log(`Fetching updated items for feed ${feedId} after refresh`);

          // Make the API request with a longer timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

          const itemsResponse = await apiRequest('GET', `/api/rss-feeds/${feedId}/items`, undefined, {
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!itemsResponse.ok) {
            throw new Error(`Failed to fetch feed items: ${itemsResponse.status} ${itemsResponse.statusText}`);
          }

          const itemsData = await itemsResponse.json();
          console.log(`Fetched ${itemsData.length} items for feed ID: ${feedId} after refresh`);

          if (itemsData.length > 0) {
            const processedItems = itemsData.map((item: RssFeedItem) => {
              if (item.title) item.title = processHtmlContent(item.title);
              if (item.content) item.content = processHtmlContent(item.content);
              return item;
            });

            // Only update state if we have items to show
            setFeedItems(processedItems);

            // Clear the selected item only after we have new items
            setSelectedItem(null);
            setArticleContent('');

            // Use setTimeout to ensure the state update has been processed
            setTimeout(() => {
              // Automatically select the first item
              console.log(`Fetching content for first item after refresh: ${processedItems[0].title.substring(0, 50)}...`);
              // Mark this item as coming from feed selection to prevent unwanted scrolling
              const itemWithFlag = { ...processedItems[0], isFromFeedSelection: true };
              fetchArticleContent(itemWithFlag);
            }, 50);
          } else {
            // If no items were found, keep the current items
            console.log(`No items found after refresh, keeping ${currentFeedItems.length} existing items`);
            setFeedItems(currentFeedItems);

            // Keep the current selected item if it exists
            if (currentSelectedItem) {
              console.log('Keeping current selected item after refresh');
              setSelectedItem(currentSelectedItem);
              setArticleContent(currentArticleContent);
            } else if (currentFeedItems.length > 0) {
              console.log('Selecting first item from existing items after refresh');
              setTimeout(() => {
                // Mark this item as coming from feed selection to prevent unwanted scrolling
                const itemWithFlag = { ...currentFeedItems[0], isFromFeedSelection: true };
                fetchArticleContent(itemWithFlag);
              }, 50);
            }
          }
        } catch (error) {
          console.error(`Error fetching items after refresh:`, error);

          // If fetching items fails, keep the current items
          console.log(`Error fetching items after refresh, keeping ${currentFeedItems.length} existing items`);
          setFeedItems(currentFeedItems);

          // Restore the selected item and article content
          if (currentSelectedItem) {
            setSelectedItem(currentSelectedItem);
            setArticleContent(currentArticleContent);
          } else if (currentFeedItems.length > 0) {
            setTimeout(() => {
              // Mark this item as coming from feed selection to prevent unwanted scrolling
              const itemWithFlag = { ...currentFeedItems[0], isFromFeedSelection: true };
              fetchArticleContent(itemWithFlag);
            }, 50);
          }
        }
      }

      // Update global last refresh time
      setLastRefreshTime(serverRefreshTime);

      // Call the onRefresh callback if provided
      if (onRefresh) {
        onRefresh(serverRefreshTime);
      }

      // Automatically click the dashboard refresh button after refreshing a feed
      console.log('Auto-clicking dashboard refresh button after feed refresh');
      // Find the dashboard refresh button and click it
      setTimeout(() => {
        const dashboardRefreshButton = document.querySelector('.enhanced-dashboard-refresh-button');
        if (dashboardRefreshButton) {
          console.log('Found dashboard refresh button, clicking it');
          (dashboardRefreshButton as HTMLButtonElement).click();
        } else {
          console.log('Dashboard refresh button not found, falling back to fetchAllFeedItems');
          fetchAllFeedItems(true);
        }
      }, 500);

      // Calculate total client-side refresh time
      const clientRefreshDuration = ((Date.now() - startTime) / 1000).toFixed(2);
      console.log(`Client-side refresh completed in ${clientRefreshDuration}s`);

      toast({
        title: 'Success',
        description: `RSS feed refreshed successfully. Found ${refreshResult.itemCount || 0} items.`,
      });
    } catch (error) {
      console.error(`Error refreshing RSS feed ${feedId}:`, error);

      // Implement retry logic
      if (retryCount < maxRetries) {
        const newRetryCount = retryCount + 1;
        setRetryCount(newRetryCount);

        console.log(`Retrying refresh for feed ${feedId} (attempt ${newRetryCount}/${maxRetries})`);
        toast({
          title: 'Retrying',
          description: `Retrying feed refresh (attempt ${newRetryCount}/${maxRetries})...`,
        });

        // Wait a moment before retrying with exponential backoff
        const backoffDelay = Math.min(2000 * Math.pow(2, newRetryCount - 1), 10000);
        await new Promise(resolve => setTimeout(resolve, backoffDelay));
        await refreshFeed(feedId);
        return;
      }

      toast({
        title: 'Error',
        description: 'Failed to refresh RSS feed after multiple attempts',
        variant: 'destructive',
      });
    } finally {
      // Clear the refreshing states
      setRefreshingFeedId(null);
      setIsRefreshingFeed(false);
      if (selectedFeedId === feedId) {
        setIsLoading(false);
      }
    }
  };

  // Refresh all RSS feeds
  const refreshAllFeeds = async () => {
    try {
      // Check if network is available
      if (!isNetworkAvailable()) {
        console.log(`Network unavailable, skipping refresh for all feeds`);
        toast({
          title: 'Network Unavailable',
          description: 'Cannot refresh feeds while offline. Please check your connection.',
          variant: 'destructive',
        });
        return;
      }

      // Store current items and selected item to restore if refresh fails
      const currentFeedItems = [...feedItems];
      const currentSelectedItem = selectedItem;
      const currentArticleContent = articleContent;

      setIsRefreshingAll(true);
      const response = await apiRequest('POST', '/api/rss-feeds/refresh-all');
      const data = await response.json();

      // Update all feeds' last refresh time in our local state
      const now = new Date();
      setFeeds(feeds.map(feed => ({ ...feed, lastRefreshTime: now })));

      // Add a longer delay to ensure the server has processed the refresh
      // This allows time for X.com content to be pre-fetched
      console.log(`Waiting for server to complete refresh-all processing (including X.com content)...`);
      await new Promise(resolve => setTimeout(resolve, 5000));

      // If a feed is selected, refresh its items
      if (selectedFeedId) {
        try {
          console.log(`Fetching updated items for feed ${selectedFeedId} after refresh-all`);

          // Make the API request with a longer timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

          const itemsResponse = await apiRequest('GET', `/api/rss-feeds/${selectedFeedId}/items`, undefined, {
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!itemsResponse.ok) {
            throw new Error(`Failed to fetch feed items: ${itemsResponse.status} ${itemsResponse.statusText}`);
          }

          const itemsData = await itemsResponse.json();
          console.log(`Fetched ${itemsData.length} items for feed ID: ${selectedFeedId} after refresh-all`);

          if (itemsData.length > 0) {
            const processedItems = itemsData.map((item: RssFeedItem) => {
              if (item.title) item.title = processHtmlContent(item.title);
              if (item.content) item.content = processHtmlContent(item.content);
              return item;
            });

            // Only update state if we have items to show
            setFeedItems(processedItems);

            // Clear the selected item only after we have new items
            setSelectedItem(null);
            setArticleContent('');

            // Use setTimeout to ensure the state update has been processed
            setTimeout(() => {
              // Automatically select the first item
              console.log(`Fetching content for first item after refresh-all: ${processedItems[0].title.substring(0, 50)}...`);
              fetchArticleContent(processedItems[0]);
            }, 50);
          } else {
            // If no items were found, keep the current items
            console.log(`No items found after refresh-all, keeping ${currentFeedItems.length} existing items`);
            setFeedItems(currentFeedItems);

            // Keep the current selected item if it exists
            if (currentSelectedItem) {
              console.log('Keeping current selected item after refresh-all');
            } else if (currentFeedItems.length > 0) {
              console.log('Selecting first item from existing items after refresh-all');
              setTimeout(() => {
                fetchArticleContent(currentFeedItems[0]);
              }, 50);
            }
          }
        } catch (error) {
          console.error(`Error fetching items after refresh-all:`, error);

          // If fetching items fails, keep the current items
          console.log(`Error fetching items after refresh-all, keeping ${currentFeedItems.length} existing items`);
          setFeedItems(currentFeedItems);

          // Restore the selected item and article content
          if (currentSelectedItem) {
            setSelectedItem(currentSelectedItem);
            setArticleContent(currentArticleContent);
          } else if (currentFeedItems.length > 0) {
            setTimeout(() => {
              fetchArticleContent(currentFeedItems[0]);
            }, 50);
          }
        }
      }

      // Update global last refresh time
      setLastRefreshTime(now);

      // Call the onRefresh callback if provided
      if (onRefresh) {
        onRefresh(now);
      }

      // Update all feed counts to ensure they're accurate
      fetchFeedArticleCounts();

      // Automatically click the dashboard refresh button after refreshing all feeds
      console.log('Auto-clicking dashboard refresh button after refreshing all feeds');
      // Find the dashboard refresh button and click it
      setTimeout(() => {
        const dashboardRefreshButton = document.querySelector('.enhanced-dashboard-refresh-button');
        if (dashboardRefreshButton) {
          console.log('Found dashboard refresh button, clicking it');
          (dashboardRefreshButton as HTMLButtonElement).click();
        } else {
          console.log('Dashboard refresh button not found, falling back to fetchAllFeedItems');
          fetchAllFeedItems(true);
        }
      }, 500);

      toast({
        title: 'Success',
        description: `Refreshed ${data.refreshed} feeds, found ${data.itemsAdded} new items`,
      });
    } catch (error) {
      console.error('Error refreshing all RSS feeds:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh all RSS feeds',
        variant: 'destructive',
      });
    } finally {
      setIsRefreshingAll(false);
    }
  };

  // State to track PDF generation without blocking UI
  const [isPdfGenerating, setIsPdfGenerating] = useState(false);

  // State to track text copying operation
  const [isCopyingText, setIsCopyingText] = useState(false);

  // State to track import/export operations
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Export all RSS feeds
  const exportFeeds = async () => {
    try {
      setIsExporting(true);

      // Create a direct download link to the export endpoint
      const downloadUrl = `/api/rss-feeds/export`;

      // Create a temporary anchor element to trigger the download
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = `rss_feeds_export.json`; // The server will set the actual filename
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      // Show a success toast
      toast({
        title: 'Export Complete',
        description: 'Your RSS feeds have been exported successfully.',
        duration: 3000,
      });

      setIsExporting(false);
    } catch (error) {
      console.error('Error exporting feeds:', error);
      toast({
        title: 'Error',
        description: 'Failed to export RSS feeds',
        variant: 'destructive',
      });
      setIsExporting(false);
    }
  };

  // Import RSS feeds
  const importFeeds = async () => {
    try {
      // Create a file input element
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = 'application/json';

      // Handle file selection
      fileInput.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (!file) return;

        setIsImporting(true);

        try {
          // Read the file
          const reader = new FileReader();

          reader.onload = async (event) => {
            try {
              const content = event.target?.result as string;
              const feeds = JSON.parse(content);

              // Validate the file format
              if (!Array.isArray(feeds)) {
                throw new Error('Invalid file format. Expected an array of feeds.');
              }

              // Show a loading toast
              toast({
                title: 'Importing Feeds',
                description: `Importing ${feeds.length} RSS feeds. This may take a moment...`,
                duration: 5000,
              });

              // Send the feeds to the server
              console.log(`Importing ${feeds.length} RSS feeds`);
              const response = await apiRequest('POST', '/api/rss-feeds/import', feeds);

              if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
              }

              const result = await response.json();
              console.log('Import result:', result);

              // Refresh the feeds list
              console.log('Refreshing feeds list after import');
              await fetchFeeds();

              // Get the updated feeds
              console.log('Getting updated feeds after import');
              const feedsResponse = await apiRequest('GET', '/api/rss-feeds');
              const updatedFeeds = await feedsResponse.json();

              console.log('Updated feeds after import:', updatedFeeds);

              // If there are feeds, select the first one and fetch its items
              if (updatedFeeds.length > 0) {
                // Clear any existing feed items to avoid showing stale data
                setFeedItems([]);

                // Select the first feed
                const firstFeedId = updatedFeeds[0].id;
                console.log(`Selecting first feed: ${firstFeedId}`);
                setSelectedFeedId(firstFeedId);

                // No need to explicitly refresh the feed since the server already fetched items during import
                // Just fetch the items directly
                console.log(`Fetching items for feed ${firstFeedId} after import`);
                try {
                  // Make the API request with a longer timeout
                  const controller = new AbortController();
                  const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

                  // Fetch the items for the selected feed
                  console.log(`Fetching items for feed ${firstFeedId}`);

                  const itemsResponse = await apiRequest('GET', `/api/rss-feeds/${firstFeedId}/items`, undefined, {
                    signal: controller.signal
                  });

                  clearTimeout(timeoutId);

                  if (!itemsResponse.ok) {
                    throw new Error(`Failed to fetch feed items: ${itemsResponse.status} ${itemsResponse.statusText}`);
                  }

                  const itemsData = await itemsResponse.json();
                  console.log(`Fetched ${itemsData.length} items for feed ID: ${firstFeedId} after import`);

                  if (itemsData.length > 0) {
                    const processedItems = itemsData.map((item: RssFeedItem) => {
                      if (item.title) item.title = processHtmlContent(item.title);
                      if (item.content) item.content = processHtmlContent(item.content);
                      return item;
                    });

                    // Only update state if we have items to show
                    setFeedItems(processedItems);

                    // Use setTimeout to ensure the state update has been processed
                    setTimeout(() => {
                      // Automatically select the first item
                      console.log(`Fetching content for first item after import: ${processedItems[0].title.substring(0, 50)}...`);
                      fetchArticleContent(processedItems[0]);
                    }, 50);
                  } else {
                    // If no items were found, try again with increasing delays
                    let retryCount = 0;
                    const maxRetries = 5; // Increase max retries for X.com content

                    while (itemsData.length === 0 && retryCount < maxRetries) {
                      retryCount++;
                      const delay = retryCount * 3000; // Increasing delay: 3s, 6s, 9s, 12s, 15s

                      console.log(`No items found after fetch attempt ${retryCount}, retrying in ${delay/1000}s...`);
                      await new Promise(resolve => setTimeout(resolve, delay));

                      // Try refreshing again
                      console.log(`Retry ${retryCount}: Refreshing feed ${firstFeedId}`);
                      await apiRequest('POST', `/api/rss-feeds/${firstFeedId}/refresh`);

                      // Wait longer to allow X.com content to be pre-fetched
                      console.log(`Waiting for server to complete refresh processing during retry ${retryCount} (including X.com content)...`);
                      await new Promise(resolve => setTimeout(resolve, 2000));

                      // Fetch items again
                      console.log(`Retry ${retryCount}: Fetching items for feed ${firstFeedId}`);
                      const retryItemsResponse = await apiRequest('GET', `/api/rss-feeds/${firstFeedId}/items`);

                      if (!retryItemsResponse.ok) {
                        throw new Error(`Failed to fetch feed items on retry: ${retryItemsResponse.status} ${retryItemsResponse.statusText}`);
                      }

                      const retryItemsData = await retryItemsResponse.json();

                      if (retryItemsData.length > 0) {
                        const retryProcessedItems = retryItemsData.map((item: RssFeedItem) => {
                          if (item.title) item.title = processHtmlContent(item.title);
                          if (item.content) item.content = processHtmlContent(item.content);
                          return item;
                        });

                        // Update feed items
                        setFeedItems(retryProcessedItems);

                        // Use setTimeout to ensure the state update has been processed
                        setTimeout(() => {
                          // Automatically select the first item
                          console.log(`Fetching content for first item after retry: ${retryProcessedItems[0].title.substring(0, 50)}...`);
                          fetchArticleContent(retryProcessedItems[0]);
                        }, 50);

                        // Break out of the retry loop
                        break;
                      }
                    }

                    if (itemsData.length === 0 && feedItems.length === 0) {
                      console.warn(`Failed to fetch items for feed ${firstFeedId} after ${maxRetries} retries`);
                      toast({
                        title: 'Warning',
                        description: 'Imported feeds but couldn\'t fetch items. Try refreshing manually.',
                        variant: 'destructive',
                      });
                    }
                  }
                } catch (refreshError) {
                  console.error('Error refreshing feed after import:', refreshError);
                  toast({
                    title: 'Warning',
                    description: 'Feeds imported but refresh failed. Try refreshing manually.',
                    variant: 'destructive',
                  });
                } finally {
                  // Clear the refreshing states
                  setRefreshingFeedId(null);
                  setIsRefreshingFeed(false);
                }
              }

              // Automatically refresh all feeds after import
              if (result.results.imported > 0) {
                console.log('Automatically refreshing all feeds after import');
                await refreshAllFeeds();
              }

              // Show a success toast
              toast({
                title: 'Import Complete',
                description: `Imported ${result.results.imported} feeds with ${result.results.itemsAdded} items, skipped ${result.results.skipped} duplicates, failed ${result.results.failed} out of ${result.results.total} total.`,
                duration: 5000,
              });
            } catch (error) {
              console.error('Error processing import file:', error);
              toast({
                title: 'Error',
                description: 'Failed to process import file. Please ensure it is a valid JSON file.',
                variant: 'destructive',
              });
            } finally {
              setIsImporting(false);
            }
          };

          reader.onerror = () => {
            toast({
              title: 'Error',
              description: 'Failed to read the import file',
              variant: 'destructive',
            });
            setIsImporting(false);
          };

          reader.readAsText(file);
        } catch (error) {
          console.error('Error reading import file:', error);
          toast({
            title: 'Error',
            description: 'Failed to read the import file',
            variant: 'destructive',
          });
          setIsImporting(false);
        }
      };

      // Trigger the file input click
      fileInput.click();
    } catch (error) {
      console.error('Error importing feeds:', error);
      toast({
        title: 'Error',
        description: 'Failed to import RSS feeds',
        variant: 'destructive',
      });
      setIsImporting(false);
    }
  };

  // Download all articles in a feed as a PDF document
  const downloadFeedDocument = async (feedId: number) => {
    try {
      // Set PDF generating state to true (this won't block the UI)
      setIsPdfGenerating(true);

      // Show a loading toast
      toast({
        title: 'Generating PDF',
        description: 'Please wait while we generate your PDF document...',
        duration: 10000, // 10 seconds should be enough for most PDFs
      });

      // Create a direct download link to the document endpoint
      const downloadUrl = `/api/rss-feeds/${feedId}/document`;

      // Use fetch to monitor the request progress
      const response = await fetch(downloadUrl);

      // Check if the request was successful
      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Get the filename from the Content-Disposition header if available
      let filename = `feed-articles-${format(new Date(), 'yyyy-MM-dd')}.pdf`;
      const contentDisposition = response.headers.get('Content-Disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1];
        }
      }

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create a temporary anchor element to trigger the download
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Show a success toast
      toast({
        title: 'PDF Generated',
        description: 'Your PDF document has been successfully generated and downloaded.',
        duration: 3000,
      });

      // Reset PDF generating state
      setIsPdfGenerating(false);
    } catch (error) {
      console.error('Error downloading feed PDF:', error);
      toast({
        title: 'Error',
        description: 'Failed to download PDF document',
        variant: 'destructive',
      });
      setIsPdfGenerating(false);
    }
  };

  // Copy all articles in a feed as formatted text to clipboard
  const copyFeedText = async (feedId: number) => {
    try {
      // Set copying state to true
      setIsCopyingText(true);

      // Show a loading toast
      toast({
        title: 'Preparing Text',
        description: 'Please wait while we prepare the article text...',
        duration: 5000,
      });

      // Create a URL to fetch the text content
      const textUrl = `/api/rss-feeds/${feedId}/text`;

      // Use fetch to get the text content
      const response = await fetch(textUrl);

      // Check if the request was successful
      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
      }

      // Get the text from the response
      const data = await response.json();
      const formattedText = data.text;

      // Copy the text to clipboard
      await navigator.clipboard.writeText(formattedText);

      // Show a success toast
      toast({
        title: 'Text Copied',
        description: 'All articles have been copied to your clipboard as formatted text.',
        duration: 3000,
      });

      // Reset copying state
      setIsCopyingText(false);
    } catch (error) {
      console.error('Error copying feed text:', error);

      // If the API endpoint doesn't exist yet, show a more specific error
      if (error instanceof Error && error.message.includes('404')) {
        toast({
          title: 'API Endpoint Missing',
          description: 'The text endpoint is not implemented on the server yet.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to copy text to clipboard',
          variant: 'destructive',
        });
      }

      setIsCopyingText(false);
    }
  };

  // Mark an item as read/unread
  const toggleItemReadStatus = async (item: RssFeedItem) => {
    try {
      const newStatus = !item.isRead;
      const response = await apiRequest('PATCH', `/api/rss-feeds/items/${item.id}`, {
        isRead: newStatus,
      });

      // Check if the article was not found (404)
      if (response.status === 404) {
        handleArticleNotFound(item.id);
        return;
      }

      // Update the item in the local state
      setFeedItems(feedItems.map(feedItem =>
        feedItem.id === item.id ? { ...feedItem, isRead: newStatus } : feedItem
      ));

      toast({
        title: 'Success',
        description: `Item marked as ${newStatus ? 'read' : 'unread'}`,
      });
    } catch (error) {
      console.error('Error updating item read status:', error);

      // Check if the error is a 404 (item not found)
      if (error instanceof Error && error.message.includes('404')) {
        handleArticleNotFound(item.id);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to update item status',
          variant: 'destructive',
        });
      }
    }
  };

  // Delete an article from the feed
  const deleteArticle = async (item: RssFeedItem) => {
    try {
      const response = await apiRequest('DELETE', `/api/rss-feeds/items/${item.id}`);

      // Check if the article was not found (404)
      if (response.status === 404) {
        handleArticleNotFound(item.id);
        return;
      }

      // Remove the item from the local state
      setFeedItems(feedItems.filter(feedItem => feedItem.id !== item.id));

      // If this was the selected item, clear the selection
      if (selectedItem?.id === item.id) {
        setSelectedItem(null);
        setArticleContent('');
      }

      // Update all feed counts to ensure they're accurate
      console.log('Updating feed counts after refreshing all feeds');
      fetchFeedArticleCounts();

      toast({
        title: 'Success',
        description: 'Article deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting article:', error);

      // Check if the error is a 404 (item not found)
      if (error instanceof Error && error.message.includes('404')) {
        handleArticleNotFound(item.id);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to delete article',
          variant: 'destructive',
        });
      }
    }
  };

  // Fetch article content
  const fetchArticleContent = async (item: RssFeedItem) => {
    try {
      // Check if network is available
      if (!isNetworkAvailable()) {
        console.log(`Network unavailable, skipping article content fetch`);
        toast({
          title: 'Network Unavailable',
          description: 'Cannot fetch article content while offline. Please check your connection.',
          variant: 'destructive',
        });

        // Still set the selected item so the UI shows something
        setSelectedItem(item);
        setIsLoadingArticle(false);
        return;
      }

      console.log(`Fetching content for article ID: ${item.id}, title: ${item.title?.substring(0, 30)}...`);
      setIsLoadingArticle(true);
      setSelectedItem(item);

      // Make sure we're in the feeds tab
      if (activeTab !== 'feeds') {
        console.log('Switching to feeds tab to display article');
        setActiveTab('feeds');
        // Small delay to allow tab switch to complete
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Special handling for MSN articles
      if (item.link.includes('msn.com')) {
        console.log('Using special MSN article endpoint');

        try {
          // Use our special MSN article endpoint with a new v2 version
          const response = await apiRequest(
            'GET',
            `/api/rss-feeds/msn-article-v2?url=${encodeURIComponent(item.link)}&title=${encodeURIComponent(item.title)}`
          );

          const data = await response.json();

          if (data.content) {
            if (!data.content.trim()) {
              console.warn('MSN article content is empty');
              // Set a placeholder content
              setArticleContent(`
                <div style="color: white !important; text-align: center; padding: 20px;">
                  <p>No content available for this MSN article.</p>
                  <p>You can try visiting the original article using the link button above.</p>
                </div>
              `);

              // Still update the selected item
              setSelectedItem(item);

              // Mark as read if not already
              if (!item.isRead) {
                toggleItemReadStatus(item);
              }

              toast({
                title: 'Warning',
                description: 'MSN article content is empty. Try visiting the original article.',
                variant: 'destructive',
              });

              setIsLoadingArticle(false);
              return;
            }

            // Process the content to ensure proper styling
            const processedContent = processHtmlContent(data.content);

            // Add style to ensure text is visible in dark mode with explicit white color
            const styledContent = `<div style="color: white !important;">${processedContent}</div>`;

            setArticleContent(styledContent);

            // Update the item with the scraped content
            const updatedItem = { ...item, scrapedContent: data.content };
            setSelectedItem(updatedItem);

            // Update the item in the feedItems array
            setFeedItems(feedItems.map(i => i.id === item.id ? updatedItem : i));

            // Mark as read if not already
            if (!item.isRead) {
              toggleItemReadStatus(item);
            }

            setIsLoadingArticle(false);
            return;
          } else {
            // Handle case where data exists but content is missing
            console.warn('MSN article response missing content property');
            throw new Error('MSN article response missing content property');
          }
        } catch (error) {
          console.error('Error using MSN article endpoint:', error);
          console.log('MSN article endpoint response:', error);
          toast({
            title: 'Error',
            description: `Failed to fetch MSN article content. Trying alternative method...`,
            variant: 'destructive',
          });
          // Continue with standard content fetching if the special endpoint fails
        }
      }

      // Check if we already have scraped content and we're not explicitly refreshing
      if (item.scrapedContent && !item.forceRefresh) {
        console.log('Using cached article content');
        // Process the content to ensure proper styling
        const processedContent = processHtmlContent(item.scrapedContent);
        // Add style to ensure text is visible in dark mode with explicit white color
        const styledContent = `<div style="color: white !important;">${processedContent}</div>`;
        setArticleContent(styledContent);
        setIsLoadingArticle(false);

        // Mark as read if not already
        if (!item.isRead) {
          toggleItemReadStatus(item);
        }

        // Only scroll to the article if we're not just selecting a feed
        // This prevents unwanted scrolling when clicking on a feed
        if (!item.isFromFeedSelection) {
          setTimeout(() => {
            const articleHeaderCard = document.getElementById('article-header-card');
            if (articleHeaderCard) {
              articleHeaderCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          }, 100);
        }

        return;
      }

      // Otherwise, fetch the content
      const response = await apiRequest('GET', `/api/rss-feeds/items/${item.id}/content`);

      // Check if the article was not found (404)
      if (response.status === 404) {
        handleArticleNotFound(item.id);
        setIsLoadingArticle(false);
        return;
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch article content: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Check if we got valid content
      if (!data.content) {
        console.warn('Received empty content from API');
        // Set a placeholder content
        setArticleContent(`
          <div style="color: white !important; text-align: center; padding: 20px;">
            <p>No content available for this article.</p>
            <p>You can try visiting the original article using the link button above.</p>
          </div>
        `);

        // Still update the selected item
        setSelectedItem(item);

        // Mark as read if not already
        if (!item.isRead) {
          toggleItemReadStatus(item);
        }
        return;
      }

      // Process the content to ensure proper styling
      const processedContent = processHtmlContent(data.content);

      // Add style to ensure text is visible in dark mode with explicit white color
      const styledContent = `<div style="color: white !important;">${processedContent}</div>`;

      setArticleContent(styledContent);

      // Update the item with the original scraped content (not processed)
      // This way we can reprocess it if needed
      const updatedItem = { ...item, scrapedContent: data.content, forceRefresh: false };
      setSelectedItem(updatedItem);

      // Update the item in the feedItems array
      setFeedItems(feedItems.map(i => i.id === item.id ? updatedItem : i));

      // Mark as read if not already
      if (!item.isRead) {
        toggleItemReadStatus(item);
      }

      // Only scroll to the article if we're not just selecting a feed
      // This prevents unwanted scrolling when clicking on a feed
      if (!item.isFromFeedSelection) {
        setTimeout(() => {
          const articleHeaderCard = document.getElementById('article-header-card');
          if (articleHeaderCard) {
            articleHeaderCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);
      }

      setIsLoadingArticle(false);
      console.log(`Successfully loaded content for article ID: ${item.id}`);
    } catch (error) {
      console.error('Error fetching article content:', error);

      // Set a placeholder content for the error case
      setArticleContent(`
        <div style="color: white !important; text-align: center; padding: 20px;">
          <p>Error loading article content.</p>
          <p>You can try refreshing the content or visiting the original article.</p>
        </div>
      `);

      // Still update the selected item so the UI shows something
      setSelectedItem(item);

      toast({
        title: 'Error',
        description: 'Failed to fetch article content. Try using the refresh button or visit the original article.',
        variant: 'destructive',
      });

      setIsLoadingArticle(false);
    }
  };

  // Track if a feed refresh is in progress

  // Load feeds on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      console.log('Loading initial RSS feed data');
      await fetchFeeds();

      // If we have feeds, set the last refresh time to now
      // This represents when the app loaded the feeds
      if (feeds.length > 0) {
        console.log(`Loaded ${feeds.length} feeds initially`);
        setLastRefreshTime(new Date());

        // If we have feeds but no selected feed, select the first one
        if (!selectedFeedId && feeds.length > 0) {
          console.log(`Auto-selecting first feed: ${feeds[0].id}`);
          setSelectedFeedId(feeds[0].id);

          // We'll let the selectedFeedId effect handle loading the items
          // This prevents the issue where items disappear after initial load
        }

        // IMPORTANT: Immediately fetch all feed items for the dashboard
        // This ensures the dashboard has data when it first loads
        console.log('Immediately fetching all feed items for dashboard on initial load');
        fetchAllFeedItems(true);
      }
    };

    loadInitialData();
  }, []);

  // Automatically click the dashboard refresh button when the component mounts
  useEffect(() => {
    // Add a delay to ensure the dashboard is fully loaded before clicking the refresh button
    const timeoutId = setTimeout(() => {
      console.log('Auto-clicking dashboard refresh button on component mount');
      // Find the dashboard refresh button and click it
      const dashboardRefreshButton = document.querySelector('.enhanced-dashboard-refresh-button');
      if (dashboardRefreshButton) {
        console.log('Found dashboard refresh button, clicking it');
        (dashboardRefreshButton as HTMLButtonElement).click();
      } else {
        console.log('Dashboard refresh button not found, falling back to fetchAllFeedItems');
        fetchAllFeedItems(true);
      }
    }, 1000); // Longer delay to ensure the dashboard is fully rendered

    return () => clearTimeout(timeoutId);
  }, []);

  // Effect to load feed items when a feed is selected
  useEffect(() => {
    if (selectedFeedId) {
      console.log(`Selected feed changed to: ${selectedFeedId}, fetching items`);
      fetchFeedItems(selectedFeedId);

      // Also update the article counts to ensure they're accurate
      fetchFeedArticleCounts();

      // We no longer exit reorder mode when a feed is selected
      // This allows users to reorder feeds even when a feed is selected
    }
  }, [selectedFeedId]);

  // Function to scroll to the selected item
  const scrollToSelectedItem = () => {
    if (selectedItemRef.current && feedItemsScrollAreaRef.current) {
      // Get the scroll container
      const scrollContainer = feedItemsScrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (!scrollContainer) return;

      // Calculate the position to scroll to
      const itemRect = selectedItemRef.current.getBoundingClientRect();
      const containerRect = scrollContainer.getBoundingClientRect();

      // Calculate the scroll position to center the item in the viewport
      const scrollTop =
        selectedItemRef.current.offsetTop -
        containerRect.height / 2 +
        itemRect.height / 2;

      // Scroll to the position
      scrollContainer.scrollTop = scrollTop;
    }
  };

  // Handle collapsing/expanding feeds while maintaining scroll position
  const handleFeedsCollapseChange = (open: boolean) => {
    // Always allow the user to manually collapse or expand the feeds
    // This fixes the flickering issue when selecting feeds
    setIsFeedsCollapsed(!open);

    // Use setTimeout to allow the DOM to update before scrolling
    setTimeout(() => {
      scrollToSelectedItem();
    }, 50);
  };



  // Scroll to selected item when it changes
  useEffect(() => {
    if (selectedItem && !selectedFeedId) {
      // Only scroll when an article is selected, not when a feed is selected
      // Use setTimeout to allow the DOM to update before scrolling
      setTimeout(() => {
        scrollToSelectedItem();
      }, 50);
    }
  }, [selectedItem]);

  // Re-sort feed items when sort option changes
  useEffect(() => {
    console.log(`Sort option changed to: ${sortOption}`);

    if (feedItems.length > 0) {
      console.log(`Re-sorting ${feedItems.length} feed items`);

      // Wait a moment to ensure any DOM updates have completed
      // This is important for the positive-terms sort which relies on DOM elements
      setTimeout(() => {
        try {
          // Sort the items with the new sort option
          const sortedItems = getSortedFeedItems(feedItems, sortOption);

          // Log the first few items before and after sorting
          console.log('Before sorting (first 3):', feedItems.slice(0, 3).map(item => ({
            id: item.id,
            title: item.title?.substring(0, 30) || 'No title',
            publishedAt: new Date(item.publishedAt).toISOString(),
            benefit: item.financialBenefitAmount || 0
          })));

          console.log('After sorting (first 3):', sortedItems.slice(0, 3).map(item => ({
            id: item.id,
            title: item.title?.substring(0, 30) || 'No title',
            publishedAt: new Date(item.publishedAt).toISOString(),
            benefit: item.financialBenefitAmount || 0
          })));

          // Update the feed items with the sorted items
          setFeedItems(sortedItems);

          // Keep the current selected item selected
          if (selectedItem) {
            const updatedSelectedItem = sortedItems.find(item => item.id === selectedItem.id);
            if (updatedSelectedItem) {
              setSelectedItem(updatedSelectedItem);
            }
          }

          // Force a re-render by updating a timestamp
          setLastRefreshTime(new Date());
        } catch (error) {
          console.error('Error sorting feed items:', error);
          toast({
            title: 'Error',
            description: 'Failed to sort feed items. Please try again.',
            variant: 'destructive',
          });
        }
      }, 100); // Small delay to ensure DOM is ready
    }
  }, [sortOption]);

  // Exit reorder mode when component unmounts or when navigating away
  useEffect(() => {
    return () => {
      // Clean up by exiting reorder mode
      setIsReorderMode(false);
    };
  }, []);

  // Periodically update article counts to ensure they're always up-to-date
  useEffect(() => {
    // Initial fetch - only if we have feeds
    if (feeds.length > 0) {
      console.log(`Fetching article counts for ${feeds.length} feeds`);
      fetchFeedArticleCounts();
    }

    // Set up interval to update counts every 30 seconds
    const intervalId = setInterval(() => {
      if (feeds.length > 0) {
        fetchFeedArticleCounts();
      }
    }, 30000);

    // Clean up interval on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [feeds.length]);

  // We no longer need this effect as the dashboard component will handle its own refresh
  // The dashboard component will refresh itself once when mounted

  // Handle selecting a saved article list
  const handleSelectSavedList = (listId: number) => {
    setSelectedSavedListId(listId);

    // We'll get the list name from the API when the SavedArticlesView component loads
    // Just set a placeholder for now
    setSelectedSavedListName(listId ? 'Saved List' : '');
  };

  // Handle saving the current article to a list
  const handleSaveArticleToList = async () => {
    if (!selectedItem || !selectedSaveListId) {
      toast({
        title: 'Error',
        description: 'Please select a list to save to',
        variant: 'destructive',
      });
      return;
    }

    try {
      await savedArticles.addArticle.mutateAsync({
        listId: selectedSaveListId,
        articleId: selectedItem.id
      });

      setIsSaveToListDialogOpen(false);
      setSelectedSaveListId(null);

      toast({
        title: 'Article saved',
        description: 'The article has been added to your saved list.',
      });
    } catch (error) {
      console.error('Error saving article:', error);
      toast({
        title: 'Error',
        description: 'Failed to save article to list',
        variant: 'destructive',
      });
    }
  };

  // Handle creating a new list and saving the article to it
  const [newListName, setNewListName] = useState('');
  const [isCreateListDialogOpen, setIsCreateListDialogOpen] = useState(false);

  const handleCreateListAndSave = async () => {
    if (!newListName.trim() || !selectedItem) {
      toast({
        title: 'Error',
        description: 'Please enter a list name',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Create the new list
      const newList = await savedArticleLists.createList.mutateAsync({ name: newListName });

      // Save the article to the new list
      await savedArticles.addArticle.mutateAsync({
        listId: newList.id,
        articleId: selectedItem.id
      });

      setNewListName('');
      setIsCreateListDialogOpen(false);
      setIsSaveToListDialogOpen(false);

      toast({
        title: 'Success',
        description: `Article saved to new list "${newListName}"`,
      });
    } catch (error) {
      console.error('Error creating list and saving article:', error);
      toast({
        title: 'Error',
        description: 'Failed to create list and save article',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-4 rss-tab overflow-y-auto" style={{ maxHeight: '100vh' }}>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold text-white">RSS Feeds</h2>
          <div className="flex items-center space-x-2 ml-4">
            <Button
              variant={activeTab === 'feeds' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('feeds')}
              className="flex items-center gap-1"
            >
              Feed Articles
            </Button>
            <Button
              variant={activeTab === 'saved' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('saved')}
              className="flex items-center gap-1"
            >
              Saved Articles
            </Button>
            <Button
              variant={activeTab === 'settings' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('settings')}
              className="flex items-center gap-1"
            >
              <Settings className="h-4 w-4 mr-1" />
              Settings
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={() => window.location.href = '/rss-dashboard'}
              className="flex items-center gap-1 bg-primary"
            >
              <BarChart className="h-4 w-4 mr-1" />
              Analytics
            </Button>
          </div>
        </div>

        {/* Search input */}
        {activeTab === 'feeds' && (
          <div className="flex items-center gap-2">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search all feeds..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64 text-white"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    searchAllFeeds();
                  }
                }}
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full"
                  onClick={clearSearch}
                  title="Clear search"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
            <Button
              variant="default"
              size="sm"
              onClick={searchAllFeeds}
              disabled={isSearching || !searchQuery.trim()}
              className="flex items-center gap-1"
            >
              {isSearching ? (
                <>
                  <div className="animate-spin h-4 w-4 border-b-2 border-white rounded-full mr-1"></div>
                  Searching...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-1" />
                  Search
                </>
              )}
            </Button>
          </div>
        )}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={importFeeds}
            disabled={isImporting}
            className="flex items-center gap-1"
            title="Import RSS feeds from a JSON file"
          >
            {isImporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-1"></div>
                Importing...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-1" />
                Import
              </>
            )}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={exportFeeds}
            disabled={isExporting || feeds.length === 0}
            className="flex items-center gap-1"
            title="Export all RSS feeds to a JSON file"
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-1"></div>
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-1" />
                Export
              </>
            )}
          </Button>

          <Dialog open={isAddFeedDialogOpen} onOpenChange={setIsAddFeedDialogOpen}>
            <DialogTrigger asChild>
              <Button
                className="flex items-center gap-2"
                onClick={() => {
                  // Don't automatically expand the feed list
                  // Let the user control this manually
                }}
              >
                <Plus className="h-4 w-4" />
                Add Feed
              </Button>
            </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-white">Add RSS Feed</DialogTitle>
              <DialogDescription className="text-white">
                Add a new Google Alerts RSS feed URL.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="feed-name" className="text-white">Feed Name</Label>
                <Input
                  id="feed-name"
                  placeholder="e.g., Social Security Alerts"
                  value={newFeedName}
                  onChange={(e) => setNewFeedName(e.target.value)}
                  className="text-white"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="feed-url" className="text-white">Feed URL</Label>
                <Input
                  id="feed-url"
                  placeholder="https://www.google.com/alerts/feeds/..."
                  value={newFeedUrl}
                  onChange={(e) => setNewFeedUrl(e.target.value)}
                  className="text-white"
                />
                <p className="text-sm text-white">
                  Enter the full Google Alerts RSS feed URL.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddFeedDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={addFeed} disabled={isLoading}>
                {isLoading ? 'Adding...' : 'Add Feed'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Feed Dialog */}
        <Dialog open={isEditFeedDialogOpen} onOpenChange={setIsEditFeedDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-white">Edit RSS Feed</DialogTitle>
              <DialogDescription className="text-white">
                Update your RSS feed details.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-feed-name" className="text-white">Feed Name</Label>
                <Input
                  id="edit-feed-name"
                  placeholder="e.g., Social Security Alerts"
                  value={editFeedName}
                  onChange={(e) => setEditFeedName(e.target.value)}
                  className="text-white"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-feed-url" className="text-white">Feed URL</Label>
                <Input
                  id="edit-feed-url"
                  placeholder="https://www.google.com/alerts/feeds/..."
                  value={editFeedUrl}
                  onChange={(e) => setEditFeedUrl(e.target.value)}
                  className="text-white"
                />
                <p className="text-sm text-white">
                  Enter the full Google Alerts RSS feed URL.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditFeedDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={editFeed} disabled={isLoading}>
                {isLoading ? 'Updating...' : 'Update Feed'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Save to List Dialog */}
        <Dialog open={isSaveToListDialogOpen} onOpenChange={setIsSaveToListDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Save to List</DialogTitle>
              <DialogDescription>
                Select a list to save the current article or create a new list.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              {savedLists.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground mb-4">You don't have any saved lists yet.</p>
                  <Button
                    onClick={() => {
                      setIsCreateListDialogOpen(true);
                      setIsSaveToListDialogOpen(false);
                    }}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First List
                  </Button>
                </div>
              ) : (
                <>
                  <div className="mb-4">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsCreateListDialogOpen(true);
                        setIsSaveToListDialogOpen(false);
                      }}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create New List
                    </Button>
                  </div>
                  <ScrollArea className="h-[200px]">
                    <div className="space-y-2">
                      {savedLists.map((list) => (
                        <div
                          key={list.id}
                          className={`p-3 rounded-md flex justify-between items-center cursor-pointer ${
                            selectedSaveListId === list.id ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
                          }`}
                          onClick={() => setSelectedSaveListId(list.id)}
                        >
                          <div className="flex flex-col">
                            <span className="font-medium">{list.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {list.articleCount} article{list.articleCount !== 1 ? 's' : ''}
                            </span>
                          </div>
                          {selectedSaveListId === list.id && (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsSaveToListDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleSaveArticleToList}
                disabled={!selectedSaveListId || savedArticles.addArticle.isPending}
              >
                {savedArticles.addArticle.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                    Saving...
                  </>
                ) : (
                  'Save Article'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Oldest Articles Dialog */}
        <Dialog open={isDeleteOldestDialogOpen} onOpenChange={setIsDeleteOldestDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Oldest Articles</DialogTitle>
              <DialogDescription>
                Delete the oldest articles from this feed. This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="delete-count" className="text-right">
                  Number of articles
                </Label>
                <Input
                  id="delete-count"
                  type="number"
                  min="1"
                  value={deleteOldestCount}
                  onChange={(e) => setDeleteOldestCount(parseInt(e.target.value) || 10)}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsDeleteOldestDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  if (deleteOldestFeedId) {
                    deleteOldestArticles(deleteOldestFeedId, deleteOldestCount);
                    setIsDeleteOldestDialogOpen(false);
                  }
                }}
                variant="destructive"
              >
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Create New List Dialog */}
        <Dialog open={isCreateListDialogOpen} onOpenChange={setIsCreateListDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New List</DialogTitle>
              <DialogDescription>
                Create a new list and save the current article to it.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="list-name">List Name</Label>
                <Input
                  id="list-name"
                  placeholder="Enter list name"
                  value={newListName}
                  onChange={(e) => setNewListName(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setIsCreateListDialogOpen(false);
                setIsSaveToListDialogOpen(true);
              }}>
                Cancel
              </Button>
              <Button
                onClick={handleCreateListAndSave}
                disabled={!newListName.trim() || savedArticleLists.createList.isPending}
              >
                {savedArticleLists.createList.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                    Creating...
                  </>
                ) : (
                  'Create & Save'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      {/* Dashboard will be rendered at the bottom of the page */}

      {activeTab === 'feeds' ? (
        <div className="flex flex-col space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Feed List - Collapsible */}
            <Card className={`md:col-span-1 ${isFeedsCollapsed ? 'md:col-span-auto' : ''}`}>
              <Collapsible open={!isFeedsCollapsed} onOpenChange={handleFeedsCollapseChange}>
              <CollapsibleTrigger asChild>
                <CardHeader className="pb-2 cursor-pointer" title={isFeedsCollapsed ? "Expand feeds" : "Collapse feeds"}>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle className="text-white">Your Feeds</CardTitle>
                    </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-7 w-7 z-10"
                      title="Refresh all feeds"
                      onClick={(e) => {
                        e.stopPropagation();
                        refreshAllFeeds();
                      }}
                      disabled={isLoading || isRefreshingAll}
                    >
                      {isRefreshingAll ? (
                        <div className="animate-spin h-4 w-4 border-b-2 border-primary rounded-full" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </Button>
                    <div className="h-8 w-8 flex items-center justify-center">
                      {isFeedsCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    </div>
                  </div>
                </div>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent>
                {isLoading && feeds.length === 0 ? (
                  <div className="flex justify-center items-center h-40">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : feeds.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-white">No feeds added yet</p>
                    <Button
                      variant="outline"
                      className="mt-4"
                      onClick={() => {
                        setIsAddFeedDialogOpen(true);
                        // Don't automatically change the collapse state
                      }}
                    >
                      Add Your First Feed
                    </Button>
                  </div>
                ) : (
                  <div>
                    {/* Reorder button */}
                    <div className="flex justify-end mb-2">
                      <Button
                        variant={isReorderMode ? "default" : "outline"}
                        size="sm"
                        onClick={() => setIsReorderMode(!isReorderMode)}
                        className="flex items-center gap-1"
                        title={isReorderMode ? "Exit reorder mode" : "Enter reorder mode to drag and reorder feeds"}
                      >
                        {isReorderMode ? (
                          <>
                            <div className="h-4 w-4 mr-1">✓</div>
                            Done
                          </>
                        ) : (
                          <>
                            <GripVertical className="h-4 w-4 mr-1" />
                            Reorder
                          </>
                        )}
                      </Button>
                    </div>

                    {/* Feed list with conditional drag and drop */}
                    <DragDropContext onDragEnd={(result) => {
                      handleFeedReorder(result);
                      // Don't exit reorder mode automatically to allow multiple reorderings
                    }}>
                      <Droppable droppableId="feeds-list">
                        {(provided) => (
                          <ScrollArea className="h-[calc(100vh-290px)]">
                            <div
                              className="space-y-2"
                              {...provided.droppableProps}
                              ref={provided.innerRef}
                            >
                              {feeds.map((feed, index) => (
                                <Draggable
                                  key={feed.id.toString()}
                                  draggableId={feed.id.toString()}
                                  index={index}
                                  isDragDisabled={!isReorderMode}
                                >
                                  {(provided, snapshot) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      // When in reorder mode, apply dragHandleProps to the entire feed item
                                      // This allows dragging from anywhere on the item
                                      {...(isReorderMode ? provided.dragHandleProps : {})}
                                      className={`p-3 rounded-md flex justify-between items-center ${
                                        isReorderMode ? 'cursor-move' : 'cursor-pointer'
                                      } ${
                                        selectedFeedId === feed.id ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
                                      } ${snapshot.isDragging ? 'opacity-70' : ''}`}
                                      onClick={() => {
                                        // Don't select feed when in reorder mode
                                        if (!isReorderMode) {
                                          // Don't change collapse state when selecting a feed
                                          // Just set the selected feed ID
                                          setSelectedFeedId(feed.id);

                                          // Don't scroll when selecting a feed
                                          // This prevents the page from scrolling down when a feed is selected
                                        }
                                        // When in reorder mode, clicking does nothing
                                        // The entire item is now draggable
                                      }}
                                    >
                                      <div className="flex-1 flex items-center">
                                        {isReorderMode && (
                                          <div
                                            className="mr-2"
                                            title="Drag anywhere to reorder"
                                          >
                                            <GripVertical className="h-4 w-4 text-gray-400" />
                                          </div>
                                        )}
                                        <div className="flex items-center">
                                          {/* Article count badge - always visible */}
                                          <div
                                            className={`mr-2 flex items-center justify-center rounded-full w-7 h-7 text-xs font-bold ${
                                              selectedFeedId === feed.id
                                                ? 'bg-primary-foreground text-primary'
                                                : 'bg-primary text-primary-foreground'
                                            }`}
                                            title={`${feedArticleCounts[feed.id] || 0} articles in this feed`}
                                          >
                                            {feedArticleCounts[feed.id] || 0}
                                          </div>
                                          <h3 className="font-medium text-white">
                                            {feed.name}
                                          </h3>
                                          {!isReorderMode && (
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                refreshFeed(feed.id);
                                              }}
                                              className={`h-6 w-6 ml-1 ${selectedFeedId === feed.id ? 'hover:bg-primary-foreground/10' : ''}`}
                                              title="Refresh feed"
                                              disabled={refreshingFeedId === feed.id || isRefreshingAll}
                                            >
                                              {refreshingFeedId === feed.id ? (
                                                <div className="animate-spin h-3 w-3 border-b-2 border-primary rounded-full" />
                                              ) : (
                                                <RefreshCw className="h-3 w-3" />
                                              )}
                                            </Button>
                                          )}
                                        </div>
                                      </div>
                                      {!isReorderMode && (
                                        <div className="flex gap-1">
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              openEditFeedDialog(feed);
                                            }}
                                            className={selectedFeedId === feed.id ? 'hover:bg-primary-foreground/10' : ''}
                                            title="Edit feed"
                                          >
                                            <Pencil className="h-4 w-4" />
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              // Open delete oldest articles dialog
                                              setDeleteOldestFeedId(feed.id);
                                              setDeleteOldestCount(10);
                                              setIsDeleteOldestDialogOpen(true);
                                            }}
                                            className={selectedFeedId === feed.id ? 'hover:bg-primary-foreground/10' : ''}
                                            title="Delete oldest articles"
                                          >
                                            <Clock className="h-4 w-4" />
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              deleteFeed(feed.id);
                                            }}
                                            className={selectedFeedId === feed.id ? 'hover:bg-primary-foreground/10' : ''}
                                            title="Delete feed"
                                          >
                                            <Trash2 className="h-4 w-4 text-destructive" />
                                          </Button>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </Draggable>
                              ))}
                              {provided.placeholder}
                            </div>
                          </ScrollArea>
                        )}
                      </Droppable>
                    </DragDropContext>
                  </div>
                )}
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Feed Items and Article Content */}
        <Card className={`md:col-span-2 ${isFeedsCollapsed ? 'md:col-span-3' : ''}`}>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <CardTitle
                  className="text-white"
                >
                  {selectedFeedId ? (
                    <span
                      className="cursor-pointer hover:underline flex items-center"
                      onClick={() => {
                        const selectedFeed = feeds.find(feed => feed.id === selectedFeedId);
                        if (selectedFeed?.url) {
                          window.open(selectedFeed.url, '_blank');
                        }
                      }}
                      title="Open RSS feed URL in new tab"
                    >
                      {feeds.find(feed => feed.id === selectedFeedId)?.name || 'Feed Items'}
                      <Link2 className="h-4 w-4 ml-1 opacity-70" />
                    </span>
                  ) : (
                    'Select a Feed'
                  )}
                </CardTitle>
                {selectedFeedId && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => refreshFeed(selectedFeedId)}
                    disabled={refreshingFeedId === selectedFeedId || isRefreshingAll}
                    className="h-7 w-7 ml-2"
                    title="Refresh this feed"
                  >
                    {refreshingFeedId === selectedFeedId ? (
                      <div className="animate-spin h-4 w-4 border-b-2 border-primary rounded-full" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                  </Button>
                )}
              </div>
            </div>

            <div className="flex justify-between items-center mt-1">
              <div>
                <CardDescription className="text-white">
                  {selectedFeedId
                    ? `${feedItems.length} items in this feed`
                    : 'Select a feed to view its items'}
                </CardDescription>
                {selectedFeedId && (
                  <div className="flex items-center text-sm text-muted-foreground mt-1">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>
                      Last refresh: {
                        feeds.find(feed => feed.id === selectedFeedId)?.lastRefreshTime
                          ? formatDistanceToNow(new Date(feeds.find(feed => feed.id === selectedFeedId)?.lastRefreshTime!), { addSuffix: true })
                          : 'Never'
                      }
                    </span>
                  </div>
                )}
              </div>
            </div>
          </CardHeader>

          {/* Generation timestamp and action buttons */}
          {selectedFeedId && feedItems.length > 0 && (
            <div className="px-6 py-2 border-t border-b border-border/40 mb-2 bg-background/30">
              <div className="flex justify-between items-center">
                <span className="text-xs text-muted-foreground">
                  Generated on {format(new Date(), 'MMM d, yyyy h:mm a')}
                </span>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyFeedText(selectedFeedId)}
                    disabled={isCopyingText || isPdfGenerating}
                    className="flex items-center gap-1 h-8"
                    title="Copy all articles as formatted text"
                  >
                    {isCopyingText ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-1"></div>
                        Copying...
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4 mr-1" />
                        Copy All
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadFeedDocument(selectedFeedId)}
                    disabled={isPdfGenerating || isCopyingText}
                    className="flex items-center gap-1 h-8"
                    title="Download all articles as a PDF document"
                  >
                    {isPdfGenerating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-1"></div>
                        Generating...
                      </>
                    ) : (
                      <>
                        <FileDown className="h-4 w-4 mr-1" />
                        Download PDF
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
          <CardContent>
            {!selectedFeedId ? (
              <div className="text-center py-16">
                <p className="text-white">Select a feed from the list to view its items</p>
              </div>
            ) : isLoading ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : feedItems.length === 0 ? (
              <div className="text-center py-16">
                <p className="text-white">No items in this feed</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Try refreshing the feed using the refresh button above
                </p>
              </div>
            ) : (
              <div className={`grid grid-cols-1 ${isFeedsCollapsed ? 'md:grid-cols-3' : 'md:grid-cols-2'} gap-4`}>
                {/* Feed Items List */}
                <div>
                  {/* Sorting options */}
                  <div className="flex justify-between items-center mb-3">
                    {isSearchActive ? (
                      <div className="flex items-center gap-2">
                        <h3 className="text-white font-medium">
                          Search Results: <span className="text-primary">{searchResults.length}</span> articles found
                        </h3>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={clearSearch}
                          className="ml-2"
                        >
                          <X className="h-3 w-3 mr-1" />
                          Clear Search
                        </Button>
                      </div>
                    ) : (
                      <>
                        <div className="text-sm text-muted-foreground">
                          {feedItems.length} articles
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">Sort by:</span>
                          <select
                            className="bg-background text-white text-sm border border-border rounded px-2 py-1"
                            value={sortOption}
                            onChange={(e) => {
                              const newSortOption = e.target.value as SortOption;
                              console.log(`Dropdown changed to: ${newSortOption}`);

                              // Set the new sort option - this will trigger the useEffect
                              setSortOption(newSortOption);

                              // Add a toast notification to confirm the sort change
                              let sortDescription = "newest first";
                              if (newSortOption === 'benefit') {
                                sortDescription = "highest benefit";
                              } else if (newSortOption === 'positive-terms') {
                                sortDescription = "most positive terms";
                              }

                              toast({
                                title: "Sort Changed",
                                description: `Articles sorted by ${sortDescription}`,
                                duration: 2000
                              });

                              // Force a re-render
                              setLastRefreshTime(new Date());
                            }}
                          >
                            <option value="newest">Newest First</option>
                            <option value="benefit">Highest Benefit</option>
                            <option value="positive-terms">Most Positive Terms</option>
                          </select>
                        </div>
                      </>
                    )}
                  </div>

                  <ScrollArea
                    ref={feedItemsScrollAreaRef}
                    className="h-[calc(100vh-250px)] pr-4"
                  >
                    <div className="space-y-3">
                      {isSearchActive ? searchResults.map((item) => (
                        <div
                          key={item.id}
                          data-id={item.id}
                          ref={selectedItem?.id === item.id ? selectedItemRef : null}
                          className={`p-3 rounded-md cursor-pointer border ${
                            selectedItem?.id === item.id
                              ? 'border-primary bg-primary/5'
                              : 'border-border hover:bg-muted'
                          } ${
                            item.isRead ? 'opacity-70' : ''
                          }`}
                          data-sentiment="neutral" // Default sentiment, will be updated by the ref callback
                          onClick={() => fetchArticleContent(item)}
                        >
                          {/* Item title and actions */}
                          <div className="flex flex-col mb-2">
                            {/* Title with better styling */}
                            <div className="mb-2 w-full">
                              <h3
                                className={`font-medium text-sm ${item.isRead ? '' : 'font-semibold'} text-white leading-snug`}
                                style={{
                                  color: 'white',
                                  lineHeight: '1.4',
                                  wordBreak: 'break-word'
                                }}
                              >
                                <div
                                  dangerouslySetInnerHTML={{
                                    __html: processHtmlContent(item.title)
                                  }}
                                  className="feed-item-title"
                                  ref={(el) => {
                                    if (el) {
                                      // Extract highlight counts from the processed content
                                      const countDiv = el.querySelector('div[data-positive-count]');
                                      if (countDiv) {
                                        const positiveCount = parseInt(countDiv.getAttribute('data-positive-count') || '0', 10);
                                        const negativeCount = parseInt(countDiv.getAttribute('data-negative-count') || '0', 10);
                                        const neutralCount = parseInt(countDiv.getAttribute('data-neutral-count') || '0', 10);

                                        // Store the counts as data attributes on the parent element for styling
                                        el.parentElement?.setAttribute('data-positive-count', positiveCount.toString());
                                        el.parentElement?.setAttribute('data-negative-count', negativeCount.toString());
                                        el.parentElement?.setAttribute('data-neutral-count', neutralCount.toString());

                                        // Find the article container (going up the DOM tree)
                                        const articleContainer = el.closest('[data-id]');
                                        if (articleContainer) {
                                          // Determine sentiment based on counts
                                          let sentimentClass = '';
                                          if (positiveCount > negativeCount && positiveCount >= 3) {
                                            sentimentClass = 'article-positive-sentiment';
                                            articleContainer.setAttribute('data-sentiment', 'positive');
                                          } else if (negativeCount > positiveCount && negativeCount >= 3) {
                                            sentimentClass = 'article-negative-sentiment';
                                            articleContainer.setAttribute('data-sentiment', 'negative');
                                          } else if (neutralCount > (positiveCount + negativeCount) && neutralCount >= 3) {
                                            // If neutral terms dominate and there are at least 3 of them
                                            sentimentClass = 'article-neutral-sentiment';
                                            articleContainer.setAttribute('data-sentiment', 'neutral');
                                          } else {
                                            sentimentClass = 'article-neutral-sentiment';
                                            articleContainer.setAttribute('data-sentiment', 'neutral');
                                          }

                                          // Apply the sentiment class
                                          articleContainer.classList.remove('article-positive-sentiment', 'article-negative-sentiment', 'article-neutral-sentiment');
                                          articleContainer.classList.add(sentimentClass);
                                        }
                                      }
                                    }
                                  }}
                                />

                                {/* Display financial benefit if available */}
                                {item.financialBenefitAmount > 0 && (
                                  <div className="mt-1 text-xs font-semibold" style={{ color: '#4ade80' }}>
                                    Benefit: ${item.financialBenefitAmount.toLocaleString()}
                                    {item.financialBenefitDescription && (
                                      <span className="ml-1 font-normal opacity-80">
                                        ({item.financialBenefitDescription})
                                      </span>
                                    )}
                                  </div>
                                )}

                                {/* Display highlight counts */}
                                <div className="mt-1 text-xs flex gap-2">
                                  <span className="font-semibold" style={{ color: '#4ade80' }}>
                                    <span className="text-white opacity-70">Positive:</span> <span data-positive-count-display>0</span>
                                  </span>
                                  <span className="font-semibold" style={{ color: '#f87171' }}>
                                    <span className="text-white opacity-70">Negative:</span> <span data-negative-count-display>0</span>
                                  </span>
                                  <span className="font-semibold" style={{ color: '#94a3b8' }}>
                                    <span className="text-white opacity-70">N:</span> <span data-neutral-count-display>0</span>
                                  </span>
                                </div>
                              </h3>
                            </div>

                            {/* Action buttons in their own row */}
                            <div className="flex gap-1 self-end">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedItem(item);
                                  setIsSaveToListDialogOpen(true);
                                }}
                                title="Save to list"
                              >
                                <Plus className="h-3 w-3 text-primary" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  window.open(item.link, '_blank');
                                }}
                                title="Open original article"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deleteArticle(item);
                                }}
                                title="Delete article"
                              >
                                <Trash2 className="h-3 w-3 text-destructive" />
                              </Button>
                            </div>
                          </div>
                          <div className="flex items-center text-xs text-white">
                            <span className="bg-primary/10 text-primary px-1.5 py-0.5 rounded-sm mr-2">
                              {formatDistanceToNow(new Date(item.publishedAt), { addSuffix: true })}
                            </span>
                            <span className="truncate text-white">{item.link.replace(/^https?:\/\//, '').split('/')[0]}</span>
                          </div>
                          {item.content && (
                            <p className="text-xs mt-2 line-clamp-2 text-white"
                              dangerouslySetInnerHTML={{
                                __html: processHtmlContent(item.content)
                              }}
                              style={{ color: 'white' }}
                            />
                          )}
                        </div>
                      )) : feedItems.map((item) => (
                        <div
                          key={item.id}
                          data-id={item.id}
                          ref={selectedItem?.id === item.id ? selectedItemRef : null}
                          className={`p-3 rounded-md cursor-pointer border ${
                            selectedItem?.id === item.id
                              ? 'border-primary bg-primary/5'
                              : 'border-border hover:bg-muted'
                          } ${
                            item.isRead ? 'opacity-70' : ''
                          }`}
                          data-sentiment="neutral" // Default sentiment, will be updated by the ref callback
                          onClick={() => fetchArticleContent(item)}
                        >
                          {/* Item title and actions */}
                          <div className="flex flex-col mb-2">
                            {/* Title with better styling */}
                            <div className="mb-2 w-full">
                              <h3
                                className={`font-medium text-sm ${item.isRead ? '' : 'font-semibold'} text-white leading-snug`}
                                style={{
                                  color: 'white',
                                  lineHeight: '1.4',
                                  wordBreak: 'break-word'
                                }}
                              >
                                <div
                                  dangerouslySetInnerHTML={{
                                    __html: processHtmlContent(item.title)
                                  }}
                                  className="feed-item-title"
                                  ref={(el) => {
                                    if (el) {
                                      // Get the counts from the item
                                      const positiveCount = item.positiveTermCount || 0;
                                      const negativeCount = item.negativeTermCount || 0;
                                      const neutralCount = item.neutralTermCount || 0;

                                      // Store the counts as data attributes on the parent element for styling
                                      el.parentElement?.setAttribute('data-positive-count', positiveCount.toString());
                                      el.parentElement?.setAttribute('data-negative-count', negativeCount.toString());
                                      el.parentElement?.setAttribute('data-neutral-count', neutralCount.toString());

                                      // Find the article container (going up the DOM tree)
                                      const articleContainer = el.closest('[data-id]');
                                      if (articleContainer) {
                                        // Determine sentiment based on counts
                                        let sentimentClass = '';
                                        if (positiveCount > negativeCount && positiveCount >= 3) {
                                          sentimentClass = 'article-positive-sentiment';
                                          articleContainer.setAttribute('data-sentiment', 'positive');
                                        } else if (negativeCount > positiveCount && negativeCount >= 3) {
                                          sentimentClass = 'article-negative-sentiment';
                                          articleContainer.setAttribute('data-sentiment', 'negative');
                                        } else if (neutralCount > (positiveCount + negativeCount) && neutralCount >= 3) {
                                          // If neutral terms dominate and there are at least 3 of them
                                          sentimentClass = 'article-neutral-sentiment';
                                          articleContainer.setAttribute('data-sentiment', 'neutral');
                                        } else {
                                          sentimentClass = 'article-neutral-sentiment';
                                          articleContainer.setAttribute('data-sentiment', 'neutral');
                                        }

                                        // Apply the sentiment class
                                        articleContainer.classList.remove('article-positive-sentiment', 'article-negative-sentiment', 'article-neutral-sentiment');
                                        articleContainer.classList.add(sentimentClass);
                                      }
                                    }
                                  }}
                                />

                                {/* Display financial benefit if available */}
                                {item.financialBenefitAmount > 0 && (
                                  <div className="mt-1 text-xs font-semibold" style={{ color: '#4ade80' }}>
                                    Benefit: ${item.financialBenefitAmount.toLocaleString()}
                                    {item.financialBenefitDescription && (
                                      <span className="ml-1 font-normal opacity-80">
                                        ({item.financialBenefitDescription})
                                      </span>
                                    )}
                                  </div>
                                )}

                                {/* Display highlight counts */}
                                <div className="mt-1 text-xs flex gap-2">
                                  <span className="font-semibold" style={{ color: '#4ade80' }}>
                                    <span className="text-white opacity-70">Positive:</span> <span data-positive-count-display>{item.positiveTermCount || 0}</span>
                                  </span>
                                  <span className="font-semibold" style={{ color: '#f87171' }}>
                                    <span className="text-white opacity-70">Negative:</span> <span data-negative-count-display>{item.negativeTermCount || 0}</span>
                                  </span>
                                  <span className="font-semibold" style={{ color: '#94a3b8' }}>
                                    <span className="text-white opacity-70">N:</span> <span data-neutral-count-display>{item.neutralTermCount || 0}</span>
                                  </span>
                                  {/* Show an indicator if the article has been analyzed */}
                                  {(item.positiveTermCount > 0 || item.negativeTermCount > 0 || item.neutralTermCount > 0) && (
                                    <span className="text-white opacity-70 text-xs ml-1" title="Financial term analysis has been performed">
                                      (analyzed)
                                    </span>
                                  )}
                                </div>
                              </h3>
                            </div>

                            {/* Action buttons in their own row */}
                            <div className="flex gap-1 self-end">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedItem(item);
                                  setIsSaveToListDialogOpen(true);
                                }}
                                title="Save to list"
                              >
                                <Plus className="h-3 w-3 text-primary" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  window.open(item.link, '_blank');
                                }}
                                title="Open original article"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deleteArticle(item);
                                }}
                                title="Delete article"
                              >
                                <Trash2 className="h-3 w-3 text-destructive" />
                              </Button>
                            </div>
                          </div>
                          <div className="flex items-center text-xs text-white">
                            <span className="bg-primary/10 text-primary px-1.5 py-0.5 rounded-sm mr-2">
                              {formatDistanceToNow(new Date(item.publishedAt), { addSuffix: true })}
                            </span>
                            <span className="truncate text-white">{item.link.replace(/^https?:\/\//, '').split('/')[0]}</span>
                            {isSearchActive && (
                              <span className="ml-2 bg-secondary/20 text-secondary px-1.5 py-0.5 rounded-sm" title="Source feed">
                                From: {feeds.find(feed => feed.id === item.feedId)?.name || 'Unknown Feed'}
                              </span>
                            )}
                          </div>
                          {item.content && (
                            <p className="text-xs mt-2 line-clamp-2 text-white"
                              dangerouslySetInnerHTML={{
                                __html: processHtmlContent(item.content)
                              }}
                              style={{ color: 'white' }}
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>

                {/* Article Content */}
                <div className={isFeedsCollapsed ? 'md:col-span-2' : ''}>
                  {selectedItem ? (
                    isLoadingArticle ? (
                      <div className="flex justify-center items-center h-[calc(100vh-220px)]">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      </div>
                    ) : (
                      <div>
                        {/* Article Header Card */}
                        <Card className="mb-4 border border-primary/20" id="article-header-card">
                          <CardHeader className="pb-2">
                            {/* Title in its own section with proper styling */}
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <CardTitle className="text-lg font-semibold leading-tight text-white">
                                  <div
                                    dangerouslySetInnerHTML={{ __html: processHtmlContent(selectedItem.title) }}
                                    className="article-title"
                                    style={{
                                      color: 'white',
                                      lineHeight: '1.4',
                                      wordBreak: 'break-word'
                                    }}
                                  />
                                </CardTitle>

                                {/* Publication info */}
                                <CardDescription className="flex items-center text-xs text-white mt-2">
                                  <span className="bg-primary/10 text-primary px-2 py-0.5 rounded-sm mr-2">
                                    {formatDistanceToNow(new Date(selectedItem.publishedAt), { addSuffix: true })}
                                  </span>
                                  <span className="truncate text-white">
                                    Source: {selectedItem.link.replace(/^https?:\/\//, '').split('/')[0]}
                                  </span>
                                </CardDescription>
                              </div>

                              {/* Space for article actions */}
                              <div className="flex gap-2 ml-2">
                                {/* Buttons moved to the green area */}
                              </div>
                            </div>
                          </CardHeader>

                          {/* Action buttons in a separate section - icon only */}
                          <CardContent className="pt-0 pb-3">
                            <div className="flex justify-center gap-4 mt-2">
                              <Button
                                variant="primary"
                                size="icon"
                                onClick={() => setIsSaveToListDialogOpen(true)}
                                className="h-9 w-9"
                                title="Save to list"
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="default"
                                size="icon"
                                onClick={() => window.open(selectedItem.link, '_blank')}
                                className="h-9 w-9"
                                title="Open original article"
                              >
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => {
                                  // Set forceRefresh flag to true to bypass cache
                                  const itemWithRefreshFlag = { ...selectedItem, forceRefresh: true };
                                  fetchArticleContent(itemWithRefreshFlag);
                                }}
                                className="h-9 w-9"
                                title="Refresh content"
                              >
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="destructive"
                                size="icon"
                                onClick={() => {
                                  deleteArticle(selectedItem);
                                }}
                                className="h-9 w-9"
                                title="Delete article"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>

                        {/* Separator before content */}
                        <Separator className="mb-4" />
                        <ScrollArea className={`${isFeedsCollapsed ? 'h-[calc(100vh-220px)]' : 'h-[calc(100vh-220px)]'}`}>
                          <div className="prose prose-sm max-w-none dark:prose-invert">
                            {articleContent ? (
                              <div>
                                {/* Financial term highlight counts */}
                                <div className="flex items-center gap-4 mb-4 p-2 border border-border rounded">
                                  <div className="text-sm font-medium">Financial Term Analysis:</div>
                                  <div className="flex gap-4">
                                    <div className="flex items-center gap-1">
                                      <div className="w-3 h-3 rounded-full bg-green-400"></div>
                                      <span className="text-white">Positive: </span>
                                      <span className="font-bold text-green-400" id="article-positive-count">0</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <div className="w-3 h-3 rounded-full bg-red-400"></div>
                                      <span className="text-white">Negative: </span>
                                      <span className="font-bold text-red-400" id="article-negative-count">0</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <div className="w-3 h-3 rounded-full bg-[#94a3b8]"></div>
                                      <span className="text-white">N: </span>
                                      <span className="font-bold text-[#94a3b8]" id="article-neutral-count">0</span>
                                    </div>
                                  </div>
                                </div>

                                <div
                                  dangerouslySetInnerHTML={{ __html: articleContent }}
                                  className="article-content"
                                  id="article-content-container"
                                  style={{
                                    color: 'white'
                                  } as React.CSSProperties}
                                  ref={(el) => {
                                    if (el) {
                                      // Extract highlight counts from the processed content
                                      const countDiv = el.querySelector('div[data-positive-count]');
                                      if (countDiv) {
                                        const positiveCount = parseInt(countDiv.getAttribute('data-positive-count') || '0', 10);
                                        const negativeCount = parseInt(countDiv.getAttribute('data-negative-count') || '0', 10);
                                        const neutralCount = parseInt(countDiv.getAttribute('data-neutral-count') || '0', 10);

                                        // Update the count displays
                                        const positiveCountDisplay = document.getElementById('article-positive-count');
                                        const negativeCountDisplay = document.getElementById('article-negative-count');
                                        const neutralCountDisplay = document.getElementById('article-neutral-count');

                                        if (positiveCountDisplay) positiveCountDisplay.textContent = positiveCount.toString();
                                        if (negativeCountDisplay) negativeCountDisplay.textContent = negativeCount.toString();
                                        if (neutralCountDisplay) neutralCountDisplay.textContent = neutralCount.toString();

                                        // Apply sentiment highlighting to the article content and header card
                                        const articleHeaderCard = document.getElementById('article-header-card');

                                        // Determine sentiment class
                                        let sentimentClass = '';
                                        if (positiveCount > negativeCount && positiveCount >= 3) {
                                          sentimentClass = 'article-positive-sentiment';
                                        } else if (negativeCount > positiveCount && negativeCount >= 3) {
                                          sentimentClass = 'article-negative-sentiment';
                                        } else if (neutralCount > (positiveCount + negativeCount) && neutralCount >= 3) {
                                          // If neutral terms dominate and there are at least 3 of them
                                          sentimentClass = 'article-neutral-sentiment';
                                        } else {
                                          sentimentClass = 'article-neutral-sentiment';
                                        }

                                        // Apply to article content
                                        el.classList.remove('article-positive-sentiment', 'article-negative-sentiment', 'article-neutral-sentiment');
                                        el.classList.add(sentimentClass);

                                        // Apply to article header card
                                        if (articleHeaderCard) {
                                          articleHeaderCard.classList.remove('article-positive-sentiment', 'article-negative-sentiment', 'article-neutral-sentiment');
                                          articleHeaderCard.classList.add(sentimentClass);
                                        }

                                        // Also update the feed item counts
                                        const feedItemElements = document.querySelectorAll('[data-positive-count-display]');
                                        const feedItemNegElements = document.querySelectorAll('[data-negative-count-display]');
                                        const feedItemNeutralElements = document.querySelectorAll('[data-neutral-count-display]');

                                        feedItemElements.forEach(el => {
                                          if (el.closest(`[data-id="${selectedItem.id}"]`)) {
                                            el.textContent = positiveCount.toString();
                                          }
                                        });

                                        feedItemNegElements.forEach(el => {
                                          if (el.closest(`[data-id="${selectedItem.id}"]`)) {
                                            el.textContent = negativeCount.toString();
                                          }
                                        });

                                        feedItemNeutralElements.forEach(el => {
                                          if (el.closest(`[data-id="${selectedItem.id}"]`)) {
                                            el.textContent = neutralCount.toString();
                                          }
                                        });
                                      }
                                    }
                                  }}
                                />
                              </div>
                            ) : (
                              <div className="text-center py-8">
                                <p className="text-white mb-2">No content available for this article.</p>
                                <div className="flex gap-2 justify-center">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => window.open(selectedItem.link, '_blank')}
                                  >
                                    Visit Original Article
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      // Set forceRefresh flag to true to bypass cache
                                      const itemWithRefreshFlag = { ...selectedItem, forceRefresh: true };
                                      fetchArticleContent(itemWithRefreshFlag);
                                    }}
                                  >
                                    <RefreshCw className="h-3 w-3 mr-1" />
                                    Retry Fetch
                                  </Button>
                                  {selectedItem.link.includes('msn.com') && (
                                    <>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                          // Extract article ID and open mobile version
                                          const articleIdMatch = selectedItem.link.match(/\/ar-([A-Za-z0-9]+)/);
                                          if (articleIdMatch && articleIdMatch[1]) {
                                            const articleId = articleIdMatch[1];
                                            window.open(`https://www.msn.com/en-us/news/other/ar-${articleId}?ocid=msnews`, '_blank');
                                          } else {
                                            window.open(selectedItem.link, '_blank');
                                          }
                                        }}
                                      >
                                        Try Mobile Version
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                          window.open(`https://archive.ph/${selectedItem.link}`, '_blank');
                                        }}
                                      >
                                        View on Archive.ph
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                          window.open(`https://12ft.io/proxy?q=${encodeURIComponent(selectedItem.link)}`, '_blank');
                                        }}
                                      >
                                        View on 12ft.io
                                      </Button>
                                    </>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </ScrollArea>
                      </div>
                    )
                  ) : (
                    <div className="flex justify-center items-center h-[calc(100vh-220px)] text-center">
                      <div>
                        <p className="text-white">Select an item to view its content</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>



          {/* Enhanced RSS Dashboard at the bottom - only show when not searching */}
          {!isSearchActive && (
            <div className="w-full mt-8">
              <EnhancedDashboard
                allFeedItems={allFeedItems}
                onRefresh={() => {
                  console.log('Dashboard refresh triggered');
                  // Force a refresh of all feed items for the dashboard
                  fetchAllFeedItems(false); // Pass false to show toast notifications
                }}
                lastRefreshed={lastRefreshTime}
                isRefreshing={isRefreshingAll}
                onOpenArticle={(articleId) => {
                  // Find the article by ID
                  const article = allFeedItems.find(item => item.id === articleId);
                  if (article) {
                    console.log(`Opening article with ID ${articleId} from dashboard`);

                    // Set the active tab to 'feeds' to ensure the article view is visible
                    setActiveTab('feeds');

                    // Set the selected feed ID if needed
                    if (article.feedId !== selectedFeedId) {
                      console.log(`Switching from feed ${selectedFeedId} to feed ${article.feedId}`);
                      setSelectedFeedId(article.feedId);

                      // Wait for feed items to load before selecting the article
                      setTimeout(() => {
                        fetchArticleContent(article);

                        // Scroll to the top of the article content
                        const articleContentContainer = document.getElementById('article-content-container');
                        if (articleContentContainer) {
                          articleContentContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }
                      }, 500); // Increased timeout to ensure feed items are loaded
                    } else {
                      // Directly fetch the article content
                      fetchArticleContent(article);

                      // Scroll to the top of the article content after a short delay
                      setTimeout(() => {
                        const articleContentContainer = document.getElementById('article-content-container');
                        if (articleContentContainer) {
                          articleContentContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }
                      }, 100);
                    }
                  } else {
                    console.error(`Article with ID ${articleId} not found in allFeedItems`);
                    toast({
                      title: 'Error',
                      description: 'Article not found. Try refreshing the dashboard.',
                      variant: 'destructive',
                    });
                  }
                }}
                onSaveArticle={(articleId) => {
                  // Find the article by ID
                  const article = allFeedItems.find(item => item.id === articleId);
                  if (article) {
                    // Set the selected item and open the save dialog
                    setSelectedItem(article);
                    setIsSaveToListDialogOpen(true);
                  }
                }}
                onOpenInBrowser={(articleId) => {
                  // Find the article by ID
                  const article = allFeedItems.find(item => item.id === articleId);
                  if (article && article.link) {
                    window.open(article.link, '_blank');
                  }
                }}
              />
            </div>
          )}
        </div>
      ) : activeTab === 'saved' ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Saved Article Lists */}
          <Card className="md:col-span-1">
            <CardHeader className="pb-2">
              <CardTitle className="text-white">Saved Articles</CardTitle>
            </CardHeader>
            <CardContent>
              <SavedArticleLists
                onSelectList={handleSelectSavedList}
                selectedListId={selectedSavedListId}
              />
            </CardContent>
          </Card>

          {/* Saved Articles View */}
          <Card className="md:col-span-2">
            <CardContent>
              <SavedArticlesView
                listId={selectedSavedListId}
                listName={selectedSavedListName}
              />
            </CardContent>
          </Card>
        </div>
      ) : (
        /* Settings Tab */
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Financial Terms Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-white">Financial Term Highlighting Settings</CardTitle>
              <CardDescription className="text-white">
                Customize which words are highlighted as financial benefits (green), losses (red), or neutral terms (N) (gray) in articles
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Financial Terms Settings Component */}
              <FinancialTermsSettings />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

// Financial Terms Settings Component
const FinancialTermsSettings = () => {
  const [positiveTerms, setPositiveTerms] = useState('');
  const [negativeTerms, setNegativeTerms] = useState('');
  const [neutralTerms, setNeutralTerms] = useState('');
  const [isLoadingTerms, setIsLoadingTerms] = useState(false);
  const [isSavingTerms, setIsSavingTerms] = useState(false);
  const { toast } = useToast();

  // Load financial terms on component mount
  useEffect(() => {
    loadFinancialTerms();
  }, []);

  // Load financial terms from the module
  const loadFinancialTerms = async () => {
    try {
      setIsLoadingTerms(true);

      // Import the financial term highlighter module
      const module = await import('./financial-term-highlighter');

      // Get the terms
      const positiveTermsList = module.getPositiveTerms();
      const negativeTermsList = module.getNegativeTerms();
      const neutralTermsList = module.getNeutralTerms();

      // Convert arrays to comma-separated strings
      setPositiveTerms(positiveTermsList.join(', '));
      setNegativeTerms(negativeTermsList.join(', '));
      setNeutralTerms(neutralTermsList.join(', '));
    } catch (error) {
      console.error('Error loading financial terms:', error);
      toast({
        title: 'Error',
        description: 'Failed to load financial terms',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingTerms(false);
    }
  };

  // Save financial terms
  const saveFinancialTerms = async (type: 'positive' | 'negative' | 'neutral', terms: string) => {
    try {
      setIsSavingTerms(true);

      // Split the comma-separated string into an array of terms
      const termsArray = terms.split(',').map(term => term.trim()).filter(term => term !== '');

      // Import the module dynamically
      const module = await import('./financial-term-highlighter');

      // Update the terms in the module
      if (type === 'positive') {
        module.setPositiveTerms(termsArray);
      } else if (type === 'negative') {
        module.setNegativeTerms(termsArray);
      } else if (type === 'neutral') {
        module.setNeutralTerms(termsArray);
      }

      toast({
        title: 'Success',
        description: `${type.charAt(0).toUpperCase() + type.slice(1)} financial terms saved successfully`,
      });
    } catch (error) {
      console.error(`Error saving ${type} terms:`, error);
      toast({
        title: 'Error',
        description: `Failed to save ${type} financial terms`,
        variant: 'destructive',
      });
    } finally {
      setIsSavingTerms(false);
    }
  };

  // Reset terms to defaults
  const resetToDefaults = async () => {
    try {
      setIsSavingTerms(true);

      // Import the module dynamically
      const module = await import('./financial-term-highlighter');

      // Reset terms to defaults
      module.resetToDefaultTerms();

      // Reload the terms
      await loadFinancialTerms();

      toast({
        title: 'Success',
        description: 'Financial terms reset to defaults',
      });
    } catch (error) {
      console.error('Error resetting financial terms:', error);
      toast({
        title: 'Error',
        description: 'Failed to reset financial terms',
        variant: 'destructive',
      });
    } finally {
      setIsSavingTerms(false);
    }
  };

  return (
    <div className="space-y-6">
      {isLoadingTerms ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Positive Terms (Benefits) */}
          <div className="space-y-2">
            <Label htmlFor="positive-terms" className="text-white flex items-center">
              <div className="w-3 h-3 rounded-full bg-[#4ade80] mr-2"></div>
              Benefit Terms (Green Highlight)
            </Label>
            <div className="relative">
              <textarea
                id="positive-terms"
                className="w-full h-32 p-3 bg-background border border-border rounded-md text-white"
                placeholder="Enter comma-separated terms, e.g.: benefit, increase, gain, bonus"
                value={positiveTerms}
                onChange={(e) => setPositiveTerms(e.target.value)}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Words that indicate financial benefits, increases, or positive changes
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => saveFinancialTerms('positive', positiveTerms)}
              disabled={isSavingTerms}
            >
              {isSavingTerms ? 'Saving...' : 'Save Benefit Terms'}
            </Button>
          </div>

          {/* Negative Terms (Losses) */}
          <div className="space-y-2">
            <Label htmlFor="negative-terms" className="text-white flex items-center">
              <div className="w-3 h-3 rounded-full bg-[#f87171] mr-2"></div>
              Loss Terms (Red Highlight)
            </Label>
            <div className="relative">
              <textarea
                id="negative-terms"
                className="w-full h-32 p-3 bg-background border border-border rounded-md text-white"
                placeholder="Enter comma-separated terms, e.g.: cut, decrease, loss, tax"
                value={negativeTerms}
                onChange={(e) => setNegativeTerms(e.target.value)}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Words that indicate financial losses, decreases, or negative changes
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => saveFinancialTerms('negative', negativeTerms)}
              disabled={isSavingTerms}
            >
              {isSavingTerms ? 'Saving...' : 'Save Loss Terms'}
            </Button>
          </div>

          {/* Neutral Terms */}
          <div className="space-y-2">
            <Label htmlFor="neutral-terms" className="text-white flex items-center">
              <div className="w-3 h-3 rounded-full bg-[#94a3b8] mr-2"></div>
              N Terms (Gray Highlight)
            </Label>
            <div className="relative">
              <textarea
                id="neutral-terms"
                className="w-full h-32 p-3 bg-background border border-border rounded-md text-white"
                placeholder="Enter comma-separated terms, e.g.: social security, irs, tax, finance"
                value={neutralTerms}
                onChange={(e) => setNeutralTerms(e.target.value)}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Neutral financial terms without positive or negative connotations (e.g., "social security", "IRS", "tax")
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => saveFinancialTerms('neutral', neutralTerms)}
              disabled={isSavingTerms}
            >
              {isSavingTerms ? 'Saving...' : 'Save N Terms'}
            </Button>
          </div>

          {/* Reset to Defaults Button */}
          <div className="pt-4 border-t border-border">
            <Button
              variant="destructive"
              size="sm"
              onClick={resetToDefaults}
              disabled={isSavingTerms}
            >
              Reset to Default Terms
            </Button>
            <p className="text-sm text-muted-foreground mt-2">
              This will restore the original financial term lists and remove any customizations
            </p>
          </div>
        </>
      )}
    </div>
  );
}
