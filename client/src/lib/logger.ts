/**
 * Logger utility to control console output based on environment and log level
 */

// Log levels
export enum LogLevel {
  NONE = 0,    // No logging
  ERROR = 1,   // Only errors
  WARN = 2,    // Errors and warnings
  INFO = 3,    // Errors, warnings, and info
  DEBUG = 4,   // All except verbose debug
  VERBOSE = 5  // All logs
}

// Import user configuration if available
let USER_LOG_LEVEL: LogLevel | undefined;
try {
  // Dynamic import to avoid circular dependencies
  const userConfig = require('@/config/logging');
  USER_LOG_LEVEL = userConfig.USER_LOG_LEVEL;
} catch (e) {
  // If the config file doesn't exist or can't be imported, use defaults
  USER_LOG_LEVEL = undefined;
}

// Default log level based on environment and user configuration
const DEFAULT_LOG_LEVEL = USER_LOG_LEVEL !== undefined ? USER_LOG_LEVEL : (
  process.env.NODE_ENV === 'production'
    ? LogLevel.ERROR  // Only show errors in production
    : LogLevel.WARN   // Show warnings and errors in development (reduced from INFO to minimize logs)
);

// Current log level - can be changed at runtime
let currentLogLevel = DEFAULT_LOG_LEVEL;

// Check if we should log at a specific level
const shouldLog = (level: LogLevel): boolean => {
  return level <= currentLogLevel;
};

// Set the current log level
export const setLogLevel = (level: LogLevel): void => {
  currentLogLevel = level;
  logger.info(`Log level set to: ${LogLevel[level]}`);
};

// Get the current log level
export const getLogLevel = (): LogLevel => {
  return currentLogLevel;
};

// Logger object with methods for each log level
export const logger = {
  error: (message: string, ...args: any[]): void => {
    if (shouldLog(LogLevel.ERROR)) {
      console.error(message, ...args);
    }
  },

  warn: (message: string, ...args: any[]): void => {
    if (shouldLog(LogLevel.WARN)) {
      console.warn(message, ...args);
    }
  },

  info: (message: string, ...args: any[]): void => {
    if (shouldLog(LogLevel.INFO)) {
      console.info(message, ...args);
    }
  },

  debug: (message: string, ...args: any[]): void => {
    if (shouldLog(LogLevel.DEBUG)) {
      console.debug(message, ...args);
    }
  },

  verbose: (message: string, ...args: any[]): void => {
    if (shouldLog(LogLevel.VERBOSE)) {
      console.log(`[VERBOSE] ${message}`, ...args);
    }
  }
};

// Enable debug mode via localStorage
try {
  const debugMode = localStorage.getItem('debug-mode');
  if (debugMode === 'true') {
    setLogLevel(LogLevel.DEBUG);
  } else if (debugMode === 'verbose') {
    setLogLevel(LogLevel.VERBOSE);
  }
} catch (e) {
  // Ignore localStorage errors
}

// Utility function to conditionally log only when necessary
// This helps reduce excessive logging in development
export const conditionalLog = (
  condition: boolean,
  level: LogLevel,
  message: string,
  ...args: any[]
): void => {
  if (!condition) return;

  switch (level) {
    case LogLevel.ERROR:
      logger.error(message, ...args);
      break;
    case LogLevel.WARN:
      logger.warn(message, ...args);
      break;
    case LogLevel.INFO:
      logger.info(message, ...args);
      break;
    case LogLevel.DEBUG:
      logger.debug(message, ...args);
      break;
    case LogLevel.VERBOSE:
      logger.verbose(message, ...args);
      break;
    default:
      // Don't log for NONE
      break;
  }
};

export default logger;
