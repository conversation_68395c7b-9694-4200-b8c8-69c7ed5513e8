import Database from 'better-sqlite3';
import { format } from 'date-fns';

// This migration adds a display_order column to the rss_feeds table
// and sets the initial order based on the feed name

async function runMigration() {
  console.log('Starting migration to add display_order column to rss_feeds table...');
  
  // Connect to the database
  const dbPath = process.env.DATABASE_URL || 'data.db';
  console.log(`Using database at: ${dbPath}`);
  const db = new Database(dbPath);
  
  try {
    // Check if the column already exists
    const tableInfo = db.prepare('PRAGMA table_info(rss_feeds)').all();
    const columnNames = tableInfo.map((col: any) => col.name);
    console.log(`Found ${columnNames.length} columns in rss_feeds table`);
    
    if (columnNames.includes('display_order')) {
      console.log('display_order column already exists. No migration needed.');
    } else {
      console.log('Adding display_order column to rss_feeds table...');
      
      // Begin transaction
      db.prepare('BEGIN TRANSACTION').run();
      
      // Add the display_order column with a default value of 0
      db.prepare('ALTER TABLE rss_feeds ADD COLUMN display_order INTEGER DEFAULT 0').run();
      
      // Get all users
      const users = db.prepare('SELECT DISTINCT user_id FROM rss_feeds').all();
      
      // For each user, update the display_order based on feed name
      for (const user of users) {
        const userId = user.user_id;
        console.log(`Updating display_order for user ${userId}...`);
        
        // Get all feeds for this user, ordered by name
        const feeds = db.prepare('SELECT id, name FROM rss_feeds WHERE user_id = ? ORDER BY name').all(userId);
        
        // Update the display_order for each feed
        feeds.forEach((feed: any, index: number) => {
          db.prepare('UPDATE rss_feeds SET display_order = ? WHERE id = ?').run(index + 1, feed.id);
        });
        
        console.log(`Updated display_order for ${feeds.length} feeds for user ${userId}`);
      }
      
      // Commit the transaction
      db.prepare('COMMIT').run();
      
      console.log('Migration completed successfully');
    }
    
    // Verify the column was added
    const updatedTableInfo = db.prepare('PRAGMA table_info(rss_feeds)').all();
    const updatedColumnNames = updatedTableInfo.map((col: any) => col.name);
    console.log(`rss_feeds table now has ${updatedColumnNames.length} columns`);
    console.log(`Column names: ${updatedColumnNames.join(', ')}`);
    
    // Sample data to verify
    const sampleData = db.prepare('SELECT user_id, name, display_order FROM rss_feeds LIMIT 5').all();
    console.log('Sample data from rss_feeds table:');
    console.log(sampleData);
    
  } catch (error) {
    // Rollback the transaction on error
    try {
      db.prepare('ROLLBACK').run();
    } catch (rollbackError) {
      console.error('Error rolling back transaction:', rollbackError);
    }
    
    console.error('Error during migration:', error);
    throw error;
  } finally {
    // Close the database connection
    db.close();
  }
}

// Run the migration
runMigration()
  .then(() => {
    console.log('Migration completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
