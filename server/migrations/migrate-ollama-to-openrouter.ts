/**
 * Migration script to migrate data from ollama_raw_data to openrouter_raw_data
 * and remove the ollama_raw_data column
 */

import { Database } from 'better-sqlite3';
import * as path from 'path';

async function main() {
  console.log('Starting migration: Migrating data from ollama_raw_data to openrouter_raw_data...');
  
  // Connect to the database
  const dbPath = path.resolve(process.cwd(), 'data.db');
  console.log(`Opening database at ${dbPath}`);
  const db = new Database(dbPath);
  
  try {
    // Begin transaction
    db.exec('BEGIN TRANSACTION');
    
    // Check if ollama_raw_data column exists
    const tableInfo = db.prepare("PRAGMA table_info(youtube_videos)").all();
    const hasOllamaRawData = tableInfo.some(column => column.name === 'ollama_raw_data');
    const hasOpenRouterRawData = tableInfo.some(column => column.name === 'openrouter_raw_data');
    
    if (!hasOllamaRawData) {
      console.log('ollama_raw_data column does not exist. No migration needed.');
      db.exec('COMMIT');
      return;
    }
    
    if (!hasOpenRouterRawData) {
      console.log('openrouter_raw_data column does not exist. Cannot migrate data.');
      db.exec('ROLLBACK');
      return;
    }
    
    // Get all videos with ollama_raw_data but no openrouter_raw_data
    const videos = db.prepare(`
      SELECT id, ollama_raw_data 
      FROM youtube_videos 
      WHERE ollama_raw_data IS NOT NULL 
      AND (openrouter_raw_data IS NULL OR openrouter_raw_data = '')
    `).all();
    
    console.log(`Found ${videos.length} videos with ollama_raw_data but no openrouter_raw_data`);
    
    // Migrate data from ollama_raw_data to openrouter_raw_data
    let migratedCount = 0;
    for (const video of videos) {
      try {
        const updateStmt = db.prepare(`
          UPDATE youtube_videos
          SET openrouter_raw_data = ?
          WHERE id = ?
        `);
        
        updateStmt.run(video.ollama_raw_data, video.id);
        migratedCount++;
      } catch (error) {
        console.error(`Error migrating data for video ${video.id}:`, error);
      }
    }
    
    console.log(`Successfully migrated data for ${migratedCount} videos`);
    
    // Create a new table without the ollama_raw_data column
    db.exec(`
      CREATE TABLE youtube_videos_new (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        thumbnail TEXT NOT NULL,
        channel_id TEXT NOT NULL,
        channel_title TEXT NOT NULL,
        view_count INTEGER NOT NULL,
        published_at TEXT NOT NULL,
        user_id INTEGER NOT NULL,
        description TEXT,
        duration TEXT,
        transcription TEXT,
        has_transcription INTEGER DEFAULT 0,
        financial_score REAL DEFAULT 0,
        financial_category TEXT DEFAULT NULL,
        financial_amount TEXT DEFAULT NULL,
        financial_timeline TEXT DEFAULT NULL,
        financial_recipients TEXT DEFAULT NULL,
        financial_steps TEXT DEFAULT NULL,
        financial_viral_potential TEXT DEFAULT NULL,
        financial_skepticism TEXT DEFAULT NULL,
        financial_analysis TEXT DEFAULT NULL,
        financial_timestamps TEXT DEFAULT NULL,
        has_financial_analysis INTEGER DEFAULT 0,
        is_simulated_transcription INTEGER DEFAULT 0,
        openrouter_raw_data TEXT,
        openrouter_benefit_amounts TEXT,
        openrouter_expected_arrival_date TEXT,
        openrouter_eligible_people TEXT,
        openrouter_proof_or_source TEXT,
        openrouter_actions_to_claim TEXT,
        openrouter_priority_tag TEXT,
        openrouter_reason_for_priority TEXT,
        openrouter_viral_potential TEXT,
        openrouter_model_used TEXT,
        openrouter_prompt TEXT,
        openrouter_system_prompt TEXT,
        openrouter_prompt_name TEXT,
        ollama_benefit_amounts TEXT,
        ollama_expected_arrival_date TEXT,
        ollama_eligible_people TEXT,
        ollama_proof_or_source TEXT,
        ollama_actions_to_claim TEXT,
        ollama_priority_tag TEXT,
        ollama_reason_for_priority TEXT,
        ollama_viral_potential TEXT,
        ollama_model_used TEXT,
        ollama_prompt TEXT,
        ollama_system_prompt TEXT,
        ollama_prompt_name TEXT
      );
    `);
    
    // Copy data from the old table to the new table
    db.exec(`
      INSERT INTO youtube_videos_new
      SELECT 
        id, title, thumbnail, channel_id, channel_title, view_count, published_at, user_id,
        description, duration, transcription, has_transcription, financial_score, financial_category,
        financial_amount, financial_timeline, financial_recipients, financial_steps, financial_viral_potential,
        financial_skepticism, financial_analysis, financial_timestamps, has_financial_analysis,
        is_simulated_transcription, openrouter_raw_data, openrouter_benefit_amounts,
        openrouter_expected_arrival_date, openrouter_eligible_people, openrouter_proof_or_source,
        openrouter_actions_to_claim, openrouter_priority_tag, openrouter_reason_for_priority,
        openrouter_viral_potential, openrouter_model_used, openrouter_prompt, openrouter_system_prompt,
        openrouter_prompt_name, ollama_benefit_amounts, ollama_expected_arrival_date, ollama_eligible_people,
        ollama_proof_or_source, ollama_actions_to_claim, ollama_priority_tag, ollama_reason_for_priority,
        ollama_viral_potential, ollama_model_used, ollama_prompt, ollama_system_prompt, ollama_prompt_name
      FROM youtube_videos;
    `);
    
    // Drop the old table and rename the new table
    db.exec(`
      DROP TABLE youtube_videos;
      ALTER TABLE youtube_videos_new RENAME TO youtube_videos;
    `);
    
    console.log('Successfully removed ollama_raw_data column');
    
    // Commit the transaction
    db.exec('COMMIT');
    console.log('Migration completed successfully');
  } catch (error) {
    // Rollback the transaction in case of error
    db.exec('ROLLBACK');
    console.error('Migration failed:', error);
    throw error;
  } finally {
    // Close the database connection
    db.close();
  }
}

main().catch(console.error);
