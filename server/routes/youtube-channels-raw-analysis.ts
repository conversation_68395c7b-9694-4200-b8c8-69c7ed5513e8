/**
 * YouTube Channels Raw Analysis Routes
 *
 * This file contains routes for retrieving raw analysis data from YouTube videos.
 */

import express from 'express';
import { storage } from '../storage';
import { createFinancialAnalysisPrompt } from '../services/openrouter-financial-analysis';

const router = express.Router();

// Get raw analysis data for a video
router.get('/videos/:videoId/raw-analysis', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    // Set content type header to ensure JSON response
    res.setHeader('Content-Type', 'application/json');

    const { videoId } = req.params;
    console.log(`\n\n==== TERMINAL LOG: RAW ANALYSIS REQUESTED for video ID: ${videoId} ====`);
    console.log(`TERMINAL LOG: Raw analysis requested for video ID: ${videoId}`);

    // Get the video from the database
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      console.error(`Video not found: ${videoId}`);
      return res.status(404).json({ error: 'Video not found' });
    }

    console.log(`TERMINAL LOG: Found video: ${video.title} (ID: ${videoId})`);
    console.log(`TERMINAL LOG: Video has transcription: ${video.hasTranscription}`);
    console.log(`TERMINAL LOG: Video has OpenRouter analysis: ${Boolean(video.openRouterModelUsed)}`);
    console.log(`TERMINAL LOG: OpenRouter model used: ${video.openRouterModelUsed || 'None'}`);
    console.log(`TERMINAL LOG: Financial score: ${video.financialScore || 'None'}`);
    console.log(`TERMINAL LOG: Priority tag: ${video.openRouterPriorityTag || 'None'}`);
    console.log(`TERMINAL LOG: OpenRouter benefit amounts:`, video.openRouterBenefitAmounts);
    console.log(`TERMINAL LOG: OpenRouter benefit amounts type:`, typeof video.openRouterBenefitAmounts);
    console.log(`TERMINAL LOG: OpenRouter benefit amounts is array:`, Array.isArray(video.openRouterBenefitAmounts));
    if (Array.isArray(video.openRouterBenefitAmounts)) {
      console.log(`TERMINAL LOG: OpenRouter benefit amounts length:`, video.openRouterBenefitAmounts.length);
    }
    console.log(`TERMINAL LOG: OpenRouter raw data available: ${Boolean(video.openRouterRawData)}`);


    // Get user settings to check for custom prompt
    const userSettings = await storage.getSettings(req.user.id);
    let userCustomPrompt = userSettings?.openRouterAnalysisPrompt || userSettings?.ollamaAnalysisPrompt;

    // Get the prompt name from the video or from the selected prompt
    let promptName = video.ollamaPromptName || video.openRouterPromptName || "Default Prompt";

    // If we have a selected prompt ID, try to get the prompt name from the database
    if (userSettings?.selectedPromptId) {
      try {
        const selectedPrompt = await storage.getOpenRouterPrompt(userSettings.selectedPromptId);
        if (selectedPrompt) {
          promptName = selectedPrompt.name;
          console.log(`Using prompt name from selected prompt: ${promptName}`);
        }
      } catch (error) {
        console.error('Error getting selected prompt name:', error);
      }
    }

    // If user has a selected prompt ID, use that prompt instead
    if (userSettings?.selectedPromptId) {
      try {
        // First try to get the OpenRouter prompt
        const selectedPrompt = await storage.getOpenRouterPrompt(userSettings.selectedPromptId);
        if (selectedPrompt) {
          userCustomPrompt = selectedPrompt.promptText;
          promptName = selectedPrompt.name;
          console.log(`Using OpenRouter prompt: ${promptName}`);
        } else {
          // If not found, try to get the Ollama prompt as fallback
          const ollamaPrompt = await storage.getOllamaPrompt(userSettings.selectedPromptId);
          if (ollamaPrompt) {
            userCustomPrompt = ollamaPrompt.promptText;
            promptName = ollamaPrompt.name;
            console.log(`Using Ollama prompt: ${promptName}`);
          }
        }
      } catch (error) {
        console.error('Error getting selected prompt:', error);
      }
    }

    // Use the stored prompt if available, otherwise generate one
    let prompt = video.openRouterPrompt || video.ollamaPrompt || "";

    // If no stored prompt but we have a transcription, generate a prompt
    if (!prompt && video.transcription) {
      console.log('Generating prompt for display purposes...');
      try {
        prompt = createFinancialAnalysisPrompt(
          video.transcription,
          video.title,
          video.description,
          userCustomPrompt,
          null, // publishedAt
          null  // viewCount
        );

        // Log the prompt preview
        console.log(`🤖 OpenRouter API: Prompt preview (first 500 chars): ${prompt.substring(0, 500)}...`);
        console.log(`🤖 OpenRouter API: Transcript placeholder replaced: ${!prompt.includes('[Paste transcript here]')}`);
      } catch (error) {
        console.error('Error creating prompt:', error);
        prompt = 'Error creating prompt';
      }
    }

    // Create a raw analysis object with all the Ollama-specific fields
    // First, get the original raw data if it exists
    let rawAnalysis = {};

    // Get raw data from OpenRouter field
    let rawData = null;

    if (video.openRouterRawData) {
      rawData = video.openRouterRawData;
      console.log('Using stored OpenRouter raw data');
    }

    // If we have raw data from either source, try to parse it
    if (rawData) {
      console.log(`\n==== TERMINAL LOG: PARSING RAW DATA for video ID: ${videoId} ====`);
      console.log(`TERMINAL LOG: Raw data length: ${rawData.length} characters`);

      try {
        // Parse the raw data
        rawAnalysis = JSON.parse(rawData);
        console.log(`TERMINAL LOG: Raw data model: ${rawAnalysis.modelUsed || 'Unknown'}`);
        console.log(`TERMINAL LOG: Raw data parsed successfully.`);
        console.log(`TERMINAL LOG: Model used:`, rawAnalysis.modelUsed);
        console.log(`TERMINAL LOG: Prompt name:`, rawAnalysis.promptName);
        console.log(`TERMINAL LOG: Score:`, rawAnalysis.score);
        console.log(`TERMINAL LOG: Priority tag:`, rawAnalysis.priorityTag);

        // Log extracted info and benefit amounts
        if (rawAnalysis.extractedInfo) {
          console.log(`TERMINAL LOG: Has extractedInfo: true`);
          if (rawAnalysis.extractedInfo.benefitAmounts) {
            console.log(`TERMINAL LOG: Has benefitAmounts: true`);
            console.log(`TERMINAL LOG: Benefit amounts:`, rawAnalysis.extractedInfo.benefitAmounts);
            console.log(`TERMINAL LOG: Benefit amounts type:`, typeof rawAnalysis.extractedInfo.benefitAmounts);
            console.log(`TERMINAL LOG: Benefit amounts is array:`, Array.isArray(rawAnalysis.extractedInfo.benefitAmounts));
            if (Array.isArray(rawAnalysis.extractedInfo.benefitAmounts)) {
              console.log(`TERMINAL LOG: Benefit amounts length:`, rawAnalysis.extractedInfo.benefitAmounts.length);
            }
          } else {
            console.log(`TERMINAL LOG: Has benefitAmounts: false`);
          }
        } else {
          console.log(`TERMINAL LOG: Has extractedInfo: false`);
        }

        // If the model in the raw data doesn't match the current model, update it
        const currentModel = video.openRouterModelUsed || video.ollamaModelUsed;

        if (currentModel && rawAnalysis.modelUsed !== currentModel) {
          console.log(`Updating model in raw data from ${rawAnalysis.modelUsed} to ${currentModel}`);
          rawAnalysis.modelUsed = currentModel;

          // Update the raw data
          const updatedRawData = JSON.stringify(rawAnalysis);
          await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
            financialScore: video.financialScore,
            financialCategory: video.financialCategory,
            financialAmount: video.financialAmount,
            financialTimeline: video.financialTimeline,
            financialRecipients: video.financialRecipients,
            financialSteps: video.financialSteps,
            financialViralPotential: video.financialViralPotential,
            financialSkepticism: video.financialSkepticism,
            financialAnalysis: video.financialAnalysis,
            financialTimestamps: video.financialTimestamps,
            hasFinancialAnalysis: video.hasFinancialAnalysis,
            openRouterRawData: updatedRawData // Store only in OpenRouter field
          });
        }

        // Add the prompt name if it's not already there
        if (!rawAnalysis.promptName) {
          rawAnalysis.promptName = promptName;
        }

        // Add the prompt and system prompt
        rawAnalysis.prompt = prompt;
        rawAnalysis.systemPrompt = video.ollamaSystemPrompt || video.openRouterSystemPrompt ||
          'You are a financial benefit analyzer specialized in identifying financial benefits mentioned in YouTube video transcripts.\nYour task is to extract specific financial benefit information and classify the content based on certainty and value.\nFocus only on financial benefits that viewers might receive (like stimulus checks, tax credits, government payments, etc.).\nIgnore general financial advice or market analysis that doesn\'t involve direct benefits to individuals.';

        // Return the raw data directly
        return res.json(rawAnalysis);
      } catch (parseError) {
        console.error(`Error parsing stored raw data:`, parseError);
        console.error('Raw data:', rawData);
        // If parsing fails, we'll fall back to reconstructing the data
      }
    }

    // If we don't have stored raw data or parsing failed, reconstruct it
    if (Object.keys(rawAnalysis).length === 0) {
      console.log(`\n==== TERMINAL LOG: RECONSTRUCTING RAW ANALYSIS DATA for video ID: ${videoId} ====`);
      console.log('TERMINAL LOG: Reconstructing raw OpenRouter analysis data');
      console.log('TERMINAL LOG: OpenRouter benefit amounts:', video.openRouterBenefitAmounts);
      console.log('TERMINAL LOG: Ollama benefit amounts:', video.ollamaBenefitAmounts);

      // Parse benefit amounts if needed
      console.log(`\n==== TERMINAL LOG: PARSING BENEFIT AMOUNTS for video ID: ${videoId} ====`);
      let benefitAmounts = [];

      // First try to get benefit amounts from OpenRouter (preferred)
      if (video.openRouterBenefitAmounts) {
        console.log('TERMINAL LOG: Checking OpenRouter benefit amounts:', video.openRouterBenefitAmounts);
        console.log('TERMINAL LOG: OpenRouter benefit amounts type:', typeof video.openRouterBenefitAmounts);
        console.log('TERMINAL LOG: Is array:', Array.isArray(video.openRouterBenefitAmounts));

        if (Array.isArray(video.openRouterBenefitAmounts)) {
          benefitAmounts = video.openRouterBenefitAmounts;
          console.log('TERMINAL LOG: Using already parsed OpenRouter benefit amounts:', benefitAmounts);
          console.log('TERMINAL LOG: Benefit amounts length:', benefitAmounts.length);
        } else {
          try {
            console.log('TERMINAL LOG: Attempting to parse OpenRouter benefit amounts from string');
            benefitAmounts = JSON.parse(video.openRouterBenefitAmounts);
            console.log('TERMINAL LOG: Parsed OpenRouter benefit amounts from string:', benefitAmounts);
            console.log('TERMINAL LOG: Parsed benefit amounts type:', typeof benefitAmounts);
            console.log('TERMINAL LOG: Is parsed array:', Array.isArray(benefitAmounts));
            console.log('TERMINAL LOG: Parsed benefit amounts length:', Array.isArray(benefitAmounts) ? benefitAmounts.length : 'not an array');

            // Update both Ollama and OpenRouter benefit amounts for consistency
            if (benefitAmounts.length > 0) {
              const benefitAmountsJson = JSON.stringify(benefitAmounts);
              await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
                financialScore: video.financialScore,
                financialCategory: video.financialCategory,
                financialAmount: video.financialAmount,
                financialTimeline: video.financialTimeline,
                financialRecipients: video.financialRecipients,
                financialSteps: video.financialSteps,
                financialViralPotential: video.financialViralPotential,
                financialSkepticism: video.financialSkepticism,
                financialAnalysis: video.financialAnalysis,
                financialTimestamps: video.financialTimestamps,
                hasFinancialAnalysis: video.hasFinancialAnalysis,
                ollamaBenefitAmounts: benefitAmounts,
                openRouterBenefitAmounts: benefitAmounts
              });
              console.log('Updated both Ollama and OpenRouter benefit amounts for consistency');
            }
          } catch (parseError) {
            console.error('Error parsing OpenRouter benefit amounts:', parseError);
            console.error('Raw OpenRouter value:', video.openRouterBenefitAmounts);
          }
        }
      }

      // If no OpenRouter benefit amounts, try Ollama benefit amounts
      if (benefitAmounts.length === 0 && video.ollamaBenefitAmounts) {
        console.log('No OpenRouter benefit amounts, checking Ollama benefit amounts:', video.ollamaBenefitAmounts);
        if (Array.isArray(video.ollamaBenefitAmounts)) {
          benefitAmounts = video.ollamaBenefitAmounts;
          console.log('Using already parsed Ollama benefit amounts:', benefitAmounts);

          // Update OpenRouter benefit amounts for consistency
          if (benefitAmounts.length > 0) {
            const benefitAmountsJson = JSON.stringify(benefitAmounts);
            await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
              financialScore: video.financialScore,
              financialCategory: video.financialCategory,
              financialAmount: video.financialAmount,
              financialTimeline: video.financialTimeline,
              financialRecipients: video.financialRecipients,
              financialSteps: video.financialSteps,
              financialViralPotential: video.financialViralPotential,
              financialSkepticism: video.financialSkepticism,
              financialAnalysis: video.financialAnalysis,
              financialTimestamps: video.financialTimestamps,
              hasFinancialAnalysis: video.hasFinancialAnalysis,
              openRouterBenefitAmounts: benefitAmounts
            });
            console.log('Updated OpenRouter benefit amounts for consistency');
          }
        } else {
          try {
            benefitAmounts = JSON.parse(video.ollamaBenefitAmounts);
            console.log('Parsed Ollama benefit amounts from string:', benefitAmounts);

            // Update OpenRouter benefit amounts for consistency
            if (benefitAmounts.length > 0) {
              const benefitAmountsJson = JSON.stringify(benefitAmounts);
              await storage.updateYoutubeVideoFinancialAnalysis(videoId, {
                financialScore: video.financialScore,
                financialCategory: video.financialCategory,
                financialAmount: video.financialAmount,
                financialTimeline: video.financialTimeline,
                financialRecipients: video.financialRecipients,
                financialSteps: video.financialSteps,
                financialViralPotential: video.financialViralPotential,
                financialSkepticism: video.financialSkepticism,
                financialAnalysis: video.financialAnalysis,
                financialTimestamps: video.financialTimestamps,
                hasFinancialAnalysis: video.hasFinancialAnalysis,
                openRouterBenefitAmounts: benefitAmounts
              });
              console.log('Updated OpenRouter benefit amounts for consistency');
            }
          } catch (parseError) {
            console.error('Error parsing Ollama benefit amounts:', parseError);
            console.error('Raw Ollama value:', video.ollamaBenefitAmounts);
          }
        }
      }

      // Get the correct model name from settings if available
      let modelName = video.openRouterModelUsed || video.ollamaModelUsed || 'Unknown';
      try {
        // Get the user's settings to check the current model
        const settings = await storage.getSettings(req.user.id);
        if (settings && settings.openRouterModel) {
          // Always use the OpenRouter model from settings
          console.log(`Using OpenRouter model from settings: ${settings.openRouterModel}`);
          modelName = settings.openRouterModel;
        }
      } catch (settingsError) {
        console.error('Error getting user settings for model name:', settingsError);
      }

      // We've already checked for OpenRouter raw data above, so if we're here, it either doesn't exist or failed to parse

      // Check if we have any actual benefit data
      const hasBenefitData = benefitAmounts && benefitAmounts.length > 0;

      // Also check other fields that might indicate benefit data
      const hasOtherBenefitData = (
        (video.openRouterExpectedArrivalDate && video.openRouterExpectedArrivalDate.trim() !== '') ||
        (video.ollamaExpectedArrivalDate && video.ollamaExpectedArrivalDate.trim() !== '') ||
        (video.openRouterEligiblePeople && video.openRouterEligiblePeople.trim() !== '') ||
        (video.ollamaEligiblePeople && video.ollamaEligiblePeople.trim() !== '') ||
        (video.openRouterProofOrSource && video.openRouterProofOrSource.trim() !== '') ||
        (video.ollamaProofOrSource && video.ollamaProofOrSource.trim() !== '') ||
        (video.openRouterActionsToClaim && video.openRouterActionsToClaim.trim() !== '') ||
        (video.ollamaActionsToClaim && video.ollamaActionsToClaim.trim() !== '')
      );

      // Start with a basic structure
      rawAnalysis = {
        hasBenefit: hasBenefitData || hasOtherBenefitData,
        extractedInfo: {},
        promptName: promptName // Include the prompt name in the raw data
      };

      console.log(`Using prompt name in raw analysis: ${promptName}`);

      // Only add score fields if we have actual benefit data
      if ((hasBenefitData || hasOtherBenefitData) && video.financialScore) {
        rawAnalysis.certaintyScore = video.financialScore;
        rawAnalysis.score = video.financialScore;
      }

      if ((hasBenefitData || hasOtherBenefitData) && video.financialCategory && video.financialCategory.trim() !== '') {
        rawAnalysis.benefitType = video.financialCategory;
      }

      if ((hasBenefitData || hasOtherBenefitData) && video.financialAnalysis && video.financialAnalysis.trim() !== '') {
        rawAnalysis.benefitDescription = video.financialAnalysis;
      }

      // Always include benefit amounts array, even if empty
      rawAnalysis.extractedInfo.benefitAmounts = benefitAmounts && benefitAmounts.length > 0 ? benefitAmounts : [];

      // Log the benefit amounts for debugging
      console.log('Final benefit amounts in raw analysis:', rawAnalysis.extractedInfo.benefitAmounts);

      // Only add other fields if they have actual data - prefer OpenRouter fields over Ollama fields
      if (video.openRouterExpectedArrivalDate && video.openRouterExpectedArrivalDate.trim() !== '') {
        rawAnalysis.extractedInfo.expectedArrivalDate = video.openRouterExpectedArrivalDate;
      } else if (video.ollamaExpectedArrivalDate && video.ollamaExpectedArrivalDate.trim() !== '') {
        rawAnalysis.extractedInfo.expectedArrivalDate = video.ollamaExpectedArrivalDate;
      }

      if (video.openRouterEligiblePeople && video.openRouterEligiblePeople.trim() !== '') {
        rawAnalysis.extractedInfo.eligiblePeople = video.openRouterEligiblePeople;
      } else if (video.ollamaEligiblePeople && video.ollamaEligiblePeople.trim() !== '') {
        rawAnalysis.extractedInfo.eligiblePeople = video.ollamaEligiblePeople;
      }

      if (video.openRouterProofOrSource && video.openRouterProofOrSource.trim() !== '') {
        rawAnalysis.extractedInfo.proofOrSource = video.openRouterProofOrSource;
      } else if (video.ollamaProofOrSource && video.ollamaProofOrSource.trim() !== '') {
        rawAnalysis.extractedInfo.proofOrSource = video.ollamaProofOrSource;
      }

      if (video.openRouterActionsToClaim && video.openRouterActionsToClaim.trim() !== '') {
        rawAnalysis.extractedInfo.actionsToClaim = video.openRouterActionsToClaim;
      } else if (video.ollamaActionsToClaim && video.ollamaActionsToClaim.trim() !== '') {
        rawAnalysis.extractedInfo.actionsToClaim = video.ollamaActionsToClaim;
      }

      if ((hasBenefitData || hasOtherBenefitData) && video.openRouterPriorityTag && video.openRouterPriorityTag.trim() !== '') {
        rawAnalysis.priorityTag = video.openRouterPriorityTag;
      } else if ((hasBenefitData || hasOtherBenefitData) && video.ollamaPriorityTag && video.ollamaPriorityTag.trim() !== '') {
        rawAnalysis.priorityTag = video.ollamaPriorityTag;
      }

      if ((hasBenefitData || hasOtherBenefitData) && video.openRouterReasonForPriority && video.openRouterReasonForPriority.trim() !== '') {
        rawAnalysis.reasoning = video.openRouterReasonForPriority;
      } else if ((hasBenefitData || hasOtherBenefitData) && video.ollamaReasonForPriority && video.ollamaReasonForPriority.trim() !== '') {
        rawAnalysis.reasoning = video.ollamaReasonForPriority;
      }

      // Only add model if it's actually known and we have benefit data
      if ((hasBenefitData || hasOtherBenefitData) && modelName && modelName !== 'Unknown') {
        rawAnalysis.modelUsed = modelName;
      }
    }

    // Always include the prompt name, regardless of whether there's a benefit
    rawAnalysis.promptName = promptName;
    console.log(`Setting final prompt name in raw analysis: ${promptName}`);

    // Only add prompt and systemPrompt if they're actually available
    if (prompt) {
      rawAnalysis.prompt = prompt;
    }

    if (video.ollamaSystemPrompt) {
      rawAnalysis.systemPrompt = video.ollamaSystemPrompt;
    }

    // Add a note to indicate if this is reconstructed data
    if (!video.openRouterRawData && !video.ollamaRawData) {
      rawAnalysis._note = 'This data was reconstructed from stored fields and may not match the original raw output.';
      console.log('Adding note about reconstructed data - no raw data available');
    } else {
      console.log(`Using raw data from ${video.openRouterRawData ? 'OpenRouter' : 'Ollama'}`);
    }

    // Final check for benefit amounts
    console.log(`\n==== TERMINAL LOG: FINAL RAW ANALYSIS CHECK for video ID: ${videoId} ====`);
    if (!rawAnalysis.extractedInfo.benefitAmounts || !Array.isArray(rawAnalysis.extractedInfo.benefitAmounts)) {
      console.log('TERMINAL LOG: Setting empty benefit amounts array in final output');
      rawAnalysis.extractedInfo.benefitAmounts = [];
    } else {
      console.log('TERMINAL LOG: Final benefit amounts array:', rawAnalysis.extractedInfo.benefitAmounts);
      console.log('TERMINAL LOG: Final benefit amounts length:', rawAnalysis.extractedInfo.benefitAmounts.length);
    }

    // Log the final raw analysis object
    console.log('TERMINAL LOG: Final raw analysis object:');
    console.log('TERMINAL LOG: Model used:', rawAnalysis.modelUsed);
    console.log('TERMINAL LOG: Prompt name:', rawAnalysis.promptName);
    console.log('TERMINAL LOG: Score:', rawAnalysis.score);
    console.log('TERMINAL LOG: Priority tag:', rawAnalysis.priorityTag);
    console.log('TERMINAL LOG: Has extractedInfo:', Boolean(rawAnalysis.extractedInfo));

    if (rawAnalysis.extractedInfo) {
      console.log('TERMINAL LOG: Has benefitAmounts:', Boolean(rawAnalysis.extractedInfo.benefitAmounts));
      if (rawAnalysis.extractedInfo.benefitAmounts) {
        console.log('TERMINAL LOG: Benefit amounts is array:', Array.isArray(rawAnalysis.extractedInfo.benefitAmounts));
        console.log('TERMINAL LOG: Benefit amounts:', rawAnalysis.extractedInfo.benefitAmounts);
        if (Array.isArray(rawAnalysis.extractedInfo.benefitAmounts)) {
          console.log('TERMINAL LOG: Benefit amounts length:', rawAnalysis.extractedInfo.benefitAmounts.length);
        }
      }
    }

    res.json(rawAnalysis);
  } catch (error) {
    console.error('Error getting raw analysis data:', error);
    console.error('Error details:', error instanceof Error ? error.message : 'Unknown error');
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace available');

    // Ensure we're sending a JSON response even in error cases
    res.setHeader('Content-Type', 'application/json');
    res.status(500).json({
      error: 'Failed to get raw analysis data',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
