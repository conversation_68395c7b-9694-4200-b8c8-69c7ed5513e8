import React from 'react';

interface RankingDataPoint {
  timestamp: number;
  ranking: number;
  channelName: string;
}

interface ChannelRankingGraphProps {
  rankingHistory: RankingDataPoint[];
  channelName: string;
  urlName: string;
  height?: number;
  width?: number;
  maxHours?: number;
}

export function ChannelRankingGraph({
  rankingHistory,
  channelName,
  urlName,
  height = 60,
  width = 200,
  maxHours = 12
}: ChannelRankingGraphProps) {
  const now = Date.now();

  // Create hourly timestamps for the last 12 hours
  const hourlyTimestamps: number[] = [];
  for (let i = 0; i < maxHours; i++) {
    const hourAgo = new Date(now - ((maxHours - i) * 60 * 60 * 1000));
    hourAgo.setMinutes(0, 0, 0); // Set to the beginning of the hour
    hourlyTimestamps.push(hourAgo.getTime());
  }

  // Filter and organize ranking data for the last 12 hours
  const hourlyRankings: { timestamp: number; ranking: number | null }[] = [];

  hourlyTimestamps.forEach((timestamp, index) => {
    const nextHourTimestamp = index < maxHours - 1
      ? hourlyTimestamps[index + 1]
      : now;

    // Find rankings within this hour
    const rankingsInHour = rankingHistory.filter(entry =>
      entry.channelName === channelName &&
      entry.timestamp >= timestamp &&
      entry.timestamp < nextHourTimestamp
    );

    if (rankingsInHour.length > 0) {
      // Use the latest ranking in this hour
      const latestRanking = rankingsInHour[rankingsInHour.length - 1];
      hourlyRankings.push({
        timestamp: timestamp,
        ranking: latestRanking.ranking
      });
    } else {
      // Find the closest ranking before this hour
      const rankingsBefore = rankingHistory.filter(entry =>
        entry.channelName === channelName && entry.timestamp < timestamp
      );

      if (rankingsBefore.length > 0) {
        const closestBefore = rankingsBefore[rankingsBefore.length - 1];
        hourlyRankings.push({
          timestamp: timestamp,
          ranking: closestBefore.ranking
        });
      } else {
        hourlyRankings.push({
          timestamp: timestamp,
          ranking: null
        });
      }
    }
  });

  // Filter out null rankings and prepare data for visualization
  const validRankings = hourlyRankings.filter(entry => entry.ranking !== null) as { timestamp: number; ranking: number }[];

  if (validRankings.length === 0) {
    return (
      <div className="flex items-center justify-center bg-muted/20 rounded" style={{ height, width }}>
        <span className="text-xs text-muted-foreground">No ranking data</span>
      </div>
    );
  }

  // Calculate chart dimensions and scaling
  const chartPadding = 10;
  const chartWidth = width - (chartPadding * 2);
  const chartHeight = height - (chartPadding * 2);

  // Find min and max rankings for scaling (invert Y-axis since rank 1 is better than rank 10)
  const rankings = validRankings.map(r => r.ranking);
  const minRanking = Math.min(...rankings);
  const maxRanking = Math.max(...rankings);
  const rankingRange = maxRanking - minRanking || 1;

  // Generate SVG path for the line
  const pathPoints = validRankings.map((entry, index) => {
    const x = chartPadding + (index / (validRankings.length - 1 || 1)) * chartWidth;
    // Invert Y-axis: better rankings (lower numbers) should be higher on the chart
    const y = chartPadding + ((maxRanking - entry.ranking) / rankingRange) * chartHeight;
    return `${x},${y}`;
  });

  const pathData = `M ${pathPoints.join(' L ')}`;

  // Determine line color based on trend
  const firstRanking = validRankings[0]?.ranking;
  const lastRanking = validRankings[validRankings.length - 1]?.ranking;
  const isImproving = lastRanking < firstRanking; // Lower ranking number is better
  const lineColor = isImproving ? '#10b981' : lastRanking > firstRanking ? '#ef4444' : '#6b7280';

  return (
    <div className="relative bg-muted/20 rounded border" style={{ height, width }}>
      {/* Chart Title */}
      <div className="absolute top-1 left-2 text-[10px] font-medium truncate max-w-[80%]">
        <span className={channelName === '@HowToGuys' ? 'text-green-400' : 'text-purple-300'}>
          {channelName}
        </span>
        <span className="text-muted-foreground"> - {urlName}</span>
      </div>

      {/* Y-axis labels */}
      <div className="absolute top-2 right-1 text-[8px] text-muted-foreground">#{minRanking}</div>
      <div className="absolute bottom-2 right-1 text-[8px] text-muted-foreground">#{maxRanking}</div>

      {/* SVG Chart */}
      <svg
        width={width}
        height={height}
        className="absolute inset-0"
        style={{ overflow: 'visible' }}
      >
        {/* Grid lines */}
        <defs>
          <pattern id="grid" width="20" height="10" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 10" fill="none" stroke="#374151" strokeWidth="0.5" opacity="0.3"/>
          </pattern>
        </defs>
        <rect width={width} height={height} fill="url(#grid)" />

        {/* Ranking line */}
        {validRankings.length > 1 && (
          <path
            d={pathData}
            fill="none"
            stroke={lineColor}
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        )}

        {/* Data points */}
        {validRankings.map((entry, index) => {
          const x = chartPadding + (index / (validRankings.length - 1 || 1)) * chartWidth;
          const y = chartPadding + ((maxRanking - entry.ranking) / rankingRange) * chartHeight;

          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r="2"
              fill={lineColor}
              stroke="white"
              strokeWidth="1"
            >
              <title>
                {new Date(entry.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}:
                Rank #{entry.ranking}
              </title>
            </circle>
          );
        })}
      </svg>

      {/* Current ranking indicator */}
      <div className="absolute bottom-1 left-2 text-[11px] font-bold bg-black/50 px-1 rounded" style={{ color: lineColor }}>
        #{lastRanking}
        {isImproving && <span className="ml-1 text-green-400">↗</span>}
        {lastRanking > firstRanking && <span className="ml-1 text-red-400">↘</span>}
        {lastRanking === firstRanking && <span className="ml-1 text-gray-400">→</span>}
      </div>

      {/* Time indicators */}
      <div className="absolute bottom-1 left-1/4 text-[8px] text-muted-foreground">-9h</div>
      <div className="absolute bottom-1 left-1/2 text-[8px] text-muted-foreground">-6h</div>
      <div className="absolute bottom-1 left-3/4 text-[8px] text-muted-foreground">-3h</div>
      <div className="absolute bottom-1 right-8 text-[8px] text-muted-foreground">now</div>
    </div>
  );
}
