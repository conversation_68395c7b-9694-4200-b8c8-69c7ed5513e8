import React from 'react';
import { marketShareCollector } from '@/services/market-share-collector';

// Define a type for market share history data
export interface MarketShareEntry {
  howToGuysViews: number;
  competitorViews: number;
  timestamp: number;
  marketShare: number;
  dayOfWeek?: string;
  date?: string;
}

interface MarketShareChartProps {
  channelName: string;
  currentHowToGuysViews: number;
  currentCompetitorViews: number;
  currentMarketShare: number;
  marketShareHistory?: MarketShareEntry[];
  height?: number;
  width?: number;
  maxDays?: number;
  showTooltip?: boolean;
  showDaySelector?: boolean;
}

// Use React.memo to prevent unnecessary re-renders
export const MarketShareChart = React.memo(function MarketShareChart({
  channelName,
  currentHowToGuysViews,
  currentCompetitorViews,
  currentMarketShare,
  marketShareHistory = [],
  height = 32,  // Increased from 20 to 32 for better visibility
  width = 80,
  maxDays = 7,
  showTooltip = true,
  showDaySelector = false
}: MarketShareChartProps) {
  const now = Date.now();
  const [showDetailedChart, setShowDetailedChart] = React.useState(false);
  const [selectedChartData, setSelectedChartData] = React.useState<MarketShareEntry | null>(null);
  const [selectedDay, setSelectedDay] = React.useState<number>(0); // 0 = today, -1 = yesterday, etc.
  const [refreshTrigger, setRefreshTrigger] = React.useState<number>(0); // Force re-render when view counts are updated

  // Generate last 7 days with proper weekday names
  const last7Days = React.useMemo(() => {
    const days = [];
    for (let i = 0; i >= -6; i--) {
      const dayTimestamp = now + (i * 24 * 60 * 60 * 1000);
      const dayDate = new Date(dayTimestamp);
      const dayName = dayDate.toLocaleDateString('en-US', { weekday: 'long' });
      const shortName = dayDate.toLocaleDateString('en-US', { weekday: 'short' });
      const dateStr = dayDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

      days.push({
        offset: i,
        dayName,
        shortName,
        dateStr,
        timestamp: dayTimestamp,
        isToday: i === 0,
        label: i === 0 ? 'Today' : i === -1 ? 'Yesterday' : dayName
      });
    }
    return days;
  }, [now]);

  // Function to update current data in the collector service
  const updateMarketShareData = React.useCallback(() => {
    // Only update if we have valid data
    if (currentHowToGuysViews > 0 || currentCompetitorViews > 0) {
      // Update the collector with current data
      marketShareCollector.updateCurrentData(
        currentHowToGuysViews,
        currentCompetitorViews,
        currentMarketShare
      );

      // Also manually collect data point immediately
      marketShareCollector.collectCurrentData();

      console.log('Market share data updated and collected:', {
        howToGuysViews: currentHowToGuysViews,
        competitorViews: currentCompetitorViews,
        marketShare: currentMarketShare,
        timestamp: new Date().toLocaleTimeString()
      });
    }
  }, [currentHowToGuysViews, currentCompetitorViews, currentMarketShare]);

  // Update data when component mounts or data changes, and start collector
  React.useEffect(() => {
    if (currentHowToGuysViews > 0 || currentCompetitorViews > 0) {
      updateMarketShareData();
    }

    // Start the data collector if not already running
    if (!marketShareCollector.isRunning()) {
      marketShareCollector.startCollection();
    }
  }, [updateMarketShareData]);

  // Listen for view count updates from refresh metadata
  React.useEffect(() => {
    const handleViewCountsUpdated = (event: CustomEvent) => {
      console.log('MarketShareChart: Received view counts updated event', event.detail);
      // Force re-render by updating the refresh trigger
      setRefreshTrigger(prev => prev + 1);
    };

    // Add event listener
    window.addEventListener('viewCountsUpdated', handleViewCountsUpdated as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('viewCountsUpdated', handleViewCountsUpdated as EventListener);
    };
  }, []);



  // Generate chart data using real 12-hour data from localStorage
  const chartData = React.useMemo(() => {
    // Get hourly data from localStorage (same storage as market share collector)
    let hourlyHistoryData: MarketShareEntry[] = [];

    try {
      const storageKey = 'market_share_hourly_history';
      const savedData = localStorage.getItem(storageKey);
      if (savedData) {
        hourlyHistoryData = JSON.parse(savedData);
      }
    } catch (error) {
      console.error('Error loading hourly market share data for small chart:', error);
    }

    // Filter data for the last 12 hours
    const twelveHoursAgo = Date.now() - (12 * 60 * 60 * 1000);
    const recentData = hourlyHistoryData.filter(entry => entry.timestamp > twelveHoursAgo);

    console.log('Small chart using real hourly data:', {
      totalHourlyData: hourlyHistoryData.length,
      recentData: recentData.length,
      currentData: { currentHowToGuysViews, currentCompetitorViews, currentMarketShare }
    });

    // Generate 12 periods of 1-hour blocks for the last 12 hours
    const periods = Array.from({ length: 12 }, (_, periodIndex) => {
      const hoursBack = 11 - periodIndex; // Start from 11 hours ago to now
      const periodStart = Date.now() - (hoursBack * 60 * 60 * 1000);
      const periodEnd = periodStart + (60 * 60 * 1000); // 1 hour period

      // Find data points within this period
      const periodData = recentData.filter(entry =>
        entry.timestamp >= periodStart && entry.timestamp < periodEnd
      );

      // Use the latest data point in this period, or current data for the most recent period
      let howToGuysViews, competitorViews, marketShare;

      if (periodData.length > 0) {
        // Use the latest data point in this period
        const latestData = periodData[periodData.length - 1];
        howToGuysViews = latestData.howToGuysViews;
        competitorViews = latestData.competitorViews;
        marketShare = latestData.marketShare;
      } else if (periodIndex === 11 && (currentHowToGuysViews > 0 || currentCompetitorViews > 0)) {
        // For the most recent period (current hour), use current data if no historical data exists
        howToGuysViews = currentHowToGuysViews;
        competitorViews = currentCompetitorViews;
        marketShare = currentMarketShare;
      } else {
        // No real data available for this period - use minimal values to maintain chart structure
        howToGuysViews = 1;
        competitorViews = 1;
        marketShare = 50; // Neutral market share
      }

      const hour = new Date(periodStart).getHours();

      return {
        howToGuysViews,
        competitorViews,
        marketShare,
        timestamp: periodStart,
        hour,
        dayOfWeek: new Date(periodStart).toLocaleDateString('en-US', { weekday: 'short' }),
        date: new Date(periodStart).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      };
    });

    return periods;
  }, [currentHowToGuysViews, currentCompetitorViews, currentMarketShare, refreshTrigger]);

  // Calculate max values for scaling with better visibility
  const maxHowToGuysViews = Math.max(...chartData.map(d => d.howToGuysViews), currentHowToGuysViews, 100);
  const maxCompetitorViews = Math.max(...chartData.map(d => d.competitorViews), currentCompetitorViews, 100);
  const maxTotalViews = Math.max(maxHowToGuysViews, maxCompetitorViews, 100); // Higher minimum scale for better visibility

  // Debug logging for chart scaling issues
  React.useEffect(() => {
    if (chartData.length > 0) {
      console.log('MarketShareChart Debug:', {
        channelName,
        chartDataLength: chartData.length,
        maxHowToGuysViews,
        maxCompetitorViews,
        maxTotalViews,
        currentHowToGuysViews,
        currentCompetitorViews,
        sampleData: chartData.slice(-2), // Last 2 data points
        height,
        width
      });
    }
  }, [chartData, maxTotalViews, channelName, currentHowToGuysViews, currentCompetitorViews, height, width]);

  // Get data for selected day
  const selectedDayData = React.useMemo(() => {
    const targetDay = last7Days.find(day => day.offset === selectedDay);
    if (!targetDay) return null;

    // Find data for the selected day from chartData
    const dayData = chartData.find(entry => {
      const entryDate = new Date(entry.timestamp);
      const targetDate = new Date(targetDay.timestamp);
      return entryDate.toDateString() === targetDate.toDateString();
    });

    return dayData || {
      howToGuysViews: currentHowToGuysViews,
      competitorViews: currentCompetitorViews,
      timestamp: targetDay.timestamp,
      marketShare: currentMarketShare,
      dayOfWeek: targetDay.shortName,
      date: targetDay.dateStr
    };
  }, [selectedDay, last7Days, chartData, currentHowToGuysViews, currentCompetitorViews, currentMarketShare]);

  return (
    <div className="space-y-2">
      {/* Day Selector - Only show in detailed analytics */}
      {showDaySelector && (
        <div className="flex flex-wrap gap-1 justify-center mb-2">
          {last7Days.map((day) => (
            <button
              key={day.offset}
              onClick={() => {
                console.log('Day selected:', day.offset, day.label);
                setSelectedDay(day.offset);
              }}
              className={`px-3 py-1.5 text-xs rounded-md transition-all duration-200 border ${
                selectedDay === day.offset
                  ? 'bg-green-500 text-white font-bold border-green-400 shadow-md'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600 border-gray-600 hover:border-gray-500'
              }`}
            >
              {day.label}
            </button>
          ))}
        </div>
      )}

      {/* Selected Day Data Display - Only show in detailed analytics */}
      {showDaySelector && selectedDayData && (
        <div className="bg-gray-800 rounded p-2 text-xs">
          <div className="flex items-center justify-between mb-1">
            <span className="font-bold text-green-400">
              {last7Days.find(d => d.offset === selectedDay)?.label} - {selectedDayData.date}
            </span>
            <span className="text-cyan-400 font-bold">
              {selectedDayData.marketShare.toFixed(1)}%
            </span>
          </div>
          <div className="flex justify-between text-[10px]">
            <span className="text-green-400">
              @HowToGuys: {selectedDayData.howToGuysViews} views
            </span>
            <span className="text-red-400">
              Competitors: {selectedDayData.competitorViews} views
            </span>
          </div>
          <div className="text-[10px] text-gray-400 mt-1">
            Total: {selectedDayData.howToGuysViews + selectedDayData.competitorViews} views
          </div>
        </div>
      )}

      <div
        className="relative bg-gray-800 rounded-sm border border-gray-600 cursor-pointer hover:border-gray-500 transition-colors"
        style={{
          height: `${height}px`,
          width: `${width}px`,
          overflow: 'hidden',
          padding: '2px'
        }}
        onClick={() => {
          setSelectedChartData(chartData[chartData.length - 1]); // Use latest data
          setShowDetailedChart(true);
        }}
        title="Click to view detailed 24-hour market share analysis"
      >
        {/* SVG for line chart */}
        <svg
          width={width - 4}
          height={height - 4}
          className="absolute top-0.5 left-0.5"
        >
              {/* Debug: Chart area background */}
              <rect
                x="0"
                y="0"
                width={width - 4}
                height={height - 4}
                fill="rgba(55, 65, 81, 0.1)"
                stroke="rgba(75, 85, 99, 0.3)"
                strokeWidth="0.5"
              />
              {/* @HowToGuys line - GREEN (consistent with big chart) */}
              <polyline
                fill="none"
                stroke="#22c55e"
                strokeWidth="2"
                points={chartData.map((entry, index) => {
                  const x = (index / (chartData.length - 1)) * (width - 4);
                  // Improved padding and scaling for better visibility
                  const chartHeight = height - 4;
                  const padding = 4; // Increased padding from 2px to 4px
                  const availableHeight = chartHeight - (padding * 2);
                  const y = padding + (availableHeight - ((entry.howToGuysViews / maxTotalViews) * availableHeight));
                  return `${x},${y}`;
                }).join(' ')}
              />

              {/* Competitors line - RED (consistent with big chart) */}
              <polyline
                fill="none"
                stroke="#ef4444"
                strokeWidth="2"
                points={chartData.map((entry, index) => {
                  const x = (index / (chartData.length - 1)) * (width - 4);
                  // Improved padding and scaling for better visibility
                  const chartHeight = height - 4;
                  const padding = 4; // Increased padding from 2px to 4px
                  const availableHeight = chartHeight - (padding * 2);
                  const y = padding + (availableHeight - ((entry.competitorViews / maxTotalViews) * availableHeight));
                  return `${x},${y}`;
                }).join(' ')}
              />

              {/* Current point indicators - consistent colors */}
              {/* @HowToGuys current point - GREEN */}
              <circle
                cx={(width - 4)}
                cy={(() => {
                  const chartHeight = height - 4;
                  const padding = 4; // Match the improved padding
                  const availableHeight = chartHeight - (padding * 2);
                  return padding + (availableHeight - ((currentHowToGuysViews / maxTotalViews) * availableHeight));
                })()}
                r="2"
                fill="#22c55e"
              />
              {/* Competitors current point - RED */}
              <circle
                cx={(width - 4)}
                cy={(() => {
                  const chartHeight = height - 4;
                  const padding = 4; // Match the improved padding
                  const availableHeight = chartHeight - (padding * 2);
                  return padding + (availableHeight - ((currentCompetitorViews / maxTotalViews) * availableHeight));
                })()}
                r="2"
                fill="#ef4444"
              />
            </svg>
      </div>

      {/* Large Detailed Chart Popup */}
      {showDetailedChart && selectedChartData && (
        <DetailedHourlyChart
          channelName={channelName}
          selectedDate={selectedChartData.date || new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
          selectedDayOfWeek={selectedChartData.dayOfWeek || new Date().toLocaleDateString('en-US', { weekday: 'long' })}
          currentHowToGuysViews={currentHowToGuysViews}
          currentCompetitorViews={currentCompetitorViews}
          currentMarketShare={currentMarketShare}
          onClose={() => setShowDetailedChart(false)}
        />
      )}

    </div>
  );
});

// Detailed 24-Hour Chart Component
interface DetailedHourlyChartProps {
  channelName: string;
  selectedDate: string;
  selectedDayOfWeek: string;
  currentHowToGuysViews: number;
  currentCompetitorViews: number;
  currentMarketShare: number;
  onClose: () => void;
}

const DetailedHourlyChart = React.memo(function DetailedHourlyChart({
  channelName,
  selectedDate,
  selectedDayOfWeek,
  currentHowToGuysViews,
  currentCompetitorViews,
  currentMarketShare,
  onClose
}: DetailedHourlyChartProps) {

  // State for refresh trigger to force re-render when view counts are updated
  const [refreshTrigger, setRefreshTrigger] = React.useState<number>(0);

  // Listen for view count updates from refresh metadata
  React.useEffect(() => {
    const handleViewCountsUpdated = (event: CustomEvent) => {
      console.log('DetailedHourlyChart: Received view counts updated event', event.detail);
      // Force re-render by updating the refresh trigger
      setRefreshTrigger(prev => prev + 1);
    };

    // Add event listener
    window.addEventListener('viewCountsUpdated', handleViewCountsUpdated as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('viewCountsUpdated', handleViewCountsUpdated as EventListener);
    };
  }, []);

  // State for tooltip
  const [tooltip, setTooltip] = React.useState<{
    visible: boolean;
    x: number;
    y: number;
    data: {
      time: string;
      values: { label: string; value: string; color: string; }[];
    };
  }>({
    visible: false,
    x: 0,
    y: 0,
    data: { time: '', values: [] }
  });

  // Scale options for the dropdown
  const scaleOptions = [
    { value: 100, label: '100' },
    { value: 200, label: '200' },
    { value: 300, label: '300' },
    { value: 400, label: '400' },
    { value: 600, label: '600' },
    { value: 800, label: '800' },
    { value: 1000, label: '1K' },
    { value: 1500, label: '1.5K' },
    { value: 2000, label: '2K' },
    { value: 3000, label: '3K' },
    { value: 5000, label: '5K' },
    { value: 8000, label: '8K' },
    { value: 10000, label: '10K' },
    { value: 20000, label: '20K' },
    { value: 50000, label: '50K' },
    { value: 100000, label: '100K' }
  ];

  // State for selected scale - Default to 300 views
  const [selectedScale, setSelectedScale] = React.useState<number>(300);

  // Chart type options
  const chartTypeOptions = [
    { value: 'current', label: 'Current URL Analysis' },
    { value: 'all-urls', label: 'All URL View Performance' },
    { value: 'url-market-share', label: 'URL Market Share' },
    { value: 'channel-success', label: 'Channel Success' }
  ];

  // State for selected chart type - Default to current URL analysis
  const [selectedChartType, setSelectedChartType] = React.useState<string>('current');

  // Function to get all URL data from localStorage
  const getAllUrlData = React.useMemo(() => {
    const urlData: { [urlName: string]: { views: number; timestamp: number; }[] } = {};

    try {
      // Get all localStorage keys that contain video data
      const keys = Object.keys(localStorage).filter(key => key.startsWith('ytr_videos_') && !key.includes('timestamp'));

      keys.forEach(key => {
        const urlKey = key.replace('ytr_videos_', '');
        if (urlKey.includes('combined_')) return; // Skip combined keys

        const videoData = localStorage.getItem(key);
        if (videoData) {
          const videos = JSON.parse(videoData);
          const totalViews = videos.reduce((sum: number, video: any) => {
            const views = video.statistics?.viewCount ? Number(video.statistics.viewCount) : (video.viewCount || 0);
            return sum + views;
          }, 0);

          // Use URL key as name (since searchUrls is not available in this component)
          const urlName = urlKey;

          if (!urlData[urlName]) {
            urlData[urlName] = [];
          }

          urlData[urlName].push({
            views: totalViews,
            timestamp: Date.now()
          });
        }
      });
    } catch (error) {
      console.error('Error getting all URL data:', error);
    }

    return urlData;
  }, [refreshTrigger]); // Include refreshTrigger to force re-calculation when view counts are updated

  // Function to get channel data for channel success chart
  const getChannelData = React.useMemo(() => {
    const channelData: { [channelName: string]: { marketShare: number; views: number; timestamp: number; }[] } = {};

    try {
      // Get market share history
      const marketShareHistory = JSON.parse(localStorage.getItem('market_share_hourly_history') || '[]');

      // Get all video data to calculate channel views
      const keys = Object.keys(localStorage).filter(key => key.startsWith('ytr_videos_') && !key.includes('timestamp'));

      const allChannelViews: { [channelName: string]: number } = {};

      keys.forEach(key => {
        const videoData = localStorage.getItem(key);
        if (videoData) {
          const videos = JSON.parse(videoData);
          videos.forEach((video: any) => {
            const channelName = video.channelTitle;
            const views = video.statistics?.viewCount ? Number(video.statistics.viewCount) : (video.viewCount || 0);

            if (!allChannelViews[channelName]) {
              allChannelViews[channelName] = 0;
            }
            allChannelViews[channelName] += views;
          });
        }
      });

      // Calculate market share for each channel
      const totalViews = Object.values(allChannelViews).reduce((sum, views) => sum + views, 0);

      Object.entries(allChannelViews).forEach(([channelName, views]) => {
        const marketShare = totalViews > 0 ? (views / totalViews) * 100 : 0;

        if (!channelData[channelName]) {
          channelData[channelName] = [];
        }

        channelData[channelName].push({
          marketShare,
          views,
          timestamp: Date.now()
        });
      });

    } catch (error) {
      console.error('Error getting channel data:', error);
    }

    return channelData;
  }, [refreshTrigger]); // Include refreshTrigger to force re-calculation when view counts are updated

  // Generate chart data based on selected chart type
  const getChartDataByType = React.useMemo(() => {
    switch (selectedChartType) {
      case 'all-urls':
        return getAllUrlData;
      case 'url-market-share':
        // Calculate market share for each URL
        const urlMarketShareData: { [urlName: string]: { marketShare: number; timestamp: number; }[] } = {};
        const allUrlViews = getAllUrlData;
        const totalUrlViews = Object.values(allUrlViews).reduce((sum, urlData) => {
          return sum + urlData.reduce((urlSum, entry) => urlSum + entry.views, 0);
        }, 0);

        Object.entries(allUrlViews).forEach(([urlName, urlData]) => {
          urlMarketShareData[urlName] = urlData.map(entry => ({
            marketShare: totalUrlViews > 0 ? (entry.views / totalUrlViews) * 100 : 0,
            timestamp: entry.timestamp
          }));
        });

        return urlMarketShareData;
      case 'channel-success':
        return getChannelData;
      default:
        return {}; // Current URL analysis uses existing hourlyData
    }
  }, [selectedChartType, getAllUrlData, getChannelData]);

  // Color palette for different URLs and channels
  const getColorForItem = React.useCallback((itemName: string, index: number) => {
    // Special handling for @HowToGuys channel - always green
    if (itemName.toLowerCase().includes('howtoguys') || itemName.toLowerCase().includes('how to guys')) {
      return '#22c55e'; // Green
    }

    // Color palette for other items
    const colors = [
      '#60A5FA', // Blue
      '#ef4444', // Red
      '#f59e0b', // Amber
      '#8b5cf6', // Purple
      '#06b6d4', // Cyan
      '#f97316', // Orange
      '#84cc16', // Lime
      '#ec4899', // Pink
      '#6b7280', // Gray
      '#14b8a6', // Teal
      '#a855f7', // Violet
      '#eab308'  // Yellow
    ];

    return colors[index % colors.length];
  }, []);

  // Helper function to format numbers
  const formatNumber = React.useCallback((num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toLocaleString();
  }, []);

  // Helper function to format time
  const formatTime = React.useCallback((hour: number, minute: number) => {
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minute.toString().padStart(2, '0')} ${period}`;
  }, []);

  // Helper function to show tooltip
  const showTooltip = React.useCallback((event: React.MouseEvent, data: any) => {
    const rect = (event.currentTarget as SVGElement).getBoundingClientRect();
    const svgRect = (event.currentTarget as SVGElement).closest('svg')?.getBoundingClientRect();

    if (svgRect) {
      setTooltip({
        visible: true,
        x: event.clientX - svgRect.left + 10,
        y: event.clientY - svgRect.top - 10,
        data
      });
    }
  }, []);

  // Helper function to hide tooltip
  const hideTooltip = React.useCallback(() => {
    setTooltip(prev => ({ ...prev, visible: false }));
  }, []);

  // Generate granular 20-minute interval data for the last 12 hours
  const hourlyData = React.useMemo(() => {
    // Get hourly data from localStorage (same storage as market share collector)
    let hourlyHistoryData: MarketShareEntry[] = [];

    try {
      const storageKey = 'market_share_hourly_history';
      const savedData = localStorage.getItem(storageKey);
      if (savedData) {
        hourlyHistoryData = JSON.parse(savedData);
      }
    } catch (error) {
      console.error('Error loading hourly market share data:', error);
    }

    // Filter data for the last 12 hours
    const twelveHoursAgo = Date.now() - (12 * 60 * 60 * 1000);
    const recentData = hourlyHistoryData.filter(entry => entry.timestamp > twelveHoursAgo);

    console.log('DetailedHourlyChart: Granular data calculation', {
      totalHourlyData: hourlyHistoryData.length,
      recentData: recentData.length,
      currentData: { currentHowToGuysViews, currentCompetitorViews, currentMarketShare }
    });

    // Generate 36 periods of 20-minute blocks for the last 12 hours (12 hours * 3 periods per hour)
    const periods = Array.from({ length: 36 }, (_, periodIndex) => {
      const minutesBack = (35 - periodIndex) * 20; // Start from 35*20 = 700 minutes ago (11h 40m) to now
      const periodStart = Date.now() - (minutesBack * 60 * 1000);
      const periodEnd = periodStart + (20 * 60 * 1000); // 20 minute period

      // Find data points within this period
      const periodData = recentData.filter(entry =>
        entry.timestamp >= periodStart && entry.timestamp < periodEnd
      );

      // Use the latest data point in this period, or current data for the most recent period
      let howToGuysViews, competitorViews, marketShare, hasRealData;

      if (periodData.length > 0) {
        // Use the latest data point in this period
        const latestData = periodData[periodData.length - 1];
        howToGuysViews = latestData.howToGuysViews;
        competitorViews = latestData.competitorViews;
        marketShare = latestData.marketShare;
        hasRealData = true;
      } else if (periodIndex === 35 && (currentHowToGuysViews > 0 || currentCompetitorViews > 0)) {
        // For the most recent period, use current data if no historical data exists
        howToGuysViews = currentHowToGuysViews;
        competitorViews = currentCompetitorViews;
        marketShare = currentMarketShare;
        hasRealData = true;
      } else {
        // No real data available for this period
        howToGuysViews = null;
        competitorViews = null;
        marketShare = null;
        hasRealData = false;
      }

      const hour = new Date(periodStart).getHours();
      const minute = new Date(periodStart).getMinutes();

      return {
        hour,
        minute,
        howToGuysViews,
        competitorViews,
        marketShare,
        hasData: hasRealData,
        periodStart,
        periodEnd,
        dataPointsInPeriod: periodData.length
      };
    });

    return periods;
  }, [currentHowToGuysViews, currentCompetitorViews, currentMarketShare, refreshTrigger]);

  // Stable chart calculations - memoized to prevent flickering
  const chartCalculations = React.useMemo(() => {
    // Filter out null values for calculating max views
    const validData = hourlyData.filter(h => h.hasData && h.howToGuysViews !== null && h.competitorViews !== null);

    // Use selected scale as max views for consistent scaling
    const maxViews = selectedScale;

    // Calculate Delta (Δ) - Difference between max competitor views and max @HowToGuys views
    const maxHowToGuysViews = validData.length > 0 ? Math.max(...validData.map(d => d.howToGuysViews || 0)) : currentHowToGuysViews;
    const maxCompetitorViews = validData.length > 0 ? Math.max(...validData.map(d => d.competitorViews || 0)) : currentCompetitorViews;
    const deltaValue = maxCompetitorViews - maxHowToGuysViews;
    const deltaPercentage = maxHowToGuysViews > 0 ? ((deltaValue / maxHowToGuysViews) * 100) : 0;

    // Find the time periods when max values occurred
    const maxHowToGuysEntry = validData.find(d => d.howToGuysViews === maxHowToGuysViews);
    const maxCompetitorEntry = validData.find(d => d.competitorViews === maxCompetitorViews);

    console.log('Chart calculations with Delta:', {
      validDataPoints: validData.length,
      selectedScale,
      maxViews,
      maxHowToGuysViews,
      maxCompetitorViews,
      deltaValue,
      deltaPercentage,
      sampleValidData: validData.slice(-2)
    });

    return {
      validData,
      maxViews,
      deltaValue,
      deltaPercentage,
      maxHowToGuysViews,
      maxCompetitorViews,
      maxHowToGuysEntry,
      maxCompetitorEntry
    };
  }, [hourlyData, selectedScale, currentHowToGuysViews, currentCompetitorViews]);

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60"
      style={{ backdropFilter: 'blur(3px)' }}
      onClick={onClose} // Click outside to close
    >
      <div
        className="bg-gray-900 border border-gray-700 rounded-lg shadow-2xl p-4 relative"
        style={{
          width: '85vw',
          height: '80vh',
          maxWidth: '1400px',
          maxHeight: '900px',
          minWidth: '1000px',
          minHeight: '700px'
        }}
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside
      >
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white text-2xl font-bold z-10 bg-gray-800 hover:bg-gray-700 rounded-full w-8 h-8 flex items-center justify-center transition-colors"
          title="Close"
        >
          ×
        </button>
        <div className="flex justify-between items-start mb-2">
          <div className="flex items-center gap-4">
            <div>
              <div className="text-white text-xl font-bold">
                {selectedDayOfWeek} - 12 Hour Market Share Analysis
              </div>
              <div className="text-gray-300 text-sm">
                {channelName} vs Competitors - Last 12 Hours Breakdown (20min intervals)
              </div>
            </div>
            <div className="flex items-center gap-4 ml-6">
              <div className="flex items-center gap-2">
                <label className="text-gray-300 text-sm">Chart:</label>
                <select
                  value={selectedChartType}
                  onChange={(e) => setSelectedChartType(e.target.value)}
                  className="bg-gray-700 text-white px-2 py-1 rounded border border-gray-600 focus:border-blue-500 focus:outline-none text-sm"
                >
                  {chartTypeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex items-center gap-2">
                <label className="text-gray-300 text-sm">Scale:</label>
                <select
                  value={selectedScale}
                  onChange={(e) => setSelectedScale(Number(e.target.value))}
                  className="bg-gray-700 text-white px-2 py-1 rounded border border-gray-600 focus:border-blue-500 focus:outline-none text-sm"
                >
                  {scaleOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
          <div></div>
        </div>

        <div className="flex flex-col h-full">
          {/* Current Stats */}
          <div className="flex gap-6 mb-3 p-4 bg-gray-800 rounded-lg">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400">{currentMarketShare.toFixed(1)}%</div>
              <div className="text-sm text-gray-400">Current Market Share</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-green-400">{currentHowToGuysViews.toLocaleString()}</div>
              <div className="text-sm text-gray-400">@HowToGuys Views</div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-red-400">{currentCompetitorViews.toLocaleString()}</div>
              <div className="text-sm text-gray-400">Competitor Views</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-cyan-400">{hourlyData.filter(h => h.hasData).length}/12</div>
              <div className="text-sm text-gray-400">Hours with Data</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-yellow-400">{selectedScale.toLocaleString()}</div>
              <div className="text-sm text-gray-400">Max Scale</div>
            </div>
            {/* Delta (Δ) Analysis */}
            <div className="text-center border-l border-gray-600 pl-6">
              <div className="flex items-center justify-center gap-1">
                <span className="text-2xl font-bold text-orange-400">Δ</span>
                <span className={`text-xl font-bold ${chartCalculations.deltaValue >= 0 ? 'text-red-400' : 'text-green-400'}`}>
                  {chartCalculations.deltaValue >= 0 ? '+' : ''}{formatNumber(chartCalculations.deltaValue)}
                </span>
              </div>
              <div className="text-sm text-gray-400">Max Views Gap</div>
              <div className={`text-xs ${chartCalculations.deltaPercentage >= 0 ? 'text-red-300' : 'text-green-300'}`}>
                {chartCalculations.deltaPercentage >= 0 ? '+' : ''}{chartCalculations.deltaPercentage.toFixed(1)}%
              </div>
            </div>
            {/* Max Performance Indicators */}
            <div className="text-center border-l border-gray-600 pl-6">
              <div className="text-lg font-bold text-green-300">{formatNumber(chartCalculations.maxHowToGuysViews)}</div>
              <div className="text-xs text-gray-400">Max @HowToGuys</div>
              <div className="text-lg font-bold text-red-300">{formatNumber(chartCalculations.maxCompetitorViews)}</div>
              <div className="text-xs text-gray-400">Max Competitors</div>
            </div>
          </div>

          {/* Chart - Much Larger */}
          <div className="flex-1 relative" style={{ minHeight: '500px' }}>
            <svg
              width="100%"
              height="100%"
              className="bg-gray-800 rounded-lg"
              viewBox="0 0 1200 600"
              preserveAspectRatio="xMidYMid meet"
            >
              {/* Grid lines for view count scale */}
              {[0, 0.2, 0.4, 0.6, 0.8, 1.0].map((ratio, index) => (
                <line
                  key={`view-${index}`}
                  x1="80"
                  y1={520 - (ratio * 420)}
                  x2="1120"
                  y2={520 - (ratio * 420)}
                  stroke="#374151"
                  strokeWidth="1"
                  strokeDasharray="4,4"
                />
              ))}

              {/* Grid lines for percentage scale */}
              {[0, 20, 40, 60, 80, 100].map(y => (
                <line
                  key={`percent-${y}`}
                  x1="80"
                  y1={520 - (y * 4.2)}
                  x2="1120"
                  y2={520 - (y * 4.2)}
                  stroke="#1E40AF"
                  strokeWidth="0.5"
                  strokeDasharray="2,2"
                  opacity="0.5"
                />
              ))}

              {/* Vertical grid lines - for 36 periods (every 3 periods = 1 hour) */}
              {[0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33].map(periodIndex => (
                <line
                  key={periodIndex}
                  x1={80 + (periodIndex * (1040 / 35))}
                  y1="80"
                  x2={80 + (periodIndex * (1040 / 35))}
                  y2="520"
                  stroke="#374151"
                  strokeWidth="1"
                  strokeDasharray="4,4"
                />
              ))}

              {/* Left Y-axis labels (View Count) */}
              {[0, 0.2, 0.4, 0.6, 0.8, 1.0].map((ratio, index) => {
                const viewCount = Math.round(selectedScale * ratio);
                const formatViews = (views: number) => {
                  if (views >= 1000) return `${(views / 1000).toFixed(views >= 10000 ? 0 : 1)}K`;
                  return views.toString();
                };
                return (
                  <text
                    key={`view-label-${index}`}
                    x="70"
                    y={525 - (ratio * 420)}
                    fill="#9CA3AF"
                    fontSize="13"
                    textAnchor="end"
                  >
                    {formatViews(viewCount)}
                  </text>
                );
              })}

              {/* Right Y-axis labels (Percentage) */}
              {[0, 20, 40, 60, 80, 100].map(y => (
                <text
                  key={`percent-label-${y}`}
                  x="1130"
                  y={525 - (y * 4.2)}
                  fill="#60A5FA"
                  fontSize="13"
                  textAnchor="start"
                >
                  {y}%
                </text>
              ))}

              {/* X-axis labels - for 36 periods (show every 3rd = 1 hour intervals) */}
              {hourlyData.map((period, index) => {
                if (index % 3 === 0) { // Show every 3rd period (1 hour) for detailed hourly markings
                  const hour = new Date(period.periodStart).getHours();
                  return (
                    <text
                      key={index}
                      x={80 + (index * (1040 / 35))}
                      y="545"
                      fill="#9CA3AF"
                      fontSize="11"
                      textAnchor="middle"
                    >
                      {hour === 0 ? '12AM' : hour === 12 ? '12PM' : hour > 12 ? `${hour-12}PM` : `${hour}AM`}
                    </text>
                  );
                }
                return null;
              })}

              {/* Market share area fill - only for valid data */}
              <defs>
                <linearGradient id="marketShareGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor="#60A5FA" stopOpacity="0.3"/>
                  <stop offset="100%" stopColor="#60A5FA" stopOpacity="0.1"/>
                </linearGradient>
              </defs>

              {chartCalculations.validData.length > 0 && (
                <path
                  fill="url(#marketShareGradient)"
                  d={`M 80 520 ${chartCalculations.validData.map((d, i) => {
                    const originalIndex = hourlyData.findIndex(h => h.hour === d.hour && h.minute === d.minute);
                    return `L ${80 + (originalIndex * (1040 / 35))} ${520 - (d.marketShare * 4.2)}`;
                  }).join(' ')} L ${80 + ((hourlyData.length - 1) * (1040 / 35))} 520 Z`}
                />
              )}

              {/* Render lines based on chart type */}
              {selectedChartType === 'current' && (
                <>
                  {/* @HowToGuys line - GREEN - only for valid data */}
                  {chartCalculations.validData.length > 1 && (
                    <polyline
                      fill="none"
                      stroke="#22c55e"
                      strokeWidth="4"
                      points={chartCalculations.validData.map((entry) => {
                        const originalIndex = hourlyData.findIndex(h => h.hour === entry.hour && h.minute === entry.minute);
                        const x = 80 + (originalIndex * (1040 / 35));
                        const y = 520 - ((entry.howToGuysViews / chartCalculations.maxViews) * 420);
                        return `${x},${y}`;
                      }).join(' ')}
                    />
                  )}

                  {/* Competitors line - RED - only for valid data */}
                  {chartCalculations.validData.length > 1 && (
                    <polyline
                      fill="none"
                      stroke="#ef4444"
                      strokeWidth="4"
                      points={chartCalculations.validData.map((entry) => {
                        const originalIndex = hourlyData.findIndex(h => h.hour === entry.hour && h.minute === entry.minute);
                        const x = 80 + (originalIndex * (1040 / 35));
                        const y = 520 - ((entry.competitorViews / chartCalculations.maxViews) * 420);
                        return `${x},${y}`;
                      }).join(' ')}
                    />
                  )}

                  {/* Market share line - BLUE - only for valid data */}
                  {chartCalculations.validData.length > 1 && (
                    <polyline
                      fill="none"
                      stroke="#60A5FA"
                      strokeWidth="4"
                      points={chartCalculations.validData.map((d) => {
                        const originalIndex = hourlyData.findIndex(h => h.hour === d.hour && h.minute === d.minute);
                        return `${80 + (originalIndex * (1040 / 35))},${520 - (d.marketShare * 4.2)}`;
                      }).join(' ')}
                    />
                  )}
                </>
              )}

              {/* Render lines for other chart types */}
              {selectedChartType !== 'current' && (
                <>
                  {Object.entries(getChartDataByType).map(([itemName, itemData], index) => {
                    const color = getColorForItem(itemName, index);
                    const isHowToGuys = itemName.toLowerCase().includes('howtoguys') || itemName.toLowerCase().includes('how to guys');

                    // Create tooltip data for other chart types
                    const getTooltipForOtherCharts = (itemName: string, itemData: any) => {
                      if (selectedChartType === 'all-urls') {
                        const totalViews = Array.isArray(itemData) ? itemData.reduce((sum: number, entry: any) => sum + entry.views, 0) : 0;
                        return {
                          time: 'Current Data',
                          values: [
                            { label: 'URL Name', value: itemName, color: color },
                            { label: 'Total Views', value: formatNumber(totalViews), color: color },
                            { label: 'Data Points', value: Array.isArray(itemData) ? itemData.length.toString() : '0', color: '#9CA3AF' }
                          ]
                        };
                      } else if (selectedChartType === 'url-market-share') {
                        const marketShare = Array.isArray(itemData) ? (itemData[0]?.marketShare || 0) : 0;
                        return {
                          time: 'Current Data',
                          values: [
                            { label: 'URL Name', value: itemName, color: color },
                            { label: 'Market Share', value: `${marketShare.toFixed(1)}%`, color: color },
                            { label: 'Type', value: 'URL Performance', color: '#9CA3AF' }
                          ]
                        };
                      } else if (selectedChartType === 'channel-success') {
                        const channelData = Array.isArray(itemData) ? itemData[0] : itemData;
                        return {
                          time: 'Current Data',
                          values: [
                            { label: 'Channel Name', value: itemName, color: color },
                            { label: 'Market Share', value: `${(channelData?.marketShare || 0).toFixed(1)}%`, color: color },
                            { label: 'Total Views', value: formatNumber(channelData?.views || 0), color: color },
                            { label: 'Type', value: isHowToGuys ? '@HowToGuys Channel' : 'Competitor Channel', color: isHowToGuys ? '#22c55e' : '#ef4444' }
                          ]
                        };
                      }
                      return { time: '', values: [] };
                    };

                    const tooltipData = getTooltipForOtherCharts(itemName, itemData);

                    // For demonstration, create simple line with current data
                    // In a real implementation, you'd process the historical data properly
                    return (
                      <g key={itemName}>
                        <line
                          x1="80"
                          y1="300"
                          x2="1120"
                          y2={300 + (index * 20)}
                          stroke={color}
                          strokeWidth={isHowToGuys ? "6" : "4"}
                          strokeDasharray={isHowToGuys ? "none" : "none"}
                        />
                        {/* Add data points with hover */}
                        <circle
                          cx="600"
                          cy={300 + (index * 20)}
                          r={isHowToGuys ? "8" : "6"}
                          fill={color}
                          stroke="#1F2937"
                          strokeWidth="2"
                          style={{ cursor: 'pointer' }}
                          onMouseEnter={(e) => showTooltip(e, tooltipData)}
                          onMouseLeave={hideTooltip}
                        />
                        {/* Invisible hover area for better UX */}
                        <circle
                          cx="600"
                          cy={300 + (index * 20)}
                          r="15"
                          fill="transparent"
                          style={{ cursor: 'pointer' }}
                          onMouseEnter={(e) => showTooltip(e, tooltipData)}
                          onMouseLeave={hideTooltip}
                        />
                      </g>
                    );
                  })}
                </>
              )}

              {/* Data points - only for current chart type and valid data */}
              {selectedChartType === 'current' && hourlyData.map((entry, index) => {
                if (!entry.hasData || entry.howToGuysViews === null || entry.competitorViews === null || entry.marketShare === null) return null;

                const x = 80 + (index * (1040 / 35));
                const yHowTo = 520 - ((entry.howToGuysViews / chartCalculations.maxViews) * 420);
                const yComp = 520 - ((entry.competitorViews / chartCalculations.maxViews) * 420);
                const yMarket = 520 - (entry.marketShare * 4.2);

                const timeStr = formatTime(entry.hour, entry.minute);
                // Calculate period-specific delta
                const periodDelta = entry.competitorViews - entry.howToGuysViews;
                const periodDeltaPercentage = entry.howToGuysViews > 0 ? ((periodDelta / entry.howToGuysViews) * 100) : 0;

                const tooltipData = {
                  time: timeStr,
                  values: [
                    { label: '@HowToGuys Views', value: formatNumber(entry.howToGuysViews), color: '#22c55e' },
                    { label: 'Competitor Views', value: formatNumber(entry.competitorViews), color: '#ef4444' },
                    { label: 'Market Share', value: `${entry.marketShare.toFixed(1)}%`, color: '#60A5FA' },
                    { label: 'Period Delta (Δ)', value: `${periodDelta >= 0 ? '+' : ''}${formatNumber(periodDelta)} (${periodDeltaPercentage >= 0 ? '+' : ''}${periodDeltaPercentage.toFixed(1)}%)`, color: periodDelta >= 0 ? '#ef4444' : '#22c55e' },
                    { label: 'Total Views', value: formatNumber(entry.howToGuysViews + entry.competitorViews), color: '#9CA3AF' }
                  ]
                };

                return (
                  <g key={index}>
                    {/* Invisible hover area for better UX */}
                    <circle
                      cx={x}
                      cy={(yHowTo + yComp + yMarket) / 3}
                      r="15"
                      fill="transparent"
                      style={{ cursor: 'pointer' }}
                      onMouseEnter={(e) => showTooltip(e, tooltipData)}
                      onMouseLeave={hideTooltip}
                    />

                    {/* @HowToGuys point - GREEN */}
                    <circle
                      cx={x}
                      cy={yHowTo}
                      r="5"
                      fill="#22c55e"
                      stroke="#1F2937"
                      strokeWidth="2"
                      style={{ cursor: 'pointer' }}
                      onMouseEnter={(e) => showTooltip(e, tooltipData)}
                      onMouseLeave={hideTooltip}
                    />
                    {/* Competitors point - RED */}
                    <circle
                      cx={x}
                      cy={yComp}
                      r="5"
                      fill="#ef4444"
                      stroke="#1F2937"
                      strokeWidth="2"
                      style={{ cursor: 'pointer' }}
                      onMouseEnter={(e) => showTooltip(e, tooltipData)}
                      onMouseLeave={hideTooltip}
                    />
                    {/* Market share point - BLUE */}
                    <circle
                      cx={x}
                      cy={yMarket}
                      r="5"
                      fill="#60A5FA"
                      stroke="#1F2937"
                      strokeWidth="2"
                      style={{ cursor: 'pointer' }}
                      onMouseEnter={(e) => showTooltip(e, tooltipData)}
                      onMouseLeave={hideTooltip}
                    />
                  </g>
                );
              })}

              {/* Delta (Δ) Visual Indicator - Show gap between max values */}
              {selectedChartType === 'current' && chartCalculations.validData.length > 0 && chartCalculations.maxHowToGuysEntry && chartCalculations.maxCompetitorEntry && (
                <>
                  {/* Find positions of max values */}
                  {(() => {
                    const maxHowToIndex = hourlyData.findIndex(h => h.hour === chartCalculations.maxHowToGuysEntry?.hour && h.minute === chartCalculations.maxHowToGuysEntry?.minute);
                    const maxCompIndex = hourlyData.findIndex(h => h.hour === chartCalculations.maxCompetitorEntry?.hour && h.minute === chartCalculations.maxCompetitorEntry?.minute);

                    if (maxHowToIndex >= 0 && maxCompIndex >= 0) {
                      const xHowTo = 80 + (maxHowToIndex * (1040 / 35));
                      const yHowTo = 520 - ((chartCalculations.maxHowToGuysViews / chartCalculations.maxViews) * 420);
                      const xComp = 80 + (maxCompIndex * (1040 / 35));
                      const yComp = 520 - ((chartCalculations.maxCompetitorViews / chartCalculations.maxViews) * 420);

                      return (
                        <g>
                          {/* Delta line connecting max points */}
                          <line
                            x1={xHowTo}
                            y1={yHowTo}
                            x2={xComp}
                            y2={yComp}
                            stroke="#f97316"
                            strokeWidth="3"
                            strokeDasharray="8,4"
                            opacity="0.8"
                          />

                          {/* Delta symbol and value at midpoint */}
                          <g transform={`translate(${(xHowTo + xComp) / 2}, ${(yHowTo + yComp) / 2 - 20})`}>
                            <rect
                              x="-35"
                              y="-12"
                              width="70"
                              height="24"
                              fill="#1F2937"
                              stroke="#f97316"
                              strokeWidth="1"
                              rx="4"
                              opacity="0.9"
                            />
                            <text
                              x="0"
                              y="0"
                              fill="#f97316"
                              fontSize="14"
                              fontWeight="bold"
                              textAnchor="middle"
                              dominantBaseline="middle"
                            >
                              Δ {formatNumber(Math.abs(chartCalculations.deltaValue))}
                            </text>
                          </g>

                          {/* Highlight max points */}
                          <circle
                            cx={xHowTo}
                            cy={yHowTo}
                            r="8"
                            fill="#22c55e"
                            stroke="#ffffff"
                            strokeWidth="2"
                            opacity="0.9"
                          />
                          <circle
                            cx={xComp}
                            cy={yComp}
                            r="8"
                            fill="#ef4444"
                            stroke="#ffffff"
                            strokeWidth="2"
                            opacity="0.9"
                          />
                        </g>
                      );
                    }
                    return null;
                  })()}
                </>
              )}

              {/* Show message when no real data is available */}
              {chartCalculations.validData.length === 0 && (
                <text x="600" y="300" fill="#9CA3AF" fontSize="18" textAnchor="middle">
                  No real data available for this time period
                </text>
              )}

              {/* Chart title */}
              <text x="600" y="40" fill="#FFFFFF" fontSize="18" fontWeight="bold" textAnchor="middle">
                {selectedChartType === 'current' && 'Market Share & View Count Trend - Last 12 Hours (20min intervals)'}
                {selectedChartType === 'all-urls' && 'All URL View Performance Comparison - Last 12 Hours'}
                {selectedChartType === 'url-market-share' && 'URL Market Share Distribution - Last 12 Hours'}
                {selectedChartType === 'channel-success' && 'Channel Success & Market Share Analysis - Last 12 Hours'}
              </text>

              {/* Y-axis labels */}
              <text x="40" y="300" fill="#9CA3AF" fontSize="14" textAnchor="middle" transform="rotate(-90, 40, 300)">
                View Count
              </text>
              <text x="1160" y="300" fill="#60A5FA" fontSize="14" textAnchor="middle" transform="rotate(90, 1160, 300)">
                Market Share %
              </text>

              {/* Dynamic Legend based on chart type */}
              <g transform="translate(120, 60)">
                {selectedChartType === 'current' && (
                  <>
                    <circle cx="0" cy="0" r="4" fill="#60A5FA"/>
                    <text x="10" y="4" fill="#60A5FA" fontSize="11" fontWeight="bold">Market Share %</text>

                    <circle cx="120" cy="0" r="4" fill="#22c55e"/>
                    <text x="130" y="4" fill="#22c55e" fontSize="11" fontWeight="bold">@HowToGuys</text>

                    <circle cx="220" cy="0" r="4" fill="#ef4444"/>
                    <text x="230" y="4" fill="#ef4444" fontSize="11" fontWeight="bold">Competitors</text>

                    <text x="320" y="4" fill="#9CA3AF" fontSize="10">Left: Views | Right: %</text>
                  </>
                )}

                {selectedChartType !== 'current' && (
                  <>
                    {Object.entries(getChartDataByType).slice(0, 6).map(([itemName, itemData], index) => {
                      const color = getColorForItem(itemName, index);
                      const isHowToGuys = itemName.toLowerCase().includes('howtoguys') || itemName.toLowerCase().includes('how to guys');
                      const xPos = index * 140;

                      return (
                        <g key={itemName}>
                          <circle cx={xPos} cy="0" r={isHowToGuys ? "5" : "4"} fill={color}/>
                          <text
                            x={xPos + 10}
                            y="4"
                            fill={color}
                            fontSize={isHowToGuys ? "12" : "11"}
                            fontWeight={isHowToGuys ? "bold" : "normal"}
                          >
                            {itemName.length > 12 ? `${itemName.substring(0, 12)}...` : itemName}
                          </text>
                        </g>
                      );
                    })}
                    {Object.keys(getChartDataByType).length > 6 && (
                      <text x="840" y="4" fill="#9CA3AF" fontSize="10">+{Object.keys(getChartDataByType).length - 6} more</text>
                    )}
                  </>
                )}
              </g>
            </svg>

            {/* Tooltip */}
            {tooltip.visible && (
              <div
                className="absolute z-50 bg-gray-900 border border-gray-600 rounded-lg shadow-xl p-3 pointer-events-none"
                style={{
                  left: `${tooltip.x}px`,
                  top: `${tooltip.y}px`,
                  transform: tooltip.x > 600 ? 'translateX(-100%)' : 'none', // Flip to left if too far right
                  maxWidth: '280px',
                  minWidth: '200px'
                }}
              >
                {/* Time header */}
                <div className="text-white font-bold text-sm mb-2 border-b border-gray-600 pb-1">
                  {tooltip.data.time}
                </div>

                {/* Values */}
                <div className="space-y-1">
                  {tooltip.data.values.map((item, index) => (
                    <div key={index} className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full border border-gray-700"
                          style={{ backgroundColor: item.color }}
                        />
                        <span className="text-gray-300">{item.label}:</span>
                      </div>
                      <span
                        className="font-bold text-right"
                        style={{ color: item.color }}
                      >
                        {item.value}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Tooltip arrow */}
                <div
                  className="absolute w-2 h-2 bg-gray-900 border-l border-b border-gray-600 transform rotate-45"
                  style={{
                    left: tooltip.x > 600 ? 'calc(100% - 8px)' : '8px',
                    top: '-4px'
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});
