const Database = require('better-sqlite3');

function resetAdmin() {
  try {
    console.log('Starting admin reset...');
    const db = new Database('data.db');
    
    // Check if admin user exists
    const adminUser = db.prepare("SELECT * FROM users WHERE username = 'admin'").get();
    
    if (adminUser) {
      console.log('Admin user found, updating...');
      // Update admin user with plain text password and admin role
      const updateResult = db.prepare("UPDATE users SET password = '1', role = 'admin', is_admin = 1 WHERE username = 'admin'").run();
      console.log(`Updated ${updateResult.changes} rows`);
    } else {
      console.log('Admin user not found, creating...');
      // Create admin user with plain text password and admin role
      const insertResult = db.prepare("INSERT INTO users (username, password, role, is_admin) VALUES ('admin', '1', 'admin', 1)").run();
      console.log(`Inserted ${insertResult.changes} rows`);
    }
    
    // Verify admin user
    const verifyAdmin = db.prepare("SELECT * FROM users WHERE username = 'admin'").get();
    if (verifyAdmin) {
      console.log('Admin user verified:', { 
        id: verifyAdmin.id, 
        username: verifyAdmin.username, 
        password: verifyAdmin.password,
        role: verifyAdmin.role,
        is_admin: verifyAdmin.is_admin
      });
    } else {
      console.log('Failed to verify admin user');
    }
    
    // Create a test user
    console.log('Creating test user...');
    const testUser = db.prepare("SELECT * FROM users WHERE username = 'test'").get();
    
    if (testUser) {
      console.log('Test user found, updating...');
      const updateResult = db.prepare("UPDATE users SET password = 'test', role = 'user', is_admin = 0 WHERE username = 'test'").run();
      console.log(`Updated ${updateResult.changes} rows`);
    } else {
      console.log('Test user not found, creating...');
      const insertResult = db.prepare("INSERT INTO users (username, password, role, is_admin) VALUES ('test', 'test', 'user', 0)").run();
      console.log(`Inserted ${insertResult.changes} rows`);
    }
    
    // Verify test user
    const verifyTest = db.prepare("SELECT * FROM users WHERE username = 'test'").get();
    if (verifyTest) {
      console.log('Test user verified:', { 
        id: verifyTest.id, 
        username: verifyTest.username, 
        password: verifyTest.password,
        role: verifyTest.role,
        is_admin: verifyTest.is_admin
      });
    } else {
      console.log('Failed to verify test user');
    }
    
    console.log('Reset completed successfully');
  } catch (error) {
    console.error('Reset failed:', error);
  }
}

resetAdmin();
