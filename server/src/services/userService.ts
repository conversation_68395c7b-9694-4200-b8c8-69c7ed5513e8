import { User } from '../models/user';

export interface UserResponse {
  id: string;
  username: string;
  role: string;
  isAdmin: boolean;
  // other fields...
}

export class UserService {
  async getUser(id: string): Promise<UserResponse> {
    const user = await User.findByPk(id);
    if (!user) throw new Error('User not found');
    
    return {
      ...user.toJSON(),
      isAdmin: user.role === 'admin' || <PERSON><PERSON><PERSON>(user.isAdmin)
    };
  }
}
