import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { useWorkflow } from '../workflow-state';
import { 
  Zap, 
  Copy, 
  Save, 
  Edit, 
  Trash2, 
  Star,
  Crown,
  Target,
  TrendingUp,
  Users,
  Clock,
  CheckCircle
} from 'lucide-react';

interface PromptTemplate {
  id: string;
  name: string;
  version: string;
  description: string;
  targetAudience: string;
  focusArea: string;
  basePrompt: string;
  strategies: string[];
  chunks: string[];
  tone: string;
  estimatedWatchTime: string;
  engagementLevel: 'high' | 'medium' | 'premium';
  dateCreated: Date;
  isDefault: boolean;
}

const PromptTemplatesModule: React.FC = () => {
  const { toast } = useToast();
  const { updateStepData, markStepComplete } = useWorkflow();
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<PromptTemplate | null>(null);
  const [generatedInstructions, setGeneratedInstructions] = useState('');
  const [originalScript, setOriginalScript] = useState('');

  // Load templates from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('txtpro_prompt_templates');
    if (saved) {
      setTemplates(JSON.parse(saved));
    } else {
      // Initialize with default templates
      const defaultTemplates = getDefaultTemplates();
      setTemplates(defaultTemplates);
      localStorage.setItem('txtpro_prompt_templates', JSON.stringify(defaultTemplates));
    }
  }, []);

  // Save templates to localStorage
  useEffect(() => {
    localStorage.setItem('txtpro_prompt_templates', JSON.stringify(templates));
  }, [templates]);

  const getDefaultTemplates = (): PromptTemplate[] => [
    {
      id: 'slick-v1',
      name: 'SlickV1',
      version: '1.0',
      description: 'Comprehensive financial expert prompt for Social Security and government benefit recipients',
      targetAudience: 'Social Security, SSI, SSDI, VA beneficiaries',
      focusArea: 'Government benefits and financial relief',
      basePrompt: `You are a highly experienced and compassionate financial expert, specializing in government benefit programs and financial relief for individuals on fixed incomes, including Social Security, Supplemental Security Income (SSI), Social Security Disability Insurance (SSDI), and Veterans Affairs (VA) beneficiaries.

Your primary objective is to generate an expert financial opinion that is incredibly beneficial, empowering, and actionable for these beneficiaries, regardless of the specific financial topic.

In crafting this opinion, you MUST adopt the following style and communication methods:

• Urgent & Direct Tone: Convey paramount importance and time-sensitivity. Address audience directly using "Hello everyone," "you need to hear this," "Are you one of the millions of Americans," and "This is urgent, this is real." Emphasize what beneficiaries "need to know right now" and "Don't let this opportunity slip away."

• Informative & Detailed Breakdown: Provide clear, comprehensive explanations. Use rhetorical questions: "What exactly is this payment?", "Who qualifies?", "Do you need to apply?", "Is this taxable?", "When is this money coming?", "What should you do with this money?" Start with "Let's start with the basics" or "Let's break down exactly what this new bill means for you."

• Empathetic & Reassuring Language: Acknowledge struggles using "struggling to stay afloat", "hit hardest by inflation", "much needed relief." Offer hope with "better days are here and brighter ones, maybe just around the corner." Use "your concern is completely valid" and "it's about restoring fairness and dignity."

• Action-Oriented & Practical Advice: Guide on concrete steps. Prioritize verifying and updating contact/banking information with SSA, VA, IRS. Recommend prudent financial management: high-interest debt repayment, emergency savings, financial advisor consultation.

• Structure & Emphasis: Use bolded headings and bullet points extensively. Strong introductory remarks that grab attention. Concluding remarks that summarize key takeaways. Emphasize with "critical update", "absolutely critical", "most important question", "huge news", "paramount importance", "life-changing opportunity."

• Proactive Scam Warnings: Clear warnings about scams. State what legitimate agencies never ask for: personal info via unsolicited contact, fees, gift cards, wire transfers, cryptocurrency, cash.

• Forward-Looking Perspective: Touch on future possibilities, legislative discussions, potential reforms. Foster awareness and hope without false expectations.

• Strong Call to Action: Encourage subscription and notifications. Prompt engagement through likes, shares, comments. "Drop a comment below with any questions" or "Let me know how this money would help you."`,
      strategies: ['Empathetic Senior Voice', 'Urgent Breaking News', 'Reassuring for Fixed Income'],
      chunks: ['Government Benefit Hook', 'Scam Warning CTA', 'Financial Action Steps'],
      tone: 'Urgent, empathetic, authoritative',
      estimatedWatchTime: '6-8 minutes',
      engagementLevel: 'high',
      dateCreated: new Date(),
      isDefault: true
    },
    {
      id: 'slick-v2-retention',
      name: 'SlickV2 - Retention Focus',
      version: '2.0',
      description: 'Optimized for maximum watch time and audience retention',
      targetAudience: 'Social Security recipients',
      focusArea: 'Watch time optimization',
      basePrompt: `You are a trusted financial advisor specializing in Social Security and government benefits. Your goal is to create content that keeps viewers engaged throughout the entire video.

RETENTION OPTIMIZATION TECHNIQUES:
• Hook within 3 seconds: Start with exact dollar amounts or specific dates
• Pattern interrupts every 30 seconds: Use "But here's what most people don't know..." or "Wait, there's more..."
• Curiosity gaps: "I'll reveal the exact amount in just a moment, but first..."
• Progress indicators: "We're covering 5 key points, and we're on number 2..."
• Cliffhangers: "The third payment method will surprise you..."

ENGAGEMENT STRUCTURE:
• Open with immediate value: "Your Social Security payment is changing by $X on [specific date]"
• Use the "Yes Ladder": Ask questions viewers will answer "yes" to
• Create urgency without panic: "This window closes on [date]"
• Personal stakes: "This could mean an extra $X per year for you"
• Social proof: "Over 70 million Americans are affected by this"

RETENTION HOOKS:
• "Before we dive in, make sure you're subscribed because this information could save you thousands"
• "I'm going to share 3 things your financial advisor probably hasn't told you"
• "Stick around until the end because I have a special resource for you"`,
      strategies: ['Storytelling + Facts', 'Urgent Breaking News'],
      chunks: ['Retention Hook', 'Progress Indicator', 'End Screen CTA'],
      tone: 'Engaging, urgent, valuable',
      estimatedWatchTime: '8-10 minutes',
      engagementLevel: 'premium',
      dateCreated: new Date(),
      isDefault: true
    },
    {
      id: 'slick-v3-conversion',
      name: 'SlickV3 - Conversion Focus',
      version: '3.0',
      description: 'Optimized for subscriber conversion and channel growth',
      targetAudience: 'New viewers and potential subscribers',
      focusArea: 'Subscriber conversion',
      basePrompt: `You are a financial educator focused on building a loyal community of Social Security and government benefit recipients.

CONVERSION OPTIMIZATION:
• Value-first approach: Deliver immediate, actionable value before any ask
• Authority building: Reference specific laws, dates, and official sources
• Community building: "Join our community of informed beneficiaries"
• Exclusive value: "Subscribers get early access to benefit updates"
• Fear of missing out: "Don't miss future updates that could affect your benefits"

SUBSCRIBER CONVERSION TACTICS:
• Early soft ask: "If this information is helpful, consider subscribing for more updates"
• Value proposition: "I post weekly updates on Social Security changes"
• Social proof: "Join [X] thousand subscribers who stay informed"
• Urgency: "Subscribe now so you never miss critical benefit updates"
• End screen optimization: "Your next video is ready - click here to continue learning"

TRUST BUILDING ELEMENTS:
• Transparency: "I'll always give you the facts, even when they're not what you want to hear"
• Consistency: "Every Tuesday, I break down the latest benefit news"
• Accessibility: "I make complex financial information simple to understand"
• Advocacy: "I'm here to help you get every dollar you're entitled to"`,
      strategies: ['Community Building', 'Authority Positioning', 'Value-First Approach'],
      chunks: ['Subscribe Value Prop', 'Community Invitation', 'Next Video Tease'],
      tone: 'Trustworthy, valuable, community-focused',
      estimatedWatchTime: '5-7 minutes',
      engagementLevel: 'high',
      dateCreated: new Date(),
      isDefault: true
    }
  ];

  const handleSelectTemplate = (template: PromptTemplate) => {
    setSelectedTemplate(template);
    
    // Update workflow with template data
    updateStepData('strategy-selection', { 
      selectedStrategies: template.strategies.map(s => ({ title: s, description: `From ${template.name}` }))
    });
    updateStepData('chunk-creation', { 
      selectedChunks: template.chunks.map(c => ({ name: c, content: `Template chunk from ${template.name}` }))
    });
    updateStepData('configuration', {
      title: `Script using ${template.name}`,
      targetAudience: template.targetAudience,
      tone: template.tone,
      length: template.estimatedWatchTime
    });
    
    markStepComplete('strategy-selection');
    markStepComplete('chunk-creation');
    markStepComplete('configuration');

    toast({
      title: "Template Selected",
      description: `${template.name} has been loaded into your workflow`
    });
  };

  const handleGenerateWithTemplate = () => {
    // Use selected template or default to SlickV1
    const templateToUse = selectedTemplate || templates.find(t => t.id === 'slick-v1') || templates[0];

    if (!templateToUse) {
      toast({
        title: "❌ No Templates Available",
        description: "Please refresh the page to load default templates",
        variant: "destructive"
      });
      return;
    }

    // Start with the template's base prompt
    let instructions = templateToUse.basePrompt;

    // Add original script context if provided
    if (originalScript.trim()) {
      instructions += `\n\n=== ORIGINAL SCRIPT FOR CONTEXT ===\n`;
      instructions += `Here is my original script for style and approach reference:\n\n`;
      instructions += `"${originalScript.trim()}"\n\n`;
      instructions += `=== END ORIGINAL SCRIPT ===\n\n`;
      instructions += `Based on the original script above, create a new script that follows the same established style, tone, and approach while incorporating any new information or topic I provide. Maintain consistency with my voice and messaging patterns shown in the original script.`;
    } else {
      instructions += `\n\nCreate a complete script following all the guidelines above.`;
    }

    instructions += `\n\nIMPORTANT: Generate a complete, engaging script that is informative and actionable for the target audience. Include specific examples, clear explanations, and strong calls-to-action as outlined in the style guidelines.`;

    setGeneratedInstructions(instructions);
    updateStepData('generation', { finalInstructions: instructions });
    markStepComplete('generation');

    toast({
      title: "🎉 Prompt Generated Successfully!",
      description: `Your ${templateToUse.name} prompt is ready. Click "Copy to Clipboard" to use it in ChatGPT.`,
      duration: 4000
    });
  };

  const handleCopyInstructions = async () => {
    if (!generatedInstructions) {
      toast({
        title: "❌ No Instructions Generated",
        description: "Please generate instructions first",
        variant: "destructive"
      });
      return;
    }

    try {
      await navigator.clipboard.writeText(generatedInstructions);
      toast({
        title: "✅ Copied to Clipboard!",
        description: `Your ${selectedTemplate?.name} prompt is ready to paste into ChatGPT`,
        duration: 5000
      });
    } catch (error) {
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = generatedInstructions;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      toast({
        title: "✅ Copied to Clipboard!",
        description: `Your ${selectedTemplate?.name} prompt is ready to paste into ChatGPT`,
        duration: 5000
      });
    }
  };

  const getEngagementColor = (level: string) => {
    switch (level) {
      case 'premium': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'high': return 'bg-green-100 text-green-800 border-green-300';
      default: return 'bg-blue-100 text-blue-800 border-blue-300';
    }
  };

  return (
    <div className="space-y-6">
      {/* Value Proposition Header */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-yellow-600" />
            Professional Prompt Templates
          </CardTitle>
          <CardDescription className="text-yellow-700">
            💡 <strong>Why use this:</strong> Access proven, high-performing prompt templates. Get professional-grade ChatGPT instructions instantly, optimized for different goals like retention, conversion, and engagement.
          </CardDescription>
          <div className="flex gap-2 mt-3">
            <Badge variant="secondary" className="text-xs">
              👑 Professional templates
            </Badge>
            <Badge variant="secondary" className="text-xs">
              🎯 Goal-optimized prompts
            </Badge>
            <Badge variant="secondary" className="text-xs">
              ⚡ Instant script generation
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Template Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Select Professional Template</CardTitle>
          <CardDescription>
            Choose a proven prompt template optimized for your specific goals
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {templates.map(template => (
              <Card
                key={template.id}
                className={`hover:shadow-lg transition-all cursor-pointer ${
                  selectedTemplate?.id === template.id ? 'ring-2 ring-yellow-500 bg-yellow-50' : ''
                }`}
                onClick={() => handleSelectTemplate(template)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {template.isDefault && <Crown className="h-4 w-4 text-yellow-600" />}
                      <CardTitle className="text-base">{template.name}</CardTitle>
                    </div>
                    <Badge className={`text-xs ${getEngagementColor(template.engagementLevel)}`}>
                      {template.engagementLevel}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{template.description}</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <Label className="text-xs font-medium text-muted-foreground">TARGET AUDIENCE</Label>
                      <p className="text-sm">{template.targetAudience}</p>
                    </div>
                    <div>
                      <Label className="text-xs font-medium text-muted-foreground">FOCUS AREA</Label>
                      <p className="text-sm">{template.focusArea}</p>
                    </div>
                    <div>
                      <Label className="text-xs font-medium text-muted-foreground">ESTIMATED WATCH TIME</Label>
                      <p className="text-sm flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {template.estimatedWatchTime}
                      </p>
                    </div>
                    <div>
                      <Label className="text-xs font-medium text-muted-foreground">INCLUDED STRATEGIES</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {template.strategies.slice(0, 2).map(strategy => (
                          <Badge key={strategy} variant="outline" className="text-xs">
                            {strategy}
                          </Badge>
                        ))}
                        {template.strategies.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{template.strategies.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  {selectedTemplate?.id === template.id && (
                    <div className="mt-4 p-3 bg-yellow-100 rounded-lg">
                      <div className="flex items-center gap-2 text-yellow-800">
                        <CheckCircle className="h-4 w-4" />
                        <span className="text-sm font-medium">Template Selected</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Original Script Input */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Original Script (Optional)</CardTitle>
          <CardDescription>
            Add your original script for context and style reference
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Textarea
              placeholder="Paste your original script here for context and style reference..."
              value={originalScript}
              onChange={(e) => setOriginalScript(e.target.value)}
              rows={8}
              className="min-h-[200px]"
            />
            <p className="text-xs text-muted-foreground">
              💡 Tip: Adding your original script helps ChatGPT understand your style and create more consistent content
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Generate Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">🚀 Generate Your ChatGPT Prompt</CardTitle>
          <CardDescription>
            Create a complete, professional prompt ready to paste into ChatGPT
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Always show generation options */}
          <div className="space-y-4">
            {selectedTemplate ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">✅ Template Selected: {selectedTemplate.name}</h4>
                <p className="text-sm text-blue-800 mb-3">
                  Ready to generate your professional ChatGPT prompt using {selectedTemplate.name}.
                </p>
              </div>
            ) : (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">🚀 Quick Generate</h4>
                <p className="text-sm text-gray-700 mb-3">
                  No template selected? No problem! Generate instructions using the default SlickV1 template.
                </p>
              </div>
            )}

            {!generatedInstructions && (
              <div className="flex gap-3 flex-wrap">
                <Button
                  onClick={handleGenerateWithTemplate}
                  className="bg-yellow-600 hover:bg-yellow-700"
                  size="lg"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  {selectedTemplate ? `Generate ${selectedTemplate.name} Prompt` : 'Generate SlickV1 Prompt'}
                </Button>

                {!selectedTemplate && (
                  <div className="text-sm text-gray-600 flex items-center">
                    💡 Will use SlickV1 (your original prompt) as default
                  </div>
                )}
              </div>
            )}
          </div>

          {generatedInstructions && (
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-2">🎉 Your ChatGPT Prompt is Ready!</h4>
                <p className="text-sm text-green-800 mb-4">
                  Your {selectedTemplate?.name} prompt has been generated and is ready to copy. This includes your template instructions{originalScript.trim() ? ' plus your original script for context' : ''}.
                </p>

                <div className="flex gap-3 flex-wrap">
                  <Button
                    onClick={handleCopyInstructions}
                    className="bg-green-600 hover:bg-green-700"
                    size="lg"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    📋 Copy Complete Prompt to Clipboard
                  </Button>
                  <Button
                    onClick={() => setGeneratedInstructions('')}
                    variant="outline"
                  >
                    🔄 Generate New Prompt
                  </Button>
                  <Button
                    onClick={() => {
                      toast({
                        title: "🧪 Copy Test",
                        description: "Testing clipboard functionality...",
                        duration: 2000
                      });
                      navigator.clipboard.writeText("Test copy - this is working!").then(() => {
                        toast({
                          title: "✅ Clipboard Works!",
                          description: "Your browser supports copying to clipboard",
                          duration: 3000
                        });
                      });
                    }}
                    variant="ghost"
                    size="sm"
                  >
                    🧪 Test Copy
                  </Button>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <Label className="text-sm font-medium">📋 Prompt Preview (First 300 characters):</Label>
                  <Badge variant="secondary" className="text-xs">
                    {generatedInstructions.length} characters total
                  </Badge>
                </div>
                <div className="bg-white border rounded p-3 text-sm text-gray-700 font-mono">
                  {generatedInstructions.substring(0, 300)}...
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">📝 Next Steps:</h4>
                <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                  <li>Click "Copy Complete Prompt to Clipboard" above</li>
                  <li>Open ChatGPT in a new browser tab</li>
                  <li>Paste the prompt and press Enter</li>
                  <li>ChatGPT will generate your optimized script</li>
                  <li>Review and edit the generated script as needed</li>
                </ol>
              </div>
            </div>
          )}


        </CardContent>
      </Card>
    </div>
  );
};

export default PromptTemplatesModule;
