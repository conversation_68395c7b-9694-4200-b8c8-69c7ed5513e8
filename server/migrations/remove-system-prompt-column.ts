import Database from 'better-sqlite3';

/**
 * Migration to remove the system_prompt column from the openrouter_prompts table
 */
export async function runMigration() {
  console.log('Running migration: Removing system_prompt column from openrouter_prompts table');

  try {
    // Connect to the database
    console.log('Connecting to database...');
    const db = new Database('../data.db');
    console.log('Connected to database');

    // Check if the openrouter_prompts table exists
    console.log('Checking if openrouter_prompts table exists...');
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='openrouter_prompts'").all();
    console.log('Tables found:', tables);

    if (tables.length === 0) {
      console.log('openrouter_prompts table does not exist, nothing to modify');
      return;
    }

    // Check if the system_prompt column exists
    console.log('Checking if system_prompt column exists...');
    const columns = db.prepare("PRAGMA table_info(openrouter_prompts)").all();
    console.log('Columns found:', columns);
    const systemPromptColumn = columns.find(col => col.name === 'system_prompt');

    if (!systemPromptColumn) {
      console.log('system_prompt column does not exist, nothing to remove');
      return;
    }
    console.log('system_prompt column found, proceeding with removal');

    // SQLite doesn't support DROP COLUMN directly, so we need to:
    // 1. Create a new table without the column
    // 2. Copy data from the old table to the new one
    // 3. Drop the old table
    // 4. Rename the new table to the original name

    // Begin transaction
    console.log('Beginning transaction...');
    db.exec('BEGIN TRANSACTION;');

    // Create a new table without the system_prompt column
    console.log('Creating new table without system_prompt column...');
    db.exec(`
      CREATE TABLE openrouter_prompts_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        prompt_text TEXT NOT NULL,
        is_default BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      );
    `);

    // Copy data from the old table to the new one
    console.log('Copying data from old table to new table...');
    db.exec(`
      INSERT INTO openrouter_prompts_new (id, user_id, name, prompt_text, is_default, created_at)
      SELECT id, user_id, name, prompt_text, is_default, created_at
      FROM openrouter_prompts;
    `);

    // Drop the old table
    console.log('Dropping old table...');
    db.exec('DROP TABLE openrouter_prompts;');

    // Rename the new table to the original name
    console.log('Renaming new table to original name...');
    db.exec('ALTER TABLE openrouter_prompts_new RENAME TO openrouter_prompts;');

    // Commit transaction
    console.log('Committing transaction...');
    db.exec('COMMIT;');

    console.log('Successfully removed system_prompt column from openrouter_prompts table');

    // Close the database connection
    console.log('Closing database connection...');
    db.close();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error removing system_prompt column:', error);
    throw error;
  }
}

// Run the migration when this file is executed directly
runMigration().then(() => {
  console.log('Migration completed successfully');
}).catch(error => {
  console.error('Migration failed:', error);
  process.exit(1);
});
