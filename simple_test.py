from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

# Set up Chrome options
chrome_options = Options()
chrome_options.add_argument("--headless")  # Run in headless mode

# Initialize the Chrome driver
print("Initializing Chrome driver...")
driver = webdriver.Chrome(options=chrome_options)

try:
    # Navigate to Google
    print("Navigating to Google...")
    driver.get("https://www.google.com")
    
    # Print the title
    print(f"Page title: {driver.title}")
    
    print("Test completed successfully!")

except Exception as e:
    print(f"An error occurred: {e}")

finally:
    # Close the browser
    print("Closing the browser...")
    driver.quit()
