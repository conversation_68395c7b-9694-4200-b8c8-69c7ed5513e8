import Database from 'better-sqlite3';

/**
 * Migration script to add OpenRouter prompts table
 */
function runMigration() {
  console.log('Running migration: Adding OpenRouter prompts table');
  
  try {
    // Connect to the database
    const db = new Database('data.db');
    
    // Check if the openrouter_prompts table already exists
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='openrouter_prompts'").all();
    
    if (tables.length === 0) {
      // Create the openrouter_prompts table
      db.exec(`
        CREATE TABLE IF NOT EXISTS openrouter_prompts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          prompt_text TEXT NOT NULL,
          system_prompt TEXT,
          is_default BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id)
        );
      `);
      
      console.log('Successfully created openrouter_prompts table');
    } else {
      console.log('openrouter_prompts table already exists');
    }
    
    // Close the database connection
    db.close();
  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
}

// Run the migration
runMigration();
