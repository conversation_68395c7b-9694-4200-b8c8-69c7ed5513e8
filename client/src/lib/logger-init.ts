/**
 * Logger initialization module
 * This is used to initialize the logger and make it available globally
 * to avoid circular dependencies when importing the logger
 */

import { logger, LogLevel } from './logger';

// Initialize the logger
const initLogger = () => {
  // Set the log level based on environment
  if (process.env.NODE_ENV === 'production') {
    logger.info('Logger initialized in production mode');
  } else {
    logger.info('Logger initialized in development mode');
  }
  
  // Make logger available globally for debugging
  try {
    (window as any).__logger = logger;
    (window as any).__LogLevel = LogLevel;
    (window as any).setLogLevel = (level: LogLevel) => {
      logger.info(`Setting log level to ${LogLevel[level]}`);
      import('./logger').then(({ setLogLevel }) => {
        setLogLevel(level);
      });
    };
  } catch (e) {
    // Ignore errors in non-browser environments
  }
};

// Initialize the logger
initLogger();

export default logger;
