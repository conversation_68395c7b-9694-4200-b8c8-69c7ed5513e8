import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { YoutubeVideo } from "@shared/schema";
import { useAnalyzeVideoWithOpenRouter } from "./use-openrouter";

export function useFinancialAnalysis() {
  const { toast } = useToast();

  // Analyze a video for financial benefits using the traditional method
  const analyzeVideo = useMutation({
    mutationFn: async (videoId: string) => {
      try {
        console.log('API request for financial analysis:', videoId);
        console.log('Making request to:', `/api/youtube-channels/videos/${videoId}/analyze-financial`);
        const res = await apiRequest("GET", `/api/youtube-channels/videos/${videoId}/analyze-financial`);

        console.log('Financial analysis API response status:', res.status);
        if (!res.ok) {
          console.error('Financial analysis API returned error status:', res.status);
          const errorText = await res.text();
          console.error('Error response body:', errorText);
          throw new Error(`API returned ${res.status}: ${errorText}`);
        }

        const data = await res.json();
        console.log('Financial analysis API response data:', data);

        return data;
      } catch (error) {
        console.error('Error in financial analysis API call:', error);
        console.error('Error details:', error instanceof Error ? error.message : 'Unknown error');
        throw error;
      }
    },
    onSuccess: (data, videoId) => {
      // Invalidate the video query to update the UI
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${videoId}`] });
      // Invalidate the financial analysis query to update the UI
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${videoId}/financial-analysis`] });
      // Invalidate the all videos query to update the video card in the feed
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/all-videos`] });

      // Show a toast notification
      toast({
        title: "Financial Analysis Complete",
        description: `Analysis completed with score: ${Math.round(data.score)}`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Analysis Failed",
        description: error.message || "Failed to analyze video for financial benefits",
        variant: "destructive",
      });
    },
  });

  // Analyze a video for financial benefits using OpenRouter API
  const analyzeVideoWithOpenRouter = useMutation({
    mutationFn: async (params: string | { videoId: string, model?: string }) => {
      try {
        // Handle both string and object parameters
        const videoId = typeof params === 'string' ? params : params.videoId;
        const model = typeof params === 'object' ? params.model : undefined;

        console.log('API request for OpenRouter financial analysis:', videoId, model ? `with model ${model}` : '');

        // Build the URL with optional model parameter
        let url = `/api/openrouter/analyze-youtube-video/${videoId}`;
        if (model) {
          url += `?model=${encodeURIComponent(model)}`;
        }

        console.log('Making request to:', url);
        const res = await apiRequest("GET", url);

        console.log('OpenRouter financial analysis API response status:', res.status);
        if (!res.ok) {
          console.error('OpenRouter financial analysis API returned error status:', res.status);
          let errorText = '';
          try {
            const errorData = await res.json();
            errorText = errorData.message || JSON.stringify(errorData);
          } catch (e) {
            errorText = await res.text();
          }
          console.error('Error response body:', errorText);
          throw new Error(`API returned ${res.status}: ${errorText}`);
        }

        const data = await res.json();
        console.log('OpenRouter financial analysis API response data:', data);

        return data;
      } catch (error) {
        console.error('Error in OpenRouter financial analysis API call:', error);
        console.error('Error details:', error instanceof Error ? error.message : 'Unknown error');
        throw error;
      }
    },
    onSuccess: (data, videoId) => {
      // Invalidate the video query to update the UI
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${videoId}`] });
      // Invalidate the financial analysis query to update the UI
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${videoId}/financial-analysis`] });
      // Invalidate the all videos query to update the video card in the feed
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/all-videos`] });

      // Log the successful analysis
      console.log('OpenRouter analysis completed successfully for video:', videoId);
      console.log('Analysis data:', data);

      // We'll handle the toast notification in the component to avoid duplicate toasts
    },
    onError: (error: Error) => {
      console.error('OpenRouter analysis mutation error:', error);
      // We'll handle the toast notification in the component to avoid duplicate toasts
    },
  });

  // Get available OpenRouter models
  const getOpenRouterModels = useQuery({
    queryKey: ['openrouter-models'],
    queryFn: async () => {
      try {
        const res = await apiRequest("GET", `/api/openrouter/models`);
        if (!res.ok) {
          throw new Error(`API returned ${res.status}`);
        }
        const data = await res.json();
        return data as {name: string}[];
      } catch (error) {
        console.error('Error fetching OpenRouter models:', error);
        return [];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  return {
    analyzeVideo,
    analyzeVideoWithOpenRouter,
    getOpenRouterModels,
  };
}

// Helper function to get a badge variant based on financial category
export function getFinancialCategoryBadge(category?: string) {
  switch (category) {
    case 'urgent':
      return {
        variant: "success" as const,
        label: "Urgent/High Benefit",
        icon: "💰",
      };
    case 'anticipated':
      return {
        variant: "warning" as const,
        label: "Anticipated Benefit",
        icon: "🕒",
      };
    case 'doubtful':
      return {
        variant: "destructive" as const,
        label: "Doubtful Claim",
        icon: "⚠️",
      };
    default:
      return {
        variant: "outline" as const,
        label: "No Analysis",
        icon: "❓",
      };
  }
}

// Helper function to format financial score
export function formatFinancialScore(score?: number) {
  if (score === undefined) return "N/A";
  return `${Math.round(score)}`;
}

// Helper function to get a color based on financial score
export function getFinancialScoreColor(score?: number) {
  if (score === undefined) return "text-muted-foreground";

  if (score >= 80) return "text-green-500";
  if (score >= 50) return "text-amber-500";
  if (score >= 20) return "text-red-500";
  return "text-muted-foreground";
}

// Helper function to get a color based on views per hour
export function getViewsPerHourColor(viewsPerHour: number) {
  // Define thresholds for color transitions
  // These can be adjusted based on what's considered low/high for your channel
  if (viewsPerHour >= 5000) return "text-green-500"; // Very high VPH
  if (viewsPerHour >= 1000) return "text-green-400";
  if (viewsPerHour >= 500) return "text-green-300";
  if (viewsPerHour >= 250) return "text-lime-500";
  if (viewsPerHour >= 100) return "text-yellow-500";
  if (viewsPerHour >= 50) return "text-amber-500";
  if (viewsPerHour >= 25) return "text-orange-500";
  if (viewsPerHour >= 10) return "text-red-400";
  return "text-red-500"; // Very low VPH
}
