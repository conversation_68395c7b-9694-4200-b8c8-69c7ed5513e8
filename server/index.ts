import 'dotenv/config';
import express, { type Request, Response, NextFunction } from "express";
import passport from "passport";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import session from "express-session";
import { storage } from "./storage";
import { promises as fs } from 'fs';
import path from 'path';
import { exec } from 'child_process';
// Import the dependency checker
import { checkAndInstallDependencies } from './utils/dependency-checker';
// Import worker manager and request queue manager
import { startWorkerManager } from './services/task-queue';
import { startRequestQueueManager } from './services/request-handlers';
// Import X.com scraping utilities
import { scrapeXcom } from './routes/rss-feeds';

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Add a test endpoint for X.com scraping (no auth required)
app.get('/test-xcom-scraping', async (req, res) => {
  try {
    const { url } = req.query;

    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    const urlString = url.toString();
    console.log('Testing X.com scraping for URL:', urlString);

    // Check if this is an X.com URL
    if (!urlString.includes('twitter.com') && !urlString.includes('x.com')) {
      return res.status(400).json({ error: 'Not an X.com URL' });
    }

    // Try to scrape the content
    const content = await scrapeXcom(urlString);

    if (content) {
      console.log('Successfully scraped X.com content');
      return res.json({ content });
    } else {
      console.log('Failed to scrape X.com content');
      return res.status(500).json({ error: 'Failed to scrape X.com content' });
    }
  } catch (error) {
    console.error('Error testing X.com scraping:', error);
    return res.status(500).json({ error: 'Error testing X.com scraping' });
  }
});

app.set("trust proxy", 1);
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: false,
  store: storage.sessionStore,
  cookie: {
    secure: false,
    httpOnly: true,
    maxAge: 30 * 24 * 60 * 60 * 1000,
    sameSite: 'lax'
  }
}));
app.use(passport.initialize());
app.use(passport.session());
app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

// Run database migrations
async function runMigrations() {
  try {
    log('Running database migrations...');

    // Run the migration to add the OpenRouter prompts table
    await import('./migrations/add-openrouter-prompts-table.ts');

    // Run the migration to remove the system_prompt column
    const { runMigration } = await import('./migrations/remove-system-prompt-column.ts');
    await runMigration();

    // Run the migration to remove the verbose_ai_logging column
    await import('./migrations/remove-verbose-ai-logging-column.ts');

    // Run the migration to add the rate_limit_type column
    await import('./migrations/add-rate-limit-type-column.ts');

    // Run the migration to fix the auto_analyze_on_refresh column
    const { runMigration: fixAutoAnalyzeColumn } = await import('./migrations/fix-auto-analyze-column.ts');
    await fixAutoAnalyzeColumn();

    log('Database migrations completed successfully');
  } catch (error) {
    console.error('Error running migrations:', error);
  }
}

(async () => {
  // Check and install dependencies if needed (in non-blocking mode)
  // This will run in the background and not block server startup
  checkAndInstallDependencies(true).catch(err => {
    console.error('Error starting dependency check:', err);
  });

  // Start the worker manager for background tasks
  startWorkerManager();

  // Start the request queue manager for API request prioritization
  startRequestQueueManager();

  // Run migrations before starting the server
  await runMigrations();

  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Get admin settings for server configuration
  try {
    // Get admin settings
    const adminSettings = await storage.getAdminSettings();

    // Prioritize admin settings port over environment variable
    // Only use PORT environment variable if explicitly running with it
    const port = adminSettings.serverPort;
    const host = adminSettings.localhostOnly ? 'localhost' : '0.0.0.0';

    // Start the server
    server.listen({
      port,
      host
    }, () => {
      log(`serving on ${host}:${port}`);
      log(`Network access: ${adminSettings.localhostOnly ? 'Disabled (localhost only)' : 'Enabled (accessible on local network)'}`);
    });
  } catch (error) {
    console.error('Error starting server with admin settings:', error);
    // Fallback to default settings
    const port = 5001;
    server.listen({
      port,
      host: '0.0.0.0'
    }, () => {
      log(`serving on port ${port} (using default settings)`);
    });
  }
})();
