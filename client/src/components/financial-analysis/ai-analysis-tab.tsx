import { YoutubeVideo } from "@shared/schema";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Activity, Code, RefreshCw } from "lucide-react";
import { useFinancialAnalysis } from "@/hooks/use-financial-analysis";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSettings } from "@/hooks/use-settings";

// Helper function for consistent logging with timestamps
const logWithTimestamp = (message: string, data?: any) => {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0]; // HH:MM:SS format
  console.log(`[${timestamp}] 🤖 AIAnalysisTab: ${message}`, data ? data : '');
};

interface AIAnalysisTabProps {
  video: YoutubeVideo;
  onAnalysisComplete?: () => void;
}

export function AIAnalysisTab({ video, onAnalysisComplete }: AIAnalysisTabProps) {
  const { analyzeVideoWithOpenRouter } = useFinancialAnalysis();
  const { settings, updateSettings } = useSettings();
  const { toast } = useToast();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentVideo, setCurrentVideo] = useState<YoutubeVideo>(video);
  const [rawData, setRawData] = useState<string>("");
  const [rawInput, setRawInput] = useState<{prompt: string, systemPrompt: string}>({prompt: "", systemPrompt: ""});
  const [analysisError, setAnalysisError] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("formatted");
  const [selectedModel, setSelectedModel] = useState<string>("");

  // Log component mount/unmount
  useEffect(() => {
    console.log('==== AI ANALYSIS TAB LOGGING ====');
    console.log(`AI Analysis Tab OPENED for video ${video.id}`);
    logWithTimestamp(`AI Analysis Tab OPENED for video ${video.id}`, {
      videoId: video.id,
      title: video.title,
      hasTranscription: Boolean(video.transcription),
      transcriptionLength: video.transcription ? video.transcription.length : 0,
      hasOpenRouterRawData: Boolean(video.openRouterRawData),
      financialScore: video.financialScore,
      openRouterModelUsed: video.openRouterModelUsed,
      openRouterPromptName: video.openRouterPromptName,
      openRouterBenefitAmounts: video.openRouterBenefitAmounts ?
        (Array.isArray(video.openRouterBenefitAmounts) ?
          JSON.stringify(video.openRouterBenefitAmounts) : video.openRouterBenefitAmounts) : 'null',
      dataSource: 'component_props'
    });

    // Log initial tab state
    console.log(`Initial active tab: ${activeTab}`);

    return () => {
      console.log(`AI Analysis Tab CLOSED for video ${video.id}`);
      logWithTimestamp(`AI Analysis Tab CLOSED for video ${video.id}`, {
        videoId: video.id
      });
    };
  }, []);

  // Available OpenRouter models (hardcoded like in settings page)
  const availableModels = [
    { name: 'google/gemini-2.0-flash-exp:free' },
    { name: 'anthropic/claude-3-5-sonnet:beta' },
    { name: 'anthropic/claude-3-opus:beta' },
    { name: 'anthropic/claude-3-haiku:beta' },
    { name: 'meta-llama/llama-3-70b-instruct:nitro' }
  ];

  // Update the current video when the prop changes
  useEffect(() => {
    logWithTimestamp(`Video prop changed in AI Analysis Tab for video ${video.id}`, {
      videoId: video.id,
      title: video.title,
      previousVideoId: currentVideo.id,
      hasTranscription: Boolean(video.transcription),
      hasOpenRouterRawData: Boolean(video.openRouterRawData),
      financialScore: video.financialScore,
      dataSource: 'prop_update'
    });
    setCurrentVideo(video);
  }, [video]);

  // Check if the video has an AI analysis - more comprehensive check
  const hasAiAnalysis = Boolean(
    // Check for OpenRouter fields
    currentVideo.openRouterModelUsed ||
    currentVideo.openRouterPriorityTag ||
    currentVideo.openRouterRawData ||
    // Check for benefit amounts
    (currentVideo.openRouterBenefitAmounts &&
     (Array.isArray(currentVideo.openRouterBenefitAmounts) ?
      currentVideo.openRouterBenefitAmounts.length > 0 :
      Boolean(currentVideo.openRouterBenefitAmounts))) ||
    // Check for other OpenRouter fields
    currentVideo.openRouterExpectedArrivalDate ||
    currentVideo.openRouterEligiblePeople ||
    currentVideo.openRouterProofOrSource ||
    currentVideo.openRouterActionsToClaim ||
    currentVideo.openRouterReasonForPriority ||
    // General financial analysis indicators
    currentVideo.financialScore ||
    currentVideo.financialCategory ||
    currentVideo.financialAmount ||
    currentVideo.financialAnalysis
  );

  // Log the analysis detection for debugging
  useEffect(() => {
    logWithTimestamp(`Checking for analysis data for video ${currentVideo.id}`, {
      id: currentVideo.id,
      title: currentVideo.title,
      // OpenRouter fields
      openRouterModelUsed: Boolean(currentVideo.openRouterModelUsed),
      openRouterModelName: currentVideo.openRouterModelUsed,
      openRouterPriorityTag: Boolean(currentVideo.openRouterPriorityTag),
      openRouterRawData: Boolean(currentVideo.openRouterRawData),
      openRouterRawDataSize: currentVideo.openRouterRawData ? currentVideo.openRouterRawData.length : 0,
      openRouterBenefitAmounts: currentVideo.openRouterBenefitAmounts,
      openRouterBenefitAmountsType: typeof currentVideo.openRouterBenefitAmounts,
      isOpenRouterBenefitAmountsArray: Array.isArray(currentVideo.openRouterBenefitAmounts),
      openRouterBenefitAmountsLength: Array.isArray(currentVideo.openRouterBenefitAmounts) ?
        currentVideo.openRouterBenefitAmounts.length : 'not an array',
      // Other OpenRouter fields
      hasOpenRouterExpectedArrivalDate: Boolean(currentVideo.openRouterExpectedArrivalDate),
      hasOpenRouterEligiblePeople: Boolean(currentVideo.openRouterEligiblePeople),
      hasOpenRouterProofOrSource: Boolean(currentVideo.openRouterProofOrSource),
      hasOpenRouterActionsToClaim: Boolean(currentVideo.openRouterActionsToClaim),
      hasOpenRouterReasonForPriority: Boolean(currentVideo.openRouterReasonForPriority),
      // General financial indicators
      financialScore: currentVideo.financialScore,
      hasFinancialCategory: Boolean(currentVideo.financialCategory),
      hasFinancialAmount: Boolean(currentVideo.financialAmount),
      hasFinancialAnalysis: Boolean(currentVideo.financialAnalysis),
      // Overall analysis status
      hasAiAnalysis: hasAiAnalysis
    });

    // If we have analysis data but no raw data, try to fetch it
    if (hasAiAnalysis && !rawData) {
      console.log(`Video ${currentVideo.id} has analysis data but no raw data in state, fetching raw data`);
      fetchRawData();
    }
  }, [currentVideo.id, hasAiAnalysis, currentVideo.openRouterModelUsed,
      currentVideo.openRouterPriorityTag, currentVideo.openRouterRawData,
      currentVideo.openRouterBenefitAmounts, currentVideo.financialScore,
      currentVideo.financialCategory, currentVideo.financialAmount,
      currentVideo.financialAnalysis, rawData]);

  // Define fetchRawData function
  const fetchRawData = async () => {
    try {
      console.log(`==== FETCHING RAW DATA for video ${currentVideo.id} ====`);
      console.log('Current video state:', {
        id: currentVideo.id,
        title: currentVideo.title,
        financialScore: currentVideo.financialScore,
        openRouterBenefitAmounts: currentVideo.openRouterBenefitAmounts,
        hasOpenRouterRawData: Boolean(currentVideo.openRouterRawData),
        openRouterRawDataLength: currentVideo.openRouterRawData ? currentVideo.openRouterRawData.length : 0
      });

      // If we already have raw data in the currentVideo, use it directly
      if (currentVideo.openRouterRawData) {
        console.log(`Using existing openRouterRawData for video ${currentVideo.id}`);
        console.log('Raw data length:', currentVideo.openRouterRawData.length);
        console.log('Raw data preview:', currentVideo.openRouterRawData.substring(0, 100));

        try {
          // Try to parse the raw data
          const parsedData = JSON.parse(currentVideo.openRouterRawData);
          console.log('Successfully parsed existing openRouterRawData');

          // Set the raw data state
          setRawData(currentVideo.openRouterRawData);

          // Extract prompt and system prompt if available
          if (parsedData.prompt) {
            setRawInput({
              prompt: parsedData.prompt,
              systemPrompt: parsedData.systemPrompt || ''
            });
          }

          // No need to fetch from API, we already have the data
          return;
        } catch (parseError) {
          console.error('Error parsing existing openRouterRawData:', parseError);
          // Continue with API fetch if parsing fails
        }
      }

      logWithTimestamp(`Fetching raw analysis data for video ${currentVideo.id}`, {
        videoId: currentVideo.id,
        title: currentVideo.title,
        hasOpenRouterRawData: Boolean(currentVideo.openRouterRawData),
        endpoint: `/api/videos/${currentVideo.id}/raw-analysis`
      });

      const response = await apiRequest("GET", `/api/videos/${currentVideo.id}/raw-analysis`);

      // Check if the response is OK
      if (response.ok) {
        // Get the raw response text first
        const rawResponseText = await response.text();
        console.log(`Raw response text length: ${rawResponseText.length}`);
        console.log(`Raw response text preview: ${rawResponseText.substring(0, 100)}...`);

        // Try to parse the response as JSON
        try {
          const data = JSON.parse(rawResponseText);
          console.log(`==== RAW DATA RECEIVED for video ${currentVideo.id} ====`);
          console.log('Raw data received:', {
            modelUsed: data.modelUsed,
            promptName: data.promptName,
            score: data.score,
            priorityTag: data.priorityTag,
            extractedInfo: data.extractedInfo,
            benefitAmounts: data.extractedInfo?.benefitAmounts,
            hasBenefitAmounts: data.extractedInfo?.benefitAmounts ? data.extractedInfo.benefitAmounts.length > 0 : false
          });

          logWithTimestamp(`Raw data fetched successfully for video ${currentVideo.id}`, {
            videoId: currentVideo.id,
            modelUsed: data.modelUsed,
            promptName: data.promptName,
            score: data.score,
            priorityTag: data.priorityTag,
            hasBenefitAmounts: data.extractedInfo?.benefitAmounts ? data.extractedInfo.benefitAmounts.length > 0 : false,
            benefitAmountsData: data.extractedInfo?.benefitAmounts ? JSON.stringify(data.extractedInfo.benefitAmounts) : 'null',
            dataSize: JSON.stringify(data).length
          });

          // Ensure extractedInfo exists
          if (!data.extractedInfo) {
            data.extractedInfo = {
              benefitAmounts: [],
              expectedArrivalDate: '',
              eligiblePeople: '',
              proofOrSource: '',
              actionsToClaim: ''
            };
          }

          // Check for benefit amounts in the raw data
          if (data.extractedInfo.benefitAmounts) {
            console.log(`==== BENEFIT AMOUNTS FOUND in raw data for video ${currentVideo.id} ====`);
            console.log('Benefit amounts:', {
              benefitAmounts: data.extractedInfo.benefitAmounts,
              benefitAmountsType: typeof data.extractedInfo.benefitAmounts,
              isArray: Array.isArray(data.extractedInfo.benefitAmounts),
              length: Array.isArray(data.extractedInfo.benefitAmounts) ? data.extractedInfo.benefitAmounts.length : 'not an array'
            });

            logWithTimestamp(`Benefit amounts found in raw data for video ${currentVideo.id}`, {
              videoId: currentVideo.id,
              benefitAmounts: data.extractedInfo.benefitAmounts,
              benefitAmountsType: typeof data.extractedInfo.benefitAmounts,
              isArray: Array.isArray(data.extractedInfo.benefitAmounts),
              length: Array.isArray(data.extractedInfo.benefitAmounts) ? data.extractedInfo.benefitAmounts.length : 'not an array'
            });

            // Ensure benefit amounts is an array
            if (!Array.isArray(data.extractedInfo.benefitAmounts)) {
              try {
                if (typeof data.extractedInfo.benefitAmounts === 'string') {
                  data.extractedInfo.benefitAmounts = JSON.parse(data.extractedInfo.benefitAmounts);
                  logWithTimestamp(`Parsed benefit amounts from string for video ${currentVideo.id}`, {
                    videoId: currentVideo.id,
                    parsedBenefitAmounts: data.extractedInfo.benefitAmounts
                  });
                }
              } catch (parseError) {
                console.error('AIAnalysisTab: Error parsing benefit amounts:', parseError);
                data.extractedInfo.benefitAmounts = [];
              }
            }
          } else if (data.benefitDescription) {
            // Try to extract benefit amounts from the benefit description
            console.log(`==== TRYING TO EXTRACT BENEFIT AMOUNTS from description for video ${currentVideo.id} ====`);
            console.log('Benefit description:', data.benefitDescription);

            const matches = data.benefitDescription.match(/\$\d+(?:,\d+)*(?:\.\d+)?/g);
            if (matches && matches.length > 0) {
              console.log(`Extracted benefit amounts from description:`, matches);

              logWithTimestamp(`Extracted benefit amounts from description for video ${currentVideo.id}`, {
                videoId: currentVideo.id,
                extractedBenefitAmounts: matches,
                benefitDescription: data.benefitDescription.substring(0, 100) + '...'
              });
              data.extractedInfo.benefitAmounts = matches;
            } else {
              console.log(`No benefit amounts found in description for video ${currentVideo.id}`);
            }
          }

          // Extract the prompt and system prompt
          if (data.prompt) {
            setRawInput({
              prompt: data.prompt,
              systemPrompt: data.systemPrompt || ''
            });
          }

          // Store the raw response text directly
          console.log(`==== SETTING RAW DATA for video ${currentVideo.id} ====`);
          console.log('Setting raw response text, length:', rawResponseText.length);
          setRawData(rawResponseText);

          // Log the raw data after processing
          logWithTimestamp(`Processed raw data for video ${currentVideo.id}`, {
            videoId: currentVideo.id,
            processedDataSize: rawResponseText.length,
            hasPrompt: Boolean(data.prompt),
            hasSystemPrompt: Boolean(data.systemPrompt),
            promptName: data.promptName || 'Unknown'
          });

          // Update the current video with benefit amounts if they were found
          if (data.extractedInfo && data.extractedInfo.benefitAmounts &&
              Array.isArray(data.extractedInfo.benefitAmounts) &&
              data.extractedInfo.benefitAmounts.length > 0) {
            logWithTimestamp(`Updating current video with benefit amounts for video ${currentVideo.id}`, {
              videoId: currentVideo.id,
              benefitAmounts: data.extractedInfo.benefitAmounts,
              benefitAmountsCount: data.extractedInfo.benefitAmounts.length
            });

            // Update the current video with all the data from the API response
            // This ensures we have the most up-to-date information
            setCurrentVideo(prev => {
              // Create a new object with all the previous properties
              const updatedVideo = { ...prev };

              // Update financial analysis fields if they exist in the data
              if (data.score !== undefined) updatedVideo.financialScore = data.score;
              if (data.priorityTag) updatedVideo.openRouterPriorityTag = data.priorityTag;
              if (data.modelUsed) updatedVideo.openRouterModelUsed = data.modelUsed;
              if (data.promptName) updatedVideo.openRouterPromptName = data.promptName;
              if (data.benefitDescription) updatedVideo.financialAmount = data.benefitDescription;

              // Update benefit amounts
              updatedVideo.openRouterBenefitAmounts = data.extractedInfo.benefitAmounts;

              // Update other extracted info if available
              if (data.extractedInfo.expectedArrivalDate) {
                updatedVideo.openRouterExpectedArrivalDate = data.extractedInfo.expectedArrivalDate;
              }
              if (data.extractedInfo.eligiblePeople) {
                updatedVideo.openRouterEligiblePeople = data.extractedInfo.eligiblePeople;
              }
              if (data.extractedInfo.proofOrSource) {
                updatedVideo.openRouterProofOrSource = data.extractedInfo.proofOrSource;
              }
              if (data.extractedInfo.actionsToClaim) {
                updatedVideo.openRouterActionsToClaim = data.extractedInfo.actionsToClaim;
              }

              // Store the raw response text in the video object
              updatedVideo.openRouterRawData = rawResponseText;

              return updatedVideo;
            });

            // If the video doesn't have a financial score but the data does, update hasAiAnalysis
            if (!currentVideo.financialScore && data.score) {
              console.log(`Video ${currentVideo.id} has analysis data from API but not in current state, updating UI`);
            }
          } else {
            logWithTimestamp(`No valid benefit amounts found to update for video ${currentVideo.id}`, {
              videoId: currentVideo.id,
              extractedInfo: data.extractedInfo
            });

            // Still update the video with the raw data
            setCurrentVideo(prev => ({
              ...prev,
              openRouterRawData: rawResponseText
            }));
          }
        } catch (parseError) {
          console.error('AIAnalysisTab: Error parsing raw data:', parseError);
          // Still set the raw response text even if parsing fails
          setRawData(rawResponseText);

          // Update the video with the raw response text
          setCurrentVideo(prev => ({
            ...prev,
            openRouterRawData: rawResponseText
          }));
        }
      } else {
        // Handle non-OK responses
        console.error('Failed to fetch raw analysis data, status:', response.status);
        setRawData(`Failed to fetch raw analysis data: ${response.status}`);
      }
    } catch (error) {
      console.error('AIAnalysisTab: Error fetching raw data:', error);
      setRawData("Error fetching raw data");
    }
  };

  // Fetch raw data when the component mounts or when the video changes
  useEffect(() => {
    console.log(`==== COMPONENT EFFECT TRIGGERED for video ${currentVideo.id} ====`);
    console.log('Current video state in effect:', {
      id: currentVideo.id,
      title: currentVideo.title,
      hasAiAnalysis: hasAiAnalysis,
      financialScore: currentVideo.financialScore,
      openRouterBenefitAmounts: currentVideo.openRouterBenefitAmounts,
      hasOpenRouterRawData: Boolean(currentVideo.openRouterRawData)
    });

    // Always try to fetch raw data, even if hasAiAnalysis is false
    // This helps in cases where the video was analyzed in batch but the UI doesn't show it yet
    console.log(`Fetching raw data for: ${currentVideo.id}`);
    logWithTimestamp(`DATA RETRIEVAL: Fetching raw data for: ${currentVideo.id}`, {
      videoId: currentVideo.id,
      title: currentVideo.title,
      hasOpenRouterRawData: Boolean(currentVideo.openRouterRawData),
      financialScore: currentVideo.financialScore,
      openRouterModelUsed: currentVideo.openRouterModelUsed,
      openRouterBenefitAmounts: currentVideo.openRouterBenefitAmounts ?
        (Array.isArray(currentVideo.openRouterBenefitAmounts) ?
          JSON.stringify(currentVideo.openRouterBenefitAmounts) : currentVideo.openRouterBenefitAmounts) : 'null',
      endpoint: `/api/videos/${currentVideo.id}/raw-analysis`,
      dataSource: 'raw_analysis_api'
    });
    fetchRawData();

    // Also refresh the video data from the API to ensure we have the latest data
    // This is especially important for videos that were batch analyzed
    apiRequest("GET", `/api/youtube-channels/videos/${currentVideo.id}`)
      .then(async (res) => {
        if (res.ok) {
          const updatedVideo = await res.json();
          logWithTimestamp(`DATA RETRIEVAL: Received refreshed video data for ${currentVideo.id}`, {
            videoId: currentVideo.id,
            hasOpenRouterRawData: Boolean(updatedVideo.openRouterRawData),
            financialScore: updatedVideo.financialScore,
            openRouterModelUsed: updatedVideo.openRouterModelUsed,
            openRouterBenefitAmounts: updatedVideo.openRouterBenefitAmounts,
            dataSource: 'video_api_response'
          });
          setCurrentVideo(updatedVideo);
        }
      })
      .catch(err => {
        logWithTimestamp(`ERROR: Failed to refresh video data for ${currentVideo.id}`, {
          videoId: currentVideo.id,
          error: err.message,
          endpoint: `/api/youtube-channels/videos/${currentVideo.id}`
        });
        console.error('Error refreshing video data:', err);
      });
  }, [currentVideo.id]);

  // Initialize selected model from settings or video
  useEffect(() => {
    let modelSource = 'default';
    let modelValue = 'google/gemini-2.0-flash-exp:free';

    if (settings?.openRouterModel) {
      modelSource = 'user_settings';
      modelValue = settings.openRouterModel;
      setSelectedModel(settings.openRouterModel);
    } else if (currentVideo.openRouterModelUsed) {
      modelSource = 'video_data';
      modelValue = currentVideo.openRouterModelUsed;
      setSelectedModel(currentVideo.openRouterModelUsed);
    } else {
      setSelectedModel('google/gemini-2.0-flash-exp:free'); // Default model
    }

    logWithTimestamp(`MODEL SELECTION: Initialized model for video ${currentVideo.id}`, {
      videoId: currentVideo.id,
      modelSource,
      modelValue,
      userSettingsModel: settings?.openRouterModel,
      videoModel: currentVideo.openRouterModelUsed,
      selectedPromptId: settings?.selectedPromptId
    });
  }, [settings, currentVideo]);



  // Parse raw data if available
  let parsedRawData = null;
  try {
    if (rawData) {
      parsedRawData = typeof rawData === 'string' ? JSON.parse(rawData) : rawData;
      console.log('AIAnalysisTab: Parsed raw data:', parsedRawData);
      console.log('AIAnalysisTab: Prompt name in raw data:', parsedRawData?.promptName);

      // Ensure we have the expected format
      if (!parsedRawData.extractedInfo && parsedRawData.benefitInfo) {
        // Convert legacy format to current format if needed
        parsedRawData = {
          ...parsedRawData,
          extractedInfo: {
            benefitAmounts: parsedRawData.benefitInfo?.benefitAmounts || [],
            expectedArrivalDate: parsedRawData.benefitInfo?.expectedArrivalDate || '',
            eligiblePeople: parsedRawData.benefitInfo?.eligiblePeople || '',
            proofOrSource: parsedRawData.benefitInfo?.proofOrSource || '',
            actionsToClaim: parsedRawData.benefitInfo?.actionsToClaim || ''
          }
        };
      }

      // Ensure extractedInfo exists
      if (!parsedRawData.extractedInfo) {
        parsedRawData.extractedInfo = {
          benefitAmounts: [],
          expectedArrivalDate: '',
          eligiblePeople: '',
          proofOrSource: '',
          actionsToClaim: ''
        };
      }

      // If no benefit amounts in extractedInfo, try to extract from benefitDescription
      if (!parsedRawData.extractedInfo.benefitAmounts || parsedRawData.extractedInfo.benefitAmounts.length === 0) {
        if (parsedRawData.benefitDescription) {
          const matches = parsedRawData.benefitDescription.match(/\$\d+(?:,\d+)*(?:\.\d+)?/g);
          if (matches && matches.length > 0) {
            console.log('AIAnalysisTab: Extracted benefit amounts from description:', matches);
            parsedRawData.extractedInfo.benefitAmounts = matches;
          }
        }
      }

      // Ensure promptName is preserved after format conversion
      if (!parsedRawData.promptName && parsedRawData._promptName) {
        parsedRawData.promptName = parsedRawData._promptName;
      }
    }
  } catch (error) {
    console.error('Error parsing raw data:', error);
  }



  // Handle analyze with OpenRouter
  const handleAnalyzeWithOpenRouter = async () => {
    try {
      // Check if analysis already exists and if the same model is selected
      const hasExistingAnalysis = Boolean(currentVideo.openRouterModelUsed);
      const isSameModel = currentVideo.openRouterModelUsed === selectedModel;

      if (hasExistingAnalysis && isSameModel) {
        // Ask for confirmation before reanalyzing with the same model
        if (!window.confirm(`This video already has an analysis using the model ${currentVideo.openRouterModelUsed}. Are you sure you want to reanalyze it with the same model?`)) {
          logWithTimestamp(`MANUAL REANALYSIS: Cancelled reanalysis for video ${currentVideo.id} - already has analysis with same model`, {
            videoId: currentVideo.id,
            existingModel: currentVideo.openRouterModelUsed,
            selectedModel: selectedModel
          });
          return;
        }

        logWithTimestamp(`MANUAL REANALYSIS: User confirmed reanalysis for video ${currentVideo.id} despite existing analysis`, {
          videoId: currentVideo.id,
          existingModel: currentVideo.openRouterModelUsed,
          selectedModel: selectedModel
        });
      }

      setIsAnalyzing(true);
      setAnalysisError("");

      // Use the selected model if available, otherwise use the default
      const modelToUse = selectedModel || settings?.openRouterModel || 'google/gemini-2.0-flash-exp:free';

      // Get the selected prompt ID from settings
      const selectedPromptId = settings?.selectedPromptId;

      logWithTimestamp(`MANUAL REANALYSIS: Starting analysis for video ${currentVideo.id}`, {
        videoId: currentVideo.id,
        title: currentVideo.title,
        modelToUse,
        selectedPromptId,
        source: 'manual_reanalysis_button',
        hasTranscription: Boolean(currentVideo.transcription),
        transcriptionLength: currentVideo.transcription ? currentVideo.transcription.length : 0,
        hasExistingAnalysis: hasExistingAnalysis,
        existingModel: currentVideo.openRouterModelUsed
      });

      // Call the API to analyze the video
      const response = await apiRequest(
        "GET",
        `/api/openrouter/analyze-youtube-video/${currentVideo.id}?model=${encodeURIComponent(modelToUse)}`
      );

      if (!response.ok) {
        const errorData = await response.json();

        // Check if this is a rate limit error
        if (errorData.message && (errorData.message.includes('rate limit exceeded') || errorData.message.includes('Rate limit exceeded'))) {
          logWithTimestamp(`MANUAL REANALYSIS: Rate limit error for video ${currentVideo.id}`, {
            videoId: currentVideo.id,
            error: errorData.message,
            errorType: 'rate_limit'
          });

          // Extract wait time if available
          let waitTimeMessage = "";
          const waitTimeMatch = errorData.message.match(/(\d+)\s*seconds/);
          if (waitTimeMatch && waitTimeMatch[1]) {
            const seconds = parseInt(waitTimeMatch[1], 10);
            const minutes = Math.ceil(seconds / 60);
            waitTimeMessage = ` (Wait time: ${minutes} minutes)`;
          }

          // Determine the type of rate limit
          let rateLimitType = "";
          if (errorData.message.includes('free-models-per-day')) {
            rateLimitType = "daily free model limit";
          } else if (errorData.message.includes('per-minute')) {
            rateLimitType = "per-minute limit";
          } else if (waitTimeMatch && parseInt(waitTimeMatch[1], 10) > 3600) {
            rateLimitType = "daily limit";
          }

          // Show a specific toast for rate limit errors
          toast({
            title: "API Rate Limit Exceeded",
            description: `The OpenRouter API ${rateLimitType || "rate limit"} has been reached${waitTimeMessage}. The system will automatically try another API key. Please try again in a moment.`,
            variant: "warning",
          });

          throw new Error(`OpenRouter API ${rateLimitType || "rate limit"} exceeded${waitTimeMessage}. Please try again in a moment.`);
        }

        throw new Error(errorData.message || 'Failed to analyze video');
      }

      const result = await response.json();
      logWithTimestamp(`MANUAL REANALYSIS: Received analysis result for video ${currentVideo.id}`, {
        videoId: currentVideo.id,
        score: result.analysis?.score,
        priorityTag: result.analysis?.priorityTag,
        modelUsed: result.analysis?.modelUsed || modelToUse,
        promptName: result.analysis?.promptName,
        hasBenefitAmounts: result.analysis?.extractedInfo?.benefitAmounts ?
          result.analysis.extractedInfo.benefitAmounts.length > 0 : false,
        rawDataSize: result.rawData ? result.rawData.length : 0
      });

      // Update the raw data state
      setRawData(result.rawData || "");

      // Get the raw input (prompt) for debugging
      try {
        const promptResponse = await apiRequest("GET", `/api/openrouter/debug-prompt/${currentVideo.id}`);
        if (promptResponse.ok) {
          const promptData = await promptResponse.json();
          setRawInput({
            prompt: promptData.prompt,
            systemPrompt: promptData.systemPrompt
          });
        }
      } catch (promptError) {
        console.error('Error fetching prompt:', promptError);
      }

      // Update the current video state with the new data
      const updatedVideo = result.video;
      setCurrentVideo(updatedVideo);

      // Log the updated state to verify
      logWithTimestamp(`MANUAL REANALYSIS: Updated currentVideo state with OpenRouter data for video ${currentVideo.id}`, {
        videoId: currentVideo.id,
        updatedScore: updatedVideo.financialScore,
        updatedPriorityTag: updatedVideo.openRouterPriorityTag,
        updatedModelUsed: updatedVideo.openRouterModelUsed,
        updatedPromptName: updatedVideo.openRouterPromptName,
        updatedBenefitAmounts: updatedVideo.openRouterBenefitAmounts,
        storedFields: [
          'financialScore', 'financialCategory', 'financialAmount', 'financialTimeline',
          'financialRecipients', 'financialSteps', 'openRouterRawData', 'openRouterBenefitAmounts',
          'openRouterExpectedArrivalDate', 'openRouterEligiblePeople', 'openRouterProofOrSource',
          'openRouterActionsToClaim', 'openRouterPriorityTag', 'openRouterReasonForPriority',
          'openRouterViralPotential', 'openRouterModelUsed', 'openRouterPrompt',
          'openRouterSystemPrompt', 'openRouterPromptName'
        ]
      });

      // Invalidate queries to ensure the UI is updated
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${currentVideo.id}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/videos/${currentVideo.id}/financial-analysis`] });
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/all-videos`] });

      // Show a toast notification
      toast({
        title: "AI Analysis Complete",
        description: `Analysis completed with score: ${result.analysis?.score || updatedVideo.financialScore || 'N/A'}/100`,
      });

      // Call the onAnalysisComplete callback if provided
      if (onAnalysisComplete) {
        logWithTimestamp(`MANUAL REANALYSIS: Calling onAnalysisComplete callback for video ${currentVideo.id}`);
        onAnalysisComplete();
      }
    } catch (error) {
      logWithTimestamp(`MANUAL REANALYSIS: Error analyzing video ${currentVideo.id}`, {
        videoId: currentVideo.id,
        error: error.message || 'Unknown error',
        stack: error.stack
      });
      setAnalysisError(error.message || 'Unknown error occurred');

      // Check if this is a rate limit error
      if (error.message && (error.message.includes('rate limit exceeded') ||
                           error.message.includes('Rate limit exceeded') ||
                           error.message.includes('free-models-per-day'))) {
        // Determine the type of rate limit
        let rateLimitType = "rate limit";
        if (error.message.includes('free-models-per-day')) {
          rateLimitType = "daily free model limit";
        } else if (error.message.includes('per-minute')) {
          rateLimitType = "per-minute limit";
        }

        logWithTimestamp(`MANUAL REANALYSIS: ${rateLimitType} error caught for video ${currentVideo.id}`, {
          videoId: currentVideo.id,
          error: error.message,
          errorType: 'rate_limit_caught',
          rateLimitType: rateLimitType
        });

        // Don't show a toast here since we already showed one in the response handler
      } else {
        // Show a generic error toast for other errors
        toast({
          title: "AI Analysis Failed",
          description: error.message || 'Unknown error occurred',
          variant: "destructive",
        });
      }
    } finally {
      setIsAnalyzing(false);
      logWithTimestamp(`MANUAL REANALYSIS: Analysis process completed for video ${currentVideo.id}`, {
        videoId: currentVideo.id,
        success: !analysisError,
        error: analysisError || null
      });
    }
  };

  // Handle model change
  const handleModelChange = (value: string) => {
    logWithTimestamp(`Model selection changed for video ${currentVideo.id}`, {
      videoId: currentVideo.id,
      previousModel: selectedModel,
      newModel: value
    });
    setSelectedModel(value);
  };

  // If no analysis is available, show a simple UI to start analysis
  if (!hasAiAnalysis) {
    return (
      <div className="p-4 text-center">
        <p className="text-muted-foreground mb-4">No AI analysis available for this video.</p>
        <div className="flex flex-col items-center gap-2">
          <div className="flex gap-2">
            <Button
              onClick={() => {
                logWithTimestamp(`BUTTON CLICK: Analyze with AI button clicked for video ${currentVideo.id}`, {
                  videoId: currentVideo.id,
                  title: currentVideo.title,
                  selectedModel,
                  buttonType: 'analyze_with_ai',
                  location: 'no_analysis_view',
                  isAnalyzing
                });
                handleAnalyzeWithOpenRouter();
              }}
              disabled={isAnalyzing}
              className="mx-auto"
            >
              {isAnalyzing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                  Analyzing...
                </>
              ) : (
                <>
                  <Activity className="h-4 w-4 mr-2" />
                  Analyze with AI
                </>
              )}
            </Button>
            <Button
              onClick={() => {
                // Force refresh the video data
                logWithTimestamp(`BUTTON CLICK: Refresh data button clicked for video ${currentVideo.id}`, {
                  videoId: currentVideo.id,
                  title: currentVideo.title,
                  buttonType: 'refresh_data',
                  location: 'no_analysis_view',
                  endpoint: `/api/youtube-channels/videos/${currentVideo.id}`,
                  dataSource: 'video_api'
                });
                apiRequest("GET", `/api/youtube-channels/videos/${currentVideo.id}`)
                  .then(async (res) => {
                    if (res.ok) {
                      const updatedVideo = await res.json();
                      logWithTimestamp(`DATA RETRIEVAL: Received refreshed video data for ${currentVideo.id}`, {
                        videoId: currentVideo.id,
                        hasOpenRouterRawData: Boolean(updatedVideo.openRouterRawData),
                        financialScore: updatedVideo.financialScore,
                        openRouterModelUsed: updatedVideo.openRouterModelUsed,
                        dataSource: 'video_api_response'
                      });
                      setCurrentVideo(updatedVideo);
                      // Also try to fetch raw data
                      fetchRawData();
                    }
                  })
                  .catch(err => {
                    logWithTimestamp(`ERROR: Failed to refresh video data for ${currentVideo.id}`, {
                      videoId: currentVideo.id,
                      error: err.message,
                      endpoint: `/api/youtube-channels/videos/${currentVideo.id}`
                    });
                    console.error('Error refreshing video data:', err);
                  });
              }}
              variant="outline"
              className="mx-auto"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Data
            </Button>
          </div>
          {analysisError && (
            <p className="text-destructive text-sm mt-2">{analysisError}</p>
          )}
          <p className="text-xs text-muted-foreground mt-4">
            If you've already analyzed this video in batch mode, try clicking "Refresh Data" to load the analysis.
          </p>
        </div>
      </div>
    );
  }

  // Format the benefit amounts for display
  const formatBenefitAmounts = (amounts: string[] = []) => {
    if (!amounts || amounts.length === 0) return "Not specified";

    // Filter out any non-string or empty values
    const validAmounts = amounts.filter(amount =>
      typeof amount === 'string' && amount.trim() !== ''
    );

    if (validAmounts.length === 0) return "Not specified";
    return validAmounts.join(", ");
  };

  // Helper function to get benefit amounts from various sources
  const getBenefitAmounts = () => {
    console.log('Getting benefit amounts from all sources');
    logWithTimestamp(`BENEFIT AMOUNTS: Getting benefit amounts for video ${currentVideo.id}`, {
      videoId: currentVideo.id,
      hasRawData: Boolean(rawData),
      hasParsedRawData: Boolean(parsedRawData),
      hasOpenRouterBenefitAmounts: Boolean(currentVideo.openRouterBenefitAmounts),
      openRouterBenefitAmountsType: typeof currentVideo.openRouterBenefitAmounts,
      isOpenRouterBenefitAmountsArray: Array.isArray(currentVideo.openRouterBenefitAmounts)
    });

    // First try to get from parsed raw data
    if (parsedRawData?.extractedInfo?.benefitAmounts &&
        Array.isArray(parsedRawData.extractedInfo.benefitAmounts) &&
        parsedRawData.extractedInfo.benefitAmounts.length > 0) {
      console.log('Using benefit amounts from parsed raw data:', parsedRawData.extractedInfo.benefitAmounts);
      logWithTimestamp(`BENEFIT AMOUNTS: Using from parsed raw data for video ${currentVideo.id}`, {
        source: 'parsed_raw_data',
        benefitAmounts: parsedRawData.extractedInfo.benefitAmounts
      });
      return parsedRawData.extractedInfo.benefitAmounts;
    }

    // Then try to get from the video object's OpenRouter benefit amounts
    if (currentVideo.openRouterBenefitAmounts) {
      // Handle both array and string formats
      if (Array.isArray(currentVideo.openRouterBenefitAmounts) &&
          currentVideo.openRouterBenefitAmounts.length > 0) {
        console.log('Using benefit amounts from video OpenRouter data (array):', currentVideo.openRouterBenefitAmounts);
        logWithTimestamp(`BENEFIT AMOUNTS: Using from video OpenRouter data (array) for video ${currentVideo.id}`, {
          source: 'video_openrouter_array',
          benefitAmounts: currentVideo.openRouterBenefitAmounts
        });
        return currentVideo.openRouterBenefitAmounts;
      } else if (typeof currentVideo.openRouterBenefitAmounts === 'string') {
        try {
          // Check if the string is already in JSON format
          if (currentVideo.openRouterBenefitAmounts.startsWith('[') &&
              currentVideo.openRouterBenefitAmounts.endsWith(']')) {
            const parsed = JSON.parse(currentVideo.openRouterBenefitAmounts);
            if (Array.isArray(parsed) && parsed.length > 0) {
              console.log('Using benefit amounts from video OpenRouter data (parsed string):', parsed);
              logWithTimestamp(`BENEFIT AMOUNTS: Using from video OpenRouter data (parsed string) for video ${currentVideo.id}`, {
                source: 'video_openrouter_parsed_string',
                benefitAmounts: parsed
              });
              return parsed;
            }
          } else {
            // It might be a comma-separated string
            const items = currentVideo.openRouterBenefitAmounts.split(',').map(item => item.trim());
            if (items.length > 0) {
              console.log('Using benefit amounts from video OpenRouter data (comma-separated string):', items);
              logWithTimestamp(`BENEFIT AMOUNTS: Using from video OpenRouter data (comma-separated) for video ${currentVideo.id}`, {
                source: 'video_openrouter_comma_string',
                benefitAmounts: items
              });
              return items;
            }
          }
        } catch (e) {
          console.error('Error parsing OpenRouter benefit amounts string:', e);
          logWithTimestamp(`BENEFIT AMOUNTS: Error parsing OpenRouter string for video ${currentVideo.id}`, {
            error: e.message,
            originalString: currentVideo.openRouterBenefitAmounts
          });
        }
      }
    }

    // Try to get from raw data directly if it's available
    if (currentVideo.openRouterRawData) {
      try {
        const parsedOpenRouterRawData = JSON.parse(currentVideo.openRouterRawData);
        if (parsedOpenRouterRawData.extractedInfo?.benefitAmounts &&
            Array.isArray(parsedOpenRouterRawData.extractedInfo.benefitAmounts) &&
            parsedOpenRouterRawData.extractedInfo.benefitAmounts.length > 0) {
          console.log('Using benefit amounts from openRouterRawData:', parsedOpenRouterRawData.extractedInfo.benefitAmounts);
          logWithTimestamp(`BENEFIT AMOUNTS: Using from openRouterRawData for video ${currentVideo.id}`, {
            source: 'openrouter_raw_data',
            benefitAmounts: parsedOpenRouterRawData.extractedInfo.benefitAmounts
          });
          return parsedOpenRouterRawData.extractedInfo.benefitAmounts;
        }
      } catch (e) {
        console.error('Error parsing openRouterRawData:', e);
        logWithTimestamp(`BENEFIT AMOUNTS: Error parsing openRouterRawData for video ${currentVideo.id}`, {
          error: e.message
        });
      }
    }

    // If we have a benefit description in the raw data, try to extract dollar amounts
    if (parsedRawData?.benefitDescription) {
      const matches = parsedRawData.benefitDescription.match(/\$\d+(?:,\d+)*(?:\.\d+)?/g);
      if (matches && matches.length > 0) {
        console.log('Extracted benefit amounts from benefit description:', matches);
        logWithTimestamp(`BENEFIT AMOUNTS: Extracted from benefit description for video ${currentVideo.id}`, {
          source: 'benefit_description',
          benefitAmounts: matches,
          description: parsedRawData.benefitDescription.substring(0, 100) + '...'
        });
        return matches;
      }
    }

    // If we have a financial amount in the video, try to extract dollar amounts
    if (currentVideo.financialAmount) {
      const matches = currentVideo.financialAmount.match(/\$\d+(?:,\d+)*(?:\.\d+)?/g);
      if (matches && matches.length > 0) {
        console.log('Extracted benefit amounts from financial amount:', matches);
        logWithTimestamp(`BENEFIT AMOUNTS: Extracted from financial amount for video ${currentVideo.id}`, {
          source: 'financial_amount',
          benefitAmounts: matches,
          financialAmount: currentVideo.financialAmount
        });
        return matches;
      }
    }

    // If we have a financial analysis in the video, try to extract dollar amounts
    if (currentVideo.financialAnalysis) {
      const matches = currentVideo.financialAnalysis.match(/\$\d+(?:,\d+)*(?:\.\d+)?/g);
      if (matches && matches.length > 0) {
        console.log('Extracted benefit amounts from financial analysis:', matches);
        logWithTimestamp(`BENEFIT AMOUNTS: Extracted from financial analysis for video ${currentVideo.id}`, {
          source: 'financial_analysis',
          benefitAmounts: matches,
          financialAnalysis: currentVideo.financialAnalysis.substring(0, 100) + '...'
        });
        return matches;
      }
    }

    // If all else fails, return an empty array
    console.log('No benefit amounts found in any source, returning empty array');
    logWithTimestamp(`BENEFIT AMOUNTS: No amounts found for video ${currentVideo.id}`, {
      source: 'none',
      videoId: currentVideo.id
    });
    return [];
  };

  // Get the priority color based on the tag
  const getPriorityColor = (priority: string = "none") => {
    switch (priority?.toLowerCase()) {
      case "high":
        return "bg-red-500 hover:bg-red-600";
      case "medium":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "low":
        return "bg-green-500 hover:bg-green-600";
      default:
        return "bg-slate-500 hover:bg-slate-600";
    }
  };

  return (
    <div className="space-y-6 p-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">Benefits Analysis</h2>
          <p className="text-muted-foreground">
            Analyzed using AI
            {currentVideo.openRouterModelUsed && !currentVideo.openRouterModelUsed.includes('Error')
              ? ` (Model: ${currentVideo.openRouterModelUsed})`
              : ''}
            {parsedRawData?.promptName && (
              <span className="block text-xs mt-1">Prompt: {parsedRawData.promptName}</span>
            )}
          </p>

        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Select value={selectedModel} onValueChange={handleModelChange}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select model" />
            </SelectTrigger>
            <SelectContent>
              {availableModels.map((model) => (
                <SelectItem key={model.name} value={model.name}>
                  {model.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            onClick={() => {
              logWithTimestamp(`BUTTON CLICK: Reanalyze button clicked for video ${currentVideo.id}`, {
                videoId: currentVideo.id,
                title: currentVideo.title,
                selectedModel,
                buttonType: 'reanalyze',
                location: 'main_view',
                isAnalyzing
              });
              handleAnalyzeWithOpenRouter();
            }}
            disabled={isAnalyzing}
            variant="secondary"
            className="whitespace-nowrap"
          >
            {isAnalyzing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                Analyzing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Reanalyze
              </>
            )}
          </Button>
        </div>
      </div>

      {analysisError && (
        <div className="bg-destructive/10 p-4 rounded-md text-destructive">
          <p className="font-semibold">Analysis Error</p>
          <p className="text-sm">{analysisError}</p>
        </div>
      )}

      <Tabs value={activeTab} onValueChange={(value) => {
        // Add very explicit console.log for debugging
        console.log(`==== TAB CHANGED: ${activeTab} -> ${value} for video ${currentVideo.id} ====`);
        console.log('Current video data:', {
          id: currentVideo.id,
          title: currentVideo.title,
          financialScore: currentVideo.financialScore,
          openRouterBenefitAmounts: currentVideo.openRouterBenefitAmounts,
          openRouterRawData: Boolean(currentVideo.openRouterRawData)
        });

        logWithTimestamp(`TAB SELECTION: Changed tab to ${value} for video ${currentVideo.id}`, {
          videoId: currentVideo.id,
          previousTab: activeTab,
          newTab: value,
          location: 'ai_analysis_tabs',
          hasRawData: Boolean(rawData),
          rawDataLength: rawData ? rawData.length : 0,
          hasRawInput: Boolean(rawInput.prompt || rawInput.systemPrompt),
          openRouterBenefitAmounts: currentVideo.openRouterBenefitAmounts ?
            (Array.isArray(currentVideo.openRouterBenefitAmounts) ?
              JSON.stringify(currentVideo.openRouterBenefitAmounts) : currentVideo.openRouterBenefitAmounts) : 'null',
          openRouterRawData: Boolean(currentVideo.openRouterRawData),
          financialScore: currentVideo.financialScore
        });

        // If switching to raw output tab, make sure we have the raw data
        if (value === 'raw-output' && !rawData) {
          console.log(`Fetching raw data for raw output tab for video ${currentVideo.id}`);
          logWithTimestamp(`DATA RETRIEVAL: Fetching raw data for raw output tab for video ${currentVideo.id}`);
          fetchRawData();
        }

        // If switching to formatted tab, log the data being displayed
        if (value === 'formatted') {
          console.log(`Displaying formatted data for video ${currentVideo.id}:`, {
            financialScore: currentVideo.financialScore,
            financialCategory: currentVideo.financialCategory,
            financialAmount: currentVideo.financialAmount,
            openRouterBenefitAmounts: currentVideo.openRouterBenefitAmounts
          });
        }

        // If switching to raw input tab, log the data being displayed
        if (value === 'raw-input') {
          console.log(`Displaying raw input for video ${currentVideo.id}:`, {
            promptLength: rawInput.prompt ? rawInput.prompt.length : 0,
            systemPromptLength: rawInput.systemPrompt ? rawInput.systemPrompt.length : 0
          });
        }

        setActiveTab(value);
      }}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger
            value="formatted"
            onClick={() => {
              console.log(`==== FORMATTED TAB CLICKED for video ${currentVideo.id} ====`);
              console.log('Current video data when formatted tab clicked:', {
                id: currentVideo.id,
                financialScore: currentVideo.financialScore,
                openRouterBenefitAmounts: currentVideo.openRouterBenefitAmounts
              });
            }}
          >
            Formatted
          </TabsTrigger>
          <TabsTrigger
            value="raw-output"
            onClick={() => {
              console.log(`==== RAW OUTPUT TAB CLICKED for video ${currentVideo.id} ====`);
              console.log('Current raw data state:', {
                hasRawData: Boolean(rawData),
                rawDataLength: rawData ? rawData.length : 0
              });
            }}
          >
            Raw Output
          </TabsTrigger>
          <TabsTrigger
            value="raw-input"
            onClick={() => {
              console.log(`==== RAW INPUT TAB CLICKED for video ${currentVideo.id} ====`);
              console.log('Current raw input state:', {
                hasPrompt: Boolean(rawInput.prompt),
                promptLength: rawInput.prompt ? rawInput.prompt.length : 0,
                hasSystemPrompt: Boolean(rawInput.systemPrompt),
                systemPromptLength: rawInput.systemPrompt ? rawInput.systemPrompt.length : 0
              });
            }}
          >
            Raw Input
          </TabsTrigger>
        </TabsList>

        <TabsContent value="formatted" className="space-y-4">
          <div className="flex justify-end mb-2">
            <Button
              onClick={() => {
                // Force refresh the video data and raw data
                logWithTimestamp(`BUTTON CLICK: Refresh data button clicked for video ${currentVideo.id}`, {
                  videoId: currentVideo.id,
                  title: currentVideo.title,
                  buttonType: 'refresh_data',
                  location: 'formatted_tab',
                  endpoint: `/api/youtube-channels/videos/${currentVideo.id}`,
                  dataSource: 'video_api'
                });
                apiRequest("GET", `/api/youtube-channels/videos/${currentVideo.id}`)
                  .then(async (res) => {
                    if (res.ok) {
                      const updatedVideo = await res.json();
                      logWithTimestamp(`DATA RETRIEVAL: Received refreshed video data for ${currentVideo.id}`, {
                        videoId: currentVideo.id,
                        hasOpenRouterRawData: Boolean(updatedVideo.openRouterRawData),
                        financialScore: updatedVideo.financialScore,
                        openRouterModelUsed: updatedVideo.openRouterModelUsed,
                        openRouterBenefitAmounts: updatedVideo.openRouterBenefitAmounts,
                        dataSource: 'video_api_response'
                      });
                      setCurrentVideo(updatedVideo);
                      // Also refresh raw data
                      fetchRawData();
                    }
                  })
                  .catch(err => {
                    logWithTimestamp(`ERROR: Failed to refresh video data for ${currentVideo.id}`, {
                      videoId: currentVideo.id,
                      error: err.message,
                      endpoint: `/api/youtube-channels/videos/${currentVideo.id}`
                    });
                    console.error('Error refreshing video data:', err);
                  });
              }}
              variant="outline"
              size="sm"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Refresh Data
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Benefits</CardTitle>
                <CardDescription>
                  {(parsedRawData?.hasBenefit || currentVideo.financialScore > 30 || getBenefitAmounts().length > 0)
                    ? "Financial benefit detected"
                    : "No financial benefit detected"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Benefit Type:</span>
                    <span className="font-medium">
                      {parsedRawData?.benefitType || currentVideo.financialCategory || "Not specified"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Certainty Score:</span>
                    <span className="font-medium">
                      {parsedRawData?.certaintyScore || currentVideo.financialScore || "N/A"}/100
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Overall Score:</span>
                    <span className="font-medium">
                      {parsedRawData?.score || currentVideo.financialScore || "N/A"}/100
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Priority:</span>
                    <Badge
                      className={`${getPriorityColor(
                        parsedRawData?.priorityTag || currentVideo.openRouterPriorityTag
                      )} text-white`}
                    >
                      {parsedRawData?.priorityTag || currentVideo.openRouterPriorityTag || "None"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Benefit Details</CardTitle>
                <CardDescription>
                  Extracted information about the benefit
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Amount(s):</span>
                    <span className="font-medium text-right">
                      {formatBenefitAmounts(getBenefitAmounts())}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Expected Arrival:</span>
                    <span className="font-medium text-right">
                      {parsedRawData?.extractedInfo?.expectedArrivalDate ||
                       currentVideo.openRouterExpectedArrivalDate ||
                       currentVideo.financialTimeline ||
                       "Not specified"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Eligible People:</span>
                    <span className="font-medium text-right">
                      {parsedRawData?.extractedInfo?.eligiblePeople ||
                       currentVideo.openRouterEligiblePeople ||
                       currentVideo.financialRecipients ||
                       "Not specified"}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Benefit Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{parsedRawData?.benefitDescription ||
                  currentVideo.financialAmount ||
                  currentVideo.financialAnalysis ||
                  "No description available"}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Actions to Claim</CardTitle>
            </CardHeader>
            <CardContent>
              <p>
                {parsedRawData?.extractedInfo?.actionsToClaim ||
                 currentVideo.openRouterActionsToClaim ||
                 currentVideo.financialSteps ||
                 "No actions specified"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Proof or Source</CardTitle>
            </CardHeader>
            <CardContent>
              <p>
                {parsedRawData?.extractedInfo?.proofOrSource ||
                 currentVideo.openRouterProofOrSource ||
                 "No source information provided"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Analysis Reasoning</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{parsedRawData?.reasoning ||
                  currentVideo.openRouterReasonForPriority ||
                  "No reasoning provided"}</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="raw-output">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Raw Analysis Output</CardTitle>
              <CardDescription>
                The raw JSON output from the AI model
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-md overflow-auto max-h-[500px] w-full">
                <pre className="text-xs whitespace-pre-wrap break-all overflow-x-hidden">
                  {(() => {
                    console.log('Rendering raw data in raw-output tab:', {
                      hasRawData: Boolean(rawData),
                      rawDataLength: rawData ? rawData.length : 0,
                      hasOpenRouterRawData: Boolean(currentVideo.openRouterRawData),
                      openRouterRawDataLength: currentVideo.openRouterRawData ? currentVideo.openRouterRawData.length : 0
                    });

                    // Try to format the raw data as pretty JSON if possible
                    try {
                      const dataToDisplay = rawData || currentVideo.openRouterRawData;
                      if (dataToDisplay) {
                        console.log('Raw data type:', typeof dataToDisplay);
                        console.log('Raw data preview:', dataToDisplay.substring ? dataToDisplay.substring(0, 100) : 'Not a string');

                        // If it's already a string, try to parse and re-stringify it for pretty printing
                        if (typeof dataToDisplay === 'string') {
                          try {
                            const parsed = JSON.parse(dataToDisplay);
                            return JSON.stringify(parsed, null, 2);
                          } catch (parseError) {
                            console.error('Error parsing raw data JSON:', parseError);
                            // If parsing fails, just return the raw string
                            return dataToDisplay;
                          }
                        }
                        // If it's an object, stringify it directly
                        else if (typeof dataToDisplay === 'object') {
                          return JSON.stringify(dataToDisplay, null, 2);
                        }
                        // Otherwise just return it as is
                        return dataToDisplay;
                      }
                      return "No raw data available";
                    } catch (e) {
                      // If any error occurs, log it and return the raw data as is
                      console.error('Error formatting raw data:', e);
                      if (rawData) {
                        return rawData;
                      } else if (currentVideo.openRouterRawData) {
                        return currentVideo.openRouterRawData;
                      } else {
                        return "No raw data available";
                      }
                    }
                  })()}
                </pre>
              </div>
              <div className="flex justify-between mt-4">
                <Button
                  onClick={() => {
                    console.log(`==== REFRESH RAW DATA BUTTON CLICKED for video ${currentVideo.id} ====`);
                    console.log('Current video state when refresh clicked:', {
                      id: currentVideo.id,
                      title: currentVideo.title,
                      financialScore: currentVideo.financialScore,
                      openRouterBenefitAmounts: currentVideo.openRouterBenefitAmounts,
                      hasOpenRouterRawData: Boolean(currentVideo.openRouterRawData),
                      openRouterRawDataLength: currentVideo.openRouterRawData ? currentVideo.openRouterRawData.length : 0
                    });

                    logWithTimestamp(`BUTTON CLICK: Refresh raw data button clicked for video ${currentVideo.id}`, {
                      videoId: currentVideo.id,
                      buttonType: 'refresh_raw_data',
                      location: 'raw_output_tab',
                      endpoint: `/api/videos/${currentVideo.id}/raw-analysis`,
                      dataSource: 'raw_analysis_api',
                      currentRawDataLength: rawData ? rawData.length : 0,
                      openRouterBenefitAmounts: currentVideo.openRouterBenefitAmounts ?
                        (Array.isArray(currentVideo.openRouterBenefitAmounts) ?
                          JSON.stringify(currentVideo.openRouterBenefitAmounts) : currentVideo.openRouterBenefitAmounts) : 'null'
                    });

                    // Show a toast to indicate that we're refreshing the data
                    toast({
                      title: "Refreshing data",
                      description: "Fetching the latest analysis data...",
                      duration: 2000
                    });

                    // Clear the current raw data to indicate that we're refreshing
                    setRawData("Refreshing raw data...");

                    // First refresh the video data to ensure we have the latest data
                    apiRequest("GET", `/api/youtube-channels/videos/${currentVideo.id}`)
                      .then(async (res) => {
                        if (res.ok) {
                          const updatedVideo = await res.json();
                          logWithTimestamp(`DATA RETRIEVAL: Received refreshed video data for ${currentVideo.id}`, {
                            videoId: currentVideo.id,
                            hasOpenRouterRawData: Boolean(updatedVideo.openRouterRawData),
                            openRouterRawDataLength: updatedVideo.openRouterRawData ? updatedVideo.openRouterRawData.length : 0,
                            financialScore: updatedVideo.financialScore,
                            openRouterModelUsed: updatedVideo.openRouterModelUsed,
                            openRouterBenefitAmounts: updatedVideo.openRouterBenefitAmounts,
                            dataSource: 'video_api_response'
                          });

                          // Update the current video with the refreshed data
                          setCurrentVideo(updatedVideo);

                          // If the refreshed video has raw data, use it directly
                          if (updatedVideo.openRouterRawData) {
                            console.log(`Using refreshed openRouterRawData for video ${currentVideo.id}`);
                            console.log('Refreshed raw data length:', updatedVideo.openRouterRawData.length);

                            try {
                              // Try to parse and pretty-print the raw data
                              const parsedData = JSON.parse(updatedVideo.openRouterRawData);
                              setRawData(JSON.stringify(parsedData, null, 2));

                              // Extract prompt and system prompt if available
                              if (parsedData.prompt) {
                                setRawInput({
                                  prompt: parsedData.prompt,
                                  systemPrompt: parsedData.systemPrompt || ''
                                });
                              }

                              toast({
                                title: "Data refreshed",
                                description: "Analysis data has been refreshed",
                                duration: 2000
                              });
                            } catch (parseError) {
                              console.error('Error parsing refreshed openRouterRawData:', parseError);
                              // Still use the raw data even if parsing fails
                              setRawData(updatedVideo.openRouterRawData);

                              toast({
                                title: "Data refreshed",
                                description: "Analysis data has been refreshed (raw format)",
                                duration: 2000
                              });
                            }
                          } else {
                            // If the refreshed video doesn't have raw data, fetch it from the API
                            console.log(`Refreshed video doesn't have raw data, fetching from API for video ${currentVideo.id}`);
                            fetchRawData();
                          }
                        } else {
                          // If the video refresh fails, still try to fetch raw data
                          console.error('Failed to refresh video data, status:', res.status);
                          fetchRawData();
                        }
                      })
                      .catch(err => {
                        logWithTimestamp(`ERROR: Failed to refresh video data for ${currentVideo.id}`, {
                          videoId: currentVideo.id,
                          error: err.message,
                          endpoint: `/api/youtube-channels/videos/${currentVideo.id}`
                        });
                        console.error('Error refreshing video data:', err);

                        // Still try to fetch raw data even if video refresh fails
                        fetchRawData();
                      });
                  }}
                  variant="outline"
                  size="sm"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Refresh Raw Data
                </Button>

                {/* Add a button to copy the raw data to clipboard */}
                <Button
                  onClick={() => {
                    const dataToCopy = rawData || currentVideo.openRouterRawData || "";
                    if (dataToCopy) {
                      navigator.clipboard.writeText(dataToCopy)
                        .then(() => {
                          toast({
                            title: "Copied to clipboard",
                            description: "Raw data has been copied to clipboard",
                            duration: 2000
                          });
                        })
                        .catch(err => {
                          console.error('Failed to copy:', err);
                          toast({
                            title: "Copy failed",
                            description: "Failed to copy raw data to clipboard",
                            variant: "destructive"
                          });
                        });
                    }
                  }}
                  variant="outline"
                  size="sm"
                  disabled={!rawData && !currentVideo.openRouterRawData}
                >
                  <Code className="h-3 w-3 mr-1" />
                  Copy to Clipboard
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="raw-input">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Prompt</CardTitle>
              <CardDescription>
                The prompt sent to the AI model
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-md overflow-auto max-h-[500px] w-full">
                <pre className="text-xs whitespace-pre-wrap break-all overflow-x-hidden">
                  {rawInput.prompt || currentVideo.openRouterPrompt || "No prompt available"}
                </pre>
              </div>
              <div className="flex justify-between mt-4">
                <Button
                  onClick={() => {
                    // Refresh the raw data to ensure we have the latest prompt
                    logWithTimestamp(`BUTTON CLICK: Refresh prompt button clicked for video ${currentVideo.id}`, {
                      videoId: currentVideo.id,
                      buttonType: 'refresh_prompt',
                      location: 'raw_input_tab'
                    });

                    // First refresh the video data
                    apiRequest("GET", `/api/youtube-channels/videos/${currentVideo.id}`)
                      .then(async (res) => {
                        if (res.ok) {
                          const updatedVideo = await res.json();
                          logWithTimestamp(`DATA RETRIEVAL: Received refreshed video data for ${currentVideo.id}`, {
                            videoId: currentVideo.id,
                            hasOpenRouterPrompt: Boolean(updatedVideo.openRouterPrompt),
                            hasOpenRouterSystemPrompt: Boolean(updatedVideo.openRouterSystemPrompt),
                            dataSource: 'video_api_response'
                          });
                          setCurrentVideo(updatedVideo);

                          // Then fetch the raw data to get the prompt
                          fetchRawData();
                        }
                      })
                      .catch(err => {
                        console.error('Error refreshing video data:', err);
                        // Still try to fetch raw data
                        fetchRawData();
                      });
                  }}
                  variant="outline"
                  size="sm"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Refresh Prompt
                </Button>

                <Button
                  onClick={() => {
                    const promptToCopy = rawInput.prompt || currentVideo.openRouterPrompt || "";
                    if (promptToCopy) {
                      navigator.clipboard.writeText(promptToCopy)
                        .then(() => {
                          toast({
                            title: "Copied to clipboard",
                            description: "Prompt has been copied to clipboard",
                            duration: 2000
                          });
                        })
                        .catch(err => {
                          console.error('Failed to copy prompt:', err);
                          toast({
                            title: "Copy failed",
                            description: "Failed to copy prompt to clipboard",
                            variant: "destructive"
                          });
                        });
                    }
                  }}
                  variant="outline"
                  size="sm"
                  disabled={!rawInput.prompt && !currentVideo.openRouterPrompt}
                >
                  <Code className="h-3 w-3 mr-1" />
                  Copy Prompt
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Add system prompt section if available */}
          {(rawInput.systemPrompt || currentVideo.openRouterSystemPrompt) && (
            <Card className="mt-4">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">System Prompt</CardTitle>
                <CardDescription>
                  The system prompt sent to the AI model
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-muted p-4 rounded-md overflow-auto max-h-[300px]">
                  <pre className="text-xs whitespace-pre-wrap">
                    {rawInput.systemPrompt || currentVideo.openRouterSystemPrompt || "No system prompt available"}
                  </pre>
                </div>
                <div className="flex justify-end mt-4">
                  <Button
                    onClick={() => {
                      const systemPromptToCopy = rawInput.systemPrompt || currentVideo.openRouterSystemPrompt || "";
                      if (systemPromptToCopy) {
                        navigator.clipboard.writeText(systemPromptToCopy)
                          .then(() => {
                            toast({
                              title: "Copied to clipboard",
                              description: "System prompt has been copied to clipboard",
                              duration: 2000
                            });
                          })
                          .catch(err => {
                            console.error('Failed to copy system prompt:', err);
                            toast({
                              title: "Copy failed",
                              description: "Failed to copy system prompt to clipboard",
                              variant: "destructive"
                            });
                          });
                      }
                    }}
                    variant="outline"
                    size="sm"
                    disabled={!rawInput.systemPrompt && !currentVideo.openRouterSystemPrompt}
                  >
                    <Code className="h-3 w-3 mr-1" />
                    Copy System Prompt
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
