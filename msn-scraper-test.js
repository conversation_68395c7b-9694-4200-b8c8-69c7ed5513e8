import axios from 'axios';
import * as cheerio from 'cheerio';

// Test URLs
const testUrls = [
  {
    title: "Social Security: Checks of Up to $5,108 Going Out This Week",
    url: "https://www.msn.com/en-us/money/retirement/social-security-checks-of-up-to-5-108-going-out-this-week/ar-AA1DKbYM"
  },
  {
    title: "Social Security Prepares an Early Payment in May",
    url: "https://www.msn.com/en-us/money/retirement/social-security-prepares-an-early-payment-in-may/ar-AA1DLk1o"
  }
];

// Scraping strategies
const strategies = [
  {
    name: "Direct Access with Desktop User Agent",
    handler: async (url) => {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 10000
      });
      return response.data;
    }
  },
  {
    name: "Direct Access with Mobile User Agent",
    handler: async (url) => {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 10000
      });
      return response.data;
    }
  },
  {
    name: "Mobile URL Format",
    handler: async (url) => {
      const articleIdMatch = url.match(/\/ar-([A-Za-z0-9]+)/);
      if (!articleIdMatch || !articleIdMatch[1]) {
        throw new Error('Could not extract article ID from URL');
      }
      const articleId = articleIdMatch[1];
      const mobileUrl = `https://www.msn.com/en-us/news/other/ar-${articleId}?ocid=msnews`;

      const response = await axios.get(mobileUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 10000
      });
      return response.data;
    }
  },
  {
    name: "Money URL Format",
    handler: async (url) => {
      const articleIdMatch = url.match(/\/ar-([A-Za-z0-9]+)/);
      if (!articleIdMatch || !articleIdMatch[1]) {
        throw new Error('Could not extract article ID from URL');
      }
      const articleId = articleIdMatch[1];
      const moneyUrl = `https://www.msn.com/en-us/money/other/ar-${articleId}?ocid=finance-verthp-feeds`;

      const response = await axios.get(moneyUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 10000
      });
      return response.data;
    }
  },
  {
    name: "12ft.io Proxy",
    handler: async (url) => {
      const proxyUrl = `https://12ft.io/proxy?q=${encodeURIComponent(url)}`;
      const response = await axios.get(proxyUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
        },
        timeout: 15000
      });
      return response.data;
    }
  },
  {
    name: "AllOrigins Proxy",
    handler: async (url) => {
      const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`;
      const response = await axios.get(proxyUrl, {
        timeout: 10000
      });
      return response.data;
    }
  }
];

// Content extraction strategies
const extractContent = (html, title) => {
  const $ = cheerio.load(html);

  // Get the title
  const extractedTitle = $('h1').first().text().trim() || title;

  // Get the author info
  const authorInfo = $('.authorinfo').text().trim() ||
                    $('.byline').text().trim() ||
                    $('[data-author-name]').text().trim() ||
                    $('[itemprop="author"]').text().trim();

  // Try different content selectors
  const contentSelectors = [
    '[data-testid="article-body-content"]',
    '[data-testid="articleBodyContent"]',
    '.articlebody',
    '.article-body',
    '.article-content',
    '.content-article',
    '.articlecontent',
    '#content-main',
    '.main-content',
    '.article-page-content',
    '.page-content',
    '.article-text',
    '.article',
    'article'
  ];

  let content = '';
  let usedSelector = '';

  for (const selector of contentSelectors) {
    if ($(selector).length) {
      content = $(selector).html() || '';
      usedSelector = selector;
      break;
    }
  }

  // If no content found with specific selectors, try paragraphs
  if (!content || content.length < 100) {
    const paragraphs = $('p').map((i, el) => $(el).html()).get();
    const filteredParagraphs = paragraphs.filter(p => {
      const text = $(p).text();
      return text.length > 30 && !text.includes('ADVERTISEMENT');
    });

    content = filteredParagraphs.map(p => `<p>${p}</p>`).join('');
    usedSelector = 'paragraphs';
  }

  return {
    title: extractedTitle,
    author: authorInfo,
    content: content,
    contentLength: content.length,
    usedSelector: usedSelector,
    hasContent: content.length > 100
  };
};

// Run the test
const runTest = async () => {
  console.log('=== MSN ARTICLE SCRAPER TEST ===\n');

  for (const testUrl of testUrls) {
    console.log(`\n\nTESTING URL: ${testUrl.title}`);
    console.log(`URL: ${testUrl.url}`);
    console.log('='.repeat(50));

    for (const strategy of strategies) {
      console.log(`\nStrategy: ${strategy.name}`);
      try {
        const startTime = Date.now();
        const html = await strategy.handler(testUrl.url);
        const endTime = Date.now();

        const result = extractContent(html, testUrl.title);

        console.log(`  Status: SUCCESS`);
        console.log(`  Time: ${endTime - startTime}ms`);
        console.log(`  Title found: ${result.title ? 'YES' : 'NO'}`);
        console.log(`  Author found: ${result.author ? 'YES' : 'NO'}`);
        console.log(`  Content found: ${result.hasContent ? 'YES' : 'NO'}`);
        console.log(`  Content length: ${result.contentLength} characters`);
        console.log(`  Content selector: ${result.usedSelector}`);

        if (result.hasContent) {
          console.log(`  Content preview: ${result.content.substring(0, 100).replace(/\n/g, ' ')}...`);
        }
      } catch (error) {
        console.log(`  Status: FAILED`);
        console.log(`  Error: ${error.message}`);
      }
    }
  }
};

runTest();
