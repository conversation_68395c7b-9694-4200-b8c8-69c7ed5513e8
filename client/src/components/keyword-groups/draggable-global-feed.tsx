import { KeywordGroup } from "@shared/schema";
import "./draggable.css";
import { <PERSON>, CardHeader, CardTitle, CardDescription, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, ChevronDown, ChevronUp, Pencil, Plus, RefreshCw, X, GripVertical } from "lucide-react";
import { useSettings } from "@/hooks/use-settings";
import { formatRefreshTime } from "@/lib/date-format";

interface DraggableGlobalFeedProps {
  expandedGroups: Record<string, boolean>;
  toggleGroupExpansion: (groupId: string) => void;
  openEditDialog: (group: KeywordGroup) => void;
  handleRefreshGroup: (groupId: number) => void;
  handleSetActiveGroup: (groupId: number | null) => void;
  openAddKeywordDialog: (groupId: number | null) => void;
  handleRemoveGlobalKeyword: (keyword: string) => void;
  refreshGlobalVideos: any;
  isDragging: boolean;
  dragHandleProps: any;
}

export function DraggableGlobalFeed({
  expandedGroups,
  toggleGroupExpansion,
  openEditDialog,
  handleRefreshGroup,
  handleSetActiveGroup,
  openAddKeywordDialog,
  handleRemoveGlobalKeyword,
  refreshGlobalVideos,
  isDragging,
  dragHandleProps
}: DraggableGlobalFeedProps) {
  const { settings } = useSettings();

  // Create a pseudo-group for the global feed
  const globalFeed: KeywordGroup = {
    id: 0,
    userId: 0,
    name: "Global Feed",
    keywords: settings?.searchKeywords || [],
    excludeWords: settings?.excludeWords || [],
    lastRefreshTime: settings?.lastRefreshTime || null,
    displayOrder: 0
  };

  return (
    <Card
      className={`draggable-item ${settings?.activeKeywordGroupId === null ? "border-primary" : ""} ${isDragging ? "opacity-50" : ""}`}
    >
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <div {...dragHandleProps} className="cursor-grab p-1 hover:bg-muted rounded-md transition-colors flex items-center justify-center bg-muted/30 h-6 w-6">
              <GripVertical className="h-4 w-4 text-primary" />
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-6 w-6"
              onClick={() => toggleGroupExpansion('global')}
            >
              {expandedGroups['global'] ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
            <span>Global Feed</span>
          </div>
          {settings?.activeKeywordGroupId === null && (
            <Badge variant="outline" className="ml-2">
              <Check className="h-3 w-3 mr-1" />
              Active
            </Badge>
          )}
        </CardTitle>
        <CardDescription className="font-sans">
          Last refreshed: <span className="font-semibold">{formatRefreshTime(settings?.lastRefreshTime)}</span>
        </CardDescription>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div>
            <div className="flex justify-between items-center mb-1">
              <div className="flex items-center gap-2">
                <h4 className="text-sm font-semibold">Keywords:</h4>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    openAddKeywordDialog(0);
                  }}
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
              <Badge variant="outline">{settings?.searchKeywords?.length || 0}</Badge>
            </div>
            {expandedGroups['global'] ? (
              <div className="flex flex-wrap gap-2 mt-2">
                {settings?.searchKeywords?.map((keyword) => (
                  <Badge key={keyword} variant="secondary" className="group relative pr-6">
                    {keyword}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveGlobalKeyword(keyword);
                      }}
                      className="absolute right-1 opacity-0 group-hover:opacity-100 hover:text-destructive transition-opacity"
                      aria-label={`Remove keyword ${keyword}`}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">
                {settings?.searchKeywords?.length || 0} keywords
              </div>
            )}
          </div>

          {settings?.excludeWords && settings.excludeWords.length > 0 && (
            <div>
              <div className="flex justify-between items-center mb-1">
                <h4 className="text-sm font-semibold">Exclude Words:</h4>
                <Badge variant="outline">{settings.excludeWords.length}</Badge>
              </div>
              {expandedGroups['global'] ? (
                <div className="flex flex-wrap gap-2 mt-2">
                  {settings.excludeWords.map((word) => (
                    <Badge key={word} variant="outline" className="group relative pr-6">
                      {word}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle remove exclude word
                        }}
                        className="absolute right-1 opacity-0 group-hover:opacity-100 hover:text-destructive transition-opacity"
                        aria-label={`Remove exclude word ${word}`}
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  {settings.excludeWords.length} exclude words
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => handleRefreshGroup(0)}
            disabled={refreshGlobalVideos.isPending}
          >
            <RefreshCw className={`h-4 w-4 ${refreshGlobalVideos.isPending ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        <Button
          variant={settings?.activeKeywordGroupId === null ? "default" : "outline"}
          onClick={() => handleSetActiveGroup(null)}
        >
          {settings?.activeKeywordGroupId === null ? "Active" : "Set Active"}
        </Button>
      </CardFooter>
    </Card>
  );
}
