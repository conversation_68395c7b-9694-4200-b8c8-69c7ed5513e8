/**
 * Logging configuration for the application
 *
 * This file contains configuration settings for the application's logging system.
 * Users can modify these settings to control the verbosity of logs.
 */

import { LogLevel } from '@/lib/logger';

/**
 * Default log level for the application
 *
 * Available levels:
 * - LogLevel.NONE: No logs (0)
 * - LogLevel.ERROR: Only errors (1)
 * - LogLevel.WARN: Warnings and errors (2)
 * - LogLevel.INFO: Info, warnings, and errors (3)
 * - LogLevel.DEBUG: Debug, info, warnings, and errors (4)
 * - LogLevel.VERBOSE: All logs (5)
 *
 * Production default: LogLevel.ERROR
 * Development default: LogLevel.WARN
 *
 * Set to LogLevel.NONE to disable all logs completely
 */
export const USER_LOG_LEVEL = LogLevel.NONE;

/**
 * Enable detailed performance logging
 *
 * When true, the application will log detailed performance metrics
 * such as component render times, data loading times, etc.
 */
export const ENABLE_PERFORMANCE_LOGGING = false;

/**
 * Enable detailed API request logging
 *
 * When true, the application will log detailed information about
 * API requests and responses.
 */
export const ENABLE_API_LOGGING = false;

/**
 * Enable memory usage logging
 *
 * When true, the application will log memory usage statistics
 * to help identify memory leaks and optimize performance.
 */
export const ENABLE_MEMORY_LOGGING = false;
