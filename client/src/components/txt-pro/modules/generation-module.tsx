import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useWorkflow } from '../workflow-state';
import {
  Bot,
  Copy,
  Wand2,
  FileText,
  Target,
  Users,
  Clock,
  Lightbulb,
  Structure,
  Info,
  Mic,
  Highlighter,
  Scissors,
  Save,
  CheckCircle,
  Trash2
} from 'lucide-react';

interface Script {
  id: string;
  title: string;
  transcript: string;
  tags: string[];
}

interface Chunk {
  id: string;
  name: string;
  content: string;
  purpose: string;
}

interface Highlight {
  id: string;
  scriptId: string;
  startIndex: number;
  endIndex: number;
  color: 'red' | 'green' | 'blue' | 'yellow';
  note: string;
  analyticsNote?: string;
  timestamp?: string;
}

interface Strategy {
  id: string;
  title: string;
  description: string;
  tone: string;
  samplePhrases: string[];
}

interface GenerationModuleProps {
  scripts: Script[];
  chunks: Chunk[];
  highlights: Highlight[];
  strategies: Strategy[];
  selectedScript: Script | null;
  toast: any;
}

const GenerationModule: React.FC<GenerationModuleProps> = ({
  scripts,
  chunks,
  highlights,
  strategies,
  selectedScript,
  toast
}) => {
  const {
    workflowState,
    updateStepData,
    markStepComplete,
    generateFinalInstructions,
    saveGeneratedScript
  } = useWorkflow();

  const [generatedInstructions, setGeneratedInstructions] = useState('');
  const [savedScripts, setSavedScripts] = useState<any[]>([]);
  const [newScriptContent, setNewScriptContent] = useState('');
  const [newScriptTitle, setNewScriptTitle] = useState('');

  // Load saved scripts from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('txtpro_generated_scripts');
    if (saved) {
      setSavedScripts(JSON.parse(saved));
    }
  }, []);

  // Save scripts to localStorage
  useEffect(() => {
    localStorage.setItem('txtpro_generated_scripts', JSON.stringify(savedScripts));
  }, [savedScripts]);

  const configStep = workflowState.steps.find(s => s.id === 'configuration');
  const generationForm = configStep?.data || {};

  const audienceOptions = [
    { value: 'seniors-ssi', label: 'Seniors on SSI' },
    { value: 'low-income', label: 'Low-income viewers' },
    { value: 'general-public', label: 'General public' },
    { value: 'financial-stressed', label: 'Financially stressed individuals' }
  ];

  const toneOptions = [
    { value: 'hopeful', label: 'Hopeful' },
    { value: 'urgent', label: 'Urgent' },
    { value: 'empathetic', label: 'Empathetic' },
    { value: 'authoritative', label: 'Authoritative' },
    { value: 'reassuring', label: 'Reassuring' }
  ];

  const lengthOptions = [
    { value: '1000', label: '1,000 words (~4 mins)' },
    { value: '1500', label: '1,500 words (~6 mins)' },
    { value: '2000', label: '2,000 words (~8 mins)' },
    { value: '2500', label: '2,500 words (~10 mins)' },
    { value: '3000', label: '3,000 words (~12 mins)' }
  ];

  const handleGenerateInstructions = () => {
    const instructions = generateFinalInstructions();
    setGeneratedInstructions(instructions);
    updateStepData('generation', { finalInstructions: instructions });
    markStepComplete('generation');

    toast({
      title: "Instructions Generated",
      description: "ChatGPT instructions are ready to copy! Works with any combination of inputs."
    });
  };

  const handleCopyInstructions = () => {
    navigator.clipboard.writeText(generatedInstructions);
    toast({
      title: "Instructions Copied",
      description: "ChatGPT instructions have been copied to clipboard. Paste them into ChatGPT to generate your script!"
    });
  };

  const handleSaveScript = () => {
    if (!newScriptTitle.trim() || !newScriptContent.trim()) {
      toast({
        title: "Validation Error",
        description: "Both script title and content are required",
        variant: "destructive"
      });
      return;
    }

    const newScript = {
      id: `generated_${Date.now()}`,
      title: newScriptTitle.trim(),
      content: newScriptContent.trim(),
      instructions: generatedInstructions,
      sourceWorkflow: getWorkflowSummary(),
      dateGenerated: new Date(),
      version: savedScripts.length + 1
    };

    setSavedScripts(prev => [...prev, newScript]);

    toast({
      title: "Script Saved",
      description: `"${newScript.title}" has been saved to your library`
    });

    // Reset form
    setNewScriptTitle('');
    setNewScriptContent('');
  };

  const getWorkflowSummary = () => {
    const scriptStep = workflowState.steps.find(s => s.id === 'script-selection');
    const chunkStep = workflowState.steps.find(s => s.id === 'chunk-creation');
    const strategyStep = workflowState.steps.find(s => s.id === 'strategy-selection');
    const highlightStep = workflowState.steps.find(s => s.id === 'highlight-analysis');
    const configStep = workflowState.steps.find(s => s.id === 'configuration');

    return {
      sourceScript: scriptStep?.data.selectedScript?.title || '',
      selectedChunks: chunkStep?.data.selectedChunks?.map((c: any) => c.name) || [],
      selectedStrategies: strategyStep?.data.selectedStrategies?.map((s: any) => s.title) || [],
      highlights: highlightStep?.data.highlights?.length || 0,
      targetAudience: configStep?.data.targetAudience || '',
      tone: configStep?.data.tone || '',
      length: configStep?.data.length || ''
    };
  };

  const handleDeleteScript = (scriptId: string) => {
    setSavedScripts(prev => prev.filter(s => s.id !== scriptId));
    toast({
      title: "Script Deleted",
      description: "Script has been deleted from your library"
    });
  };

  const handleCopyScript = (script: any) => {
    navigator.clipboard.writeText(script.content);
    toast({
      title: "Script Copied",
      description: `"${script.title}" has been copied to clipboard`
    });
  };



  const getStepSummary = (step: any) => {
    switch (step.id) {
      case 'script-selection':
        return step.data.selectedScript ? `Selected: ${step.data.selectedScript.title}` : 'No script selected';
      case 'chunk-creation':
        return `${step.data.selectedChunks?.length || 0} chunks selected`;
      case 'highlight-analysis':
        return `${step.data.highlights?.length || 0} highlights marked`;
      case 'strategy-selection':
        return `${step.data.selectedStrategies?.length || 0} strategies chosen`;
      case 'configuration':
        return step.data.title ? 'Configuration complete' : 'Not configured';
      case 'generation':
        return step.data.finalInstructions ? 'Instructions generated' : 'Not generated';
      default:
        return '';
    }
  };

  return (
    <div className="space-y-6">
      {/* Final Instructions Generation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            Generate ChatGPT Instructions
          </CardTitle>
          <CardDescription>
            Compile all your workflow selections into comprehensive ChatGPT instructions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left: Workflow Summary */}
            <div className="space-y-4">
              <h3 className="font-medium">Workflow Summary</h3>
              <div className="space-y-3">
                {workflowState.steps.map((step, index) => (
                  <div key={step.id} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                      step.completed ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600'
                    }`}>
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{step.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {getStepSummary(step)}
                      </div>
                    </div>
                    {step.completed && <CheckCircle className="h-4 w-4 text-green-500" />}
                  </div>
                ))}
              </div>

              <div className="space-y-3">
                <Button onClick={handleGenerateInstructions} className="w-full" size="lg">
                  <Wand2 className="h-4 w-4 mr-2" />
                  Generate ChatGPT Instructions
                </Button>

                <div className="text-center border-t pt-3">
                  <p className="text-sm text-muted-foreground mb-2">🚀 Skip the workflow entirely:</p>
                  <Button
                    onClick={() => {
                      // Generate with SlickV1 template directly
                      const slickV1Prompt = `You are a highly experienced and compassionate financial expert, specializing in government benefit programs and financial relief for individuals on fixed incomes, including Social Security, Supplemental Security Income (SSI), Social Security Disability Insurance (SSDI), and Veterans Affairs (VA) beneficiaries.

Your primary objective is to generate an expert financial opinion that is incredibly beneficial, empowering, and actionable for these beneficiaries, regardless of the specific financial topic.

In crafting this opinion, you MUST adopt the following style and communication methods:

• Urgent & Direct Tone: Convey paramount importance and time-sensitivity. Address audience directly using "Hello everyone," "you need to hear this," "Are you one of the millions of Americans," and "This is urgent, this is real." Emphasize what beneficiaries "need to know right now" and "Don't let this opportunity slip away."

• Informative & Detailed Breakdown: Provide clear, comprehensive explanations. Use rhetorical questions: "What exactly is this payment?", "Who qualifies?", "Do you need to apply?", "Is this taxable?", "When is this money coming?", "What should you do with this money?" Start with "Let's start with the basics" or "Let's break down exactly what this new bill means for you."

• Empathetic & Reassuring Language: Acknowledge struggles using "struggling to stay afloat", "hit hardest by inflation", "much needed relief." Offer hope with "better days are here and brighter ones, maybe just around the corner." Use "your concern is completely valid" and "it's about restoring fairness and dignity."

• Action-Oriented & Practical Advice: Guide on concrete steps. Prioritize verifying and updating contact/banking information with SSA, VA, IRS. Recommend prudent financial management: high-interest debt repayment, emergency savings, financial advisor consultation.

• Structure & Emphasis: Use bolded headings and bullet points extensively. Strong introductory remarks that grab attention. Concluding remarks that summarize key takeaways. Emphasize with "critical update", "absolutely critical", "most important question", "huge news", "paramount importance", "life-changing opportunity."

• Proactive Scam Warnings: Clear warnings about scams. State what legitimate agencies never ask for: personal info via unsolicited contact, fees, gift cards, wire transfers, cryptocurrency, cash.

• Forward-Looking Perspective: Touch on future possibilities, legislative discussions, potential reforms. Foster awareness and hope without false expectations.

• Strong Call to Action: Encourage subscription and notifications. Prompt engagement through likes, shares, comments. "Drop a comment below with any questions" or "Let me know how this money would help you."

Generate a complete script that follows all the guidelines above. Make it engaging, informative, and actionable for the target audience.`;

                      navigator.clipboard.writeText(slickV1Prompt);
                      toast({
                        title: "✅ SlickV1 Prompt Copied!",
                        description: "Your complete SlickV1 prompt is ready to paste into ChatGPT",
                        duration: 5000
                      });
                    }}
                    variant="outline"
                    className="w-full"
                  >
                    📋 Copy SlickV1 Prompt Instantly
                  </Button>
                </div>
              </div>
            </div>

            {/* Right: Generated Instructions */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">ChatGPT Instructions</h3>
                {generatedInstructions && (
                  <Button onClick={handleCopyInstructions} variant="outline">
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Instructions
                  </Button>
                )}
              </div>

              {generatedInstructions ? (
                <div className="space-y-4">
                  <Textarea
                    value={generatedInstructions}
                    readOnly
                    rows={15}
                    className="font-mono text-sm"
                  />
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="font-medium text-green-900 mb-2">Next Steps:</h4>
                    <ol className="text-sm text-green-800 space-y-1">
                      <li>1. Copy the instructions above</li>
                      <li>2. Open ChatGPT in a new tab</li>
                      <li>3. Paste the instructions and generate your script</li>
                      <li>4. Come back here to save the generated script</li>
                    </ol>
                  </div>
                </div>
              ) : (
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h4 className="font-medium mb-2">Ready to Generate</h4>
                  <p className="text-sm text-muted-foreground">
                    Complete the workflow steps and click "Generate Instructions"
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Script Storage */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Save className="h-5 w-5" />
            Save Generated Scripts
          </CardTitle>
          <CardDescription>
            Store multiple versions of your ChatGPT-generated scripts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left: Save New Script */}
            <div className="space-y-4">
              <h3 className="font-medium">Save New Script</h3>
              <div className="space-y-4 p-4 border rounded-lg">
                <div className="space-y-2">
                  <Label htmlFor="scriptTitle">Script Title *</Label>
                  <Input
                    id="scriptTitle"
                    placeholder="Enter title for your generated script"
                    value={newScriptTitle}
                    onChange={(e) => setNewScriptTitle(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="scriptContent">Generated Script Content *</Label>
                  <Textarea
                    id="scriptContent"
                    placeholder="Paste your ChatGPT-generated script here..."
                    value={newScriptContent}
                    onChange={(e) => setNewScriptContent(e.target.value)}
                    rows={12}
                    className="min-h-[300px]"
                  />
                </div>

                <Button onClick={handleSaveScript} className="w-full">
                  <Save className="h-4 w-4 mr-2" />
                  Save Script Version
                </Button>
              </div>
            </div>

            {/* Right: Saved Scripts Library */}
            <div className="space-y-4">
              <h3 className="font-medium">Saved Scripts ({savedScripts.length})</h3>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {savedScripts.map(script => (
                    <Card key={script.id} className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium">{script.title}</h4>
                          <p className="text-xs text-muted-foreground mt-1">
                            Version {script.version} • {new Date(script.dateGenerated).toLocaleDateString()}
                          </p>
                          <p className="text-sm mt-2 line-clamp-3">
                            {script.content.substring(0, 150)}...
                          </p>

                          {/* Workflow Summary */}
                          <div className="mt-2 space-y-1">
                            <div className="text-xs text-muted-foreground">
                              Source: {script.sourceWorkflow.sourceScript}
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {script.sourceWorkflow.selectedChunks.slice(0, 2).map((chunk: string) => (
                                <Badge key={chunk} variant="outline" className="text-xs">
                                  {chunk}
                                </Badge>
                              ))}
                              {script.sourceWorkflow.selectedChunks.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{script.sourceWorkflow.selectedChunks.length - 2} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-1 ml-4">
                          <Button onClick={() => handleCopyScript(script)} variant="ghost" size="sm">
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button onClick={() => handleDeleteScript(script.id)} variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                  {savedScripts.length === 0 && (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No scripts saved yet</p>
                      <p className="text-sm text-muted-foreground">
                        Generate instructions, create scripts with ChatGPT, then save them here
                      </p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GenerationModule;
