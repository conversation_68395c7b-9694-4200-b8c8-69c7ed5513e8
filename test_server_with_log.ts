// Test server with custom log file
import express from "express";
import fs from "fs";
import path from "path";

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), "test_logs");
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Set up custom logger
const logFile = path.join(logsDir, "test_server.log");
const logger = fs.createWriteStream(logFile, { flags: "a" });

function log(message: string) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} - ${message}\n`;
  logger.write(logMessage);
  console.log(logMessage);
}

try {
  log("Starting test server with custom log file...");
  
  // Create Express app
  const app = express();
  log("Express app created");
  
  // Set up middleware
  app.use(express.json());
  app.use(express.urlencoded({ extended: false }));
  log("Middleware set up");
  
  // Set up routes
  app.get("/", (req, res) => {
    log("Root route accessed");
    res.send("Test server is running!");
  });
  
  // Start the server
  const port = 5008;
  const server = app.listen(port, () => {
    log(`Test server running on port ${port}`);
  });
  
  // Automatically shut down after 30 seconds
  setTimeout(() => {
    log("Shutting down test server after 30 seconds");
    server.close(() => {
      log("Server closed");
      logger.end();
    });
  }, 30000);
  
} catch (error) {
  log(`Error starting test server: ${error}`);
  logger.end();
}
