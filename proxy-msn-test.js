import axios from 'axios';
import { <PERSON><PERSON><PERSON> } from 'jsdom';

async function scrapeMSNWithProxy(url) {
  try {
    console.log('Scraping URL with proxy:', url);
    
    // Extract the article ID from the URL
    const articleIdMatch = url.match(/\/ar-([A-Za-z0-9]+)/);
    if (!articleIdMatch || !articleIdMatch[1]) {
      console.log('Could not extract article ID from URL');
      return null;
    }
    
    const articleId = articleIdMatch[1];
    console.log('Article ID:', articleId);
    
    // Try different proxy services
    const proxyServices = [
      {
        name: 'AllOrigins',
        url: `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`
      },
      {
        name: '12ft.io',
        url: `https://12ft.io/proxy?q=${encodeURIComponent(url)}`
      },
      {
        name: 'Bypass Paywalls',
        url: `https://bypass.paywalls.news/proxy?url=${encodeURIComponent(url)}`
      }
    ];
    
    for (const service of proxyServices) {
      try {
        console.log(`Trying proxy service: ${service.name} - ${service.url}`);
        
        const response = await axios.get(service.url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
          },
          timeout: 20000
        });
        
        console.log(`Response received from ${service.name}, status:`, response.status);
        
        // Use JSDOM to parse the HTML
        const dom = new JSDOM(response.data);
        const document = dom.window.document;
        
        // Get the title
        const title = document.querySelector('h1')?.textContent.trim() || document.title.trim();
        console.log('Found title:', title);
        
        if (!title || title === 'MSN') {
          console.log('No valid title found, skipping this proxy service');
          continue;
        }
        
        // Get the author info
        const authorInfo = document.querySelector('.authorinfo')?.textContent.trim() || 
                          document.querySelector('.byline')?.textContent.trim() || 
                          document.querySelector('[data-author-name]')?.textContent.trim() ||
                          document.querySelector('[itemprop="author"]')?.textContent.trim();
        
        // Look for article content
        let content = '';
        
        // MSN-specific content selectors
        const contentSelectors = [
          '[data-testid="article-body-content"]',
          '[data-testid="articleBodyContent"]',
          '.articlebody',
          '.article-body',
          '.article-content',
          '.content-article',
          '.articlecontent',
          '#content-main',
          '.main-content',
          '.article-page-content',
          '.page-content',
          '.article-text',
          '.article',
          'article'
        ];
        
        for (const selector of contentSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            content = element.innerHTML || '';
            console.log(`Found content using selector: ${selector}`);
            break;
          }
        }
        
        // If no content found with specific selectors, try paragraphs
        if (!content || content.length < 100) {
          console.log('No content found with specific selectors, trying paragraphs');
          const paragraphs = Array.from(document.querySelectorAll('p'));
          const filteredParagraphs = paragraphs.filter(p => {
            const text = p.textContent;
            return text.length > 30 && !text.includes('ADVERTISEMENT');
          });
          
          content = filteredParagraphs.map(p => `<p>${p.innerHTML}</p>`).join('');
          console.log(`Found ${filteredParagraphs.length} paragraphs`);
        }
        
        // If we found content, return it
        if (content && content.length > 100) {
          let fullContent = '';
          if (title) fullContent += `<h1>${title}</h1>`;
          if (authorInfo) fullContent += `<p class="article-author">${authorInfo}</p>`;
          fullContent += content;
          
          console.log(`Found content with length: ${fullContent.length}`);
          console.log('Content preview:', fullContent.substring(0, 500));
          return fullContent;
        }
      } catch (error) {
        console.error(`Error with proxy service ${service.name}:`, error.message);
      }
    }
    
    console.log('All proxy services failed');
    return null;
  } catch (error) {
    console.error('Error scraping MSN article with proxy:', error.message);
    return null;
  }
}

// Test with the URL
const url = 'https://www.msn.com/en-us/money/retirement/social-security-checks-of-up-to-5-108-going-out-this-week/ar-AA1DKbYM';
scrapeMSNWithProxy(url).then(content => {
  if (content) {
    console.log('Successfully scraped content with proxy!');
  } else {
    console.log('Failed to scrape content with proxy');
  }
});
