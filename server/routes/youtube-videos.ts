import { Router } from 'express';
import { z } from 'zod';
import { IStorage } from '../storage';

const router = Router();
let storage: IStorage;

export function setupYoutubeVideosRoutes(storageInstance: IStorage) {
  storage = storageInstance;
  return router;
}

// Get a specific YouTube video
router.get('/:id', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const videoId = req.params.id;
    console.log(`Server: Fetching video with ID ${videoId}`);

    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: "YouTube video not found" });
    }

    console.log(`Server: Returning video with ID ${videoId}`);
    res.json(video);
  } catch (error) {
    console.error('Error fetching video:', error);
    res.status(500).json({ message: "Failed to fetch video" });
  }
});

// Update a YouTube video's view count
router.post('/:id/update-view-count', async (req, res) => {
  if (!req.isAuthenticated()) return res.sendStatus(401);

  try {
    const videoId = req.params.id;
    const { viewCount } = req.body;

    if (viewCount === undefined || typeof viewCount !== 'number') {
      return res.status(400).json({ message: "View count is required and must be a number" });
    }

    console.log(`Server: Updating view count for video ${videoId} to ${viewCount}`);

    // Get the video to check if it exists
    const video = await storage.getYoutubeVideo(videoId);

    if (!video) {
      return res.status(404).json({ message: "YouTube video not found" });
    }

    // Update the view count
    await storage.updateYoutubeVideoMetadata(videoId, { viewCount }, true);

    console.log(`Server: Successfully updated view count for video ${videoId} to ${viewCount}`);
    res.json({ success: true, message: `Updated view count for video ${videoId} to ${viewCount}` });
  } catch (error) {
    console.error(`Error updating view count for video ${req.params.id}:`, error);
    res.status(500).json({ message: "Failed to update view count" });
  }
});

export default router;
