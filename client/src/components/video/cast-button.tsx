import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { castEvents, castVideo, disconnectCastSession, getCurrentSession, isCastingAvailable } from '@/lib/cast';
import { initializeCastApiSingleton, onCastApiReady } from '@/lib/cast-singleton';
import { Cast, Loader2, MonitorSmartphone } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Video } from '@shared/schema';

interface CastButtonProps {
  video: Video;
  className?: string;
  size?: 'default' | 'sm' | 'lg' | 'icon';
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  iconSize?: number;
  showTooltip?: boolean;
  showDropdown?: boolean;
  onCastSuccess?: () => void;
}

export function CastButton({
  video,
  className,
  size = 'icon',
  variant = 'ghost',
  iconSize = 4,
  showTooltip = true,
  showDropdown = true,
  onCastSuccess
}: CastButtonProps) {
  const [isAvailable, setIsAvailable] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentVideoId, setCurrentVideoId] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    // Initialize the Cast API using our singleton wrapper
    initializeCastApiSingleton().catch(error => {
      console.error('Error initializing Cast API:', error);
    });

    // Register a callback for when the Cast API is ready
    onCastApiReady(() => {
      if (isMounted) {
        setIsAvailable(isCastingAvailable());
        setIsConnected(!!getCurrentSession());
      }
    });

    // Listen for cast availability changes
    const availabilityHandler = (available: boolean) => {
      if (isMounted) setIsAvailable(available);
    };

    // Listen for cast connection changes
    const connectedHandler = () => {
      if (isMounted) {
        setIsConnected(true);
        setIsLoading(false);
      }
    };

    // Listen for cast disconnection
    const disconnectedHandler = () => {
      if (isMounted) {
        setIsConnected(false);
        setCurrentVideoId(null);
      }
    };

    // Check initial state immediately
    setIsAvailable(isCastingAvailable());
    setIsConnected(!!getCurrentSession());

    // Subscribe to events
    castEvents.on('availability', availabilityHandler);
    castEvents.on('connected', connectedHandler);
    castEvents.on('disconnected', disconnectedHandler);

    // Cleanup
    return () => {
      isMounted = false;
      castEvents.off('availability', availabilityHandler);
      castEvents.off('connected', connectedHandler);
      castEvents.off('disconnected', disconnectedHandler);
    };
  }, []);

  const handleCastClick = async () => {
    if (!isAvailable) return;

    setIsLoading(true);
    try {
      if (isConnected && currentVideoId === video.id) {
        // If it's the same video, disconnect
        await disconnectCastSession();
      } else {
        // Cast the video
        await castVideo(video.id, video.title, video.thumbnail);
        setCurrentVideoId(video.id);
        if (onCastSuccess) onCastSuccess();
      }
    } catch (error) {
      console.error('Error with cast operation:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // If casting is not available, don't render the button
  if (!isAvailable && !isConnected) {
    return null;
  }

  const button = (
    <Button
      variant={variant}
      size={size}
      className={cn(
        isConnected && "text-primary",
        className
      )}
      onClick={handleCastClick}
      disabled={isLoading}
      aria-label={isConnected ? "Disconnect from Chromecast" : "Cast to Chromecast"}
    >
      {isLoading ? (
        <Loader2 className={`h-${iconSize} w-${iconSize} animate-spin`} />
      ) : isConnected ? (
        <MonitorSmartphone className={`h-${iconSize} w-${iconSize}`} />
      ) : (
        <Cast className={`h-${iconSize} w-${iconSize}`} />
      )}
    </Button>
  );

  // Add tooltip if needed
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {button}
          </TooltipTrigger>
          <TooltipContent>
            {isConnected ?
              (currentVideoId === video.id ? "Disconnect from Chromecast" : "Cast this video") :
              "Cast to Chromecast"
            }
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return button;
}
