import { useState, useEffect, useCallback, useRef } from "react";
import { toast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { RefreshCw, Pause, Clock } from "lucide-react";
import { logger, LogLevel } from '@/lib/logger';

interface AutoRefreshProps {
  isActive: boolean; // Is the realtime tab active
  onRefresh: () => Promise<void>; // Function to call for refreshing data
  videos: any[]; // Current videos being displayed
  updatePreviousVphValues: (videos: any[], isPreRefresh: boolean) => void;
  updateCache: (videos: any[]) => void;
  compact?: boolean; // Whether to use a compact layout
  defaultInterval?: number; // Default refresh interval in seconds
}

export function RealtimeAutoRefresh({
  isActive,
  onRefresh,
  videos,
  updatePreviousVphValues,
  updateCache,
  compact = false,
  defaultInterval = 120
}: AutoRefreshProps) {
  // State for auto-refresh settings
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState<boolean>(() => {
    // Try to load auto-refresh state from localStorage
    const savedState = localStorage.getItem('realtimeAutoRefreshEnabled');
    // If there's a saved state, use it; otherwise, default to true
    return savedState !== null ? savedState === 'true' : true;
  });

  const [autoRefreshInterval, setAutoRefreshInterval] = useState<number>(() => {
    // Try to load auto-refresh interval from localStorage
    const savedInterval = localStorage.getItem('realtimeAutoRefreshInterval');
    // If there's a saved interval, use it; otherwise, use the provided defaultInterval
    return savedInterval ? parseInt(savedInterval) : defaultInterval;
  });

  // State for tracking refresh status
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const [lastRefreshTimestamp, setLastRefreshTimestamp] = useState(0);
  const [autoRefreshIntervalId, setAutoRefreshIntervalId] = useState<NodeJS.Timeout | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [pauseEndTime, setPauseEndTime] = useState<Date | null>(null);
  const [debugMode, setDebugMode] = useState(false);
  const [highContrastMode, setHighContrastMode] = useState(true); // Enable high contrast by default

  // New countdown implementation
  const [nextRefreshTime, setNextRefreshTime] = useState<Date | null>(null);
  const [countdownDisplay, setCountdownDisplay] = useState("--:--");

  // Reference for the countdown timer
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Track if a refresh is in progress to prevent multiple refreshes
  const refreshInProgressRef = useRef(false);

  // Function to perform the refresh with throttling
  const handleRefresh = useCallback(async () => {
    // Get stack trace to identify what triggered the refresh
    const stack = new Error().stack;
    console.log(`%c REFRESH TRIGGERED at ${new Date().toLocaleTimeString()}`, 'background: #ff0000; color: white; font-weight: bold; padding: 2px 5px;');
    console.log(`%c Refresh trigger stack trace:`, 'font-weight: bold;');
    console.log(stack);

    const now = Date.now();
    const minTimeBetweenRefreshes = 20000; // 20 seconds minimum between manual refreshes to prevent refresh loops and UI glitches

    // Check if a refresh is already in progress using the ref
    if (refreshInProgressRef.current) {
      logger.verbose("Refresh already in progress (tracked by ref), skipping");
      console.log(`%c Refresh skipped: already in progress (ref)`, 'color: orange;');
      return;
    }

    // Prevent rapid successive refreshes
    if (now - lastRefreshTimestamp < minTimeBetweenRefreshes) {
      const waitTime = Math.ceil((minTimeBetweenRefreshes - (now - lastRefreshTimestamp)) / 1000);
      toast({
        title: "Please wait",
        description: `You can refresh again in ${waitTime} seconds`,
        duration: 3000
      });
      console.log(`%c Refresh skipped: too soon (${waitTime}s remaining)`, 'color: orange;');
      return;
    }

    // Prevent refresh if already refreshing
    if (isRefreshing) {
      logger.verbose("Refresh already in progress (tracked by state), skipping");
      console.log(`%c Refresh skipped: already in progress (state)`, 'color: orange;');
      return;
    }

    // Set both state and ref to indicate refresh is in progress
    setIsRefreshing(true);
    refreshInProgressRef.current = true;
    setLastRefreshTimestamp(now);

    try {
      logger.verbose("Starting refresh operation...");

      // Store current VPH values before refreshing
      if (videos.length > 0) {
        // Make a copy of the videos array to avoid mutation issues
        const videosCopy = [...videos];
        updatePreviousVphValues(videosCopy, true);
        updateCache(videosCopy);
      }

      // Perform the refresh with a single operation
      // This will be handled by the parent component
      logger.verbose("Calling parent refresh function");
      await onRefresh();
      logger.verbose("Parent refresh function completed");

      // Update last refresh time
      const refreshTime = new Date();
      setLastRefreshTime(refreshTime);

      // Set the next refresh time after successful refresh
      if (autoRefreshEnabled && isActive && !isPaused) {
        // Wait a short delay to ensure the refresh is complete before starting the countdown
        setTimeout(() => {
          // Calculate the next refresh time
          const now = new Date();
          const nextTime = new Date(now.getTime() + autoRefreshInterval * 1000);

          // Set the next refresh time
          setNextRefreshTime(nextTime);

          // Calculate and format the countdown display
          const totalSeconds = autoRefreshInterval;
          const minutes = Math.floor(totalSeconds / 60);
          const seconds = totalSeconds % 60;
          const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;

          // Update the countdown display
          setCountdownDisplay(display);

          logger.verbose(`Set next refresh time to: ${nextTime.toLocaleTimeString()} (${display} from now)`);

          // Clear any existing countdown timer
          if (countdownTimerRef.current) {
            clearInterval(countdownTimerRef.current);
            countdownTimerRef.current = null;
          }

          // Set up a new countdown timer
          const updateCountdown = () => {
            const currentTime = new Date();
            const diffMs = nextTime.getTime() - currentTime.getTime();

            if (diffMs <= 0) {
              // Time's up, clear the timer
              if (countdownTimerRef.current) {
                clearInterval(countdownTimerRef.current);
                countdownTimerRef.current = null;
              }
              return;
            }

            // Calculate minutes and seconds
            const remainingSeconds = Math.floor(diffMs / 1000);
            const mins = Math.floor(remainingSeconds / 60);
            const secs = remainingSeconds % 60;

            // Format the countdown display
            const newDisplay = `${mins}:${secs.toString().padStart(2, '0')}`;
            setCountdownDisplay(newDisplay);
          };

          // Update immediately
          updateCountdown();

          // Set up interval for updates
          const timer = setInterval(updateCountdown, 1000);
          countdownTimerRef.current = timer;
        }, 500);
      }

      // Delay the toast to prevent UI overload
      // This ensures the toast appears after the UI has stabilized
      setTimeout(() => {
        toast({
          title: "Refresh Complete",
          description: `Updated video data at ${refreshTime.toLocaleTimeString()}`,
          duration: 3000
        });
      }, 1000);
    } catch (error) {
      logger.error("Refresh failed:", error);
      toast({
        title: "Refresh Failed",
        description: "Could not update video data. Please try again.",
        variant: "destructive",
        duration: 5000
      });
    } finally {
      // Clear the refresh in progress indicators with a shorter delay
      // This ensures the UI has time to stabilize but doesn't block the next refresh for too long
      setTimeout(() => {
        logger.verbose("Clearing refresh in progress state");
        setIsRefreshing(false);
        refreshInProgressRef.current = false;
      }, 1000);
    }
  }, [lastRefreshTimestamp, videos, onRefresh, updatePreviousVphValues, updateCache, isRefreshing, autoRefreshEnabled, isActive, isPaused, autoRefreshInterval, compact]);

  // Function to handle auto-refresh toggle
  const handleAutoRefreshToggle = useCallback((enabled: boolean) => {
    if (isPaused && enabled) {
      toast({
        title: "Auto-refresh is paused",
        description: "Resume auto-refresh to enable it again",
        duration: 3000
      });
      return;
    }

    logger.verbose(`${enabled ? 'Enabling' : 'Disabling'} auto-refresh`);
    console.log(`Auto-refresh toggle: ${enabled ? 'ON' : 'OFF'}`);
    setAutoRefreshEnabled(enabled);

    // Save to localStorage
    localStorage.setItem('realtimeAutoRefreshEnabled', enabled.toString());

    // Clear any existing interval
    if (autoRefreshIntervalId) {
      logger.verbose("Clearing existing auto-refresh interval");
      clearInterval(autoRefreshIntervalId);
      setAutoRefreshIntervalId(null);
    }

    // Clear any existing countdown timer
    if (countdownTimerRef.current) {
      logger.verbose("Clearing existing countdown timer");
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }

    // Reset countdown and set next refresh time
    if (enabled) {
      // Calculate the next refresh time
      const now = new Date();
      const nextTime = new Date(now.getTime() + autoRefreshInterval * 1000);

      // Set the next refresh time
      setNextRefreshTime(nextTime);

      // Calculate and format the countdown display
      const totalSeconds = autoRefreshInterval;
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;

      // Update the countdown display
      setCountdownDisplay(display);

      logger.verbose(`Set next refresh time to: ${nextTime.toLocaleTimeString()} (${display} from now)`);

      // We don't need to set up an interval here anymore
      // The main useEffect will handle setting up the timer when autoRefreshEnabled changes
      if (isActive && !isPaused) {
        logger.verbose(`Auto-refresh enabled, the main effect will set up the timer`);

        // Perform an initial refresh immediately
        console.log("Scheduling initial refresh after enabling auto-refresh");
        setTimeout(() => {
          if (!refreshInProgressRef.current) {
            logger.verbose("Performing initial refresh after enabling auto-refresh");
            console.log("Executing initial refresh");
            handleRefresh().catch(err => {
              logger.error("Error during initial auto-refresh:", err);
              console.error("Initial refresh error:", err);
            });
          } else {
            console.log("Skipping initial refresh - refresh already in progress");
          }
        }, 100);
      }

      // Show a toast to confirm auto-refresh is enabled
      toast({
        title: "Auto-refresh Enabled",
        description: `Will refresh every ${autoRefreshInterval} seconds`,
        duration: 3000
      });
    } else {
      // Show a toast to confirm auto-refresh is disabled
      toast({
        title: "Auto-refresh Disabled",
        description: "Manual refresh only",
        duration: 3000
      });
    }

    // Log the change
    logger.verbose(`Auto-refresh ${enabled ? 'enabled' : 'disabled'} with interval: ${autoRefreshInterval} seconds`);
  }, [autoRefreshInterval, autoRefreshIntervalId, isPaused, isActive, handleRefresh, compact]);

  // Function to handle interval change
  const handleIntervalChange = useCallback((value: string) => {
    if (value === "manual") {
      // If manual is selected, disable auto-refresh
      if (autoRefreshEnabled) {
        handleAutoRefreshToggle(false);
      }
      return;
    }

    const seconds = parseInt(value);
    logger.verbose(`Changing auto-refresh interval from ${autoRefreshInterval} to ${seconds} seconds`);

    setAutoRefreshInterval(seconds);

    // Save to localStorage
    localStorage.setItem('realtimeAutoRefreshInterval', seconds.toString());

    // Clear any existing countdown timer
    if (countdownTimerRef.current) {
      logger.verbose("Clearing existing countdown timer during interval change");
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }

    // Reset countdown and set next refresh time with new interval
    if (autoRefreshEnabled) {
      // Calculate the next refresh time
      const now = new Date();
      const nextTime = new Date(now.getTime() + seconds * 1000);

      // Set the next refresh time
      setNextRefreshTime(nextTime);

      // Calculate and format the countdown display
      const totalSeconds = seconds;
      const minutes = Math.floor(totalSeconds / 60);
      const secondsDisplay = totalSeconds % 60;
      const display = `${minutes}:${secondsDisplay.toString().padStart(2, '0')}`;

      // Update the countdown display
      setCountdownDisplay(display);

      logger.verbose(`Set next refresh time to: ${nextTime.toLocaleTimeString()} (${display} from now)`);
    }

    // If auto-refresh is enabled, restart the interval with the new value
    if (autoRefreshEnabled) {
      logger.verbose("Restarting auto-refresh with new interval");
      handleAutoRefreshToggle(false); // Stop current interval

      // Use a short timeout to ensure state updates have propagated
      setTimeout(() => {
        handleAutoRefreshToggle(true); // Start new interval on next tick

        // Force update the countdown display after a short delay
        setTimeout(() => {
          // Calculate the next refresh time
          const now = new Date();
          const nextTime = new Date(now.getTime() + seconds * 1000);

          // Set the next refresh time
          setNextRefreshTime(nextTime);

          // Calculate and format the countdown display
          const totalSeconds = seconds;
          const minutes = Math.floor(totalSeconds / 60);
          const secondsDisplay = totalSeconds % 60;
          const display = `${minutes}:${secondsDisplay.toString().padStart(2, '0')}`;

          // Update the countdown display
          setCountdownDisplay(display);

          logger.verbose(`Forced countdown update after interval change: ${display}`);
        }, 100);
      }, 100);
    }

    // Show a toast to confirm the change
    toast({
      title: "Auto-refresh Interval Changed",
      description: `Set to ${seconds} seconds`,
      duration: 3000
    });

    logger.verbose(`Auto-refresh interval changed to ${seconds} seconds`);
  }, [autoRefreshEnabled, handleAutoRefreshToggle, autoRefreshInterval, compact]);

  // Function to calculate suggested refresh interval based on video activity
  // This only returns a suggestion, it doesn't override the user's setting
  const calculateSuggestedRefreshInterval = useCallback((videos) => {
    if (!videos || videos.length === 0) return autoRefreshInterval;

    // Check if any videos have high VPH (views per hour)
    const hasHighActivityVideos = videos.some(video => video.vph > 1000);
    const hasModerateActivityVideos = videos.some(video => video.vph > 500);

    if (hasHighActivityVideos) {
      return 60; // Suggest 1 minute for high activity
    } else if (hasModerateActivityVideos) {
      return 120; // Suggest 2 minutes for moderate activity
    }

    return autoRefreshInterval; // Use user setting for low activity
  }, [autoRefreshInterval]);

  // Function to temporarily pause auto-refresh
  const handlePauseAutoRefresh = useCallback((minutes: number) => {
    const wasEnabled = autoRefreshEnabled;

    // Disable auto-refresh
    handleAutoRefreshToggle(false);
    setIsPaused(true);

    // Calculate and set pause end time
    const endTime = new Date();
    endTime.setMinutes(endTime.getMinutes() + minutes);
    setPauseEndTime(endTime);

    toast({
      title: `Auto-refresh paused for ${minutes} minutes`,
      description: `It will resume at ${endTime.toLocaleTimeString()}`,
      duration: 5000
    });

    // Resume after the pause period
    const pauseTimer = setTimeout(() => {
      setIsPaused(false);
      setPauseEndTime(null);

      if (wasEnabled) {
        handleAutoRefreshToggle(true);
        toast({
          title: "Auto-refresh resumed",
          description: `Continuing with ${autoRefreshInterval} second interval`,
          duration: 3000
        });
      }
    }, minutes * 60 * 1000);

    // Store the timer ID for cleanup
    return pauseTimer;
  }, [autoRefreshEnabled, autoRefreshInterval, handleAutoRefreshToggle]);

  // Main effect for auto-refresh functionality - optimized for CPU usage
  useEffect(() => {
    // Always clean up any existing timers first to prevent duplicates
    if (autoRefreshIntervalId) {
      logger.verbose(`Cleaning up existing auto-refresh timer ID: ${autoRefreshIntervalId}`);
      clearInterval(autoRefreshIntervalId);
      clearTimeout(autoRefreshIntervalId as unknown as NodeJS.Timeout);
      setAutoRefreshIntervalId(null);
    }

    // Only proceed if all conditions are met
    if (!isActive || isPaused || !autoRefreshEnabled || autoRefreshInterval <= 0) {
      logger.verbose("Not setting up auto-refresh timer - conditions not met");
      return;
    }

    // Use a longer minimum interval to reduce CPU usage
    const effectiveInterval = Math.max(autoRefreshInterval, compact ? 60 : 30); // At least 30 seconds, or 60 if compact

    logger.verbose(`Setting up NEW auto-refresh with interval: ${effectiveInterval} seconds`);

    // Calculate the exact next refresh time based on the current time and interval
    // This ensures we have a consistent refresh schedule
    const now = new Date();
    const exactNextRefreshTime = new Date(now.getTime() + effectiveInterval * 1000);

    // Update the next refresh time state
    setNextRefreshTime(exactNextRefreshTime);
    logger.verbose(`Setting exact next refresh time to: ${exactNextRefreshTime.toLocaleTimeString()}`);

    // Calculate the exact milliseconds until the next refresh
    const msUntilNextRefresh = effectiveInterval * 1000;

    // Create a single timeout for the next refresh instead of an interval
    // This ensures more precise timing and prevents drift
    console.log(`%c SCHEDULING AUTO-REFRESH TIMER for ${new Date(now.getTime() + msUntilNextRefresh).toLocaleTimeString()}`, 'background: #4CAF50; color: white; font-weight: bold; padding: 2px 5px;');
    console.log(`%c Timer will trigger in ${msUntilNextRefresh/1000} seconds (${effectiveInterval} seconds interval)`, 'color: #4CAF50;');

    const newTimeoutId = setTimeout(() => {
      console.log(`%c AUTO-REFRESH TIMER FIRED at ${new Date().toLocaleTimeString()}`, 'background: #FF9800; color: white; font-weight: bold; padding: 2px 5px;');

      // Skip if refresh is already in progress
      if (refreshInProgressRef.current) {
        logger.verbose("Auto-refresh timeout triggered but refresh already in progress, skipping");
        console.log(`%c Timer skipped refresh: already in progress (ref)`, 'color: orange;');
        return;
      }

      // Skip if conditions have changed
      if (!isActive || !autoRefreshEnabled || isPaused) {
        logger.verbose("Auto-refresh timeout triggered but conditions no longer valid, skipping");
        console.log(`%c Timer skipped refresh: conditions changed (active: ${isActive}, enabled: ${autoRefreshEnabled}, paused: ${isPaused})`, 'color: orange;');
        return;
      }

      logger.verbose(`Auto-refresh timeout triggered at ${new Date().toLocaleTimeString()}`);

      // Execute the refresh
      if (!refreshInProgressRef.current && isActive && autoRefreshEnabled && !isPaused) {
        logger.verbose("Executing scheduled auto-refresh");
        console.log(`%c Timer executing refresh`, 'color: #FF9800; font-weight: bold;');
        handleRefresh().catch(err => {
          logger.error("Error during scheduled auto-refresh:", err);
          console.error("Error during scheduled auto-refresh:", err);
        });
      } else {
        logger.verbose("Skipping scheduled auto-refresh - conditions changed");
        console.log(`%c Timer skipped refresh: conditions changed in double-check`, 'color: orange;');
      }
    }, msUntilNextRefresh);

    // Store the new timeout ID (we're using the same state variable for simplicity)
    logger.verbose(`New auto-refresh timeout set with ID: ${newTimeoutId}`);
    setAutoRefreshIntervalId(newTimeoutId as unknown as NodeJS.Timeout);

    // Clean up on unmount or when dependencies change
    return () => {
      logger.verbose(`Cleaning up auto-refresh timeout ID: ${newTimeoutId}`);
      clearTimeout(newTimeoutId);
    };
  }, [isActive, isPaused, autoRefreshEnabled, autoRefreshInterval, handleRefresh, compact]);

  // Simplified countdown timer implementation
  useEffect(() => {
    // Clear any existing timer
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }

    // Only set up timer if auto-refresh is enabled and we're on the active tab
    if (!autoRefreshEnabled || !isActive || isPaused) {
      setCountdownDisplay("--:--");
      return;
    }

    // Initialize countdown
    let remainingSeconds = autoRefreshInterval;

    // Function to update the countdown display
    const updateCountdown = () => {
      if (remainingSeconds <= 0) {
        // Reset for next cycle
        remainingSeconds = autoRefreshInterval;
      }

      const minutes = Math.floor(remainingSeconds / 60);
      const seconds = remainingSeconds % 60;
      const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      setCountdownDisplay(display);

      remainingSeconds--;
    };

    // Update immediately
    updateCountdown();

    // Set up timer to update every second
    const timer = setInterval(updateCountdown, 1000);
    countdownTimerRef.current = timer;

    // Clean up on unmount or when dependencies change
    return () => {
      clearInterval(timer);
      countdownTimerRef.current = null;
    };
  }, [autoRefreshEnabled, autoRefreshInterval, isActive, isPaused]);

  // Add a separate effect to monitor and log countdown changes
  useEffect(() => {
    if (autoRefreshEnabled && isActive && !isPaused) {
      logger.verbose(`Countdown display changed to: ${countdownDisplay}`);
    }
  }, [countdownDisplay, autoRefreshEnabled, isActive, isPaused]);

  // Effect to update the countdown display when the next refresh time changes
  useEffect(() => {
    if (nextRefreshTime && autoRefreshEnabled && isActive && !isPaused) {
      const now = new Date();
      const diffMs = nextRefreshTime.getTime() - now.getTime();

      if (diffMs > 0) {
        const totalSeconds = Math.floor(diffMs / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        setCountdownDisplay(display);
        logger.verbose(`Updated countdown display to ${display} based on next refresh time`);
      }
    }
  }, [nextRefreshTime, autoRefreshEnabled, isActive, isPaused]);

  // Special effect to initialize countdown immediately when component mounts
  useEffect(() => {
    // Only run this effect once when the component mounts
    logger.verbose(`Component mounted - initializing countdown`);
    console.log(`%c COMPONENT MOUNTED at ${new Date().toLocaleTimeString()}`, 'background: #2196F3; color: white; font-weight: bold; padding: 2px 5px;');
    console.log(`%c Auto-refresh enabled: ${autoRefreshEnabled}, isActive: ${isActive}, isPaused: ${isPaused}`, 'color: #2196F3;');

    if (autoRefreshEnabled && isActive && !isPaused) {
      logger.verbose(`Auto-refresh is enabled - setting up countdown`);
      console.log(`%c Setting up initial countdown`, 'color: #2196F3;');

      // Calculate the next refresh time
      const now = new Date();
      const nextTime = new Date(now.getTime() + autoRefreshInterval * 1000);

      // Set the next refresh time
      setNextRefreshTime(nextTime);

      // Calculate and format the countdown display
      const totalSeconds = autoRefreshInterval;
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;

      // Update the countdown display
      setCountdownDisplay(display);

      logger.verbose(`Set initial next refresh time to: ${nextTime.toLocaleTimeString()} (${display} from now)`);
      console.log(`%c Set initial next refresh time to: ${nextTime.toLocaleTimeString()} (${display} from now)`, 'color: #2196F3;');
    } else {
      logger.verbose(`Auto-refresh not enabled or active - not setting up countdown`);
      console.log(`%c Not setting up countdown - conditions not met`, 'color: #2196F3;');
      setCountdownDisplay("--:--");
    }

    // Make sure to clean up any existing timer when the component unmounts
    return () => {
      if (countdownTimerRef.current) {
        clearInterval(countdownTimerRef.current);
        countdownTimerRef.current = null;
        logger.verbose("Cleaned up countdown timer on unmount");
      }
    };
  }, []); // Empty dependency array means this runs once on mount

  // Effect for adaptive refresh rate suggestions
  useEffect(() => {
    if (!isActive || !autoRefreshEnabled || videos.length === 0) return;

    const suggestedInterval = calculateSuggestedRefreshInterval(videos);

    // Only suggest if significantly different from current setting
    if (suggestedInterval < autoRefreshInterval * 0.5) {
      toast({
        title: "High Activity Detected",
        description: `Consider setting a shorter refresh interval (${suggestedInterval}s) for more timely updates`,
        duration: 5000
      });
    }
  }, [videos, isActive, autoRefreshEnabled, autoRefreshInterval, calculateSuggestedRefreshInterval]);

  // Effect for background throttling
  useEffect(() => {
    // Function to handle visibility change
    const handleVisibilityChange = () => {
      if (!autoRefreshEnabled || !isActive) return;

      // When visibility changes, we'll just force a re-render of the main effect
      // by toggling and then restoring the autoRefreshEnabled state
      // This will cause the main timer to be recreated with the appropriate interval
      if (document.hidden) {
        logger.verbose("Page hidden, will use slower refresh rate");
        // We don't need to do anything here - the main effect will handle it
        // Just log for debugging purposes
      } else {
        logger.verbose("Page visible again, will use normal refresh rate");
        // Force the main effect to re-run by toggling autoRefreshEnabled
        if (autoRefreshEnabled && isActive) {
          // Clear any existing timer
          if (autoRefreshIntervalId) {
            clearInterval(autoRefreshIntervalId);
            clearTimeout(autoRefreshIntervalId as unknown as NodeJS.Timeout);
            setAutoRefreshIntervalId(null);
          }
        }
      }
    };

    // Add event listener
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Clean up
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [autoRefreshEnabled, isActive, autoRefreshIntervalId]);

  // Effect for debug mode keyboard shortcut
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+Shift+D to toggle debug mode
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        setDebugMode(prev => !prev);
        toast({
          title: `Debug mode ${!debugMode ? 'enabled' : 'disabled'}`,
          duration: 2000
        });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [debugMode]);

  // Format time remaining for pause
  const formatPauseTimeRemaining = () => {
    if (!pauseEndTime) return "";

    const now = new Date();
    const diffMs = pauseEndTime.getTime() - now.getTime();

    if (diffMs <= 0) return "Resuming...";

    const diffMins = Math.floor(diffMs / 60000);
    const diffSecs = Math.floor((diffMs % 60000) / 1000);

    return `${diffMins}:${diffSecs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={compact ? "space-y-1" : "space-y-4"}>
      {/* Compact layout */}
      {compact ? (
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Clear URL metrics in the parent component before refreshing
              if (typeof window !== 'undefined') {
                // Create and dispatch a custom event to notify the parent component
                const event = new CustomEvent('clearUrlMetrics', { detail: { message: 'Clear URL metrics before refresh' } });
                window.dispatchEvent(event);

                console.log('RealtimeAutoRefresh: Dispatched clearUrlMetrics event');

                // Clear all URL caches in localStorage
                const keys = Object.keys(localStorage);
                const urlCacheKeys = keys.filter(key =>
                  key.startsWith('ytr_videos_') ||
                  key.startsWith('ytr_videos_timestamp_')
                );

                urlCacheKeys.forEach(key => {
                  localStorage.removeItem(key);
                  console.log(`RealtimeAutoRefresh: Cleared cache for ${key}`);
                });
              }

              // Then proceed with the refresh
              handleRefresh();
            }}
            disabled={isRefreshing}
            className="h-7 px-2 py-0 text-xs"
          >
            {isRefreshing ? (
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
            ) : (
              <RefreshCw className="h-3 w-3 mr-1" />
            )}
            Refresh
          </Button>

          <div className="flex items-center gap-1">
            <Select
              value={autoRefreshEnabled ? autoRefreshInterval.toString() : "manual"}
              onValueChange={handleIntervalChange}
              disabled={isRefreshing || isPaused}
            >
              <SelectTrigger className="w-[100px] h-7 text-xs">
                <SelectValue placeholder="Refresh interval" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="manual">Manual only</SelectItem>
                <SelectItem value="30">30 seconds</SelectItem>
                <SelectItem value="60">1 minute</SelectItem>
                <SelectItem value="120">2 minutes</SelectItem>
                <SelectItem value="300">5 minutes</SelectItem>
                <SelectItem value="600">10 minutes</SelectItem>
                <SelectItem value="1200">20 minutes</SelectItem>
                <SelectItem value="1800">30 minutes</SelectItem>
                <SelectItem value="2400">40 minutes</SelectItem>
                <SelectItem value="3000">50 minutes</SelectItem>
                <SelectItem value="3600">1 hour</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-1">
              <span className="text-xs text-muted-foreground">Auto</span>
              <Switch
                checked={autoRefreshEnabled}
                onCheckedChange={handleAutoRefreshToggle}
                disabled={isRefreshing || autoRefreshInterval <= 0 || isPaused}
                className="h-3 w-6"
              />
            </div>
          </div>

          {/* Pause button */}
          {autoRefreshEnabled && !isPaused && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePauseAutoRefresh(5)}
              disabled={isRefreshing}
              className="h-7 px-2 py-0 text-xs"
            >
              <Pause className="h-3 w-3 mr-1" />
              Pause 5m
            </Button>
          )}

          {/* Resume button when paused */}
          {isPaused && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsPaused(false);
                setPauseEndTime(null);
                handleAutoRefreshToggle(true);
              }}
              className="h-7 px-2 py-0 text-xs"
            >
              <Clock className="h-3 w-3 mr-1" />
              Resume ({formatPauseTimeRemaining()})
            </Button>
          )}

          {/* Compact status indicators */}
          <div className="flex items-center gap-2 text-xs">
            <div className="flex items-center">
              {lastRefreshTime && (
                <span className="text-xs text-muted-foreground">
                  Last: <span className="text-white font-medium">{lastRefreshTime.toLocaleTimeString()}</span>
                </span>
              )}
            </div>

            <div className="flex items-center">
              {autoRefreshEnabled && !isPaused ? (
                <span className="text-xs text-muted-foreground">
                  Next: <span className={`text-white font-medium ${
                    nextRefreshTime && ((nextRefreshTime.getTime() - new Date().getTime()) / 1000 < 10) ?
                    'text-red-400' : ''
                  }`}>{countdownDisplay}</span>
                </span>
              ) : isPaused && pauseEndTime ? (
                <span className="text-xs text-muted-foreground">
                  Paused until: <span className="text-white font-medium">{pauseEndTime.toLocaleTimeString()}</span>
                </span>
              ) : null}
            </div>
          </div>
        </div>
      ) : (
        /* Standard layout */
        <>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Clear URL metrics in the parent component before refreshing
                if (typeof window !== 'undefined') {
                  // Create and dispatch a custom event to notify the parent component
                  const event = new CustomEvent('clearUrlMetrics', { detail: { message: 'Clear URL metrics before refresh' } });
                  window.dispatchEvent(event);

                  console.log('RealtimeAutoRefresh: Dispatched clearUrlMetrics event');

                  // Clear all URL caches in localStorage
                  const keys = Object.keys(localStorage);
                  const urlCacheKeys = keys.filter(key =>
                    key.startsWith('ytr_videos_') ||
                    key.startsWith('ytr_videos_timestamp_')
                  );

                  urlCacheKeys.forEach(key => {
                    localStorage.removeItem(key);
                    console.log(`RealtimeAutoRefresh: Cleared cache for ${key}`);
                  });
                }

                // Then proceed with the refresh
                handleRefresh();
              }}
              disabled={isRefreshing}
              className="relative"
            >
              {isRefreshing ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh Now
            </Button>

            <div className="flex items-center gap-2">
              <Select
                value={autoRefreshEnabled ? autoRefreshInterval.toString() : "manual"}
                onValueChange={handleIntervalChange}
                disabled={isRefreshing || isPaused}
              >
                <SelectTrigger className="w-[180px] h-9">
                  <SelectValue placeholder="Select refresh interval" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="manual">Manual refresh only</SelectItem>
                  <SelectItem value="30">30 seconds</SelectItem>
                  <SelectItem value="60">1 minute</SelectItem>
                  <SelectItem value="120">2 minutes</SelectItem>
                  <SelectItem value="300">5 minutes</SelectItem>
                  <SelectItem value="600">10 minutes</SelectItem>
                  <SelectItem value="1200">20 minutes</SelectItem>
                  <SelectItem value="1800">30 minutes</SelectItem>
                  <SelectItem value="2400">40 minutes</SelectItem>
                  <SelectItem value="3000">50 minutes</SelectItem>
                  <SelectItem value="3600">1 hour</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex items-center gap-1">
                <span className="text-sm text-muted-foreground">Auto</span>
                <Switch
                  checked={autoRefreshEnabled}
                  onCheckedChange={handleAutoRefreshToggle}
                  disabled={isRefreshing || autoRefreshInterval <= 0 || isPaused}
                />
              </div>
            </div>

            {/* Pause button */}
            {autoRefreshEnabled && !isPaused && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePauseAutoRefresh(5)}
                disabled={isRefreshing}
              >
                <Pause className="h-4 w-4 mr-2" />
                Pause for 5m
              </Button>
            )}

            {/* Resume button when paused */}
            {isPaused && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsPaused(false);
                  setPauseEndTime(null);
                  handleAutoRefreshToggle(true);
                }}
              >
                <Clock className="h-4 w-4 mr-2" />
                Resume ({formatPauseTimeRemaining()})
              </Button>
            )}
          </div>

          {/* Status indicators - fixed height container to prevent layout shifts */}
          <div className="flex items-center gap-6 text-sm py-2 px-4 bg-transparent rounded-lg my-2 h-14">
            <div className="flex items-center gap-6 w-full">
              {/* Last refresh time - always visible with fixed width */}
              <div className="flex items-center min-w-[240px]">
                {lastRefreshTime ? (
                  <>
                    <span className="text-white font-medium mr-1 px-2 py-1 rounded-md bg-gray-900 border border-gray-600 shadow-sm">Last refresh:</span>
                    <span className="text-white bg-gray-900 font-bold px-4 py-1 rounded-md inline-block min-w-[120px] text-center border border-gray-600 shadow-sm">{lastRefreshTime.toLocaleTimeString()}</span>
                  </>
                ) : (
                  <>
                    <span className="text-white font-medium mr-1 px-2 py-1 rounded-md bg-gray-900 border border-gray-600 shadow-sm opacity-0">Last refresh:</span>
                    <span className="text-white bg-gray-900 font-bold px-4 py-1 rounded-md inline-block min-w-[120px] text-center border border-gray-600 shadow-sm opacity-0">00:00:00</span>
                  </>
                )}
              </div>

              {/* Next refresh countdown - always visible with fixed width */}
              <div className="flex items-center min-w-[240px]">
                {autoRefreshEnabled && !isPaused ? (
                  <>
                    <span className="text-white font-medium mr-1 px-2 py-1 rounded-md bg-gray-900 border border-gray-600 shadow-sm">Next refresh in:</span>
                    <span className={`
                      text-white bg-gray-900
                      font-bold text-xl px-4 py-1 rounded-md inline-block min-w-[80px] text-center border border-gray-600 shadow-sm ${
                      nextRefreshTime &&
                      ((nextRefreshTime.getTime() - new Date().getTime()) / 1000 < 10) ?
                      'animate-pulse bg-red-800 text-white border-red-600' : ''
                    }`}>{countdownDisplay}</span>
                    {debugMode && <span className="text-white ml-1 bg-gray-900 px-2 py-1 rounded-md border border-gray-600 shadow-sm">(interval: {autoRefreshInterval}s, next: {nextRefreshTime?.toLocaleTimeString() || 'N/A'})</span>}
                  </>
                ) : isPaused && pauseEndTime ? (
                  <>
                    <span className="text-white font-medium mr-1 px-2 py-1 rounded-md bg-gray-900 border border-gray-600 shadow-sm">Auto-refresh paused until</span>
                    <span className="text-white bg-gray-900 font-bold px-4 py-1 rounded-md inline-block min-w-[120px] text-center border border-gray-600 shadow-sm">{pauseEndTime.toLocaleTimeString()}</span>
                  </>
                ) : (
                  <>
                    <span className="text-white font-medium mr-1 px-2 py-1 rounded-md bg-gray-900 border border-gray-600 shadow-sm opacity-0">Next refresh in:</span>
                    <span className="text-white bg-gray-900 font-bold text-xl px-4 py-1 rounded-md inline-block min-w-[80px] text-center border border-gray-600 shadow-sm opacity-0">0:00</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </>
      )}

      {/* Debug information */}
      {debugMode && (
        <Card className="mt-4 p-4 bg-muted/50">
          <h3 className="text-sm font-semibold">Auto-refresh Debug Info</h3>
          <ul className="text-xs mt-2 space-y-1">
            <li>Auto-refresh enabled: {autoRefreshEnabled ? 'Yes' : 'No'}</li>
            <li>Interval: {autoRefreshInterval} seconds</li>
            <li>Is active tab: {isActive ? 'Yes' : 'No'}</li>
            <li>Is paused: {isPaused ? 'Yes' : 'No'}</li>
            <li>Last refresh: {lastRefreshTime ? <span className="text-white bg-gray-900 font-bold px-2 py-0.5 rounded-md inline-block text-center border border-gray-600 shadow-sm">{lastRefreshTime.toLocaleTimeString()}</span> : 'Never'}</li>
            <li>Interval ID active: {autoRefreshIntervalId ? `Yes (ID: ${autoRefreshIntervalId})` : 'No'}</li>
            <li>Refresh in progress: {refreshInProgressRef.current ? 'Yes' : 'No'}</li>
            <li>Countdown timer active: {countdownTimerRef.current ? 'Yes' : 'No'}</li>
            <li>Videos count: {videos.length}</li>
            <li>Suggested interval: {calculateSuggestedRefreshInterval(videos)} seconds</li>
            <li>Next refresh time: {nextRefreshTime ? <span className="text-white bg-gray-900 font-bold px-2 py-0.5 rounded-md inline-block text-center border border-gray-600 shadow-sm">{nextRefreshTime.toLocaleTimeString()}</span> : 'Not set'}</li>
            <li>Countdown display: {countdownDisplay}</li>
            <li>Time until next refresh: {
              nextRefreshTime ? (() => {
                const now = new Date();
                const diffMs = nextRefreshTime.getTime() - now.getTime();
                const totalSeconds = Math.max(0, Math.floor(diffMs / 1000));
                return `${Math.floor(totalSeconds / 60)}:${(totalSeconds % 60).toString().padStart(2, '0')} (${totalSeconds}s)`;
              })() : 'Not set'
            }</li>
          </ul>
          <div className="flex gap-2 mt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                logger.debug({
                  autoRefreshEnabled,
                  autoRefreshInterval,
                  isActive,
                  isPaused,
                  lastRefreshTime: lastRefreshTime ? lastRefreshTime.toLocaleTimeString() : 'Never',
                  nextRefreshTime: nextRefreshTime ? nextRefreshTime.toLocaleTimeString() : 'Not set',
                  countdownDisplay,
                  timeUntilNextRefresh: nextRefreshTime ? (() => {
                    const now = new Date();
                    const diffMs = nextRefreshTime.getTime() - now.getTime();
                    const totalSeconds = Math.max(0, Math.floor(diffMs / 1000));
                    return `${Math.floor(totalSeconds / 60)}:${(totalSeconds % 60).toString().padStart(2, '0')} (${totalSeconds}s)`;
                  })() : 'Not set',
                  autoRefreshIntervalId,
                  refreshInProgress: refreshInProgressRef.current,
                  countdownTimerActive: !!countdownTimerRef.current,
                  videosCount: videos.length,
                  suggestedInterval: calculateSuggestedRefreshInterval(videos)
                });
              }}
            >
              Log Debug Info
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Force a refresh for debugging
                handleRefresh().catch(err => {
                  logger.error("Error during manual debug refresh:", err);
                });

                // Also force update the countdown display
                if (nextRefreshTime) {
                  const now = new Date();
                  const diffMs = nextRefreshTime.getTime() - now.getTime();
                  const totalSeconds = Math.max(0, Math.floor(diffMs / 1000));
                  const minutes = Math.floor(totalSeconds / 60);
                  const seconds = totalSeconds % 60;
                  const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                  setCountdownDisplay(display);
                }
              }}
            >
              Force Refresh
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}
