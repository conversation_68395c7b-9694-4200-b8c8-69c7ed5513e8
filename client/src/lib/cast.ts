declare global {
  interface Window {
    __onGCastApiAvailable: (isAvailable: boolean) => void;
    chrome: {
      cast: {
        // API configuration
        initialize: (apiConfig: any, successCallback: () => void, errorCallback: (error: Error) => void) => void;
        isAvailable: boolean;

        // Session request
        SessionRequest: new (appId: string) => any;

        // API config
        ApiConfig: new (
          sessionRequest: any,
          sessionListener: (session: any) => void,
          receiverListener: (availability: string) => void,
          autoJoinPolicy?: string
        ) => any;

        // Auto join policy
        AutoJoinPolicy: {
          ORIGIN_SCOPED: string;
          TAB_AND_ORIGIN_SCOPED: string;
          PAGE_SCOPED: string;
        };

        // Receiver availability
        ReceiverAvailability: {
          AVAILABLE: string;
          UNAVAILABLE: string;
        };

        // Session management
        requestSession: (
          successCallback: (session: any) => void,
          errorCallback: (error: any) => void
        ) => void;

        // Media namespace
        media: {
          // Media info
          MediaInfo: new (contentId: string, contentType: string) => any;

          // Load request
          LoadRequest: new (mediaInfo: any) => any;

          // Default receiver app ID
          DEFAULT_MEDIA_RECEIVER_APP_ID: string;
        };

        // Error codes
        ErrorCode: {
          TIMEOUT: string;
          INVALID_PARAMETER: string;
          API_NOT_INITIALIZED: string;
          EXTENSION_MISSING: string;
          EXTENSION_NOT_COMPATIBLE: string;
          INVALID_ACTION: string;
          AUTHENTICATION_EXPIRED: string;
          RECEIVER_UNAVAILABLE: string;
          SESSION_ERROR: string;
          CHANNEL_ERROR: string;
          LOAD_MEDIA_FAILED: string;
        };
      };
    };
  }
}

// Event emitter for cast events
class CastEventEmitter {
  private listeners: { [event: string]: Function[] } = {};

  on(event: string, callback: Function) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
    return this;
  }

  off(event: string, callback: Function) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
    return this;
  }

  emit(event: string, ...args: any[]) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(...args));
    }
    return this;
  }
}

export const castEvents = new CastEventEmitter();

// Constants
// Use the YouTube receiver app ID for better YouTube video playback
const APPLICATION_ID = '233637DE'; // YouTube receiver app ID
let initialized = false;
let castingAvailable = false;

let currentSession: any = null;
let currentMedia: any = null;

/**
 * Initialize the Cast API
 */
export function initializeCastApi(): Promise<void> {
  if (initialized) {
    return Promise.resolve();
  }

  return new Promise((resolve, reject) => {
    // Set up the Cast API availability callback
    window.__onGCastApiAvailable = (isAvailable) => {
      if (isAvailable) {
        initializeApi(resolve, reject);
      } else {
        reject(new Error('Google Cast API is not available'));
      }
    };

    // Check if the Cast framework is already initialized
    if (window.chrome && window.chrome.cast && window.chrome.cast.isAvailable) {
      console.log('Cast API already available, initializing directly');
      initializeApi(resolve, reject);
      return;
    }

    // Check if the Cast API script is already being loaded
    if (document.querySelector('script[src*="cast_sender.js"]')) {
      console.log('Cast API script already loading, waiting for availability');
      // The script is already loading, just wait for the API to become available
      return;
    }

    // Load the Cast API script
    console.log('Loading Cast API script');
    const script = document.createElement('script');
    script.src = 'https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1';
    document.head.appendChild(script);
  });
}

/**
 * Initialize the Cast API with the proper configuration
 */
function initializeApi(resolve: Function, reject: Function) {
  try {
    const sessionRequest = new window.chrome.cast.SessionRequest(APPLICATION_ID);

    const apiConfig = new window.chrome.cast.ApiConfig(
      sessionRequest,
      sessionListener,
      receiverListener,
      window.chrome.cast.AutoJoinPolicy.ORIGIN_SCOPED
    );

    window.chrome.cast.initialize(
      apiConfig,
      () => {
        console.log('Cast API initialized successfully');
        initialized = true;
        resolve();
      },
      (error) => {
        console.error('Cast API initialization error:', error);
        reject(error);
      }
    );
  } catch (error) {
    console.error('Error initializing Cast API:', error);
    reject(error);
  }
}

/**
 * Session listener - called when a session is started or resumed
 */
function sessionListener(session: any) {
  console.log('New cast session:', session);
  currentSession = session;

  // Listen for session updates
  session.addUpdateListener((isAlive: boolean) => {
    if (!isAlive) {
      currentSession = null;
      castEvents.emit('disconnected');
    }
  });

  // Listen for media status updates
  session.addMediaListener((media: any) => {
    currentMedia = media;
    castEvents.emit('mediaStatus', media);
  });

  castEvents.emit('connected', session);
}

/**
 * Receiver listener - called when receiver availability changes
 */
function receiverListener(availability: string) {
  console.log('Receiver availability:', availability);
  castingAvailable = availability === window.chrome.cast.ReceiverAvailability.AVAILABLE;
  castEvents.emit('availability', castingAvailable);
}

/**
 * Cast a video to the Chromecast using the YouTube receiver
 */
export function castVideo(videoId: string, title?: string, thumbnail?: string): Promise<void> {
  if (!initialized) {
    return initializeCastApi().then(() => castVideo(videoId, title, thumbnail));
  }

  return new Promise((resolve, reject) => {
    // Create media info specifically for YouTube videos
    const mediaInfo = new window.chrome.cast.media.MediaInfo(
      videoId, // Just pass the videoId, not the full URL
      'application/x-youtube'
    );

    // Add metadata for better display on the receiver
    mediaInfo.metadata = {
      type: 0, // MEDIA_TYPE_GENERIC
      metadataType: 0,
      title: title || `YouTube Video (${videoId})`,
      images: [
        { url: thumbnail || `https://img.youtube.com/vi/${videoId}/hqdefault.jpg` }
      ]
    };

    // Set customData to tell the receiver this is a YouTube video
    mediaInfo.customData = {
      source: 'youtube'
    };

    const request = new window.chrome.cast.media.LoadRequest(mediaInfo);

    // If we already have a session, use it
    if (currentSession) {
      console.log('Using existing cast session');
      currentSession.loadMedia(
        request,
        (media: any) => {
          currentMedia = media;
          castEvents.emit('mediaStatus', media);
          resolve();
        },
        (error: any) => {
          console.error('Error loading media in existing session:', error);
          // If loading fails, try requesting a new session
          requestNewSession(request, resolve, reject);
        }
      );
    } else {
      // Otherwise request a new session
      requestNewSession(request, resolve, reject);
    }
  });
}

/**
 * Request a new casting session
 */
function requestNewSession(request: any, resolve: Function, reject: Function) {
  window.chrome.cast.requestSession(
    (session) => {
      currentSession = session;

      // Listen for session updates
      session.addUpdateListener((isAlive: boolean) => {
        if (!isAlive) {
          currentSession = null;
          castEvents.emit('disconnected');
        }
      });

      // Listen for media status updates
      session.addMediaListener((media: any) => {
        currentMedia = media;
        castEvents.emit('mediaStatus', media);
      });

      castEvents.emit('connected', session);

      session.loadMedia(
        request,
        (media: any) => {
          currentMedia = media;
          castEvents.emit('mediaStatus', media);
          resolve();
        },
        (error: any) => {
          console.error('Error loading media:', error);
          reject(error);
        }
      );
    },
    (error) => {
      console.error('Error requesting cast session:', error);
      reject(error);
    }
  );
}

/**
 * Disconnect from the current cast session
 */
export function disconnectCastSession(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (currentSession) {
      currentSession.stop(
        () => {
          currentSession = null;
          currentMedia = null;
          castEvents.emit('disconnected');
          resolve();
        },
        (error: Error) => {
          console.error('Error disconnecting cast session:', error);
          reject(error);
        }
      );
    } else {
      resolve();
    }
  });
}

/**
 * Check if casting is available
 */
export function isCastingAvailable(): boolean {
  return initialized && castingAvailable;
}

/**
 * Get the current session
 */
export function getCurrentSession() {
  return currentSession;
}

/**
 * Get the current media
 */
export function getCurrentMedia() {
  return currentMedia;
}

/**
 * Get error message from error code
 */
export function getErrorMessage(errorCode: string): string {
  switch (errorCode) {
    case window.chrome.cast.ErrorCode.API_NOT_INITIALIZED:
      return 'The API is not initialized.';
    case window.chrome.cast.ErrorCode.CANCEL:
      return 'The operation was canceled by the user.';
    case window.chrome.cast.ErrorCode.CHANNEL_ERROR:
      return 'A channel error occurred.';
    case window.chrome.cast.ErrorCode.EXTENSION_MISSING:
      return 'The Cast extension is not available.';
    case window.chrome.cast.ErrorCode.INVALID_PARAMETER:
      return 'The parameters to the operation were not valid.';
    case window.chrome.cast.ErrorCode.RECEIVER_UNAVAILABLE:
      return 'No receiver was compatible with the session request.';
    case window.chrome.cast.ErrorCode.SESSION_ERROR:
      return 'A session error occurred.';
    case window.chrome.cast.ErrorCode.TIMEOUT:
      return 'The operation timed out.';
    default:
      return 'Unknown error: ' + errorCode;
  }
}


