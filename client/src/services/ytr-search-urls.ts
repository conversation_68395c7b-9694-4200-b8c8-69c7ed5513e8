import axios from 'axios';

export interface YtrSearchUrl {
  id: string;
  name: string;
  url: string;
  resultsLimit: number;
  displayOrder?: number;
}

export interface CreateYtrSearchUrlRequest {
  name: string;
  url: string;
  resultsLimit?: number;
}

export interface UpdateYtrSearchUrlRequest {
  name: string;
  url: string;
  resultsLimit?: number;
}

export interface ReorderYtrSearchUrlsRequest {
  urlOrders: Array<{ id: string; displayOrder: number }>;
}

export interface MigrateYtrSearchUrlsRequest {
  searchUrls: Array<{ name: string; url: string; resultsLimit?: number }>;
}

export class YtrSearchUrlsService {
  private static readonly BASE_PATH = '/api/ytr-search-urls';

  /**
   * Get all search URLs for the current user
   */
  static async getSearchUrls(): Promise<YtrSearchUrl[]> {
    try {
      const response = await axios.get(this.BASE_PATH);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching YTR search URLs:', error);
      throw error;
    }
  }

  /**
   * Create a new search URL
   */
  static async createSearchUrl(data: CreateYtrSearchUrlRequest): Promise<YtrSearchUrl> {
    try {
      const response = await axios.post(this.BASE_PATH, data);
      return response.data;
    } catch (error) {
      console.error('Error creating YTR search URL:', error);
      throw error;
    }
  }

  /**
   * Update an existing search URL
   */
  static async updateSearchUrl(id: string, data: UpdateYtrSearchUrlRequest): Promise<YtrSearchUrl> {
    try {
      const response = await axios.put(`${this.BASE_PATH}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating YTR search URL:', error);
      throw error;
    }
  }

  /**
   * Delete a search URL
   */
  static async deleteSearchUrl(id: string): Promise<void> {
    try {
      await axios.delete(`${this.BASE_PATH}/${id}`);
    } catch (error) {
      console.error('Error deleting YTR search URL:', error);
      throw error;
    }
  }

  /**
   * Reorder search URLs
   */
  static async reorderSearchUrls(data: ReorderYtrSearchUrlsRequest): Promise<void> {
    try {
      await axios.post(`${this.BASE_PATH}/reorder`, data);
    } catch (error) {
      console.error('Error reordering YTR search URLs:', error);
      throw error;
    }
  }

  /**
   * Migrate localStorage data to database
   */
  static async migrateSearchUrls(data: MigrateYtrSearchUrlsRequest): Promise<any> {
    try {
      const response = await axios.post(`${this.BASE_PATH}/migrate`, data);
      return response.data;
    } catch (error) {
      console.error('Error migrating YTR search URLs:', error);
      throw error;
    }
  }

  /**
   * Migrate existing localStorage search URLs to database
   * This function reads from localStorage and migrates the data
   */
  static async migrateFromLocalStorage(): Promise<{ success: boolean; message: string; results?: any[] }> {
    try {
      // Read existing search URLs from localStorage
      const localStorageUrls = this.getSearchUrlsFromLocalStorage();

      if (localStorageUrls.length === 0) {
        return {
          success: true,
          message: 'No search URLs found in localStorage to migrate'
        };
      }

      console.log(`Found ${localStorageUrls.length} search URLs in localStorage, migrating to database...`);

      // Migrate to database with default results limit
      const urlsWithDefaults = localStorageUrls.map(url => ({
        ...url,
        resultsLimit: 25 // Default results limit for migrated URLs
      }));
      const result = await this.migrateSearchUrls({ searchUrls: urlsWithDefaults });

      // If migration was successful, clear localStorage
      if (result.results) {
        const successCount = result.results.filter((r: any) => r.success).length;
        if (successCount > 0) {
          console.log(`Successfully migrated ${successCount} search URLs, clearing localStorage...`);
          this.clearSearchUrlsFromLocalStorage();
        }
      }

      return {
        success: true,
        message: result.message,
        results: result.results
      };
    } catch (error) {
      console.error('Error migrating from localStorage:', error);
      return {
        success: false,
        message: `Failed to migrate: ${error.message}`
      };
    }
  }

  /**
   * Get search URLs from localStorage (for migration)
   */
  private static getSearchUrlsFromLocalStorage(): Array<{ name: string; url: string }> {
    try {
      const stored = localStorage.getItem('ytrSearchUrls');
      if (!stored) return [];

      const parsed = JSON.parse(stored);
      if (!Array.isArray(parsed)) return [];

      return parsed.map((item: any) => ({
        name: item.name || 'Unnamed URL',
        url: item.url
      })).filter((item: any) => item.url);
    } catch (error) {
      console.error('Error reading search URLs from localStorage:', error);
      return [];
    }
  }

  /**
   * Clear search URLs from localStorage (after successful migration)
   */
  private static clearSearchUrlsFromLocalStorage(): void {
    try {
      localStorage.removeItem('ytrSearchUrls');
      console.log('Cleared search URLs from localStorage');
    } catch (error) {
      console.error('Error clearing search URLs from localStorage:', error);
    }
  }

  /**
   * Check if there are search URLs in localStorage that need migration
   */
  static hasLocalStorageData(): boolean {
    try {
      const stored = localStorage.getItem('ytrSearchUrls');
      if (!stored) return false;

      const parsed = JSON.parse(stored);
      return Array.isArray(parsed) && parsed.length > 0;
    } catch (error) {
      return false;
    }
  }
}

export default YtrSearchUrlsService;
