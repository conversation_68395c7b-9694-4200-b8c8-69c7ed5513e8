import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Save, 
  Plus, 
  Edit, 
  Trash2, 
  Palette,
  Download,
  ExternalLink,
  Clock,
  FileText
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ManualScriptEditorProps {
  transcripts: any[];
  onTranscriptCreated: (transcript: any) => void;
  onTranscriptUpdated: (transcript: any) => void;
  onTranscriptDeleted: (transcriptId: number) => void;
}

interface Highlight {
  id: number;
  startPos: number;
  endPos: number;
  color: string;
  note?: string;
}

const HIGHLIGHT_COLORS = [
  { name: 'Yellow', value: '#ffff00' },
  { name: 'Green', value: '#00ff00' },
  { name: 'Blue', value: '#00bfff' },
  { name: 'Pink', value: '#ff69b4' },
  { name: 'Orange', value: '#ffa500' },
  { name: 'Purple', value: '#9370db' },
  { name: 'Red', value: '#ff6b6b' },
  { name: 'Cyan', value: '#00ffff' }
];

export function ManualScriptEditor({ 
  transcripts, 
  onTranscriptCreated, 
  onTranscriptUpdated, 
  onTranscriptDeleted 
}: ManualScriptEditorProps) {
  const [selectedTranscript, setSelectedTranscript] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [videoUrl, setVideoUrl] = useState('');
  const [highlights, setHighlights] = useState<Highlight[]>([]);
  const [selectedColor, setSelectedColor] = useState(HIGHLIGHT_COLORS[0].value);
  const [isCreating, setIsCreating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [transcriptToDelete, setTranscriptToDelete] = useState<any>(null);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (selectedTranscript) {
      setTitle(selectedTranscript.title);
      setContent(selectedTranscript.content);
      setVideoUrl(selectedTranscript.video_url || '');
      fetchHighlights(selectedTranscript.id);
    }
  }, [selectedTranscript]);

  const fetchHighlights = async (transcriptId: number) => {
    try {
      const response = await fetch(`/api/txt-tab/transcripts/${transcriptId}/highlights`);
      if (response.ok) {
        const data = await response.json();
        setHighlights(data);
      }
    } catch (error) {
      console.error('Error fetching highlights:', error);
    }
  };

  const handleNewTranscript = () => {
    setSelectedTranscript(null);
    setTitle('');
    setContent('');
    setVideoUrl('');
    setHighlights([]);
    setIsEditing(true);
    setIsCreating(true);
  };

  const handleSelectTranscript = (transcript: any) => {
    if (isEditing) {
      // Ask user if they want to save changes
      if (window.confirm('You have unsaved changes. Do you want to discard them?')) {
        setIsEditing(false);
        setIsCreating(false);
        setSelectedTranscript(transcript);
      }
    } else {
      setSelectedTranscript(transcript);
    }
  };

  const handleSave = async () => {
    if (!title.trim() || !content.trim()) {
      toast({
        title: "Error",
        description: "Title and content are required",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSaving(true);
      
      const data = {
        title: title.trim(),
        content: content.trim(),
        videoUrl: videoUrl.trim() || undefined
      };

      let response;
      if (isCreating) {
        response = await fetch('/api/txt-tab/transcripts', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
      } else {
        response = await fetch(`/api/txt-tab/transcripts/${selectedTranscript.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
      }

      if (response.ok) {
        const savedTranscript = await response.json();
        
        if (isCreating) {
          onTranscriptCreated(savedTranscript);
          setSelectedTranscript(savedTranscript);
        } else {
          onTranscriptUpdated(savedTranscript);
          setSelectedTranscript(savedTranscript);
        }
        
        setIsEditing(false);
        setIsCreating(false);
      } else {
        throw new Error('Failed to save transcript');
      }
    } catch (error) {
      console.error('Error saving transcript:', error);
      toast({
        title: "Error",
        description: "Failed to save transcript",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async (transcript: any) => {
    try {
      const response = await fetch(`/api/txt-tab/transcripts/${transcript.id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        onTranscriptDeleted(transcript.id);
        if (selectedTranscript?.id === transcript.id) {
          setSelectedTranscript(null);
          setTitle('');
          setContent('');
          setVideoUrl('');
          setHighlights([]);
        }
      } else {
        throw new Error('Failed to delete transcript');
      }
    } catch (error) {
      console.error('Error deleting transcript:', error);
      toast({
        title: "Error",
        description: "Failed to delete transcript",
        variant: "destructive",
      });
    } finally {
      setDeleteDialogOpen(false);
      setTranscriptToDelete(null);
    }
  };

  const handleTextSelection = async () => {
    if (!textareaRef.current || !selectedTranscript) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    if (start === end) {
      toast({
        title: "No Selection",
        description: "Please select text to highlight",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await fetch('/api/txt-tab/highlights', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          transcriptId: selectedTranscript.id,
          startPos: start,
          endPos: end,
          color: selectedColor
        })
      });

      if (response.ok) {
        const newHighlight = await response.json();
        setHighlights(prev => [...prev, newHighlight]);
        toast({
          title: "Success",
          description: "Text highlighted successfully",
        });
      } else {
        throw new Error('Failed to create highlight');
      }
    } catch (error) {
      console.error('Error creating highlight:', error);
      toast({
        title: "Error",
        description: "Failed to create highlight",
        variant: "destructive",
      });
    }
  };

  const removeHighlight = async (highlightId: number) => {
    try {
      const response = await fetch(`/api/txt-tab/highlights/${highlightId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setHighlights(prev => prev.filter(h => h.id !== highlightId));
        toast({
          title: "Success",
          description: "Highlight removed successfully",
        });
      } else {
        throw new Error('Failed to remove highlight');
      }
    } catch (error) {
      console.error('Error removing highlight:', error);
      toast({
        title: "Error",
        description: "Failed to remove highlight",
        variant: "destructive",
      });
    }
  };

  const exportTranscript = (format: 'txt' | 'json' | 'html') => {
    if (!selectedTranscript) return;

    let content = '';
    let filename = '';
    let mimeType = 'text/plain';

    switch (format) {
      case 'txt':
        content = `${selectedTranscript.title}\n\n${selectedTranscript.content}`;
        filename = `${selectedTranscript.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
        mimeType = 'text/plain';
        break;

      case 'json':
        const exportData = {
          title: selectedTranscript.title,
          content: selectedTranscript.content,
          videoUrl: selectedTranscript.video_url,
          highlights: highlights,
          createdAt: selectedTranscript.created_at,
          updatedAt: selectedTranscript.updated_at
        };
        content = JSON.stringify(exportData, null, 2);
        filename = `${selectedTranscript.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;
        mimeType = 'application/json';
        break;

      case 'html':
        content = `
<!DOCTYPE html>
<html>
<head>
    <title>${selectedTranscript.title}</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { border-bottom: 2px solid #ccc; padding-bottom: 10px; margin-bottom: 20px; }
        .content { white-space: pre-wrap; line-height: 1.6; }
        .highlight { padding: 2px 4px; border-radius: 3px; }
        .meta { color: #666; font-size: 0.9em; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${selectedTranscript.title}</h1>
        ${selectedTranscript.video_url ? `<p><a href="${selectedTranscript.video_url}" target="_blank">View Video</a></p>` : ''}
    </div>
    <div class="content">${renderHighlightedContent()}</div>
    <div class="meta">
        <p>Created: ${new Date(selectedTranscript.created_at).toLocaleString()}</p>
        <p>Updated: ${new Date(selectedTranscript.updated_at).toLocaleString()}</p>
        <p>Highlights: ${highlights.length}</p>
    </div>
</body>
</html>`;
        filename = `${selectedTranscript.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.html`;
        mimeType = 'text/html';
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Success",
      description: `Transcript exported as ${format.toUpperCase()}`,
    });
  };

  const exportAllTranscripts = () => {
    const exportData = {
      version: '1.0',
      exported_at: new Date().toISOString(),
      transcripts: transcripts.map(t => ({
        title: t.title,
        content: t.content,
        video_url: t.video_url,
        source_type: t.source_type,
        created_at: t.created_at,
        updated_at: t.updated_at
      }))
    };

    const content = JSON.stringify(exportData, null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'all-transcripts-export.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Success",
      description: "All transcripts exported successfully",
    });
  };

  const renderHighlightedContent = () => {
    if (!content || highlights.length === 0) {
      return content;
    }

    // Sort highlights by start position
    const sortedHighlights = [...highlights].sort((a, b) => a.startPos - b.startPos);
    
    let result = '';
    let lastIndex = 0;

    sortedHighlights.forEach((highlight) => {
      // Add text before highlight
      result += content.slice(lastIndex, highlight.startPos);
      
      // Add highlighted text
      const highlightedText = content.slice(highlight.startPos, highlight.endPos);
      result += `<span style="background-color: ${highlight.color}; padding: 2px 4px; border-radius: 3px;">${highlightedText}</span>`;
      
      lastIndex = highlight.endPos;
    });

    // Add remaining text
    result += content.slice(lastIndex);
    
    return result;
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
      {/* Transcript List */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-white">My Transcripts</CardTitle>
            <div className="flex items-center gap-2">
              {transcripts.length > 0 && (
                <Button size="sm" variant="outline" onClick={exportAllTranscripts}>
                  <Download className="h-4 w-4 mr-1" />
                  Export All
                </Button>
              )}
              <Button size="sm" onClick={handleNewTranscript}>
                <Plus className="h-4 w-4 mr-1" />
                New
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            <div className="space-y-2">
              {transcripts.map((transcript) => (
                <div
                  key={transcript.id}
                  className={`p-3 rounded-md cursor-pointer border transition-colors ${
                    selectedTranscript?.id === transcript.id
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:bg-muted'
                  }`}
                  onClick={() => handleSelectTranscript(transcript)}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm truncate">{transcript.title}</h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(transcript.updated_at).toLocaleDateString()}
                      </p>
                      {transcript.highlight_count > 0 && (
                        <Badge variant="outline" className="text-xs mt-1">
                          {transcript.highlight_count} highlights
                        </Badge>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setTranscriptToDelete(transcript);
                        setDeleteDialogOpen(true);
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Editor */}
      <Card className="lg:col-span-2">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-white">
              {isCreating ? 'New Transcript' : selectedTranscript ? 'Edit Transcript' : 'Script Editor'}
            </CardTitle>
            <div className="flex items-center gap-2">
              {selectedTranscript && !isEditing && (
                <>
                  <div className="flex items-center gap-1">
                    <Button size="sm" variant="outline" onClick={() => exportTranscript('txt')}>
                      <Download className="h-4 w-4 mr-1" />
                      TXT
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => exportTranscript('json')}>
                      JSON
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => exportTranscript('html')}>
                      HTML
                    </Button>
                  </div>
                  <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                </>
              )}
              {isEditing && (
                <>
                  <Button size="sm" variant="outline" onClick={() => {
                    setIsEditing(false);
                    setIsCreating(false);
                    if (selectedTranscript) {
                      setTitle(selectedTranscript.title);
                      setContent(selectedTranscript.content);
                      setVideoUrl(selectedTranscript.video_url || '');
                    }
                  }}>
                    Cancel
                  </Button>
                  <Button size="sm" onClick={handleSave} disabled={isSaving}>
                    <Save className="h-4 w-4 mr-1" />
                    {isSaving ? 'Saving...' : 'Save'}
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {isEditing ? (
            <>
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter transcript title..."
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="videoUrl">Video URL (optional)</Label>
                <Input
                  id="videoUrl"
                  value={videoUrl}
                  onChange={(e) => setVideoUrl(e.target.value)}
                  placeholder="https://www.youtube.com/watch?v=..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">Content</Label>
                <Textarea
                  ref={textareaRef}
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Write your script content here..."
                  className="min-h-[300px] font-mono"
                />
              </div>

              {selectedTranscript && (
                <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
                  <Palette className="h-4 w-4" />
                  <Label>Highlight Color:</Label>
                  <Select value={selectedColor} onValueChange={setSelectedColor}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {HIGHLIGHT_COLORS.map((color) => (
                        <SelectItem key={color.value} value={color.value}>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-4 h-4 rounded border"
                              style={{ backgroundColor: color.value }}
                            />
                            {color.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button size="sm" onClick={handleTextSelection}>
                    Highlight Selected
                  </Button>
                </div>
              )}
            </>
          ) : selectedTranscript ? (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-lg">{selectedTranscript.title}</h3>
                {selectedTranscript.video_url && (
                  <a 
                    href={selectedTranscript.video_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-500 hover:underline flex items-center gap-1 mt-1"
                  >
                    <ExternalLink className="h-3 w-3" />
                    View Video
                  </a>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Created: {new Date(selectedTranscript.created_at).toLocaleString()}
                  {selectedTranscript.updated_at !== selectedTranscript.created_at && (
                    <> • Updated: {new Date(selectedTranscript.updated_at).toLocaleString()}</>
                  )}
                </p>
              </div>

              <ScrollArea className="h-[400px] border rounded-md p-4">
                <div 
                  className="whitespace-pre-wrap font-mono text-sm"
                  dangerouslySetInnerHTML={{ __html: renderHighlightedContent() }}
                />
              </ScrollArea>

              {highlights.length > 0 && (
                <div className="space-y-2">
                  <Label>Highlights ({highlights.length})</Label>
                  <div className="flex flex-wrap gap-2">
                    {highlights.map((highlight) => (
                      <Badge 
                        key={highlight.id}
                        variant="outline"
                        className="cursor-pointer"
                        style={{ backgroundColor: highlight.color + '20', borderColor: highlight.color }}
                        onClick={() => removeHighlight(highlight.id)}
                      >
                        <span style={{ color: highlight.color }}>●</span>
                        <span className="ml-1">
                          {content.slice(highlight.startPos, Math.min(highlight.endPos, highlight.startPos + 20))}
                          {highlight.endPos - highlight.startPos > 20 ? '...' : ''}
                        </span>
                        <Trash2 className="h-3 w-3 ml-1" />
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Select a transcript to view or create a new one</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Transcript</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{transcriptToDelete?.title}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={() => transcriptToDelete && handleDelete(transcriptToDelete)}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
