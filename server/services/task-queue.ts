import { worker<PERSON>anager, Task, TaskStatus, TaskPriority } from './worker-manager';
import { storage } from '../storage';
import { analyzeTranscriptForFinancialBenefits } from './financial-analysis';
import { enhancedFinancialAnalysis } from './financial-analysis-integration';
import { isClientIdle } from '../routes/client-state';
// Import OpenRouter task queue handlers

import { registerOpenRouterFinancialAnalysisHandler, registerAutoAnalysisAfterTranscription as registerOpenRouterAutoAnalysisAfterTranscription, queueOpenRouterFinancialAnalysis } from './task-queue-openrouter';

// Import fetchTranscription from youtube-channels.ts
import { fetchTranscription } from '../routes/youtube-channels';

// Initialize task handlers
export function initializeTaskHandlers() {
  // Register handler for fetching transcriptions
  workerManager.registerHandler('fetch-transcription', async (task, updateProgress) => {
    const { videoId } = task.data;

    try {
      updateProgress(10);
      console.log(`Worker: Fetching transcription for video ${videoId}`);

      // Fetch transcription
      const transcription = await fetchTranscription(videoId);
      updateProgress(50);

      // Save transcription to database
      await storage.updateYoutubeVideoTranscription(videoId, transcription);
      updateProgress(80);

      // Get the video details for financial analysis
      const video = await storage.getYoutubeVideo(videoId);
      updateProgress(90);

      // Return the transcription
      return {
        transcription,
        videoId,
        hasTranscription: true
      };
    } catch (error) {
      console.error(`Worker: Error fetching transcription for video ${videoId}:`, error);
      throw error;
    }
  });

  // Register handler for financial analysis
  workerManager.registerHandler('financial-analysis', async (task, updateProgress) => {
    const { videoId, transcription, title, description } = task.data;

    try {
      updateProgress(10);
      console.log(`Worker: Analyzing financial benefits for video ${videoId}`);

      // Use enhanced NLP-based financial analysis
      const analysis = await enhancedFinancialAnalysis(
        transcription,
        title,
        description
      );
      updateProgress(70);

      // Save analysis to database
      await storage.updateYoutubeVideoFinancialAnalysis(videoId, analysis);
      updateProgress(90);

      // Return the analysis
      return {
        analysis,
        videoId,
        hasFinancialAnalysis: true
      };
    } catch (error) {
      console.error(`Worker: Error analyzing financial benefits for video ${videoId}:`, error);
      throw error;
    }
  });

  // Register handler for channel video refresh
  workerManager.registerHandler('refresh-channel-videos', async (task, updateProgress) => {
    const { channelId, userId } = task.data;

    try {
      updateProgress(10);
      console.log(`Worker: Refreshing videos for channel ${channelId}`);

      // Get channel details
      const channel = await storage.getYoutubeChannel(channelId);
      if (!channel) {
        throw new Error(`Channel not found: ${channelId}`);
      }

      // Refresh videos for the channel
      const result = await storage.refreshYoutubeChannelVideos(channelId);
      const videos = result.videos;
      const videosNeedingAnalysis = result.videosNeedingAnalysis;
      updateProgress(50);

      console.log(`Worker: Refresh complete for ${channel.channelTitle}. Found ${videos.length} videos.`);

      // Filter videos that need transcription
      const videosNeedingTranscription = videos.filter(video => !video.hasTranscription);
      updateProgress(60);

      // Create transcription tasks for each video
      if (videosNeedingTranscription.length > 0) {
        console.log(`Worker: Creating transcription tasks for ${videosNeedingTranscription.length} videos`);

        for (const video of videosNeedingTranscription) {
          workerManager.addTask(
            'fetch-transcription',
            `Fetch transcription for ${video.title}`,
            `Fetching transcription for video ${video.id} from channel ${channel.channelTitle}`,
            userId,
            { videoId: video.id },
            TaskPriority.NORMAL
          );
        }
      }

      // Create Ollama analysis tasks for videos with transcriptions but no analysis
      if (videosNeedingAnalysis.length > 0) {
        // Check user settings to see if auto-analyze is enabled
        const userSettings = await storage.getUserSettings(userId);
        const autoAnalyzeEnabled = userSettings?.autoAnalyzeOnRefresh === true; // Must be explicitly true

        // Check if the client is idle
        const isUserIdle = isClientIdle(userId);

        if (autoAnalyzeEnabled) {
          console.log(`Worker: Creating AI analysis tasks for ${videosNeedingAnalysis.length} videos (auto-analyze enabled, client idle: ${isUserIdle})`);

          // If client is idle, only process a limited number of videos to reduce CPU usage
          const maxVideosToProcess = isUserIdle ? 3 : videosNeedingAnalysis.length;
          const videosToProcess = videosNeedingAnalysis.slice(0, maxVideosToProcess);

          if (isUserIdle && videosNeedingAnalysis.length > maxVideosToProcess) {
            console.log(`Worker: Client is idle, limiting analysis to ${maxVideosToProcess} videos out of ${videosNeedingAnalysis.length}`);
          }

          for (const videoId of videosToProcess) {
            // Get the video details
            const video = await storage.getYoutubeVideo(videoId);

            if (video && video.hasTranscription) {
              // Get the transcription
              const transcription = video.transcription;

              if (transcription) {
                // Queue OpenRouter analysis with appropriate priority based on client state
                const priority = isUserIdle ? TaskPriority.LOW : TaskPriority.NORMAL;

                await queueOpenRouterFinancialAnalysis(
                  videoId,
                  transcription,
                  video.title,
                  video.description || '',
                  userId,
                  isUserIdle ? 'idle_refresh' : 'active_refresh', // Track the source
                  false, // Don't force reanalysis
                  priority // Use lower priority when client is idle
                );

                console.log(`Worker: Queued OpenRouter analysis for video ${videoId} - ${video.title} with priority ${priority}`);
              }
            }
          }
        } else {
          console.log(`Worker: Skipping AI analysis for ${videosNeedingAnalysis.length} videos (auto-analyze disabled)`);
        }
      }

      updateProgress(100);

      // Return the videos
      return {
        videos,
        channelId,
        channelTitle: channel.channelTitle,
        totalVideos: videos.length,
        videosNeedingTranscription: videosNeedingTranscription.length,
        videosNeedingAnalysis: videosNeedingAnalysis.length
      };
    } catch (error) {
      console.error(`Worker: Error refreshing videos for channel ${channelId}:`, error);
      throw error;
    }
  });

  // Register handler for channel refresh
  workerManager.registerHandler('refresh-channel', async (task, updateProgress) => {
    const { channelId } = task.data;

    try {
      updateProgress(10);
      console.log(`Worker: Refreshing channel ${channelId}`);

      // Get channel details
      const channel = await storage.getYoutubeChannel(channelId);
      if (!channel) {
        throw new Error(`Channel not found: ${channelId}`);
      }

      // Refresh videos for the channel
      const result = await storage.refreshYoutubeChannelVideos(channelId);
      const videos = result.videos;
      const videosNeedingAnalysis = result.videosNeedingAnalysis;
      updateProgress(50);

      console.log(`Worker: Refresh complete for ${channel.channelTitle}. Found ${videos.length} videos.`);

      // Update the last refresh time
      await storage.updateYoutubeChannelLastRefreshTime(channelId);
      updateProgress(60);

      // Filter videos that need transcription
      const videosNeedingTranscription = videos.filter(video => !video.hasTranscription);
      updateProgress(70);

      // Create transcription tasks for each video
      if (videosNeedingTranscription.length > 0) {
        console.log(`Worker: Creating transcription tasks for ${videosNeedingTranscription.length} videos`);

        for (const video of videosNeedingTranscription) {
          workerManager.addTask(
            'fetch-transcription',
            `Fetch transcription for ${video.title}`,
            `Fetching transcription for video ${video.id} from channel ${channel.channelTitle}`,
            channel.userId,
            { videoId: video.id },
            TaskPriority.NORMAL
          );
        }
      }

      // Create Ollama analysis tasks for videos with transcriptions but no analysis
      if (videosNeedingAnalysis.length > 0) {
        // Check user settings to see if auto-analyze is enabled
        const userSettings = await storage.getUserSettings(channel.userId);
        const autoAnalyzeEnabled = userSettings?.autoAnalyzeOnRefresh === true; // Must be explicitly true

        if (autoAnalyzeEnabled) {
          console.log(`Worker: Creating AI analysis tasks for ${videosNeedingAnalysis.length} videos (auto-analyze enabled)`);

          for (const videoId of videosNeedingAnalysis) {
            // Get the video details
            const video = await storage.getYoutubeVideo(videoId);

            if (video && video.hasTranscription) {
              // Get the transcription
              const transcription = video.transcription;

              if (transcription) {
                // Queue OpenRouter analysis
                await queueOpenRouterFinancialAnalysis(
                  videoId,
                  transcription,
                  video.title,
                  video.description || '',
                  channel.userId
                );

                console.log(`Worker: Queued OpenRouter analysis for video ${videoId} - ${video.title}`);
              }
            }
          }
        } else {
          console.log(`Worker: Skipping AI analysis for ${videosNeedingAnalysis.length} videos (auto-analyze disabled)`);
        }
      }

      updateProgress(100);

      // Return the videos
      return {
        videos,
        channelId,
        channelTitle: channel.channelTitle,
        totalVideos: videos.length,
        videosNeedingTranscription: videosNeedingTranscription.length,
        videosNeedingAnalysis: videosNeedingAnalysis.length
      };
    } catch (error) {
      console.error(`Worker: Error refreshing channel ${channelId}:`, error);
      throw error;
    }
  });

  // Register handler for batch transcription fetching
  workerManager.registerHandler('batch-fetch-transcriptions', async (task, updateProgress) => {
    const { videos, userId } = task.data;

    try {
      if (!videos || videos.length === 0) {
        return { processed: 0 };
      }

      console.log(`Worker: Batch fetching transcriptions for ${videos.length} videos`);

      // Process videos in batches to avoid overwhelming the system
      const batchSize = 5;
      const batches = [];

      // Split videos into batches
      for (let i = 0; i < videos.length; i += batchSize) {
        batches.push(videos.slice(i, i + batchSize));
      }

      console.log(`Worker: Split videos into ${batches.length} batches of size ${batchSize}`);

      let processedCount = 0;

      // Process each batch sequentially
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Worker: Processing batch ${i + 1}/${batches.length} with ${batch.length} videos`);

        // Update progress based on batch completion
        updateProgress((i / batches.length) * 100);

        // Process each video in the batch concurrently
        await Promise.all(batch.map(async (video) => {
          try {
            // Create individual tasks for each video
            workerManager.addTask(
              'fetch-transcription',
              `Fetch transcription for ${video.title}`,
              `Fetching transcription for video ${video.id}`,
              userId,
              { videoId: video.id },
              TaskPriority.NORMAL
            );

            processedCount++;
          } catch (error) {
            console.error(`Worker: Error processing video ${video.id} in batch:`, error);
            // Continue with other videos even if one fails
          }
        }));
      }

      updateProgress(100);

      return {
        processed: processedCount,
        total: videos.length
      };
    } catch (error) {
      console.error('Worker: Error in batch transcription fetching:', error);
      throw error;
    }
  });

  // Register OpenRouter financial analysis handler
  registerOpenRouterFinancialAnalysisHandler();

  // Register auto-analysis after transcription with OpenRouter
  registerOpenRouterAutoAnalysisAfterTranscription();

  console.log('Task handlers initialized');
}

// Helper function to fetch transcriptions for multiple videos
export async function queueTranscriptionsForVideos(videos: any[], userId: number): Promise<string> {
  // Filter out videos that already have transcriptions
  const videosNeedingTranscription = videos.filter(video => !video.hasTranscription);

  if (videosNeedingTranscription.length === 0) {
    console.log('No videos need transcription, skipping transcription fetching');
    return 'No videos need transcription';
  }

  // Create a batch task for fetching transcriptions
  const task = workerManager.addTask(
    'batch-fetch-transcriptions',
    `Fetch transcriptions for ${videosNeedingTranscription.length} videos`,
    `Fetching transcriptions for ${videosNeedingTranscription.length} videos`,
    userId,
    { videos: videosNeedingTranscription, userId },
    TaskPriority.NORMAL
  );

  return task.id;
}

// Helper function to queue financial analysis for a video
export async function queueFinancialAnalysis(
  videoId: string,
  transcription: string,
  title: string,
  description: string,
  userId: number
): Promise<string> {
  const task = workerManager.addTask(
    'financial-analysis',
    `Analyze financial benefits for ${title}`,
    `Analyzing financial benefits for video ${videoId}`,
    userId,
    { videoId, transcription, title, description },
    TaskPriority.NORMAL
  );

  return task.id;
}

// Helper function to queue channel video refresh
export async function queueChannelVideoRefresh(channelId: string, userId: number): Promise<string> {
  const task = workerManager.addTask(
    'refresh-channel-videos',
    `Refresh videos for channel`,
    `Refreshing videos for channel ${channelId}`,
    userId,
    { channelId, userId },
    TaskPriority.HIGH
  );

  return task.id;
}

// Helper function to queue channel refresh
export async function queueChannelRefresh(channelId: string): Promise<string> {
  // Get the channel to find the user ID
  const channel = await storage.getYoutubeChannel(channelId);
  if (!channel) {
    throw new Error(`Channel not found: ${channelId}`);
  }

  const task = workerManager.addTask(
    'refresh-channel',
    `Refresh channel ${channel.channelTitle}`,
    `Refreshing channel ${channelId} (${channel.channelTitle})`,
    channel.userId,
    { channelId },
    TaskPriority.HIGH
  );

  return task.id;
}

// Start the worker manager
export function startWorkerManager() {
  initializeTaskHandlers();
  workerManager.start();

  // Disable task cleanup for now
  // setInterval(() => {
  //   workerManager.cleanupTasks();
  // }, 24 * 60 * 60 * 1000); // Clean up once a day

  return workerManager;
}
