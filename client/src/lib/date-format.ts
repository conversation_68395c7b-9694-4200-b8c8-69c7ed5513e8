/**
 * Indian Standard Time (IST) timezone offset: UTC+5:30
 */
const IST_TIMEZONE = 'Asia/Kolkata';

/**
 * Formats a date in Indian Standard Time (IST) with a user-friendly format:
 * - "Today at HH:MM AM/PM" for today
 * - "Yesterday at HH:MM AM/PM" for yesterday
 * - "DD/MM at HH:MM AM/PM" for older dates
 */
export function formatRefreshTime(date: Date | string | null): string {
  if (!date) return "Never";

  const refreshDate = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();

  // Check if the date is valid
  if (isNaN(refreshDate.getTime())) return "Invalid date";

  // Format time as HH:MM AM/PM in IST
  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
    timeZone: IST_TIMEZONE
  };
  const timeString = refreshDate.toLocaleTimeString('en-IN', timeOptions);

  // Convert dates to IST for comparison
  const istRefreshDate = new Date(refreshDate.toLocaleString('en-US', { timeZone: IST_TIMEZONE }));
  const istNow = new Date(now.toLocaleString('en-US', { timeZone: IST_TIMEZONE }));

  // Check if it's today in IST
  if (istRefreshDate.getDate() === istNow.getDate() &&
      istRefreshDate.getMonth() === istNow.getMonth() &&
      istRefreshDate.getFullYear() === istNow.getFullYear()) {
    return `Today at ${timeString} IST`;
  }

  // Check if it's yesterday in IST
  const istYesterday = new Date(istNow);
  istYesterday.setDate(istNow.getDate() - 1);
  if (istRefreshDate.getDate() === istYesterday.getDate() &&
      istRefreshDate.getMonth() === istYesterday.getMonth() &&
      istRefreshDate.getFullYear() === istYesterday.getFullYear()) {
    return `Yesterday at ${timeString} IST`;
  }

  // For older dates, show DD/MM format in IST
  const dateOptions: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    timeZone: IST_TIMEZONE
  };
  const dateString = refreshDate.toLocaleDateString('en-IN', dateOptions);

  return `${dateString} at ${timeString} IST`;
}

/**
 * Formats a date in Indian Standard Time (IST) with a standard format
 * @param dateString The date string or Date object to format
 * @param includeTime Whether to include the time in the formatted string
 * @returns Formatted date string in IST
 */
export function formatDate(dateString?: string | Date | null, includeTime: boolean = true): string {
  if (!dateString) return "Never";

  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;

    // Check if the date is valid
    if (isNaN(date.getTime())) return "Invalid date";

    const options: Intl.DateTimeFormatOptions = {
      timeZone: IST_TIMEZONE,
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      ...(includeTime ? {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      } : {})
    };

    return date.toLocaleString('en-IN', options) + (includeTime ? ' IST' : '');
  } catch (e) {
    console.error('Error formatting date:', e);
    return "Invalid date";
  }
}

/**
 * Formats a date in Indian Standard Time (IST) as a relative time (e.g., "2 hours ago")
 * @param dateString The date string or Date object to format
 * @returns Formatted relative time string in IST
 */
export function formatRelativeTime(dateString?: string | Date | null): string {
  if (!dateString) return "Never";

  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;

    // Check if the date is valid
    if (isNaN(date.getTime())) return "Invalid date";

    // Convert to IST
    const istDate = new Date(date.toLocaleString('en-US', { timeZone: IST_TIMEZONE }));
    const istNow = new Date(new Date().toLocaleString('en-US', { timeZone: IST_TIMEZONE }));

    const diffMs = istNow.getTime() - istDate.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);
    const diffMonth = Math.floor(diffDay / 30);
    const diffYear = Math.floor(diffMonth / 12);

    if (diffSec < 60) return diffSec === 1 ? '1 second ago' : `${diffSec} seconds ago`;
    if (diffMin < 60) return diffMin === 1 ? '1 minute ago' : `${diffMin} minutes ago`;
    if (diffHour < 24) return diffHour === 1 ? '1 hour ago' : `${diffHour} hours ago`;
    if (diffDay < 30) return diffDay === 1 ? '1 day ago' : `${diffDay} days ago`;
    if (diffMonth < 12) return diffMonth === 1 ? '1 month ago' : `${diffMonth} months ago`;
    return diffYear === 1 ? '1 year ago' : `${diffYear} years ago`;
  } catch (e) {
    console.error('Error formatting relative time:', e);
    return "Unknown time ago";
  }
}

/**
 * Formats a date in Indian Standard Time (IST) for display in the UI
 * This is a convenience function that can be used in most places
 * @param dateString The date string or Date object to format
 * @returns Formatted date string in IST
 */
export function formatDateForDisplay(dateString?: string | Date | null): string {
  if (!dateString) return "Never";

  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;

    // Check if the date is valid
    if (isNaN(date.getTime())) return "Invalid date";

    // For recent dates (within last 24 hours), show relative time
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);

    if (diffHours < 24) {
      return formatRelativeTime(date);
    }

    // Otherwise show the full date
    return formatDate(date);
  } catch (e) {
    console.error('Error formatting date for display:', e);
    return "Invalid date";
  }
}
