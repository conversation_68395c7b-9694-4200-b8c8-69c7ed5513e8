import axios from 'axios';
import * as cheerio from 'cheerio';

/**
 * Extracts YouTube video IDs from a string containing URLs
 * @param content The content to search for YouTube URLs
 * @returns Array of YouTube video IDs
 */
export function extractYouTubeVideoIds(content: string): string[] {
  if (!content) return [];

  const videoIds: string[] = [];

  // Regular expressions to match different YouTube URL formats
  const patterns = [
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})(?:&[^"&\s]*)?/g,
    /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})(?:\?[^"&\s]*)?/g,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})(?:\?[^"&\s]*)?/g,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([a-zA-Z0-9_-]{11})(?:\?[^"&\s]*)?/g,
  ];

  // Extract video IDs using each pattern
  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      if (match[1] && !videoIds.includes(match[1])) {
        videoIds.push(match[1]);
      }
    }
  }

  return videoIds;
}

/**
 * Fetches the transcript for a YouTube video
 * @param videoId The YouTube video ID
 * @returns The transcript text or null if not available
 */
export async function fetchYouTubeTranscript(videoId: string): Promise<string | null> {
  try {
    console.log(`Fetching transcript for YouTube video: ${videoId}`);

    // First, try the YouTube transcript API
    try {
      const transcript = await fetchTranscriptFromAPI(videoId);
      if (transcript) {
        console.log(`Successfully fetched transcript from API for video ${videoId}`);
        return transcript;
      }
    } catch (apiError) {
      console.log(`API transcript fetch failed for ${videoId}, falling back to scraping`);
    }

    // If API fails, try scraping the transcript from the YouTube page
    const transcript = await scrapeTranscriptFromYouTube(videoId);
    if (transcript) {
      console.log(`Successfully scraped transcript for video ${videoId}`);
      return transcript;
    }

    console.log(`No transcript available for video ${videoId}`);
    return null;
  } catch (error) {
    console.error(`Error fetching YouTube transcript for video ${videoId}:`, error);
    return null;
  }
}

/**
 * Attempts to fetch a transcript using the YouTube transcript API
 */
async function fetchTranscriptFromAPI(videoId: string): Promise<string | null> {
  try {
    // YouTube transcript API endpoint (this is a third-party service)
    const response = await axios.get(`https://youtubetranscript.com/?server_vid=${videoId}`);

    if (response.data && typeof response.data === 'string' && response.data.includes('transcript')) {
      // Parse the response to extract the transcript
      const $ = cheerio.load(response.data);
      const transcriptText = $('#transcript-text').text();

      if (transcriptText && transcriptText.length > 0) {
        return formatTranscript(transcriptText);
      }
    }

    return null;
  } catch (error) {
    console.error(`Error using transcript API for video ${videoId}:`, error);
    return null;
  }
}

/**
 * Attempts to scrape a transcript directly from the YouTube page
 */
async function scrapeTranscriptFromYouTube(videoId: string): Promise<string | null> {
  try {
    // Use a user agent to avoid being blocked
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Accept-Language': 'en-US,en;q=0.9',
    };

    // Fetch the YouTube video page
    const response = await axios.get(`https://www.youtube.com/watch?v=${videoId}`, { headers });
    const html = response.data;

    // Look for transcript data in the page
    const transcriptMatch = html.match(/"captionTracks":\[\{"baseUrl":"([^"]+)"/);
    if (!transcriptMatch || !transcriptMatch[1]) {
      return null;
    }

    // Decode the URL (it's usually encoded in the HTML)
    const transcriptUrl = transcriptMatch[1].replace(/\\u0026/g, '&');

    // Fetch the transcript XML
    const transcriptResponse = await axios.get(transcriptUrl);
    const transcriptXml = transcriptResponse.data;

    // Parse the XML to extract text
    const $ = cheerio.load(transcriptXml, { xmlMode: true });
    const textElements = $('text');

    if (textElements.length === 0) {
      return null;
    }

    // Combine all text elements into a transcript
    let transcript = '';
    textElements.each((_, element) => {
      transcript += $(element).text() + ' ';
    });

    return formatTranscript(transcript);
  } catch (error) {
    console.error(`Error scraping transcript for video ${videoId}:`, error);
    return null;
  }
}

/**
 * Formats a transcript for better readability
 */
function formatTranscript(transcript: string): string {
  // Clean up the transcript
  let formatted = transcript
    .replace(/\s+/g, ' ')  // Replace multiple spaces with a single space
    .trim();

  // Add paragraph breaks every few sentences for readability
  const sentences = formatted.split(/(?<=[.!?])\s+/);
  let result = '';

  for (let i = 0; i < sentences.length; i++) {
    result += sentences[i] + ' ';

    // Add a paragraph break every 3-5 sentences
    if ((i + 1) % 4 === 0 && i < sentences.length - 1) {
      result += '\n\n';
    }
  }

  return result.trim();
}
