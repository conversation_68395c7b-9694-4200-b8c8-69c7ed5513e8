import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useSettings } from '@/hooks/use-settings';
import { useToast } from '@/hooks/use-toast';
import { createPortal } from 'react-dom';
import { useFloatingVideo } from '@/context/floating-video-context';
import { useCastQueue } from '@/context/cast-queue-context';
import { Button } from '@/components/ui/button';
import { X, Minimize, Maximize, ExternalLink, CornerRightDown, CornerLeftDown, CornerLeftUp, CornerRightUp, Maximize2, SkipBack, SkipForward, Cast } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useLocation } from 'wouter';
import { CastButton } from './cast-button';

// Extend Window interface to include our timeout clear functions and YouTube API
declare global {
  interface Window {
    clearDragTimeout?: () => void;
    clearResizeTimeout?: () => void;
    YT?: {
      Player: new (elementId: string, options: any) => any;
    };
    onYouTubeIframeAPIReady?: (() => void) | null;
  }
}

// Aspect ratio constant (16:9)
const ASPECT_RATIO = 16 / 9;
// Minimum width for the player
const MIN_WIDTH = 240;
// Minimum height based on aspect ratio
const MIN_HEIGHT = MIN_WIDTH / ASPECT_RATIO;

// Predefined snap positions
interface SnapPosition {
  id: string;
  name: string;
  getPosition: (windowWidth: number, windowHeight: number, playerWidth: number, playerHeight: number) => { x: number; y: number };
  isInSnapZone: (x: number, y: number, windowWidth: number, windowHeight: number, playerWidth: number, playerHeight: number) => boolean;
}

// Snap threshold - how close to a viewport edge the player needs to be to snap (in pixels)
const SNAP_THRESHOLD = 30;

// Helper function to ensure position is within viewport boundaries
const ensureWithinViewport = (x: number, y: number, width: number, height: number, windowWidth: number, windowHeight: number) => {
  // Ensure x is within bounds
  x = Math.max(0, x); // Don't go off left edge
  x = Math.min(windowWidth - width, x); // Don't go off right edge

  // Ensure y is within bounds
  y = Math.max(0, y); // Don't go off top edge
  y = Math.min(windowHeight - height, y); // Don't go off bottom edge

  return { x, y };
};

export function FloatingVideoPlayer() {
  // Get current location to hide player on login page
  const [location] = useLocation();
  const isAuthPage = location === '/auth';
  // Define snap positions based on viewport edges
  const snapPositions: SnapPosition[] = [
    {
      id: 'top-left',
      name: 'Top Left',
      getPosition: (windowWidth, windowHeight, playerWidth, playerHeight) => {
        // Snap to top-left corner
        return ensureWithinViewport(0, 0, playerWidth, playerHeight, windowWidth, windowHeight);
      },
      isInSnapZone: (x, y, windowWidth, windowHeight, playerWidth, playerHeight) => {
        // Check if left edge of player is near left edge of viewport
        const isLeftEdgeNear = x < SNAP_THRESHOLD;
        // Check if top edge of player is near top edge of viewport
        const isTopEdgeNear = y < SNAP_THRESHOLD;
        return isLeftEdgeNear && isTopEdgeNear;
      }
    },
    {
      id: 'top-right',
      name: 'Top Right',
      getPosition: (windowWidth, windowHeight, playerWidth, playerHeight) => {
        // Snap to top-right corner
        return ensureWithinViewport(windowWidth - playerWidth, 0, playerWidth, playerHeight, windowWidth, windowHeight);
      },
      isInSnapZone: (x, y, windowWidth, windowHeight, playerWidth, playerHeight) => {
        // Check if right edge of player is near right edge of viewport
        const isRightEdgeNear = Math.abs((windowWidth) - (x + playerWidth)) < SNAP_THRESHOLD;
        // Check if top edge of player is near top edge of viewport
        const isTopEdgeNear = y < SNAP_THRESHOLD;
        return isRightEdgeNear && isTopEdgeNear;
      }
    },
    {
      id: 'bottom-left',
      name: 'Bottom Left',
      getPosition: (windowWidth, windowHeight, playerWidth, playerHeight) => {
        // Snap to bottom-left corner
        return ensureWithinViewport(0, windowHeight - playerHeight, playerWidth, playerHeight, windowWidth, windowHeight);
      },
      isInSnapZone: (x, y, windowWidth, windowHeight, playerWidth, playerHeight) => {
        // Check if left edge of player is near left edge of viewport
        const isLeftEdgeNear = x < SNAP_THRESHOLD;
        // Check if bottom edge of player is near bottom edge of viewport
        const isBottomEdgeNear = Math.abs((windowHeight) - (y + playerHeight)) < SNAP_THRESHOLD;
        return isLeftEdgeNear && isBottomEdgeNear;
      }
    },
    {
      id: 'bottom-right',
      name: 'Bottom Right',
      getPosition: (windowWidth, windowHeight, playerWidth, playerHeight) => {
        // Snap to bottom-right corner
        return ensureWithinViewport(windowWidth - playerWidth, windowHeight - playerHeight, playerWidth, playerHeight, windowWidth, windowHeight);
      },
      isInSnapZone: (x, y, windowWidth, windowHeight, playerWidth, playerHeight) => {
        // Check if right edge of player is near right edge of viewport
        const isRightEdgeNear = Math.abs((windowWidth) - (x + playerWidth)) < SNAP_THRESHOLD;
        // Check if bottom edge of player is near bottom edge of viewport
        const isBottomEdgeNear = Math.abs((windowHeight) - (y + playerHeight)) < SNAP_THRESHOLD;
        return isRightEdgeNear && isBottomEdgeNear;
      }
    }
  ];
  const {
    state,
    closeFloatingVideo,
    updatePosition,
    updateSize,
    toggleMinimize,
    isControlsVisible,
    setControlsVisible,
    playNextVideo,
    openFloatingVideo
  } = useFloatingVideo();

  const castQueue = useCastQueue();
  const {
    playNext: playNextInQueue,
    playPrevious: playPreviousInQueue,
    getCurrentVideo: getCurrentQueueVideo,
    state: castQueueState
  } = castQueue;

  const { isOpen, video, position, size, isMinimized } = state;

  const playerRef = useRef<HTMLDivElement>(null);
  const dragRef = useRef<{ isDragging: boolean; startX: number; startY: number }>({
    isDragging: false,
    startX: 0,
    startY: 0
  });
  const resizeRef = useRef<{
    isResizing: boolean;
    startWidth: number;
    startHeight: number;
    startX: number;
    startY: number;
    direction: string;
    initialDistance?: number; // For pinch-to-zoom
    initialScale?: number; // For pinch-to-zoom
  }>({
    isResizing: false,
    startWidth: 0,
    startHeight: 0,
    startX: 0,
    startY: 0,
    direction: ''
  });

  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const [windowSize, setWindowSize] = useState<{ width: number; height: number }>({
    width: typeof window !== 'undefined' ? window.innerWidth : 1920,
    height: typeof window !== 'undefined' ? window.innerHeight : 1080
  });
  const [isSnapping, setIsSnapping] = useState(false);
  const [snapIndicator, setSnapIndicator] = useState<string | null>(null);
  const [seekIndicator, setSeekIndicator] = useState<{direction: string, time: number} | null>(null);

  // Reference to the YouTube player instance
  const playerInstanceRef = useRef<any>(null);
  const playerStateRef = useRef<{currentTime: number, isPlaying: boolean, watchTracked: boolean}>({
    currentTime: 0,
    isPlaying: false,
    watchTracked: false
  });

  // Get the settings functions
  const { settings, updateSettings } = useSettings();
  const { toast } = useToast();

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Create portal container on mount and add keyboard listener
  useEffect(() => {
    let container = document.getElementById('floating-video-portal');
    if (!container) {
      container = document.createElement('div');
      container.id = 'floating-video-portal';
      document.body.appendChild(container);
    }
    setPortalContainer(container);

    // Add keyboard listener for player controls
    const handleKeyDown = (e: KeyboardEvent) => {
      // Handle Escape key
      if (e.key === 'Escape') {
        // If we're dragging or resizing, cancel that first
        if (dragRef.current.isDragging || resizeRef.current.isResizing) {
          // Cancel dragging
          if (dragRef.current.isDragging) {
            dragRef.current.isDragging = false;
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            if (playerRef.current) {
              playerRef.current.classList.remove('dragging');
              playerRef.current.style.transition = '';
            }
          }

          // Cancel resizing
          if (resizeRef.current.isResizing) {
            resizeRef.current.isResizing = false;
            document.removeEventListener('mousemove', handleResizeMove);
            document.removeEventListener('mouseup', handleResizeEnd);
            if (playerRef.current) {
              playerRef.current.classList.remove('resizing');
            }
          }
        } else if (isOpen) {
          // If not dragging or resizing, close the player
          closeFloatingVideo();
        }
        return;
      }

      // Handle arrow keys for seeking
      if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
        // Only if player is open and initialized
        if (isOpen && playerInstanceRef.current) {
          try {
            // Get current time
            const currentTime = playerInstanceRef.current.getCurrentTime ? playerInstanceRef.current.getCurrentTime() : 0;

            // Seek forward or backward by 10 seconds
            const seekAmount = e.key === 'ArrowRight' ? 10 : -10;
            const newTime = Math.max(0, currentTime + seekAmount);

            // Apply the seek
            if (playerInstanceRef.current.seekTo) {
              playerInstanceRef.current.seekTo(newTime);

              // Show a visual indicator
              const direction = e.key === 'ArrowRight' ? 'forward' : 'backward';
              console.log(`Seeking ${direction} to ${newTime}s`);

              // Set seek indicator
              setSeekIndicator({ direction, time: newTime });

              // Hide indicator after a short time
              setTimeout(() => setSeekIndicator(null), 1000);

              // Prevent default browser behavior (like scrolling)
              e.preventDefault();
            }
          } catch (error) {
            console.error('Error seeking video:', error);
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    // Add custom event listener for seeking to timestamps from other components
    const handleSeekEvent = (event: CustomEvent) => {
      const { videoId, timestamp } = event.detail;

      console.log(`Received seek event for video ${videoId} to timestamp ${timestamp}s`);

      // Only handle if this is the current video
      if (video && video.id === videoId) {
        console.log(`Video matches current video (${video.id}), attempting to seek`);

        // Function to perform the actual seeking
        const performSeek = () => {
          try {
            if (playerInstanceRef.current && typeof playerInstanceRef.current.seekTo === 'function') {
              console.log(`Player ready, seeking to ${timestamp}s`);

              // Force the player to play if it's paused
              if (playerInstanceRef.current.getPlayerState() !== 1) { // 1 = playing
                playerInstanceRef.current.playVideo();
              }

              console.log(`CRITICAL: About to seek to ${timestamp} seconds`);

              // Force play first to ensure the player is active
              playerInstanceRef.current.playVideo();

              // Wait a moment for the player to start playing
              setTimeout(() => {
                // Seek to the timestamp (second parameter true = seek and then play)
                console.log(`CRITICAL: Actually seeking to ${timestamp} seconds`);
                playerInstanceRef.current.seekTo(timestamp, true);

                // Force play again after seeking
                setTimeout(() => {
                  playerInstanceRef.current.playVideo();
                  console.log(`CRITICAL: Forced play after seeking`);
                }, 500);
              }, 500);

              // Show seek indicator
              setSeekIndicator({ direction: 'timestamp', time: timestamp });

              // Hide indicator after a short time
              setTimeout(() => setSeekIndicator(null), 2000);

              return true; // Seek was successful
            }
            return false; // Player not ready
          } catch (error) {
            console.error('Error seeking to timestamp:', error);
            return false;
          }
        };

        // Try to seek immediately
        if (!performSeek()) {
          console.log('Player not ready, will retry seeking in 500ms');

          // If not successful, try again after a short delay
          let attempts = 0;
          const maxAttempts = 10;
          const attemptInterval = 500; // ms

          const seekInterval = setInterval(() => {
            attempts++;
            console.log(`Seek attempt ${attempts}/${maxAttempts}`);

            if (performSeek() || attempts >= maxAttempts) {
              clearInterval(seekInterval);
              if (attempts >= maxAttempts) {
                console.error('Failed to seek after maximum attempts');
              }
            }
          }, attemptInterval);
        }
      } else {
        console.warn(`Video mismatch or player not ready: current=${video?.id}, target=${videoId}`);
      }
    };

    // Add the event listener
    window.addEventListener('seek-floating-video', handleSeekEvent as EventListener);

    // Add a click listener to detect clicks outside the player to cancel drag/resize
    const handleDocumentClick = (e: MouseEvent) => {
      if (playerRef.current && !playerRef.current.contains(e.target as Node)) {
        // Cancel dragging
        if (dragRef.current.isDragging) {
          dragRef.current.isDragging = false;
          document.removeEventListener('mousemove', handleMouseMove);
          document.removeEventListener('mouseup', handleMouseUp);
          if (playerRef.current) {
            playerRef.current.classList.remove('dragging');
            playerRef.current.style.transition = '';
          }
        }

        // Cancel resizing
        if (resizeRef.current.isResizing) {
          resizeRef.current.isResizing = false;
          document.removeEventListener('mousemove', handleResizeMove);
          document.removeEventListener('mouseup', handleResizeEnd);
          if (playerRef.current) {
            playerRef.current.classList.remove('resizing');
          }
        }
      }
    };

    document.addEventListener('click', handleDocumentClick);

    return () => {
      // Clean up only if we created it
      if (container && container.parentNode && container.childNodes.length === 0) {
        container.parentNode.removeChild(container);
      }

      // Remove event listeners
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('click', handleDocumentClick);
      window.removeEventListener('seek-floating-video', handleSeekEvent as EventListener);

      // Ensure we clean up any lingering event listeners
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
      document.removeEventListener('touchcancel', handleTouchEnd);
    };
  }, [isOpen, closeFloatingVideo]);

  // Handle double click to show/hide controls
  const handleDoubleClick = (e: React.MouseEvent) => {
    // Prevent double click from triggering drag
    e.stopPropagation();
    setControlsVisible(!isControlsVisible);
  };

  // Check if the player is near any viewport edge and snap if needed during dragging
  const checkAndSnapToPosition = (x: number, y: number) => {
    if (!playerRef.current) return null;

    // Get current player dimensions directly from the element to ensure we have the most up-to-date size
    const playerWidth = playerRef.current.offsetWidth || size.width;
    const playerHeight = playerRef.current.offsetHeight || size.height;

    // Check if we're in any snap zone based on the player's edges
    for (const snapPosition of snapPositions) {
      if (snapPosition.isInSnapZone(x, y, windowSize.width, windowSize.height, playerWidth, playerHeight)) {
        // Calculate new position based on the snap position
        const newPosition = snapPosition.getPosition(
          windowSize.width,
          windowSize.height,
          playerWidth,
          playerHeight
        );

        // Show snap indicator
        setSnapIndicator(snapPosition.id);
        setIsSnapping(true);

        // Apply smooth transition without affecting the video container
        if (playerRef.current) {
          // Save the video container to prevent it from being affected by the transition
          const videoContainer = document.getElementById('youtube-player-container');
          if (videoContainer) {
            // Temporarily remove transition from the video container
            videoContainer.style.transition = 'none';
          }

          playerRef.current.style.transition = 'left 0.15s ease, top 0.15s ease';
          playerRef.current.style.left = `${newPosition.x}px`;
          playerRef.current.style.top = `${newPosition.y}px`;

          // Reset transition after animation completes
          setTimeout(() => {
            if (playerRef.current) {
              playerRef.current.style.transition = '';
            }
            setIsSnapping(false);
            setTimeout(() => setSnapIndicator(null), 1000); // Hide indicator after 1 second
          }, 150);
        }

        return newPosition;
      }
    }

    // Not in any snap zone
    setSnapIndicator(null);
    return null;
  };



  // Find the nearest edge position to snap to when dropping the player
  const findNearestEdgePosition = (x: number, y: number) => {
    if (!playerRef.current) return null;

    // Get current player dimensions directly from the element to ensure we have the most up-to-date size
    // This is crucial after resizing
    const playerWidth = playerRef.current.offsetWidth || size.width;
    const playerHeight = playerRef.current.offsetHeight || size.height;

    // First ensure the player is within viewport
    const safePosition = ensureWithinViewport(x, y, playerWidth, playerHeight, windowSize.width, windowSize.height);

    // If player is too large for the window, snap to top-left
    const isPlayerTooLarge = playerWidth > windowSize.width * 0.8 || playerHeight > windowSize.height * 0.8;
    if (isPlayerTooLarge) {
      // Use top-left position with safety margins
      const topLeftPosition = { x: 0, y: 0 };

      // Show snap indicator
      setSnapIndicator('top-left');
      setIsSnapping(true);

      // Apply smooth transition without affecting the video container
      if (playerRef.current) {
        // Save the video container to prevent it from being affected by the transition
        const videoContainer = document.getElementById('youtube-player-container');
        if (videoContainer) {
          // Temporarily remove transition from the video container
          videoContainer.style.transition = 'none';
        }

        playerRef.current.style.transition = 'left 0.2s ease, top 0.2s ease';
        playerRef.current.style.left = `${topLeftPosition.x}px`;
        playerRef.current.style.top = `${topLeftPosition.y}px`;

        // Reset transition after animation completes
        setTimeout(() => {
          if (playerRef.current) {
            playerRef.current.style.transition = '';
          }
          setIsSnapping(false);
          setTimeout(() => setSnapIndicator(null), 1000); // Hide indicator after 1 second
        }, 200);
      }

      return topLeftPosition;
    }

    // Check if any edge of the player is near a viewport edge
    // This prioritizes edge snapping over distance calculations
    for (const snapPos of snapPositions) {
      if (snapPos.isInSnapZone(safePosition.x, safePosition.y, windowSize.width, windowSize.height, playerWidth, playerHeight)) {
        const targetPos = snapPos.getPosition(
          windowSize.width,
          windowSize.height,
          playerWidth,
          playerHeight
        );

        // Show snap indicator
        setSnapIndicator(snapPos.id);
        setIsSnapping(true);

        // Apply smooth transition without affecting the video container
        if (playerRef.current) {
          // Save the video container to prevent it from being affected by the transition
          const videoContainer = document.getElementById('youtube-player-container');
          if (videoContainer) {
            // Temporarily remove transition from the video container
            videoContainer.style.transition = 'none';
          }

          playerRef.current.style.transition = 'left 0.2s ease, top 0.2s ease';
          playerRef.current.style.left = `${targetPos.x}px`;
          playerRef.current.style.top = `${targetPos.y}px`;

          // Reset transition after animation completes
          setTimeout(() => {
            if (playerRef.current) {
              playerRef.current.style.transition = '';
            }
            setIsSnapping(false);
            setTimeout(() => setSnapIndicator(null), 1000); // Hide indicator after 1 second
          }, 200);
        }

        return targetPos;
      }
    }

    // If no edge is near a viewport edge, calculate distances to each snap position
    const distances = snapPositions.map(snapPos => {
      // Get the target position for this snap point
      let targetPos = snapPos.getPosition(
        windowSize.width,
        windowSize.height,
        playerWidth,
        playerHeight
      );

      // Calculate Euclidean distance from current position to this snap point
      const dx = targetPos.x - safePosition.x;
      const dy = targetPos.y - safePosition.y;
      return {
        position: snapPos,
        targetPos,
        distance: Math.sqrt(dx * dx + dy * dy)
      };
    });

    // Find the closest position
    const closest = distances.reduce((prev, current) =>
      (current.distance < prev.distance) ? current : prev
    );

    // Use the pre-calculated target position that's guaranteed to be within viewport
    const newPosition = closest.targetPos;

    // Show snap indicator
    setSnapIndicator(closest.position.id);
    setIsSnapping(true);

    // Apply smooth transition without affecting the video container
    if (playerRef.current) {
      // Save the video container to prevent it from being affected by the transition
      const videoContainer = document.getElementById('youtube-player-container');
      if (videoContainer) {
        // Temporarily remove transition from the video container
        videoContainer.style.transition = 'none';
      }

      playerRef.current.style.transition = 'left 0.2s ease, top 0.2s ease';
      playerRef.current.style.left = `${newPosition.x}px`;
      playerRef.current.style.top = `${newPosition.y}px`;

      // Reset transition after animation completes
      setTimeout(() => {
        if (playerRef.current) {
          playerRef.current.style.transition = '';
        }
        setIsSnapping(false);
        setTimeout(() => setSnapIndicator(null), 1000); // Hide indicator after 1 second
      }, 200);
    }

    return newPosition;
  };

  // Calculate distance between two touch points
  const getDistance = (touch1: Touch, touch2: Touch) => {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  };

  // Calculate center point between two touches
  const getCenter = (touch1: Touch, touch2: Touch) => {
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2
    };
  };

  // Handle touch start for pinch-to-zoom
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.touches.length !== 2 || isMinimized) return;

    e.preventDefault();
    e.stopPropagation();

    const touch1 = e.touches[0];
    const touch2 = e.touches[1];
    const distance = getDistance(touch1, touch2);
    const center = getCenter(touch1, touch2);

    // Store initial values for the pinch gesture
    resizeRef.current = {
      ...resizeRef.current,
      isResizing: true,
      startWidth: size.width,
      startHeight: size.height,
      startX: center.x,
      startY: center.y,
      direction: 'pinch',
      initialDistance: distance,
      initialScale: 1
    };

    // Add event listeners for touch events
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
    document.addEventListener('touchcancel', handleTouchEnd);

    // Add resizing class for visual feedback
    if (playerRef.current) {
      playerRef.current.classList.add('resizing');
    }
  }, [size, isMinimized]);

  // Handle touch move for pinch-to-zoom
  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!resizeRef.current.isResizing || !playerRef.current || e.touches.length !== 2) return;

    e.preventDefault();

    const touch1 = e.touches[0];
    const touch2 = e.touches[1];
    const currentDistance = getDistance(touch1, touch2);
    const initialDistance = resizeRef.current.initialDistance || 1;

    // Calculate scale factor based on the change in distance
    const scale = currentDistance / initialDistance;

    // Calculate new dimensions while maintaining aspect ratio
    let newWidth = Math.max(MIN_WIDTH, resizeRef.current.startWidth * scale);
    let newHeight = newWidth / ASPECT_RATIO;

    // Calculate center point of the pinch gesture
    const center = getCenter(touch1, touch2);

    // Calculate new position to keep the player centered on the pinch point
    const centerOffsetX = (center.x - position.x) / size.width;
    const centerOffsetY = (center.y - position.y) / size.height;

    const newX = center.x - (newWidth * centerOffsetX);
    const newY = center.y - (newHeight * centerOffsetY);

    // Apply changes directly to the element for smoother resizing
    const currentStyle = playerRef.current.style;
    currentStyle.width = `${newWidth}px`;
    currentStyle.height = `${newHeight}px`;
    currentStyle.left = `${newX}px`;
    currentStyle.top = `${newY}px`;
  }, [position.x, position.y, size.width, size.height]);

  // Handle touch end for pinch-to-zoom
  const handleTouchEnd = useCallback(() => {
    if (!resizeRef.current.isResizing || !playerRef.current) return;

    resizeRef.current.isResizing = false;

    // Get final dimensions and position from the element
    const finalWidth = parseInt(playerRef.current.style.width, 10) || size.width;
    const finalHeight = parseInt(playerRef.current.style.height, 10) || size.height;
    const finalX = parseInt(playerRef.current.style.left, 10) || position.x;
    const finalY = parseInt(playerRef.current.style.top, 10) || position.y;

    // Update state with final values
    updateSize({ width: finalWidth, height: finalHeight });
    updatePosition({ x: finalX, y: finalY });

    // Remove resizing class
    playerRef.current.classList.remove('resizing');

    // Remove event listeners
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
    document.removeEventListener('touchcancel', handleTouchEnd);
  }, [size.width, size.height, position.x, position.y, updateSize, updatePosition]);


  // Handle drag start - now only triggered by the drag handle
  const handleDragStart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    dragRef.current = {
      isDragging: true,
      startX: e.clientX - position.x,
      startY: e.clientY - position.y
    };

    // Add event listeners for drag
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Add a dragging class to improve performance
    if (playerRef.current) {
      playerRef.current.style.transition = 'none';
      playerRef.current.classList.add('dragging');
    }

    // Set a safety timeout to cancel dragging if mouse is released outside the window
    const safetyTimeout = setTimeout(() => {
      if (dragRef.current.isDragging) {
        dragRef.current.isDragging = false;
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        if (playerRef.current) {
          playerRef.current.classList.remove('dragging');
          playerRef.current.style.transition = '';

          // Update position with current style values
          const finalX = parseInt(playerRef.current.style.left, 10) || position.x;
          const finalY = parseInt(playerRef.current.style.top, 10) || position.y;
          updatePosition({ x: finalX, y: finalY });
        }
      }
    }, 5000); // 5 seconds safety timeout

    // Store the timeout ID so we can clear it in handleMouseUp
    const timeoutId = safetyTimeout;

    // Add a cleanup function to the window object
    window.clearDragTimeout = () => {
      clearTimeout(timeoutId);
    };
  };

  // Handle dragging
  const handleMouseMove = (e: MouseEvent) => {
    if (!dragRef.current.isDragging || !playerRef.current) return;

    // Check if cursor is outside the viewport
    const isOutsideViewport =
      e.clientX <= 0 ||
      e.clientX >= windowSize.width ||
      e.clientY <= 0 ||
      e.clientY >= windowSize.height;

    if (isOutsideViewport) {
      // If cursor is outside viewport, treat it as a drop at the edge
      // Calculate the position as if dropped at the edge
      let edgeX = e.clientX;
      let edgeY = e.clientY;

      // Clamp to viewport edges
      if (edgeX < 0) edgeX = 0;
      if (edgeX > windowSize.width) edgeX = windowSize.width;
      if (edgeY < 0) edgeY = 0;
      if (edgeY > windowSize.height) edgeY = windowSize.height;

      // Calculate new position based on the edge position
      const newX = edgeX - dragRef.current.startX;
      const newY = edgeY - dragRef.current.startY;

      // Update position
      playerRef.current.style.left = `${newX}px`;
      playerRef.current.style.top = `${newY}px`;

      // End the drag operation
      handleMouseUp();
      return;
    }

    const newX = e.clientX - dragRef.current.startX;
    const newY = e.clientY - dragRef.current.startY;

    // Use direct style manipulation for smoother dragging
    playerRef.current.style.left = `${newX}px`;
    playerRef.current.style.top = `${newY}px`;

    // Check for snap zones while dragging
    checkAndSnapToPosition(newX, newY);
  };

  // Handle drag end
  const handleMouseUp = () => {
    // Clear the safety timeout
    if (window.clearDragTimeout) {
      window.clearDragTimeout();
    }

    if (!dragRef.current.isDragging) return;

    dragRef.current.isDragging = false;

    // Remove event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    // Update position in state after dragging ends
    if (playerRef.current) {
      const newX = parseInt(playerRef.current.style.left, 10) || position.x;
      const newY = parseInt(playerRef.current.style.top, 10) || position.y;

      // Always find and snap to the nearest edge position when dropping
      const nearestEdgePosition = findNearestEdgePosition(newX, newY);

      // Update position in state
      updatePosition(nearestEdgePosition || { x: newX, y: newY });

      // Restore transition when animation completes (handled in findNearestEdgePosition)
      playerRef.current.classList.remove('dragging');
    }
  };

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent, direction: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (!playerRef.current) return;

    // Get current dimensions directly from the DOM for accuracy
    const currentWidth = playerRef.current.offsetWidth;
    const currentHeight = playerRef.current.offsetHeight;

    resizeRef.current = {
      isResizing: true,
      startWidth: currentWidth,
      startHeight: currentHeight,
      startX: e.clientX,
      startY: e.clientY,
      direction
    };

    // Add event listeners for resize
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);

    // Set a safety timeout to cancel resizing if mouse is released outside the window
    const safetyTimeout = setTimeout(() => {
      if (resizeRef.current.isResizing) {
        resizeRef.current.isResizing = false;
        document.removeEventListener('mousemove', handleResizeMove);
        document.removeEventListener('mouseup', handleResizeEnd);
        if (playerRef.current) {
          playerRef.current.classList.remove('resizing');

          // Update size and position with current style values
          const finalWidth = playerRef.current.offsetWidth;
          const finalHeight = playerRef.current.offsetHeight;
          const finalX = parseInt(playerRef.current.style.left, 10) || position.x;
          const finalY = parseInt(playerRef.current.style.top, 10) || position.y;

          updateSize({ width: finalWidth, height: finalHeight });
          updatePosition({ x: finalX, y: finalY });
        }
      }
    }, 5000); // 5 seconds safety timeout

    // Store the timeout ID so we can clear it in handleResizeEnd
    const timeoutId = safetyTimeout;

    // Add a cleanup function to the window object
    window.clearResizeTimeout = () => {
      clearTimeout(timeoutId);
    };
  };

  // Handle resizing
  const handleResizeMove = (e: MouseEvent) => {
    if (!resizeRef.current.isResizing || !playerRef.current) return;

    const { startWidth, startHeight, startX, startY, direction } = resizeRef.current;

    // Use direct style manipulation for smoother resizing
    const currentStyle = playerRef.current.style;

    // Calculate new dimensions and position based on resize direction
    let newWidth = startWidth;
    let newHeight = startHeight;
    let newX = position.x;
    let newY = position.y;

    // Calculate new dimensions based on resize direction - simplified logic
    switch (direction) {
      case 'e': // Right
        newWidth = Math.max(MIN_WIDTH, startWidth + (e.clientX - startX));
        newHeight = newWidth / ASPECT_RATIO;
        break;
      case 'w': // Left
        newWidth = Math.max(MIN_WIDTH, startWidth - (e.clientX - startX));
        newHeight = newWidth / ASPECT_RATIO;
        newX = position.x - (newWidth - startWidth);
        break;
      case 's': // Bottom
        newHeight = Math.max(MIN_HEIGHT, startHeight + (e.clientY - startY));
        newWidth = newHeight * ASPECT_RATIO;
        break;
      case 'n': // Top
        newHeight = Math.max(MIN_HEIGHT, startHeight - (e.clientY - startY));
        newWidth = newHeight * ASPECT_RATIO;
        newY = position.y - (newHeight - startHeight);
        break;
      case 'se': // Bottom-right
        newWidth = Math.max(MIN_WIDTH, startWidth + (e.clientX - startX));
        newHeight = newWidth / ASPECT_RATIO;
        break;
      case 'sw': // Bottom-left
        newWidth = Math.max(MIN_WIDTH, startWidth - (e.clientX - startX));
        newHeight = newWidth / ASPECT_RATIO;
        newX = position.x - (newWidth - startWidth);
        break;
      case 'ne': // Top-right
        newWidth = Math.max(MIN_WIDTH, startWidth + (e.clientX - startX));
        newHeight = newWidth / ASPECT_RATIO;
        newY = position.y - (newHeight - startHeight);
        break;
      case 'nw': // Top-left
        newWidth = Math.max(MIN_WIDTH, startWidth - (e.clientX - startX));
        newHeight = newWidth / ASPECT_RATIO;
        newX = position.x - (newWidth - startWidth);
        newY = position.y - (newHeight - startHeight);
        break;
    }

    // Apply changes directly to the element for smoother resizing
    currentStyle.width = `${newWidth}px`;
    currentStyle.height = `${newHeight}px`;
    currentStyle.left = `${newX}px`;
    currentStyle.top = `${newY}px`;

    // Add resizing class for visual feedback
    playerRef.current.classList.add('resizing');
  };

  // Handle resize end
  const handleResizeEnd = () => {
    // Clear the safety timeout
    if (window.clearResizeTimeout) {
      window.clearResizeTimeout();
    }

    if (!resizeRef.current.isResizing || !playerRef.current) return;

    resizeRef.current.isResizing = false;

    // Get final dimensions and position directly from the DOM
    const finalWidth = playerRef.current.offsetWidth;
    const finalHeight = playerRef.current.offsetHeight;
    const finalX = parseInt(playerRef.current.style.left, 10) || position.x;
    const finalY = parseInt(playerRef.current.style.top, 10) || position.y;

    // Simple boundary check - only adjust if completely outside viewport
    let adjustedX = finalX;
    let adjustedY = finalY;

    if (adjustedX + finalWidth < 0) adjustedX = 0;
    if (adjustedY + finalHeight < 0) adjustedY = 0;
    if (adjustedX > windowSize.width) adjustedX = windowSize.width - finalWidth;
    if (adjustedY > windowSize.height) adjustedY = windowSize.height - finalHeight;

    // Apply adjusted position only if needed
    if (adjustedX !== finalX || adjustedY !== finalY) {
      playerRef.current.style.left = `${adjustedX}px`;
      playerRef.current.style.top = `${adjustedY}px`;
    }

    // Update state with final values
    updateSize({ width: finalWidth, height: finalHeight });
    updatePosition({ x: adjustedX, y: adjustedY });

    // Remove resizing class
    playerRef.current.classList.remove('resizing');

    // Remove event listeners
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);

    // No automatic snapping after resize - player stays where it is

    // Restore player state after resizing
    if (playerInstanceRef.current && playerStateRef.current) {
      const restoreAfterResize = () => {
        try {
          console.log('Restoring player state after resize:', playerStateRef.current);

          // Make sure the player is ready and has the necessary methods
          if (!playerInstanceRef.current || typeof playerInstanceRef.current.seekTo !== 'function') {
            console.log('Player not ready for seeking after resize, retrying in 100ms');
            setTimeout(restoreAfterResize, 100);
            return;
          }

          // If it was playing, resume playback at the same position
          if (playerStateRef.current.isPlaying) {
            playerInstanceRef.current.seekTo(playerStateRef.current.currentTime);
            playerInstanceRef.current.playVideo();
          } else {
            // If it was paused, just seek to the right position
            playerInstanceRef.current.seekTo(playerStateRef.current.currentTime);
          }
        } catch (e) {
          console.error('Error restoring player state after resize:', e);
        }
      };

      // Wait a moment for the player to be ready after resize
      setTimeout(restoreAfterResize, 200);
    }
  };

  // Open in external player
  const openExternal = () => {
    if (video) {
      window.open(`https://www.youtube.com/watch?v=${video.id}`, '_blank');
    }
  };


  // Track the current video ID to prevent unnecessary player reinitialization
  const currentVideoIdRef = useRef<string | null>(null);

  // Track if the player is currently being initialized
  const isInitializingRef = useRef<boolean>(false);

  // Setup YouTube API for detecting when video ends - further optimized for memory usage
  useEffect(() => {
    if (!isOpen || !video) return;

    // Check if we're already playing this video
    // If so, don't reinitialize the player
    if (currentVideoIdRef.current === video.id && playerInstanceRef.current) {
      return;
    }

    // Check if we're already initializing the player
    if (isInitializingRef.current) {
      return;
    }

    // Set the initializing flag
    isInitializingRef.current = true;

    // Update the current video ID reference
    currentVideoIdRef.current = video.id;

    // Use the ref instead of a local variable
    playerInstanceRef.current = null;
    let initializationTimer: NodeJS.Timeout | null = null;

    // Function to initialize the player
    const initPlayer = () => {
      try {
        // Wait for container to be available
        const container = document.getElementById('youtube-player-container');
        if (!container || !window.YT || !window.YT.Player) {
          // If not ready yet, try again in a moment
          initializationTimer = setTimeout(initPlayer, 500);
          return;
        }

        // Clear the container first
        while (container.firstChild) {
          container.removeChild(container.firstChild);
        }

        // Create a new player directly in the container with optimized settings
        playerInstanceRef.current = new window.YT.Player('youtube-player-container', {
          videoId: video.id,
          playerVars: {
            autoplay: 1, // Enable autoplay when player is created
            modestbranding: 1,
            showinfo: 0,
            fs: 1,
            rel: 0,
            playsinline: 1,
            controls: 1,
            disablekb: 0,
            iv_load_policy: 3, // Hide annotations
            cc_load_policy: 0, // Don't show captions by default
            origin: window.location.origin,
            color: 'white',
            widget_referrer: window.location.origin, // Helps with memory usage
            enablejsapi: 1
          },
          events: {
            'onReady': (event: any) => {
              // Start playback when the player is ready
              event.target.playVideo();

              // Set playback quality to reduce memory usage
              // Options: small, medium, large, hd720, hd1080, highres
              // Using 'medium' or 'large' reduces memory usage compared to HD
              try {
                event.target.setPlaybackQuality('large');
              } catch (e) {
                // Silent error handling
              }
            },
            'onStateChange': (event: any) => {
              // Update player state reference
              if (playerInstanceRef.current) {
                try {
                  // Get current time
                  const currentTime = playerInstanceRef.current.getCurrentTime() || 0;

                  // Store current time and playing state
                  playerStateRef.current = {
                    currentTime: currentTime,
                    isPlaying: event.data === 1, // 1 = playing
                    watchTracked: playerStateRef.current.watchTracked
                  };

                  // Mark video as watched after 30 seconds of playback
                  if (video && currentTime >= 30 && !playerStateRef.current.watchTracked) {
                    // Add to watched videos
                    const watchedVideos = [...(settings?.watchedVideos || []), video.id];
                    updateSettings.mutate({ ...settings, watchedVideos });
                    playerStateRef.current.watchTracked = true;
                  }
                } catch (e) {
                  // Silent error handling
                }
              }

              // When video ends (state = 0), play next video
              if (event.data === 0 && video) {
                // Add a small delay to ensure the video has fully ended
                setTimeout(() => {
                  try {
                    // If we have videos in the cast queue and the current video is in the queue,
                    // play the next video in the queue
                    if (castQueueState.queue.length > 0 && castQueueState.queue.some(v => v.id === video.id)) {
                      const nextVideo = playNextInQueue();
                      if (nextVideo) {
                        // Pass isCasting: true to prevent feed reload when playing next video
                        openFloatingVideo(nextVideo, { isCasting: true });
                      }
                    } else {
                      // Otherwise use the regular feed-based next video functionality
                      playNextVideo();
                    }
                  } catch (error) {
                    // Silent error handling
                  }
                }, 500);
              }
            },
            'onError': (event: any) => {
              // Silent error handling in production
              if (process.env.NODE_ENV !== 'production') {
                console.error('YouTube player error:', event.data);
              }
            }
          }
        });

        // Reset the initializing flag
        isInitializingRef.current = false;
      } catch (error) {
        // Reset the initializing flag even on error
        isInitializingRef.current = false;
      }
    };

    // Load YouTube API if not already loaded
    if (!window.YT) {
      // Check if the script is already being loaded
      if (!document.querySelector('script[src*="youtube.com/iframe_api"]')) {
        // Store the original callback if it exists
        const originalCallback = window.onYouTubeIframeAPIReady;

        // Create a global callback function that calls both callbacks
        window.onYouTubeIframeAPIReady = () => {
          // Call our callback
          initPlayer();

          // Call the original callback if it exists
          if (typeof originalCallback === 'function') {
            originalCallback();
          }
        };

        const tag = document.createElement('script');
        tag.src = 'https://www.youtube.com/iframe_api';
        tag.async = true; // Load asynchronously to reduce blocking
        const firstScriptTag = document.getElementsByTagName('script')[0];
        if (firstScriptTag && firstScriptTag.parentNode) {
          firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
        } else {
          document.head.appendChild(tag);
        }
      } else {
        // Script is loading but not ready yet, set up our callback
        const originalCallback = window.onYouTubeIframeAPIReady;
        window.onYouTubeIframeAPIReady = () => {
          initPlayer();
          if (typeof originalCallback === 'function') {
            originalCallback();
          }
        };
      }
    } else {
      // If API is already loaded, initialize player
      initPlayer();
    }

    // Cleanup function
    return () => {
      // Clean up
      if (initializationTimer) {
        clearTimeout(initializationTimer);
      }
      if (playerInstanceRef.current && typeof playerInstanceRef.current.destroy === 'function') {
        try {
          playerInstanceRef.current.destroy();
          playerInstanceRef.current = null;
        } catch (error) {
          // Silent error handling
        }
      }
      if (typeof window.onYouTubeIframeAPIReady === 'function') {
        window.onYouTubeIframeAPIReady = null;
      }

      // Reset the initializing flag
      isInitializingRef.current = false;
    };
  }, [isOpen, video, playNextVideo, settings, updateSettings, playNextInQueue, openFloatingVideo, castQueueState.queue]);

  // Effect to restore player state after snapping or resizing
  useEffect(() => {
    if (isSnapping && playerInstanceRef.current && playerStateRef.current) {
      const restorePlayerState = () => {
        try {
          console.log('Restoring player state:', playerStateRef.current);

          // Make sure the player is ready and has the necessary methods
          if (!playerInstanceRef.current || typeof playerInstanceRef.current.seekTo !== 'function') {
            console.log('Player not ready for seeking, retrying in 100ms');
            setTimeout(restorePlayerState, 100);
            return;
          }

          // If it was playing, resume playback at the same position
          if (playerStateRef.current.isPlaying) {
            playerInstanceRef.current.seekTo(playerStateRef.current.currentTime);
            playerInstanceRef.current.playVideo();
          } else {
            // If it was paused, just seek to the right position
            playerInstanceRef.current.seekTo(playerStateRef.current.currentTime);
          }
        } catch (e) {
          console.error('Error restoring player state:', e);
        }
      };

      // Wait for the snap animation to complete
      const timeoutId = setTimeout(restorePlayerState, 250);
      return () => clearTimeout(timeoutId);
    }
  }, [isSnapping]);

  // If not open, no portal container, or on auth page, don't render
  if (!isOpen || !video || !portalContainer || isAuthPage) {
    return null;
  }

  // Calculate minimized size
  const minimizedSize = {
    width: 180,
    height: 180 / ASPECT_RATIO
  };

  // Use minimized size if minimized
  const currentSize = isMinimized ? minimizedSize : size;

  return createPortal(
    <div
      ref={playerRef}
      data-floating-player="true"
      className={cn(
        "fixed z-[100] shadow-lg rounded-md overflow-hidden border bg-background",
        "animate-in fade-in-0 zoom-in-95 duration-300",
        isMinimized && "opacity-70 hover:opacity-100 transition-opacity",
        !isMinimized && "border-2 border-primary shadow-[0_0_15px_rgba(var(--primary),0.4)]"
      )}
      style={{
        width: `${currentSize.width}px`,
        height: `${currentSize.height}px`,
        left: `${position.x}px`,
        top: `${position.y}px`,
        cursor: dragRef.current.isDragging ? 'grabbing' : 'grab'
      }}
      onDoubleClick={handleDoubleClick}
      onTouchStart={handleTouchStart}
    >
      {/* Title bar - draggable area (YouTube style) */}
      <div
        className="absolute top-0 left-0 right-0 h-12 bg-[#0f0f0f] cursor-grab z-20 drag-handle flex items-center px-3 border-b border-[#272727]"
        onMouseDown={handleDragStart}
      >
        <div className="flex items-center w-full">
          {/* YouTube-like icon */}
          <div className="mr-2 flex-shrink-0">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="red">
              <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path>
            </svg>
          </div>
          {/* Video title */}
          <div className="text-sm font-medium text-white truncate max-w-[calc(100%-80px)]" title={video.title}>
            {video.title}
          </div>
        </div>
      </div>

      {/* Video iframe */}
      <div className="w-full h-full pt-10 relative youtube-container overflow-hidden">
        {/* Overlay to hide YouTube title */}
        <div className="absolute top-0 left-0 right-0 h-10 z-10 pointer-events-none bg-black/50" />
        <div id="youtube-player-container" className="w-full h-full"></div>
      </div>



      {/* Close button in title bar */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-2 right-2 h-6 w-6 opacity-70 hover:opacity-100 hover:bg-red-100/10 transition-opacity z-30"
        onClick={closeFloatingVideo}
        title="Close player (ESC)"
      >
        <X className="h-3 w-3" />
      </Button>

      {/* Cast Queue Controls - at the bottom of the player */}
      {castQueueState.queue.length > 0 && (
        <div
          className={cn(
            "absolute bottom-0 left-0 right-0 p-1 flex justify-between items-center bg-background/80 backdrop-blur-sm transition-opacity z-30",
            !isControlsVisible && "opacity-0 pointer-events-none",
            isControlsVisible && "opacity-100"
          )}
          aria-label="Cast queue controls"
          role="toolbar"
        >
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => {
              try {
                const prevVideo = playPreviousInQueue();
                if (prevVideo) {
                  // Pass isCasting: true to prevent feed reload when playing previous video
                  openFloatingVideo(prevVideo, { isCasting: true });
                }
              } catch (error) {
                console.error('Error playing previous video:', error);
              }
            }}
            disabled={castQueueState.currentIndex <= 0}
            title="Previous video"
            aria-label="Play previous video in queue"
          >
            <SkipBack className="h-4 w-4" />
          </Button>

          <div className="text-xs text-center flex-1 truncate px-2">
            <div className="flex items-center justify-center gap-1">
              <Cast className="h-3 w-3" />
              <span>
                {castQueueState.currentIndex >= 0 && castQueueState.queue.length > 0 ?
                  `${castQueueState.currentIndex + 1}/${castQueueState.queue.length}` :
                  '0/0'}
              </span>
            </div>
          </div>

          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => {
              try {
                const nextVideo = playNextInQueue();
                if (nextVideo) {
                  // Pass isCasting: true to prevent feed reload when playing next video
                  openFloatingVideo(nextVideo, { isCasting: true });
                }
              } catch (error) {
                console.error('Error playing next video:', error);
              }
            }}
            disabled={castQueueState.currentIndex >= castQueueState.queue.length - 1}
            title="Next video"
            aria-label="Play next video in queue"
          >
            <SkipForward className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Controls - enhanced for better accessibility */}
      <div
        className={cn(
          "absolute top-10 right-0 p-1 flex gap-1 bg-background/80 backdrop-blur-sm rounded-bl-md transition-opacity z-30",
          !isControlsVisible && "opacity-0 pointer-events-none",
          isControlsVisible && "opacity-100"
        )}
        aria-label="Video player controls"
        role="toolbar"
      >
        {/* Cast button */}
        <CastButton
          video={video}
          size="icon"
          className="h-6 w-6"
          iconSize={3}
          onCastSuccess={() => {
            // Auto-close the floating player when casting successfully
            closeFloatingVideo();
          }}
        />

        {/* Previous video button - show when there are videos in the queue */}
        {castQueueState.queue.length > 0 && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => {
              try {
                console.log('Attempting to play previous video in queue');
                console.log('Current queue state:', castQueueState);

                // Get the previous video directly from the queue
                if (castQueueState.currentIndex > 0) {
                  const prevIndex = castQueueState.currentIndex - 1;
                  const prevVideo = castQueueState.queue[prevIndex];

                  console.log('Playing previous video:', prevVideo);

                  // Call the context's playPrevious function to update the current index
                  const prevVideoFromContext = playPreviousInQueue();
                  console.log('Previous video from context:', prevVideoFromContext);

                  // Open the previous video in the floating player
                  openFloatingVideo(prevVideo, { isCasting: true });
                } else {
                  console.log('No previous video available - at the beginning of the queue');
                  toast({
                    title: "Beginning of Queue",
                    description: "You're at the beginning of your cast queue."
                  });
                }
              } catch (error) {
                console.error('Error playing previous video:', error);
                toast({
                  title: "Error",
                  description: "Failed to play the previous video.",
                  variant: "destructive"
                });
              }
            }}
            disabled={castQueueState.currentIndex <= 0}
            title="Previous video"
            aria-label="Play previous video in queue"
          >
            <SkipBack className="h-3 w-3" />
          </Button>
        )}

        {/* Next video button - always show when there are videos in the queue */}
        {castQueueState.queue.length > 0 && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => {
              try {
                console.log('Attempting to play next video in queue');
                console.log('Current queue state:', castQueueState);

                // Get the next video directly from the queue
                if (castQueueState.currentIndex < castQueueState.queue.length - 1) {
                  const nextIndex = castQueueState.currentIndex + 1;
                  const nextVideo = castQueueState.queue[nextIndex];

                  console.log('Playing next video:', nextVideo);

                  // Call the context's playNext function to update the current index
                  const nextVideoFromContext = playNextInQueue();
                  console.log('Next video from context:', nextVideoFromContext);

                  // Open the next video in the floating player
                  openFloatingVideo(nextVideo, { isCasting: true });
                } else {
                  console.log('No next video available - at the end of the queue');
                  toast({
                    title: "End of Queue",
                    description: "You've reached the end of your cast queue."
                  });
                }
              } catch (error) {
                console.error('Error playing next video:', error);
                toast({
                  title: "Error",
                  description: "Failed to play the next video.",
                  variant: "destructive"
                });
              }
            }}
            disabled={castQueueState.currentIndex >= castQueueState.queue.length - 1}
            title="Next video"
            aria-label="Play next video in queue"
          >
            <SkipForward className="h-3 w-3" />
          </Button>
        )}

        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6"
          onClick={toggleMinimize}
          title="Minimize/Maximize"
          aria-label={isMinimized ? "Maximize video player" : "Minimize video player"}
        >
          {isMinimized ? <Maximize className="h-3 w-3" /> : <Minimize className="h-3 w-3" />}
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6"
          onClick={openExternal}
          title="Open in YouTube"
          aria-label="Open video in YouTube"
        >
          <ExternalLink className="h-3 w-3" />
        </Button>
      </div>

      {/* Snap position indicator */}
      {snapIndicator && (
        <div
          className="absolute top-0 left-0 right-0 bg-green-500/80 text-white text-xs py-1 px-2 text-center animate-in fade-in-0 duration-300 z-30"
          style={{ animationDuration: '150ms' }}
        >
          Snapped to {snapPositions.find(p => p.id === snapIndicator)?.name}
        </div>
      )}

      {/* Seek indicator */}
      {seekIndicator && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center p-4 bg-black/80 text-white rounded-full z-30 animate-in fade-in-0 zoom-in-95 duration-300">
          <div className="flex flex-col items-center">
            <div className="text-2xl mb-1">
              {seekIndicator.direction === 'forward' ? '⏩' :
               seekIndicator.direction === 'backward' ? '⏪' :
               seekIndicator.direction === 'timestamp' ? '🕒' : '⏩'}
            </div>
            <div className="text-sm">
              {Math.floor(seekIndicator.time / 60)}:{(seekIndicator.time % 60).toString().padStart(2, '0')}
            </div>
          </div>
        </div>
      )}

      {/* Resize handles - only show when not minimized and not on touch devices */}
      {!isMinimized && (
        <>
          <div
            className="resize-handle resize-e absolute top-1/2 right-0 w-2 h-8 -translate-y-1/2 cursor-e-resize"
            onMouseDown={(e) => handleResizeStart(e, 'e')}
          />
          <div
            className="resize-handle resize-w absolute top-1/2 left-0 w-2 h-8 -translate-y-1/2 cursor-w-resize"
            onMouseDown={(e) => handleResizeStart(e, 'w')}
          />
          <div
            className="resize-handle resize-s absolute bottom-0 left-1/2 h-2 w-8 -translate-x-1/2 cursor-s-resize"
            onMouseDown={(e) => handleResizeStart(e, 's')}
          />
          <div
            className="resize-handle resize-n absolute top-0 left-1/2 h-2 w-8 -translate-x-1/2 cursor-n-resize"
            onMouseDown={(e) => handleResizeStart(e, 'n')}
          />
          <div
            className="resize-handle resize-se absolute bottom-0 right-0 w-4 h-4 cursor-se-resize"
            onMouseDown={(e) => handleResizeStart(e, 'se')}
          />
          <div
            className="resize-handle resize-sw absolute bottom-0 left-0 w-4 h-4 cursor-sw-resize"
            onMouseDown={(e) => handleResizeStart(e, 'sw')}
          />
          <div
            className="resize-handle resize-ne absolute top-0 right-0 w-4 h-4 cursor-ne-resize"
            onMouseDown={(e) => handleResizeStart(e, 'ne')}
          />
          <div
            className="resize-handle resize-nw absolute top-0 left-0 w-4 h-4 cursor-nw-resize"
            onMouseDown={(e) => handleResizeStart(e, 'nw')}
          />
        </>
      )}
    </div>,
    portalContainer
  );
}
