--- src/components/realtime-auto-refresh.tsx
+++ src/components/realtime-auto-refresh.tsx
@@ -196,7 +196,7 @@
     // Save to localStorage
     localStorage.setItem('realtimeAutoRefreshEnabled', enabled.toString());
 
-    // Clear any existing interval
+    // Clear any existing timer
     if (autoRefreshIntervalId) {
       logger.verbose("Clearing existing auto-refresh interval");
       clearInterval(autoRefreshIntervalId);
@@ -229,7 +229,7 @@
       logger.verbose(`Set next refresh time to: ${nextTime.toLocaleTimeString()} (${display} from now)`);
 
       // Immediately set up a new refresh interval
-      if (isActive && !isPaused) {
+      /* if (isActive && !isPaused) {
         logger.verbose(`Setting up new auto-refresh interval: ${autoRefreshInterval} seconds`);
 
         // Create a new interval for auto-refresh
@@ -257,7 +257,7 @@
           } else {
             console.log("Skipping initial refresh - refresh already in progress");
           }
-        }, 100);
+        }, 100); */
       }
 
       // Show a toast to confirm auto-refresh is enabled
