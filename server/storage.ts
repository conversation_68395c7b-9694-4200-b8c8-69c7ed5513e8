import { User, Video, UserSettings, InsertUser, KeywordGroup, InsertKeywordGroup, OpenRouterPrompt, YoutubeChannel, YoutubeVideo, RssFeed, RssFeedItem, SavedArticleList, SavedArticle, TsChannel, TsVideo } from "@shared/schema";
import session from "express-session";
import createMemoryStore from "memorystore";
import fetch from "node-fetch";
import 'dotenv/config';
import { SQLiteStorage } from './sqliteStorage';

const MemoryStore = createMemoryStore(session);

// Define VidIQ API Key interface
export interface VidIQApiKey {
  id: number;
  token: string;
  auth_token?: string;
  cookie?: string;
  name?: string;
  is_active: boolean;
  last_used?: Date;
  created_at: Date;
  exhausted_until?: Date;
  user_id?: number;
}

// Define OpenRouter API Key interface
export interface OpenRouterApiKey {
  id: number;
  token: string;
  masked_token: string;
  name?: string;
  is_active: boolean;
  last_used?: Date;
  created_at: Date;
  user_id: number;
  is_default?: boolean;
  exhausted_until?: Date;
  rate_limit_type?: string;
}

export interface AdminSettings {
  serverPort: number;
  localhostOnly: boolean;
  lastUpdated: Date;
}

// Define playlist types
export interface Playlist {
  id: number;
  userId: number;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PlaylistVideo {
  id: number;
  playlistId: number;
  videoId: string;
  title: string;
  thumbnail: string;
  channelTitle: string;
  viewCount: number;
  publishedAt: Date;
  addedAt: Date;
}

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getAllUsers(): Promise<User[]>;
  createUser(user: InsertUser): Promise<User>;
  deleteUser(id: number): Promise<void>;
  getVideos(userId: number): Promise<Video[]>;
  getVideosByGroup(groupId: number): Promise<Video[]>;
  refreshVideos(user: User): Promise<Video[]>;
  refreshGroupVideos(groupId: number, userId: number): Promise<Video[]>;
  getSettings(userId: number): Promise<UserSettings | undefined>;
  getUserSettings(userId: number): Promise<UserSettings | undefined>; // Alias for getSettings
  updateSettings(userId: number, settings: UserSettings): Promise<UserSettings>;
  deleteUserData(userId: number): Promise<void>;
  getWatchedVideos(userId: number): Promise<string[]>;

  // Admin settings methods
  getAdminSettings(): Promise<AdminSettings>;
  updateAdminSettings(settings: { serverPort: number, localhostOnly: boolean }): Promise<AdminSettings>;

  // Keyword group methods
  getKeywordGroups(userId: number): Promise<KeywordGroup[]>;
  getKeywordGroup(groupId: number): Promise<KeywordGroup | undefined>;
  createKeywordGroup(userId: number, group: InsertKeywordGroup): Promise<KeywordGroup>;
  updateKeywordGroup(groupId: number, group: Partial<KeywordGroup>): Promise<KeywordGroup>;
  deleteKeywordGroup(groupId: number): Promise<void>;
  setActiveKeywordGroup(userId: number, groupId: number | null): Promise<void>;

  // VidIQ API Key methods
  getAllApiKeys(): Promise<VidIQApiKey[]>;
  getActiveApiKeys(): Promise<VidIQApiKey[]>;
  getApiKey(id: number): Promise<VidIQApiKey | undefined>;
  addApiKey(apiKey: Omit<VidIQApiKey, 'id' | 'created_at'>): Promise<VidIQApiKey>;
  updateApiKey(id: number, apiKey: Partial<VidIQApiKey>): Promise<VidIQApiKey | undefined>;
  deleteApiKey(id: number): Promise<void>;
  getNextAvailableApiKey(): Promise<VidIQApiKey | undefined>;
  markApiKeyExhausted(id: number, minutes: number): Promise<void>;

  // OpenRouter API Key methods
  getOpenRouterApiKeys(userId: number): Promise<OpenRouterApiKey[]>;
  getActiveOpenRouterApiKeys(): Promise<OpenRouterApiKey[]>;
  getOpenRouterApiKey(id: number): Promise<OpenRouterApiKey | undefined>;
  addOpenRouterApiKey(userId: number, token: string, name?: string, isActive?: boolean): Promise<OpenRouterApiKey>;
  updateOpenRouterApiKey(id: number, data: Partial<OpenRouterApiKey>): Promise<OpenRouterApiKey>;
  deleteOpenRouterApiKey(id: number): Promise<void>;
  testOpenRouterApiKey(id: number): Promise<{ success: boolean; message: string; rateLimitInfo?: { detected: boolean; type?: string; resetTime?: Date } }>;
  getNextAvailableOpenRouterApiKey(): Promise<OpenRouterApiKey | undefined>;
  markOpenRouterApiKeyExhausted(id: number, minutes?: number, rateLimitType?: string): Promise<void>;

  // Playlist methods
  getPlaylists(userId: number): Promise<Playlist[]>;
  getPlaylist(playlistId: number): Promise<Playlist | undefined>;
  createPlaylist(userId: number, name: string, description?: string): Promise<Playlist>;
  updatePlaylist(playlistId: number, updates: Partial<Playlist>): Promise<Playlist>;
  deletePlaylist(playlistId: number): Promise<void>;

  // Playlist video methods
  getPlaylistVideos(playlistId: number): Promise<PlaylistVideo[]>;
  addVideoToPlaylist(playlistId: number, video: Video): Promise<PlaylistVideo>;
  removeVideoFromPlaylist(playlistId: number, videoId: string): Promise<void>;

  // Cast queue methods
  getCastQueue(userId: number): Promise<Video[]>;
  addToCastQueue(userId: number, video: Video): Promise<void>;
  removeFromCastQueue(userId: number, videoId: string): Promise<void>;
  clearCastQueue(userId: number): Promise<void>;
  updateCastQueuePosition(userId: number, position: number): Promise<void>;

  // YouTube methods
  getYoutubeVideos(userId: number): Promise<YoutubeVideo[]>;
  getYoutubeVideosCount(userId: number): Promise<number>;
  getYoutubeVideosPaginated(userId: number, offset: number, limit: number): Promise<YoutubeVideo[]>;
  getAllChannelVideosPaginated(userId: number, offset: number, limit: number): Promise<{ videos: YoutubeVideo[], totalCount: number }>;
  getYoutubeVideo(videoId: string): Promise<YoutubeVideo | undefined>;
  saveYoutubeVideo(video: Partial<YoutubeVideo>): Promise<void>;
  updateYoutubeVideoMetadata(videoId: string, updates: { viewCount?: number, title?: string, description?: string, thumbnail?: string, isUnplayable?: boolean }, skipAutoAnalysis?: boolean): Promise<void>;
  fetchChannelVideos(channel: YoutubeChannel): Promise<Partial<YoutubeVideo>[]>;
  getYoutubeChannelsCounts(userId: number): Promise<Record<string, number>>;

  // OpenRouter Prompt methods
  getOpenRouterPrompts(userId: number): Promise<OpenRouterPrompt[]>;
  getOpenRouterPrompt(promptId: number): Promise<OpenRouterPrompt | undefined>;
  createOpenRouterPrompt(userId: number, name: string, promptText: string, systemPrompt?: string, isDefault?: boolean): Promise<OpenRouterPrompt>;
  updateOpenRouterPrompt(promptId: number, updates: Partial<OpenRouterPrompt>): Promise<OpenRouterPrompt>;
  deleteOpenRouterPrompt(promptId: number): Promise<void>;
  setDefaultOpenRouterPrompt(userId: number, promptId: number): Promise<void>;

  // RSS Feed methods
  getRssFeeds(userId: number): Promise<RssFeed[]>;
  getRssFeed(feedId: number): Promise<RssFeed | undefined>;
  addRssFeed(userId: number, feed: { name: string, url: string }): Promise<RssFeed>;
  updateRssFeed(feedId: number, updates: Partial<RssFeed>): Promise<RssFeed>;
  deleteRssFeed(feedId: number): Promise<void>;
  updateRssFeedsOrder(userId: number, feedOrders: { id: number, displayOrder: number }[]): Promise<RssFeed[]>;

  // RSS Feed Item methods
  getRssFeedItems(feedId: number): Promise<RssFeedItem[]>;
  getRssFeedItem(itemId: number): Promise<RssFeedItem | undefined>;
  getRssFeedItemByLink(feedId: number, link: string): Promise<RssFeedItem[]>;
  addRssFeedItem(item: Partial<RssFeedItem>): Promise<RssFeedItem>;
  updateRssFeedItem(itemId: number, updates: Partial<RssFeedItem>): Promise<RssFeedItem>;
  deleteRssFeedItem(itemId: number): Promise<void>;

  // Saved Article List methods
  getSavedArticleLists(userId: number): Promise<SavedArticleList[]>;
  getSavedArticleList(listId: number): Promise<SavedArticleList | undefined>;
  createSavedArticleList(userId: number, name: string): Promise<SavedArticleList>;
  updateSavedArticleList(listId: number, name: string): Promise<SavedArticleList>;
  deleteSavedArticleList(listId: number): Promise<void>;

  // Saved Article methods
  getSavedArticles(listId: number): Promise<SavedArticle[]>;
  addSavedArticle(listId: number, article: RssFeedItem): Promise<SavedArticle>;
  removeSavedArticle(articleId: number): Promise<void>;

  // TXT Tab methods
  createTxtTranscript(userId: number, data: { title: string; content: string; videoUrl?: string; sourceType?: string; sourceId?: string }): Promise<number>;
  getTxtTranscripts(userId: number): Promise<any[]>;
  getTxtTranscript(id: number, userId: number): Promise<any | null>;
  updateTxtTranscript(id: number, userId: number, data: { title?: string; content?: string; videoUrl?: string }): Promise<boolean>;
  deleteTxtTranscript(id: number, userId: number): Promise<boolean>;
  createTxtHighlight(userId: number, data: { transcriptId: number; startPos: number; endPos: number; color: string; note?: string }): Promise<number>;
  getTxtHighlights(transcriptId: number, userId: number): Promise<any[]>;
  deleteTxtHighlight(id: number, userId: number): Promise<boolean>;
  createTxtPrompt(userId: number, data: { name: string; content: string; category?: string }): Promise<number>;
  getTxtPrompts(userId: number): Promise<any[]>;
  updateTxtPrompt(id: number, userId: number, data: { name?: string; content?: string; category?: string }): Promise<boolean>;
  deleteTxtPrompt(id: number, userId: number): Promise<boolean>;
  getAllExistingTranscripts(userId: number): Promise<any[]>;

  // TS (Transcript) Tab methods
  createTsChannel(userId: number, data: { channelId: string; channelTitle: string; channelUrl: string; thumbnail?: string; description?: string; subscriberCount?: number; videoLimit: number }): Promise<TsChannel>;
  getTsChannels(userId: number): Promise<TsChannel[]>;
  getTsChannel(id: number, userId: number): Promise<TsChannel | null>;
  updateTsChannel(id: number, userId: number, data: Partial<TsChannel>): Promise<boolean>;
  deleteTsChannel(id: number, userId: number): Promise<boolean>;
  createTsVideo(userId: number, data: Partial<TsVideo>): Promise<TsVideo>;
  getTsVideos(userId: number, channelId?: string): Promise<TsVideo[]>;
  getTsVideo(id: string, userId: number): Promise<TsVideo | null>;
  updateTsVideo(id: string, userId: number, data: Partial<TsVideo>): Promise<boolean>;
  deleteTsVideo(id: string, userId: number): Promise<boolean>;
  refreshTsChannel(channelId: number, userId: number): Promise<void>;
  downloadTranscript(videoId: string, userId: number): Promise<string | null>;

  // TXT PRO Tab methods
  createTxtProScript(userId: number, data: { id: string; title: string; sourceVideoUrl?: string; transcript: string; tags: string[]; dateAdded: Date; editHistory?: Array<{ date: Date; changes: string }> }): Promise<string>;
  getTxtProScripts(userId: number): Promise<any[]>;
  updateTxtProScript(id: string, userId: number, data: Partial<{ title: string; sourceVideoUrl: string; transcript: string; tags: string[]; editHistory: Array<{ date: Date; changes: string }> }>): Promise<boolean>;
  deleteTxtProScript(id: string, userId: number): Promise<boolean>;
  createTxtProChunk(userId: number, data: { id: string; name: string; description?: string; content: string; purpose: string; tags: string[]; sourceScriptId?: string }): Promise<string>;
  getTxtProChunks(userId: number): Promise<any[]>;
  updateTxtProChunk(id: string, userId: number, data: Partial<{ name: string; description: string; content: string; purpose: string; tags: string[]; sourceScriptId: string }>): Promise<boolean>;
  deleteTxtProChunk(id: string, userId: number): Promise<boolean>;
  createTxtProHighlight(userId: number, data: { id: string; scriptId: string; startIndex: number; endIndex: number; color: string; note: string; analyticsNote?: string }): Promise<string>;
  getTxtProHighlights(userId: number): Promise<any[]>;
  updateTxtProHighlight(id: string, userId: number, data: Partial<{ scriptId: string; startIndex: number; endIndex: number; color: string; note: string; analyticsNote: string }>): Promise<boolean>;
  deleteTxtProHighlight(id: string, userId: number): Promise<boolean>;
  createTxtProStrategy(userId: number, data: { id: string; title: string; description?: string; samplePhrases: string[]; tone?: string }): Promise<string>;
  getTxtProStrategies(userId: number): Promise<any[]>;
  updateTxtProStrategy(id: string, userId: number, data: Partial<{ title: string; description: string; samplePhrases: string[]; tone: string }>): Promise<boolean>;
  deleteTxtProStrategy(id: string, userId: number): Promise<boolean>;
  createTxtProContentStructure(userId: number, data: { id: string; name: string; category?: string; introFormat?: string; mainInfoDrop?: string; explanationBreakdown?: string; engagementInsert?: string; outroCTA?: string }): Promise<string>;
  getTxtProContentStructures(userId: number): Promise<any[]>;
  updateTxtProContentStructure(id: string, userId: number, data: Partial<{ name: string; category: string; introFormat: string; mainInfoDrop: string; explanationBreakdown: string; engagementInsert: string; outroCTA: string }>): Promise<boolean>;
  deleteTxtProContentStructure(id: string, userId: number): Promise<boolean>;
  createTxtProCoreInfo(userId: number, data: { id: string; eventDate?: string; eligibilityCriteria?: string; actBillProposalName?: string; payDates?: string; programType?: string; sourceLink?: string; notes?: string }): Promise<string>;
  getTxtProCoreInfos(userId: number): Promise<any[]>;
  updateTxtProCoreInfo(id: string, userId: number, data: Partial<{ eventDate: string; eligibilityCriteria: string; actBillProposalName: string; payDates: string; programType: string; sourceLink: string; notes: string }>): Promise<boolean>;
  deleteTxtProCoreInfo(id: string, userId: number): Promise<boolean>;
  createTxtProNarrationStyle(userId: number, data: { id: string; landmarkName: string; description?: string; speakerTone?: string; commonPhrases: string[]; timingRhythm?: string }): Promise<string>;
  getTxtProNarrationStyles(userId: number): Promise<any[]>;
  updateTxtProNarrationStyle(id: string, userId: number, data: Partial<{ landmarkName: string; description: string; speakerTone: string; commonPhrases: string[]; timingRhythm: string }>): Promise<boolean>;
  deleteTxtProNarrationStyle(id: string, userId: number): Promise<boolean>;

  sessionStore: session.Store;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private videos: Map<number, Video[]>;
  private currentId: number;
  readonly sessionStore: session.Store;

  constructor() {
    this.users = new Map();
    this.videos = new Map();
    this.currentId = 1;
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000,
    });
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async getAllUsers(): Promise<User[]> {
    return Array.from(this.users.values());
  }

  async deleteUser(id: number): Promise<void> {
    this.users.delete(id);
    this.videos.delete(id);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = {
      id,
      ...insertUser,
      darkMode: true,
      searchKeywords: ["vlog", "funny", "fail"],
      minViewsPerHour: 10,
      removeDuplicates: true,
      excludeWords: [],
      autoRefreshInterval: 0,
      preferredPlayback: "in_app",
      lastRefreshTime: null,
      parallelApiCalls: true,
      watchedVideos: [],
      useInAppPlayer: true
    };
    this.users.set(id, user);
    return user;
  }

  async getVideos(userId: number): Promise<Video[]> {
    return this.videos.get(userId) || [];
  }

  async refreshVideos(user: User): Promise<Video[]> {
    const headers = {
      'Accept': 'application/json, text/plain, */*',
      'Authorization': `Bearer ${process.env.VIDIQ_API_KEY || ''}`,
      'Referer': 'https://app.vidiq.com/',
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
      'X-TimeZone': 'Asia/Calcutta',
      'X-Vidiq-Client': 'web 1a7277ec05e25b41808d7ed49f9a3f3c6ff86254',
      'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'x-vidiq-auth': process.env.VIDIQ_API_KEY || ''
    };

    const fetchVideos = async (keyword: string) => {
      console.log(`Fetching videos for keyword: ${keyword}`);
      try {
        const response = await fetch(
          `https://api.vidiq.com/v0/trendy?q[]=${encodeURIComponent(keyword)}&c=US&l=en&content_details=true`,
          { headers }
        );

        if (!response.ok) {
          console.error(`API request failed for ${keyword}:`, response.statusText);
          throw new Error(`API request failed: ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`Received ${data.videos?.length || 0} videos for keyword: ${keyword}`);
        return data.videos || [];
      } catch (error) {
        console.error(`Error fetching videos for ${keyword}:`, error);
        throw error;
      }
    };

    let allVideos: Video[] = [];

    if (user.parallelApiCalls) {
      const promises = user.searchKeywords.map(keyword => fetchVideos(keyword));
      const results = await Promise.all(promises);
      allVideos = results.flat();
    } else {
      for (const keyword of user.searchKeywords) {
        const videos = await fetchVideos(keyword);
        allVideos = allVideos.concat(videos);
      }
    }

    // Apply filters
    let filteredVideos = allVideos
      .filter(video => {
        const hoursElapsed = Math.max(1, (Date.now() - new Date(video.published_at).getTime()) / (1000 * 60 * 60));
        const viewsPerHour = video.views_count / hoursElapsed;
        return viewsPerHour >= user.minViewsPerHour;
      })
      .filter(video => !user.excludeWords.some(word =>
        video.title.toLowerCase().includes(word.toLowerCase())
      ));

    if (user.removeDuplicates) {
      const seen = new Set();
      filteredVideos = filteredVideos.filter(video => {
        if (seen.has(video.id)) return false;
        seen.add(video.id);
        return true;
      });
    }

    const processedVideos = filteredVideos.map(video => ({
      id: video.id,
      url: video.url,
      title: video.title,
      thumbnail: `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`,
      channelTitle: video.channel_title,
      viewCount: video.views_count,
      publishedAt: new Date(video.published_at),
      userId: user.id,
      // Pass all metadata from VidIQ API
      description: video.description,
      channel_id: video.channel_id,
      vph: video.vph,
      tags: video.tags || [],
      matched_tags: video.matched_tags || [],
      unmatched_tags: video.unmatched_tags || [],
      seen_from: video.seen_from || [],
      related_to: video.related_to || [],
      contentDetails: video.content_details,
      statistics: video.statistics
    }));

    this.videos.set(user.id, processedVideos);

    await this.updateSettings(user.id, {
      ...user,
      lastRefreshTime: new Date(),
    });

    return processedVideos;
  }

  async getSettings(userId: number): Promise<UserSettings | undefined> {
    const user = await this.getUser(userId);
    if (!user) return undefined;

    const { id, username, password, ...settings } = user;
    return settings;
  }

  // Alias for getSettings to maintain compatibility with task-queue.ts
  async getUserSettings(userId: number): Promise<UserSettings | undefined> {
    return this.getSettings(userId);
  }

  async updateSettings(userId: number, settings: UserSettings): Promise<UserSettings> {
    const user = await this.getUser(userId);
    if (!user) throw new Error("User not found");

    const updatedUser = { ...user, ...settings };
    this.users.set(userId, updatedUser);

    const { id, username, password, ...updatedSettings } = updatedUser;
    return updatedSettings;
  }

  async deleteUserData(userId: number): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) return;

    this.videos.delete(userId);
    const resetUser = {
      ...user,
      searchKeywords: ["vlog", "funny", "fail"],
      minViewsPerHour: 10,
      removeDuplicates: true,
      excludeWords: [],
      autoRefreshInterval: 0,
      preferredPlayback: "in_app",
      lastRefreshTime: null,
      parallelApiCalls: true,
      watchedVideos: [],
      useInAppPlayer: true
    };
    this.users.set(userId, resetUser);
  }

  // OpenRouter Prompt methods
  private openRouterPrompts: Map<number, OpenRouterPrompt[]> = new Map();
  private openRouterPromptId: number = 1;

  async getOpenRouterPrompts(userId: number): Promise<OpenRouterPrompt[]> {
    return this.openRouterPrompts.get(userId) || [];
  }

  async getOpenRouterPrompt(promptId: number): Promise<OpenRouterPrompt | undefined> {
    for (const prompts of this.openRouterPrompts.values()) {
      const prompt = prompts.find(p => p.id === promptId);
      if (prompt) return prompt;
    }
    return undefined;
  }

  async createOpenRouterPrompt(
    userId: number,
    name: string,
    promptText: string,
    systemPrompt: string = '',
    isDefault: boolean = false
  ): Promise<OpenRouterPrompt> {
    // Get existing prompts for this user
    const prompts = this.openRouterPrompts.get(userId) || [];

    // If this prompt is set as default, unset any existing default prompts
    if (isDefault) {
      prompts.forEach(p => p.isDefault = false);
    }

    // Create the new prompt
    const newPrompt: OpenRouterPrompt = {
      id: this.openRouterPromptId++,
      userId,
      name,
      promptText,
      systemPrompt,
      isDefault,
      createdAt: new Date()
    };

    // Add the new prompt to the list
    prompts.push(newPrompt);
    this.openRouterPrompts.set(userId, prompts);

    // If this is the default prompt, update the user's settings
    if (isDefault) {
      const user = await this.getUser(userId);
      if (user) {
        await this.updateSettings(userId, {
          ...user,
          selectedPromptId: newPrompt.id
        });
      }
    }

    return newPrompt;
  }

  async updateOpenRouterPrompt(
    promptId: number,
    updates: Partial<OpenRouterPrompt>
  ): Promise<OpenRouterPrompt> {
    // Find the prompt
    const prompt = await this.getOpenRouterPrompt(promptId);
    if (!prompt) {
      throw new Error(`Prompt with ID ${promptId} not found`);
    }

    // Get all prompts for this user
    const prompts = this.openRouterPrompts.get(prompt.userId) || [];

    // If setting this prompt as default, unset any existing default prompts
    if (updates.isDefault && !prompt.isDefault) {
      prompts.forEach(p => p.isDefault = false);
    }

    // Update the prompt
    const updatedPrompt = {
      ...prompt,
      name: updates.name || prompt.name,
      promptText: updates.promptText || prompt.promptText,
      systemPrompt: updates.systemPrompt !== undefined ? updates.systemPrompt : prompt.systemPrompt,
      isDefault: updates.isDefault !== undefined ? updates.isDefault : prompt.isDefault
    };

    // Replace the old prompt with the updated one
    const promptIndex = prompts.findIndex(p => p.id === promptId);
    prompts[promptIndex] = updatedPrompt;
    this.openRouterPrompts.set(prompt.userId, prompts);

    // If this is now the default prompt, update the user's settings
    if (updatedPrompt.isDefault) {
      const user = await this.getUser(prompt.userId);
      if (user) {
        await this.updateSettings(prompt.userId, {
          ...user,
          selectedPromptId: updatedPrompt.id
        });
      }
    }

    return updatedPrompt;
  }

  async deleteOpenRouterPrompt(promptId: number): Promise<void> {
    // Find the prompt
    const prompt = await this.getOpenRouterPrompt(promptId);
    if (!prompt) return; // Prompt doesn't exist, nothing to delete

    // Get all prompts for this user
    const prompts = this.openRouterPrompts.get(prompt.userId) || [];

    // Remove the prompt
    const updatedPrompts = prompts.filter(p => p.id !== promptId);
    this.openRouterPrompts.set(prompt.userId, updatedPrompts);

    // If this was the default prompt, set another prompt as default
    if (prompt.isDefault && updatedPrompts.length > 0) {
      // Set the first prompt as default
      updatedPrompts[0].isDefault = true;

      // Update the user's settings
      const user = await this.getUser(prompt.userId);
      if (user) {
        await this.updateSettings(prompt.userId, {
          ...user,
          selectedPromptId: updatedPrompts[0].id
        });
      }
    } else if (prompt.isDefault) {
      // No other prompts, set selectedPromptId to null
      const user = await this.getUser(prompt.userId);
      if (user) {
        await this.updateSettings(prompt.userId, {
          ...user,
          selectedPromptId: null
        });
      }
    }
  }

  async setDefaultOpenRouterPrompt(userId: number, promptId: number): Promise<void> {
    // Get all prompts for this user
    const prompts = this.openRouterPrompts.get(userId) || [];

    // Find the prompt
    const promptIndex = prompts.findIndex(p => p.id === promptId);
    if (promptIndex === -1) {
      throw new Error(`Prompt with ID ${promptId} not found`);
    }

    // Unset any existing default prompts
    prompts.forEach(p => p.isDefault = false);

    // Set the new default prompt
    prompts[promptIndex].isDefault = true;
    this.openRouterPrompts.set(userId, prompts);

    // Update the user's settings
    const user = await this.getUser(userId);
    if (user) {
      await this.updateSettings(userId, {
        ...user,
        selectedPromptId: promptId
      });
    }
  }
}

export const storage = new SQLiteStorage();
