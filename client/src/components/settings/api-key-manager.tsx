import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus, Trash2, RefreshCw, Check, X, Edit } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useApiKeys } from "@/hooks/use-api-keys";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { formatDate } from '@/lib/date-format';

interface ApiKey {
  id: number;
  name: string;
  masked_token: string;
  is_active: boolean;
  last_used?: string;
  created_at: string;
  exhausted_until?: string;
  is_default?: boolean;
}

export function ApiKeyManager() {
  const { toast } = useToast();
  const {
    apiKeys,
    isLoading,
    addApiKey: addApiKeyMutation,
    updateApiKey: updateApiKeyMutation,
    deleteApiKey: deleteApiKeyMutation,
    testApiKey: testApiKeyMutation,
    testDefaultApiKey: testDefaultApiKeyMutation,
    toggleDefaultApiKey: toggleDefaultApiKeyMutation,
    deleteDefaultApiKey: deleteDefaultApiKeyMutation
  } = useApiKeys();
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [newKeyName, setNewKeyName] = useState("");
  const [newKeyToken, setNewKeyToken] = useState("");
  const [editingKey, setEditingKey] = useState<ApiKey | null>(null);
  const [editKeyName, setEditKeyName] = useState("");
  const [editKeyToken, setEditKeyToken] = useState("");
  const [editKeyActive, setEditKeyActive] = useState(true);
  const [testResults, setTestResults] = useState<Record<number | string, { loading: boolean, success?: boolean, message?: string }>>({});

  // Add a new API key
  const handleAddApiKey = async () => {
    if (!newKeyToken.trim()) {
      toast({
        title: "Error",
        description: "API key token is required",
        variant: "destructive",
      });
      return;
    }

    // Extract token from curl command if needed
    let token = newKeyToken.trim();
    let extractedName = newKeyName;

    // If it's a curl command, try to extract the token
    if (token.startsWith('curl ')) {
      const bearerMatch = token.match(/--header\s+'Authorization:\s+Bearer\s+([^']+)'/);
      if (bearerMatch && bearerMatch[1]) {
        token = bearerMatch[1].trim();
        if (!extractedName) {
          extractedName = `VidIQ Key (${new Date().toLocaleString()})`;
        }
        toast({
          title: "Token extracted",
          description: "Successfully extracted Bearer token from curl command",
        });
      }
    }

    addApiKeyMutation.mutate(
      {
        token: token,
        name: extractedName || undefined,
        is_active: true,
      },
      {
        onSuccess: () => {
          toast({
            title: "Success",
            description: "API key added successfully",
          });
          setAddDialogOpen(false);
          setNewKeyName("");
          setNewKeyToken("");
        },
        onError: (error: any) => {
          toast({
            title: "Error",
            description: error.message || "Failed to add API key",
            variant: "destructive",
          });
        }
      }
    );
  };

  // Delete an API key
  const handleDeleteApiKey = async (id: number) => {
    if (!confirm("Are you sure you want to delete this API key?")) {
      return;
    }

    // Check if it's the default key
    const isDefault = apiKeys.find(key => key.id === id)?.is_default;

    if (isDefault) {
      // Use the deleteDefaultApiKey mutation for the default key
      deleteDefaultApiKeyMutation.mutate(undefined, {
        onSuccess: () => {
          toast({
            title: "Default API key deleted",
            description: "The default API key has been removed from the application.",
          });
        }
      });
    } else {
      // Use the regular deleteApiKey mutation for user-added keys
      deleteApiKeyMutation.mutate(id);
    }
  };

  // Toggle API key active status
  const handleToggleApiKeyActive = async (id: number, isActive: boolean) => {
    updateApiKeyMutation.mutate({
      id,
      data: { is_active: !isActive }
    });
  };

  // Open edit dialog for an API key
  const handleOpenEditDialog = (key: ApiKey) => {
    setEditingKey(key);
    setEditKeyName(key.name || `API Key ${key.id}`);
    setEditKeyToken(""); // Don't pre-fill the token for security reasons
    setEditKeyActive(key.is_active);
    setEditDialogOpen(true);
  };

  // Handle saving edited API key
  const handleSaveEditedApiKey = () => {
    if (!editingKey) return;

    const updateData: Partial<ApiKey> = {
      name: editKeyName,
      is_active: editKeyActive
    };

    // Only include token if it was changed
    if (editKeyToken.trim()) {
      // Extract token from curl command if needed
      let token = editKeyToken.trim();

      // If it's a curl command, try to extract the token
      if (token.startsWith('curl ')) {
        const bearerMatch = token.match(/--header\s+'Authorization:\s+Bearer\s+([^']+)'/);
        if (bearerMatch && bearerMatch[1]) {
          token = bearerMatch[1].trim();
          toast({
            title: "Token extracted",
            description: "Successfully extracted Bearer token from curl command",
          });
        }
      }

      updateData.token = token;
    }

    // Check if it's the default key
    if (editingKey.is_default) {
      // For default key, we just toggle the active status
      // In a real implementation, we would update the token as well
      toggleDefaultApiKeyMutation.mutate(editKeyActive, {
        onSuccess: () => {
          setEditDialogOpen(false);
          setEditingKey(null);
          setEditKeyName("");
          setEditKeyToken("");

          // Show a message about token changes for default key
          if (editKeyToken.trim()) {
            toast({
              title: "Note about default key",
              description: "Token changes to the default key would require updating environment variables. This is simulated in the UI.",
            });
          }
        }
      });
    } else {
      // For regular keys, use the normal update mutation
      updateApiKeyMutation.mutate({
        id: editingKey.id,
        data: updateData
      }, {
        onSuccess: () => {
          setEditDialogOpen(false);
          setEditingKey(null);
          setEditKeyName("");
          setEditKeyToken("");
        }
      });
    }
  };

  // Test an API key
  const handleTestApiKey = async (id: number) => {
    setTestResults(prev => ({
      ...prev,
      [id]: { loading: true }
    }));

    testApiKeyMutation.mutate(id, {
      onSuccess: (result) => {
        setTestResults(prev => ({
          ...prev,
          [id]: {
            loading: false,
            success: result.success,
            message: result.message
          }
        }));

        // Clear test result after 5 seconds
        setTimeout(() => {
          setTestResults(prev => {
            const newResults = { ...prev };
            delete newResults[id];
            return newResults;
          });
        }, 5000);
      },
      onError: (error) => {
        setTestResults(prev => ({
          ...prev,
          [id]: {
            loading: false,
            success: false,
            message: "Failed to test API key"
          }
        }));
      }
    });
  };

  // Test the default API key
  const handleTestDefaultApiKey = async () => {
    setTestResults(prev => ({
      ...prev,
      'default': { loading: true }
    }));

    testDefaultApiKeyMutation.mutate(undefined, {
      onSuccess: (result) => {
        setTestResults(prev => ({
          ...prev,
          'default': {
            loading: false,
            success: result.success,
            message: result.message
          }
        }));

        // Clear test result after 5 seconds
        setTimeout(() => {
          setTestResults(prev => {
            const newResults = { ...prev };
            delete newResults['default'];
            return newResults;
          });
        }, 5000);
      },
      onError: (error) => {
        setTestResults(prev => ({
          ...prev,
          'default': {
            loading: false,
            success: false,
            message: "Failed to test default API key"
          }
        }));
      }
    });
  };

  // Toggle default API key active status
  const handleToggleDefaultApiKey = async (isActive: boolean) => {
    toggleDefaultApiKeyMutation.mutate(!isActive);
  };

  // Using formatDate from our utility imported at the top

  // Check if a key is currently exhausted
  const isKeyExhausted = (key: ApiKey) => {
    if (!key.exhausted_until) return false;
    const exhaustedUntil = new Date(key.exhausted_until);
    return exhaustedUntil > new Date();
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">VidIQ API Keys</h3>
        <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add API Key
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New API Key</DialogTitle>
              <DialogDescription>
                Add a new VidIQ API key to use with the application. You can paste the full curl command, Bearer token, or raw token.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name (Optional)</Label>
                <Input
                  id="name"
                  placeholder="My API Key"
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="token">API Key Token or curl Command</Label>
                <Textarea
                  id="token"
                  placeholder="Paste your VidIQ API key or curl command here"
                  value={newKeyToken}
                  onChange={(e) => setNewKeyToken(e.target.value)}
                  rows={5}
                />
                <p className="text-sm text-muted-foreground">
                  You can paste the full curl command from the network tab, or just the Bearer token.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddApiKey} disabled={addApiKeyMutation.isPending}>
                {addApiKeyMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Add API Key
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Edit API Key Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit API Key</DialogTitle>
            <DialogDescription>
              Update your API key details. Leave the token field empty if you don't want to change the token.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                placeholder="My API Key"
                value={editKeyName}
                onChange={(e) => setEditKeyName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-token">New API Key Token (Optional)</Label>
              <Textarea
                id="edit-token"
                placeholder="Leave empty to keep the current token"
                value={editKeyToken}
                onChange={(e) => setEditKeyToken(e.target.value)}
                rows={5}
              />
              <p className="text-xs text-muted-foreground">
                You can paste a new token or curl command here. Leave empty to keep the current token.
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Label htmlFor="edit-active" className="cursor-pointer">Active</Label>
              <Switch
                id="edit-active"
                checked={editKeyActive}
                onCheckedChange={setEditKeyActive}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveEditedApiKey}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : apiKeys.length === 0 ? (
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-muted-foreground">
              <p>No API keys found. Add your first API key to get started.</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {apiKeys.map((key) => (
            <Card key={key.id || 'default'} className={`${isKeyExhausted(key) ? "border-yellow-500" : ""} ${key.is_default ? "border-blue-500" : ""}`}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">{key.name || `API Key ${key.id}`}</CardTitle>
                      {key.is_default && (
                        <Badge variant="outline" className="text-blue-500 border-blue-500">
                          Default
                        </Badge>
                      )}
                    </div>
                    <CardDescription className="font-mono text-xs mt-1">
                      {key.masked_token}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    {isKeyExhausted(key) && (
                      <Badge variant="outline" className="text-yellow-500 border-yellow-500">
                        Exhausted until {formatDate(key.exhausted_until)}
                      </Badge>
                    )}
                    <Badge variant={key.is_active ? "default" : "outline"}>
                      {key.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pb-2 pt-0">
                <div className="text-sm text-muted-foreground grid grid-cols-2 gap-2">
                  <div>
                    <span className="font-medium">Created:</span> {formatDate(key.created_at)}
                  </div>
                  <div>
                    <span className="font-medium">Last used:</span> {formatDate(key.last_used)}
                  </div>
                </div>
                {key.is_default ? (
                  testResults['default'] && (
                    <div className={`mt-2 text-sm ${testResults['default'].success ? "text-green-500" : "text-red-500"}`}>
                      {testResults['default'].loading ? (
                        <div className="flex items-center">
                          <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                          Testing default API key...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          {testResults['default'].success ? (
                            <Check className="h-3 w-3 mr-2" />
                          ) : (
                            <X className="h-3 w-3 mr-2" />
                          )}
                          {testResults['default'].message}
                        </div>
                      )}
                    </div>
                  )
                ) : (
                  testResults[key.id] && (
                    <div className={`mt-2 text-sm ${testResults[key.id].success ? "text-green-500" : "text-red-500"}`}>
                      {testResults[key.id].loading ? (
                        <div className="flex items-center">
                          <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                          Testing API key...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          {testResults[key.id].success ? (
                            <Check className="h-3 w-3 mr-2" />
                          ) : (
                            <X className="h-3 w-3 mr-2" />
                          )}
                          {testResults[key.id].message}
                        </div>
                      )}
                    </div>
                  )
                )}
                {key.is_default && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    <p>This is the default API key from environment variables. It can be edited or deleted like any other key.</p>
                  </div>
                )}
              </CardContent>
              <CardFooter className="pt-2">
                <div className="flex justify-between w-full">
                  <div className="flex items-center space-x-2">
                    <Label htmlFor={`active-${key.id || 'default'}`} className="cursor-pointer">Active</Label>
                    <Switch
                      id={`active-${key.id || 'default'}`}
                      checked={key.is_active}
                      onCheckedChange={() => key.is_default
                        ? handleToggleDefaultApiKey(key.is_active)
                        : handleToggleApiKeyActive(key.id, key.is_active)
                      }
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => key.is_default
                        ? handleTestDefaultApiKey()
                        : handleTestApiKey(key.id)
                      }
                      disabled={key.is_default
                        ? testResults['default']?.loading
                        : testResults[key.id]?.loading
                      }
                    >
                      {(key.is_default ? testResults['default']?.loading : testResults[key.id]?.loading) ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4 mr-2" />
                      )}
                      Test
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleOpenEditDialog(key)}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteApiKey(key.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

export default ApiKeyManager;
