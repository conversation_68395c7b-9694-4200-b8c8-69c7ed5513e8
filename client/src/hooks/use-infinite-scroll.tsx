import { useEffect, useRef, useState, useCallback } from 'react';
import { logger, LogLevel } from '@/lib/logger';

interface UseInfiniteScrollOptions {
  threshold?: number;
  rootMargin?: string;
  enabled?: boolean;
}

/**
 * A hook that implements infinite scrolling using the Intersection Observer API
 * @param callback Function to call when the sentinel element is visible
 * @param options Configuration options for the intersection observer
 * @returns An object containing the sentinel ref to attach to the last element
 */
export function useInfiniteScroll(
  callback: () => void,
  options: UseInfiniteScrollOptions = {}
) {
  const { threshold = 0.1, rootMargin = '200px', enabled = true } = options;
  const [isIntersecting, setIsIntersecting] = useState(false);
  const observer = useRef<IntersectionObserver | null>(null);
  const sentinelRef = useRef<HTMLDivElement | null>(null);
  const callbackTimeoutRef = useRef<number | null>(null);

  // Track if callback is currently executing to prevent multiple calls
  const isCallbackRunningRef = useRef(false);

  // Simple function to temporarily disable infinite scroll
  const pauseInfiniteScroll = useCallback(() => {
    console.log('Pausing infinite scroll');
    // No complex logic needed
  }, []);

  // Memoize the callback to avoid unnecessary observer recreation
  // Add debouncing to prevent multiple rapid calls
  const memoizedCallback = useCallback(() => {
    if (!enabled || isCallbackRunningRef.current) return;

    // Set flag to prevent multiple calls
    isCallbackRunningRef.current = true;

    // Clear any existing timeout
    if (callbackTimeoutRef.current) {
      window.clearTimeout(callbackTimeoutRef.current);
    }

    // Execute callback after a short delay to prevent multiple calls
    callbackTimeoutRef.current = window.setTimeout(() => {
      callback();
      // Reset flag after a delay to allow new calls
      setTimeout(() => {
        isCallbackRunningRef.current = false;
      }, 1000);
    }, 100);
  }, [callback, enabled]);

  // Setup and cleanup the intersection observer
  useEffect(() => {
    if (!enabled) return;

    // Cleanup previous observer
    if (observer.current) {
      observer.current.disconnect();
    }

    // Create a new observer with improved settings
    observer.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsIntersecting(entry.isIntersecting);

        if (entry.isIntersecting) {
          console.log('Sentinel element is intersecting - loading more content');
          memoizedCallback();
        }
      },
      {
        threshold, // How much of the element needs to be visible
        rootMargin, // Load earlier by extending the bottom margin
        root: null // Use the viewport as the root
      }
    );

    // Function to attach observer to the sentinel
    const attachObserver = () => {
      const currentSentinel = sentinelRef.current;
      if (currentSentinel && observer.current) {
        logger.verbose('Observing sentinel element for infinite scroll');
        observer.current.observe(currentSentinel);
      } else {
        logger.debug('Sentinel element not found - infinite scroll may not work');
        // Retry after a short delay in case the sentinel is not yet in the DOM
        setTimeout(attachObserver, 500);
      }
    };

    // Initial attempt to attach the observer
    attachObserver();

    // Cleanup on unmount
    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }

      // Clear any pending timeouts
      if (callbackTimeoutRef.current) {
        window.clearTimeout(callbackTimeoutRef.current);
      }
    };
  }, [memoizedCallback, threshold, rootMargin, enabled]);

  return { sentinelRef, isIntersecting, pauseInfiniteScroll };
}
