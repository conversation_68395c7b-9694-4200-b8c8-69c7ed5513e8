import { useQuery, useMutation } from "@tanstack/react-query";
import { YoutubeChannel, YoutubeVideo } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useRef, useEffect } from "react";

export function useYoutubeChannels() {
  const { toast } = useToast();

  // Get all YouTube channels
  const {
    data: channels = [],
    isLoading,
    error,
    refetch,
  } = useQuery<YoutubeChannel[]>({
    queryKey: ["/api/youtube-channels"],
  });

  // Get video counts for each channel - optimized to use cached data when possible
  const channelVideoCounts = useQuery<Record<string, number>>({
    queryKey: ["/api/youtube-channels/video-counts"],
    queryFn: async () => {
      // Try to get counts from localStorage first for immediate display
      let counts: Record<string, number> = {};
      try {
        const cachedCounts = localStorage.getItem('channelVideoCounts');
        if (cachedCounts) {
          counts = JSON.parse(cachedCounts);
          console.log('Using cached video counts from localStorage');
        }
      } catch (e) {
        console.error('Error reading cached video counts:', e);
      }

      // Use a more efficient API endpoint that returns all counts at once
      try {
        const res = await apiRequest("GET", `/api/youtube-channels/counts`);
        const apiCounts = await res.json();

        // Update counts with fresh data
        counts = {...counts, ...apiCounts};

        // Cache the updated counts
        localStorage.setItem('channelVideoCounts', JSON.stringify(counts));
      } catch (error) {
        console.error(`Error fetching video counts:`, error);

        // If API fails, fetch counts individually but in parallel
        const countPromises = channels.map(async (channel) => {
          try {
            // Check if we already have this channel's count in the cache
            if (counts[channel.id]) {
              return { id: channel.id, count: counts[channel.id] };
            }

            // Try to get the count from the query cache first
            const cachedData = queryClient.getQueryData([`/api/youtube-channels/${channel.id}/videos`]);
            if (cachedData) {
              return { id: channel.id, count: Array.isArray(cachedData) ? cachedData.length : 0 };
            }

            // If not in cache, fetch from API
            const res = await apiRequest("GET", `/api/youtube-channels/${channel.id}/videos`);
            const videos = await res.json();
            return { id: channel.id, count: videos.length };
          } catch (error) {
            console.error(`Error fetching video count for channel ${channel.id}:`, error);
            return { id: channel.id, count: counts[channel.id] || 0 };
          }
        });

        // Wait for all promises to resolve
        const results = await Promise.allSettled(countPromises);

        // Update counts with results
        results.forEach(result => {
          if (result.status === 'fulfilled') {
            counts[result.value.id] = result.value.count;
          }
        });

        // Cache the updated counts
        localStorage.setItem('channelVideoCounts', JSON.stringify(counts));
      }

      return counts;
    },
    enabled: channels.length > 0,
    staleTime: 300000, // 5 minutes - increased to reduce unnecessary fetches
  });

  // Create a new YouTube channel
  const createChannel = useMutation({
    mutationFn: async (data: {
      channelId: string;
      channelTitle: string;
      channelUrl: string;
      thumbnail?: string;
      description?: string;
      videoLimit?: number;
    }) => {
      const res = await apiRequest("POST", "/api/youtube-channels", data);

      // Check if the response is not ok (status code outside 200-299 range)
      if (!res.ok) {
        // Try to parse the error message from the response
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to add channel');
      }

      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels"] });
      // We'll handle success toast in the component to avoid duplicate toasts
    },
    onError: (error: Error) => {
      // We'll handle error toasts in the component to provide more context
      console.error('Error in createChannel mutation:', error);
    },
  });

  // Update a YouTube channel
  const updateChannel = useMutation({
    mutationFn: async ({ id, data }: {
      id: number;
      data: {
        channelId: string;
        channelTitle: string;
        channelUrl: string;
        thumbnail?: string;
        description?: string;
        videoLimit?: number;
      }
    }) => {
      const res = await apiRequest("PUT", `/api/youtube-channels/${id}`, data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels"] });
      toast({
        title: "Channel updated",
        description: "The YouTube channel has been updated.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update channel",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete a YouTube channel
  const deleteChannel = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/youtube-channels/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels"] });
      toast({
        title: "Channel deleted",
        description: "The YouTube channel has been removed from your subscriptions.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete channel",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete channel data (videos, transcripts, financial analysis) but keep the channel
  const deleteChannelData = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("DELETE", `/api/youtube-channels/${id}/data`);
      return response.json();
    },
    onSuccess: (data, variables) => {
      // Use the variables (channelId) passed to the mutation
      const channelId = variables;
      console.log(`Successfully deleted data for channel ID: ${channelId}`);

      // First, completely remove the queries from cache
      queryClient.removeQueries({ queryKey: [`/api/youtube-channels/${channelId}/videos`] });
      queryClient.removeQueries({ queryKey: ["/api/youtube-channels/all-videos"] });
      queryClient.removeQueries({ queryKey: ["/api/youtube-channels/video-counts"] });

      // Then invalidate all relevant queries to trigger refetches
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels"] });
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/${channelId}/videos`] });
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels/video-counts"] });
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels/all-videos"] });

      // Force immediate refetches to ensure fresh data
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: [`/api/youtube-channels/${channelId}/videos`] });
        queryClient.refetchQueries({ queryKey: ["/api/youtube-channels/all-videos"] });
        queryClient.refetchQueries({ queryKey: ["/api/youtube-channels/video-counts"] });
      }, 500);

      toast({
        title: "Channel data deleted",
        description: data.message || "All videos, transcripts, and financial analyses have been deleted.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete channel data",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Refresh videos for a specific channel
  const refreshChannelVideos = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest("POST", `/api/youtube-channels/${id}/refresh`);
      return res.json();
    },
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: [`/api/youtube-channels/${id}/videos`] });
      toast({
        title: "Videos refreshed",
        description: "Your channel's videos have been updated.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to refresh videos",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Get videos for a specific channel - optimized for memory usage
  const useChannelVideos = (channelId: number | null) => {
    return useQuery<YoutubeVideo[]>({
      queryKey: [`/api/youtube-channels/${channelId}/videos`],
      enabled: channelId !== null,
      staleTime: 300000, // 5 minutes - keep data fresh longer
      // Reduce memory usage by not keeping old data in cache
      gcTime: 600000, // 10 minutes - keep data in cache longer
      // Keep previous data while fetching new data for better UX
      keepPreviousData: true,
      queryFn: async () => {
        if (channelId === null) return [];
        const res = await apiRequest("GET", `/api/youtube-channels/${channelId}/videos`);
        const videos = await res.json();

        // Process the data to ensure we're not storing unnecessary properties
        return videos.map((video: YoutubeVideo) => {
          // Calculate VPH if it's not already present
          let vph = video.vph;
          if (!vph && video.viewCount && video.publishedAt) {
            try {
              const hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
              vph = video.viewCount / hoursElapsed;
            } catch (e) {
              console.error('Error calculating VPH:', e);
            }
          }

          return {
            id: video.id,
            title: video.title,
            channelId: video.channelId,
            channelTitle: video.channelTitle,
            publishedAt: video.publishedAt,
            thumbnail: video.thumbnail,
            description: video.description?.substring(0, 300), // Limit description length
            hasTranscription: video.hasTranscription,
            hasFinancialAnalysis: video.hasFinancialAnalysis,
            duration: video.duration,
            viewCount: video.viewCount,
            likeCount: video.likeCount,
            vph: vph, // Include VPH for sorting
            // Include financial analysis properties
            financialAmount: video.financialAmount,
            financialScore: video.financialScore,
            ollamaBenefitAmounts: video.ollamaBenefitAmounts,
            openRouterBenefitAmounts: video.openRouterBenefitAmounts,
            openRouterRawData: video.openRouterRawData,
            ollamaRawData: video.ollamaRawData
          };
        });
      },
    });
  };

  // Get all videos from all channels with pagination - optimized for memory usage
  const useAllChannelVideos = (
    page = 1,
    limit = 30,
    options: {
      sortBy?: string,
      searchQuery?: string,
      durationFilter?: string,
      fetchAll?: boolean,
      channelId?: string
    } = {}
  ) => {
    // If fetchAll is true, use a much larger limit to get all videos at once
    const effectiveLimit = options.fetchAll ? 1000 : limit;
    const {
      sortBy = 'date_desc',
      searchQuery = '',
      durationFilter = 'all',
      channelId = null
    } = options;

    // Log the current sort value for debugging
    useEffect(() => {
      console.log(`API call with sort: ${sortBy}`);
    }, [sortBy, page, effectiveLimit, searchQuery, durationFilter, channelId]);

    return useQuery<{
      videos: YoutubeVideo[],
      pagination: {
        page: number,
        limit: number,
        totalCount: number,
        totalPages: number,
        hasMore: boolean,
        nextPage: number | null
      }
    }>({
      queryKey: ["/api/youtube-channels/all-videos", page, effectiveLimit, sortBy, searchQuery, durationFilter, channelId],
      staleTime: 0, // Always fetch fresh data - this ensures view count updates are immediately visible
      // Reduce memory usage by not keeping old data in cache
      gcTime: 300000, // 5 minutes - clean up unused data more aggressively
      // Don't keep previous data while fetching new data
      keepPreviousData: false,
      queryFn: async () => {
        try {
          // Build the URL with all query parameters
          const queryParams = new URLSearchParams({
            page: page.toString(),
            limit: effectiveLimit.toString(),
            sortBy: sortBy, // Use the sort value passed in from the component
            searchQuery,
            durationFilter
          });

          // Add channelId if provided
          if (channelId) {
            queryParams.append('channelId', channelId);
            console.log(`Filtering by channel ID: ${channelId}`);
          }

          // Log the API request once per call
          console.log(`API request with sort: ${sortBy}`);

          // Fetch videos with pagination and filters
          const response = await apiRequest("GET", `/api/youtube-channels/all-videos?${queryParams.toString()}`);
          if (!response.ok) {
            throw new Error(`API returned ${response.status}`);
          }
          const data = await response.json();

          // Check if we have videos in the response
          if (!data.videos || data.videos.length === 0) {
            console.log('No videos returned from API');
            return {
              videos: [],
              pagination: data.pagination || {
                page,
                limit,
                totalCount: 0,
                totalPages: 0,
                hasMore: false,
                nextPage: null
              }
            };
          }

          // Process the data to ensure we're not storing unnecessary properties
          const processedVideos = data.videos.map((video: YoutubeVideo) => {
            // Calculate VPH if it's not already present
            let vph = video.vph;
            if (!vph && video.viewCount && video.publishedAt) {
              try {
                const hoursElapsed = Math.max(1, (Date.now() - new Date(video.publishedAt).getTime()) / (1000 * 60 * 60));
                vph = video.viewCount / hoursElapsed;
              } catch (e) {
                console.error('Error calculating VPH:', e);
              }
            }

            return {
              id: video.id,
              title: video.title,
              channelId: video.channelId,
              channelTitle: video.channelTitle,
              publishedAt: video.publishedAt,
              thumbnail: video.thumbnail,
              description: video.description?.substring(0, 200), // Further limit description length
              hasTranscription: video.hasTranscription,
              hasFinancialAnalysis: video.hasFinancialAnalysis,
              duration: video.duration,
              viewCount: video.viewCount,
              likeCount: video.likeCount,
              vph: vph, // Include VPH for sorting
              // Include financial analysis properties
              financialAmount: video.financialAmount,
              financialScore: video.financialScore,
              ollamaBenefitAmounts: video.ollamaBenefitAmounts,
              openRouterBenefitAmounts: video.openRouterBenefitAmounts,
              openRouterRawData: video.openRouterRawData,
              ollamaRawData: video.ollamaRawData
              // Don't include other properties to save memory
            };
          });

          // Apply client-side sorting based on the sortBy parameter
          // This ensures the videos are sorted correctly regardless of server response
          if (sortBy === 'views_desc') {
            console.log('Applying client-side sort by views_desc to API response');
            processedVideos.sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0));

            // Log the top 3 videos by views to verify the sort
            if (processedVideos.length > 0) {
              console.log('Top 3 videos by views after API sort:');
              for (let i = 0; i < Math.min(3, processedVideos.length); i++) {
                console.log(`  ${i+1}. ${processedVideos[i].title} (${processedVideos[i].viewCount || 0} views)`);
              }
            }
          } else if (sortBy === 'vph_desc') {
            console.log('Applying client-side sort by vph_desc to API response');
            processedVideos.sort((a, b) => (b.vph || 0) - (a.vph || 0));
          } else if (sortBy === 'date_desc') {
            console.log('Applying client-side sort by date_desc to API response');
            processedVideos.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());
          } else if (sortBy === 'date_asc') {
            console.log('Applying client-side sort by date_asc to API response');
            processedVideos.sort((a, b) => new Date(a.publishedAt).getTime() - new Date(b.publishedAt).getTime());
          }

          return {
            videos: processedVideos,
            pagination: data.pagination
          };
        } catch (error) {
          console.error('Error fetching videos:', error);
          return { videos: [], pagination: { page, limit, totalCount: 0, totalPages: 0, hasMore: false, nextPage: null } };
        }
      },
    });
  };

  // Get transcription for a specific video
  const getVideoTranscription = useMutation({
    mutationFn: async (videoId: string) => {
      try {
        console.log('API request for transcription:', videoId);
        const res = await apiRequest("GET", `/api/youtube-channels/videos/${videoId}/transcription`);
        if (!res.ok) {
          console.error('Transcription API returned error status:', res.status);
          throw new Error(`API returned ${res.status}`);
        }
        const data = await res.json();
        console.log('Transcription API response:', data);

        // Check if the response contains a transcription property
        if (data && typeof data.transcription === 'string') {
          console.log(`Got transcription of length ${data.transcription.length}`);
          return data;
        } else if (data && data.taskId) {
          // If the response contains a taskId, it means a background task was created
          console.log(`Transcription task created with ID: ${data.taskId}`);
          return data;
        } else {
          console.error('Invalid transcription data format:', data);
          throw new Error('Invalid transcription data format');
        }
      } catch (error) {
        console.error('Error in transcription API call:', error);
        // Return a fallback message instead of throwing
        return { transcription: `Unable to load transcription. You can view this video on YouTube: https://www.youtube.com/watch?v=${videoId}` };
      }
    }
  });

  // Refresh all channels with parallel fetching
  const refreshAllChannels = useMutation({
    mutationFn: async () => {
      console.log(`Starting parallel refresh for ${channels.length} channels`);
      const startTime = Date.now();

      // Create an array of promises to refresh all channels in parallel
      const refreshPromises = channels.map(async (channel) => {
        try {
          console.log(`Refreshing channel: ${channel.channelTitle} (ID: ${channel.id})`);
          const res = await apiRequest("POST", `/api/youtube-channels/${channel.id}/refresh`);
          const videos = await res.json();
          console.log(`Refreshed channel ${channel.channelTitle}: ${videos.length} videos`);
          return { channelId: channel.id, videos, success: true };
        } catch (error) {
          console.error(`Error refreshing channel ${channel.id} (${channel.channelTitle}):`, error);
          return { channelId: channel.id, videos: [], success: false, error };
        }
      });

      // Wait for all refreshes to complete in parallel
      const results = await Promise.all(refreshPromises);

      // Calculate statistics
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;
      const totalVideos = results.reduce((sum, r) => sum + (r.videos?.length || 0), 0);

      console.log(`Parallel refresh completed in ${totalTime}ms:`);
      console.log(`- ${successCount}/${channels.length} channels refreshed successfully`);
      console.log(`- ${failCount} channels failed`);
      console.log(`- ${totalVideos} total videos fetched`);

      return {
        success: successCount > 0,
        totalTime,
        successCount,
        failCount,
        totalVideos
      };
    },
    onSuccess: (result) => {
      // Invalidate all channel videos queries
      queryClient.invalidateQueries({
        predicate: (query) => query.queryKey[0]?.toString().includes('/api/youtube-channels/')
      });

      toast({
        title: "All channels refreshed",
        description: `Updated ${result.successCount} channels with ${result.totalVideos} videos in ${(result.totalTime / 1000).toFixed(1)}s.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to refresh channels",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete all channel data (videos, transcripts, financial analysis) but keep the channels
  const deleteAllChannelsData = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("DELETE", `/api/youtube-channels/all-data`);
      return response.json();
    },
    onSuccess: (data) => {
      // First, completely remove the queries from cache
      queryClient.removeQueries({ queryKey: ["/api/youtube-channels/all-videos"] });
      queryClient.removeQueries({ queryKey: ["/api/youtube-channels/video-counts"] });

      // Remove individual channel video queries
      channels.forEach(channel => {
        queryClient.removeQueries({ queryKey: [`/api/youtube-channels/${channel.id}/videos`] });
      });

      // Then invalidate all relevant queries to trigger refetches
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels"] });
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels/video-counts"] });
      queryClient.invalidateQueries({ queryKey: ["/api/youtube-channels/all-videos"] });

      // Force immediate refetches to ensure fresh data
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ["/api/youtube-channels/all-videos"] });
        queryClient.refetchQueries({ queryKey: ["/api/youtube-channels/video-counts"] });
        channels.forEach(channel => {
          queryClient.refetchQueries({ queryKey: [`/api/youtube-channels/${channel.id}/videos`] });
        });
      }, 500);

      toast({
        title: "All channel data deleted",
        description: data.message || `Deleted videos from ${data.totalChannels} channels.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete all channel data",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    channels,
    isLoading,
    error,
    refetch,
    createChannel,
    updateChannel,
    deleteChannel,
    deleteChannelData,
    deleteAllChannelsData,
    refreshChannelVideos,
    useChannelVideos,
    useAllChannelVideos,
    getVideoTranscription,
    refreshAllChannels,
    channelVideoCounts: channelVideoCounts.data || {},
    isLoadingVideoCounts: channelVideoCounts.isLoading,
  };
}
