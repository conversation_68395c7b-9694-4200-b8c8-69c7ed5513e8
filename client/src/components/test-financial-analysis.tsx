import { But<PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { DollarSign, AlertCircle, Clock, CheckCircle2 } from "lucide-react";
import { YoutubeVideo } from "@shared/schema";
import { Badge } from "@/components/ui/badge";

export function TestFinancialAnalysis() {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingVideos, setIsLoadingVideos] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [videos, setVideos] = useState<YoutubeVideo[]>([]);
  const [selectedVideoId, setSelectedVideoId] = useState<string>("");
  const { toast } = useToast();

  // Fetch videos with transcriptions
  useEffect(() => {
    const fetchVideos = async () => {
      setIsLoadingVideos(true);
      try {
        // Fetch all videos from all channels
        const res = await apiRequest("GET", "/api/youtube-channels/all-videos");

        if (!res.ok) {
          const errorText = await res.text();
          throw new Error(`API returned ${res.status}: ${errorText}`);
        }

        const data = await res.json();
        console.log("Fetched videos:", data);

        // Filter videos to only include those with transcriptions
        const videosWithTranscriptions = data.filter((video: YoutubeVideo) => video.hasTranscription);
        console.log("Videos with transcriptions:", videosWithTranscriptions.length);
        setVideos(videosWithTranscriptions);
      } catch (error) {
        console.error('Error fetching videos:', error);
        toast({
          title: "Failed to Load Videos",
          description: error instanceof Error ? error.message : "Unknown error",
          variant: "destructive",
        });
      } finally {
        setIsLoadingVideos(false);
      }
    };

    fetchVideos();
  }, [toast]);

  const handleTestFinancialAnalysis = async () => {
    // If a video is selected, analyze that specific video
    // Otherwise, use the random test endpoint
    const useSelectedVideo = selectedVideoId !== "";

    setIsLoading(true);
    try {
      let res;

      if (useSelectedVideo) {
        console.log(`Analyzing video with ID: ${selectedVideoId}`);
        res = await apiRequest("POST", "/api/youtube-channels/analyze-video", { videoId: selectedVideoId });
      } else {
        console.log('Testing financial analysis with random video...');
        res = await apiRequest("GET", "/api/youtube-channels/test-financial-analysis");
      }

      if (!res.ok) {
        const errorText = await res.text();
        throw new Error(`API returned ${res.status}: ${errorText}`);
      }

      const data = await res.json();
      console.log('Financial analysis result:', data);
      setResult(data);

      // Check if this was a reanalysis (cached property will be false if it's a new analysis)
      const isReanalysis = useSelectedVideo && data.cached === false;

      toast({
        title: isReanalysis
          ? "Video Reanalyzed Successfully"
          : (useSelectedVideo ? "Financial Analysis Successful" : "Financial Analysis Test Successful"),
        description: `Analyzed video ${data.videoId} with score: ${data.analysis.score}/100`,
      });
    } catch (error) {
      console.error('Error analyzing video:', error);
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-md">
      <h3 className="text-lg font-semibold mb-2">Financial Analysis Tool</h3>
      <p className="text-sm text-muted-foreground mb-4">
        Select a video with a transcription to analyze for financial benefits, or analyze a random video.
      </p>

      {/* Video List */}
      <div className="mb-4">
        <h4 className="text-sm font-medium mb-2">Videos with transcriptions:</h4>

        {isLoadingVideos ? (
          <div className="flex items-center justify-center p-4 border rounded-md">
            <div className="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full mr-2"></div>
            <span>Loading videos...</span>
          </div>
        ) : videos.length === 0 ? (
          <div className="flex items-center text-sm text-muted-foreground p-4 border rounded-md">
            <AlertCircle className="h-4 w-4 mr-2" />
            No videos with transcriptions found
          </div>
        ) : (
          <div className="border rounded-md">
            <div className="h-[300px] overflow-y-auto p-2">
              {videos.map((video) => (
                <div
                  key={video.id}
                  className={`p-3 mb-1 rounded-md cursor-pointer hover:bg-muted transition-colors ${selectedVideoId === video.id ? 'bg-muted border-l-4 border-primary' : ''}`}
                  onClick={() => setSelectedVideoId(video.id)}
                >
                  <div className="flex items-start gap-3">
                    {video.thumbnail && (
                      <img
                        src={video.thumbnail}
                        alt={video.title || 'Video thumbnail'}
                        className="w-24 h-14 object-cover rounded-md flex-shrink-0"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <h5 className="font-medium text-sm line-clamp-2">{video.title}</h5>
                      <p className="text-xs text-muted-foreground mt-1">{video.channelTitle}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {new Date(video.publishedAt).toLocaleDateString()}
                        </span>
                        {video.hasFinancialAnalysis && (
                          <Badge variant="outline" className="text-xs py-0 h-5 flex items-center gap-1">
                            <CheckCircle2 className="h-3 w-3" />
                            Analyzed
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <Button
        onClick={handleTestFinancialAnalysis}
        disabled={isLoading}
        className="w-full"
      >
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
            {selectedVideoId ? "Analyzing..." : "Testing..."}
          </>
        ) : (
          <>
            <DollarSign className="h-4 w-4 mr-2" />
            {selectedVideoId ? "Analyze/Reanalyze Selected Video" : "Analyze Random Video"}
          </>
        )}
      </Button>

      {result && (
        <div className="mt-4 p-4 border rounded-md bg-muted">
          <h4 className="font-medium mb-2">Analysis Results:</h4>
          <p><strong>Video ID:</strong> {result.videoId}</p>
          <p><strong>Score:</strong> {result.analysis.score}/100</p>
          <p><strong>Category:</strong> {result.analysis.category}</p>

          {/* Primary Amount */}
          {result.analysis.amount && (
            <div className="mt-2">
              <p><strong>Primary Amount:</strong> {result.analysis.amount}</p>
            </div>
          )}

          {/* Additional Amounts */}
          {result.analysis.allAmounts && result.analysis.allAmounts.length > 1 && (
            <div className="mt-2">
              <p><strong>Additional Amounts:</strong></p>
              <ul className="list-disc pl-5 mt-1">
                {result.analysis.allAmounts.slice(1, 6).map((amount, index) => (
                  <li key={index} className="text-sm">
                    {amount.formatted}
                    {amount.frequency && amount.frequency !== 'unknown' && (
                      <span className="text-xs text-muted-foreground ml-1">
                        ({amount.frequency.replace('_', ' ')})
                      </span>
                    )}
                  </li>
                ))}
                {result.analysis.allAmounts.length > 6 && (
                  <li className="text-xs text-muted-foreground">
                    ...and {result.analysis.allAmounts.length - 6} more
                  </li>
                )}
              </ul>
            </div>
          )}

          {/* Timeline */}
          {result.analysis.timeline && (
            <div className="mt-2">
              <p><strong>Timeline:</strong> {result.analysis.timeline}</p>
            </div>
          )}

          {/* Analysis */}
          {result.analysis.analysis && (
            <div className="mt-2">
              <p><strong>Analysis:</strong></p>
              <p className="text-sm whitespace-pre-line">{result.analysis.analysis}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
