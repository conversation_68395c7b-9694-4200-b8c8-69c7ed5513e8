import express from 'express';
import { IStorage } from '../storage';
import { TsChannel, TsVideo } from '@shared/schema';

export function createTsRoutes(storage: IStorage) {
    const router = express.Router();

    // Get all channels for user
    router.get('/channels', async (req, res) => {
        try {
            const userId = req.user?.id;
            if (!userId) {
                return res.status(401).json({ error: 'Unauthorized' });
            }

            const channels = await storage.getTsChannels(userId);
            console.log(`TS Channels: Found ${channels.length} channels for user ${userId}:`, channels.map(c => ({ id: c.id, title: c.channelTitle, channelId: c.channelId })));
            res.json(channels);
        } catch (error) {
            console.error('Error fetching TS channels:', error);
            res.status(500).json({ error: 'Failed to fetch channels' });
        }
    });

    // Add new channel
    router.post('/channels', async (req, res) => {
        try {
            console.log('🔵 TS CHANNEL ADD: Starting channel addition process');
            const userId = req.user?.id;
            if (!userId) {
                console.log('❌ TS CHANNEL ADD: No user ID found');
                return res.status(401).json({ error: 'Unauthorized' });
            }

            const { channelUrl, videoLimit = 50 } = req.body;
            console.log('🔵 TS CHANNEL ADD: Request data:', { userId, channelUrl, videoLimit });
            
            if (!channelUrl) {
                console.log('❌ TS CHANNEL ADD: No channel URL provided');
                return res.status(400).json({ error: 'Channel URL is required' });
            }

            // Extract channel ID from URL
            console.log('🔵 TS CHANNEL ADD: Extracting channel ID from URL:', channelUrl);
            const channelId = extractChannelId(channelUrl);
            console.log('🔵 TS CHANNEL ADD: Extracted channel ID:', channelId);
            if (!channelId) {
                console.log('❌ TS CHANNEL ADD: Failed to extract channel ID');
                return res.status(400).json({ error: 'Invalid YouTube channel URL' });
            }

            // Fetch channel info from YouTube API
            console.log('🔵 TS CHANNEL ADD: Fetching channel info for:', channelId);
            const channelInfo = await fetchChannelInfo(channelId);
            console.log('🔵 TS CHANNEL ADD: Channel info result:', channelInfo);
            if (!channelInfo) {
                console.log('❌ TS CHANNEL ADD: Failed to fetch channel info');
                return res.status(404).json({ error: 'Channel not found' });
            }

            // Check if channel already exists for this user
            console.log('🔵 TS CHANNEL ADD: Checking if channel already exists');
            const existingChannels = await storage.getTsChannels(userId);
            const exists = existingChannels.find(ch => ch.channelId === channelId);
            if (exists) {
                console.log('❌ TS CHANNEL ADD: Channel already exists');
                return res.status(409).json({ error: 'Channel already added' });
            }

            // Create channel
            console.log('🔵 TS CHANNEL ADD: Creating channel in database');
            const channel = await storage.createTsChannel(userId, {
                channelId,
                channelTitle: channelInfo.title,
                channelUrl,
                thumbnail: channelInfo.thumbnail,
                description: channelInfo.description,
                subscriberCount: channelInfo.subscriberCount,
                videoLimit
            });

            console.log('✅ TS CHANNEL ADD: Channel created successfully:', { id: channel.id, title: channel.channelTitle, channelId: channel.channelId });
            res.status(201).json(channel);
        } catch (error) {
            console.error('❌ TS CHANNEL ADD: Error adding channel:', error);
            res.status(500).json({ error: 'Failed to add channel' });
        }
    });

    // Update channel
    router.put('/channels/:id', async (req, res) => {
        try {
            const userId = req.user?.id;
            if (!userId) {
                return res.status(401).json({ error: 'Unauthorized' });
            }

            const channelId = parseInt(req.params.id);
            const updates = req.body;

            const success = await storage.updateTsChannel(channelId, userId, updates);
            if (!success) {
                return res.status(404).json({ error: 'Channel not found' });
            }

            res.json({ success: true });
        } catch (error) {
            console.error('Error updating TS channel:', error);
            res.status(500).json({ error: 'Failed to update channel' });
        }
    });

    // Delete channel
    router.delete('/channels/:id', async (req, res) => {
        try {
            const userId = req.user?.id;
            if (!userId) {
                return res.status(401).json({ error: 'Unauthorized' });
            }

            const channelId = parseInt(req.params.id);
            const success = await storage.deleteTsChannel(channelId, userId);
            
            if (!success) {
                return res.status(404).json({ error: 'Channel not found' });
            }

            res.json({ success: true });
        } catch (error) {
            console.error('Error deleting TS channel:', error);
            res.status(500).json({ error: 'Failed to delete channel' });
        }
    });

    // Get videos for a channel or all channels
    router.get('/videos', async (req, res) => {
        try {
            const userId = req.user?.id;
            if (!userId) {
                return res.status(401).json({ error: 'Unauthorized' });
            }

            const { channelId } = req.query;
            const videos = await storage.getTsVideos(userId, channelId as string);
            res.json(videos);
        } catch (error) {
            console.error('Error fetching TS videos:', error);
            res.status(500).json({ error: 'Failed to fetch videos' });
        }
    });

    // Refresh channel (fetch new videos)
    router.post('/channels/:id/refresh', async (req, res) => {
        try {
            const userId = req.user?.id;
            if (!userId) {
                return res.status(401).json({ error: 'Unauthorized' });
            }

            const channelId = parseInt(req.params.id);
            await storage.refreshTsChannel(channelId, userId);
            
            res.json({ success: true });
        } catch (error) {
            console.error('Error refreshing TS channel:', error);
            res.status(500).json({ error: 'Failed to refresh channel' });
        }
    });

    // Download transcript for a video
    router.post('/videos/:id/transcript', async (req, res) => {
        try {
            const userId = req.user?.id;
            if (!userId) {
                return res.status(401).json({ error: 'Unauthorized' });
            }

            const videoId = req.params.id;
            const transcriptPath = await storage.downloadTranscript(videoId, userId);
            
            if (!transcriptPath) {
                return res.status(404).json({ error: 'Transcript not available' });
            }

            res.json({ transcriptPath });
        } catch (error) {
            console.error('Error downloading transcript:', error);
            res.status(500).json({ error: 'Failed to download transcript' });
        }
    });

    return router;
}

// Helper function to extract channel ID from YouTube URL
function extractChannelId(url: string): string | null {
    const patterns = [
        /youtube\.com\/channel\/([a-zA-Z0-9_-]+)/,
        /youtube\.com\/c\/([a-zA-Z0-9_-]+)/,
        /youtube\.com\/user\/([a-zA-Z0-9_-]+)/,
        /youtube\.com\/@([a-zA-Z0-9_-]+)/
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
            return match[1];
        }
    }

    return null;
}

// Helper function to fetch channel info from YouTube by scraping
async function fetchChannelInfo(channelId: string): Promise<{
    title: string;
    thumbnail: string;
    description: string;
    subscriberCount: number;
} | null> {
    try {
        const axios = require('axios');
        const cheerio = require('cheerio');

        // Try different URL formats for the channel
        const possibleUrls = [
            `https://www.youtube.com/channel/${channelId}`,
            `https://www.youtube.com/@${channelId}`,
            `https://www.youtube.com/c/${channelId}`,
            `https://www.youtube.com/user/${channelId}`
        ];

        for (const url of possibleUrls) {
            try {
                const response = await axios.get(url, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept-Language': 'en-US,en;q=0.9',
                    },
                    timeout: 10000
                });

                const $ = cheerio.load(response.data);

                // Extract channel title
                let title = $('meta[property="og:title"]').attr('content') ||
                           $('meta[name="title"]').attr('content') ||
                           $('title').text() ||
                           `Channel ${channelId}`;

                // Clean up title
                title = title.replace(' - YouTube', '').trim();

                // Extract thumbnail
                let thumbnail = $('meta[property="og:image"]').attr('content') ||
                               $('link[rel="image_src"]').attr('href') ||
                               'https://yt3.ggpht.com/default_channel_avatar.jpg';

                // Extract description
                let description = $('meta[property="og:description"]').attr('content') ||
                                 $('meta[name="description"]').attr('content') ||
                                 '';

                // Extract subscriber count (this is challenging without API)
                let subscriberCount = 0;
                const subscriberText = $('#subscriber-count').text() ||
                                     $('[id*="subscriber"]').text() ||
                                     $('span:contains("subscriber")').text();

                if (subscriberText) {
                    const match = subscriberText.match(/([0-9.]+)([KMB]?)/);
                    if (match) {
                        const num = parseFloat(match[1]);
                        const unit = match[2];

                        if (unit === 'K') subscriberCount = Math.round(num * 1000);
                        else if (unit === 'M') subscriberCount = Math.round(num * 1000000);
                        else if (unit === 'B') subscriberCount = Math.round(num * 1000000000);
                        else subscriberCount = Math.round(num);
                    }
                }

                return {
                    title,
                    thumbnail,
                    description,
                    subscriberCount
                };

            } catch (urlError) {
                console.log(`Failed to fetch from ${url}:`, urlError.message);
                continue;
            }
        }

        // If all URLs failed, return basic info
        return {
            title: `Channel ${channelId}`,
            thumbnail: 'https://yt3.ggpht.com/default_channel_avatar.jpg',
            description: '',
            subscriberCount: 0
        };

    } catch (error) {
        console.error('Error fetching channel info:', error);
        return null;
    }
}
