/**
 * Enhanced Financial Analysis Service
 *
 * This service analyzes YouTube video transcripts to identify financial benefits
 * and categorizes them based on certainty, amount, timeline, and source reliability.
 * It provides detailed analysis of financial benefits mentioned in the video.
 */

// Financial benefit categories with more granularity
export enum FinancialCategory {
  URGENT = 'urgent',           // Confirmed benefits with clear proof and immediate availability
  ANTICIPATED = 'anticipated', // Approved benefits with future availability
  PROPOSED = 'proposed',       // Proposed benefits that are not yet approved
  DOUBTFUL = 'doubtful',       // Unrealistic claims without proof
  NONE = 'none'                // No financial benefits mentioned
}

// Benefit status for more precise categorization
export enum BenefitStatus {
  CONFIRMED = 'confirmed',     // Benefit is confirmed and available
  APPROVED = 'approved',       // Benefit is approved but not yet available
  PROPOSED = 'proposed',       // Benefit is proposed but not yet approved
  RUMORED = 'rumored',         // Benefit is rumored but not confirmed
  UNKNOWN = 'unknown'          // Benefit status is unknown
}

// Benefit frequency for distinguishing between one-time and recurring payments
export enum BenefitFrequency {
  ONE_TIME = 'one_time',       // One-time payment
  MONTHLY = 'monthly',         // Monthly recurring payment
  QUARTERLY = 'quarterly',     // Quarterly recurring payment
  ANNUAL = 'annual',           // Annual recurring payment
  UNKNOWN = 'unknown'          // Unknown frequency
}

// Financial amount with more detailed information
export interface FinancialAmount {
  raw: string;                 // Raw amount string as detected
  value: number;               // Numeric value
  formatted: string;           // Formatted amount string
  frequency: BenefitFrequency; // Payment frequency
  context: string;             // Context in which the amount was mentioned
  timestamp: string;           // Timestamp where the amount was mentioned (format: "MM:SS")
  timestampSeconds: number;    // Timestamp in seconds for seeking in the video player
  confidence: number;          // Confidence score (0-1) for this amount
}

// Enhanced financial analysis result interface
export interface FinancialAnalysisResult {
  score: number;               // 0-100 score based on financial benefit
  category: FinancialCategory; // Overall category
  status: BenefitStatus;       // Benefit status
  amount: string;              // Primary identified financial benefit amount
  allAmounts: FinancialAmount[]; // All detected financial amounts
  timeline: string;            // When money is arriving
  recipients: string;          // Who is eligible
  eligibilityCriteria: string[]; // Specific eligibility criteria
  steps: string;               // Steps to claim the benefit
  source: string;              // Source of the benefit (e.g., government program)
  sourceReliability: number;   // Reliability score for the source (0-100)
  viralPotential: string;      // Why the video might go viral
  skepticism: string;          // Reasons for skepticism (if applicable)
  analysis: string;            // Detailed analysis
  timestamps: string;          // Transcript timestamps with financial mentions
}

/**
 * Analyzes a transcript for financial benefits with enhanced detection and analysis
 * @param transcript The video transcript to analyze
 * @param title The video title
 * @param description The video description
 * @returns Enhanced financial analysis result
 */
export async function analyzeTranscriptForFinancialBenefits(
  transcript: string,
  title: string,
  description?: string
): Promise<FinancialAnalysisResult> {
  // Skip excessive logging during analysis
  if (!transcript || transcript.length === 0) {
    console.log('Financial Analysis Service: Warning - Empty or invalid transcript');
  }
  // Default result with no financial benefits
  const defaultResult: FinancialAnalysisResult = {
    score: 0,
    category: FinancialCategory.NONE,
    status: BenefitStatus.UNKNOWN,
    amount: '',
    timeline: '',
    recipients: '',
    steps: '',
    viralPotential: '',
    skepticism: '',
    analysis: 'No financial benefits detected in this video.',
    timestamps: '',
    allAmounts: [],
    eligibilityCriteria: [],
    source: '',
    sourceReliability: 0
  };

  // Check if transcript is available and valid
  if (!transcript ||
      transcript.includes('not found in your subscriptions') ||
      transcript.includes('Transcription not available') ||
      transcript.includes('Unable to fetch transcription')) {
    return {
      ...defaultResult,
      analysis: 'Unable to analyze: No valid transcript available for this video.'
    };
  }

  // Extract timestamps and text from transcript
  try {
    const timestampRegex = /\[(\d{2}:\d{2})\] (.*?)(?=\n\[\d{2}:\d{2}\]|$)/gs;
    const timestampMatches = [...transcript.matchAll(timestampRegex)];
    console.log(`Financial Analysis Service: Found ${timestampMatches.length} timestamp matches`);
    if (timestampMatches.length > 0) {
      console.log('Financial Analysis Service: First few matches:', timestampMatches.slice(0, 3).map(m => `[${m[1]}] ${m[2].substring(0, 30)}...`));
    }

    // If no timestamps found, return default result
    if (timestampMatches.length === 0) {
      console.log('Financial Analysis Service: No timestamp matches found, returning default result');
      return defaultResult;
    }

    // Extract financial information with enhanced detection
    const financialInfo = extractFinancialInformation(timestampMatches, title, description);

    // Determine benefit status
    const status = determineBenefitStatus(financialInfo);

    // Categorize the financial benefit with enhanced categorization
    const category = categorizeFinancialBenefit(financialInfo, status);

    // Calculate score based on category, status, and amount with improved scoring
    const score = calculateEnhancedFinancialScore(financialInfo, category, status);

    // Generate detailed analysis with enhanced information
    const analysis = generateEnhancedAnalysis(financialInfo, category, status);

    // Format timestamps with financial mentions
    const timestamps = formatFinancialTimestamps(financialInfo.timestamps);

    return {
      score,
      category,
      status,
      amount: financialInfo.amount,
      timeline: financialInfo.timeline,
      recipients: financialInfo.recipients,
      steps: financialInfo.steps,
      viralPotential: financialInfo.viralPotential,
      skepticism: financialInfo.skepticism,
      analysis,
      timestamps,
      // Include all detected amounts
      allAmounts: financialInfo.allAmounts || [],
      eligibilityCriteria: [],
      source: '',
      sourceReliability: 0
    };
  } catch (error) {
    console.error('Financial Analysis Service: Error analyzing transcript:', error);
    return defaultResult;
  }
}

// Interface for extracted financial information
// Enum for benefit payment frequency
enum BenefitFrequency {
  UNKNOWN = 'unknown',
  ONE_TIME = 'one_time',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly'
}

// Interface for financial amount with additional metadata
interface FinancialAmount {
  raw: string;           // Original raw text
  value: number;         // Numeric value
  formatted: string;     // Formatted amount
  frequency: BenefitFrequency; // Payment frequency
  context: string;       // Context where amount was found
  timestamp: string;     // Timestamp where amount was found (format: "MM:SS")
  timestampSeconds: number; // Timestamp in seconds for seeking in the video player
  confidence: number;    // Confidence score (0-1)
}

interface ExtractedFinancialInfo {
  moneyMentions: { amount: string; context: string; timestamp: string }[];
  benefitMentions: { benefit: string; context: string; timestamp: string }[];
  governmentMentions: { program: string; context: string; timestamp: string }[];
  dateMentions: { date: string; context: string; timestamp: string }[];
  recipientMentions: { recipients: string; context: string; timestamp: string }[];
  stepMentions: { step: string; context: string; timestamp: string }[];
  amount: string;
  timeline: string;
  recipients: string;
  steps: string;
  viralPotential: string;
  skepticism: string;
  timestamps: { timestamp: string; text: string; isFinancial: boolean }[];
  allAmounts?: FinancialAmount[]; // All detected financial amounts with metadata
}

/**
 * Extracts financial information from transcript timestamps
 */
function extractFinancialInformation(
  timestampMatches: RegExpMatchArray[],
  title: string,
  description?: string
): ExtractedFinancialInfo {
  // Reduced logging for performance
  // Initialize result
  const result: ExtractedFinancialInfo = {
    moneyMentions: [],
    benefitMentions: [],
    governmentMentions: [],
    dateMentions: [],
    recipientMentions: [],
    stepMentions: [],
    amount: '',
    timeline: '',
    recipients: '',
    steps: '',
    viralPotential: '',
    skepticism: '',
    timestamps: []
  };

  // Enhanced money amount regex patterns with improved accuracy
  // Basic money pattern (e.g., $100, 100 dollars) - more strict to avoid false positives
  const basicMoneyRegex = /\$\s*\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?|\b\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?\s+dollars\b|\b\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?\s+USD\b/gi;

  // Money with magnitude words (e.g., $2 million, 2 million dollars) - more precise
  const magnitudeMoneyRegex = /\$\s*\d+(?:\.\d{1,2})?\s+(?:thousand|million|billion|trillion)\b|\b\d+(?:\.\d{1,2})?\s+(?:thousand|million|billion|trillion)\s+(?:dollars|USD)\b|\$\s*\d+(?:\.\d{1,2})?\s*(?:k|m|b|t)\b|\b\d+(?:\.\d{1,2})?\s+(?:k|m|b|t)\s+(?:dollars|USD)\b/gi;

  // Money ranges (e.g., $100-$200, between $100 and $200)
  const rangeMoneyRegex = /(?:between\s+)?\$\s*\d+(?:,\d{3})*(?:\.\d{1,2})?\s*(?:to|-|and)\s*\$\s*\d+(?:,\d{3})*(?:\.\d{1,2})?/gi;

  // Money with words (e.g., two hundred dollars, five thousand dollars)
  const wordMoneyRegex = /\b(?:one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|fifteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety)\s+(?:hundred|thousand|million|billion)(?:\s+and\s+(?:one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|fifteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety))?\s+(?:dollars|USD)\b|\b(?:one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|fifteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety)\s+(?:dollars|USD)\b|\b(?:a|one)\s+(?:hundred|thousand|million|billion)\s+(?:dollars|USD)\b/gi;

  // More complex word-based money patterns (e.g., "two thousand five hundred dollars")
  const complexWordMoneyRegex = /\b(?:one|two|three|four|five|six|seven|eight|nine)\s+(?:thousand)\s+(?:one|two|three|four|five|six|seven|eight|nine)\s+(?:hundred)(?:\s+and\s+(?:one|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve|fifteen|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety))?\s+(?:dollars|USD)\b/gi;

  // Money in title pattern - special case for video titles (e.g., "$5,000 Stimulus Check")
  const titleMoneyRegex = /\$\s*\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?\s+(?:stimulus|check|payment|benefit)\b/gi;

  // Contextual money mentions (e.g., "benefit of 1,200", "payment is 600")
  const contextualMoneyRegex = /(?:benefit|payment|check|stimulus|amount|receive|get|paid|pay|eligible|qualify|approved|grant|fund|assistance|aid|support|relief|income|credit|deduction|rebate|refund)\s+(?:of|is|for|up to|about|around|approximately)?\s+(?:up\s+to)?\s+(?:\$\s*)?\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?/gi;

  // Contextual money with magnitude (e.g., "benefit of 2 thousand", "payment is 1.5 million")
  const contextualMagnitudeRegex = /(?:benefit|payment|check|stimulus|amount|receive|get|paid|pay|eligible|qualify|approved|grant|fund|assistance|aid|support|relief|income|credit|deduction|rebate|refund)\s+(?:of|is|for|up to|about|around|approximately)?\s+(?:up\s+to)?\s+(?:\$\s*)?\d+(?:\.\d{1,2})?\s+(?:thousand|million|billion|trillion)/gi;

  // Money with hundred (e.g., "six hundred dollars", "$600")
  const hundredMoneyRegex = /\b(?:\d+)\s+hundred\s+(?:dollars|USD)?\b|\$\s*\d+00\b/gi;

  // Money with hundred and ones (e.g., "six hundred and fifty dollars")
  const hundredAndOnesRegex = /\b(?:\d+)\s+hundred\s+and\s+(?:\d+)\s+(?:dollars|USD)?\b/gi;







  // Regex to identify beneficiary count mentions (to avoid confusing with benefit amounts)
  const beneficiaryCountRegex = /\b(?:\d{1,3}(?:,\d{3})*|\d+)\s+(?:people|individuals|families|households|recipients|beneficiaries|americans|citizens|residents|persons|workers|employees|million\s+people|million\s+americans|million\s+individuals|million\s+families|million\s+households|million\s+recipients|million\s+beneficiaries)\b/gi;

  // Regex to identify percentage mentions (to avoid confusing with dollar amounts)
  const percentageRegex = /\b(?:\d{1,3}(?:\.\d{1,2})?)\s*(?:percent|%)\b/gi;

  // Regex to identify age mentions (to avoid confusing with dollar amounts)
  const ageRegex = /\b(?:age\s+\d+|\d+\s+years\s+old|\d+\s+year\s+old)\b/gi;

  // Regex to identify year mentions (to avoid confusing with dollar amounts)
  const yearRegex = /\b(?:in\s+\d{4}|since\s+\d{4}|year\s+\d{4}|\d{4}\s+year)\b/gi;

  // Regex to identify count mentions (to avoid confusing with dollar amounts)
  const countRegex = /\b(?:\d{1,3}(?:,\d{3})*|\d+)\s+(?:times|days|months|years|weeks|hours|minutes|seconds)\b/gi;

  // Combined regex for all money patterns
  const moneyRegex = new RegExp([
    basicMoneyRegex.source,
    magnitudeMoneyRegex.source,
    rangeMoneyRegex.source,
    wordMoneyRegex.source,
    complexWordMoneyRegex.source,
    titleMoneyRegex.source,
    contextualMoneyRegex.source,
    contextualMagnitudeRegex.source,
    hundredMoneyRegex.source,
    hundredAndOnesRegex.source
  ].join('|'), 'gi');

  // Benefit keywords with enhanced coverage
  const benefitKeywords = [
    'stimulus', 'check', 'payment', 'benefit', 'relief', 'aid', 'assistance',
    'grant', 'fund', 'rebate', 'refund', 'credit', 'deduction', 'tax break',
    'subsidy', 'stipend', 'allowance', 'compensation', 'reimbursement',
    'support', 'income', 'cash', 'money', 'financial', 'economic', 'fiscal',
    'payout', 'disbursement', 'entitlement', 'bonus', 'supplement', 'incentive'
  ];

  // Eligibility keywords for better recipient detection
  const eligibilityKeywords = [
    'eligible', 'qualify', 'qualifies', 'qualified', 'qualification',
    'requirements', 'required', 'requirement', 'criteria', 'criterion',
    'who can', 'who is eligible', 'who qualifies', 'if you are', 'if you have',
    'must be', 'must have', 'need to be', 'need to have', 'should be', 'should have',
    'can get', 'can receive', 'can apply', 'can claim', 'can file', 'can request',
    'income limit', 'income threshold', 'income cap', 'income requirement',
    'age requirement', 'age limit', 'age restriction', 'age criteria',
    'citizenship', 'resident', 'residency', 'legal status', 'documentation'
  ];

  // Government program keywords
  const governmentKeywords = [
    'government', 'federal', 'state', 'IRS', 'treasury', 'congress', 'senate',
    'house', 'bill', 'act', 'law', 'legislation', 'program', 'SSI', 'social security',
    'medicare', 'medicaid', 'snap', 'tanf', 'wic', 'unemployment', 'disability'
  ];

  // Date/timeline keywords
  const dateKeywords = [
    'january', 'february', 'march', 'april', 'may', 'june', 'july', 'august',
    'september', 'october', 'november', 'december', 'jan', 'feb', 'mar', 'apr',
    'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec', 'today', 'tomorrow',
    'next week', 'next month', 'coming soon', 'upcoming', 'this week', 'this month',
    'within days', 'within weeks', 'deadline', 'due date', 'expiration'
  ];

  // Recipient keywords
  const recipientKeywords = [
    'eligible', 'qualify', 'qualified', 'recipient', 'beneficiary', 'taxpayer',
    'citizen', 'resident', 'household', 'family', 'individual', 'person', 'people',
    'everyone', 'americans', 'seniors', 'retirees', 'veterans', 'workers', 'unemployed'
  ];

  // Step keywords
  const stepKeywords = [
    'apply', 'application', 'file', 'filing', 'claim', 'register', 'sign up',
    'enroll', 'submit', 'complete', 'fill out', 'form', 'website', 'portal',
    'online', 'visit', 'call', 'contact', 'deadline', 'requirement', 'qualify'
  ];

  // Process each timestamp
  timestampMatches.forEach(match => {
    const timestamp = match[1];
    const text = match[2];
    let isFinancial = false;

    // Check for money mentions with improved handling and validation
    const moneyMatches = text.match(moneyRegex);
    if (moneyMatches) {
      // Check for various non-benefit mentions to avoid false positives
      const beneficiaryCounts = text.match(beneficiaryCountRegex) || [];
      const percentageMentions = text.match(percentageRegex) || [];
      const ageMentions = text.match(ageRegex) || [];
      const yearMentions = text.match(yearRegex) || [];
      const countMentions = text.match(countRegex) || [];

      moneyMatches.forEach(amount => {
        // Skip if this amount is part of a non-benefit mention
        if (beneficiaryCounts.some(count => count.includes(amount)) ||
            percentageMentions.some(percent => percent.includes(amount)) ||
            ageMentions.some(age => age.includes(amount)) ||
            yearMentions.some(year => year.includes(amount)) ||
            countMentions.some(count => count.includes(amount))) {
          return; // Skip this match
        }

        // Process the amount with full context for better validation
        const processedAmount = processMoneyAmount(amount, text);

        // Check if the surrounding context indicates this is a benefit amount
        const lowerText = text.toLowerCase();
        const isBenefitAmount =
          lowerText.includes('benefit') ||
          lowerText.includes('payment') ||
          lowerText.includes('check') ||
          lowerText.includes('stimulus') ||
          lowerText.includes('receive') ||
          lowerText.includes('get') ||
          lowerText.includes('eligible') ||
          lowerText.includes('qualify') ||
          lowerText.includes('money') ||
          lowerText.includes('financial') ||
          lowerText.includes('assistance') ||
          lowerText.includes('aid') ||
          lowerText.includes('support') ||
          lowerText.includes('relief');

        // Add confidence score based on context
        const confidence = isBenefitAmount ? 0.8 : 0.4;

        // Only include valid amounts
        if (processedAmount.isValid) {
          result.moneyMentions.push({
            amount: processedAmount.formatted,
            context: text,
            timestamp,
            confidence: confidence,
            isBenefitAmount: isBenefitAmount
          });
          isFinancial = true;
        }
      });
    }

    // Check for benefit keywords
    benefitKeywords.forEach(keyword => {
      if (text.toLowerCase().includes(keyword)) {
        result.benefitMentions.push({ benefit: keyword, context: text, timestamp });
        isFinancial = true;
      }
    });

    // Check for government program mentions
    governmentKeywords.forEach(keyword => {
      if (text.toLowerCase().includes(keyword)) {
        result.governmentMentions.push({ program: keyword, context: text, timestamp });
        isFinancial = true;
      }
    });

    // Check for date/timeline mentions
    dateKeywords.forEach(keyword => {
      if (text.toLowerCase().includes(keyword)) {
        result.dateMentions.push({ date: keyword, context: text, timestamp });
        isFinancial = true;
      }
    });

    // Check for recipient mentions
    recipientKeywords.forEach(keyword => {
      if (text.toLowerCase().includes(keyword)) {
        result.recipientMentions.push({ recipients: keyword, context: text, timestamp });
        isFinancial = true;
      }
    });

    // Check for eligibility mentions with enhanced detection
    eligibilityKeywords.forEach(keyword => {
      if (text.toLowerCase().includes(keyword)) {
        // Extract the full sentence containing the eligibility keyword for better context
        const sentences = text.split(/[.!?]/);
        const relevantSentences = sentences.filter(sentence =>
          sentence.toLowerCase().includes(keyword)
        );

        if (relevantSentences.length > 0) {
          result.recipientMentions.push({
            recipients: keyword,
            context: relevantSentences.join('. '),
            timestamp,
            isEligibilityCriteria: true  // Mark as explicit eligibility criteria
          });
          isFinancial = true;
        }
      }
    });

    // Check for step mentions
    stepKeywords.forEach(keyword => {
      if (text.toLowerCase().includes(keyword)) {
        result.stepMentions.push({ step: keyword, context: text, timestamp });
        isFinancial = true;
      }
    });

    // Add to timestamps array
    result.timestamps.push({ timestamp, text, isFinancial });
  });

  // Process all financial amounts with enhanced tracking and validation
  if (result.moneyMentions.length > 0) {
    // Track unique amounts and their frequencies
    const amountFrequency: Record<string, number> = {};
    const amountValues: Record<string, number> = {};
    const amountContexts: Record<string, string[]> = {};
    const amountTimestamps: Record<string, string[]> = {};

    // Collect all unique amounts with their contexts
    result.moneyMentions.forEach(mention => {
      // Update frequency counter
      amountFrequency[mention.amount] = (amountFrequency[mention.amount] || 0) + 1;

      // Extract numeric value for sorting
      const numericValue = extractNumericValue(mention.amount);
      amountValues[mention.amount] = numericValue;

      // Store contexts and timestamps for each amount
      if (!amountContexts[mention.amount]) {
        amountContexts[mention.amount] = [];
        amountTimestamps[mention.amount] = [];
      }
      amountContexts[mention.amount].push(mention.context);
      amountTimestamps[mention.amount].push(mention.timestamp);
    });

    // Create FinancialAmount objects for each unique amount
    const allAmounts: FinancialAmount[] = Object.keys(amountFrequency).map(amount => {
      // Determine the most representative context for this amount
      const contexts = amountContexts[amount];
      const bestContext = contexts[0]; // Use the first occurrence for simplicity

      // Determine payment frequency from context
      const frequency = determinePaymentFrequency(bestContext);

      // Calculate confidence based on frequency, context, and amount value
      let confidence = Math.min(1, 0.5 + (amountFrequency[amount] / 10) + (frequency !== BenefitFrequency.UNKNOWN ? 0.1 : 0));

      // Adjust confidence based on amount value and context
      const value = amountValues[amount];

      // Lower confidence for extremely high values that are likely misinterpretations
      if (value >= 1000000000000) { // Trillion+ range
        confidence *= 0.3; // Significantly reduce confidence
      } else if (value >= 1000000000) { // Billion range
        confidence *= 0.5; // Reduce confidence
      } else if (value >= 100000000) { // 100M+ range
        confidence *= 0.7; // Slightly reduce confidence
      }

      // Increase confidence for common benefit amounts
      if (value >= 500 && value <= 5000) {
        confidence = Math.min(1, confidence * 1.2); // Boost confidence for typical stimulus range
      }

      // Check for contextual indicators that this is a benefit amount
      const lowerContext = bestContext.toLowerCase();
      if (lowerContext.includes('receive') ||
          lowerContext.includes('eligible') ||
          lowerContext.includes('qualify') ||
          lowerContext.includes('get') ||
          lowerContext.includes('payment')) {
        confidence = Math.min(1, confidence * 1.1); // Boost confidence
      }

      // Check for contextual indicators that this is NOT a benefit amount
      if (lowerContext.includes('budget') ||
          lowerContext.includes('total cost') ||
          lowerContext.includes('allocated') ||
          lowerContext.includes('spent')) {
        confidence *= 0.8; // Reduce confidence
      }

      // Convert timestamp string (MM:SS) to seconds for video seeking
      const timestamp = amountTimestamps[amount][0];
      const timestampSeconds = convertTimestampToSeconds(timestamp);

      // Format the amount value using standard numeric format with commas
      const amountValue = amountValues[amount];
      const formattedAmount = `$${amountValue.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      })}`;

      return {
        raw: amount,
        value: amountValue,
        formatted: formattedAmount,
        frequency,
        context: bestContext,
        timestamp: timestamp,
        timestampSeconds,
        confidence
      };
    });

    // Filter out amounts with very low confidence
    const validAmounts = allAmounts.filter(amount => amount.confidence > 0.2);

    // Sort amounts by confidence and value
    const sortedAmounts = validAmounts.sort((a, b) => {
      // First sort by confidence
      const confidenceDiff = b.confidence - a.confidence;
      if (confidenceDiff !== 0) return confidenceDiff;
      // Then by value
      return b.value - a.value;
    });

    // Store all amounts in the result
    result.allAmounts = sortedAmounts;

    // Set the primary amount to the highest confidence/value amount
    if (sortedAmounts.length > 0) {
      result.amount = sortedAmounts[0].formatted;
    }
  }

  // Process the video title for financial information
  // This is especially important for videos that mention specific amounts in the title
  if (title) {
    // Check for money mentions in the title using our special title regex
    const titleMoneyMatches = title.match(titleMoneyRegex);
    if (titleMoneyMatches) {
      titleMoneyMatches.forEach(amount => {
        // Process the amount with the title as context
        const processedAmount = processMoneyAmount(amount, title);

        // If we found a valid amount in the title, and it's a stimulus check/payment
        if (processedAmount.isValid &&
            (title.toLowerCase().includes('stimulus') ||
             title.toLowerCase().includes('check') ||
             title.toLowerCase().includes('payment'))) {

          // Create a financial amount object for this title amount
          const amountValue = processedAmount.value;
          const formattedAmount = `$${amountValue.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          })}`;

          // Add this as a high-confidence amount
          const titleAmount: FinancialAmount = {
            raw: amount,
            value: amountValue,
            formatted: formattedAmount,
            frequency: BenefitFrequency.ONE_TIME, // Assume one-time for stimulus
            context: title,
            timestamp: '[Title]',
            timestampSeconds: 0,
            confidence: 0.95 // High confidence for title mentions
          };

          // Add to allAmounts if it doesn't exist yet
          if (!result.allAmounts) {
            result.allAmounts = [titleAmount];
          } else {
            // Check if we already have this amount
            const existingAmount = result.allAmounts.find(a => a.value === amountValue);
            if (!existingAmount) {
              result.allAmounts.push(titleAmount);
            } else {
              // Increase confidence of existing amount
              existingAmount.confidence = Math.min(1, existingAmount.confidence + 0.1);
            }
          }

          // Sort amounts by confidence and value
          if (result.allAmounts) {
            result.allAmounts.sort((a, b) => {
              // First by confidence
              if (b.confidence !== a.confidence) {
                return b.confidence - a.confidence;
              }
              // Then by value
              return b.value - a.value;
            });

            // Set the primary amount to the highest confidence/value amount
            if (result.allAmounts.length > 0) {
              result.amount = result.allAmounts[0].formatted;
            }
          }
        }
      });
    }
  }

  // Extract additional information using our helper functions
  fixTimelineExtraction(result);
  extractRecipients(result);
  extractSteps(result);
  extractViralPotential(result);
  extractSkepticism(result);

  return result;
}

/**
 * Determines the payment frequency from context
 */
function determinePaymentFrequency(context: string): BenefitFrequency {
  const lowerContext = context.toLowerCase();

  // Check for one-time payment indicators
  if (lowerContext.includes('one time') ||
      lowerContext.includes('one-time') ||
      lowerContext.includes('lump sum') ||
      lowerContext.includes('stimulus') ||
      lowerContext.includes('check')) {
    return BenefitFrequency.ONE_TIME;
  }

  // Check for weekly payment indicators
  if (lowerContext.includes('weekly') ||
      lowerContext.includes('per week') ||
      lowerContext.includes('a week') ||
      lowerContext.includes('each week')) {
    return BenefitFrequency.WEEKLY;
  }

  // Check for monthly payment indicators
  if (lowerContext.includes('monthly') ||
      lowerContext.includes('per month') ||
      lowerContext.includes('a month') ||
      lowerContext.includes('each month')) {
    return BenefitFrequency.MONTHLY;
  }

  // Check for yearly payment indicators
  if (lowerContext.includes('yearly') ||
      lowerContext.includes('per year') ||
      lowerContext.includes('a year') ||
      lowerContext.includes('annual') ||
      lowerContext.includes('annually')) {
    return BenefitFrequency.YEARLY;
  }

  // Default to unknown frequency
  return BenefitFrequency.UNKNOWN;
}

/**
 * Process a money amount string to handle various formats including magnitude words
 * @param amount The raw money amount string
 * @returns Processed amount with numeric value and formatted string
 */
/**
 * Process a money amount string with improved accuracy and validation
 * @param amount The raw money amount string
 * @param context Optional full context where the amount was found
 * @returns Processed value and formatted string
 */
function processMoneyAmount(amount: string, context?: string): { value: number; formatted: string; isValid: boolean } {
  // Convert to lowercase for consistent processing
  const lowerAmount = amount.toLowerCase();
  const fullContext = context ? context.toLowerCase() : lowerAmount;

  // Define regex patterns for non-benefit mentions
  const beneficiaryCountRegex = /\b(?:million|billion|trillion)\s+(?:people|americans|recipients|households|families|beneficiaries)\b/gi;
  const percentageRegex = /\b\d+(?:\.\d+)?\s*%\b/gi;
  const ageRegex = /\b(?:age|ages)\s+\d+\b|\b\d+\s+(?:years?\s+old|year-old)\b/gi;
  const yearRegex = /\b(?:19|20)\d{2}\b/g;
  const countRegex = /\b(?:number|count|total)\s+of\s+\d+\b/gi;

  // Check if this is likely a non-benefit mention rather than a benefit amount
  if (context) {
    if (beneficiaryCountRegex.test(context)) {
      return { value: 0, formatted: amount, isValid: false };
    }

    if (percentageRegex.test(lowerAmount)) {
      return { value: 0, formatted: amount, isValid: false };
    }

    if (ageRegex.test(context)) {
      return { value: 0, formatted: amount, isValid: false };
    }

    if (yearRegex.test(context)) {
      return { value: 0, formatted: amount, isValid: false };
    }

    if (countRegex.test(context)) {
      return { value: 0, formatted: amount, isValid: false };
    }
  }

  // Handle money ranges (e.g., $100-$200, between $100 and $200)
  if (lowerAmount.includes('to') || lowerAmount.includes('-') || lowerAmount.includes('and')) {
    // Extract all numeric values
    const allNumbers = lowerAmount.match(/\d+(?:,\d{3})*(?:\.\d+)?/g);
    if (allNumbers && allNumbers.length >= 2) {
      // Use the higher value in the range
      const values = allNumbers.map(n => parseFloat(n.replace(/,/g, '')));
      const maxValue = Math.max(...values);
      return processNumericValue(maxValue, lowerAmount, fullContext);
    }
  }

  // Handle word-based money (e.g., two hundred dollars)
  if (!lowerAmount.match(/\d+/) && (lowerAmount.includes('dollars') || lowerAmount.includes('usd'))) {
    const wordValue = extractSimpleWordValue(lowerAmount);
    if (wordValue > 0) {
      return processNumericValue(wordValue, lowerAmount, fullContext);
    }
  }

  // Special case for stimulus checks in titles
  if (fullContext.includes('stimulus check') || fullContext.includes('stimulus payment')) {
    // Try to extract the amount from the title format like "$5,000 Stimulus Check"
    const titleMatch = lowerAmount.match(/\$\s*(\d+(?:,\d{3})*)/);
    if (titleMatch) {
      const numericValue = parseFloat(titleMatch[1].replace(/,/g, ''));
      // For stimulus checks, we directly return the value with high confidence
      return {
        value: numericValue,
        formatted: `$${numericValue.toLocaleString('en-US')}`,
        isValid: true
      };
    }
  }

  // Extract numeric part for standard formats
  const numericMatch = lowerAmount.match(/\d+(?:,\d{3})*(?:\.\d+)?/);
  if (!numericMatch) {
    return { value: 0, formatted: amount, isValid: false };
  }

  const numericValue = parseFloat(numericMatch[0].replace(/,/g, ''));
  return processNumericValue(numericValue, lowerAmount, fullContext);
}

/**
 * Process a numeric value with context to determine magnitude and format
 */
/**
 * Process a numeric value with context to determine magnitude and format with improved validation
 * @param value The numeric value
 * @param context The immediate context (the money mention)
 * @param fullContext The full context where the amount was found (optional)
 * @returns Processed value, formatted string, and validity flag
 */
function processNumericValue(value: number, context: string, fullContext?: string): { value: number; formatted: string; isValid: boolean } {
  let multiplier = 1;
  let isValid = true;

  // Check for magnitude words and abbreviations with more precise matching
  // Use word boundaries to avoid partial matches
  if (/\bthousand\b/.test(context) || /\bk\b/.test(context)) {
    multiplier = 1000;
  } else if (/\bmillion\b/.test(context) || /\bm\b/.test(context)) {
    multiplier = 1000000;
  } else if (/\bbillion\b/.test(context) || /\bb\b/.test(context)) {
    multiplier = 1000000000;
  } else if (/\btrillion\b/.test(context) || /\bt\b/.test(context)) {
    multiplier = 1000000000000;
  }

  // Calculate the final value
  const finalValue = value * multiplier;

  // Validate the amount based on context and magnitude
  if (fullContext) {
    // Check for unrealistic amounts based on context
    if (finalValue >= 1000000000000) { // Trillion+ range
      // Trillions are extremely rare in individual benefits
      // Usually only used for total program costs or national budgets
      if (fullContext.includes('individual') ||
          fullContext.includes('per person') ||
          fullContext.includes('each person') ||
          fullContext.includes('per family') ||
          fullContext.includes('per household') ||
          fullContext.includes('stimulus check') ||
          fullContext.includes('stimulus payment') ||
          fullContext.includes('receive') ||
          fullContext.includes('eligible') ||
          fullContext.includes('qualify')) {
        isValid = false; // Likely a misinterpretation
      }
    } else if (finalValue >= 1000000000) { // Billion range
      // Billions are very rare for individual benefits
      // Usually only for program totals or large group benefits
      if (fullContext.includes('individual') ||
          fullContext.includes('per person') ||
          fullContext.includes('each person') ||
          fullContext.includes('per family') ||
          fullContext.includes('per household') ||
          fullContext.includes('stimulus check') ||
          fullContext.includes('stimulus payment') ||
          fullContext.includes('receive') ||
          fullContext.includes('eligible') ||
          fullContext.includes('qualify')) {
        isValid = false; // Likely a misinterpretation
      }
    } else if (finalValue >= 10000000) { // 10M+ range
      // Very large amounts for individual benefits are suspicious
      if (fullContext.includes('individual') ||
          fullContext.includes('per person') ||
          fullContext.includes('each person') ||
          fullContext.includes('stimulus check') ||
          fullContext.includes('stimulus payment')) {
        // Check if there are indicators this is a total program cost
        if (fullContext.includes('total') ||
            fullContext.includes('budget') ||
            fullContext.includes('funding') ||
            fullContext.includes('allocated')) {
          isValid = false; // Likely a program total, not individual benefit
        } else {
          // If no program total indicators but still very high for individual benefit
          // Check if there are strong indicators this is a real benefit
          if (!fullContext.includes('confirmed') &&
              !fullContext.includes('approved') &&
              !fullContext.includes('official') &&
              !fullContext.includes('guaranteed')) {
            isValid = false; // Likely an exaggeration or misinterpretation
          }
        }
      }
    } else if (finalValue >= 100000) { // Six-figure range
      // Six-figure individual benefits are possible but rare
      // Check for additional context that might indicate this is a program total
      if (fullContext.includes('individual') ||
          fullContext.includes('per person') ||
          fullContext.includes('stimulus check') ||
          fullContext.includes('stimulus payment')) {
        // Individual benefits over $100k are very rare
        // Require strong evidence that this is a real benefit amount
        if (!fullContext.includes('confirmed') &&
            !fullContext.includes('approved') &&
            !fullContext.includes('official')) {
          // Lower confidence but don't invalidate completely
          isValid = true; // Keep it valid but we'll adjust confidence later
        }
      }
    }

    // Special case for common stimulus check amounts ($1,000-$5,000)
    // These are very common in financial benefit videos and should be detected
    if (finalValue >= 1000 && finalValue <= 5000 &&
        (fullContext.includes('stimulus') || fullContext.includes('check') ||
         fullContext.includes('payment') || fullContext.includes('benefit'))) {
      isValid = true; // Always consider these valid as they're common benefit amounts
    }
  }

  // Format the amount for display using standard numeric format with commas
  // This avoids confusing formats like "$2.90 thousand" and instead shows "$2,900"
  let formatted = '';

  // Always format as a standard number with commas for better readability
  formatted = `$${finalValue.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  })}`;

  // For very large numbers, we can add a note about the magnitude for clarity
  // but keep the primary format as a standard number
  let magnitudeNote = '';
  if (finalValue >= 1000000000000) {
    magnitudeNote = ' (trillion range)';
  } else if (finalValue >= 1000000000) {
    magnitudeNote = ' (billion range)';
  }

  // Only add the magnitude note for extremely large numbers
  if (finalValue >= 1000000000) {
    formatted += magnitudeNote;
  }

  return { value: finalValue, formatted, isValid };
}

/**
 * Extract numeric value from word-based money amounts
 * @param text Text containing word-based money amount
 * @returns Numeric value
 */
function extractSimpleWordValue(text: string): number {
  const wordValues: Record<string, number> = {
    'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
    'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10,
    'eleven': 11, 'twelve': 12, 'fifteen': 15, 'twenty': 20,
    'thirty': 30, 'forty': 40, 'fifty': 50, 'sixty': 60,
    'seventy': 70, 'eighty': 80, 'ninety': 90, 'hundred': 100,
    'thousand': 1000
  };

  let value = 0;
  let multiplier = 1;

  // Check for presence of magnitude words
  if (text.includes('thousand')) {
    multiplier = 1000;
  } else if (text.includes('million')) {
    multiplier = 1000000;
  } else if (text.includes('billion')) {
    multiplier = 1000000000;
  } else if (text.includes('trillion')) {
    multiplier = 1000000000000;
  }

  // Extract word-based numbers
  Object.entries(wordValues).forEach(([word, val]) => {
    if (text.includes(word)) {
      if (word === 'hundred' || word === 'thousand') {
        // These are multipliers
        if (value === 0) value = val;
        else value *= val;
      } else {
        value += val;
      }
    }
  });

  return value * multiplier;
}

/**
 * Extract numeric value from word-based amount strings
 * @param wordAmount The word-based amount string (e.g., "two thousand dollars")
 * @returns The numeric value
 */
function extractWordValue(wordAmount: string): number {
  const lowerAmount = wordAmount.toLowerCase();

  // Define word-to-number mapping
  const wordValues: Record<string, number> = {
    'one': 1,
    'two': 2,
    'three': 3,
    'four': 4,
    'five': 5,
    'six': 6,
    'seven': 7,
    'eight': 8,
    'nine': 9,
    'ten': 10,
    'eleven': 11,
    'twelve': 12,
    'thirteen': 13,
    'fourteen': 14,
    'fifteen': 15,
    'sixteen': 16,
    'seventeen': 17,
    'eighteen': 18,
    'nineteen': 19,
    'twenty': 20,
    'thirty': 30,
    'forty': 40,
    'fifty': 50,
    'sixty': 60,
    'seventy': 70,
    'eighty': 80,
    'ninety': 90,
    'hundred': 100,
    'thousand': 1000,
    'million': 1000000,
    'billion': 1000000000,
    'trillion': 1000000000000,
    'a': 1
  };

  // Handle special case "a hundred" or "a thousand"
  if (lowerAmount.includes('a hundred')) {
    return 100;
  }
  if (lowerAmount.includes('a thousand')) {
    return 1000;
  }
  if (lowerAmount.includes('a million')) {
    return 1000000;
  }
  if (lowerAmount.includes('a billion')) {
    return 1000000000;
  }

  // Split the string into words
  const words = lowerAmount.replace(/[^a-z\s]/gi, '').split(/\s+/);

  let total = 0;
  let currentNumber = 0;

  // Process each word
  for (const word of words) {
    if (word === 'dollars' || word === 'usd') {
      continue; // Skip currency indicators
    }

    if (word === 'and') {
      continue; // Skip connecting words
    }

    const value = wordValues[word];
    if (value === undefined) {
      continue; // Skip unknown words
    }

    if (value === 100) {
      // Multiply the current number by 100
      currentNumber = currentNumber === 0 ? 100 : currentNumber * 100;
    } else if (value >= 1000) {
      // Multiply the current number by the magnitude and add to total
      currentNumber = currentNumber === 0 ? value : currentNumber * value;
      total += currentNumber;
      currentNumber = 0;
    } else {
      // Add the value to the current number
      currentNumber += value;
    }
  }

  // Add any remaining current number to the total
  total += currentNumber;

  return total;
}

/**
 * Extract numeric value from a formatted money string
 * @param formattedAmount The formatted money string
 * @returns The numeric value
 */
function extractNumericValue(formattedAmount: string): number {
  // Handle magnitude words
  const lowerAmount = formattedAmount.toLowerCase();

  // Check if this is a word-based amount (e.g., "two thousand dollars")
  if (/\b(one|two|three|four|five|six|seven|eight|nine|ten|twenty|thirty|forty|fifty|sixty|seventy|eighty|ninety|hundred|thousand)\b/.test(lowerAmount) &&
      !lowerAmount.match(/\d+/)) {
    return extractWordValue(lowerAmount); // Use the new enhanced word value extraction
  }

  const numericMatch = lowerAmount.match(/\d+(?:\.\d+)?/);
  if (!numericMatch) return 0;

  const numericValue = parseFloat(numericMatch[0]);

  // Apply multiplier based on magnitude words and abbreviations
  if (lowerAmount.includes('thousand') || lowerAmount.endsWith('k')) {
    return numericValue * 1000;
  } else if (lowerAmount.includes('million') || lowerAmount.endsWith('m')) {
    return numericValue * 1000000;
  } else if (lowerAmount.includes('billion') || lowerAmount.endsWith('b')) {
    return numericValue * 1000000000;
  } else if (lowerAmount.includes('trillion') || lowerAmount.endsWith('t')) {
    return numericValue * 1000000000000;
  }

  // Remove non-numeric characters and parse
  return numericValue;
}

/**
 * This function was incorrectly placed outside any function scope
 * It has been moved into the extractFinancialInformation function
 */
function fixTimelineExtraction(result: any) {
  // Determine timeline
  if (result.dateMentions.length > 0) {
  // Look for specific dates or timeframes
  const timelineContexts = result.dateMentions
    .filter(mention => {
      const lowerContext = mention.context.toLowerCase();
      return lowerContext.includes('payment') ||
             lowerContext.includes('receive') ||
             lowerContext.includes('deposit') ||
             lowerContext.includes('arrive') ||
             lowerContext.includes('coming') ||
             lowerContext.includes('start');
    })
    .map(mention => mention.context);

  if (timelineContexts.length > 0) {
    result.timeline = timelineContexts[0];
  } else {
    result.timeline = result.dateMentions[0].context;
  }
}

  // Determine recipients
  return result;
}

/**
 * Extract recipients information
 */
function extractRecipients(result: any) {
  if (result.recipientMentions.length > 0) {
    const recipientContexts = result.recipientMentions
      .filter(mention => {
        const lowerContext = mention.context.toLowerCase();
        return lowerContext.includes('eligible') ||
               lowerContext.includes('qualify') ||
               lowerContext.includes('receive');
      })
      .map(mention => mention.context);

    if (recipientContexts.length > 0) {
      result.recipients = recipientContexts[0];
    } else {
      result.recipients = result.recipientMentions[0].context;
    }
  }

  return result;
}

/**
 * Extract steps information
 */
function extractSteps(result: any) {
  // Determine steps to claim
  if (result.stepMentions.length > 0) {
    const stepContexts = result.stepMentions
      .filter(mention => {
        const lowerContext = mention.context.toLowerCase();
        return lowerContext.includes('how to') ||
               lowerContext.includes('steps') ||
               lowerContext.includes('process') ||
               lowerContext.includes('apply');
      })
      .map(mention => mention.context);

    if (stepContexts.length > 0) {
      result.steps = stepContexts.join(' | ');
    } else {
      result.steps = result.stepMentions[0].context;
    }
  }

  return result;
}

/**
 * Extract viral potential information
 */
function extractViralPotential(result: any) {
  // Determine viral potential
  if (result.moneyMentions.length > 0 && result.benefitMentions.length > 0) {
    result.viralPotential = `Video mentions ${result.amount} in financial benefits`;
    if (result.timeline) {
      result.viralPotential += ` with timeline: ${result.timeline}`;
    }
  }

  return result;
}

/**
 * Extract skepticism factors
 */
function extractSkepticism(result: any) {
  // Determine skepticism factors
  const skepticismFactors = [];

  // Check for lack of specific dates
  if (result.moneyMentions.length > 0 && result.dateMentions.length === 0) {
    skepticismFactors.push('No specific dates mentioned for when benefits will be available');
  }

  // Check for lack of government program mentions
  if (result.moneyMentions.length > 0 && result.governmentMentions.length === 0) {
    skepticismFactors.push('No specific government program or authority mentioned');
  }

  // Check for unrealistic amounts
  if (result.amount) {
    const amountValue = parseFloat(result.amount.replace(/[^0-9.]/g, ''));
    if (amountValue > 10000) {
      skepticismFactors.push(`Unusually large benefit amount (${result.amount})`);
    }
  }

  // Combine skepticism factors
  if (skepticismFactors.length > 0) {
    result.skepticism = skepticismFactors.join('. ');
  }

  return result;
}

/**
 * Determines the benefit status based on context and keywords
 * @param info Extracted financial information
 * @returns Benefit status (confirmed, approved, proposed, etc.)
 */
function determineBenefitStatus(info: ExtractedFinancialInfo): BenefitStatus {
  // Special case for stimulus checks/payments
  // If we have a stimulus check mention, consider it at least PROPOSED
  const hasStimulus = info.timestamps.some(ts =>
    ts.text.toLowerCase().includes('stimulus check') ||
    ts.text.toLowerCase().includes('stimulus payment') ||
    ts.text.toLowerCase().includes('stimulus rebate'));

  if (hasStimulus && info.amount) {
    // Check if it's a rumored stimulus
    const isRumored = info.timestamps.some(ts =>
      ts.text.toLowerCase().includes('rumor') ||
      ts.text.toLowerCase().includes('might') ||
      ts.text.toLowerCase().includes('may') ||
      ts.text.toLowerCase().includes('could'));

    if (isRumored) {
      return BenefitStatus.RUMORED;
    } else {
      return BenefitStatus.PROPOSED; // Default to PROPOSED for stimulus mentions
    }
  }

  // Check all contexts for status-related keywords
  const allContexts = [
    ...info.moneyMentions.map(m => m.context),
    ...info.benefitMentions.map(m => m.context),
    ...info.governmentMentions.map(m => m.context),
    ...info.dateMentions.map(m => m.context)
  ].map(ctx => ctx.toLowerCase());

  // Check for confirmed benefits
  const confirmedKeywords = ['confirmed', 'available now', 'already available', 'in effect',
                           'active', 'current', 'existing', 'ongoing', 'in place',
                           'started', 'began', 'launched', 'released', 'distributed',
                           'being paid', 'being sent', 'being distributed', 'checks are out',
                           'payments are out', 'has started', 'has begun', 'is available',
                           'can claim now', 'can apply now', 'open for applications',
                           'accepting applications', 'direct deposit', 'deposited'];

  // Check for approved benefits
  const approvedKeywords = ['approved', 'passed', 'signed into law', 'enacted',
                          'authorized', 'official', 'finalized', 'guaranteed',
                          'will be paid', 'will be sent', 'will be distributed',
                          'coming soon', 'starting soon', 'next month', 'next week',
                          'scheduled for', 'set to begin', 'will start', 'will begin',
                          'will launch', 'will be available', 'will be released',
                          'has been approved', 'has passed', 'was signed'];

  // Check for proposed benefits
  const proposedKeywords = ['proposed', 'plan', 'bill', 'legislation', 'considering',
                          'discussing', 'debating', 'negotiating', 'working on',
                          'may be', 'might be', 'could be', 'potential', 'possible',
                          'in talks', 'in discussion', 'in congress', 'in the senate',
                          'in the house', 'being considered', 'under consideration',
                          'on the table', 'new proposal', 'new bill', 'new plan',
                          'if passed', 'if approved', 'if signed', 'would provide'];

  // Check for rumored benefits
  const rumoredKeywords = ['rumor', 'rumored', 'speculation', 'speculated',
                         'unconfirmed', 'unverified', 'alleged', 'supposedly',
                         'reportedly', 'claimed', 'sources say', 'according to sources',
                         'might happen', 'could happen', 'may happen', 'potentially',
                         'not confirmed', 'not verified', 'not official', 'unofficial',
                         'hearsay', 'word is', 'people are saying', 'some say',
                         'expected to', 'anticipated to', 'believed to'];

  // Check for each status type
  for (const context of allContexts) {
    if (confirmedKeywords.some(keyword => context.includes(keyword))) {
      return BenefitStatus.CONFIRMED;
    }
  }

  for (const context of allContexts) {
    if (approvedKeywords.some(keyword => context.includes(keyword))) {
      return BenefitStatus.APPROVED;
    }
  }

  for (const context of allContexts) {
    if (proposedKeywords.some(keyword => context.includes(keyword))) {
      return BenefitStatus.PROPOSED;
    }
  }

  for (const context of allContexts) {
    if (rumoredKeywords.some(keyword => context.includes(keyword))) {
      return BenefitStatus.RUMORED;
    }
  }

  // Default to unknown if no clear status indicators
  return BenefitStatus.UNKNOWN;
}

/**
 * Categorizes the financial benefit based on extracted information and benefit status
 */
function categorizeFinancialBenefit(info: ExtractedFinancialInfo, status: BenefitStatus): FinancialCategory {
  // If no money mentions, no financial benefit
  if (info.moneyMentions.length === 0) {
    return FinancialCategory.NONE;
  }

  // Special case for stimulus checks/payments
  // If we have a stimulus check mention and a dollar amount, consider it a financial benefit
  const hasStimulus = info.timestamps.some(ts =>
    ts.text.toLowerCase().includes('stimulus check') ||
    ts.text.toLowerCase().includes('stimulus payment') ||
    ts.text.toLowerCase().includes('stimulus rebate'));

  if (hasStimulus && info.amount) {
    // Default to ANTICIPATED for stimulus mentions
    return FinancialCategory.ANTICIPATED;
  }

  // Check for urgent/high certainty benefits
  const hasGovernmentProgram = info.governmentMentions.length > 0;
  const hasSpecificTimeline = info.dateMentions.length > 0;
  const hasSpecificSteps = info.stepMentions.length > 0;

  // If there are skepticism factors, mark as doubtful
  if (info.skepticism) {
    return FinancialCategory.DOUBTFUL;
  }

  // Categorize based on benefit status
  switch (status) {
    case BenefitStatus.CONFIRMED:
      // Confirmed benefits with specific details are urgent
      if (hasGovernmentProgram || hasSpecificTimeline || hasSpecificSteps) {
        return FinancialCategory.URGENT;
      }
      // Confirmed benefits without details are still anticipated
      return FinancialCategory.ANTICIPATED;

    case BenefitStatus.APPROVED:
      // Approved benefits with timeline are anticipated
      if (hasSpecificTimeline) {
        return FinancialCategory.ANTICIPATED;
      }
      // Approved benefits without timeline are proposed
      return FinancialCategory.PROPOSED;

    case BenefitStatus.PROPOSED:
      // Proposed benefits are categorized as proposed
      return FinancialCategory.PROPOSED;

    case BenefitStatus.RUMORED:
      // Rumored benefits are doubtful
      return FinancialCategory.DOUBTFUL;

    case BenefitStatus.UNKNOWN:
      // For unknown status, use the old logic
      if (hasGovernmentProgram && hasSpecificTimeline && hasSpecificSteps) {
        return FinancialCategory.URGENT;
      } else if (hasGovernmentProgram || hasSpecificTimeline || hasSpecificSteps) {
        return FinancialCategory.ANTICIPATED;
      } else {
        return FinancialCategory.DOUBTFUL;
      }
  }
}

/**
 * Calculates an enhanced financial score based on the extracted information, category, and status
 */
function calculateEnhancedFinancialScore(info: ExtractedFinancialInfo, category: FinancialCategory, status: BenefitStatus): number {
  // Base score based on category
  let score = 0;

  // Base score based on category - prioritize urgent and confirmed benefits
  switch (category) {
    case FinancialCategory.URGENT:
      score = 85; // Increased to prioritize urgent benefits
      break;
    case FinancialCategory.ANTICIPATED:
      score = 60; // Increased to prioritize anticipated benefits
      break;
    case FinancialCategory.PROPOSED:
      score = 35; // Slightly increased for proposed benefits
      break;
    case FinancialCategory.DOUBTFUL:
      score = 10; // Decreased for doubtful benefits
      break;
    case FinancialCategory.NONE:
      return 0;
  }

  // Adjust score based on benefit status - prioritize confirmed benefits
  switch (status) {
    case BenefitStatus.CONFIRMED:
      score += 15;
      break;
    case BenefitStatus.APPROVED:
      score += 10;
      break;
    case BenefitStatus.PROPOSED:
      score += 5;
      break;
    case BenefitStatus.RUMORED:
      score -= 15; // Increased penalty for rumored benefits
      break;
    // No adjustment for UNKNOWN
  }

  // Adjust score based on all detected amounts with improved scaling and validation
  // Consider both primary amount and total of all amounts
  let totalAmountValue = 0;
  let primaryAmountValue = 0;
  let multipleAmounts = false;
  let validAmountsCount = 0;

  // Calculate total value of all detected amounts
  if (info.allAmounts && info.allAmounts.length > 0) {
    // Only consider amounts with reasonable confidence
    const validAmounts = info.allAmounts.filter(amount => amount.confidence >= 0.4);
    validAmountsCount = validAmounts.length;

    if (validAmountsCount > 0) {
      // Sum up all valid amounts, weighted by their confidence
      validAmounts.forEach(amount => {
        // Apply higher weight to amounts in typical benefit ranges
        let amountWeight = amount.confidence;

        // Boost weight for amounts in typical benefit ranges
        if (amount.value >= 500 && amount.value <= 5000) {
          amountWeight *= 1.2; // Typical stimulus/benefit range
        } else if (amount.value > 5000 && amount.value <= 10000) {
          amountWeight *= 1.1; // Still reasonable benefit range
        } else if (amount.value > 10000 && amount.value <= 50000) {
          amountWeight *= 0.9; // Less common but possible
        } else if (amount.value > 50000 && amount.value <= 1000000) {
          amountWeight *= 0.7; // Uncommon for individual benefits
        } else if (amount.value > 1000000) {
          amountWeight *= 0.5; // Rare for individual benefits
        }

        totalAmountValue += amount.value * amountWeight;
      });

      // Get the primary amount value (highest confidence)
      primaryAmountValue = validAmounts[0].value;

      // Check if we have multiple significant amounts
      multipleAmounts = validAmountsCount > 1;
    }
  } else if (info.amount) {
    // Fallback to the primary amount if allAmounts is not available
    primaryAmountValue = extractNumericValue(info.amount);
    totalAmountValue = primaryAmountValue;
    validAmountsCount = 1;
  }

  // Calculate amount bonus based on the primary amount with more realistic scaling
  // Prioritize direct money benefits in the typical range for individuals
  let amountBonus = 0;
  if (primaryAmountValue > 0) {
    // Enhanced amount bonus calculation with better scaling for individual benefits
    if (primaryAmountValue >= 1000000000) { // Billions - extremely rare for individual benefits
      amountBonus = 10; // Lower value due to likely being program total, not individual benefit
    } else if (primaryAmountValue >= 1000000) { // Millions - rare for individual benefits
      amountBonus = 15; // Still significant but less likely to be individual
    } else if (primaryAmountValue >= 100000) { // Hundred thousands
      amountBonus = 18; // Significant but uncommon for individual benefits
    } else if (primaryAmountValue >= 10000) { // Tens of thousands
      amountBonus = 22; // Very significant for most people
    } else if (primaryAmountValue >= 2000) { // Few thousand (typical stimulus range)
      amountBonus = 25; // Maximum bonus - very relevant for most people
    } else if (primaryAmountValue >= 1000) { // One thousand
      amountBonus = 23; // Very relevant for most people
    } else if (primaryAmountValue >= 500) { // Hundreds
      amountBonus = 20; // Moderately relevant
    } else if (primaryAmountValue >= 100) { // Small hundreds
      amountBonus = 15; // Less relevant but still beneficial
    } else if (primaryAmountValue >= 50) { // Tens
      amountBonus = 10; // Minor relevance
    } else if (primaryAmountValue > 0) { // Any positive amount
      amountBonus = 5; // Minimal relevance
    }

    // Boost score for multiple benefit amounts (indicates more comprehensive analysis)
    if (multipleAmounts) {
      amountBonus += Math.min(5, validAmountsCount); // Up to 5 extra points for multiple amounts
    }

    // Add the amount bonus to the score
    score += amountBonus;
  }

  // Add bonus for multiple amounts (more comprehensive financial information)
  if (multipleAmounts) {
    // Calculate additional bonus based on total amount value
    // This rewards videos that mention multiple benefits
    const totalAmountBonus = Math.min(8, Math.floor(Math.log10(totalAmountValue) - Math.log10(primaryAmountValue) + 1));
    score += totalAmountBonus;

    // Add bonus for number of different valid amounts (max +5)
    const multipleAmountsBonus = Math.min(5, validAmountsCount - 1);
    score += multipleAmountsBonus;

    // Add bonus for having multiple amounts in typical benefit ranges
    const typicalBenefitAmounts = info.allAmounts.filter(amount =>
      amount.value >= 500 && amount.value <= 5000 && amount.confidence >= 0.5
    );

    if (typicalBenefitAmounts.length > 1) {
      score += Math.min(5, typicalBenefitAmounts.length);
    }
  }

  // Adjust score based on specificity
  if (info.timeline) score += 5;
  if (info.recipients) score += 5;
  if (info.steps) score += 5;

  // Adjust score based on government program mentions
  if (info.governmentMentions.length > 0) {
    // More mentions of government programs increases credibility
    const programBonus = Math.min(5, info.governmentMentions.length);
    score += programBonus;
  }

  // Adjust score based on skepticism
  if (info.skepticism) {
    score -= 15;
  }

  // Ensure score is between 0 and 100
  return Math.max(0, Math.min(100, score));
}

/**
 * Generates an enhanced detailed analysis based on the extracted information
 */
function generateEnhancedAnalysis(info: ExtractedFinancialInfo, category: FinancialCategory, status: BenefitStatus): string {
  if (category === FinancialCategory.NONE) {
    return 'No financial benefits detected in this video.';
  }

  let analysis = '';

  // Add category-specific intro with status information
  switch (category) {
    case FinancialCategory.URGENT:
      analysis += '✅ URGENT/HIGH CERTAINTY: This video discusses a financial benefit with clear proof and specific details.\n\n';
      break;
    case FinancialCategory.ANTICIPATED:
      analysis += '🕒 ANTICIPATED/POSSIBLE: This video discusses an upcoming or possible financial benefit.\n\n';
      break;
    case FinancialCategory.PROPOSED:
      analysis += '📝 PROPOSED: This video discusses a proposed financial benefit that is not yet approved.\n\n';
      break;
    case FinancialCategory.DOUBTFUL:
      analysis += '⚠️ DOUBTFUL: This video mentions financial benefits but lacks specific details or proof.\n\n';
      break;
  }

  // Add benefit status information
  switch (status) {
    case BenefitStatus.CONFIRMED:
      analysis += '✓ Status: CONFIRMED - This benefit is confirmed and currently available.\n\n';
      break;
    case BenefitStatus.APPROVED:
      analysis += '✓ Status: APPROVED - This benefit has been approved but is not yet available.\n\n';
      break;
    case BenefitStatus.PROPOSED:
      analysis += '⏳ Status: PROPOSED - This benefit has been proposed but not yet approved.\n\n';
      break;
    case BenefitStatus.RUMORED:
      analysis += '❓ Status: RUMORED - This benefit is rumored but not confirmed by official sources.\n\n';
      break;
    case BenefitStatus.UNKNOWN:
      // Don't add anything for unknown status
      break;
  }

  // Add financial amounts with enhanced display
  if (info.allAmounts && info.allAmounts.length > 0) {
    // Display primary amount first
    analysis += `💰 Primary Benefit: ${info.allAmounts[0].formatted}`;

    // Add frequency information if available
    if (info.allAmounts[0].frequency !== BenefitFrequency.UNKNOWN) {
      const frequencyText = info.allAmounts[0].frequency === BenefitFrequency.ONE_TIME ? 'one-time payment' :
                           info.allAmounts[0].frequency === BenefitFrequency.WEEKLY ? 'weekly payment' :
                           info.allAmounts[0].frequency === BenefitFrequency.MONTHLY ? 'monthly payment' :
                           info.allAmounts[0].frequency === BenefitFrequency.YEARLY ? 'yearly payment' :
                           info.allAmounts[0].frequency === BenefitFrequency.QUARTERLY ? 'quarterly payment' : '';

      if (frequencyText) {
        analysis += ` (${frequencyText})`;
      }
    }

    analysis += '\n\n';

    // Display additional amounts if there are any
    if (info.allAmounts.length > 1) {
      analysis += '💵 Additional Benefit Amounts:\n';

      // Display up to 5 additional amounts to avoid overwhelming the user
      const additionalAmounts = info.allAmounts.slice(1, 6);
      additionalAmounts.forEach((amount, index) => {
        analysis += `   ${index + 1}. ${amount.formatted}`;

        // Add frequency information if available
        if (amount.frequency !== BenefitFrequency.UNKNOWN) {
          const frequencyText = amount.frequency === BenefitFrequency.ONE_TIME ? 'one-time' :
                               amount.frequency === BenefitFrequency.WEEKLY ? 'weekly' :
                               amount.frequency === BenefitFrequency.MONTHLY ? 'monthly' :
                               amount.frequency === BenefitFrequency.YEARLY ? 'yearly' :
                               amount.frequency === BenefitFrequency.QUARTERLY ? 'quarterly' : '';

          if (frequencyText) {
            analysis += ` (${frequencyText})`;
          }
        }

        analysis += '\n';
      });

      // Indicate if there are more amounts not shown
      if (info.allAmounts.length > 6) {
        analysis += `   ...and ${info.allAmounts.length - 6} more\n`;
      }

      analysis += '\n';
    }
  } else if (info.amount) {
    // Fallback to the primary amount if allAmounts is not available
    // Make sure it's properly formatted with standard numeric format
    const numericValue = extractNumericValue(info.amount);
    const formattedAmount = `$${numericValue.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })}`;
    analysis += `💰 Benefit Amount: ${formattedAmount}\n\n`;
  }

  // Add timeline
  if (info.timeline) {
    analysis += `📅 Timeline: ${info.timeline}\n\n`;
  }

  // Add recipients
  if (info.recipients) {
    analysis += `👥 Eligible Recipients: ${info.recipients}\n\n`;
  }

  // Add steps
  if (info.steps) {
    analysis += `📋 Steps to Claim: ${info.steps}\n\n`;
  }

  // Add government program information if available
  if (info.governmentMentions.length > 0) {
    const uniquePrograms = [...new Set(info.governmentMentions.map(m => m.program))];
    if (uniquePrograms.length > 0) {
      analysis += `🏛️ Related Programs/Agencies: ${uniquePrograms.join(', ')}\n\n`;
    }
  }

  // Add skepticism
  if (info.skepticism) {
    analysis += `⚠️ Reasons for Skepticism: ${info.skepticism}\n\n`;
  }

  // Add viral potential
  if (info.viralPotential) {
    analysis += `🔥 Viral Potential: ${info.viralPotential}\n\n`;
  }

  // Add summary with more detailed information
  analysis += 'Summary: ';

  // Build a more detailed summary based on category and status
  let summaryText = `This video ${status === BenefitStatus.CONFIRMED ? 'provides information about' :
                     status === BenefitStatus.APPROVED ? 'announces' :
                     status === BenefitStatus.PROPOSED ? 'proposes' :
                     status === BenefitStatus.RUMORED ? 'claims' : 'mentions'} `;

  // Add amount information with proper formatting
  if (info.amount) {
    // Format the amount using standard numeric format
    const numericValue = extractNumericValue(info.amount);
    const formattedAmount = `$${numericValue.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })}`;

    // Update the info.amount to use the properly formatted amount
    // This ensures the database stores the correctly formatted amount
    info.amount = formattedAmount;

    summaryText += `a ${formattedAmount} benefit`;
  } else {
    summaryText += 'a benefit';
  }

  // Add status context
  switch (status) {
    case BenefitStatus.CONFIRMED:
      summaryText += ' that is currently available';
      break;
    case BenefitStatus.APPROVED:
      summaryText += ' that has been approved';
      break;
    case BenefitStatus.PROPOSED:
      summaryText += ' that has been proposed';
      break;
    case BenefitStatus.RUMORED:
      summaryText += ' that is rumored';
      break;
  }

  // Add details about available information
  const details = [];
  if (info.timeline) details.push('timeline');
  if (info.recipients) details.push('eligibility');
  if (info.steps) details.push('how to claim');

  if (details.length > 0) {
    summaryText += ` with ${details.join(', ')} details`;
  }

  summaryText += '.';
  analysis += summaryText;

  return analysis;
}

/**
 * Generates a detailed analysis based on the extracted information
 * @deprecated Use generateEnhancedAnalysis instead
 */
function generateAnalysis(info: ExtractedFinancialInfo, category: FinancialCategory): string {
  if (category === FinancialCategory.NONE) {
    return 'No financial benefits detected in this video.';
  }

  let analysis = '';

  // Add category-specific intro
  switch (category) {
    case FinancialCategory.URGENT:
      analysis += '✅ URGENT/HIGH CERTAINTY: This video discusses a financial benefit with clear proof and specific details.\n\n';
      break;
    case FinancialCategory.ANTICIPATED:
      analysis += '🕒 ANTICIPATED/POSSIBLE: This video discusses an upcoming or possible financial benefit.\n\n';
      break;
    case FinancialCategory.DOUBTFUL:
      analysis += '⚠️ DOUBTFUL: This video mentions financial benefits but lacks specific details or proof.\n\n';
      break;
  }

  // Add financial amount
  if (info.amount) {
    analysis += `💰 Benefit Amount: ${info.amount}\n\n`;
  }

  // Add timeline
  if (info.timeline) {
    analysis += `📅 Timeline: ${info.timeline}\n\n`;
  }

  // Add recipients
  if (info.recipients) {
    analysis += `👥 Eligible Recipients: ${info.recipients}\n\n`;
  }

  // Add steps
  if (info.steps) {
    analysis += `📋 Steps to Claim: ${info.steps}\n\n`;
  }

  // Add skepticism
  if (info.skepticism) {
    analysis += `⚠️ Reasons for Skepticism: ${info.skepticism}\n\n`;
  }

  // Add viral potential
  if (info.viralPotential) {
    analysis += `🔥 Viral Potential: ${info.viralPotential}\n\n`;
  }

  // Add summary
  analysis += 'Summary: ';
  switch (category) {
    case FinancialCategory.URGENT:
      analysis += `This video provides information about a benefit of ${info.amount} with clear details on eligibility, timeline, and how to claim it.`;
      break;
    case FinancialCategory.ANTICIPATED:
      analysis += `This video discusses a possible benefit of ${info.amount} that may be available in the future.`;
      break;
    case FinancialCategory.DOUBTFUL:
      analysis += `This video mentions a benefit of ${info.amount} but lacks sufficient proof or details to verify its legitimacy.`;
      break;
  }

  return analysis;
}

/**
 * Formats financial timestamps for display
 */
function formatFinancialTimestamps(timestamps: { timestamp: string; text: string; isFinancial: boolean }[]): string {
  return timestamps
    .filter(ts => ts.isFinancial)
    .map(ts => `[${ts.timestamp}] ${ts.text}`)
    .join('\n');
}



/**
 * Convert a timestamp string (MM:SS) to seconds
 */
function convertTimestampToSeconds(timestamp: string): number {
  if (!timestamp) return 0;

  try {
    // Handle different timestamp formats
    // Format 1: MM:SS
    if (/^\d+:\d+$/.test(timestamp)) {
      const [minutes, seconds] = timestamp.split(':').map(Number);
      return minutes * 60 + seconds;
    }

    // Format 2: HH:MM:SS
    if (/^\d+:\d+:\d+$/.test(timestamp)) {
      const [hours, minutes, seconds] = timestamp.split(':').map(Number);
      return hours * 3600 + minutes * 60 + seconds;
    }

    // Format 3: [MM:SS]
    if (/^\[\d+:\d+\]$/.test(timestamp)) {
      const timeStr = timestamp.substring(1, timestamp.length - 1);
      const [minutes, seconds] = timeStr.split(':').map(Number);
      return minutes * 60 + seconds;
    }

    // If it's just a number, assume it's already in seconds
    if (!isNaN(Number(timestamp))) {
      return Number(timestamp);
    }
  } catch (error) {
    console.error('Error converting timestamp to seconds:', error);
  }

  return 0;
}
