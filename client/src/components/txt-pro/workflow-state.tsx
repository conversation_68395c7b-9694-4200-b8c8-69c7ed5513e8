import React, { createContext, useContext, useState, useEffect } from 'react';

// Workflow State Types
export interface WorkflowStep {
  id: string;
  name: string;
  completed: boolean;
  data: any;
}

export interface WorkflowState {
  currentStep: number;
  steps: WorkflowStep[];
  finalInstructions: string;
  generatedScripts: GeneratedScript[];
}

export interface GeneratedScript {
  id: string;
  title: string;
  content: string;
  instructions: string;
  sourceWorkflow: WorkflowSummary;
  dateGenerated: Date;
  version: number;
}

export interface WorkflowSummary {
  sourceScript: string;
  selectedChunks: string[];
  selectedStrategies: string[];
  highlights: number;
  targetAudience: string;
  tone: string;
  length: string;
}

interface WorkflowContextType {
  workflowState: WorkflowState;
  updateStepData: (stepId: string, data: any) => void;
  markStepComplete: (stepId: string) => void;
  goToStep: (stepIndex: number) => void;
  generateFinalInstructions: () => string;
  saveGeneratedScript: (script: Omit<GeneratedScript, 'id' | 'dateGenerated' | 'version'>) => void;
  resetWorkflow: () => void;
  getWorkflowProgress: () => number;
  canProceedToNext: () => boolean;
}

const WorkflowContext = createContext<WorkflowContextType | undefined>(undefined);

const initialWorkflowState: WorkflowState = {
  currentStep: 0,
  steps: [
    {
      id: 'script-selection',
      name: 'Select Source Script',
      completed: false,
      data: { selectedScript: null }
    },
    {
      id: 'chunk-creation',
      name: 'Create Content Chunks',
      completed: false,
      data: { selectedChunks: [] }
    },
    {
      id: 'highlight-analysis',
      name: 'Highlight Engagement Hotspots',
      completed: false,
      data: { highlights: [], includeHighlights: true }
    },
    {
      id: 'strategy-selection',
      name: 'Choose Writing Strategies',
      completed: false,
      data: { selectedStrategies: [] }
    },
    {
      id: 'configuration',
      name: 'Configure Script Settings',
      completed: false,
      data: {
        title: '',
        targetAudience: 'seniors-ssi',
        tone: 'hopeful',
        length: '2000',
        scriptMode: 'single',
        customInstructions: ''
      }
    },
    {
      id: 'generation',
      name: 'Generate & Save Script',
      completed: false,
      data: { finalInstructions: '', generatedScripts: [] }
    }
  ],
  finalInstructions: '',
  generatedScripts: []
};

export const WorkflowProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [workflowState, setWorkflowState] = useState<WorkflowState>(() => {
    const saved = localStorage.getItem('txtpro_workflow');
    return saved ? JSON.parse(saved) : initialWorkflowState;
  });

  // Save to localStorage whenever workflow state changes
  useEffect(() => {
    localStorage.setItem('txtpro_workflow', JSON.stringify(workflowState));
  }, [workflowState]);

  const updateStepData = (stepId: string, data: any) => {
    setWorkflowState(prev => ({
      ...prev,
      steps: prev.steps.map(step =>
        step.id === stepId
          ? { ...step, data: { ...step.data, ...data } }
          : step
      )
    }));
  };

  const markStepComplete = (stepId: string) => {
    setWorkflowState(prev => ({
      ...prev,
      steps: prev.steps.map(step =>
        step.id === stepId
          ? { ...step, completed: true }
          : step
      )
    }));
  };

  const goToStep = (stepIndex: number) => {
    setWorkflowState(prev => ({
      ...prev,
      currentStep: stepIndex
    }));
  };

  const generateFinalInstructions = (): string => {
    const steps = workflowState.steps;
    const scriptStep = steps.find(s => s.id === 'script-selection');
    const chunkStep = steps.find(s => s.id === 'chunk-creation');
    const highlightStep = steps.find(s => s.id === 'highlight-analysis');
    const strategyStep = steps.find(s => s.id === 'strategy-selection');
    const configStep = steps.find(s => s.id === 'configuration');

    if (!scriptStep?.data.selectedScript || !configStep?.data) {
      return '';
    }

    const config = configStep.data;
    const selectedScript = scriptStep.data.selectedScript;

    let instructions = `You are a YouTube script writer specialized in Social Security and financial news. `;
    instructions += `Write for ${config.targetAudience.replace('-', ' ')} using a ${config.tone}, humanlike tone. `;
    
    if (config.scriptMode === 'multi') {
      instructions += `Write in multiple parts - wait for the user to type 'continue' before generating the next section. `;
    } else {
      instructions += `Generate the complete script in one response. `;
    }

    instructions += `Target length: ${config.length} words. `;

    // Add strategies
    if (strategyStep?.data.selectedStrategies?.length > 0) {
      instructions += `\n\nApply these writing strategies:\n`;
      strategyStep.data.selectedStrategies.forEach((strategy: any) => {
        instructions += `- ${strategy.title}: ${strategy.description}\n`;
      });
    }

    // Add chunks
    if (chunkStep?.data.selectedChunks?.length > 0) {
      instructions += `\n\nIncorporate these content chunks:\n`;
      chunkStep.data.selectedChunks.forEach((chunk: any) => {
        instructions += `- ${chunk.name}: "${chunk.content.substring(0, 100)}..."\n`;
      });
    }

    // Add highlights
    if (highlightStep?.data.highlights?.length > 0 && highlightStep.data.includeHighlights) {
      instructions += `\n\nFocus on these engagement hotspots:\n`;
      highlightStep.data.highlights.forEach((highlight: any) => {
        instructions += `- ${highlight.note} (${highlight.color} highlight)\n`;
      });
    }

    instructions += `\n\nSource context: "${selectedScript.title}"\n`;
    instructions += `Original transcript: "${selectedScript.transcript.substring(0, 200)}..."\n`;

    if (config.customInstructions) {
      instructions += `\n\nAdditional instructions: ${config.customInstructions}\n`;
    }

    instructions += `\n\nFormat with clear sections and natural speech patterns. Focus on valuable information.`;

    setWorkflowState(prev => ({
      ...prev,
      finalInstructions: instructions
    }));

    return instructions;
  };

  const saveGeneratedScript = (script: Omit<GeneratedScript, 'id' | 'dateGenerated' | 'version'>) => {
    const newScript: GeneratedScript = {
      ...script,
      id: `script_${Date.now()}`,
      dateGenerated: new Date(),
      version: workflowState.generatedScripts.length + 1
    };

    setWorkflowState(prev => ({
      ...prev,
      generatedScripts: [...prev.generatedScripts, newScript]
    }));
  };

  const resetWorkflow = () => {
    setWorkflowState(initialWorkflowState);
  };

  const getWorkflowProgress = (): number => {
    const completedSteps = workflowState.steps.filter(step => step.completed).length;
    return Math.round((completedSteps / workflowState.steps.length) * 100);
  };

  const canProceedToNext = (): boolean => {
    // Always allow proceeding to maximize workflow flexibility
    return true;
  };

  return (
    <WorkflowContext.Provider value={{
      workflowState,
      updateStepData,
      markStepComplete,
      goToStep,
      generateFinalInstructions,
      saveGeneratedScript,
      resetWorkflow,
      getWorkflowProgress,
      canProceedToNext
    }}>
      {children}
    </WorkflowContext.Provider>
  );
};

export const useWorkflow = () => {
  const context = useContext(WorkflowContext);
  if (context === undefined) {
    throw new Error('useWorkflow must be used within a WorkflowProvider');
  }
  return context;
};
