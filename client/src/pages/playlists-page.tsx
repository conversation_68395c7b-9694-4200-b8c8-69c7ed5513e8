import { useState, useEffect } from "react";
import { <PERSON> } from "wouter";
import { usePlaylists, Playlist, PlaylistVideo } from "@/hooks/use-playlists";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Loader2, Plus, Pencil, Trash2, BookmarkPlus, ExternalLink, Menu, Home } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { Sidebar } from "@/components/navigation/sidebar";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function PlaylistsPage() {
  const { toast } = useToast();
  const [selectedPlaylistId, setSelectedPlaylistId] = useState<number | null>(null);
  const { playlists, isLoading, createPlaylist, updatePlaylist, deletePlaylist, playlistVideos, isLoadingVideos, refetchVideos } = usePlaylists(selectedPlaylistId);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState("");
  const [newPlaylistDescription, setNewPlaylistDescription] = useState("");
  const [editPlaylistName, setEditPlaylistName] = useState("");
  const [editPlaylistDescription, setEditPlaylistDescription] = useState("");
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Handle window resize for mobile detection
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Refetch videos when selected playlist changes
  useEffect(() => {
    if (selectedPlaylistId) {
      refetchVideos();
    }
  }, [selectedPlaylistId, refetchVideos]);

  // Find the selected playlist
  const selectedPlaylist = playlists.find(p => p.id === selectedPlaylistId);

  // Handle creating a new playlist
  const handleCreatePlaylist = async () => {
    if (!newPlaylistName.trim()) return;

    await createPlaylist.mutateAsync({
      name: newPlaylistName,
      description: newPlaylistDescription,
    });

    // Reset form and close dialog
    setNewPlaylistName("");
    setNewPlaylistDescription("");
    setIsCreateDialogOpen(false);
  };

  // Handle editing a playlist
  const handleEditPlaylist = async () => {
    if (!selectedPlaylistId || !editPlaylistName.trim()) return;

    await updatePlaylist.mutateAsync({
      id: selectedPlaylistId,
      name: editPlaylistName,
      description: editPlaylistDescription,
    });

    // Reset form and close dialog
    setEditPlaylistName("");
    setEditPlaylistDescription("");
    setIsEditDialogOpen(false);
  };

  // Handle deleting a playlist
  const handleDeletePlaylist = async () => {
    if (!selectedPlaylistId) return;

    await deletePlaylist.mutateAsync(selectedPlaylistId);

    // Reset selected playlist
    setSelectedPlaylistId(null);
  };

  // Open edit dialog with current playlist data
  const openEditDialog = (playlist: Playlist) => {
    setEditPlaylistName(playlist.name);
    setEditPlaylistDescription(playlist.description || "");
    setIsEditDialogOpen(true);
  };

  // Handle opening a video
  const handleOpenVideo = (videoId: string) => {
    window.open(`https://youtube.com/watch?v=${videoId}`, "_blank");
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar - Always present, either full or mini */}
      <Sidebar />

      <main className="flex-1 overflow-auto">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              {/* Menu button removed - sidebar toggle is handled by the sidebar component */}
              <Link href="/">
                <Button variant="ghost" size="icon" title="Back to Home">
                  <Home className="h-5 w-5" />
                </Button>
              </Link>
              <h1 className="text-3xl font-bold">My Playlists</h1>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Playlist
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create new playlist</DialogTitle>
                  <DialogDescription>
                    Create a new playlist to save your favorite videos.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      placeholder="My Playlist"
                      value={newPlaylistName}
                      onChange={(e) => setNewPlaylistName(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description (optional)</Label>
                    <Input
                      id="description"
                      placeholder="A collection of my favorite videos"
                      value={newPlaylistDescription}
                      onChange={(e) => setNewPlaylistDescription(e.target.value)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreatePlaylist}>Create</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {playlists.length === 0 ? (
            <Card className="w-full">
              <CardHeader>
                <CardTitle>No playlists yet</CardTitle>
                <CardDescription>
                  Create your first playlist to start saving your favorite videos.
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Playlist
                </Button>
              </CardFooter>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="md:col-span-1">
                <div className="space-y-2">
                  <h2 className="text-lg font-medium mb-4">Your Playlists</h2>
                  <div className="space-y-2">
                    {playlists.map((playlist) => (
                      <Button
                        key={playlist.id}
                        variant={selectedPlaylistId === playlist.id ? "default" : "outline"}
                        className="w-full justify-start"
                        onClick={() => setSelectedPlaylistId(playlist.id)}
                      >
                        <BookmarkPlus className="h-4 w-4 mr-2" />
                        {playlist.name}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="md:col-span-3">
                {selectedPlaylistId ? (
                  <>
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h2 className="text-2xl font-bold">{selectedPlaylist?.name}</h2>
                        {selectedPlaylist?.description && (
                          <p className="text-muted-foreground mt-1">{selectedPlaylist.description}</p>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openEditDialog(selectedPlaylist!)}
                        >
                          <Pencil className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="destructive" size="sm">
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This will permanently delete the "{selectedPlaylist?.name}" playlist and all its saved videos.
                                This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={handleDeletePlaylist}>
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>

                    <Separator className="my-4" />

                    {isLoadingVideos ? (
                      <div className="flex items-center justify-center h-64">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    ) : playlistVideos.length === 0 ? (
                      <div className="text-center py-12">
                        <p className="text-muted-foreground">No videos in this playlist yet.</p>
                        <p className="text-muted-foreground mt-2">
                          Add videos to this playlist by clicking the bookmark button on videos.
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {playlistVideos.map((video) => (
                          <Card key={video.id} className="overflow-hidden">
                            <div className="flex flex-col sm:flex-row">
                              <div className="w-full sm:w-48 h-32 relative">
                                <img
                                  src={video.thumbnail}
                                  alt={video.title}
                                  className="w-full h-full object-cover"
                                />
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white rounded-full"
                                  onClick={() => handleOpenVideo(video.videoId)}
                                >
                                  <ExternalLink className="h-4 w-4" />
                                </Button>
                              </div>
                              <CardContent className="p-4 flex-1">
                                <h3
                                  className="font-semibold leading-tight hover:text-primary cursor-pointer"
                                  onClick={() => handleOpenVideo(video.videoId)}
                                >
                                  {video.title}
                                </h3>
                                <p className="text-sm text-muted-foreground mt-1">{video.channelTitle}</p>
                                <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
                                  <div className="flex items-center gap-1">
                                    <span>{video.viewCount.toLocaleString()} views</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <span>Added {formatDistanceToNow(new Date(video.addedAt))} ago</span>
                                  </div>
                                </div>
                              </CardContent>
                            </div>
                          </Card>
                        ))}
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">Select a playlist to view its videos.</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Edit Playlist Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit playlist</DialogTitle>
                <DialogDescription>
                  Update your playlist details.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-name">Name</Label>
                  <Input
                    id="edit-name"
                    placeholder="My Playlist"
                    value={editPlaylistName}
                    onChange={(e) => setEditPlaylistName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-description">Description (optional)</Label>
                  <Input
                    id="edit-description"
                    placeholder="A collection of my favorite videos"
                    value={editPlaylistDescription}
                    onChange={(e) => setEditPlaylistDescription(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleEditPlaylist}>Save Changes</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </main>
    </div>
  );
}
