import React, { useState, useEffect } from 'react';
import { apiRequest } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { PlusCircle, RefreshCw, Trash2, ExternalLink } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface RssFeed {
  id: number;
  name: string;
  url: string;
  createdAt: string;
}

interface RssFeedItem {
  id: number;
  feedId: number;
  title: string;
  link: string;
  publishedAt: string;
  content: string;
  scrapedContent: string | null;
  isRead: boolean;
}

export function RssTab() {
  const [feeds, setFeeds] = useState<RssFeed[]>([]);
  const [feedItems, setFeedItems] = useState<RssFeedItem[]>([]);
  const [selectedFeed, setSelectedFeed] = useState<number | null>(null);
  const [selectedItem, setSelectedItem] = useState<RssFeedItem | null>(null);
  const [newFeedName, setNewFeedName] = useState('');
  const [newFeedUrl, setNewFeedUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch RSS feeds on component mount
  useEffect(() => {
    fetchFeeds();
  }, []);

  // Fetch feed items when a feed is selected
  useEffect(() => {
    if (selectedFeed) {
      fetchFeedItems(selectedFeed);
    }
  }, [selectedFeed]);

  // Fetch RSS feeds
  const fetchFeeds = async () => {
    try {
      setIsLoading(true);
      const response = await apiRequest('GET', '/api/rss-feeds');
      const data = await response.json();
      setFeeds(data);

      // Select the first feed if available
      if (data.length > 0 && !selectedFeed) {
        setSelectedFeed(data[0].id);
      }

      setError(null);
    } catch (error) {
      console.error('Error fetching RSS feeds:', error);
      setError('Failed to fetch RSS feeds');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch feed items
  const fetchFeedItems = async (feedId: number) => {
    try {
      setIsLoading(true);
      const response = await apiRequest('GET', `/api/rss-feeds/${feedId}/items`);
      const data = await response.json();
      setFeedItems(data);
      setError(null);
    } catch (error) {
      console.error('Error fetching feed items:', error);
      setError('Failed to fetch feed items');
    } finally {
      setIsLoading(false);
    }
  };

  // Add a new RSS feed
  const addFeed = async () => {
    if (!newFeedName || !newFeedUrl) {
      setError('Feed name and URL are required');
      return;
    }

    try {
      setIsLoading(true);
      const response = await apiRequest('POST', '/api/rss-feeds', {
        name: newFeedName,
        url: newFeedUrl,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add RSS feed');
      }

      const newFeed = await response.json();
      setFeeds([...feeds, newFeed]);
      setNewFeedName('');
      setNewFeedUrl('');
      setSelectedFeed(newFeed.id);
      setError(null);
    } catch (error) {
      console.error('Error adding RSS feed:', error);
      setError(error instanceof Error ? error.message : 'Failed to add RSS feed');
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a feed
  const deleteFeed = async (feedId: number) => {
    if (!confirm('Are you sure you want to delete this feed?')) {
      return;
    }

    try {
      setIsLoading(true);
      const response = await apiRequest('DELETE', `/api/rss-feeds/${feedId}`);

      if (!response.ok) {
        throw new Error('Failed to delete RSS feed');
      }

      setFeeds(feeds.filter(feed => feed.id !== feedId));

      if (selectedFeed === feedId) {
        const remainingFeeds = feeds.filter(feed => feed.id !== feedId);
        setSelectedFeed(remainingFeeds.length > 0 ? remainingFeeds[0].id : null);
        setFeedItems([]);
      }

      setError(null);
    } catch (error) {
      console.error('Error deleting RSS feed:', error);
      setError('Failed to delete RSS feed');
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh a feed
  const refreshFeed = async (feedId: number) => {
    try {
      setIsLoading(true);
      const response = await apiRequest('POST', `/api/rss-feeds/${feedId}/refresh`);

      if (!response.ok) {
        throw new Error('Failed to refresh RSS feed');
      }

      // Fetch updated items
      fetchFeedItems(feedId);
      setError(null);
    } catch (error) {
      console.error('Error refreshing RSS feed:', error);
      setError('Failed to refresh RSS feed');
    } finally {
      setIsLoading(false);
    }
  };

  // View item content
  const viewItemContent = async (item: RssFeedItem) => {
    setSelectedItem(item);

    // If the item doesn't have scraped content, fetch it
    if (!item.scrapedContent) {
      try {
        setIsLoading(true);
        const response = await apiRequest('GET', `/api/rss-feeds/items/${item.id}/content`);

        if (!response.ok) {
          throw new Error('Failed to fetch item content');
        }

        const data = await response.json();

        // Update the item with the scraped content
        const updatedItem = { ...item, scrapedContent: data.content };
        setSelectedItem(updatedItem);

        // Update the item in the feedItems array
        setFeedItems(feedItems.map(i => i.id === item.id ? updatedItem : i));

        setError(null);
      } catch (error) {
        console.error('Error fetching item content:', error);
        setError('Failed to fetch item content');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Mark item as read/unread
  const toggleItemRead = async (item: RssFeedItem) => {
    try {
      setIsLoading(true);
      const response = await apiRequest('PATCH', `/api/rss-feeds/items/${item.id}`, {
        isRead: !item.isRead,
      });

      if (!response.ok) {
        throw new Error('Failed to update item');
      }

      const updatedItem = await response.json();

      // Update the item in the feedItems array
      setFeedItems(feedItems.map(i => i.id === item.id ? updatedItem : i));

      // Update the selected item if it's the one being toggled
      if (selectedItem && selectedItem.id === item.id) {
        setSelectedItem(updatedItem);
      }

      setError(null);
    } catch (error) {
      console.error('Error updating item:', error);
      setError('Failed to update item');
    } finally {
      setIsLoading(false);
    }
  };

  // Extract plain text from HTML content
  const extractTextFromHtml = (html: string): string => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  return (
    <div className="flex flex-col h-full">
      <Tabs defaultValue="feeds" className="w-full h-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="feeds">RSS Feeds</TabsTrigger>
          <TabsTrigger value="add">Add New Feed</TabsTrigger>
        </TabsList>

        <TabsContent value="feeds" className="flex flex-col h-full">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <div className="flex flex-col md:flex-row h-full gap-4">
            {/* Feed List */}
            <div className="w-full md:w-1/4 flex flex-col">
              <h3 className="text-lg font-semibold mb-2">Your Feeds</h3>
              <ScrollArea className="h-[200px] md:h-[calc(100vh-300px)]">
                {feeds.length === 0 ? (
                  <p className="text-muted-foreground">No feeds added yet</p>
                ) : (
                  <div className="space-y-2">
                    {feeds.map(feed => (
                      <div
                        key={feed.id}
                        className={`p-2 rounded flex justify-between items-center cursor-pointer ${selectedFeed === feed.id ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
                        onClick={() => setSelectedFeed(feed.id)}
                      >
                        <span className="truncate">{feed.name}</span>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              refreshFeed(feed.id);
                            }}
                          >
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteFeed(feed.id);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </div>

            {/* Feed Items */}
            <div className="w-full md:w-3/4 flex flex-col">
              {selectedFeed ? (
                <div className="flex flex-col h-full">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-semibold">
                      {feeds.find(f => f.id === selectedFeed)?.name || 'Feed Items'}
                    </h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => refreshFeed(selectedFeed)}
                      disabled={isLoading}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh
                    </Button>
                  </div>

                  <div className="flex flex-col md:flex-row h-full gap-4">
                    {/* Items List */}
                    <div className="w-full md:w-1/2">
                      <ScrollArea className="h-[200px] md:h-[calc(100vh-350px)]">
                        {feedItems.length === 0 ? (
                          <p className="text-muted-foreground">No items in this feed</p>
                        ) : (
                          <div className="space-y-2">
                            {feedItems.map(item => (
                              <div
                                key={item.id}
                                className={`p-2 rounded cursor-pointer ${selectedItem?.id === item.id ? 'bg-primary text-primary-foreground' : item.isRead ? 'opacity-60 hover:bg-muted' : 'hover:bg-muted'}`}
                                onClick={() => viewItemContent(item)}
                              >
                                <div className="flex justify-between items-start">
                                  <h4 className="font-medium">{item.title}</h4>

                                </div>
                                <p className="text-xs text-muted-foreground">
                                  {formatDistanceToNow(new Date(item.publishedAt), { addSuffix: true })}
                                </p>
                              </div>
                            ))}
                          </div>
                        )}
                      </ScrollArea>
                    </div>

                    {/* Item Content */}
                    <div className="w-full md:w-1/2">
                      {selectedItem ? (
                        <div className="h-full flex flex-col">
                          <div className="flex justify-between items-start mb-2">
                            <h3 className="text-lg font-semibold">{selectedItem.title}</h3>
                            <a
                              href={selectedItem.link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center text-blue-500 hover:text-blue-700"
                            >
                              <ExternalLink className="h-4 w-4 mr-1" />
                              Open
                            </a>
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">
                            Published {formatDistanceToNow(new Date(selectedItem.publishedAt), { addSuffix: true })}
                          </p>
                          <Separator className="mb-4" />
                          <ScrollArea className="flex-grow h-[calc(100vh-450px)]">
                            {isLoading ? (
                              <p>Loading content...</p>
                            ) : (
                              <div
                                className="prose dark:prose-invert max-w-none"
                                dangerouslySetInnerHTML={{
                                  __html: selectedItem.scrapedContent || selectedItem.content || 'No content available'
                                }}
                              />
                            )}
                          </ScrollArea>
                        </div>
                      ) : (
                        <p className="text-muted-foreground">Select an item to view its content</p>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">Select a feed or add a new one</p>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="add" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Add New RSS Feed</CardTitle>
              <CardDescription>
                Add a new RSS feed to track news and updates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                  {error}
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="feed-name">Feed Name</Label>
                <Input
                  id="feed-name"
                  placeholder="Google Alerts - Social Security"
                  value={newFeedName}
                  onChange={(e) => setNewFeedName(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="feed-url">Feed URL</Label>
                <Input
                  id="feed-url"
                  placeholder="https://www.google.com/alerts/feeds/..."
                  value={newFeedUrl}
                  onChange={(e) => setNewFeedUrl(e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Enter the RSS feed URL. For Google Alerts, go to your Google Alerts page, click the RSS icon next to an alert, and copy the URL.
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={addFeed}
                disabled={isLoading || !newFeedName || !newFeedUrl}
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                Add Feed
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
