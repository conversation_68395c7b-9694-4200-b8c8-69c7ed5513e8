// Modified version of server/index.ts with additional logging
import 'dotenv/config';
import express, { type Request, Response, NextFunction } from "express";
import passport from "passport";
import session from "express-session";
import { promises as fs } from 'fs';
import path from 'path';
import { exec } from 'child_process';

console.log('Starting server with additional logging...');

try {
  const app = express();
  console.log('Express app created');
  
  app.use(express.json());
  app.use(express.urlencoded({ extended: false }));
  console.log('Middleware set up');
  
  app.set("trust proxy", 1);
  
  // Simple session setup
  app.use(session({
    secret: process.env.SESSION_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: false,
      httpOnly: true,
      maxAge: 30 * 24 * 60 * 60 * 1000,
      sameSite: 'lax'
    }
  }));
  console.log('Session middleware set up');
  
  app.use(passport.initialize());
  app.use(passport.session());
  console.log('Passport initialized');
  
  // Simple route for testing
  app.get('/api/test', (req, res) => {
    res.json({ message: 'Server is running!' });
  });
  console.log('Test route set up');
  
  // Start the server
  const port = process.env.PORT ? parseInt(process.env.PORT) : 5004;
  app.listen(port, () => {
    console.log(`Test server running on port ${port}`);
  });
  
} catch (error) {
  console.error('Error starting server:', error);
}
