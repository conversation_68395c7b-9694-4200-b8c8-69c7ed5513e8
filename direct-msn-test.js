import axios from 'axios';
import { <PERSON><PERSON><PERSON> } from 'jsdom';

async function scrapeMSN(url) {
  try {
    console.log('Scraping URL:', url);
    
    // Extract the article ID from the URL
    const articleIdMatch = url.match(/\/ar-([A-Za-z0-9]+)/);
    if (!articleIdMatch || !articleIdMatch[1]) {
      console.log('Could not extract article ID from URL');
      return null;
    }
    
    const articleId = articleIdMatch[1];
    console.log('Article ID:', articleId);
    
    // Try different URL variations
    const urlVariations = [
      url, // Original URL
      `https://www.msn.com/en-us/news/other/ar-${articleId}?ocid=msnews`, // News format
      `https://www.msn.com/en-us/money/other/ar-${articleId}?ocid=finance-verthp-feeds`, // Money format
      `https://www.msn.com/en-us/money/retirement/ar-${articleId}?ocid=finance-verthp-feeds`, // Retirement format
    ];
    
    for (const currentUrl of urlVariations) {
      try {
        console.log(`Trying URL variation: ${currentUrl}`);
        
        const response = await axios.get(currentUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
          },
          timeout: 15000
        });
        
        console.log('Response received, status:', response.status);
        
        // Use JSDOM to parse the HTML
        const dom = new JSDOM(response.data);
        const document = dom.window.document;
        
        // Get the title
        const title = document.querySelector('h1')?.textContent.trim() || document.title.trim();
        console.log('Found title:', title);
        
        if (!title || title === 'MSN') {
          console.log('No valid title found, skipping this URL variation');
          continue;
        }
        
        // Get the author info
        const authorInfo = document.querySelector('.authorinfo')?.textContent.trim() || 
                          document.querySelector('.byline')?.textContent.trim() || 
                          document.querySelector('[data-author-name]')?.textContent.trim() ||
                          document.querySelector('[itemprop="author"]')?.textContent.trim();
        
        // Look for article content
        let content = '';
        
        // MSN-specific content selectors
        const contentSelectors = [
          '[data-testid="article-body-content"]',
          '[data-testid="articleBodyContent"]',
          '.articlebody',
          '.article-body',
          '.article-content',
          '.content-article',
          '.articlecontent',
          '#content-main',
          '.main-content',
          '.article-page-content',
          '.page-content',
          '.article-text',
          '.article',
          'article'
        ];
        
        for (const selector of contentSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            content = element.innerHTML || '';
            console.log(`Found content using selector: ${selector}`);
            break;
          }
        }
        
        // If no content found with specific selectors, try paragraphs
        if (!content || content.length < 100) {
          console.log('No content found with specific selectors, trying paragraphs');
          const paragraphs = Array.from(document.querySelectorAll('p'));
          const filteredParagraphs = paragraphs.filter(p => {
            const text = p.textContent;
            return text.length > 30 && !text.includes('ADVERTISEMENT');
          });
          
          content = filteredParagraphs.map(p => `<p>${p.innerHTML}</p>`).join('');
          console.log(`Found ${filteredParagraphs.length} paragraphs`);
        }
        
        // If we found content, return it
        if (content && content.length > 100) {
          let fullContent = '';
          if (title) fullContent += `<h1>${title}</h1>`;
          if (authorInfo) fullContent += `<p class="article-author">${authorInfo}</p>`;
          fullContent += content;
          
          console.log(`Found content with length: ${fullContent.length}`);
          console.log('Content preview:', fullContent.substring(0, 500));
          return fullContent;
        }
      } catch (error) {
        console.error(`Error with URL variation ${currentUrl}:`, error.message);
      }
    }
    
    console.log('All URL variations failed');
    return null;
  } catch (error) {
    console.error('Error scraping MSN article:', error.message);
    return null;
  }
}

// Test with the URL
const url = 'https://www.msn.com/en-us/money/retirement/social-security-checks-of-up-to-5-108-going-out-this-week/ar-AA1DKbYM';
scrapeMSN(url).then(content => {
  if (content) {
    console.log('Successfully scraped content!');
  } else {
    console.log('Failed to scrape content');
  }
});
