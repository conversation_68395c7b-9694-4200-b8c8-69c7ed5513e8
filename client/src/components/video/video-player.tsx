import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Video } from "@shared/schema";
import { useSettings } from "@/hooks/use-settings";
import { useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { CastButton } from "./cast-button";
import { initializeCastApi } from "@/lib/cast";

interface VideoPlayerProps {
  video: Video | null;
  onClose: () => void;
}

export function VideoPlayer({ video, onClose }: VideoPlayerProps) {
  const { settings } = useSettings();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    // Initialize Cast API
    initializeCastApi().catch(console.error);

    // Preferred playback setting removed - always use in-app player
  }, [video]);

  if (!video) {
    return null;
  }

  return (
    <Dialog open={!!video} onOpenChange={() => onClose()}>
      <DialogContent className="max-w-4xl p-0">
        <div className="relative">
          <div className="pb-[56.25%] h-0">
            <iframe
              ref={iframeRef}
              src={`https://www.youtube.com/embed/${video.id}?autoplay=1&enablejsapi=1&modestbranding=1&showinfo=0&fs=1&rel=0&playsinline=1`}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share; remote-playback"
              allowFullScreen
              className="absolute top-0 left-0 w-full h-full"
            />
          </div>
          <div className="absolute top-2 right-2 flex gap-2">
            {video && <CastButton video={video} variant="outline" size="icon" className="bg-background/80 backdrop-blur-sm" />}
            <Button
              variant="outline"
              size="icon"
              className="bg-background/80 backdrop-blur-sm"
              onClick={() => onClose()}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}