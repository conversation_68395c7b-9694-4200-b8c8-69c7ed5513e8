import requests
import json
import time

# Base URL for the API
BASE_URL = "http://localhost:5001/api"

def test_playback_sound_toggle():
    print("Starting API test for playback sound toggle...")
    
    # Step 1: Login to get a session cookie
    login_data = {
        "username": "kedar",
        "password": "1"
    }
    
    session = requests.Session()
    login_response = session.post(f"{BASE_URL}/login", json=login_data)
    
    if login_response.status_code != 200:
        print(f"<PERSON><PERSON> failed with status code: {login_response.status_code}")
        print(login_response.text)
        return False
    
    print("Login successful!")
    
    # Step 2: Get current settings
    settings_response = session.get(f"{BASE_URL}/settings")
    
    if settings_response.status_code != 200:
        print(f"Failed to get settings with status code: {settings_response.status_code}")
        print(settings_response.text)
        return False
    
    settings = settings_response.json()
    current_playback_sound = settings.get("playbackSound", True)
    
    print(f"Current playback sound setting: {current_playback_sound}")
    
    # Step 3: Toggle playback sound
    new_playback_sound = not current_playback_sound
    settings["playbackSound"] = new_playback_sound
    
    # Use PUT instead of POST
    update_response = session.put(f"{BASE_URL}/settings", json=settings)
    
    if update_response.status_code != 200:
        print(f"Failed to update settings with status code: {update_response.status_code}")
        print(update_response.text)
        return False
    
    print(f"Toggled playback sound to: {new_playback_sound}")
    
    # Step 4: Verify the change was saved
    settings_response = session.get(f"{BASE_URL}/settings")
    
    if settings_response.status_code != 200:
        print(f"Failed to get settings with status code: {settings_response.status_code}")
        print(settings_response.text)
        return False
    
    updated_settings = settings_response.json()
    updated_playback_sound = updated_settings.get("playbackSound", True)
    
    if updated_playback_sound == new_playback_sound:
        print("SUCCESS: Playback sound toggle was saved correctly!")
    else:
        print(f"FAILURE: Playback sound toggle was not saved correctly. Expected: {new_playback_sound}, Got: {updated_playback_sound}")
        return False
    
    # Step 5: Toggle back to original value
    settings["playbackSound"] = current_playback_sound
    
    # Use PUT instead of POST
    update_response = session.put(f"{BASE_URL}/settings", json=settings)
    
    if update_response.status_code != 200:
        print(f"Failed to reset settings with status code: {update_response.status_code}")
        print(update_response.text)
        return False
    
    print(f"Reset playback sound to original value: {current_playback_sound}")
    
    # Step 6: Verify the reset was saved
    settings_response = session.get(f"{BASE_URL}/settings")
    
    if settings_response.status_code != 200:
        print(f"Failed to get settings with status code: {settings_response.status_code}")
        print(settings_response.text)
        return False
    
    final_settings = settings_response.json()
    final_playback_sound = final_settings.get("playbackSound", True)
    
    if final_playback_sound == current_playback_sound:
        print("SUCCESS: Playback sound toggle was reset correctly!")
    else:
        print(f"FAILURE: Playback sound toggle was not reset correctly. Expected: {current_playback_sound}, Got: {final_playback_sound}")
        return False
    
    return True

if __name__ == "__main__":
    try:
        result = test_playback_sound_toggle()
        if result:
            print("Test completed successfully!")
        else:
            print("Test failed!")
    except Exception as e:
        print(f"An error occurred: {e}")
