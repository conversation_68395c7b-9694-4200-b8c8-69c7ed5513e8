/**
 * Migration to update financial scores for videos with Ollama analysis
 */
import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

export async function updateFinancialScores() {
  // Run the migration
  await runMigration();
}

async function runMigration() {
  console.log('Running migration: Updating financial scores for videos with Ollama analysis');

  try {
    // Get the current file's directory
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);

    // Create a connection to the database
    const dbPath = path.resolve(__dirname, '../../database.sqlite');
    console.log('Database path:', dbPath);

    // Check if the database file exists
    try {
      const fs = await import('fs');
      if (!fs.existsSync(dbPath)) {
        console.error('Database file does not exist at path:', dbPath);
        return 'Database file not found';
      }
    } catch (fsError) {
      console.error('Error checking database file:', fsError);
    }

    const db = new Database(dbPath);

    // Check if the youtube_videos table exists
    try {
      const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='youtube_videos'").get();
      if (!tableExists) {
        console.error('youtube_videos table does not exist in the database');
        return 'youtube_videos table not found';
      }
    } catch (tableError) {
      console.error('Error checking for youtube_videos table:', tableError);
      return `Error checking tables: ${tableError.message}`;
    }

    // First, get all videos with Ollama analysis but zero scores
    const videos = db.prepare(`
      SELECT id, ollama_priority_tag, financial_score
      FROM youtube_videos
      WHERE has_financial_analysis = 1
      AND financial_score = 0
      AND ollama_priority_tag IS NOT NULL
    `).all();

    console.log(`Found ${videos.length} videos with Ollama analysis but zero scores`);

    if (videos.length === 0) {
      console.log('No videos need score updates');
      return;
    }

    // Prepare the update statement
    const updateStmt = db.prepare(`
      UPDATE youtube_videos
      SET financial_score = ?
      WHERE id = ?
    `);

    // Start a transaction for better performance
    const transaction = db.transaction((videos) => {
      let updatedCount = 0;

      for (const video of videos) {
        // Set score based on priority tag
        let score = 0;

        if (video.ollama_priority_tag === 'Urgent - High Certainty') {
          score = 85;
        } else if (video.ollama_priority_tag === 'Anticipated') {
          score = 65;
        } else if (video.ollama_priority_tag === 'Doubtful') {
          score = 30;
        } else {
          score = 20; // Default for unknown tags
        }

        // Only update if we're setting a non-zero score
        if (score > 0) {
          updateStmt.run(score, video.id);
          updatedCount++;
        }
      }

      return updatedCount;
    });

    // Execute the transaction
    const updatedCount = transaction(videos);
    console.log(`Updated financial scores for ${updatedCount} videos`);

    return `Updated financial scores for ${updatedCount} videos`;
  } catch (error) {
    console.error('Error updating financial scores:', error);
    return `Error updating financial scores: ${error.message}`;
  }
}

// Self-executing function to run the migration
// This will run when the file is imported during server startup
// No need for a special check in ES modules
updateFinancialScores()
  .then((result) => {
    if (process.env.NODE_ENV !== 'production') {
      console.log('Financial scores migration completed:', result);
    }
  })
  .catch((error) => {
    console.error('Financial scores migration failed:', error);
  });
