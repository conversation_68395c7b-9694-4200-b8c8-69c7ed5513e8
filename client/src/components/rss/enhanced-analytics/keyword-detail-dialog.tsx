import React, { useState } from 'react';
import { RssFeedItem } from '@shared/schema';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { highlightFinancialTerms } from '../financial-term-highlighter';
import * as RechartsPrimitive from 'recharts';
import { ChartContainer } from '@/components/ui/chart';
import { ExternalLink, Eye, Save } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface KeywordDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  keyword: string;
  feedItems: RssFeedItem[];
  type: 'word' | 'topic';
  topicKeywords?: string[];
  onOpenArticle?: (articleId: number) => void;
  onSaveArticle?: (articleId: number) => void;
  onOpenInBrowser?: (articleId: number) => void;
}

interface KeywordContext {
  before: string;
  keyword: string;
  after: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  source: string;
  articleId: number;
}

interface LongTailKeyword {
  text: string;
  count: number;
  sentiment: 'positive' | 'negative' | 'neutral';
  contexts: {
    before: string;
    keyword: string;
    after: string;
    source: string;
    articleId: number;
  }[];
}

interface KeywordPhrase {
  phrase: string;
  count: number;
  sentiment: 'positive' | 'negative' | 'neutral';
}

export function KeywordDetailDialog({
  isOpen,
  onClose,
  keyword,
  feedItems,
  type,
  topicKeywords = [],
  onOpenArticle,
  onSaveArticle,
  onOpenInBrowser,
}: KeywordDetailDialogProps) {
  // State for active tab
  const [activeTab, setActiveTab] = useState('contexts');

  // Find all occurrences of the keyword in context
  const keywordContexts = React.useMemo(() => {
    if (!isOpen || !keyword || feedItems.length === 0) return [];

    const contexts: KeywordContext[] = [];
    const keywordLower = keyword.toLowerCase();
    const keywordsToSearch = type === 'topic'
      ? [keywordLower, ...topicKeywords.map(k => k.toLowerCase())]
      : [keywordLower];

    feedItems.forEach(item => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      // Combine title and content for analysis
      const allText = `${title} ${content} ${scrapedContent}`;

      // Strip HTML tags and formatting elements
      const strippedText = allText
        .replace(/<[^>]*>/g, ' ') // Remove HTML tags
        .replace(/&[a-z]+;/g, ' ') // Remove HTML entities like &nbsp;
        .replace(/style="[^"]*"/g, ' ') // Remove style attributes
        .replace(/class="[^"]*"/g, ' ') // Remove class attributes
        .replace(/data-[a-z-]+="[^"]*"/g, ' ') // Remove data attributes
        .replace(/span|div|source|color|white|important|style|class|data/g, ' '); // Remove common formatting words

      // Get sentiment analysis
      const { counts, highlightedText } = highlightFinancialTerms(strippedText);

      // Determine dominant sentiment for this article
      let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral';
      if (counts.positive > counts.negative && counts.positive > counts.neutral) {
        sentiment = 'positive';
      } else if (counts.negative > counts.positive && counts.negative > counts.neutral) {
        sentiment = 'negative';
      }

      // Search for all keywords
      keywordsToSearch.forEach(searchKeyword => {
        // Convert to lowercase for case-insensitive search
        const textLower = strippedText.toLowerCase();
        let startIndex = 0;

        while (startIndex < textLower.length) {
          const keywordIndex = textLower.indexOf(searchKeyword, startIndex);
          if (keywordIndex === -1) break;

          // Extract context (up to 50 characters before and after)
          const contextStart = Math.max(0, keywordIndex - 50);
          const contextEnd = Math.min(textLower.length, keywordIndex + searchKeyword.length + 50);

          // Get the actual text with original casing
          const beforeText = strippedText.substring(contextStart, keywordIndex);
          const keywordText = strippedText.substring(keywordIndex, keywordIndex + searchKeyword.length);
          const afterText = strippedText.substring(keywordIndex + searchKeyword.length, contextEnd);

          // Clean up the title to use as source
          const cleanTitle = (item.title || 'Untitled')
            .replace(/<[^>]*>/g, '') // Remove HTML tags
            .replace(/&[a-z]+;/g, '') // Remove HTML entities
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();

          contexts.push({
            before: beforeText,
            keyword: keywordText,
            after: afterText,
            sentiment,
            source: cleanTitle,
            articleId: item.id
          });

          // Move past this occurrence
          startIndex = keywordIndex + searchKeyword.length;
        }
      });
    });

    return contexts;
  }, [isOpen, keyword, feedItems, type, topicKeywords]);

  // Group by sentiment for statistics
  const stats = React.useMemo(() => {
    const total = keywordContexts.length;
    const positive = keywordContexts.filter(ctx => ctx.sentiment === 'positive').length;
    const negative = keywordContexts.filter(ctx => ctx.sentiment === 'negative').length;
    const neutral = keywordContexts.filter(ctx => ctx.sentiment === 'neutral').length;

    return { total, positive, negative, neutral };
  }, [keywordContexts]);

  // Extract long-tail keywords
  const longTailKeywords = React.useMemo(() => {
    if (!isOpen || keywordContexts.length === 0) return [];

    const keywordMap = new Map<string, LongTailKeyword>();
    const keywordLower = keyword.toLowerCase();

    // Process each context to extract phrases
    keywordContexts.forEach(context => {
      // Get the full text
      const fullText = `${context.before} ${context.keyword} ${context.after}`;

      // Clean the text first
      const cleanText = fullText
        .replace(/<[^>]*>/g, ' ') // Remove HTML tags
        .replace(/&[a-z]+;/g, ' ') // Remove HTML entities
        .replace(/style="[^"]*"/g, ' ') // Remove style attributes
        .replace(/class="[^"]*"/g, ' ') // Remove class attributes
        .replace(/data-[a-z-]+="[^"]*"/g, ' ') // Remove data attributes
        .replace(/span|div|source|color|white|important|style|class|data/g, ' '); // Remove common formatting words

      // Split into words and create n-grams (2-4 word phrases)
      const words = cleanText.split(/\s+/)
        .filter(w =>
          w.length > 0 &&
          // Exclude common formatting words and HTML-related terms
          !['span', 'div', 'color', 'white', 'important', 'style', 'class', 'data', 'source'].includes(w.toLowerCase()) &&
          !w.includes('span') &&
          !w.includes('div') &&
          !w.includes('style') &&
          !w.includes('class') &&
          !w.includes('data') &&
          !/^\d+$/.test(w) && // Exclude words that are just numbers
          !/^[a-f0-9]{6,}$/i.test(w) // Exclude what might be hex colors or hashes
        );

      // Create n-grams containing the keyword
      for (let i = 0; i < words.length; i++) {
        // Skip if this word isn't our keyword
        if (!words[i].toLowerCase().includes(keywordLower)) continue;

        // Create phrases of different lengths
        for (let n = 2; n <= 4; n++) {
          // Ensure we have enough words for this n-gram
          if (i - (n-1) >= 0) {
            // Create phrase with keyword at the end
            const phraseWords = words.slice(i-(n-1), i+1);
            const phrase = phraseWords.join(' ');

            // Skip if phrase is too short or just the keyword
            if (phrase.length <= keywordLower.length || phrase.toLowerCase() === keywordLower) continue;

            // Skip if phrase contains HTML or formatting-related terms
            if (
              phrase.toLowerCase().includes('span') ||
              phrase.toLowerCase().includes('div') ||
              phrase.toLowerCase().includes('style') ||
              phrase.toLowerCase().includes('class') ||
              phrase.toLowerCase().includes('data') ||
              phrase.toLowerCase().includes('color') ||
              phrase.toLowerCase().includes('white') ||
              phrase.toLowerCase().includes('important') ||
              phrase.toLowerCase().includes('source')
            ) continue;

            // Add to map
            if (!keywordMap.has(phrase)) {
              keywordMap.set(phrase, {
                text: phrase,
                count: 0,
                sentiment: context.sentiment,
                contexts: []
              });
            }

            const entry = keywordMap.get(phrase)!;
            entry.count++;
            // Use the already cleaned source from the context
            entry.contexts.push({
              before: context.before,
              keyword: context.keyword,
              after: context.after,
              source: context.source,
              articleId: context.articleId
            });
          }

          // Ensure we have enough words for this n-gram
          if (i + n <= words.length) {
            // Create phrase with keyword at the beginning
            const phraseWords = words.slice(i, i+n);
            const phrase = phraseWords.join(' ');

            // Skip if phrase is too short or just the keyword
            if (phrase.length <= keywordLower.length || phrase.toLowerCase() === keywordLower) continue;

            // Skip if phrase contains HTML or formatting-related terms
            if (
              phrase.toLowerCase().includes('span') ||
              phrase.toLowerCase().includes('div') ||
              phrase.toLowerCase().includes('style') ||
              phrase.toLowerCase().includes('class') ||
              phrase.toLowerCase().includes('data') ||
              phrase.toLowerCase().includes('color') ||
              phrase.toLowerCase().includes('white') ||
              phrase.toLowerCase().includes('important') ||
              phrase.toLowerCase().includes('source')
            ) continue;

            // Add to map
            if (!keywordMap.has(phrase)) {
              keywordMap.set(phrase, {
                text: phrase,
                count: 0,
                sentiment: context.sentiment,
                contexts: []
              });
            }

            const entry = keywordMap.get(phrase)!;
            entry.count++;
            // Use the already cleaned source from the context
            entry.contexts.push({
              before: context.before,
              keyword: context.keyword,
              after: context.after,
              source: context.source,
              articleId: context.articleId
            });
          }
        }
      }
    });

    // Convert map to array and sort by count
    return Array.from(keywordMap.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 30); // Limit to top 30
  }, [isOpen, keywordContexts, keyword]);

  // Prepare chart data for long-tail keywords
  const chartData = React.useMemo(() => {
    return longTailKeywords.slice(0, 15).map(item => ({
      phrase: item.text,
      count: item.count,
      sentiment: item.sentiment
    }));
  }, [longTailKeywords]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[800px] h-[80vh] overflow-y-auto flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-white">
            {type === 'topic' ? 'Topic Analysis' : 'Keyword Analysis'}: "{keyword}"
          </DialogTitle>
          <DialogDescription className="text-white">
            Found {keywordContexts.length} occurrences across {new Set(keywordContexts.map(ctx => ctx.articleId)).size} articles
          </DialogDescription>
        </DialogHeader>

        <div className="flex gap-3 my-2">
          <Badge variant="outline" className="bg-green-500/20 text-green-400">
            Positive: {stats.positive}
          </Badge>
          <Badge variant="outline" className="bg-red-500/20 text-red-400">
            Negative: {stats.negative}
          </Badge>
          <Badge variant="outline" className="bg-gray-500/20 text-gray-400">
            Neutral: {stats.neutral}
          </Badge>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="longtail">Long-tail Keywords</TabsTrigger>
            <TabsTrigger value="contexts">Contexts</TabsTrigger>
          </TabsList>

          <TabsContent value="longtail" className="flex-1 flex flex-col">
            {longTailKeywords.length > 0 ? (
              <>
                <div className="mb-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm text-white">Long-tail Keyword Distribution</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="h-[250px] w-full">
                        <ChartContainer
                          className="h-full"
                          style={{
                            '--color-positive': 'var(--green-500)',
                            '--color-negative': 'var(--red-500)',
                            '--color-neutral': 'var(--gray-500)',
                          } as React.CSSProperties}
                          config={{}}
                        >
                          <RechartsPrimitive.BarChart
                            data={chartData}
                            layout="vertical"
                            margin={{ top: 10, right: 10, left: 120, bottom: 10 }}
                          >
                            <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" />
                            <RechartsPrimitive.XAxis type="number" />
                            <RechartsPrimitive.YAxis
                              type="category"
                              dataKey="phrase"
                              width={120}
                              tick={{ fontSize: 12 }}
                            />
                            <RechartsPrimitive.Tooltip />
                            <RechartsPrimitive.Bar
                              dataKey="count"
                              name="Occurrences"
                              fill={(entry) => {
                                if (entry.sentiment === 'positive') return 'var(--color-positive)';
                                if (entry.sentiment === 'negative') return 'var(--color-negative)';
                                return 'var(--color-neutral)';
                              }}
                            />
                          </RechartsPrimitive.BarChart>
                        </ChartContainer>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="flex-1 overflow-y-auto h-[300px]">
                  <div className="space-y-2 pr-4">
                    {longTailKeywords.map((item, index) => (
                      <div
                        key={index}
                        className={`p-3 border rounded-md hover:bg-primary/10 ${
                          item.sentiment === 'positive'
                            ? 'border-green-500/30 bg-green-500/5'
                            : item.sentiment === 'negative'
                              ? 'border-red-500/30 bg-red-500/5'
                              : 'border-gray-500/30 bg-gray-500/5'
                        }`}
                      >
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-white">{item.text}</span>
                          <Badge variant="outline" className="bg-primary/20">
                            {item.count} occurrences
                          </Badge>
                        </div>

                        {/* Show a sample context */}
                        {item.contexts.length > 0 && (
                          <div className="mt-2 text-sm text-muted-foreground">
                            <div className="flex justify-between items-center text-xs mb-1">
                              <div>Source: {item.contexts[0].source}</div>
                              {/* Only show buttons if we have article IDs */}
                              {item.contexts[0].articleId && (
                                <div className="flex gap-2">
                                  {onOpenArticle && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="h-6 px-2 text-xs"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        onOpenArticle(item.contexts[0].articleId);
                                      }}
                                    >
                                      <Eye className="h-3 w-3 mr-1" />
                                      Open
                                    </Button>
                                  )}
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="h-6 px-2 text-xs"
                                        onClick={(e) => e.stopPropagation()}
                                      >
                                        Actions
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>
                                      {onSaveArticle && (
                                        <DropdownMenuItem onClick={() => onSaveArticle(item.contexts[0].articleId)}>
                                          <Save className="h-4 w-4 mr-2" />
                                          Save to List
                                        </DropdownMenuItem>
                                      )}
                                      {onOpenInBrowser && (
                                        <DropdownMenuItem onClick={() => onOpenInBrowser(item.contexts[0].articleId)}>
                                          <ExternalLink className="h-4 w-4 mr-2" />
                                          Open in Browser
                                        </DropdownMenuItem>
                                      )}
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              )}
                            </div>
                            <div>
                              <span>...{item.contexts[0].before}</span>
                              <span className="font-bold text-primary">{item.contexts[0].keyword}</span>
                              <span>{item.contexts[0].after}...</span>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8 text-white">
                No long-tail keywords found for "{keyword}"
              </div>
            )}
          </TabsContent>

          <TabsContent value="contexts" className="flex-1">
            <div className="overflow-y-auto h-[500px]">
              <div className="space-y-4 pr-4">
                {keywordContexts.map((context, index) => (
                  <div
                    key={index}
                    className={`p-3 border rounded-md ${
                      context.sentiment === 'positive'
                        ? 'border-green-500/30 bg-green-500/5'
                        : context.sentiment === 'negative'
                          ? 'border-red-500/30 bg-red-500/5'
                          : 'border-gray-500/30 bg-gray-500/5'
                    }`}
                  >
                    <div className="flex justify-between items-center text-sm text-muted-foreground mb-1">
                      <div>Source: {context.source}</div>
                      <div className="flex gap-2">
                        {onOpenArticle && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 px-2 text-xs"
                            onClick={() => onOpenArticle(context.articleId)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            Open Article
                          </Button>
                        )}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                              Actions
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            {onSaveArticle && (
                              <DropdownMenuItem onClick={() => onSaveArticle(context.articleId)}>
                                <Save className="h-4 w-4 mr-2" />
                                Save to List
                              </DropdownMenuItem>
                            )}
                            {onOpenInBrowser && (
                              <DropdownMenuItem onClick={() => onOpenInBrowser(context.articleId)}>
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Open in Browser
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    <div className="text-white">
                      <span className="text-muted-foreground">...{context.before}</span>
                      <span className="font-bold text-primary">{context.keyword}</span>
                      <span className="text-muted-foreground">{context.after}...</span>
                    </div>
                  </div>
                ))}

                {keywordContexts.length === 0 && (
                  <div className="text-center py-8 text-white">
                    No occurrences found for "{keyword}"
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
