import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { useWorkflow } from '../workflow-state';
import {
  Lightbulb,
  Plus,
  Save,
  Edit,
  Trash2,
  Copy,
  Heart,
  Zap,
  BookOpen,
  Shield,
  Target,
  Mic,
  Users,
  X
} from 'lucide-react';

interface Strategy {
  id: string;
  title: string;
  description: string;
  tone: string;
  targetAudience: string;
  samplePhrases: string[];
  category: 'tone' | 'structure' | 'engagement' | 'persuasion' | 'custom';
  dateCreated: Date;
  lastUsed?: Date;
}

interface StrategiesModuleProps {
  strategies: Strategy[];
  setStrategies: React.Dispatch<React.SetStateAction<Strategy[]>>;
  toast: any;
}

const StrategiesModuleRedesigned: React.FC<StrategiesModuleProps> = ({
  strategies,
  setStrategies,
  toast
}) => {
  const { updateStepData, markStepComplete } = useWorkflow();
  const [isCreatingStrategy, setIsCreatingStrategy] = useState(false);
  const [editingStrategy, setEditingStrategy] = useState<Strategy | null>(null);
  const [strategyForm, setStrategyForm] = useState({
    title: '',
    description: '',
    tone: '',
    targetAudience: '',
    samplePhrases: [] as string[],
    category: 'custom' as Strategy['category'],
    newPhrase: ''
  });
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [selectedStrategies, setSelectedStrategies] = useState<string[]>([]);

  // Load strategies from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('txtpro_strategies');
    if (saved) {
      setStrategies(JSON.parse(saved));
    } else {
      // Initialize with default strategies
      const defaultStrategies = getDefaultStrategies();
      setStrategies(defaultStrategies);
      localStorage.setItem('txtpro_strategies', JSON.stringify(defaultStrategies));
    }
  }, [setStrategies]);

  // Save strategies to localStorage
  useEffect(() => {
    localStorage.setItem('txtpro_strategies', JSON.stringify(strategies));
  }, [strategies]);

  const getDefaultStrategies = (): Strategy[] => [
    {
      id: 'empathetic-senior',
      title: 'Empathetic Senior Voice',
      description: 'Warm, understanding tone that resonates with senior audience concerns',
      tone: 'Compassionate, respectful, reassuring',
      targetAudience: 'Seniors on Social Security',
      samplePhrases: [
        'I understand your concerns about your benefits',
        'You\'ve worked hard for this, and you deserve clarity',
        'Let me walk you through this step by step',
        'Many of you have asked about this, so let\'s address it together'
      ],
      category: 'tone',
      dateCreated: new Date()
    },
    {
      id: 'urgent-breaking',
      title: 'Urgent Breaking News',
      description: 'High-energy approach for time-sensitive benefit updates',
      tone: 'Urgent, authoritative, action-oriented',
      targetAudience: 'All Social Security recipients',
      samplePhrases: [
        'BREAKING: This changes everything for Social Security recipients',
        'You need to know this NOW before it\'s too late',
        'This is happening faster than expected',
        'Don\'t let this deadline catch you off guard'
      ],
      category: 'engagement',
      dateCreated: new Date()
    },
    {
      id: 'storytelling-facts',
      title: 'Storytelling + Facts',
      description: 'Blend personal stories with hard data for emotional connection',
      tone: 'Narrative, informative, relatable',
      targetAudience: 'General audience',
      samplePhrases: [
        'Let me tell you about Maria, a 67-year-old from Texas...',
        'Here\'s what the numbers actually show',
        'This reminds me of when my own mother faced this situation',
        'The data tells a clear story'
      ],
      category: 'structure',
      dateCreated: new Date()
    },
    {
      id: 'reassuring-fixed-income',
      title: 'Reassuring for Fixed Income',
      description: 'Calm, stable tone for viewers worried about financial security',
      tone: 'Steady, reassuring, protective',
      targetAudience: 'Fixed income seniors',
      samplePhrases: [
        'Your benefits are protected',
        'This won\'t affect your current payments',
        'You can rest easy knowing that...',
        'Let me put your mind at ease about this'
      ],
      category: 'tone',
      dateCreated: new Date()
    }
  ];

  const categoryOptions = [
    { value: 'tone', label: 'Tone & Voice', icon: Mic },
    { value: 'structure', label: 'Content Structure', icon: BookOpen },
    { value: 'engagement', label: 'Engagement Tactics', icon: Zap },
    { value: 'persuasion', label: 'Persuasion Methods', icon: Target },
    { value: 'custom', label: 'Custom Strategies', icon: Lightbulb }
  ];

  const handleCreateStrategy = () => {
    if (!strategyForm.title.trim() || !strategyForm.description.trim()) {
      toast({
        title: "Validation Error",
        description: "Title and description are required",
        variant: "destructive"
      });
      return;
    }

    const strategy: Strategy = {
      id: editingStrategy?.id || `strategy_${Date.now()}`,
      title: strategyForm.title.trim(),
      description: strategyForm.description.trim(),
      tone: strategyForm.tone.trim(),
      targetAudience: strategyForm.targetAudience.trim(),
      samplePhrases: strategyForm.samplePhrases,
      category: strategyForm.category,
      dateCreated: editingStrategy?.dateCreated || new Date(),
      lastUsed: editingStrategy?.lastUsed
    };

    if (editingStrategy) {
      setStrategies(prev => prev.map(s => s.id === strategy.id ? strategy : s));
      toast({
        title: "Strategy Updated",
        description: `"${strategy.title}" has been updated`
      });
    } else {
      setStrategies(prev => [...prev, strategy]);
      toast({
        title: "Strategy Created",
        description: `"${strategy.title}" has been added to your library`
      });
    }

    resetForm();
  };

  const resetForm = () => {
    setStrategyForm({
      title: '',
      description: '',
      tone: '',
      targetAudience: '',
      samplePhrases: [],
      category: 'custom',
      newPhrase: ''
    });
    setIsCreatingStrategy(false);
    setEditingStrategy(null);
  };

  const handleEditStrategy = (strategy: Strategy) => {
    setStrategyForm({
      title: strategy.title,
      description: strategy.description,
      tone: strategy.tone,
      targetAudience: strategy.targetAudience,
      samplePhrases: [...strategy.samplePhrases],
      category: strategy.category,
      newPhrase: ''
    });
    setEditingStrategy(strategy);
    setIsCreatingStrategy(true);
  };

  const handleDeleteStrategy = (strategyId: string) => {
    setStrategies(prev => prev.filter(s => s.id !== strategyId));
    toast({
      title: "Strategy Deleted",
      description: "Strategy has been removed from your library"
    });
  };

  const handleCopyStrategy = (strategy: Strategy) => {
    const strategyText = `${strategy.title}\n\n${strategy.description}\n\nTone: ${strategy.tone}\n\nSample phrases:\n${strategy.samplePhrases.map(phrase => `- ${phrase}`).join('\n')}`;
    navigator.clipboard.writeText(strategyText);
    toast({
      title: "Strategy Copied",
      description: "Strategy details have been copied to clipboard"
    });
  };

  const handleSelectStrategy = (strategyId: string) => {
    setSelectedStrategies(prev => {
      const newSelection = prev.includes(strategyId) 
        ? prev.filter(id => id !== strategyId)
        : [...prev, strategyId];
      
      // Update workflow state
      const selectedStrategyData = strategies.filter(s => newSelection.includes(s.id));
      updateStepData('strategy-selection', { selectedStrategies: selectedStrategyData });
      if (newSelection.length > 0) {
        markStepComplete('strategy-selection');
      }
      
      return newSelection;
    });
  };

  const addSamplePhrase = () => {
    if (strategyForm.newPhrase.trim() && !strategyForm.samplePhrases.includes(strategyForm.newPhrase.trim())) {
      setStrategyForm(prev => ({
        ...prev,
        samplePhrases: [...prev.samplePhrases, prev.newPhrase.trim()],
        newPhrase: ''
      }));
    }
  };

  const removeSamplePhrase = (phraseToRemove: string) => {
    setStrategyForm(prev => ({
      ...prev,
      samplePhrases: prev.samplePhrases.filter(phrase => phrase !== phraseToRemove)
    }));
  };

  const filteredStrategies = strategies.filter(strategy => {
    if (filterCategory === 'all') return true;
    return strategy.category === filterCategory;
  });

  return (
    <div className="space-y-6">
      {/* Value Proposition Header */}
      <Card className="border-purple-200 bg-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-purple-600" />
            Writing Strategies & Approaches
          </CardTitle>
          <CardDescription className="text-purple-700">
            💡 <strong>Why use this:</strong> Define your writing voice and approach. Create consistent, effective strategies that you can apply across multiple scripts for better audience connection.
          </CardDescription>
          <div className="flex gap-2 mt-3">
            <Badge variant="secondary" className="text-xs">
              🎭 Consistent voice & tone
            </Badge>
            <Badge variant="secondary" className="text-xs">
              🎯 Audience-specific approaches
            </Badge>
            <Badge variant="secondary" className="text-xs">
              📝 Reusable writing templates
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Your Writing Strategies ({strategies.length})</CardTitle>
              <CardDescription>
                Select strategies to apply to your script generation
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button onClick={() => setIsCreatingStrategy(true)} className="bg-purple-600 hover:bg-purple-700">
                <Plus className="h-4 w-4 mr-2" />
                Create Strategy
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Quick Strategy Templates */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-purple-600" />
              <Label className="font-medium">Quick Strategy Templates</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                { icon: Heart, title: 'Empathetic', desc: 'Warm, understanding tone', category: 'tone' },
                { icon: Zap, title: 'Urgent', desc: 'High-energy, action-oriented', category: 'engagement' },
                { icon: Shield, title: 'Reassuring', desc: 'Calm, protective approach', category: 'tone' },
                { icon: Users, title: 'Community', desc: 'Inclusive, collective voice', category: 'engagement' }
              ].map(template => {
                const IconComponent = template.icon;
                return (
                  <Card 
                    key={template.title}
                    className="p-4 hover:shadow-md transition-shadow cursor-pointer border-purple-200 hover:bg-purple-50"
                    onClick={() => {
                      setStrategyForm(prev => ({ 
                        ...prev, 
                        title: `${template.title} Approach`,
                        category: template.category as Strategy['category']
                      }));
                      setIsCreatingStrategy(true);
                    }}
                  >
                    <div className="text-center">
                      <IconComponent className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                      <h4 className="font-medium text-purple-800">{template.title}</h4>
                      <p className="text-xs text-purple-600">{template.desc}</p>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Strategy Creation Form */}
      {isCreatingStrategy && (
        <Card className="border-purple-200 bg-purple-50">
          <CardHeader>
            <CardTitle className="text-lg text-purple-800">
              {editingStrategy ? 'Edit Strategy' : 'Create New Strategy'}
            </CardTitle>
            <CardDescription className="text-purple-700">
              Define your writing approach and tone for consistent script creation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="strategyTitle">Strategy Title *</Label>
                <Input
                  id="strategyTitle"
                  placeholder="e.g., Empathetic Senior Voice"
                  value={strategyForm.title}
                  onChange={(e) => setStrategyForm(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="strategyCategory">Category</Label>
                <Select
                  value={strategyForm.category}
                  onValueChange={(value: Strategy['category']) => setStrategyForm(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="strategyDescription">Description *</Label>
              <Textarea
                id="strategyDescription"
                placeholder="Describe when and how to use this strategy..."
                value={strategyForm.description}
                onChange={(e) => setStrategyForm(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="strategyTone">Tone & Voice</Label>
                <Input
                  id="strategyTone"
                  placeholder="e.g., Warm, authoritative, reassuring"
                  value={strategyForm.tone}
                  onChange={(e) => setStrategyForm(prev => ({ ...prev, tone: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="targetAudience">Target Audience</Label>
                <Input
                  id="targetAudience"
                  placeholder="e.g., Seniors on Social Security"
                  value={strategyForm.targetAudience}
                  onChange={(e) => setStrategyForm(prev => ({ ...prev, targetAudience: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Sample Phrases</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Add a sample phrase..."
                  value={strategyForm.newPhrase}
                  onChange={(e) => setStrategyForm(prev => ({ ...prev, newPhrase: e.target.value }))}
                  onKeyPress={(e) => e.key === 'Enter' && addSamplePhrase()}
                />
                <Button onClick={addSamplePhrase} variant="outline" size="sm">
                  Add
                </Button>
              </div>
              <div className="space-y-1 mt-2">
                {strategyForm.samplePhrases.map((phrase, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-white rounded border">
                    <span className="flex-1 text-sm">"{phrase}"</span>
                    <Button onClick={() => removeSamplePhrase(phrase)} variant="ghost" size="sm">
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              <Button onClick={handleCreateStrategy} className="bg-purple-600 hover:bg-purple-700">
                <Save className="h-4 w-4 mr-2" />
                {editingStrategy ? 'Update Strategy' : 'Save Strategy'}
              </Button>
              <Button onClick={resetForm} variant="outline">
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Strategy Library */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Strategy Library</CardTitle>
            <div className="flex gap-2">
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categoryOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedStrategies.length > 0 && (
                <Badge variant="secondary" className="px-3 py-1">
                  {selectedStrategies.length} selected for workflow
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {filteredStrategies.map(strategy => {
              const categoryOption = categoryOptions.find(opt => opt.value === strategy.category);
              const IconComponent = categoryOption?.icon || Lightbulb;
              const isSelected = selectedStrategies.includes(strategy.id);

              return (
                <Card
                  key={strategy.id}
                  className={`hover:shadow-md transition-shadow ${isSelected ? 'ring-2 ring-purple-500 bg-purple-50' : ''}`}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4 text-muted-foreground" />
                        <CardTitle className="text-base">{strategy.title}</CardTitle>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          onClick={() => handleSelectStrategy(strategy.id)}
                          variant={isSelected ? "default" : "outline"}
                          size="sm"
                          className={isSelected ? "bg-purple-600 hover:bg-purple-700" : ""}
                        >
                          {isSelected ? 'Selected' : 'Select'}
                        </Button>
                        <Button onClick={() => handleCopyStrategy(strategy)} variant="ghost" size="sm">
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button onClick={() => handleEditStrategy(strategy)} variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button onClick={() => handleDeleteStrategy(strategy.id)} variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <Badge variant="outline">
                      {categoryOption?.label}
                    </Badge>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm mb-3">{strategy.description}</p>

                    {strategy.tone && (
                      <div className="mb-3">
                        <Label className="text-xs font-medium text-muted-foreground">TONE</Label>
                        <p className="text-sm">{strategy.tone}</p>
                      </div>
                    )}

                    {strategy.targetAudience && (
                      <div className="mb-3">
                        <Label className="text-xs font-medium text-muted-foreground">TARGET AUDIENCE</Label>
                        <p className="text-sm">{strategy.targetAudience}</p>
                      </div>
                    )}

                    {strategy.samplePhrases.length > 0 && (
                      <div>
                        <Label className="text-xs font-medium text-muted-foreground">SAMPLE PHRASES</Label>
                        <div className="space-y-1 mt-1">
                          {strategy.samplePhrases.slice(0, 2).map((phrase, index) => (
                            <p key={index} className="text-xs italic text-blue-700 bg-blue-50 p-1 rounded">
                              "{phrase}"
                            </p>
                          ))}
                          {strategy.samplePhrases.length > 2 && (
                            <p className="text-xs text-muted-foreground">
                              +{strategy.samplePhrases.length - 2} more phrases
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {filteredStrategies.length === 0 && (
            <div className="text-center py-12">
              <Lightbulb className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Strategies Found</h3>
              <p className="text-muted-foreground mb-4">
                {filterCategory !== 'all'
                  ? 'No strategies match your filter criteria'
                  : 'Create your first writing strategy to get started'
                }
              </p>
              {filterCategory === 'all' && (
                <Button onClick={() => setIsCreatingStrategy(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Strategy
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default StrategiesModuleRedesigned;
