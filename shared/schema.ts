import { pgTable, text, serial, integer, boolean, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const adminSettings = pgTable("admin_settings", {
  id: serial("id").primaryKey(),
  serverPort: integer("server_port").notNull().default(5001),
  localhostOnly: boolean("localhost_only").notNull().default(false),
  lastUpdated: timestamp("last_updated").defaultNow(),
});

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  isAdmin: boolean("is_admin").notNull().default(false),
  darkMode: boolean("dark_mode").notNull().default(true),
  searchKeywords: text("search_keywords").array().notNull().default([]),
  minViewsPerHour: integer("min_views_per_hour").notNull().default(10),
  removeDuplicates: boolean("remove_duplicates").notNull().default(true),
  excludeWords: text("exclude_words").array().notNull().default([]),
  autoRefreshInterval: integer("auto_refresh_interval").notNull().default(0),
  // preferredPlayback setting removed as it was not functioning properly
  lastRefreshTime: timestamp("last_refresh_time"),
  parallelApiCalls: boolean("parallel_api_calls").notNull().default(true),
  watchedVideos: text("watched_videos").array().notNull().default([]),
  useInAppPlayer: boolean("use_in_app_player").notNull().default(true),
  previewSound: boolean("preview_sound").notNull().default(false),
  playbackSound: boolean("playback_sound").notNull().default(true),
  activeKeywordGroupId: integer("active_keyword_group_id"),
  // Default sort and filter settings removed - we use localStorage instead
  homePage: text("home_page").notNull().default("trendy"),
  openRouterModel: text("openrouter_model").default("google/gemini-2.0-flash-exp:free"),
  openRouterAnalysisPrompt: text("openrouter_analysis_prompt"),
  selectedPromptId: integer("selected_prompt_id"),
  autoAnalyzeOnRefresh: boolean("auto_analyze_on_refresh").notNull().default(true),
});

export const videos = pgTable("videos", {
  id: text("id").primaryKey(),
  title: text("title").notNull(),
  thumbnail: text("thumbnail").notNull(),
  channelTitle: text("channel_title").notNull(),
  viewCount: integer("view_count").notNull(),
  publishedAt: timestamp("published_at").notNull(),
  userId: integer("user_id").notNull(),
  keywordGroupId: integer("keyword_group_id"),
});

export const keywordGroups = pgTable("keyword_groups", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  name: text("name").notNull(),
  keywords: text("keywords").array().notNull(),
  excludeWords: text("exclude_words").array().notNull().default([]),
  lastRefreshTime: timestamp("last_refresh_time"),
  displayOrder: integer("display_order").notNull().default(0),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const userSettingsSchema = createInsertSchema(users).omit({
  id: true,
  username: true,
  password: true,
});

export const keywordGroupSchema = createInsertSchema(keywordGroups);

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type Video = typeof videos.$inferSelect;
export type UserSettings = z.infer<typeof userSettingsSchema>;
export type KeywordGroup = typeof keywordGroups.$inferSelect;
export type InsertKeywordGroup = z.infer<typeof keywordGroupSchema>;

// YouTube interfaces
export interface YoutubeChannel {
  id: number;
  userId: number;
  channelId: string;
  channelTitle: string;
  channelUrl: string;
  thumbnail?: string;
  description?: string;
  subscriberCount?: number;
  lastRefreshTime?: Date;
  displayOrder: number;
  videoLimit?: number; // Number of videos to fetch per channel
}

export interface OpenRouterPrompt {
  id: number;
  userId: number;
  name: string;
  promptText: string;
  isDefault: boolean;
  createdAt?: Date;
}

export interface OllamaPrompt {
  id: number;
  userId: number;
  name: string;
  promptText: string;
  isDefault: boolean;
  createdAt?: Date;
}

// Define OpenRouter API key schemas
export interface OpenRouterApiKeyExport {
  name: string;
  token: string;
  is_active: boolean;
  is_default?: boolean;
}

export interface OpenRouterApiKeyImportResult {
  success: boolean;
  message: string;
  imported: number;
  failed: number;
  errors?: string[];
}

export interface OpenRouterApiKeyExportData {
  version: string;
  exported_at: string;
  keys: OpenRouterApiKeyExport[];
}

// RSS Feed interface
export interface RssFeed {
  id: number;
  userId: number;
  name: string;
  url: string;
  lastRefreshTime?: Date;
  createdAt: Date;
  displayOrder?: number;
}

// RSS Feed Item interface
export interface RssFeedItem {
  id: number;
  feedId: number;
  title: string;
  link: string;
  publishedAt: Date;
  content?: string;
  scrapedContent?: string;
  isRead: boolean;
  forceRefresh?: boolean; // Client-side only flag to force content refresh
  financialBenefitAmount?: number; // The detected financial benefit amount
  financialBenefitDescription?: string; // Description of the financial benefit
  financialBenefitType?: string; // Type of financial benefit (e.g., 'benefit', 'threshold', 'payment')
}

// Saved Article List interface
export interface SavedArticleList {
  id: number;
  userId: number;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  articleCount?: number; // Optional count of articles in the list (for UI display)
}

// Saved Article interface
export interface SavedArticle {
  id: number;
  listId: number;
  feedId: number;
  title: string;
  link: string;
  publishedAt: Date;
  content?: string;
  scrapedContent?: string;
  addedAt: Date;
}

export interface YoutubeVideo {
  id: string;
  title: string;
  thumbnail: string;
  channelId: string;
  channelTitle: string;
  viewCount: number;
  publishedAt: Date;
  publishedTimeText?: string; // Original relative time text from YouTube (e.g., "2 days ago")
  userId: number;
  description?: string;
  duration?: string;
  transcription?: string;
  hasTranscription?: boolean;
  isSimulatedTranscription?: boolean;
  isUnplayable?: boolean; // Flag for unplayable ended live streams
  statistics?: {
    viewCount: string;
    likeCount?: string;
    favoriteCount?: string;
    commentCount?: string;
  };
  contentDetails?: {
    duration?: string;
    dimension?: string;
    definition?: string;
    caption?: string;
    licensedContent?: boolean;
    projection?: string;
  };
  tags?: string[];
  // Views per hour for sorting
  vph?: number;
  // Financial analysis fields
  financialScore?: number;
  financialCategory?: string; // 'urgent', 'anticipated', 'doubtful'
  financialAmount?: string;
  financialTimeline?: string;
  financialRecipients?: string;
  financialSteps?: string;
  financialViralPotential?: string;
  financialSkepticism?: string;
  financialAnalysis?: string;
  financialTimestamps?: string;
  hasFinancialAnalysis?: boolean;
  // Ollama LLM analysis fields have been removed

  // OpenRouter API analysis fields
  openRouterBenefitAmounts?: string[];
  openRouterExpectedArrivalDate?: string;
  openRouterEligiblePeople?: string;
  openRouterProofOrSource?: string;
  openRouterActionsToClaim?: string;
  openRouterPriorityTag?: string; // 'high', 'medium', 'low', 'none'
  openRouterModelUsed?: string; // The name of the OpenRouter model used for analysis
  openRouterPromptName?: string; // The name of the prompt template used for analysis
  openRouterPrompt?: string; // The actual prompt text sent to OpenRouter
  openRouterSystemPrompt?: string; // The system prompt sent to OpenRouter
  openRouterReasonForPriority?: string;
  openRouterViralPotential?: string;
  openRouterRawData?: string; // The raw JSON response from OpenRouter
}

// TXT Tab interfaces
export interface TxtTranscript {
  id: number;
  userId: number;
  title: string;
  content: string;
  videoUrl?: string;
  sourceType: 'manual' | 'rss' | 'ytr' | 'realtime' | 'feed' | 'imported';
  sourceId?: string;
  createdAt: Date;
  updatedAt: Date;
  highlights?: TxtHighlight[];
}

export interface TxtHighlight {
  id: number;
  userId: number;
  transcriptId: number;
  startPos: number;
  endPos: number;
  color: string;
  note?: string;
  createdAt: Date;
}

export interface TxtPrompt {
  id: number;
  userId: number;
  name: string;
  content: string;
  category: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// TXT Tab API request/response types
export interface CreateTxtTranscriptRequest {
  title: string;
  content: string;
  videoUrl?: string;
  sourceType?: 'manual' | 'rss' | 'ytr' | 'realtime' | 'feed' | 'imported';
  sourceId?: string;
}

export interface UpdateTxtTranscriptRequest {
  title?: string;
  content?: string;
  videoUrl?: string;
}

export interface CreateTxtHighlightRequest {
  transcriptId: number;
  startPos: number;
  endPos: number;
  color: string;
  note?: string;
}

export interface CreateTxtPromptRequest {
  name: string;
  content: string;
  category?: string;
}

export interface UpdateTxtPromptRequest {
  name?: string;
  content?: string;
  category?: string;
}

// ChatGPT integration types
export interface ChatGptProcessRequest {
  transcriptId: number;
  promptId: number;
  customPrompt?: string;
}

export interface ChatGptProcessResponse {
  success: boolean;
  processedContent?: string;
  error?: string;
}