import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import ScriptEntryModule from './modules/script-entry-module';
import ChunkingModuleRedesigned from './modules/chunking-module-redesigned';
import HighlightingModuleRedesigned from './modules/highlighting-module-redesigned';
import StrategiesModuleRedesigned from './modules/strategies-module-redesigned';
import GenerationModule from './modules/generation-module';
import ContentStructuresModule from './modules/content-structures-module';
import CoreInfoModule from './modules/core-info-module';
import SuccessLandmarksModule from './modules/success-landmarks-module';
import PromptTemplatesModule from './modules/prompt-templates-module';
import ScriptGeneratorModule from './modules/script-generator-module';
import InstantScriptGenerator from './modules/instant-script-generator';
import { WorkflowProvider, useWorkflow } from './workflow-state';
import WorkflowProgress from './workflow-progress';
import {
  ChevronDown,
  ChevronRight,
  Plus,
  Save,
  Search,
  Tag,
  FileText,
  Highlighter,
  Lightbulb,
  Building2,
  Info,
  Mic,
  Bot,
  Copy,
  Edit,
  Trash2,
  DragHandleDots2Icon
} from 'lucide-react';

// Types for our data structures
interface Script {
  id: string;
  title: string;
  sourceVideoUrl?: string;
  transcript: string;
  tags: string[];
  dateAdded: Date;
  editHistory?: Array<{ date: Date; changes: string }>;
}

interface Chunk {
  id: string;
  name: string;
  description: string;
  content: string;
  purpose: 'opening-hook' | 'call-to-action' | 'information-drop' | 'summary' | 'other';
  tags: string[];
  sourceScriptId?: string;
}

interface Highlight {
  id: string;
  scriptId: string;
  startIndex: number;
  endIndex: number;
  color: 'red' | 'green' | 'blue' | 'yellow';
  note: string;
  analyticsNote?: string;
}

interface Strategy {
  id: string;
  title: string;
  description: string;
  samplePhrases: string[];
  tone: string;
}

interface ContentStructure {
  id: string;
  name: string;
  category: string;
  introFormat: string;
  mainInfoDrop: string;
  explanationBreakdown: string;
  engagementInsert: string;
  outroCTA: string;
}

interface CoreInfo {
  id: string;
  eventDate?: string;
  eligibilityCriteria?: string;
  actBillProposalName?: string;
  payDates?: string;
  programType?: string;
  sourceLink?: string;
  notes?: string;
}

interface NarrationStyle {
  id: string;
  landmarkName: string;
  description: string;
  speakerTone: string;
  commonPhrases: string[];
  timingRhythm: string;
}

const TxtProTabContent: React.FC = () => {
  const { toast } = useToast();
  const { workflowState } = useWorkflow();
  
  // State for all modules
  const [scripts, setScripts] = useState<Script[]>([]);
  const [chunks, setChunks] = useState<Chunk[]>([]);
  const [highlights, setHighlights] = useState<Highlight[]>([]);
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [contentStructures, setContentStructures] = useState<ContentStructure[]>([]);
  const [coreInfos, setCoreInfos] = useState<CoreInfo[]>([]);
  const [narrationStyles, setNarrationStyles] = useState<NarrationStyle[]>([]);
  
  // UI State
  const [activeModule, setActiveModule] = useState<string>('instant-generator');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedScript, setSelectedScript] = useState<Script | null>(null);
  
  // Module expansion states
  const [moduleStates, setModuleStates] = useState({
    scriptEntry: true,
    chunking: false,
    highlighting: false,
    strategies: false,
    structures: false,
    coreInfo: false,
    narration: false,
    generation: false
  });

  const toggleModule = (module: string) => {
    setModuleStates(prev => ({
      ...prev,
      [module]: !prev[module as keyof typeof prev]
    }));
  };

  // Load data from localStorage on component mount
  useEffect(() => {
    const loadData = () => {
      try {
        const savedScripts = localStorage.getItem('txtpro_scripts');
        const savedChunks = localStorage.getItem('txtpro_chunks');
        const savedHighlights = localStorage.getItem('txtpro_highlights');
        const savedStrategies = localStorage.getItem('txtpro_strategies');
        const savedStructures = localStorage.getItem('txtpro_structures');
        const savedCoreInfos = localStorage.getItem('txtpro_coreinfos');
        const savedNarrationStyles = localStorage.getItem('txtpro_narration');

        if (savedScripts) setScripts(JSON.parse(savedScripts));
        if (savedChunks) setChunks(JSON.parse(savedChunks));
        if (savedHighlights) setHighlights(JSON.parse(savedHighlights));
        if (savedStrategies) setStrategies(JSON.parse(savedStrategies));
        if (savedStructures) setContentStructures(JSON.parse(savedStructures));
        if (savedCoreInfos) setCoreInfos(JSON.parse(savedCoreInfos));
        if (savedNarrationStyles) setNarrationStyles(JSON.parse(savedNarrationStyles));
      } catch (error) {
        console.error('Error loading TXT PRO data:', error);
      }
    };

    loadData();
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    localStorage.setItem('txtpro_scripts', JSON.stringify(scripts));
  }, [scripts]);

  useEffect(() => {
    localStorage.setItem('txtpro_chunks', JSON.stringify(chunks));
  }, [chunks]);

  useEffect(() => {
    localStorage.setItem('txtpro_highlights', JSON.stringify(highlights));
  }, [highlights]);

  useEffect(() => {
    localStorage.setItem('txtpro_strategies', JSON.stringify(strategies));
  }, [strategies]);

  useEffect(() => {
    localStorage.setItem('txtpro_structures', JSON.stringify(contentStructures));
  }, [contentStructures]);

  useEffect(() => {
    localStorage.setItem('txtpro_coreinfos', JSON.stringify(coreInfos));
  }, [coreInfos]);

  useEffect(() => {
    localStorage.setItem('txtpro_narration', JSON.stringify(narrationStyles));
  }, [narrationStyles]);

  return (
    <div className="flex h-full bg-background">
      {/* Sidebar */}
      <div className="w-80 border-r bg-muted/30 p-4 overflow-y-auto">
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            <h2 className="font-semibold">TXT PRO</h2>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search scripts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>

          {/* Scripts List */}
          <ScrollArea className="h-96">
            <div className="space-y-2">
              {scripts
                .filter(script => 
                  script.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  script.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
                )
                .map(script => (
                  <Card 
                    key={script.id} 
                    className={`cursor-pointer transition-colors ${
                      selectedScript?.id === script.id ? 'bg-primary/10 border-primary' : 'hover:bg-muted/50'
                    }`}
                    onClick={() => setSelectedScript(script)}
                  >
                    <CardContent className="p-3">
                      <h4 className="font-medium text-sm">{script.title}</h4>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {script.tags.map(tag => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(script.dateAdded).toLocaleDateString()}
                      </p>
                    </CardContent>
                  </Card>
                ))}
            </div>
          </ScrollArea>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="bg-background p-2 rounded border">
              <div className="font-medium">{scripts.length}</div>
              <div className="text-muted-foreground">Scripts</div>
            </div>
            <div className="bg-background p-2 rounded border">
              <div className="font-medium">{chunks.length}</div>
              <div className="text-muted-foreground">Chunks</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 p-6 overflow-y-auto">
        <div className="max-w-6xl mx-auto space-y-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold">TXT PRO - Script Writing Workflow</h1>
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              New Script
            </Button>
          </div>

          <p className="text-muted-foreground">
            Create, analyze, and repurpose video text transcripts for high-performing scripts with modular content generation.
          </p>

          {/* Workflow Progress */}
          <WorkflowProgress />

          {/* Quick Generate Section */}
          <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-purple-900">⚡ Instant Script Generator</h3>
                  <p className="text-sm text-purple-700">
                    Generate high-performing video scripts in 10 seconds using proven algorithms with performance predictions
                  </p>
                </div>
                <Button
                  onClick={() => setActiveModule('instant-generator')}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  Generate Script Now
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Module Navigation */}
          <Tabs value={activeModule} onValueChange={setActiveModule} className="w-full">
            <TabsList className="grid w-full grid-cols-4 lg:grid-cols-11">
              <TabsTrigger value="instant-generator" className="text-xs">⚡ Instant</TabsTrigger>
              <TabsTrigger value="script-generator" className="text-xs">🎛️ Advanced</TabsTrigger>
              <TabsTrigger value="templates" className="text-xs">👑 Templates</TabsTrigger>
              <TabsTrigger value="script-entry" className="text-xs">Entry</TabsTrigger>
              <TabsTrigger value="chunking" className="text-xs">Chunks</TabsTrigger>
              <TabsTrigger value="highlighting" className="text-xs">Highlights</TabsTrigger>
              <TabsTrigger value="strategies" className="text-xs">Strategies</TabsTrigger>
              <TabsTrigger value="structures" className="text-xs">Structures</TabsTrigger>
              <TabsTrigger value="coreinfo" className="text-xs">Core Info</TabsTrigger>
              <TabsTrigger value="narration" className="text-xs">Narration</TabsTrigger>
              <TabsTrigger value="generation" className="text-xs">Generate</TabsTrigger>
            </TabsList>

            {/* Instant Script Generator */}
            <TabsContent value="instant-generator" className="space-y-4">
              <InstantScriptGenerator />
            </TabsContent>

            {/* Advanced Script Generator */}
            <TabsContent value="script-generator" className="space-y-4">
              <ScriptGeneratorModule />
            </TabsContent>

            {/* Professional Prompt Templates */}
            <TabsContent value="templates" className="space-y-4">
              <PromptTemplatesModule />
            </TabsContent>

            {/* Module 1: Script & Transcript Entry */}
            <TabsContent value="script-entry" className="space-y-4">
              <ScriptEntryModule
                scripts={scripts}
                setScripts={setScripts}
                selectedScript={selectedScript}
                setSelectedScript={setSelectedScript}
                toast={toast}
              />
            </TabsContent>

            {/* Module 2: Segment Chunking & Reusability */}
            <TabsContent value="chunking" className="space-y-4">
              <ChunkingModuleRedesigned
                scripts={scripts}
                chunks={chunks}
                setChunks={setChunks}
                selectedScript={selectedScript}
                toast={toast}
              />
            </TabsContent>

            {/* Module 3: Highlighting Watchtime Hotspots */}
            <TabsContent value="highlighting" className="space-y-4">
              <HighlightingModuleRedesigned
                scripts={scripts}
                highlights={highlights}
                setHighlights={setHighlights}
                selectedScript={selectedScript}
                toast={toast}
              />
            </TabsContent>

            {/* Module 4: Entertainment Strategies */}
            <TabsContent value="strategies" className="space-y-4">
              <StrategiesModuleRedesigned
                strategies={strategies}
                setStrategies={setStrategies}
                toast={toast}
              />
            </TabsContent>

            {/* Module 8: ChatGPT Script Generation */}
            <TabsContent value="generation" className="space-y-4">
              <GenerationModule
                scripts={scripts}
                chunks={chunks}
                highlights={highlights}
                strategies={strategies}
                selectedScript={selectedScript}
                toast={toast}
              />
            </TabsContent>

            {/* Placeholder modules for future implementation */}
            <TabsContent value="structures" className="space-y-4">
              <ContentStructuresModule />
            </TabsContent>

            <TabsContent value="coreinfo" className="space-y-4">
              <CoreInfoModule />
            </TabsContent>

            <TabsContent value="narration" className="space-y-4">
              <SuccessLandmarksModule />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

const TxtProTab: React.FC = () => {
  return (
    <WorkflowProvider>
      <TxtProTabContent />
    </WorkflowProvider>
  );
};

export default TxtProTab;
