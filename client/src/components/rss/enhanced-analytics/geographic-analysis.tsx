import React, { useMemo, useState, useEffect } from 'react';
import { RssFeedItem } from '@shared/schema';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import * as RechartsPrimitive from 'recharts';
import { ChartContainer } from '@/components/ui/chart';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { highlightFinancialTerms } from '../financial-term-highlighter';
import { KeywordDetailDialog } from './keyword-detail-dialog';

interface GeographicAnalysisProps {
  feedItems: RssFeedItem[];
  onOpenArticle?: (articleId: number) => void;
  onSaveArticle?: (articleId: number) => void;
  onOpenInBrowser?: (articleId: number) => void;
}

// US States with full names and abbreviations
const US_STATE_MAP: Record<string, string> = {
  'alabama': 'AL',
  'alaska': 'AK',
  'arizona': 'AZ',
  'arkansas': 'AR',
  'california': 'CA',
  'colorado': 'CO',
  'connecticut': 'CT',
  'delaware': 'DE',
  'florida': 'FL',
  'georgia': 'GA',
  'hawaii': 'HI',
  'idaho': 'ID',
  'illinois': 'IL',
  'indiana': 'IN',
  'iowa': 'IA',
  'kansas': 'KS',
  'kentucky': 'KY',
  'louisiana': 'LA',
  'maine': 'ME',
  'maryland': 'MD',
  'massachusetts': 'MA',
  'michigan': 'MI',
  'minnesota': 'MN',
  'mississippi': 'MS',
  'missouri': 'MO',
  'montana': 'MT',
  'nebraska': 'NE',
  'nevada': 'NV',
  'new hampshire': 'NH',
  'new jersey': 'NJ',
  'new mexico': 'NM',
  'new york': 'NY',
  'north carolina': 'NC',
  'north dakota': 'ND',
  'ohio': 'OH',
  'oklahoma': 'OK',
  'oregon': 'OR',
  'pennsylvania': 'PA',
  'rhode island': 'RI',
  'south carolina': 'SC',
  'south dakota': 'SD',
  'tennessee': 'TN',
  'texas': 'TX',
  'utah': 'UT',
  'vermont': 'VT',
  'virginia': 'VA',
  'washington': 'WA',
  'west virginia': 'WV',
  'wisconsin': 'WI',
  'wyoming': 'WY',
  'district of columbia': 'DC'
};

interface StateData {
  state: string;
  abbreviation: string;
  count: number;
  articles: number[];
  positiveCount: number;
  negativeCount: number;
  neutralCount: number;
}

export function GeographicAnalysis({
  feedItems,
  onOpenArticle,
  onSaveArticle,
  onOpenInBrowser
}: GeographicAnalysisProps) {
  const [selectedState, setSelectedState] = useState<string | null>(null);
  const [selectedKeyword, setSelectedKeyword] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Handle keyword click
  const handleKeywordClick = (keyword: string) => {
    setSelectedKeyword(keyword);
    setIsDialogOpen(true);
  };

  // Log when selectedState changes
  useEffect(() => {
    console.log('selectedState changed to:', selectedState);
  }, [selectedState]);

  // Analyze state mentions in feed items
  const stateData = useMemo(() => {
    if (!feedItems || feedItems.length === 0) return [];

    const stateMap = new Map<string, StateData>();

    // Initialize state data
    Object.entries(US_STATE_MAP).forEach(([stateName, abbr]) => {
      stateMap.set(stateName, {
        state: stateName,
        abbreviation: abbr,
        count: 0,
        articles: [],
        positiveCount: 0,
        negativeCount: 0,
        neutralCount: 0
      });
    });

    // Process each feed item
    feedItems.forEach(item => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      // Combine all text content
      const allText = `${title} ${content} ${scrapedContent}`;

      // First, strip HTML tags and formatting elements
      const strippedText = allText
        .replace(/<[^>]*>/g, ' ') // Remove HTML tags
        .replace(/&[a-z]+;/g, ' ') // Remove HTML entities like &nbsp;
        .replace(/style="[^"]*"/g, ' ') // Remove style attributes
        .replace(/class="[^"]*"/g, ' ') // Remove class attributes
        .replace(/data-[a-z-]+="[^"]*"/g, ' '); // Remove data attributes

      // Convert to lowercase for state matching
      const cleanedText = strippedText.toLowerCase();

      // Analyze sentiment
      const { counts } = highlightFinancialTerms(strippedText);

      // Check for each state name and abbreviation
      Object.entries(US_STATE_MAP).forEach(([stateName, abbr]) => {
        const stateNameRegex = new RegExp(`\\b${stateName}\\b`, 'i');
        const abbrRegex = new RegExp(`\\b${abbr}\\b`, 'i');

        if (stateNameRegex.test(cleanedText) || abbrRegex.test(cleanedText)) {
          const data = stateMap.get(stateName)!;
          data.count += 1;

          // Only count the article once per state
          if (!data.articles.includes(item.id)) {
            data.articles.push(item.id);

            // Add sentiment counts
            data.positiveCount += counts.positive;
            data.negativeCount += counts.negative;
            data.neutralCount += counts.neutral;
          }
        }
      });
    });

    // Convert map to array, filter out states with no mentions, and sort by count
    return Array.from(stateMap.values())
      .filter(data => data.count > 0)
      .sort((a, b) => b.count - a.count);
  }, [feedItems]);

  // Prepare data for the chart
  const chartData = useMemo(() => {
    return stateData.map(data => ({
      state: `${data.state.charAt(0).toUpperCase() + data.state.slice(1)} (${data.abbreviation})`,
      mentions: data.count,
      articles: data.articles.length,
      positive: data.positiveCount,
      negative: data.negativeCount,
      neutral: data.neutralCount
    }));
  }, [stateData]);

  // Get keywords for selected state
  const stateKeywords = useMemo(() => {
    if (!selectedState || !feedItems || feedItems.length === 0) {
      console.log('No selected state or feed items', { selectedState, feedItemsCount: feedItems.length });
      return [];
    }

    console.log('Looking for state data for:', selectedState);

    // Try to find the state data by name or abbreviation
    console.log('All state data:', stateData);

    // Force lowercase for comparison
    const selectedStateLower = selectedState.toLowerCase();

    // Try to find by exact match first
    let selectedStateData = stateData.find(data =>
      data.state === selectedStateLower ||
      data.abbreviation === selectedStateLower.toUpperCase()
    );

    // If not found, try to find by partial match
    if (!selectedStateData) {
      selectedStateData = stateData.find(data =>
        data.state.includes(selectedStateLower) ||
        selectedStateLower.includes(data.state)
      );
    }

    if (!selectedStateData) {
      console.log('No state data found for:', selectedState, 'Available states:', stateData.map(d => d.state));
      return [];
    }

    console.log('Found state data:', selectedStateData);

    // Get articles that mention this state
    const relevantArticles = feedItems.filter(item =>
      selectedStateData.articles.includes(item.id)
    );

    // Extract keywords from these articles
    const keywordMap = new Map<string, { count: number, articles: number[] }>();

    relevantArticles.forEach(item => {
      const title = item.title || '';
      const content = item.content || '';
      const scrapedContent = item.scrapedContent || '';

      // Combine all text content
      const allText = `${title} ${content} ${scrapedContent}`;

      // First, strip HTML tags
      const strippedText = allText
        .replace(/<[^>]*>/g, ' ') // Remove HTML tags
        .replace(/&[a-z]+;/g, ' ') // Remove HTML entities like &nbsp;
        .replace(/style="[^"]*"/g, ' ') // Remove style attributes
        .replace(/class="[^"]*"/g, ' ') // Remove class attributes
        .replace(/data-[a-z-]+="[^"]*"/g, ' ') // Remove data attributes
        .replace(/span|div|source|color|white|important|style|class|data/g, ' '); // Remove common formatting words

      // Extract words, convert to lowercase, and remove punctuation
      const words = strippedText.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word =>
          // Must be longer than 3 characters
          word.length > 3 &&
          // Exclude common words
          !['the', 'and', 'that', 'this', 'with', 'from', 'have', 'for', 'span', 'div', 'color', 'white',
            'important', 'style', 'class', 'data', 'source', 'count', 'positive', 'negative', 'neutral',
            'highlight', 'amount', 'dollar', 'content', 'article'].includes(word) &&
          // Exclude words that are likely HTML/CSS related
          !word.includes('span') &&
          !word.includes('div') &&
          !word.includes('style') &&
          !word.includes('class') &&
          !word.includes('data') &&
          // Exclude words that are likely not meaningful
          !/^\d+$/.test(word) && // Exclude words that are just numbers
          !/^[a-f0-9]{6,}$/i.test(word) // Exclude what might be hex colors or hashes
        );

      // Count word frequencies
      words.forEach(word => {
        if (!keywordMap.has(word)) {
          keywordMap.set(word, { count: 0, articles: [] });
        }

        const data = keywordMap.get(word)!;
        data.count += 1;

        if (!data.articles.includes(item.id)) {
          data.articles.push(item.id);
        }
      });
    });

    // Convert map to array and sort by count
    const result = Array.from(keywordMap.entries())
      .map(([keyword, data]) => ({
        keyword,
        count: data.count,
        articles: data.articles.length
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20); // Top 20 keywords

    console.log('Generated keywords for state:', selectedState, 'Count:', result.length);
    return result;
  }, [selectedState, feedItems, stateData]);

  // Debug: Log when component renders
  console.log('GeographicAnalysis rendering with selectedState:', selectedState);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-white">Geographic Analysis</CardTitle>
        <CardDescription className="text-white">
          US states mentioned in articles with sentiment analysis
        </CardDescription>
      </CardHeader>
      <CardContent>
        {stateData.length === 0 ? (
          <div className="flex justify-center items-center h-[200px]">
            <p className="text-white text-lg">No state mentions found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {selectedState ? (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-white text-lg">
                    Keywords for {selectedState.charAt(0).toUpperCase() + selectedState.slice(1)}
                  </h3>
                  <div className="flex gap-2">
                    <Badge
                      variant="outline"
                      className="cursor-pointer hover:bg-primary/30"
                      onClick={() => {
                        console.log('Clearing selected state');
                        setSelectedState(null);
                      }}
                    >
                      Back to all states
                    </Badge>
                  </div>
                </div>
                <div className="text-center text-sm text-muted-foreground mb-4 p-2 border border-dashed border-primary/30 rounded-md bg-primary/5">
                  <span className="font-medium">Interactive Keywords:</span> Click on any keyword to see related articles and context
                </div>

                <ScrollArea className="h-[400px]">
                  <div className="space-y-2">
                    {stateKeywords.length > 0 ? (
                      stateKeywords.map((data, index) => (
                        <div
                          key={index}
                          className="flex justify-between items-center p-3 border rounded-md border-primary/30 bg-primary/5 hover:bg-primary/20 cursor-pointer mb-2 transition-all duration-200 transform hover:scale-[1.01] hover:shadow-md"
                          onClick={() => {
                            console.log('Keyword clicked:', data.keyword);
                            handleKeywordClick(data.keyword);
                          }}
                        >
                          <span className="text-white flex items-center">
                            <span className="font-medium">{data.keyword}</span>
                            <span className="ml-2 text-xs text-primary flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                              </svg>
                              click for article details
                            </span>
                          </span>
                          <div className="flex gap-2">
                            <Badge variant="outline" className="bg-primary/20">
                              {data.count} mentions
                            </Badge>
                            <Badge variant="outline" className="bg-primary/20">
                              {data.articles} articles
                            </Badge>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-muted-foreground">
                        No keywords found for this state. Try clicking on a different state.
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="text-center text-sm text-muted-foreground mb-2 p-2 border border-dashed border-primary/30 rounded-md bg-primary/5">
                  <span className="font-medium">Interactive Chart:</span> Click on any state name, bar, or label to see detailed keywords and article analysis
                </div>
                <div className="h-[500px]">
                  <ChartContainer
                    config={{
                      mentions: { color: "#3b82f6" },
                      articles: { color: "#10b981" },
                      positive: { color: "#4ade80" },
                      negative: { color: "#f87171" },
                      neutral: { color: "#94a3b8" }
                    }}
                  >
                    <RechartsPrimitive.BarChart
                      data={chartData}
                      layout="vertical"
                      margin={{ top: 20, right: 30, left: 150, bottom: 20 }}
                      onClick={(data) => {
                        if (data && data.activeLabel) {
                          console.log('Chart clicked, data:', data);
                          const stateName = data.activeLabel.split(' (')[0].toLowerCase();
                          console.log('Setting selected state to:', stateName);

                          // Force a re-render by setting to null first
                          setSelectedState(null);

                          // Use setTimeout to ensure the state change is processed
                          setTimeout(() => {
                            setSelectedState(stateName);
                          }, 10);
                        }
                      }}
                    >
                      <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                      <RechartsPrimitive.XAxis type="number" />
                      <RechartsPrimitive.YAxis
                        type="category"
                        dataKey="state"
                        width={150}
                        tick={(props) => {
                          const { x, y, payload } = props;
                          const stateName = payload.value.split(' (')[0].toLowerCase();

                          return (
                            <g transform={`translate(${x},${y})`}>
                              <text
                                x={0}
                                y={0}
                                dy={4}
                                textAnchor="end"
                                fill="#888"
                                fontSize={12}
                                className="cursor-pointer hover:text-primary"
                                onClick={() => {
                                  console.log('State label clicked:', payload.value);
                                  // Force a re-render by setting to null first
                                  setSelectedState(null);

                                  // Use setTimeout to ensure the state change is processed
                                  setTimeout(() => {
                                    setSelectedState(stateName);
                                  }, 10);
                                }}
                              >
                                {payload.value}
                              </text>
                            </g>
                          );
                        }}
                      />
                      <RechartsPrimitive.Tooltip />
                      <RechartsPrimitive.Legend />
                      <RechartsPrimitive.Bar
                        dataKey="mentions"
                        name="Mentions"
                        fill="var(--color-mentions)"
                        onClick={(data) => {
                          console.log('Bar clicked, data:', data);
                          // Extract state name and ensure it's properly formatted
                          const fullStateName = data.state.split(' (')[0];
                          const stateName = fullStateName.toLowerCase();
                          console.log('Setting selected state to:', stateName);

                          // Force a re-render by setting to null first
                          setSelectedState(null);

                          // Use setTimeout to ensure the state change is processed
                          setTimeout(() => {
                            setSelectedState(stateName);
                          }, 10);
                        }}
                        cursor="pointer"
                        className="hover:opacity-80 transition-opacity"
                        // Add animation
                        animationDuration={300}
                        // Add active animation
                        activeBar={{ fill: 'var(--color-primary)' }}
                      />
                      <RechartsPrimitive.Bar
                        dataKey="articles"
                        name="Articles"
                        fill="var(--color-articles)"
                        onClick={(data) => {
                          console.log('Articles bar clicked, data:', data);
                          // Extract state name and ensure it's properly formatted
                          const fullStateName = data.state.split(' (')[0];
                          const stateName = fullStateName.toLowerCase();
                          console.log('Setting selected state to:', stateName);

                          // Force a re-render by setting to null first
                          setSelectedState(null);

                          // Use setTimeout to ensure the state change is processed
                          setTimeout(() => {
                            setSelectedState(stateName);
                          }, 10);
                        }}
                        cursor="pointer"
                        className="hover:opacity-80 transition-opacity"
                        // Add animation
                        animationDuration={300}
                        // Add active animation
                        activeBar={{ fill: 'var(--color-primary)' }}
                      />
                    </RechartsPrimitive.BarChart>
                  </ChartContainer>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Keyword Detail Dialog */}
      {selectedKeyword && (
        <KeywordDetailDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          keyword={selectedKeyword}
          feedItems={feedItems}
          type="word"
          onOpenArticle={onOpenArticle}
          onSaveArticle={onSaveArticle}
          onOpenInBrowser={onOpenInBrowser}
        />
      )}
    </Card>
  );
}
