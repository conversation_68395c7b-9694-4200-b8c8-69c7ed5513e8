import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  BarChart3, 
  TrendingUp, 
  FileText, 
  Palette,
  Clock,
  Target,
  Users,
  Eye
} from 'lucide-react';

interface AnalyticsDashboardProps {
  transcripts: any[];
  onRefresh: () => void;
}

export function AnalyticsDashboard({ transcripts, onRefresh }: AnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<any>({
    totalTranscripts: 0,
    totalHighlights: 0,
    avgHighlightsPerTranscript: 0,
    mostUsedColors: [],
    transcriptsBySource: {},
    recentActivity: [],
    topHighlightedSegments: []
  });

  useEffect(() => {
    calculateAnalytics();
  }, [transcripts]);

  const calculateAnalytics = async () => {
    try {
      // Basic stats
      const totalTranscripts = transcripts.length;
      let totalHighlights = 0;
      const colorUsage: Record<string, number> = {};
      const sourceTypes: Record<string, number> = {};
      const recentActivity: any[] = [];

      // Fetch highlights for each transcript and calculate stats
      for (const transcript of transcripts) {
        const highlightsResponse = await fetch(`/api/txt-tab/transcripts/${transcript.id}/highlights`);
        if (highlightsResponse.ok) {
          const highlights = await highlightsResponse.json();
          totalHighlights += highlights.length;
          
          // Count color usage
          highlights.forEach((highlight: any) => {
            colorUsage[highlight.color] = (colorUsage[highlight.color] || 0) + 1;
          });
        }

        // Count source types
        const source = transcript.source_type || 'manual';
        sourceTypes[source] = (sourceTypes[source] || 0) + 1;

        // Add to recent activity
        recentActivity.push({
          type: 'transcript',
          title: transcript.title,
          date: new Date(transcript.updated_at),
          action: 'updated'
        });
      }

      // Sort colors by usage
      const mostUsedColors = Object.entries(colorUsage)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([color, count]) => ({ color, count }));

      // Sort recent activity
      recentActivity.sort((a, b) => b.date.getTime() - a.date.getTime());

      setAnalytics({
        totalTranscripts,
        totalHighlights,
        avgHighlightsPerTranscript: totalTranscripts > 0 ? (totalHighlights / totalTranscripts).toFixed(1) : 0,
        mostUsedColors,
        transcriptsBySource: sourceTypes,
        recentActivity: recentActivity.slice(0, 10),
        topHighlightedSegments: [] // This would require more complex analysis
      });
    } catch (error) {
      console.error('Error calculating analytics:', error);
    }
  };

  const getColorName = (hex: string) => {
    const colorNames: Record<string, string> = {
      '#ffff00': 'Yellow',
      '#00ff00': 'Green',
      '#00bfff': 'Blue',
      '#ff69b4': 'Pink',
      '#ffa500': 'Orange',
      '#9370db': 'Purple',
      '#ff6b6b': 'Red',
      '#00ffff': 'Cyan'
    };
    return colorNames[hex] || hex;
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'youtube': return <Eye className="h-4 w-4" />;
      case 'rss': return <BarChart3 className="h-4 w-4" />;
      case 'manual': return <FileText className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Analytics Dashboard
        </h3>
        <p className="text-sm text-muted-foreground">
          Insights and statistics about your transcript collection and highlighting patterns
        </p>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">{analytics.totalTranscripts}</p>
                <p className="text-sm text-muted-foreground">Total Transcripts</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">{analytics.totalHighlights}</p>
                <p className="text-sm text-muted-foreground">Total Highlights</p>
              </div>
              <Palette className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">{analytics.avgHighlightsPerTranscript}</p>
                <p className="text-sm text-muted-foreground">Avg Highlights/Script</p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">
                  {Object.keys(analytics.transcriptsBySource).length}
                </p>
                <p className="text-sm text-muted-foreground">Source Types</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Most Used Highlight Colors */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Most Used Highlight Colors</CardTitle>
            <CardDescription>
              Your highlighting preferences and patterns
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analytics.mostUsedColors.length > 0 ? (
              <div className="space-y-3">
                {analytics.mostUsedColors.map((colorData: any, index: number) => (
                  <div key={colorData.color} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-6 h-6 rounded border"
                        style={{ backgroundColor: colorData.color }}
                      />
                      <span className="text-sm font-medium">
                        {getColorName(colorData.color)}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {colorData.count} uses
                      </span>
                      <Badge variant="outline" className="text-xs">
                        #{index + 1}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">
                No highlights created yet
              </p>
            )}
          </CardContent>
        </Card>

        {/* Transcripts by Source */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Transcripts by Source</CardTitle>
            <CardDescription>
              Distribution of your transcript sources
            </CardDescription>
          </CardHeader>
          <CardContent>
            {Object.keys(analytics.transcriptsBySource).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(analytics.transcriptsBySource).map(([source, count]: [string, any]) => (
                  <div key={source} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getSourceIcon(source)}
                      <span className="text-sm font-medium capitalize">
                        {source.replace('-', ' ')}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {count} transcripts
                      </span>
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full"
                          style={{ 
                            width: `${(count / analytics.totalTranscripts) * 100}%` 
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">
                No transcripts available
              </p>
            )}
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-base">Recent Activity</CardTitle>
            <CardDescription>
              Latest updates to your transcript collection
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[200px]">
              {analytics.recentActivity.length > 0 ? (
                <div className="space-y-3">
                  {analytics.recentActivity.map((activity: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-md">
                      <div className="flex items-center gap-3">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">{activity.title}</p>
                          <p className="text-xs text-muted-foreground">
                            {activity.action} • {activity.date.toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {activity.type}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground text-center py-8">
                  No recent activity
                </p>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Insights & Recommendations</CardTitle>
          <CardDescription>
            AI-powered suggestions for improving your script writing workflow
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analytics.totalTranscripts === 0 && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  🚀 <strong>Get Started:</strong> Create your first transcript to begin building your script library.
                </p>
              </div>
            )}
            
            {analytics.totalTranscripts > 0 && analytics.totalHighlights === 0 && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-sm text-yellow-800">
                  ✨ <strong>Try Highlighting:</strong> Start highlighting effective segments in your transcripts to track what works best.
                </p>
              </div>
            )}
            
            {analytics.totalHighlights > 10 && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-800">
                  📊 <strong>Great Progress:</strong> You've created {analytics.totalHighlights} highlights! Consider analyzing patterns to optimize future scripts.
                </p>
              </div>
            )}
            
            {Object.keys(analytics.transcriptsBySource).length > 1 && (
              <div className="p-3 bg-purple-50 border border-purple-200 rounded-md">
                <p className="text-sm text-purple-800">
                  🔄 <strong>Multi-Source Strategy:</strong> You're importing from multiple sources. Consider creating templates based on your most successful formats.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
