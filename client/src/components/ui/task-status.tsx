import React from 'react';
import { Task, TaskStatus, useBackgroundTasks } from '@/hooks/use-background-tasks';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { X, CheckCircle, AlertCircle, Clock, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { formatRelativeTime } from '@/lib/date-format';

interface TaskStatusProps {
  task: Task;
  onCancel?: (taskId: string) => void;
  showDetails?: boolean;
}

export function TaskStatusBadge({ status }: { status: TaskStatus }) {
  switch (status) {
    case TaskStatus.PENDING:
      return <Badge variant="outline" className="bg-gray-100"><Clock className="h-3 w-3 mr-1" /> Pending</Badge>;
    case TaskStatus.RUNNING:
      return <Badge variant="outline" className="bg-blue-100 text-blue-800"><Play className="h-3 w-3 mr-1" /> Running</Badge>;
    case TaskStatus.COMPLETED:
      return <Badge variant="outline" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" /> Completed</Badge>;
    case TaskStatus.FAILED:
      return <Badge variant="outline" className="bg-red-100 text-red-800"><AlertCircle className="h-3 w-3 mr-1" /> Failed</Badge>;
    case TaskStatus.CANCELLED:
      return <Badge variant="outline" className="bg-gray-100"><X className="h-3 w-3 mr-1" /> Cancelled</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
}

export function TaskStatusItem({ task, onCancel, showDetails = false }: TaskStatusProps) {
  const isActive = task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING;

  // Handle date formatting safely using our IST formatter
  const formatDate = (dateStr: string | Date | undefined) => {
    if (!dateStr) return 'Unknown';
    try {
      return formatRelativeTime(dateStr);
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid date';
    }
  };

  const createdAtFormatted = formatDate(task.createdAt);
  const updatedAtFormatted = formatDate(task.updatedAt);
  const startedAtFormatted = task.startedAt ? formatDate(task.startedAt) : null;
  const completedAtFormatted = task.completedAt ? formatDate(task.completedAt) : null;

  return (
    <div className="bg-card/70 rounded-md border border-border/50 p-2 shadow-sm hover:shadow transition-shadow">
      <div className="flex justify-between items-start gap-2">
        <div className="flex-1 min-w-0"> {/* Added min-width to prevent text overflow */}
          <h3 className="font-medium text-sm truncate">{task.name}</h3>
          <p className="text-xs text-muted-foreground truncate">{task.description}</p>
        </div>
        <TaskStatusBadge status={task.status} />
      </div>

      {isActive && (
        <div className="mt-1.5">
          <Progress value={task.progress} className="h-1.5" />
          <p className="text-xs text-muted-foreground mt-0.5">{task.progress}% complete</p>
        </div>
      )}

      {task.error && (
        <div className="mt-1.5 text-xs text-red-500">
          <p className="truncate">Error: {task.error}</p>
        </div>
      )}

      {showDetails && (
        <div className="mt-1.5 text-xs text-muted-foreground">
          <p>Created: {createdAtFormatted}</p>
          <p>Last updated: {updatedAtFormatted}</p>
          {startedAtFormatted && <p>Started: {startedAtFormatted}</p>}
          {completedAtFormatted && <p>Completed: {completedAtFormatted}</p>}
        </div>
      )}

      {isActive && onCancel && (
        <div className="mt-1.5 flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onCancel(task.id)}
            className="text-xs h-7 px-2"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => onCancel(task.id)}
            className="text-xs h-7 px-2"
          >
            Stop
          </Button>
        </div>
      )}
    </div>
  );
}

export function TaskStatusList({
  tasks,
  onCancel,
  showDetails = false,
  limit
}: {
  tasks: Task[],
  onCancel?: (taskId: string) => void,
  showDetails?: boolean,
  limit?: number
}) {
  // Filter and sort tasks
  const sortedTasks = [...tasks]
    .sort((a, b) => {
      // Active tasks first
      const aActive = a.status === TaskStatus.PENDING || a.status === TaskStatus.RUNNING;
      const bActive = b.status === TaskStatus.PENDING || b.status === TaskStatus.RUNNING;

      if (aActive && !bActive) return -1;
      if (!aActive && bActive) return 1;

      // Then by updated date (newest first)
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    });

  // Apply limit if specified
  const displayTasks = limit ? sortedTasks.slice(0, limit) : sortedTasks;

  if (displayTasks.length === 0) {
    return null; // Empty state is now handled in the parent component
  }

  return (
    <div className="space-y-2 overflow-y-auto" style={{ maxHeight: '500px' }}>
      {displayTasks.map(task => (
        <TaskStatusItem
          key={task.id}
          task={task}
          onCancel={onCancel}
          showDetails={showDetails}
        />
      ))}
    </div>
  );
}

export function RecentTasksWidget({ onViewAll, compact = false }: { onViewAll?: () => void, compact?: boolean }) {
  const { recentTasks, cancelTask } = useBackgroundTasks();

  return (
    <div className={`bg-background/80 rounded-md w-full max-w-full overflow-hidden ${compact ? '' : 'p-2'}`} style={{ maxHeight: compact ? '300px' : '400px' }}>
      {/* Header for the widget */}
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-sm font-medium">Recent Tasks</h3>
        {onViewAll && (
          <Button variant="ghost" size="sm" onClick={onViewAll} className="h-7 px-2 text-xs">
            View All
          </Button>
        )}
      </div>

      <TaskStatusList
        tasks={recentTasks}
        onCancel={cancelTask}
        limit={compact ? 5 : 3}
      />

      {recentTasks.length === 0 && (
        <div className="text-center py-2 text-muted-foreground text-sm">
          <div className="flex justify-center mb-1">
            <Clock className="h-4 w-4 opacity-50" />
          </div>
          No active tasks
        </div>
      )}
    </div>
  );
}
