import { OpenRouterPrompt as Prompt } from '@shared/schema';
import {
  getPrompts,
  getPrompt,
  createPrompt,
  updatePrompt,
  deletePrompt,
  setDefaultPrompt
} from './promptsService';

/**
 * Get all Ollama prompts for the current user
 * Redirects to the unified prompts service
 */
export async function getOllamaPrompts(): Promise<Prompt[]> {
  // Use the unified prompts service
  return await getPrompts();
}

/**
 * Get a specific Ollama prompt by ID
 * Redirects to the unified prompts service
 */
export async function getOllamaPrompt(promptId: number): Promise<Prompt> {
  // Use the unified prompts service
  return await getPrompt(promptId);
}

/**
 * Create a new Ollama prompt
 * Redirects to the unified prompts service
 */
export async function createOllamaPrompt(
  name: string,
  promptText: string,
  isDefault: boolean = false
): Promise<Prompt> {
  // Use the unified prompts service
  return await createPrompt(name, promptText, isDefault);
}

/**
 * Update an existing Ollama prompt
 * Redirects to the unified prompts service
 */
export async function updateOllamaPrompt(
  promptId: number,
  updates: Partial<Prompt>
): Promise<Prompt> {
  // Use the unified prompts service
  return await updatePrompt(promptId, updates);
}

/**
 * Delete an Ollama prompt
 * Redirects to the unified prompts service
 */
export async function deleteOllamaPrompt(promptId: number): Promise<void> {
  // Use the unified prompts service
  await deletePrompt(promptId);
}

/**
 * Set a prompt as the default for the current user
 * Redirects to the unified prompts service
 */
export async function setDefaultOllamaPrompt(promptId: number): Promise<void> {
  // Use the unified prompts service
  await setDefaultPrompt(promptId);
}
