import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { User, Globe, Edit, Check, X, ExternalLink, Copy, Layers, RotateCw, Loader2, Plus, Minus, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";

// Default URL options
const DEFAULT_URLS = [
  { name: 'NotebookLM', url: 'https://notebooklm.google.com/' },
  { name: 'YouTube', url: 'https://www.youtube.com/' },
  { name: 'Google Docs', url: 'https://docs.google.com/' },
  { name: 'Google Drive', url: 'https://drive.google.com/' },
];

interface CustomUrlAccountLauncherProps {
  // Any props needed
}

const CustomUrlAccountLauncher: React.FC<CustomUrlAccountLauncherProps> = () => {
  const { toast } = useToast();

  // State for managing URLs
  const [urlOptions, setUrlOptions] = useState<{ name: string; url: string }[]>(() => {
    // Try to load from localStorage
    const savedOptions = localStorage.getItem('customUrlOptions');
    if (savedOptions) {
      try {
        return JSON.parse(savedOptions);
      } catch (error) {
        console.error('Error parsing saved URL options:', error);
      }
    }
    return DEFAULT_URLS;
  });

  // State for selected URL
  const [selectedUrlName, setSelectedUrlName] = useState<string>(urlOptions[0]?.name || 'NotebookLM');

  // State for custom URL input
  const [customUrl, setCustomUrl] = useState<string>(() => {
    const selected = urlOptions.find(option => option.name === selectedUrlName);
    return selected?.url || 'https://notebooklm.google.com/';
  });

  // State for editing mode
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editingName, setEditingName] = useState<string>('');
  const [editingUrl, setEditingUrl] = useState<string>('');

  // State for new URL
  const [newUrlName, setNewUrlName] = useState<string>('');
  const [newUrlValue, setNewUrlValue] = useState<string>('');
  const [isAddingUrl, setIsAddingUrl] = useState<boolean>(false);

  // State for batch URL opener
  const [multiUrls, setMultiUrls] = useState<string>(() => {
    const saved = localStorage.getItem('multiUrls');
    return saved || '';
  });

  const [multiMessages, setMultiMessages] = useState<string>(() => {
    const saved = localStorage.getItem('multiMessages');
    return saved || '';
  });

  const [additionalMessage, setAdditionalMessage] = useState<string>(() => {
    const saved = localStorage.getItem('additionalMessage');
    return saved || '';
  });

  const [numAccounts, setNumAccounts] = useState<number>(() => {
    const saved = localStorage.getItem('numAccounts');
    return saved ? parseInt(saved, 10) : 6;
  });

  const [isOpeningMultiUrls, setIsOpeningMultiUrls] = useState<boolean>(false);

  // Update customUrl when selectedUrlName changes
  useEffect(() => {
    const selected = urlOptions.find(option => option.name === selectedUrlName);
    if (selected) {
      setCustomUrl(selected.url);
    }
  }, [selectedUrlName, urlOptions]);

  // Save URL options to localStorage when they change
  useEffect(() => {
    localStorage.setItem('customUrlOptions', JSON.stringify(urlOptions));
  }, [urlOptions]);

  // Save multi URLs and messages to localStorage
  useEffect(() => {
    localStorage.setItem('multiUrls', multiUrls);
    localStorage.setItem('multiMessages', multiMessages);
    localStorage.setItem('additionalMessage', additionalMessage);
    localStorage.setItem('numAccounts', numAccounts.toString());
  }, [multiUrls, multiMessages, additionalMessage, numAccounts]);

  // Function to open URL with specific account
  const openUrlWithAccount = (accountNumber: number) => {
    // For account 1, just open the URL
    if (accountNumber === 1) {
      window.open(customUrl, '_blank');
      return;
    }

    // For other accounts, add the authuser parameter
    let urlToOpen = customUrl;

    // Check if URL already has parameters
    if (urlToOpen.includes('?')) {
      // Check if URL already has authuser parameter
      if (urlToOpen.includes('authuser=')) {
        // Replace existing authuser parameter
        urlToOpen = urlToOpen.replace(/authuser=\d+/, `authuser=${accountNumber - 1}`);
      } else {
        // Add authuser parameter
        urlToOpen += `&authuser=${accountNumber - 1}`;
      }
    } else {
      // Add authuser parameter as first parameter
      urlToOpen += `?authuser=${accountNumber - 1}`;
    }

    window.open(urlToOpen, '_blank');

    toast({
      title: "URL Opened",
      description: `Opened ${selectedUrlName} with Account ${accountNumber}`,
      duration: 3000,
    });
  };

  // Function to add a new URL
  const handleAddUrl = () => {
    if (!newUrlName.trim() || !newUrlValue.trim()) {
      toast({
        title: "Error",
        description: "Please enter both a name and URL",
        variant: "destructive",
      });
      return;
    }

    // Check if URL is valid
    try {
      new URL(newUrlValue);
    } catch (error) {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid URL including http:// or https://",
        variant: "destructive",
      });
      return;
    }

    // Check if name already exists
    if (urlOptions.some(option => option.name === newUrlName)) {
      toast({
        title: "Name already exists",
        description: "Please choose a different name",
        variant: "destructive",
      });
      return;
    }

    // Add new URL
    setUrlOptions([...urlOptions, { name: newUrlName, url: newUrlValue }]);
    setNewUrlName('');
    setNewUrlValue('');
    setIsAddingUrl(false);

    toast({
      title: "URL Added",
      description: `Added ${newUrlName} to your custom URLs`,
      duration: 3000,
    });
  };

  // Function to update a URL
  const handleUpdateUrl = () => {
    if (!editingName.trim() || !editingUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter both a name and URL",
        variant: "destructive",
      });
      return;
    }

    // Check if URL is valid
    try {
      new URL(editingUrl);
    } catch (error) {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid URL including http:// or https://",
        variant: "destructive",
      });
      return;
    }

    // Update URL
    const updatedOptions = urlOptions.map(option =>
      option.name === selectedUrlName
        ? { name: editingName, url: editingUrl }
        : option
    );

    setUrlOptions(updatedOptions);
    setSelectedUrlName(editingName);
    setIsEditing(false);

    toast({
      title: "URL Updated",
      description: `Updated ${selectedUrlName} in your custom URLs`,
      duration: 3000,
    });
  };

  // Function to delete a URL
  const handleDeleteUrl = () => {
    if (urlOptions.length <= 1) {
      toast({
        title: "Cannot Delete",
        description: "You must have at least one URL",
        variant: "destructive",
      });
      return;
    }

    // Delete URL
    const updatedOptions = urlOptions.filter(option => option.name !== selectedUrlName);
    setUrlOptions(updatedOptions);
    setSelectedUrlName(updatedOptions[0].name);

    toast({
      title: "URL Deleted",
      description: `Deleted ${selectedUrlName} from your custom URLs`,
      duration: 3000,
    });
  };

  // Function to open multiple URLs with specific account
  const openMultipleUrls = async (accountNumber: number) => {
    if (!multiUrls.trim()) {
      toast({
        title: "No URLs",
        description: "Please enter at least one URL",
        variant: "destructive",
      });
      return;
    }

    setIsOpeningMultiUrls(true);

    try {
      // Split URLs and messages
      const urls = multiUrls.split('\n').filter(url => url.trim());
      const messages = multiMessages.split('\n').filter(msg => msg.trim());

      // Process each URL
      for (let i = 0; i < urls.length; i++) {
        let url = urls[i].trim();

        // Skip empty URLs
        if (!url) continue;

        // Try to parse URL
        try {
          // Add https:// if missing
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }

          // For account 1, just open the URL
          if (accountNumber === 1) {
            window.open(url, '_blank');
          } else {
            // For other accounts, add the authuser parameter
            let urlToOpen = url;

            // Check if URL already has parameters
            if (urlToOpen.includes('?')) {
              // Check if URL already has authuser parameter
              if (urlToOpen.includes('authuser=')) {
                // Replace existing authuser parameter
                urlToOpen = urlToOpen.replace(/authuser=\d+/, `authuser=${accountNumber - 1}`);
              } else {
                // Add authuser parameter
                urlToOpen += `&authuser=${accountNumber - 1}`;
              }
            } else {
              // Add authuser parameter as first parameter
              urlToOpen += `?authuser=${accountNumber - 1}`;
            }

            window.open(urlToOpen, '_blank');
          }

          // Copy messages to clipboard if available
          const messagesToCopy = [];

          // Add random message from the main messages list if available
          if (messages.length > 0) {
            const randomIndex = Math.floor(Math.random() * messages.length);
            const selectedMessage = messages[randomIndex];
            messagesToCopy.push(selectedMessage);
          }

          // Add random message from additional messages if available
          if (additionalMessage.trim()) {
            const additionalMessages = additionalMessage.split('\n').filter(msg => msg.trim());
            if (additionalMessages.length > 0) {
              const randomIndex = Math.floor(Math.random() * additionalMessages.length);
              const selectedAdditionalMessage = additionalMessages[randomIndex];
              messagesToCopy.push(selectedAdditionalMessage.trim());
            }
          }

          // Copy both messages to clipboard if any are available
          if (messagesToCopy.length > 0) {
            const combinedMessage = messagesToCopy.join('\n');
            await navigator.clipboard.writeText(combinedMessage);

            const messageCount = messagesToCopy.length;
            const description = messageCount === 1
              ? `Copied 1 message to clipboard`
              : `Copied ${messageCount} messages to clipboard`;

            toast({
              title: "Messages Copied",
              description: description,
              duration: 2000,
            });
          }

          // Small delay between opening URLs
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
          console.error('Error opening URL:', url, error);
          toast({
            title: "Error Opening URL",
            description: `Could not open ${url}`,
            variant: "destructive",
          });
        }
      }

      toast({
        title: "URLs Opened",
        description: `Opened ${urls.length} URLs with Account ${accountNumber}`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error in batch URL opener:', error);
      toast({
        title: "Error",
        description: "An error occurred while opening URLs",
        variant: "destructive",
      });
    } finally {
      setIsOpeningMultiUrls(false);
    }
  };

  return (
    <div className="bg-background/80 rounded-md p-4 border border-border">
      <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
        <Globe className="h-5 w-5 text-primary" />
        Custom URL Account Launcher
      </h3>
      <p className="text-sm text-muted-foreground mb-4">
        Open any website with different Google accounts to maximize your productivity.
      </p>

      <Tabs defaultValue="quick-launch" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="quick-launch">Quick Launch</TabsTrigger>
          <TabsTrigger value="batch-opener">Batch URL Opener</TabsTrigger>
        </TabsList>

        <TabsContent value="quick-launch" className="space-y-4 mt-4">
          {/* Quick Launch Tab Content */}
          <div className="space-y-4">
            {/* URL Selection and Management */}
            <div className="flex flex-col space-y-4">
              {/* URL Selector */}
              <div className="flex items-center gap-2">
                <div className="flex-1">
                  {isEditing ? (
                    <div className="flex gap-2">
                      <Input
                        value={editingName}
                        onChange={(e) => setEditingName(e.target.value)}
                        placeholder="URL Name"
                        className="flex-1"
                      />
                      <Input
                        value={editingUrl}
                        onChange={(e) => setEditingUrl(e.target.value)}
                        placeholder="https://example.com"
                        className="flex-1"
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleUpdateUrl}
                        title="Save changes"
                      >
                        <Check className="h-4 w-4 text-green-500" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setIsEditing(false)}
                        title="Cancel"
                      >
                        <X className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex gap-2">
                      <Select
                        value={selectedUrlName}
                        onValueChange={setSelectedUrlName}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select a URL" />
                        </SelectTrigger>
                        <SelectContent>
                          {urlOptions.map((option) => (
                            <SelectItem key={option.name} value={option.name}>
                              {option.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          const selected = urlOptions.find(option => option.name === selectedUrlName);
                          if (selected) {
                            setEditingName(selected.name);
                            setEditingUrl(selected.url);
                            setIsEditing(true);
                          }
                        }}
                        title="Edit URL"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleDeleteUrl}
                        title="Delete URL"
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Add New URL */}
              {isAddingUrl ? (
                <div className="flex flex-col space-y-2 bg-muted/30 p-3 rounded-md">
                  <h4 className="text-sm font-medium">Add New URL</h4>
                  <div className="flex gap-2">
                    <Input
                      value={newUrlName}
                      onChange={(e) => setNewUrlName(e.target.value)}
                      placeholder="URL Name"
                      className="flex-1"
                    />
                    <Input
                      value={newUrlValue}
                      onChange={(e) => setNewUrlValue(e.target.value)}
                      placeholder="https://example.com"
                      className="flex-1"
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setIsAddingUrl(false);
                        setNewUrlName('');
                        setNewUrlValue('');
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleAddUrl}
                    >
                      Add URL
                    </Button>
                  </div>
                </div>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => setIsAddingUrl(true)}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add New URL
                </Button>
              )}

              {/* URL Display */}
              <div className="flex items-center gap-2">
                <Input
                  value={customUrl}
                  onChange={(e) => setCustomUrl(e.target.value)}
                  placeholder="https://example.com"
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => window.open(customUrl, '_blank')}
                  title="Open URL"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>

              {/* Account Buttons */}
              <div>
                <div className="text-sm font-medium mb-2">Open with account:</div>
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2">
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(accountNumber => (
                    <Button
                      key={accountNumber}
                      onClick={() => openUrlWithAccount(accountNumber)}
                      variant="outline"
                      className="h-auto py-2 px-1 relative text-xs"
                    >
                      <div className="flex flex-col items-center gap-0.5">
                        <User className="h-3.5 w-3.5" />
                        <span>Acc {accountNumber}</span>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="batch-opener" className="space-y-4 mt-4">
          {/* Batch URL Opener Tab Content */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="multi-urls" className="text-sm font-medium">URLs (one per line)</Label>
                <Textarea
                  id="multi-urls"
                  value={multiUrls}
                  onChange={(e) => setMultiUrls(e.target.value)}
                  placeholder="https://example.com&#10;https://another-example.com"
                  className="min-h-[150px] mt-1"
                />
              </div>
              <div>
                <Label htmlFor="multi-messages" className="text-sm font-medium">Messages to copy (one per line)</Label>
                <Textarea
                  id="multi-messages"
                  value={multiMessages}
                  onChange={(e) => setMultiMessages(e.target.value)}
                  placeholder="First message to copy&#10;Second message to copy"
                  className="min-h-[150px] mt-1"
                />
              </div>
            </div>

            <div className="mt-4">
              <Label htmlFor="additional-message" className="text-sm font-medium">Additional messages to copy (one per line)</Label>
              <Textarea
                id="additional-message"
                value={additionalMessage}
                onChange={(e) => setAdditionalMessage(e.target.value)}
                placeholder="First additional message&#10;Second additional message&#10;Third additional message"
                className="min-h-[80px] mt-1"
              />
            </div>

            <div className="flex items-center gap-4">
              <div className="text-sm font-medium">Number of Google accounts:</div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setNumAccounts(Math.max(1, numAccounts - 1))}
                  className="h-8 w-8"
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="w-8 text-center font-medium">{numAccounts}</span>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setNumAccounts(Math.min(12, numAccounts + 1))}
                  className="h-8 w-8"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="mt-4">
              <div className="text-sm font-medium mb-2">Open all URLs with account:</div>
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2 mb-4">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].slice(0, numAccounts).map(accountNumber => (
                  <Button
                    key={accountNumber}
                    onClick={() => openMultipleUrls(accountNumber)}
                    disabled={isOpeningMultiUrls || !multiUrls.trim()}
                    variant="outline"
                    className="h-auto py-2 px-1 relative text-xs"
                  >
                    <div className="flex flex-col items-center gap-0.5">
                      <User className="h-3.5 w-3.5" />
                      <span>Acc {accountNumber}</span>
                      {isOpeningMultiUrls && (
                        <Loader2 className="h-3 w-3 mt-1 animate-spin" />
                      )}
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            <div className="text-xs text-muted-foreground bg-muted/30 p-3 rounded-md">
              <strong>How to use:</strong>
              <ol className="list-decimal list-inside mt-1 space-y-1">
                <li>Paste multiple URLs (one per line)</li>
                <li>Optionally add messages (one per line) in the first message box - one will be randomly selected</li>
                <li>Optionally add additional messages (one per line) in the second message box - one will be randomly selected</li>
                <li>Set the correct number of Google accounts you have using the + and - buttons</li>
                <li>Click on any account button to open all URLs in new tabs with that specific account</li>
                <li>For each URL opened, one random message from each message box will be copied to your clipboard (separated by new lines)</li>
                <li>For YouTube links, the correct account parameter will be added automatically</li>
              </ol>
              <p className="mt-2">
                <strong>Tip:</strong> For Google services, you may need to sign in to multiple accounts first. If you have 6 Google accounts, make sure all 6 are signed in to your browser before using accounts 2-6. Your settings will be saved and remembered even if you refresh the page.
              </p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CustomUrlAccountLauncher;
